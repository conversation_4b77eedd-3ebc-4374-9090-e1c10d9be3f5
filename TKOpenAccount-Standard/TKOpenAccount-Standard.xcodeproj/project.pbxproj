// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		B3D794591B8EA14F00768134 /* build_TKOpenResource */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = B3D7945A1B8EA14F00768134 /* Build configuration list for PBXAggregateTarget "build_TKOpenResource" */;
			buildPhases = (
				B3D7945D1B8EA15400768134 /* ShellScript */,
			);
			dependencies = (
			);
			name = build_TKOpenResource;
			productName = build_TKOpenResource;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		042CE7052408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = 042CE7042408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m */; };
		043015EC23FCC811004F0C17 /* TKOpenPlugin60028.m in Sources */ = {isa = PBXBuildFile; fileRef = 043015EB23FCC811004F0C17 /* TKOpenPlugin60028.m */; };
		043015EF23FCC936004F0C17 /* TKFaceImageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 043015EE23FCC936004F0C17 /* TKFaceImageViewController.m */; };
		043015F323FCCDBF004F0C17 /* TKFaceImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 043015F123FCCDBE004F0C17 /* TKFaceImageView.m */; };
		043884B923B05A240009F14D /* KeyBoard.xml in Resources */ = {isa = PBXBuildFile; fileRef = 043884B823B05A240009F14D /* KeyBoard.xml */; };
		0441008C23C43BD1005B9D05 /* TKFxcAccountInfoType.m in Sources */ = {isa = PBXBuildFile; fileRef = 0441008B23C43BD1005B9D05 /* TKFxcAccountInfoType.m */; };
		04441AA0221691F300CFDC2D /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 040B88412148BC19008DBE05 /* VideoToolbox.framework */; };
		045ACEA622A4FC30004D8557 /* TKWebViewApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 045ACEA522A4FC30004D8557 /* TKWebViewApp.framework */; };
		045EF862241BA81000032B22 /* TKOpenPlugin60032.m in Sources */ = {isa = PBXBuildFile; fileRef = 045EF861241BA81000032B22 /* TKOpenPlugin60032.m */; };
		04652354212E84C100F8C0D0 /* TKSignatureController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0465234E212E84C000F8C0D0 /* TKSignatureController.m */; };
		04652355212E84C100F8C0D0 /* TKOpenPlugin60022.m in Sources */ = {isa = PBXBuildFile; fileRef = 04652350212E84C000F8C0D0 /* TKOpenPlugin60022.m */; };
		04652356212E84C100F8C0D0 /* TKTouchView.m in Sources */ = {isa = PBXBuildFile; fileRef = 04652353212E84C000F8C0D0 /* TKTouchView.m */; };
		0472CA8623FB9DD4000B5EB7 /* TKOpenPlugin60007.m in Sources */ = {isa = PBXBuildFile; fileRef = 0472CA8123FB9DD4000B5EB7 /* TKOpenPlugin60007.m */; };
		0472CA8B23FB9EC4000B5EB7 /* TKLiveFaceViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0472CA8A23FB9EC4000B5EB7 /* TKLiveFaceViewController.m */; };
		0472CA8E23FBA05F000B5EB7 /* TKLiveFaceView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0472CA8C23FBA05F000B5EB7 /* TKLiveFaceView.m */; };
		047AF9682137BF5E003B1366 /* version.txt in Resources */ = {isa = PBXBuildFile; fileRef = 047AF9672137BF5D003B1366 /* version.txt */; };
		048CB77924398BA70050B550 /* OpenInfo.xml in Resources */ = {isa = PBXBuildFile; fileRef = 048CB77824398BA70050B550 /* OpenInfo.xml */; };
		048DC9952407E66400A6F3EC /* TKOpenPlugin60030.m in Sources */ = {isa = PBXBuildFile; fileRef = 048DC9912407E66400A6F3EC /* TKOpenPlugin60030.m */; };
		048DC9962407E66400A6F3EC /* TKOrdinaryOneVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 048DC9942407E66400A6F3EC /* TKOrdinaryOneVideoView.m */; };
		048DC9992407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 048DC9982407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m */; };
		049A8350211ECD6B00AA2048 /* TKAsset.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 049A82E6211ECD6A00AA2048 /* TKAsset.bundle */; };
		049A8359211ECD6B00AA2048 /* libcrypto.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 049A834D211ECD6B00AA2048 /* libcrypto.a */; };
		049A835A211ECD6B00AA2048 /* libssl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 049A834E211ECD6B00AA2048 /* libssl.a */; };
		04D9604922C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml in Resources */ = {isa = PBXBuildFile; fileRef = 04D9604822C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml */; };
		18034324291CD426002C8E2C /* TKPlayerToolView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18034323291CD426002C8E2C /* TKPlayerToolView.m */; };
		18138943244B269A007F96FA /* TKOpenPlugin60037.m in Sources */ = {isa = PBXBuildFile; fileRef = 1813893E244B269A007F96FA /* TKOpenPlugin60037.m */; };
		18138944244B269A007F96FA /* TKTakeBankPhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18138941244B269A007F96FA /* TKTakeBankPhotoViewController.m */; };
		181D4CA1286E94F600679EB3 /* TKVideoAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 181D4CA0286E94F600679EB3 /* TKVideoAlertView.m */; };
		182CA7DE25BA6B820084C9F8 /* TKOpenPlugin60044.m in Sources */ = {isa = PBXBuildFile; fileRef = 182CA7DD25BA6B820084C9F8 /* TKOpenPlugin60044.m */; };
		182F804725DFCDC900505CDE /* TKOpenPlugin60049.m in Sources */ = {isa = PBXBuildFile; fileRef = 182F804625DFCDC900505CDE /* TKOpenPlugin60049.m */; };
		1876A14E2949CF57007F0500 /* TKTakeIDPhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1876A14D2949CF57007F0500 /* TKTakeIDPhotoViewController.m */; };
		1878E03D2888F1E200C25151 /* AIPIFlyMSC.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1878E03C2888F1E200C25151 /* AIPIFlyMSC.framework */; };
		187942C8255CD0AC0067C701 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 187942C7255CD0AC0067C701 /* AdSupport.framework */; };
		187CC37228F15C5E003442D6 /* TKOpenViewStyleHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 187CC37128F15C5E003442D6 /* TKOpenViewStyleHelper.m */; };
		187F35FB26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F35FA26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m */; };
		187F360026AAB4530046E9E3 /* TKFaceImageLandscapeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F35FF26AAB4530046E9E3 /* TKFaceImageLandscapeView.m */; };
		1895FCB326959A9B00513E5F /* TKOpenPrivacyAgreementView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1895FCB126959A9B00513E5F /* TKOpenPrivacyAgreementView.m */; };
		189F778926D8E6AE00F4089E /* TTTAttributedLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 189F778826D8E6AE00F4089E /* TTTAttributedLabel.m */; };
		18BBEF4F28A24AC100A26825 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 18BBEF4E28A24AC100A26825 /* WebKit.framework */; };
		18C3191725F74AAD003B36C7 /* TKSpeechSynthesisManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18C318F425F74AAD003B36C7 /* TKSpeechSynthesisManager.m */; };
		18C3191B25F74AAD003B36C7 /* TKSpeechRecognizeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18C318FE25F74AAD003B36C7 /* TKSpeechRecognizeManager.m */; };
		18C3191F25F74AAD003B36C7 /* TKOneWayVideoViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 18C3190A25F74AAD003B36C7 /* TKOneWayVideoViewController.mm */; };
		18C3192025F74AAD003B36C7 /* TKOpenPlugin60026.m in Sources */ = {isa = PBXBuildFile; fileRef = 18C3190B25F74AAD003B36C7 /* TKOpenPlugin60026.m */; };
		18C3192125F74AAD003B36C7 /* TKOneWayVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18C3190D25F74AAD003B36C7 /* TKOneWayVideoEndView.m */; };
		18C3192225F74AAD003B36C7 /* TKOneWayVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18C3190E25F74AAD003B36C7 /* TKOneWayVideoView.m */; };
		18C46C37291A54A10076BAC0 /* TKChatTokenHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 18C46C36291A54A10076BAC0 /* TKChatTokenHelper.m */; };
		18F129EB25FEF96000F26F5C /* TKFaceDetectManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18F129E925FEF96000F26F5C /* TKFaceDetectManager.m */; };
		18F129F825FF231600F26F5C /* TKOpenTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18F129F725FF231600F26F5C /* TKOpenTipView.m */; };
		18F12A1425FF24E000F26F5C /* TKOneWayVideoAlertTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18F12A1125FF24E000F26F5C /* TKOneWayVideoAlertTipView.m */; };
		18FF460E28E1B7AE0026440D /* SenseID_Liveness_Silent.lic in Resources */ = {isa = PBXBuildFile; fileRef = 18FF460B28E1B7AE0026440D /* SenseID_Liveness_Silent.lic */; };
		18FF460F28E1B7AE0026440D /* STLivenessModel.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 18FF460C28E1B7AE0026440D /* STLivenessModel.bundle */; };
		33294B122A0A469F00A72052 /* TChat.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 33294B102A0A469800A72052 /* TChat.framework */; };
		33294B132A0A469F00A72052 /* TChat.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 33294B102A0A469800A72052 /* TChat.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		33575EB72D2E2553009B7B83 /* TKSmartVirtualManViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33575EAD2D2E2553009B7B83 /* TKSmartVirtualManViewController.m */; };
		33575EB82D2E2553009B7B83 /* TKSmartVirtualManVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33575EB02D2E2553009B7B83 /* TKSmartVirtualManVideoEndView.m */; };
		33575EB92D2E2553009B7B83 /* TKSmartVirtualManVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33575EB22D2E2553009B7B83 /* TKSmartVirtualManVideoView.m */; };
		33575EBA2D2E2553009B7B83 /* TKOpenPlugin60072.m in Sources */ = {isa = PBXBuildFile; fileRef = 33575EB52D2E2553009B7B83 /* TKOpenPlugin60072.m */; };
		33575EBD2D2E26D3009B7B83 /* TKChatVideoRecordManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33575EBC2D2E26D3009B7B83 /* TKChatVideoRecordManager.m */; };
		338924672AB825B3005C6B5C /* TKVideoRecordManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 338924662AB825B3005C6B5C /* TKVideoRecordManager.mm */; };
		3389246D2AB825B9005C6B5C /* TKSampleBufferConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = 338924692AB825B9005C6B5C /* TKSampleBufferConverter.m */; };
		338924842AB8261E005C6B5C /* TKReadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 338924822AB8261E005C6B5C /* TKReadingView.m */; };
		3389248A2AB827AD005C6B5C /* TKringBuf.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 338924892AB827AD005C6B5C /* TKringBuf.cpp */; };
		3389248D2AB827FF005C6B5C /* TKNLSPlayAudio.mm in Sources */ = {isa = PBXBuildFile; fileRef = 3389248B2AB827FF005C6B5C /* TKNLSPlayAudio.mm */; };
		338DF1CB2B038104005DFCD1 /* AnyChatSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 338DF1C52B038104005DFCD1 /* AnyChatSDK.framework */; };
		33B365392D375AA40078392A /* TKChatLocalVideoRecordManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 33B365382D375AA40078392A /* TKChatLocalVideoRecordManager.mm */; };
		33B3654A2D375AB80078392A /* TKNewOneWayPlayerControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B365402D375AB80078392A /* TKNewOneWayPlayerControlView.m */; };
		33B3654B2D375AB80078392A /* TKOpenPlugin600261.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B365482D375AB80078392A /* TKOpenPlugin600261.m */; };
		33B3654C2D375AB80078392A /* TKNewOneWayVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B3653B2D375AB80078392A /* TKNewOneWayVideoViewController.m */; };
		33B3654D2D375AB80078392A /* TKNewOneWayLandScapeControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B3653E2D375AB80078392A /* TKNewOneWayLandScapeControlView.m */; };
		33B3654E2D375AB80078392A /* TKNewOneWayVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B365432D375AB80078392A /* TKNewOneWayVideoEndView.m */; };
		33B3654F2D375AB80078392A /* TKNewOneWayVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B365452D375AB80078392A /* TKNewOneWayVideoView.m */; };
		33C502B32A0A4D890050DB0C /* STLivenessDetector.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 33C502B12A0A4D860050DB0C /* STLivenessDetector.xcframework */; };
		33C502B42A0A4D890050DB0C /* STLivenessDetector.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 33C502B12A0A4D860050DB0C /* STLivenessDetector.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		33CB8E8C2DA67FF6000EA7C4 /* TKZFKVOController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E512DA67FF6000EA7C4 /* TKZFKVOController.m */; };
		33CB8E8D2DA67FF6000EA7C4 /* UIScrollView+TKZFPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E342DA67FF6000EA7C4 /* UIScrollView+TKZFPlayer.m */; };
		33CB8E8E2DA67FF6000EA7C4 /* TKZFPortraitViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E492DA67FF6000EA7C4 /* TKZFPortraitViewController.m */; };
		33CB8E8F2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E392DA67FF6000EA7C4 /* TKZFLandscapeRotationManager.m */; };
		33CB8E902DA67FF6000EA7C4 /* UIView+TKZFFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E362DA67FF6000EA7C4 /* UIView+TKZFFrame.m */; };
		33CB8E912DA67FF6000EA7C4 /* TKZFPlayerNotification.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E572DA67FF6000EA7C4 /* TKZFPlayerNotification.m */; };
		33CB8E922DA67FF6000EA7C4 /* TKZFLandscapeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E3F2DA67FF6000EA7C4 /* TKZFLandscapeViewController.m */; };
		33CB8E932DA67FF6000EA7C4 /* TKZFSmallFloatControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E672DA67FF6000EA7C4 /* TKZFSmallFloatControlView.m */; };
		33CB8E942DA67FF6000EA7C4 /* TKZFNetworkSpeedMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E532DA67FF6000EA7C4 /* TKZFNetworkSpeedMonitor.m */; };
		33CB8E952DA67FF6000EA7C4 /* TKZFFloatView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E652DA67FF6000EA7C4 /* TKZFFloatView.m */; };
		33CB8E962DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS15.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E3B2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS15.m */; };
		33CB8E972DA67FF6000EA7C4 /* TKZFPlayerStatusBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E7C2DA67FF6000EA7C4 /* TKZFPlayerStatusBar.m */; };
		33CB8E982DA67FF6000EA7C4 /* TKZFPersentInteractiveTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E472DA67FF6000EA7C4 /* TKZFPersentInteractiveTransition.m */; };
		33CB8E992DA67FF6000EA7C4 /* TKPlayerControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E5E2DA67FF6000EA7C4 /* TKPlayerControlView.m */; };
		33CB8E9A2DA67FF6000EA7C4 /* TKZFReachabilityManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E592DA67FF6000EA7C4 /* TKZFReachabilityManager.m */; };
		33CB8E9B2DA67FF6000EA7C4 /* TKFragmentVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E6C2DA67FF6000EA7C4 /* TKFragmentVideoView.m */; };
		33CB8E9C2DA67FF6000EA7C4 /* TKZFUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E5B2DA67FF6000EA7C4 /* TKZFUtilities.m */; };
		33CB8E9D2DA67FF6000EA7C4 /* TKPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E852DA67FF6000EA7C4 /* TKPlayer.m */; };
		33CB8E9E2DA67FF6000EA7C4 /* TKZFPresentTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E4B2DA67FF6000EA7C4 /* TKZFPresentTransition.m */; };
		33CB8E9F2DA67FF6000EA7C4 /* TKZFLandscapeWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E432DA67FF6000EA7C4 /* TKZFLandscapeWindow.m */; };
		33CB8EA02DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS16.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E3D2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS16.m */; };
		33CB8EA12DA67FF6000EA7C4 /* TKZFPlayerLogManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E552DA67FF6000EA7C4 /* TKZFPlayerLogManager.m */; };
		33CB8EA22DA67FF6000EA7C4 /* TKZFPlayerController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E882DA67FF6000EA7C4 /* TKZFPlayerController.m */; };
		33CB8EA32DA67FF6000EA7C4 /* TKZFSpeedLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E732DA67FF6000EA7C4 /* TKZFSpeedLoadingView.m */; };
		33CB8EA42DA67FF6000EA7C4 /* TKSpeedSelectView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E792DA67FF6000EA7C4 /* TKSpeedSelectView.m */; };
		33CB8EA52DA67FF6000EA7C4 /* TKZFVolumeBrightnessView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E7F2DA67FF6000EA7C4 /* TKZFVolumeBrightnessView.m */; };
		33CB8EA62DA67FF6000EA7C4 /* TKZFPortraitControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E622DA67FF6000EA7C4 /* TKZFPortraitControlView.m */; };
		33CB8EA72DA67FF6000EA7C4 /* TKZFLandScapeControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E602DA67FF6000EA7C4 /* TKZFLandScapeControlView.m */; };
		33CB8EA82DA67FF6000EA7C4 /* TKFragmentTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E6A2DA67FF6000EA7C4 /* TKFragmentTableViewCell.m */; };
		33CB8EA92DA67FF6000EA7C4 /* TKVideoFragmentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E6E2DA67FF6000EA7C4 /* TKVideoFragmentModel.m */; };
		33CB8EAA2DA67FF6000EA7C4 /* TKZFPlayerGestureControl.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E8A2DA67FF6000EA7C4 /* TKZFPlayerGestureControl.m */; };
		33CB8EAB2DA67FF6000EA7C4 /* TKZFLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E712DA67FF6000EA7C4 /* TKZFLoadingView.m */; };
		33CB8EAC2DA67FF6000EA7C4 /* TKZFLandscapeViewController_iOS15.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E412DA67FF6000EA7C4 /* TKZFLandscapeViewController_iOS15.m */; };
		33CB8EAD2DA67FF6000EA7C4 /* TKZFOrientationObserver.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E452DA67FF6000EA7C4 /* TKZFOrientationObserver.m */; };
		33CB8EAE2DA67FF6000EA7C4 /* TKZFSliderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E762DA67FF6000EA7C4 /* TKZFSliderView.m */; };
		33CB8EAF2DA67FF6000EA7C4 /* UIImageView+TKZFCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E322DA67FF6000EA7C4 /* UIImageView+TKZFCache.m */; };
		33CB8EB02DA67FF6000EA7C4 /* TKZFPlayerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E822DA67FF6000EA7C4 /* TKZFPlayerView.m */; };
		33EA39DC2B4D24000038FBD6 /* TKPlayRangeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 33EA39DB2B4D24000038FBD6 /* TKPlayRangeModel.m */; };
		33F248D029713BA900547805 /* TKCardPreview.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F248CF29713BA900547805 /* TKCardPreview.m */; };
		33F50EF82A92F52900246237 /* TKOpenPlugin60005.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50ECC2A92F52900246237 /* TKOpenPlugin60005.m */; };
		33F50EF92A92F52900246237 /* TKSmartTwoVideoController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50ECE2A92F52900246237 /* TKSmartTwoVideoController.m */; };
		33F50F012A92F52A00246237 /* TKChatService.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50ED82A92F52900246237 /* TKChatService.m */; };
		33F50F022A92F52A00246237 /* TKVideoWitnessViewController+CommonKit.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EDD2A92F52900246237 /* TKVideoWitnessViewController+CommonKit.m */; };
		33F50F032A92F52A00246237 /* TKVideoWitnessViewController+AnyChat.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EDE2A92F52900246237 /* TKVideoWitnessViewController+AnyChat.m */; };
		33F50F042A92F52A00246237 /* TKVideoWitnessViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EDF2A92F52900246237 /* TKVideoWitnessViewController.m */; };
		33F50F052A92F52A00246237 /* TKOpenVideoChatView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EE62A92F52900246237 /* TKOpenVideoChatView.m */; };
		33F50F062A92F52A00246237 /* TKOpenQueueView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EE72A92F52900246237 /* TKOpenQueueView.m */; };
		33F50F072A92F52A00246237 /* TKVideoReadAgreeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EE82A92F52900246237 /* TKVideoReadAgreeView.m */; };
		33F50F082A92F52A00246237 /* TKVideoMsgTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EE92A92F52900246237 /* TKVideoMsgTableViewCell.m */; };
		33F50F092A92F52A00246237 /* TKOpenPlugin60034.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EED2A92F52900246237 /* TKOpenPlugin60034.m */; };
		33F50F0A2A92F52A00246237 /* TKDirectVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EEF2A92F52900246237 /* TKDirectVideoViewController.m */; };
		33F50F0B2A92F52A00246237 /* TKDirectVideoChatView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EF32A92F52900246237 /* TKDirectVideoChatView.m */; };
		33F50F0C2A92F52A00246237 /* TKDirectVideoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50EF72A92F52900246237 /* TKDirectVideoModel.m */; };
		33F50F142A92F60200246237 /* page_bg.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 33F50F0D2A92F60200246237 /* page_bg.jpg */; };
		33F50F152A92F60200246237 /* TKAnyChatViewController4.xib in Resources */ = {isa = PBXBuildFile; fileRef = 33F50F0E2A92F60200246237 /* TKAnyChatViewController4.xib */; };
		33F50F162A92F60200246237 /* kefu_bg_img.png in Resources */ = {isa = PBXBuildFile; fileRef = 33F50F0F2A92F60200246237 /* kefu_bg_img.png */; };
		33F50F172A92F60200246237 /* chat_cancel_btn.png in Resources */ = {isa = PBXBuildFile; fileRef = 33F50F102A92F60200246237 /* chat_cancel_btn.png */; };
		33F50F182A92F60200246237 /* TKAnyChatViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 33F50F112A92F60200246237 /* TKAnyChatViewController.xib */; };
		33F50F192A92F60200246237 /* TKVideoWitnessViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 33F50F122A92F60200246237 /* TKVideoWitnessViewController.xib */; };
		33F50F1A2A92F60200246237 /* tk_video_icon_iphone.png in Resources */ = {isa = PBXBuildFile; fileRef = 33F50F132A92F60200246237 /* tk_video_icon_iphone.png */; };
		33F50F2A2A92F8C400246237 /* TKStatisticEventHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50F292A92F8C400246237 /* TKStatisticEventHelper.m */; };
		33F50F322A92F8D200246237 /* TKAnyChatSmartTwoVideoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50F2B2A92F8D200246237 /* TKAnyChatSmartTwoVideoManager.m */; };
		33F50F332A92F8D200246237 /* TKTChatSmartTwoVideoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50F2F2A92F8D200246237 /* TKTChatSmartTwoVideoManager.m */; };
		33F50F342A92F8D200246237 /* TKSmartTwoVideoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F50F312A92F8D200246237 /* TKSmartTwoVideoManager.m */; };
		B30DC50A1BCFC873007072FB /* Configuration.xml in Resources */ = {isa = PBXBuildFile; fileRef = B30DC5061BCFC873007072FB /* Configuration.xml */; };
		B30DC50B1BCFC873007072FB /* OpenPlugin.xml in Resources */ = {isa = PBXBuildFile; fileRef = B30DC5071BCFC873007072FB /* OpenPlugin.xml */; };
		B30DC50C1BCFC873007072FB /* SystemPlugin.xml in Resources */ = {isa = PBXBuildFile; fileRef = B30DC5081BCFC873007072FB /* SystemPlugin.xml */; };
		B30FD8A71EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m in Sources */ = {isa = PBXBuildFile; fileRef = B30FD8A61EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m */; };
		B32C870C1D87E96E00373C19 /* config.plist in Resources */ = {isa = PBXBuildFile; fileRef = B32C870B1D87E96E00373C19 /* config.plist */; };
		B3386D6B1F271753006EF60A /* TKAVCaptureManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B3386D6A1F271753006EF60A /* TKAVCaptureManager.m */; };
		B33FDC101F692BCB00C644F2 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = B33FDC0F1F692BCB00C644F2 /* libxml2.tbd */; };
		B33FDC121F692BE400C644F2 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = B33FDC111F692BE400C644F2 /* libresolv.tbd */; };
		B3556A8E1DDBF96B00C76E7C /* TKOpenPlugin60000.m in Sources */ = {isa = PBXBuildFile; fileRef = B35569F71DDBF96B00C76E7C /* TKOpenPlugin60000.m */; };
		B3556A8F1DDBF96B00C76E7C /* TKOpenPlugin60001.m in Sources */ = {isa = PBXBuildFile; fileRef = B35569FA1DDBF96B00C76E7C /* TKOpenPlugin60001.m */; };
		B3556A901DDBF96B00C76E7C /* TKOpenPlugin60002.m in Sources */ = {isa = PBXBuildFile; fileRef = B35569FD1DDBF96B00C76E7C /* TKOpenPlugin60002.m */; };
		B3556A911DDBF96B00C76E7C /* MTakeCardViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A001DDBF96B00C76E7C /* MTakeCardViewController.m */; };
		B3556A921DDBF96B00C76E7C /* TKOpenPlugin60003.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A031DDBF96B00C76E7C /* TKOpenPlugin60003.m */; };
		B3556A931DDBF96B00C76E7C /* TKOpenPlugin60004.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A061DDBF96B00C76E7C /* TKOpenPlugin60004.m */; };
		B3556AAE1DDBF96B00C76E7C /* TKOpenPlugin60010.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A411DDBF96B00C76E7C /* TKOpenPlugin60010.m */; };
		B3556AB01DDBF96B00C76E7C /* MTakeBigPictureViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A471DDBF96B00C76E7C /* MTakeBigPictureViewController.m */; };
		B3556AB11DDBF96B00C76E7C /* MTakePhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A491DDBF96B00C76E7C /* MTakePhotoViewController.m */; };
		B3556AB21DDBF96B00C76E7C /* TKOpenPlugin60013.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A4B1DDBF96B00C76E7C /* TKOpenPlugin60013.m */; };
		B3556AD91DDBF96B00C76E7C /* TKOpenPlugin60017.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A8A1DDBF96B00C76E7C /* TKOpenPlugin60017.m */; };
		B3556ADA1DDBF96B00C76E7C /* TKOpenPlugin60018.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A8D1DDBF96B00C76E7C /* TKOpenPlugin60018.m */; };
		B35EBF3B1C4393770059F885 /* MNavViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B35EBF3A1C4393770059F885 /* MNavViewController.m */; };
		B35EBF431C43942F0059F885 /* MDRadialProgressLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = B35EBF3E1C43942F0059F885 /* MDRadialProgressLabel.m */; };
		B35EBF441C43942F0059F885 /* MDRadialProgressTheme.m in Sources */ = {isa = PBXBuildFile; fileRef = B35EBF401C43942F0059F885 /* MDRadialProgressTheme.m */; };
		B35EBF451C43942F0059F885 /* MDRadialProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = B35EBF421C43942F0059F885 /* MDRadialProgressView.m */; };
		B379D393206CD57000D1440E /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B379D392206CD57000D1440E /* CoreTelephony.framework */; };
		B37F84291BE365230016F93D /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B37F84281BE365230016F93D /* OpenGLES.framework */; };
		B384CDCC1B8177B400AFD817 /* TKButton.m in Sources */ = {isa = PBXBuildFile; fileRef = B384CDC91B8177B400AFD817 /* TKButton.m */; };
		B384CDCD1B8177B400AFD817 /* TKCameraTools.m in Sources */ = {isa = PBXBuildFile; fileRef = B384CDCB1B8177B400AFD817 /* TKCameraTools.m */; };
		B386BA0E1F947E6C001CBD13 /* TKOpenPlugin60099.m in Sources */ = {isa = PBXBuildFile; fileRef = B386BA0D1F947E6C001CBD13 /* TKOpenPlugin60099.m */; };
		B38D07B71FC402EC0013DA81 /* TKMAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B38D07B51FC402EC0013DA81 /* TKMAlertViewController.m */; };
		B39BADD01B4D524F00F071D4 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B39BADCF1B4D524F00F071D4 /* CoreLocation.framework */; };
		B39E930F208F0532006EC8A3 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B39E930E208F0532006EC8A3 /* CoreMotion.framework */; };
		B39E9311208F054A006EC8A3 /* MessageUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B39E9310208F054A006EC8A3 /* MessageUI.framework */; };
		B39E9313208F05CD006EC8A3 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B39E9312208F05CD006EC8A3 /* Accelerate.framework */; };
		B3A2DD331EA461A700B18842 /* TKCommonUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = B3A2DD321EA461A700B18842 /* TKCommonUtil.m */; };
		B3AB8AFA1C15708900571BE6 /* TKOpenResource.bundle in Resources */ = {isa = PBXBuildFile; fileRef = B3AB8AF91C15708900571BE6 /* TKOpenResource.bundle */; };
		B3B108841B44DF2800546D96 /* libiconv.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B3B108831B44DF2800546D96 /* libiconv.dylib */; };
		B3C6ADC71C92ADDD00FE95F0 /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3C6ADC61C92ADDD00FE95F0 /* AssetsLibrary.framework */; };
		B3C6ADC91C92ADEA00FE95F0 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3C6ADC81C92ADEA00FE95F0 /* CoreMedia.framework */; };
		B3E1B3121B8DB81400CDD258 /* Resources in Resources */ = {isa = PBXBuildFile; fileRef = B3E1B2EF1B8D9FEF00CDD258 /* Resources */; };
		B3FACBB71AFDDF0E0088CDF1 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = B3FACBB61AFDDF0E0088CDF1 /* main.m */; };
		B3FACBBA1AFDDF0E0088CDF1 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = B3FACBB91AFDDF0E0088CDF1 /* AppDelegate.m */; };
		B3FACBBD1AFDDF0E0088CDF1 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3FACBBC1AFDDF0E0088CDF1 /* ViewController.m */; };
		B3FACBC21AFDDF0E0088CDF1 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B3FACBC11AFDDF0E0088CDF1 /* Images.xcassets */; };
		B3FACBDD1AFDE2D10088CDF1 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBDC1AFDE2D10088CDF1 /* UIKit.framework */; };
		B3FACBDF1AFDE2E50088CDF1 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBDE1AFDE2E50088CDF1 /* QuartzCore.framework */; };
		B3FACBE11AFDE2F40088CDF1 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBE01AFDE2F40088CDF1 /* Security.framework */; };
		B3FACBE51AFDE30B0088CDF1 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBE41AFDE30B0088CDF1 /* CFNetwork.framework */; };
		B3FACBE71AFDE31A0088CDF1 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBE61AFDE31A0088CDF1 /* SystemConfiguration.framework */; };
		B3FACBE91AFDE33A0088CDF1 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBE81AFDE33A0088CDF1 /* Foundation.framework */; };
		B3FACBEB1AFDE3460088CDF1 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBEA1AFDE3460088CDF1 /* CoreGraphics.framework */; };
		B3FACBEF1AFDE3750088CDF1 /* libsqlite3.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBEE1AFDE3750088CDF1 /* libsqlite3.dylib */; };
		B3FACBF11AFDE3800088CDF1 /* libz.1.2.5.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBF01AFDE3800088CDF1 /* libz.1.2.5.dylib */; };
		B3FACBF31AFDE3940088CDF1 /* libc++.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBF21AFDE3940088CDF1 /* libc++.dylib */; };
		B3FCF6131BAFB2F3009AB7C0 /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = B3FACBC31AFDDF0E0088CDF1 /* LaunchScreen.xib */; };
		B3FEEB361B845D6D00468924 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FEEB351B845D6D00468924 /* AVFoundation.framework */; };
		B3FF6E3A1EC5B2A0009FAF95 /* tk_open in Resources */ = {isa = PBXBuildFile; fileRef = B3FF6E391EC5B2A0009FAF95 /* tk_open */; };
		BE4F171B2E1D265D009DCF6C /* TKTTSCloudPlayerManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = BE4F17192E1D265D009DCF6C /* TKTTSCloudPlayerManager.mm */; };
		BE4F171C2E1D265D009DCF6C /* TKSubtitlesRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = BE4F17162E1D265D009DCF6C /* TKSubtitlesRenderer.m */; };
		BE6CD0431B5000B7003DCF98 /* TKOpenController.m in Sources */ = {isa = PBXBuildFile; fileRef = BE6CCFB71B5000B6003DCF98 /* TKOpenController.m */; };
		BE6CD0441B5000B7003DCF98 /* TKOpenAccountService.m in Sources */ = {isa = PBXBuildFile; fileRef = BE6CCFBB1B5000B6003DCF98 /* TKOpenAccountService.m */; };
		BE6CD0471B5000B7003DCF98 /* YLProgressBar.m in Sources */ = {isa = PBXBuildFile; fileRef = BE6CCFC51B5000B6003DCF98 /* YLProgressBar.m */; };
		************************ /* TKBaseVideoRecordEndLandscapeView.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* TKBaseVideoRecordEndLandscapeView.m */; };
		************************ /* TKSmartQuestionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* TKSmartQuestionModel.m */; };
		************************ /* TKBaseVideoRecordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* TKBaseVideoRecordViewController.m */; };
		************************ /* TKVideoWitnessViewController+TChat.m in Sources */ = {isa = PBXBuildFile; fileRef = B32E91C81F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.m */; };
		DC9BF92F2617249200A37295 /* TKiflySpeechSynthesisManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DC9BF92E2617249200A37295 /* TKiflySpeechSynthesisManager.m */; };
		DC9BF93A261C06AB00A37295 /* TKiflySpeechRecognizeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DC9BF939261C06AB00A37295 /* TKiflySpeechRecognizeManager.m */; };
		DCBFCEFD2A122B5600418EFE /* www in Resources */ = {isa = PBXBuildFile; fileRef = DCBFCEFC2A122B5600418EFE /* www */; };
		DCC262C2291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC262C1291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m */; };
		DCC7E86B262D7134006D378F /* TKTempService.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC7E868262D7133006D378F /* TKTempService.m */; };
		DCCDDA1B295C2AF5002F268B /* TKFaceDectTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCCDDA1A295C2AF5002F268B /* TKFaceDectTipView.m */; };
		DCD47D3528E18E1600AE44C6 /* TKBaseVideoRecordEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCD47D3128E18E1600AE44C6 /* TKBaseVideoRecordEndView.m */; };
		DCD47D3628E18E1600AE44C6 /* TKBaseVideoRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCD47D3228E18E1600AE44C6 /* TKBaseVideoRecordView.m */; };
		DCECF967292F5EF000A802EE /* TKSVGEngine.mm in Sources */ = {isa = PBXBuildFile; fileRef = DCECF95B292F5EEF00A802EE /* TKSVGEngine.mm */; };
		DCECF968292F5EF000A802EE /* TKSVGLayer.m in Sources */ = {isa = PBXBuildFile; fileRef = DCECF95C292F5EF000A802EE /* TKSVGLayer.m */; };
		DCECF969292F5EF000A802EE /* TKSVGBezierPath.mm in Sources */ = {isa = PBXBuildFile; fileRef = DCECF95E292F5EF000A802EE /* TKSVGBezierPath.mm */; };
		DCECF96B292F5EF000A802EE /* TKSVGImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCECF964292F5EF000A802EE /* TKSVGImageView.m */; };
		DCECF96E292F628A00A802EE /* TKSVGImage.m in Sources */ = {isa = PBXBuildFile; fileRef = DCECF96D292F628A00A802EE /* TKSVGImage.m */; };
		E22996F526AABD490039F6E9 /* SelectViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = E22996F426AABD490039F6E9 /* SelectViewController.m */; };
		E22996F626AABFB90039F6E9 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B3FACBBE1AFDDF0E0088CDF1 /* Main.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B3AA8EC21EBB2B0400255737 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B3FACBA91AFDDF0E0088CDF1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B3D794591B8EA14F00768134;
			remoteInfo = build_TKOpenResource;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		04EE357922732298009EA9D1 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				33C502B42A0A4D890050DB0C /* STLivenessDetector.xcframework in Embed Frameworks */,
				33294B132A0A469F00A72052 /* TChat.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		040B88412148BC19008DBE05 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		042CE7032408DD7900B9AC15 /* TKOrdinaryOneVideoEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrdinaryOneVideoEndView.h; sourceTree = "<group>"; };
		042CE7042408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrdinaryOneVideoEndView.m; sourceTree = "<group>"; };
		043015EA23FCC811004F0C17 /* TKOpenPlugin60028.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60028.h; sourceTree = "<group>"; };
		043015EB23FCC811004F0C17 /* TKOpenPlugin60028.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60028.m; sourceTree = "<group>"; };
		043015ED23FCC936004F0C17 /* TKFaceImageViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKFaceImageViewController.h; sourceTree = "<group>"; };
		043015EE23FCC936004F0C17 /* TKFaceImageViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKFaceImageViewController.m; sourceTree = "<group>"; };
		043015F123FCCDBE004F0C17 /* TKFaceImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFaceImageView.m; sourceTree = "<group>"; };
		043015F223FCCDBE004F0C17 /* TKFaceImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFaceImageView.h; sourceTree = "<group>"; };
		043884B823B05A240009F14D /* KeyBoard.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = KeyBoard.xml; sourceTree = "<group>"; };
		0441008A23C43BD1005B9D05 /* TKFxcAccountInfoType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKFxcAccountInfoType.h; sourceTree = "<group>"; };
		0441008B23C43BD1005B9D05 /* TKFxcAccountInfoType.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKFxcAccountInfoType.m; sourceTree = "<group>"; };
		045ACEA522A4FC30004D8557 /* TKWebViewApp.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TKWebViewApp.framework; sourceTree = "<group>"; };
		045EF860241BA81000032B22 /* TKOpenPlugin60032.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60032.h; sourceTree = "<group>"; };
		045EF861241BA81000032B22 /* TKOpenPlugin60032.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60032.m; sourceTree = "<group>"; };
		0465234D212E84C000F8C0D0 /* TKSignatureController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSignatureController.h; sourceTree = "<group>"; };
		0465234E212E84C000F8C0D0 /* TKSignatureController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSignatureController.m; sourceTree = "<group>"; };
		0465234F212E84C000F8C0D0 /* TKOpenPlugin60022.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60022.h; sourceTree = "<group>"; };
		04652350212E84C000F8C0D0 /* TKOpenPlugin60022.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60022.m; sourceTree = "<group>"; };
		04652352212E84C000F8C0D0 /* TKTouchView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTouchView.h; sourceTree = "<group>"; };
		04652353212E84C000F8C0D0 /* TKTouchView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTouchView.m; sourceTree = "<group>"; };
		0472CA8023FB9DD4000B5EB7 /* TKOpenPlugin60007.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60007.h; sourceTree = "<group>"; };
		0472CA8123FB9DD4000B5EB7 /* TKOpenPlugin60007.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60007.m; sourceTree = "<group>"; };
		0472CA8923FB9EC4000B5EB7 /* TKLiveFaceViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKLiveFaceViewController.h; sourceTree = "<group>"; };
		0472CA8A23FB9EC4000B5EB7 /* TKLiveFaceViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKLiveFaceViewController.m; sourceTree = "<group>"; };
		0472CA8C23FBA05F000B5EB7 /* TKLiveFaceView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKLiveFaceView.m; sourceTree = "<group>"; };
		0472CA8D23FBA05F000B5EB7 /* TKLiveFaceView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKLiveFaceView.h; sourceTree = "<group>"; };
		047AF9672137BF5D003B1366 /* version.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = version.txt; sourceTree = "<group>"; };
		048CB77824398BA70050B550 /* OpenInfo.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = OpenInfo.xml; sourceTree = "<group>"; };
		048DC9902407E66400A6F3EC /* TKOpenPlugin60030.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60030.h; sourceTree = "<group>"; };
		048DC9912407E66400A6F3EC /* TKOpenPlugin60030.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60030.m; sourceTree = "<group>"; };
		048DC9932407E66400A6F3EC /* TKOrdinaryOneVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrdinaryOneVideoView.h; sourceTree = "<group>"; };
		048DC9942407E66400A6F3EC /* TKOrdinaryOneVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrdinaryOneVideoView.m; sourceTree = "<group>"; };
		048DC9972407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOrdinaryOneVideoViewController.h; sourceTree = "<group>"; };
		048DC9982407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOrdinaryOneVideoViewController.m; sourceTree = "<group>"; };
		049A82E6211ECD6A00AA2048 /* TKAsset.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = TKAsset.bundle; sourceTree = "<group>"; };
		049A8301211ECD6B00AA2048 /* aes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aes.h; sourceTree = "<group>"; };
		049A8302211ECD6B00AA2048 /* asn1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1.h; sourceTree = "<group>"; };
		049A8303211ECD6B00AA2048 /* asn1_mac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1_mac.h; sourceTree = "<group>"; };
		049A8304211ECD6B00AA2048 /* asn1t.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1t.h; sourceTree = "<group>"; };
		049A8305211ECD6B00AA2048 /* bio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bio.h; sourceTree = "<group>"; };
		049A8306211ECD6B00AA2048 /* blowfish.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = blowfish.h; sourceTree = "<group>"; };
		049A8307211ECD6B00AA2048 /* bn.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bn.h; sourceTree = "<group>"; };
		049A8308211ECD6B00AA2048 /* buffer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = buffer.h; sourceTree = "<group>"; };
		049A8309211ECD6B00AA2048 /* camellia.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = camellia.h; sourceTree = "<group>"; };
		049A830A211ECD6B00AA2048 /* cast.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cast.h; sourceTree = "<group>"; };
		049A830B211ECD6B00AA2048 /* cmac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cmac.h; sourceTree = "<group>"; };
		049A830C211ECD6B00AA2048 /* cms.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cms.h; sourceTree = "<group>"; };
		049A830D211ECD6B00AA2048 /* comp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = comp.h; sourceTree = "<group>"; };
		049A830E211ECD6B00AA2048 /* conf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = conf.h; sourceTree = "<group>"; };
		049A830F211ECD6B00AA2048 /* conf_api.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = conf_api.h; sourceTree = "<group>"; };
		049A8310211ECD6B00AA2048 /* crypto.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = crypto.h; sourceTree = "<group>"; };
		049A8311211ECD6B00AA2048 /* des.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = des.h; sourceTree = "<group>"; };
		049A8312211ECD6B00AA2048 /* des_old.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = des_old.h; sourceTree = "<group>"; };
		049A8313211ECD6B00AA2048 /* dh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dh.h; sourceTree = "<group>"; };
		049A8314211ECD6B00AA2048 /* dsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dsa.h; sourceTree = "<group>"; };
		049A8315211ECD6B00AA2048 /* dso.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dso.h; sourceTree = "<group>"; };
		049A8316211ECD6B00AA2048 /* dtls1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dtls1.h; sourceTree = "<group>"; };
		049A8317211ECD6B00AA2048 /* e_os2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = e_os2.h; sourceTree = "<group>"; };
		049A8318211ECD6B00AA2048 /* ebcdic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ebcdic.h; sourceTree = "<group>"; };
		049A8319211ECD6B00AA2048 /* ec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ec.h; sourceTree = "<group>"; };
		049A831A211ECD6B00AA2048 /* ecdh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ecdh.h; sourceTree = "<group>"; };
		049A831B211ECD6B00AA2048 /* ecdsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ecdsa.h; sourceTree = "<group>"; };
		049A831C211ECD6B00AA2048 /* engine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = engine.h; sourceTree = "<group>"; };
		049A831D211ECD6B00AA2048 /* err.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = err.h; sourceTree = "<group>"; };
		049A831E211ECD6B00AA2048 /* evp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = evp.h; sourceTree = "<group>"; };
		049A831F211ECD6B00AA2048 /* hmac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hmac.h; sourceTree = "<group>"; };
		049A8320211ECD6B00AA2048 /* idea.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = idea.h; sourceTree = "<group>"; };
		049A8321211ECD6B00AA2048 /* krb5_asn.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = krb5_asn.h; sourceTree = "<group>"; };
		049A8322211ECD6B00AA2048 /* kssl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = kssl.h; sourceTree = "<group>"; };
		049A8323211ECD6B00AA2048 /* lhash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lhash.h; sourceTree = "<group>"; };
		049A8324211ECD6B00AA2048 /* md4.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = md4.h; sourceTree = "<group>"; };
		049A8325211ECD6B00AA2048 /* md5.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = md5.h; sourceTree = "<group>"; };
		049A8326211ECD6B00AA2048 /* mdc2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mdc2.h; sourceTree = "<group>"; };
		049A8327211ECD6B00AA2048 /* modes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = modes.h; sourceTree = "<group>"; };
		049A8328211ECD6B00AA2048 /* obj_mac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = obj_mac.h; sourceTree = "<group>"; };
		049A8329211ECD6B00AA2048 /* objects.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = objects.h; sourceTree = "<group>"; };
		049A832A211ECD6B00AA2048 /* ocsp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ocsp.h; sourceTree = "<group>"; };
		049A832B211ECD6B00AA2048 /* opensslconf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opensslconf.h; sourceTree = "<group>"; };
		049A832C211ECD6B00AA2048 /* opensslv.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opensslv.h; sourceTree = "<group>"; };
		049A832D211ECD6B00AA2048 /* ossl_typ.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ossl_typ.h; sourceTree = "<group>"; };
		049A832E211ECD6B00AA2048 /* pem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pem.h; sourceTree = "<group>"; };
		049A832F211ECD6B00AA2048 /* pem2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pem2.h; sourceTree = "<group>"; };
		049A8330211ECD6B00AA2048 /* pkcs12.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pkcs12.h; sourceTree = "<group>"; };
		049A8331211ECD6B00AA2048 /* pkcs7.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pkcs7.h; sourceTree = "<group>"; };
		049A8332211ECD6B00AA2048 /* pqueue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pqueue.h; sourceTree = "<group>"; };
		049A8333211ECD6B00AA2048 /* rand.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rand.h; sourceTree = "<group>"; };
		049A8334211ECD6B00AA2048 /* rc2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rc2.h; sourceTree = "<group>"; };
		049A8335211ECD6B00AA2048 /* rc4.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rc4.h; sourceTree = "<group>"; };
		049A8336211ECD6B00AA2048 /* ripemd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ripemd.h; sourceTree = "<group>"; };
		049A8337211ECD6B00AA2048 /* rsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rsa.h; sourceTree = "<group>"; };
		049A8338211ECD6B00AA2048 /* safestack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = safestack.h; sourceTree = "<group>"; };
		049A8339211ECD6B00AA2048 /* seed.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = seed.h; sourceTree = "<group>"; };
		049A833A211ECD6B00AA2048 /* sha.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sha.h; sourceTree = "<group>"; };
		049A833B211ECD6B00AA2048 /* srp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = srp.h; sourceTree = "<group>"; };
		049A833C211ECD6B00AA2048 /* srtp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = srtp.h; sourceTree = "<group>"; };
		049A833D211ECD6B00AA2048 /* ssl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl.h; sourceTree = "<group>"; };
		049A833E211ECD6B00AA2048 /* ssl2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl2.h; sourceTree = "<group>"; };
		049A833F211ECD6B00AA2048 /* ssl23.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl23.h; sourceTree = "<group>"; };
		049A8340211ECD6B00AA2048 /* ssl3.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl3.h; sourceTree = "<group>"; };
		049A8341211ECD6B00AA2048 /* stack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = stack.h; sourceTree = "<group>"; };
		049A8342211ECD6B00AA2048 /* symhacks.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = symhacks.h; sourceTree = "<group>"; };
		049A8343211ECD6B00AA2048 /* tls1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = tls1.h; sourceTree = "<group>"; };
		049A8344211ECD6B00AA2048 /* ts.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ts.h; sourceTree = "<group>"; };
		049A8345211ECD6B00AA2048 /* txt_db.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = txt_db.h; sourceTree = "<group>"; };
		049A8346211ECD6B00AA2048 /* ui.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ui.h; sourceTree = "<group>"; };
		049A8347211ECD6B00AA2048 /* ui_compat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ui_compat.h; sourceTree = "<group>"; };
		049A8348211ECD6B00AA2048 /* whrlpool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = whrlpool.h; sourceTree = "<group>"; };
		049A8349211ECD6B00AA2048 /* x509.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509.h; sourceTree = "<group>"; };
		049A834A211ECD6B00AA2048 /* x509_vfy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509_vfy.h; sourceTree = "<group>"; };
		049A834B211ECD6B00AA2048 /* x509v3.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509v3.h; sourceTree = "<group>"; };
		049A834D211ECD6B00AA2048 /* libcrypto.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libcrypto.a; sourceTree = "<group>"; };
		049A834E211ECD6B00AA2048 /* libssl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libssl.a; sourceTree = "<group>"; };
		04D9604822C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = Configuration_Open_BuriedPoint.xml; sourceTree = "<group>"; };
		18034322291CD426002C8E2C /* TKPlayerToolView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKPlayerToolView.h; sourceTree = "<group>"; };
		18034323291CD426002C8E2C /* TKPlayerToolView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKPlayerToolView.m; sourceTree = "<group>"; };
		1813893E244B269A007F96FA /* TKOpenPlugin60037.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60037.m; sourceTree = "<group>"; };
		18138940244B269A007F96FA /* TKTakeBankPhotoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTakeBankPhotoViewController.h; sourceTree = "<group>"; };
		18138941244B269A007F96FA /* TKTakeBankPhotoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTakeBankPhotoViewController.m; sourceTree = "<group>"; };
		18138942244B269A007F96FA /* TKOpenPlugin60037.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60037.h; sourceTree = "<group>"; };
		181D4C9F286E94F600679EB3 /* TKVideoAlertView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoAlertView.h; sourceTree = "<group>"; };
		181D4CA0286E94F600679EB3 /* TKVideoAlertView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoAlertView.m; sourceTree = "<group>"; };
		182CA7DC25BA6B820084C9F8 /* TKOpenPlugin60044.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60044.h; sourceTree = "<group>"; };
		182CA7DD25BA6B820084C9F8 /* TKOpenPlugin60044.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60044.m; sourceTree = "<group>"; };
		182F804525DFCDC900505CDE /* TKOpenPlugin60049.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60049.h; sourceTree = "<group>"; };
		182F804625DFCDC900505CDE /* TKOpenPlugin60049.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60049.m; sourceTree = "<group>"; };
		1876A14C2949CF57007F0500 /* TKTakeIDPhotoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTakeIDPhotoViewController.h; sourceTree = "<group>"; };
		1876A14D2949CF57007F0500 /* TKTakeIDPhotoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTakeIDPhotoViewController.m; sourceTree = "<group>"; };
		1878E03C2888F1E200C25151 /* AIPIFlyMSC.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = AIPIFlyMSC.framework; sourceTree = "<group>"; };
		187942C7255CD0AC0067C701 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		187CC37028F15C5E003442D6 /* TKOpenViewStyleHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenViewStyleHelper.h; sourceTree = "<group>"; };
		187CC37128F15C5E003442D6 /* TKOpenViewStyleHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenViewStyleHelper.m; sourceTree = "<group>"; };
		187F35F926AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFaceImageLandscapeViewController.h; sourceTree = "<group>"; };
		187F35FA26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFaceImageLandscapeViewController.m; sourceTree = "<group>"; };
		187F35FE26AAB4530046E9E3 /* TKFaceImageLandscapeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFaceImageLandscapeView.h; sourceTree = "<group>"; };
		187F35FF26AAB4530046E9E3 /* TKFaceImageLandscapeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFaceImageLandscapeView.m; sourceTree = "<group>"; };
		1895FCAF26959A9B00513E5F /* TKOpenPrivacyAgreementView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPrivacyAgreementView.h; sourceTree = "<group>"; };
		1895FCB126959A9B00513E5F /* TKOpenPrivacyAgreementView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPrivacyAgreementView.m; sourceTree = "<group>"; };
		189F778726D8E6AD00F4089E /* TTTAttributedLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TTTAttributedLabel.h; sourceTree = "<group>"; };
		189F778826D8E6AE00F4089E /* TTTAttributedLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TTTAttributedLabel.m; sourceTree = "<group>"; };
		18BBEF4E28A24AC100A26825 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		18C318F225F74AAD003B36C7 /* TKSpeechSynthesisManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeechSynthesisManagerProtocol.h; sourceTree = "<group>"; };
		18C318F425F74AAD003B36C7 /* TKSpeechSynthesisManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSpeechSynthesisManager.m; sourceTree = "<group>"; };
		18C318F625F74AAD003B36C7 /* TKSpeechSynthesisManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeechSynthesisManager.h; sourceTree = "<group>"; };
		18C318FE25F74AAD003B36C7 /* TKSpeechRecognizeManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSpeechRecognizeManager.m; sourceTree = "<group>"; };
		18C318FF25F74AAD003B36C7 /* TKSpeechRecognizeManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeechRecognizeManagerProtocol.h; sourceTree = "<group>"; };
		18C3190225F74AAD003B36C7 /* TKSpeechRecognizeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeechRecognizeManager.h; sourceTree = "<group>"; };
		18C3190725F74AAD003B36C7 /* TKOpenPlugin60026.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60026.h; sourceTree = "<group>"; };
		18C3190925F74AAD003B36C7 /* TKOneWayVideoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayVideoViewController.h; sourceTree = "<group>"; };
		18C3190A25F74AAD003B36C7 /* TKOneWayVideoViewController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKOneWayVideoViewController.mm; sourceTree = "<group>"; };
		18C3190B25F74AAD003B36C7 /* TKOpenPlugin60026.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60026.m; sourceTree = "<group>"; };
		18C3190D25F74AAD003B36C7 /* TKOneWayVideoEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOneWayVideoEndView.m; sourceTree = "<group>"; };
		18C3190E25F74AAD003B36C7 /* TKOneWayVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOneWayVideoView.m; sourceTree = "<group>"; };
		18C3190F25F74AAD003B36C7 /* TKOneWayVideoEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayVideoEndView.h; sourceTree = "<group>"; };
		18C3191025F74AAD003B36C7 /* TKOneWayVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayVideoView.h; sourceTree = "<group>"; };
		18C3192525F74AE2003B36C7 /* CallKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CallKit.framework; path = System/Library/Frameworks/CallKit.framework; sourceTree = SDKROOT; };
		18C46C35291A54A10076BAC0 /* TKChatTokenHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKChatTokenHelper.h; sourceTree = "<group>"; };
		18C46C36291A54A10076BAC0 /* TKChatTokenHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKChatTokenHelper.m; sourceTree = "<group>"; };
		18F129E825FEF96000F26F5C /* TKFaceDetectManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFaceDetectManager.h; sourceTree = "<group>"; };
		18F129E925FEF96000F26F5C /* TKFaceDetectManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFaceDetectManager.m; sourceTree = "<group>"; };
		18F129F525FF231600F26F5C /* TKOpenTipView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenTipView.h; sourceTree = "<group>"; };
		18F129F725FF231600F26F5C /* TKOpenTipView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenTipView.m; sourceTree = "<group>"; };
		18F12A1125FF24E000F26F5C /* TKOneWayVideoAlertTipView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOneWayVideoAlertTipView.m; sourceTree = "<group>"; };
		18F12A1325FF24E000F26F5C /* TKOneWayVideoAlertTipView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayVideoAlertTipView.h; sourceTree = "<group>"; };
		18FF460B28E1B7AE0026440D /* SenseID_Liveness_Silent.lic */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = SenseID_Liveness_Silent.lic; sourceTree = "<group>"; };
		18FF460C28E1B7AE0026440D /* STLivenessModel.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = STLivenessModel.bundle; sourceTree = "<group>"; };
		33294B102A0A469800A72052 /* TChat.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TChat.framework; sourceTree = "<group>"; };
		33575EAC2D2E2553009B7B83 /* TKSmartVirtualManViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartVirtualManViewController.h; sourceTree = "<group>"; };
		33575EAD2D2E2553009B7B83 /* TKSmartVirtualManViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartVirtualManViewController.m; sourceTree = "<group>"; };
		33575EAF2D2E2553009B7B83 /* TKSmartVirtualManVideoEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartVirtualManVideoEndView.h; sourceTree = "<group>"; };
		33575EB02D2E2553009B7B83 /* TKSmartVirtualManVideoEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartVirtualManVideoEndView.m; sourceTree = "<group>"; };
		33575EB12D2E2553009B7B83 /* TKSmartVirtualManVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartVirtualManVideoView.h; sourceTree = "<group>"; };
		33575EB22D2E2553009B7B83 /* TKSmartVirtualManVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartVirtualManVideoView.m; sourceTree = "<group>"; };
		33575EB42D2E2553009B7B83 /* TKOpenPlugin60072.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60072.h; sourceTree = "<group>"; };
		33575EB52D2E2553009B7B83 /* TKOpenPlugin60072.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60072.m; sourceTree = "<group>"; };
		33575EBB2D2E26D3009B7B83 /* TKChatVideoRecordManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKChatVideoRecordManager.h; sourceTree = "<group>"; };
		33575EBC2D2E26D3009B7B83 /* TKChatVideoRecordManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKChatVideoRecordManager.m; sourceTree = "<group>"; };
		338924662AB825B3005C6B5C /* TKVideoRecordManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKVideoRecordManager.mm; sourceTree = "<group>"; };
		338924692AB825B9005C6B5C /* TKSampleBufferConverter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSampleBufferConverter.m; sourceTree = "<group>"; };
		3389246A2AB825B9005C6B5C /* TKSampleBufferConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSampleBufferConverter.h; sourceTree = "<group>"; };
		338924822AB8261E005C6B5C /* TKReadingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKReadingView.m; sourceTree = "<group>"; };
		338924832AB8261E005C6B5C /* TKReadingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKReadingView.h; sourceTree = "<group>"; };
		338924882AB827AD005C6B5C /* TKringBuf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKringBuf.h; sourceTree = "<group>"; };
		338924892AB827AD005C6B5C /* TKringBuf.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = TKringBuf.cpp; sourceTree = "<group>"; };
		3389248B2AB827FF005C6B5C /* TKNLSPlayAudio.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKNLSPlayAudio.mm; sourceTree = "<group>"; };
		3389248C2AB827FF005C6B5C /* TKNLSPlayAudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKNLSPlayAudio.h; sourceTree = "<group>"; };
		338DF1C52B038104005DFCD1 /* AnyChatSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = AnyChatSDK.framework; sourceTree = "<group>"; };
		338DF1C72B038104005DFCD1 /* AnyChatErrorCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AnyChatErrorCode.h; sourceTree = "<group>"; };
		338DF1C82B038104005DFCD1 /* AnyChatPlatform.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AnyChatPlatform.h; sourceTree = "<group>"; };
		338DF1C92B038104005DFCD1 /* AnyChatDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AnyChatDefine.h; sourceTree = "<group>"; };
		338DF1CA2B038104005DFCD1 /* AnyChatObjectDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AnyChatObjectDefine.h; sourceTree = "<group>"; };
		33B365372D375AA40078392A /* TKChatLocalVideoRecordManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKChatLocalVideoRecordManager.h; sourceTree = "<group>"; };
		33B365382D375AA40078392A /* TKChatLocalVideoRecordManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = TKChatLocalVideoRecordManager.mm; sourceTree = "<group>"; };
		33B3653A2D375AB80078392A /* TKNewOneWayVideoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKNewOneWayVideoViewController.h; sourceTree = "<group>"; };
		33B3653B2D375AB80078392A /* TKNewOneWayVideoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKNewOneWayVideoViewController.m; sourceTree = "<group>"; };
		33B3653D2D375AB80078392A /* TKNewOneWayLandScapeControlView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKNewOneWayLandScapeControlView.h; sourceTree = "<group>"; };
		33B3653E2D375AB80078392A /* TKNewOneWayLandScapeControlView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKNewOneWayLandScapeControlView.m; sourceTree = "<group>"; };
		33B3653F2D375AB80078392A /* TKNewOneWayPlayerControlView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKNewOneWayPlayerControlView.h; sourceTree = "<group>"; };
		33B365402D375AB80078392A /* TKNewOneWayPlayerControlView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKNewOneWayPlayerControlView.m; sourceTree = "<group>"; };
		33B365422D375AB80078392A /* TKNewOneWayVideoEndView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKNewOneWayVideoEndView.h; sourceTree = "<group>"; };
		33B365432D375AB80078392A /* TKNewOneWayVideoEndView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKNewOneWayVideoEndView.m; sourceTree = "<group>"; };
		33B365442D375AB80078392A /* TKNewOneWayVideoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKNewOneWayVideoView.h; sourceTree = "<group>"; };
		33B365452D375AB80078392A /* TKNewOneWayVideoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKNewOneWayVideoView.m; sourceTree = "<group>"; };
		33B365472D375AB80078392A /* TKOpenPlugin600261.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin600261.h; sourceTree = "<group>"; };
		33B365482D375AB80078392A /* TKOpenPlugin600261.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin600261.m; sourceTree = "<group>"; };
		33C502B12A0A4D860050DB0C /* STLivenessDetector.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = STLivenessDetector.xcframework; sourceTree = "<group>"; };
		33CB8E312DA67FF6000EA7C4 /* UIImageView+TKZFCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+TKZFCache.h"; sourceTree = "<group>"; };
		33CB8E322DA67FF6000EA7C4 /* UIImageView+TKZFCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+TKZFCache.m"; sourceTree = "<group>"; };
		33CB8E332DA67FF6000EA7C4 /* UIScrollView+TKZFPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+TKZFPlayer.h"; sourceTree = "<group>"; };
		33CB8E342DA67FF6000EA7C4 /* UIScrollView+TKZFPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+TKZFPlayer.m"; sourceTree = "<group>"; };
		33CB8E352DA67FF6000EA7C4 /* UIView+TKZFFrame.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+TKZFFrame.h"; sourceTree = "<group>"; };
		33CB8E362DA67FF6000EA7C4 /* UIView+TKZFFrame.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+TKZFFrame.m"; sourceTree = "<group>"; };
		33CB8E382DA67FF6000EA7C4 /* TKZFLandscapeRotationManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeRotationManager.h; sourceTree = "<group>"; };
		33CB8E392DA67FF6000EA7C4 /* TKZFLandscapeRotationManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeRotationManager.m; sourceTree = "<group>"; };
		33CB8E3A2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS15.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeRotationManager_iOS15.h; sourceTree = "<group>"; };
		33CB8E3B2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS15.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeRotationManager_iOS15.m; sourceTree = "<group>"; };
		33CB8E3C2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS16.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeRotationManager_iOS16.h; sourceTree = "<group>"; };
		33CB8E3D2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS16.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeRotationManager_iOS16.m; sourceTree = "<group>"; };
		33CB8E3E2DA67FF6000EA7C4 /* TKZFLandscapeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeViewController.h; sourceTree = "<group>"; };
		33CB8E3F2DA67FF6000EA7C4 /* TKZFLandscapeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeViewController.m; sourceTree = "<group>"; };
		33CB8E402DA67FF6000EA7C4 /* TKZFLandscapeViewController_iOS15.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeViewController_iOS15.h; sourceTree = "<group>"; };
		33CB8E412DA67FF6000EA7C4 /* TKZFLandscapeViewController_iOS15.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeViewController_iOS15.m; sourceTree = "<group>"; };
		33CB8E422DA67FF6000EA7C4 /* TKZFLandscapeWindow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeWindow.h; sourceTree = "<group>"; };
		33CB8E432DA67FF6000EA7C4 /* TKZFLandscapeWindow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeWindow.m; sourceTree = "<group>"; };
		33CB8E442DA67FF6000EA7C4 /* TKZFOrientationObserver.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFOrientationObserver.h; sourceTree = "<group>"; };
		33CB8E452DA67FF6000EA7C4 /* TKZFOrientationObserver.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFOrientationObserver.m; sourceTree = "<group>"; };
		33CB8E462DA67FF6000EA7C4 /* TKZFPersentInteractiveTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPersentInteractiveTransition.h; sourceTree = "<group>"; };
		33CB8E472DA67FF6000EA7C4 /* TKZFPersentInteractiveTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPersentInteractiveTransition.m; sourceTree = "<group>"; };
		33CB8E482DA67FF6000EA7C4 /* TKZFPortraitViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPortraitViewController.h; sourceTree = "<group>"; };
		33CB8E492DA67FF6000EA7C4 /* TKZFPortraitViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPortraitViewController.m; sourceTree = "<group>"; };
		33CB8E4A2DA67FF6000EA7C4 /* TKZFPresentTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPresentTransition.h; sourceTree = "<group>"; };
		33CB8E4B2DA67FF6000EA7C4 /* TKZFPresentTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPresentTransition.m; sourceTree = "<group>"; };
		33CB8E4D2DA67FF6000EA7C4 /* TKZFPlayerMediaControl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerMediaControl.h; sourceTree = "<group>"; };
		33CB8E4E2DA67FF6000EA7C4 /* TKZFPlayerMediaPlayback.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerMediaPlayback.h; sourceTree = "<group>"; };
		33CB8E502DA67FF6000EA7C4 /* TKZFKVOController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFKVOController.h; sourceTree = "<group>"; };
		33CB8E512DA67FF6000EA7C4 /* TKZFKVOController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFKVOController.m; sourceTree = "<group>"; };
		33CB8E522DA67FF6000EA7C4 /* TKZFNetworkSpeedMonitor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFNetworkSpeedMonitor.h; sourceTree = "<group>"; };
		33CB8E532DA67FF6000EA7C4 /* TKZFNetworkSpeedMonitor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFNetworkSpeedMonitor.m; sourceTree = "<group>"; };
		33CB8E542DA67FF6000EA7C4 /* TKZFPlayerLogManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerLogManager.h; sourceTree = "<group>"; };
		33CB8E552DA67FF6000EA7C4 /* TKZFPlayerLogManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerLogManager.m; sourceTree = "<group>"; };
		33CB8E562DA67FF6000EA7C4 /* TKZFPlayerNotification.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerNotification.h; sourceTree = "<group>"; };
		33CB8E572DA67FF6000EA7C4 /* TKZFPlayerNotification.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerNotification.m; sourceTree = "<group>"; };
		33CB8E582DA67FF6000EA7C4 /* TKZFReachabilityManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFReachabilityManager.h; sourceTree = "<group>"; };
		33CB8E592DA67FF6000EA7C4 /* TKZFReachabilityManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFReachabilityManager.m; sourceTree = "<group>"; };
		33CB8E5A2DA67FF6000EA7C4 /* TKZFUtilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFUtilities.h; sourceTree = "<group>"; };
		33CB8E5B2DA67FF6000EA7C4 /* TKZFUtilities.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFUtilities.m; sourceTree = "<group>"; };
		33CB8E5D2DA67FF6000EA7C4 /* TKPlayerControlView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKPlayerControlView.h; sourceTree = "<group>"; };
		33CB8E5E2DA67FF6000EA7C4 /* TKPlayerControlView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKPlayerControlView.m; sourceTree = "<group>"; };
		33CB8E5F2DA67FF6000EA7C4 /* TKZFLandScapeControlView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFLandScapeControlView.h; sourceTree = "<group>"; };
		33CB8E602DA67FF6000EA7C4 /* TKZFLandScapeControlView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFLandScapeControlView.m; sourceTree = "<group>"; };
		33CB8E612DA67FF6000EA7C4 /* TKZFPortraitControlView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPortraitControlView.h; sourceTree = "<group>"; };
		33CB8E622DA67FF6000EA7C4 /* TKZFPortraitControlView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPortraitControlView.m; sourceTree = "<group>"; };
		33CB8E642DA67FF6000EA7C4 /* TKZFFloatView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFFloatView.h; sourceTree = "<group>"; };
		33CB8E652DA67FF6000EA7C4 /* TKZFFloatView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFFloatView.m; sourceTree = "<group>"; };
		33CB8E662DA67FF6000EA7C4 /* TKZFSmallFloatControlView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFSmallFloatControlView.h; sourceTree = "<group>"; };
		33CB8E672DA67FF6000EA7C4 /* TKZFSmallFloatControlView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFSmallFloatControlView.m; sourceTree = "<group>"; };
		33CB8E692DA67FF6000EA7C4 /* TKFragmentTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKFragmentTableViewCell.h; sourceTree = "<group>"; };
		33CB8E6A2DA67FF6000EA7C4 /* TKFragmentTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKFragmentTableViewCell.m; sourceTree = "<group>"; };
		33CB8E6B2DA67FF6000EA7C4 /* TKFragmentVideoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKFragmentVideoView.h; sourceTree = "<group>"; };
		33CB8E6C2DA67FF6000EA7C4 /* TKFragmentVideoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKFragmentVideoView.m; sourceTree = "<group>"; };
		33CB8E6D2DA67FF6000EA7C4 /* TKVideoFragmentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKVideoFragmentModel.h; sourceTree = "<group>"; };
		33CB8E6E2DA67FF6000EA7C4 /* TKVideoFragmentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKVideoFragmentModel.m; sourceTree = "<group>"; };
		33CB8E702DA67FF6000EA7C4 /* TKZFLoadingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFLoadingView.h; sourceTree = "<group>"; };
		33CB8E712DA67FF6000EA7C4 /* TKZFLoadingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFLoadingView.m; sourceTree = "<group>"; };
		33CB8E722DA67FF6000EA7C4 /* TKZFSpeedLoadingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFSpeedLoadingView.h; sourceTree = "<group>"; };
		33CB8E732DA67FF6000EA7C4 /* TKZFSpeedLoadingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFSpeedLoadingView.m; sourceTree = "<group>"; };
		33CB8E752DA67FF6000EA7C4 /* TKZFSliderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFSliderView.h; sourceTree = "<group>"; };
		33CB8E762DA67FF6000EA7C4 /* TKZFSliderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFSliderView.m; sourceTree = "<group>"; };
		33CB8E782DA67FF6000EA7C4 /* TKSpeedSelectView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKSpeedSelectView.h; sourceTree = "<group>"; };
		33CB8E792DA67FF6000EA7C4 /* TKSpeedSelectView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKSpeedSelectView.m; sourceTree = "<group>"; };
		33CB8E7B2DA67FF6000EA7C4 /* TKZFPlayerStatusBar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerStatusBar.h; sourceTree = "<group>"; };
		33CB8E7C2DA67FF6000EA7C4 /* TKZFPlayerStatusBar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerStatusBar.m; sourceTree = "<group>"; };
		33CB8E7E2DA67FF6000EA7C4 /* TKZFVolumeBrightnessView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFVolumeBrightnessView.h; sourceTree = "<group>"; };
		33CB8E7F2DA67FF6000EA7C4 /* TKZFVolumeBrightnessView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFVolumeBrightnessView.m; sourceTree = "<group>"; };
		33CB8E812DA67FF6000EA7C4 /* TKZFPlayerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerView.h; sourceTree = "<group>"; };
		33CB8E822DA67FF6000EA7C4 /* TKZFPlayerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerView.m; sourceTree = "<group>"; };
		33CB8E842DA67FF6000EA7C4 /* TKPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKPlayer.h; sourceTree = "<group>"; };
		33CB8E852DA67FF6000EA7C4 /* TKPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKPlayer.m; sourceTree = "<group>"; };
		33CB8E862DA67FF6000EA7C4 /* TKZFPlayerConst.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerConst.h; sourceTree = "<group>"; };
		33CB8E872DA67FF6000EA7C4 /* TKZFPlayerController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerController.h; sourceTree = "<group>"; };
		33CB8E882DA67FF6000EA7C4 /* TKZFPlayerController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerController.m; sourceTree = "<group>"; };
		33CB8E892DA67FF6000EA7C4 /* TKZFPlayerGestureControl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerGestureControl.h; sourceTree = "<group>"; };
		33CB8E8A2DA67FF6000EA7C4 /* TKZFPlayerGestureControl.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerGestureControl.m; sourceTree = "<group>"; };
		33EA39DA2B4D24000038FBD6 /* TKPlayRangeModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKPlayRangeModel.h; sourceTree = "<group>"; };
		33EA39DB2B4D24000038FBD6 /* TKPlayRangeModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKPlayRangeModel.m; sourceTree = "<group>"; };
		33F248CE29713BA900547805 /* TKCardPreview.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKCardPreview.h; sourceTree = "<group>"; };
		33F248CF29713BA900547805 /* TKCardPreview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKCardPreview.m; sourceTree = "<group>"; };
		33F50ECC2A92F52900246237 /* TKOpenPlugin60005.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60005.m; sourceTree = "<group>"; };
		33F50ECE2A92F52900246237 /* TKSmartTwoVideoController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartTwoVideoController.m; sourceTree = "<group>"; };
		33F50ECF2A92F52900246237 /* TKSmartTwoVideoController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartTwoVideoController.h; sourceTree = "<group>"; };
		33F50ED82A92F52900246237 /* TKChatService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKChatService.m; sourceTree = "<group>"; };
		33F50EDA2A92F52900246237 /* TKVideoWitnessViewController+CommonKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "TKVideoWitnessViewController+CommonKit.h"; sourceTree = "<group>"; };
		33F50EDB2A92F52900246237 /* TKVideoWitnessViewController+AnyChat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "TKVideoWitnessViewController+AnyChat.h"; sourceTree = "<group>"; };
		33F50EDC2A92F52900246237 /* TKVideoWitnessViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoWitnessViewController.h; sourceTree = "<group>"; };
		33F50EDD2A92F52900246237 /* TKVideoWitnessViewController+CommonKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "TKVideoWitnessViewController+CommonKit.m"; sourceTree = "<group>"; };
		33F50EDE2A92F52900246237 /* TKVideoWitnessViewController+AnyChat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "TKVideoWitnessViewController+AnyChat.m"; sourceTree = "<group>"; };
		33F50EDF2A92F52900246237 /* TKVideoWitnessViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoWitnessViewController.m; sourceTree = "<group>"; };
		33F50EE02A92F52900246237 /* TKOpenPlugin60005.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60005.h; sourceTree = "<group>"; };
		33F50EE22A92F52900246237 /* TKOpenQueueView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenQueueView.h; sourceTree = "<group>"; };
		33F50EE32A92F52900246237 /* TKOpenVideoChatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenVideoChatView.h; sourceTree = "<group>"; };
		33F50EE42A92F52900246237 /* TKVideoReadAgreeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoReadAgreeView.h; sourceTree = "<group>"; };
		33F50EE52A92F52900246237 /* TKVideoMsgTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoMsgTableViewCell.h; sourceTree = "<group>"; };
		33F50EE62A92F52900246237 /* TKOpenVideoChatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenVideoChatView.m; sourceTree = "<group>"; };
		33F50EE72A92F52900246237 /* TKOpenQueueView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenQueueView.m; sourceTree = "<group>"; };
		33F50EE82A92F52900246237 /* TKVideoReadAgreeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoReadAgreeView.m; sourceTree = "<group>"; };
		33F50EE92A92F52900246237 /* TKVideoMsgTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoMsgTableViewCell.m; sourceTree = "<group>"; };
		33F50EEA2A92F52900246237 /* TKChatServiceDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKChatServiceDelegate.h; sourceTree = "<group>"; };
		33F50EEB2A92F52900246237 /* TKChatService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKChatService.h; sourceTree = "<group>"; };
		33F50EED2A92F52900246237 /* TKOpenPlugin60034.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60034.m; sourceTree = "<group>"; };
		33F50EEF2A92F52900246237 /* TKDirectVideoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKDirectVideoViewController.m; sourceTree = "<group>"; };
		33F50EF02A92F52900246237 /* TKDirectVideoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKDirectVideoViewController.h; sourceTree = "<group>"; };
		33F50EF12A92F52900246237 /* TKOpenPlugin60034.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60034.h; sourceTree = "<group>"; };
		33F50EF32A92F52900246237 /* TKDirectVideoChatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKDirectVideoChatView.m; sourceTree = "<group>"; };
		33F50EF42A92F52900246237 /* TKDirectVideoChatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKDirectVideoChatView.h; sourceTree = "<group>"; };
		33F50EF62A92F52900246237 /* TKDirectVideoModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKDirectVideoModel.h; sourceTree = "<group>"; };
		33F50EF72A92F52900246237 /* TKDirectVideoModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKDirectVideoModel.m; sourceTree = "<group>"; };
		33F50F0D2A92F60200246237 /* page_bg.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = page_bg.jpg; sourceTree = "<group>"; };
		33F50F0E2A92F60200246237 /* TKAnyChatViewController4.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = TKAnyChatViewController4.xib; sourceTree = "<group>"; };
		33F50F0F2A92F60200246237 /* kefu_bg_img.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = kefu_bg_img.png; sourceTree = "<group>"; };
		33F50F102A92F60200246237 /* chat_cancel_btn.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = chat_cancel_btn.png; sourceTree = "<group>"; };
		33F50F112A92F60200246237 /* TKAnyChatViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = TKAnyChatViewController.xib; sourceTree = "<group>"; };
		33F50F122A92F60200246237 /* TKVideoWitnessViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = TKVideoWitnessViewController.xib; sourceTree = "<group>"; };
		33F50F132A92F60200246237 /* tk_video_icon_iphone.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = tk_video_icon_iphone.png; sourceTree = "<group>"; };
		33F50F272A92F8C400246237 /* TKStatisticEventHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKStatisticEventHelper.h; sourceTree = "<group>"; };
		33F50F282A92F8C400246237 /* TKStatisticEventHelperPrivate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKStatisticEventHelperPrivate.h; sourceTree = "<group>"; };
		33F50F292A92F8C400246237 /* TKStatisticEventHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKStatisticEventHelper.m; sourceTree = "<group>"; };
		33F50F2B2A92F8D200246237 /* TKAnyChatSmartTwoVideoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKAnyChatSmartTwoVideoManager.m; sourceTree = "<group>"; };
		33F50F2C2A92F8D200246237 /* TKTChatSmartTwoVideoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTChatSmartTwoVideoManager.h; sourceTree = "<group>"; };
		33F50F2D2A92F8D200246237 /* TKSmartTwoVideoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartTwoVideoManager.h; sourceTree = "<group>"; };
		33F50F2E2A92F8D200246237 /* TKSmartTwoVideoManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartTwoVideoManagerProtocol.h; sourceTree = "<group>"; };
		33F50F2F2A92F8D200246237 /* TKTChatSmartTwoVideoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTChatSmartTwoVideoManager.m; sourceTree = "<group>"; };
		33F50F302A92F8D200246237 /* TKAnyChatSmartTwoVideoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKAnyChatSmartTwoVideoManager.h; sourceTree = "<group>"; };
		33F50F312A92F8D200246237 /* TKSmartTwoVideoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartTwoVideoManager.m; sourceTree = "<group>"; };
		B30DC5061BCFC873007072FB /* Configuration.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = Configuration.xml; sourceTree = "<group>"; };
		B30DC5071BCFC873007072FB /* OpenPlugin.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = OpenPlugin.xml; sourceTree = "<group>"; };
		B30DC5081BCFC873007072FB /* SystemPlugin.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = SystemPlugin.xml; sourceTree = "<group>"; };
		B30FD8A51EF1063F000D3E94 /* UIViewController+TKAuthorityKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIViewController+TKAuthorityKit.h"; sourceTree = "<group>"; };
		B30FD8A61EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+TKAuthorityKit.m"; sourceTree = "<group>"; };
		B32C870B1D87E96E00373C19 /* config.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = config.plist; sourceTree = "<group>"; };
		B32E91C71F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "TKVideoWitnessViewController+TChat.h"; sourceTree = "<group>"; };
		B32E91C81F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "TKVideoWitnessViewController+TChat.m"; sourceTree = "<group>"; };
		B3386D691F271753006EF60A /* TKAVCaptureManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKAVCaptureManager.h; sourceTree = "<group>"; };
		B3386D6A1F271753006EF60A /* TKAVCaptureManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKAVCaptureManager.m; sourceTree = "<group>"; };
		B33FDC0F1F692BCB00C644F2 /* libxml2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.tbd; path = usr/lib/libxml2.tbd; sourceTree = SDKROOT; };
		B33FDC111F692BE400C644F2 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		B35569F61DDBF96B00C76E7C /* TKOpenPlugin60000.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60000.h; sourceTree = "<group>"; };
		B35569F71DDBF96B00C76E7C /* TKOpenPlugin60000.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60000.m; sourceTree = "<group>"; };
		B35569F91DDBF96B00C76E7C /* TKOpenPlugin60001.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60001.h; sourceTree = "<group>"; };
		B35569FA1DDBF96B00C76E7C /* TKOpenPlugin60001.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60001.m; sourceTree = "<group>"; };
		B35569FC1DDBF96B00C76E7C /* TKOpenPlugin60002.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60002.h; sourceTree = "<group>"; };
		B35569FD1DDBF96B00C76E7C /* TKOpenPlugin60002.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60002.m; sourceTree = "<group>"; };
		B35569FF1DDBF96B00C76E7C /* MTakeCardViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTakeCardViewController.h; sourceTree = "<group>"; };
		B3556A001DDBF96B00C76E7C /* MTakeCardViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MTakeCardViewController.m; sourceTree = "<group>"; };
		B3556A021DDBF96B00C76E7C /* TKOpenPlugin60003.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60003.h; sourceTree = "<group>"; };
		B3556A031DDBF96B00C76E7C /* TKOpenPlugin60003.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60003.m; sourceTree = "<group>"; };
		B3556A051DDBF96B00C76E7C /* TKOpenPlugin60004.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60004.h; sourceTree = "<group>"; };
		B3556A061DDBF96B00C76E7C /* TKOpenPlugin60004.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60004.m; sourceTree = "<group>"; };
		B3556A401DDBF96B00C76E7C /* TKOpenPlugin60010.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60010.h; sourceTree = "<group>"; };
		B3556A411DDBF96B00C76E7C /* TKOpenPlugin60010.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60010.m; sourceTree = "<group>"; };
		B3556A461DDBF96B00C76E7C /* MTakeBigPictureViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTakeBigPictureViewController.h; sourceTree = "<group>"; };
		B3556A471DDBF96B00C76E7C /* MTakeBigPictureViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MTakeBigPictureViewController.m; sourceTree = "<group>"; };
		B3556A481DDBF96B00C76E7C /* MTakePhotoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTakePhotoViewController.h; sourceTree = "<group>"; };
		B3556A491DDBF96B00C76E7C /* MTakePhotoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MTakePhotoViewController.m; sourceTree = "<group>"; };
		B3556A4A1DDBF96B00C76E7C /* TKOpenPlugin60013.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60013.h; sourceTree = "<group>"; };
		B3556A4B1DDBF96B00C76E7C /* TKOpenPlugin60013.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60013.m; sourceTree = "<group>"; };
		B3556A891DDBF96B00C76E7C /* TKOpenPlugin60017.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60017.h; sourceTree = "<group>"; };
		B3556A8A1DDBF96B00C76E7C /* TKOpenPlugin60017.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60017.m; sourceTree = "<group>"; };
		B3556A8C1DDBF96B00C76E7C /* TKOpenPlugin60018.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60018.h; sourceTree = "<group>"; };
		B3556A8D1DDBF96B00C76E7C /* TKOpenPlugin60018.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60018.m; sourceTree = "<group>"; };
		B35EBF391C4393770059F885 /* MNavViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MNavViewController.h; sourceTree = "<group>"; };
		B35EBF3A1C4393770059F885 /* MNavViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MNavViewController.m; sourceTree = "<group>"; };
		B35EBF3D1C43942F0059F885 /* MDRadialProgressLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MDRadialProgressLabel.h; sourceTree = "<group>"; };
		B35EBF3E1C43942F0059F885 /* MDRadialProgressLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MDRadialProgressLabel.m; sourceTree = "<group>"; };
		B35EBF3F1C43942F0059F885 /* MDRadialProgressTheme.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MDRadialProgressTheme.h; sourceTree = "<group>"; };
		B35EBF401C43942F0059F885 /* MDRadialProgressTheme.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MDRadialProgressTheme.m; sourceTree = "<group>"; };
		B35EBF411C43942F0059F885 /* MDRadialProgressView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MDRadialProgressView.h; sourceTree = "<group>"; };
		B35EBF421C43942F0059F885 /* MDRadialProgressView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MDRadialProgressView.m; sourceTree = "<group>"; };
		B379D392206CD57000D1440E /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		B37F84281BE365230016F93D /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		B384CDC81B8177B400AFD817 /* TKButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKButton.h; sourceTree = "<group>"; };
		B384CDC91B8177B400AFD817 /* TKButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKButton.m; sourceTree = "<group>"; };
		B384CDCA1B8177B400AFD817 /* TKCameraTools.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKCameraTools.h; sourceTree = "<group>"; };
		B384CDCB1B8177B400AFD817 /* TKCameraTools.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKCameraTools.m; sourceTree = "<group>"; };
		B386BA0C1F947E6C001CBD13 /* TKOpenPlugin60099.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60099.h; sourceTree = "<group>"; };
		B386BA0D1F947E6C001CBD13 /* TKOpenPlugin60099.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60099.m; sourceTree = "<group>"; };
		B38D07B51FC402EC0013DA81 /* TKMAlertViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKMAlertViewController.m; sourceTree = "<group>"; };
		B38D07B61FC402EC0013DA81 /* TKMAlertViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKMAlertViewController.h; sourceTree = "<group>"; };
		B39BADCF1B4D524F00F071D4 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		B39E930E208F0532006EC8A3 /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		B39E9310208F054A006EC8A3 /* MessageUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MessageUI.framework; path = System/Library/Frameworks/MessageUI.framework; sourceTree = SDKROOT; };
		B39E9312208F05CD006EC8A3 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		B3A2DD311EA461A700B18842 /* TKCommonUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKCommonUtil.h; sourceTree = "<group>"; };
		B3A2DD321EA461A700B18842 /* TKCommonUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKCommonUtil.m; sourceTree = "<group>"; };
		B3AB8AF91C15708900571BE6 /* TKOpenResource.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = TKOpenResource.bundle; sourceTree = "<group>"; };
		B3B108831B44DF2800546D96 /* libiconv.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libiconv.dylib; path = usr/lib/libiconv.dylib; sourceTree = SDKROOT; };
		B3C6ADC61C92ADDD00FE95F0 /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		B3C6ADC81C92ADEA00FE95F0 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		B3E1B2EF1B8D9FEF00CDD258 /* Resources */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Resources; sourceTree = "<group>"; };
		B3E1B30B1B8DB80800CDD258 /* TKOpenResource.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TKOpenResource.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		B3E9E0D91C1170DC0012E37D /* TKOpenDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenDelegate.h; sourceTree = "<group>"; };
		B3FACBB11AFDDF0E0088CDF1 /* 思迪TChatRtc.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "思迪TChatRtc.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		B3FACBB51AFDDF0E0088CDF1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B3FACBB61AFDDF0E0088CDF1 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		B3FACBB81AFDDF0E0088CDF1 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		B3FACBB91AFDDF0E0088CDF1 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		B3FACBBB1AFDDF0E0088CDF1 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		B3FACBBC1AFDDF0E0088CDF1 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		B3FACBBF1AFDDF0E0088CDF1 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		B3FACBC11AFDDF0E0088CDF1 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		B3FACBC41AFDDF0E0088CDF1 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		B3FACBDC1AFDE2D10088CDF1 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		B3FACBDE1AFDE2E50088CDF1 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		B3FACBE01AFDE2F40088CDF1 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		B3FACBE21AFDE3010088CDF1 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		B3FACBE41AFDE30B0088CDF1 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		B3FACBE61AFDE31A0088CDF1 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		B3FACBE81AFDE33A0088CDF1 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		B3FACBEA1AFDE3460088CDF1 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		B3FACBEC1AFDE35E0088CDF1 /* libstdc++.6.0.9.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = "libstdc++.6.0.9.dylib"; path = "usr/lib/libstdc++.6.0.9.dylib"; sourceTree = SDKROOT; };
		B3FACBEE1AFDE3750088CDF1 /* libsqlite3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libsqlite3.dylib; path = usr/lib/libsqlite3.dylib; sourceTree = SDKROOT; };
		B3FACBF01AFDE3800088CDF1 /* libz.1.2.5.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.1.2.5.dylib; path = usr/lib/libz.1.2.5.dylib; sourceTree = SDKROOT; };
		B3FACBF21AFDE3940088CDF1 /* libc++.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = "libc++.dylib"; path = "usr/lib/libc++.dylib"; sourceTree = SDKROOT; };
		B3FACCBC1AFDF2F80088CDF1 /* TKOpenAccount.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenAccount.pch; sourceTree = "<group>"; };
		B3FEEB351B845D6D00468924 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		B3FF6E391EC5B2A0009FAF95 /* tk_open */ = {isa = PBXFileReference; lastKnownFileType = folder; path = tk_open; sourceTree = "<group>"; };
		B3FF6E3B1EC5B4F0009FAF95 /* TKYKHEmbedHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKYKHEmbedHelper.h; sourceTree = "<group>"; };
		B3FF6E3C1EC5B4F0009FAF95 /* TKYKHEmbedHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKYKHEmbedHelper.m; sourceTree = "<group>"; };
		BE4F17152E1D265D009DCF6C /* TKSubtitlesRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKSubtitlesRenderer.h; sourceTree = "<group>"; };
		BE4F17162E1D265D009DCF6C /* TKSubtitlesRenderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKSubtitlesRenderer.m; sourceTree = "<group>"; };
		BE4F17182E1D265D009DCF6C /* TKTTSCloudPlayerManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKTTSCloudPlayerManager.h; sourceTree = "<group>"; };
		BE4F17192E1D265D009DCF6C /* TKTTSCloudPlayerManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = TKTTSCloudPlayerManager.mm; sourceTree = "<group>"; };
		BE6CCFB61B5000B6003DCF98 /* TKOpenController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenController.h; sourceTree = "<group>"; };
		BE6CCFB71B5000B6003DCF98 /* TKOpenController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenController.m; sourceTree = "<group>"; };
		BE6CCFBA1B5000B6003DCF98 /* TKOpenAccountService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenAccountService.h; sourceTree = "<group>"; };
		BE6CCFBB1B5000B6003DCF98 /* TKOpenAccountService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenAccountService.m; sourceTree = "<group>"; };
		BE6CCFC41B5000B6003DCF98 /* YLProgressBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = YLProgressBar.h; sourceTree = "<group>"; };
		BE6CCFC51B5000B6003DCF98 /* YLProgressBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = YLProgressBar.m; sourceTree = "<group>"; };
		************************ /* TKBaseVideoRecordEndLandscapeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordEndLandscapeView.h; sourceTree = "<group>"; };
		************************ /* TKBaseVideoRecordEndLandscapeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordEndLandscapeView.m; sourceTree = "<group>"; };
		************************ /* TKSmartQuestionModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartQuestionModel.m; sourceTree = "<group>"; };
		DC84D1562857290700941BF5 /* TKSmartQuestionModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartQuestionModel.h; sourceTree = "<group>"; };
		DC84D1582857290700941BF5 /* TKRecordManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKRecordManagerProtocol.h; sourceTree = "<group>"; };
		DC84D1592857290700941BF5 /* TKBaseVideoRecordViewProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordViewProtocol.h; sourceTree = "<group>"; };
		DC84D15A2857290700941BF5 /* TKBaseVideoRecordEndViewProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordEndViewProtocol.h; sourceTree = "<group>"; };
		************************ /* TKBaseVideoRecordViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordViewController.h; sourceTree = "<group>"; };
		************************ /* TKBaseVideoRecordViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordViewController.m; sourceTree = "<group>"; };
		DC9BF92D2617249200A37295 /* TKiflySpeechSynthesisManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKiflySpeechSynthesisManager.h; sourceTree = "<group>"; };
		DC9BF92E2617249200A37295 /* TKiflySpeechSynthesisManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKiflySpeechSynthesisManager.m; sourceTree = "<group>"; };
		DC9BF938261C06AB00A37295 /* TKiflySpeechRecognizeManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKiflySpeechRecognizeManager.h; sourceTree = "<group>"; };
		DC9BF939261C06AB00A37295 /* TKiflySpeechRecognizeManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKiflySpeechRecognizeManager.m; sourceTree = "<group>"; };
		DCBFCEFC2A122B5600418EFE /* www */ = {isa = PBXFileReference; lastKnownFileType = folder; path = www; sourceTree = "<group>"; };
		DCC262C0291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordLandscapeView.h; sourceTree = "<group>"; };
		DCC262C1291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordLandscapeView.m; sourceTree = "<group>"; };
		DCC7E868262D7133006D378F /* TKTempService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTempService.m; sourceTree = "<group>"; };
		DCC7E86A262D7133006D378F /* TKTempService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTempService.h; sourceTree = "<group>"; };
		DCCDDA19295C2AF5002F268B /* TKFaceDectTipView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKFaceDectTipView.h; sourceTree = "<group>"; };
		DCCDDA1A295C2AF5002F268B /* TKFaceDectTipView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKFaceDectTipView.m; sourceTree = "<group>"; };
		DCD47D2D28E18CB800AE44C6 /* TKVideoRecordManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoRecordManager.h; sourceTree = "<group>"; };
		DCD47D3128E18E1600AE44C6 /* TKBaseVideoRecordEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordEndView.m; sourceTree = "<group>"; };
		DCD47D3228E18E1600AE44C6 /* TKBaseVideoRecordView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordView.m; sourceTree = "<group>"; };
		DCD47D3328E18E1600AE44C6 /* TKBaseVideoRecordEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordEndView.h; sourceTree = "<group>"; };
		DCD47D3428E18E1600AE44C6 /* TKBaseVideoRecordView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordView.h; sourceTree = "<group>"; };
		DCECF95B292F5EEF00A802EE /* TKSVGEngine.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKSVGEngine.mm; sourceTree = "<group>"; };
		DCECF95C292F5EF000A802EE /* TKSVGLayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSVGLayer.m; sourceTree = "<group>"; };
		DCECF95D292F5EF000A802EE /* TKSVGBezierPath.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSVGBezierPath.h; sourceTree = "<group>"; };
		DCECF95E292F5EF000A802EE /* TKSVGBezierPath.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKSVGBezierPath.mm; sourceTree = "<group>"; };
		DCECF961292F5EF000A802EE /* TKSVGEngine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSVGEngine.h; sourceTree = "<group>"; };
		DCECF963292F5EF000A802EE /* TKSVGImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSVGImageView.h; sourceTree = "<group>"; };
		DCECF964292F5EF000A802EE /* TKSVGImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSVGImageView.m; sourceTree = "<group>"; };
		DCECF966292F5EF000A802EE /* TKSVGLayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSVGLayer.h; sourceTree = "<group>"; };
		DCECF96C292F628A00A802EE /* TKSVGImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKSVGImage.h; sourceTree = "<group>"; };
		DCECF96D292F628A00A802EE /* TKSVGImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKSVGImage.m; sourceTree = "<group>"; };
		E22996F326AABD490039F6E9 /* SelectViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SelectViewController.h; sourceTree = "<group>"; };
		E22996F426AABD490039F6E9 /* SelectViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SelectViewController.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B3FACBAE1AFDDF0E0088CDF1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				18BBEF4F28A24AC100A26825 /* WebKit.framework in Frameworks */,
				187942C8255CD0AC0067C701 /* AdSupport.framework in Frameworks */,
				04441AA0221691F300CFDC2D /* VideoToolbox.framework in Frameworks */,
				B39E9313208F05CD006EC8A3 /* Accelerate.framework in Frameworks */,
				B39E9311208F054A006EC8A3 /* MessageUI.framework in Frameworks */,
				1878E03D2888F1E200C25151 /* AIPIFlyMSC.framework in Frameworks */,
				33294B122A0A469F00A72052 /* TChat.framework in Frameworks */,
				B39E930F208F0532006EC8A3 /* CoreMotion.framework in Frameworks */,
				B379D393206CD57000D1440E /* CoreTelephony.framework in Frameworks */,
				B33FDC121F692BE400C644F2 /* libresolv.tbd in Frameworks */,
				B33FDC101F692BCB00C644F2 /* libxml2.tbd in Frameworks */,
				B3C6ADC91C92ADEA00FE95F0 /* CoreMedia.framework in Frameworks */,
				B37F84291BE365230016F93D /* OpenGLES.framework in Frameworks */,
				049A8359211ECD6B00AA2048 /* libcrypto.a in Frameworks */,
				045ACEA622A4FC30004D8557 /* TKWebViewApp.framework in Frameworks */,
				B3FEEB361B845D6D00468924 /* AVFoundation.framework in Frameworks */,
				33C502B32A0A4D890050DB0C /* STLivenessDetector.xcframework in Frameworks */,
				B39BADD01B4D524F00F071D4 /* CoreLocation.framework in Frameworks */,
				B3B108841B44DF2800546D96 /* libiconv.dylib in Frameworks */,
				B3C6ADC71C92ADDD00FE95F0 /* AssetsLibrary.framework in Frameworks */,
				B3FACBF31AFDE3940088CDF1 /* libc++.dylib in Frameworks */,
				B3FACBF11AFDE3800088CDF1 /* libz.1.2.5.dylib in Frameworks */,
				B3FACBEF1AFDE3750088CDF1 /* libsqlite3.dylib in Frameworks */,
				B3FACBEB1AFDE3460088CDF1 /* CoreGraphics.framework in Frameworks */,
				B3FACBE91AFDE33A0088CDF1 /* Foundation.framework in Frameworks */,
				B3FACBE71AFDE31A0088CDF1 /* SystemConfiguration.framework in Frameworks */,
				338DF1CB2B038104005DFCD1 /* AnyChatSDK.framework in Frameworks */,
				B3FACBE51AFDE30B0088CDF1 /* CFNetwork.framework in Frameworks */,
				049A835A211ECD6B00AA2048 /* libssl.a in Frameworks */,
				B3FACBE11AFDE2F40088CDF1 /* Security.framework in Frameworks */,
				B3FACBDF1AFDE2E50088CDF1 /* QuartzCore.framework in Frameworks */,
				B3FACBDD1AFDE2D10088CDF1 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		043015E823FCC755004F0C17 /* TKOpenPlugin60028 */ = {
			isa = PBXGroup;
			children = (
				043015F023FCCDBE004F0C17 /* View */,
				043015E923FCC755004F0C17 /* Controller */,
				043015EA23FCC811004F0C17 /* TKOpenPlugin60028.h */,
				043015EB23FCC811004F0C17 /* TKOpenPlugin60028.m */,
			);
			path = TKOpenPlugin60028;
			sourceTree = "<group>";
		};
		043015E923FCC755004F0C17 /* Controller */ = {
			isa = PBXGroup;
			children = (
				187F35F926AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.h */,
				187F35FA26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m */,
				043015ED23FCC936004F0C17 /* TKFaceImageViewController.h */,
				043015EE23FCC936004F0C17 /* TKFaceImageViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		043015F023FCCDBE004F0C17 /* View */ = {
			isa = PBXGroup;
			children = (
				187F35FE26AAB4530046E9E3 /* TKFaceImageLandscapeView.h */,
				187F35FF26AAB4530046E9E3 /* TKFaceImageLandscapeView.m */,
				043015F223FCCDBE004F0C17 /* TKFaceImageView.h */,
				043015F123FCCDBE004F0C17 /* TKFaceImageView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		045EF85F241BA74F00032B22 /* TKOpenPlugin60032 */ = {
			isa = PBXGroup;
			children = (
				1876A14B2949CF57007F0500 /* Controller */,
				045EF860241BA81000032B22 /* TKOpenPlugin60032.h */,
				045EF861241BA81000032B22 /* TKOpenPlugin60032.m */,
			);
			path = TKOpenPlugin60032;
			sourceTree = "<group>";
		};
		0465234B212E84C000F8C0D0 /* TKOpenPlugin60022 */ = {
			isa = PBXGroup;
			children = (
				0465234C212E84C000F8C0D0 /* controllers */,
				0465234F212E84C000F8C0D0 /* TKOpenPlugin60022.h */,
				04652350212E84C000F8C0D0 /* TKOpenPlugin60022.m */,
				04652351212E84C000F8C0D0 /* views */,
			);
			path = TKOpenPlugin60022;
			sourceTree = "<group>";
		};
		0465234C212E84C000F8C0D0 /* controllers */ = {
			isa = PBXGroup;
			children = (
				0465234D212E84C000F8C0D0 /* TKSignatureController.h */,
				0465234E212E84C000F8C0D0 /* TKSignatureController.m */,
			);
			path = controllers;
			sourceTree = "<group>";
		};
		04652351212E84C000F8C0D0 /* views */ = {
			isa = PBXGroup;
			children = (
				04652352212E84C000F8C0D0 /* TKTouchView.h */,
				04652353212E84C000F8C0D0 /* TKTouchView.m */,
			);
			path = views;
			sourceTree = "<group>";
		};
		0472CA7A23FB9DD4000B5EB7 /* TKOpenPlugin60007 */ = {
			isa = PBXGroup;
			children = (
				0472CA8723FB9E50000B5EB7 /* Controller */,
				0472CA8823FB9E50000B5EB7 /* View */,
				0472CA8023FB9DD4000B5EB7 /* TKOpenPlugin60007.h */,
				0472CA8123FB9DD4000B5EB7 /* TKOpenPlugin60007.m */,
			);
			path = TKOpenPlugin60007;
			sourceTree = "<group>";
		};
		0472CA8723FB9E50000B5EB7 /* Controller */ = {
			isa = PBXGroup;
			children = (
				0472CA8923FB9EC4000B5EB7 /* TKLiveFaceViewController.h */,
				0472CA8A23FB9EC4000B5EB7 /* TKLiveFaceViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		0472CA8823FB9E50000B5EB7 /* View */ = {
			isa = PBXGroup;
			children = (
				0472CA8D23FBA05F000B5EB7 /* TKLiveFaceView.h */,
				0472CA8C23FBA05F000B5EB7 /* TKLiveFaceView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		048DC98E2407E66400A6F3EC /* TKOpenPlugin60030 */ = {
			isa = PBXGroup;
			children = (
				048DC9922407E66400A6F3EC /* View */,
				048DC98F2407E66400A6F3EC /* Controller */,
				048DC9902407E66400A6F3EC /* TKOpenPlugin60030.h */,
				048DC9912407E66400A6F3EC /* TKOpenPlugin60030.m */,
			);
			path = TKOpenPlugin60030;
			sourceTree = "<group>";
		};
		048DC98F2407E66400A6F3EC /* Controller */ = {
			isa = PBXGroup;
			children = (
				048DC9972407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.h */,
				048DC9982407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		048DC9922407E66400A6F3EC /* View */ = {
			isa = PBXGroup;
			children = (
				181D4C9F286E94F600679EB3 /* TKVideoAlertView.h */,
				181D4CA0286E94F600679EB3 /* TKVideoAlertView.m */,
				18F12A1325FF24E000F26F5C /* TKOneWayVideoAlertTipView.h */,
				18F12A1125FF24E000F26F5C /* TKOneWayVideoAlertTipView.m */,
				18F129F525FF231600F26F5C /* TKOpenTipView.h */,
				18F129F725FF231600F26F5C /* TKOpenTipView.m */,
				042CE7032408DD7900B9AC15 /* TKOrdinaryOneVideoEndView.h */,
				042CE7042408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m */,
				048DC9932407E66400A6F3EC /* TKOrdinaryOneVideoView.h */,
				048DC9942407E66400A6F3EC /* TKOrdinaryOneVideoView.m */,
				18034322291CD426002C8E2C /* TKPlayerToolView.h */,
				18034323291CD426002C8E2C /* TKPlayerToolView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		049A82E7211ECD6B00AA2048 /* THFMWK */ = {
			isa = PBXGroup;
			children = (
				049A82FE211ECD6B00AA2048 /* OpensslSDK */,
			);
			path = THFMWK;
			sourceTree = "<group>";
		};
		049A82FE211ECD6B00AA2048 /* OpensslSDK */ = {
			isa = PBXGroup;
			children = (
				049A82FF211ECD6B00AA2048 /* include */,
				049A834C211ECD6B00AA2048 /* libs */,
			);
			path = OpensslSDK;
			sourceTree = "<group>";
		};
		049A82FF211ECD6B00AA2048 /* include */ = {
			isa = PBXGroup;
			children = (
				049A8300211ECD6B00AA2048 /* openssl */,
			);
			path = include;
			sourceTree = "<group>";
		};
		049A8300211ECD6B00AA2048 /* openssl */ = {
			isa = PBXGroup;
			children = (
				049A8301211ECD6B00AA2048 /* aes.h */,
				049A8302211ECD6B00AA2048 /* asn1.h */,
				049A8303211ECD6B00AA2048 /* asn1_mac.h */,
				049A8304211ECD6B00AA2048 /* asn1t.h */,
				049A8305211ECD6B00AA2048 /* bio.h */,
				049A8306211ECD6B00AA2048 /* blowfish.h */,
				049A8307211ECD6B00AA2048 /* bn.h */,
				049A8308211ECD6B00AA2048 /* buffer.h */,
				049A8309211ECD6B00AA2048 /* camellia.h */,
				049A830A211ECD6B00AA2048 /* cast.h */,
				049A830B211ECD6B00AA2048 /* cmac.h */,
				049A830C211ECD6B00AA2048 /* cms.h */,
				049A830D211ECD6B00AA2048 /* comp.h */,
				049A830E211ECD6B00AA2048 /* conf.h */,
				049A830F211ECD6B00AA2048 /* conf_api.h */,
				049A8310211ECD6B00AA2048 /* crypto.h */,
				049A8311211ECD6B00AA2048 /* des.h */,
				049A8312211ECD6B00AA2048 /* des_old.h */,
				049A8313211ECD6B00AA2048 /* dh.h */,
				049A8314211ECD6B00AA2048 /* dsa.h */,
				049A8315211ECD6B00AA2048 /* dso.h */,
				049A8316211ECD6B00AA2048 /* dtls1.h */,
				049A8317211ECD6B00AA2048 /* e_os2.h */,
				049A8318211ECD6B00AA2048 /* ebcdic.h */,
				049A8319211ECD6B00AA2048 /* ec.h */,
				049A831A211ECD6B00AA2048 /* ecdh.h */,
				049A831B211ECD6B00AA2048 /* ecdsa.h */,
				049A831C211ECD6B00AA2048 /* engine.h */,
				049A831D211ECD6B00AA2048 /* err.h */,
				049A831E211ECD6B00AA2048 /* evp.h */,
				049A831F211ECD6B00AA2048 /* hmac.h */,
				049A8320211ECD6B00AA2048 /* idea.h */,
				049A8321211ECD6B00AA2048 /* krb5_asn.h */,
				049A8322211ECD6B00AA2048 /* kssl.h */,
				049A8323211ECD6B00AA2048 /* lhash.h */,
				049A8324211ECD6B00AA2048 /* md4.h */,
				049A8325211ECD6B00AA2048 /* md5.h */,
				049A8326211ECD6B00AA2048 /* mdc2.h */,
				049A8327211ECD6B00AA2048 /* modes.h */,
				049A8328211ECD6B00AA2048 /* obj_mac.h */,
				049A8329211ECD6B00AA2048 /* objects.h */,
				049A832A211ECD6B00AA2048 /* ocsp.h */,
				049A832B211ECD6B00AA2048 /* opensslconf.h */,
				049A832C211ECD6B00AA2048 /* opensslv.h */,
				049A832D211ECD6B00AA2048 /* ossl_typ.h */,
				049A832E211ECD6B00AA2048 /* pem.h */,
				049A832F211ECD6B00AA2048 /* pem2.h */,
				049A8330211ECD6B00AA2048 /* pkcs12.h */,
				049A8331211ECD6B00AA2048 /* pkcs7.h */,
				049A8332211ECD6B00AA2048 /* pqueue.h */,
				049A8333211ECD6B00AA2048 /* rand.h */,
				049A8334211ECD6B00AA2048 /* rc2.h */,
				049A8335211ECD6B00AA2048 /* rc4.h */,
				049A8336211ECD6B00AA2048 /* ripemd.h */,
				049A8337211ECD6B00AA2048 /* rsa.h */,
				049A8338211ECD6B00AA2048 /* safestack.h */,
				049A8339211ECD6B00AA2048 /* seed.h */,
				049A833A211ECD6B00AA2048 /* sha.h */,
				049A833B211ECD6B00AA2048 /* srp.h */,
				049A833C211ECD6B00AA2048 /* srtp.h */,
				049A833D211ECD6B00AA2048 /* ssl.h */,
				049A833E211ECD6B00AA2048 /* ssl2.h */,
				049A833F211ECD6B00AA2048 /* ssl23.h */,
				049A8340211ECD6B00AA2048 /* ssl3.h */,
				049A8341211ECD6B00AA2048 /* stack.h */,
				049A8342211ECD6B00AA2048 /* symhacks.h */,
				049A8343211ECD6B00AA2048 /* tls1.h */,
				049A8344211ECD6B00AA2048 /* ts.h */,
				049A8345211ECD6B00AA2048 /* txt_db.h */,
				049A8346211ECD6B00AA2048 /* ui.h */,
				049A8347211ECD6B00AA2048 /* ui_compat.h */,
				049A8348211ECD6B00AA2048 /* whrlpool.h */,
				049A8349211ECD6B00AA2048 /* x509.h */,
				049A834A211ECD6B00AA2048 /* x509_vfy.h */,
				049A834B211ECD6B00AA2048 /* x509v3.h */,
			);
			path = openssl;
			sourceTree = "<group>";
		};
		049A834C211ECD6B00AA2048 /* libs */ = {
			isa = PBXGroup;
			children = (
				049A834D211ECD6B00AA2048 /* libcrypto.a */,
				049A834E211ECD6B00AA2048 /* libssl.a */,
			);
			path = libs;
			sourceTree = "<group>";
		};
		1813893D244B269A007F96FA /* TKOpenPlugin60037 */ = {
			isa = PBXGroup;
			children = (
				18138942244B269A007F96FA /* TKOpenPlugin60037.h */,
				1813893E244B269A007F96FA /* TKOpenPlugin60037.m */,
				1813893F244B269A007F96FA /* Controller */,
			);
			path = TKOpenPlugin60037;
			sourceTree = "<group>";
		};
		1813893F244B269A007F96FA /* Controller */ = {
			isa = PBXGroup;
			children = (
				18138940244B269A007F96FA /* TKTakeBankPhotoViewController.h */,
				18138941244B269A007F96FA /* TKTakeBankPhotoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		182CA7DB25BA6B820084C9F8 /* TKOpenPlugin60044 */ = {
			isa = PBXGroup;
			children = (
				182CA7DC25BA6B820084C9F8 /* TKOpenPlugin60044.h */,
				182CA7DD25BA6B820084C9F8 /* TKOpenPlugin60044.m */,
			);
			path = TKOpenPlugin60044;
			sourceTree = "<group>";
		};
		182F804425DFCDC900505CDE /* TKOpenPlugin60049 */ = {
			isa = PBXGroup;
			children = (
				182F804525DFCDC900505CDE /* TKOpenPlugin60049.h */,
				182F804625DFCDC900505CDE /* TKOpenPlugin60049.m */,
			);
			path = TKOpenPlugin60049;
			sourceTree = "<group>";
		};
		1876A14B2949CF57007F0500 /* Controller */ = {
			isa = PBXGroup;
			children = (
				1876A14C2949CF57007F0500 /* TKTakeIDPhotoViewController.h */,
				1876A14D2949CF57007F0500 /* TKTakeIDPhotoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		18C318EF25F74AAD003B36C7 /* TKOpenPlugin60026 */ = {
			isa = PBXGroup;
			children = (
				18C318F025F74AAD003B36C7 /* Util */,
				18C3190725F74AAD003B36C7 /* TKOpenPlugin60026.h */,
				18C3190B25F74AAD003B36C7 /* TKOpenPlugin60026.m */,
				18C3190825F74AAD003B36C7 /* Controller */,
				18C3190C25F74AAD003B36C7 /* View */,
			);
			path = TKOpenPlugin60026;
			sourceTree = "<group>";
		};
		18C318F025F74AAD003B36C7 /* Util */ = {
			isa = PBXGroup;
			children = (
				BE4F17172E1D265D009DCF6C /* SubtitlesRenderer */,
				BE4F171A2E1D265D009DCF6C /* TTSCloud */,
				338924892AB827AD005C6B5C /* TKringBuf.cpp */,
				338924882AB827AD005C6B5C /* TKringBuf.h */,
				3389248C2AB827FF005C6B5C /* TKNLSPlayAudio.h */,
				3389248B2AB827FF005C6B5C /* TKNLSPlayAudio.mm */,
				18C318F125F74AAD003B36C7 /* SpeechSynthesis */,
				18C318FC25F74AAD003B36C7 /* SpeechRecognize */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		18C318F125F74AAD003B36C7 /* SpeechSynthesis */ = {
			isa = PBXGroup;
			children = (
				DC9BF92D2617249200A37295 /* TKiflySpeechSynthesisManager.h */,
				DC9BF92E2617249200A37295 /* TKiflySpeechSynthesisManager.m */,
				18C318F625F74AAD003B36C7 /* TKSpeechSynthesisManager.h */,
				18C318F425F74AAD003B36C7 /* TKSpeechSynthesisManager.m */,
				18C318F225F74AAD003B36C7 /* TKSpeechSynthesisManagerProtocol.h */,
			);
			path = SpeechSynthesis;
			sourceTree = "<group>";
		};
		18C318FC25F74AAD003B36C7 /* SpeechRecognize */ = {
			isa = PBXGroup;
			children = (
				18C3190225F74AAD003B36C7 /* TKSpeechRecognizeManager.h */,
				18C318FE25F74AAD003B36C7 /* TKSpeechRecognizeManager.m */,
				18C318FF25F74AAD003B36C7 /* TKSpeechRecognizeManagerProtocol.h */,
				DC9BF938261C06AB00A37295 /* TKiflySpeechRecognizeManager.h */,
				DC9BF939261C06AB00A37295 /* TKiflySpeechRecognizeManager.m */,
			);
			path = SpeechRecognize;
			sourceTree = "<group>";
		};
		18C3190825F74AAD003B36C7 /* Controller */ = {
			isa = PBXGroup;
			children = (
				18C3190925F74AAD003B36C7 /* TKOneWayVideoViewController.h */,
				18C3190A25F74AAD003B36C7 /* TKOneWayVideoViewController.mm */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		18C3190C25F74AAD003B36C7 /* View */ = {
			isa = PBXGroup;
			children = (
				18C3190F25F74AAD003B36C7 /* TKOneWayVideoEndView.h */,
				18C3190D25F74AAD003B36C7 /* TKOneWayVideoEndView.m */,
				18C3191025F74AAD003B36C7 /* TKOneWayVideoView.h */,
				18C3190E25F74AAD003B36C7 /* TKOneWayVideoView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18F129E725FEF96000F26F5C /* FaceDetect */ = {
			isa = PBXGroup;
			children = (
				18F129E825FEF96000F26F5C /* TKFaceDetectManager.h */,
				18F129E925FEF96000F26F5C /* TKFaceDetectManager.m */,
			);
			path = FaceDetect;
			sourceTree = "<group>";
		};
		18FF460A28E1B7AE0026440D /* libSTSilentLivenessController */ = {
			isa = PBXGroup;
			children = (
				33C502B12A0A4D860050DB0C /* STLivenessDetector.xcframework */,
				18FF460B28E1B7AE0026440D /* SenseID_Liveness_Silent.lic */,
				18FF460C28E1B7AE0026440D /* STLivenessModel.bundle */,
			);
			path = libSTSilentLivenessController;
			sourceTree = "<group>";
		};
		33575EAE2D2E2553009B7B83 /* Controller */ = {
			isa = PBXGroup;
			children = (
				33575EAC2D2E2553009B7B83 /* TKSmartVirtualManViewController.h */,
				33575EAD2D2E2553009B7B83 /* TKSmartVirtualManViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		33575EB32D2E2553009B7B83 /* View */ = {
			isa = PBXGroup;
			children = (
				33575EAF2D2E2553009B7B83 /* TKSmartVirtualManVideoEndView.h */,
				33575EB02D2E2553009B7B83 /* TKSmartVirtualManVideoEndView.m */,
				33575EB12D2E2553009B7B83 /* TKSmartVirtualManVideoView.h */,
				33575EB22D2E2553009B7B83 /* TKSmartVirtualManVideoView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		33575EB62D2E2553009B7B83 /* TKOpenPlugin60072 */ = {
			isa = PBXGroup;
			children = (
				33575EAE2D2E2553009B7B83 /* Controller */,
				33575EB32D2E2553009B7B83 /* View */,
				33575EB42D2E2553009B7B83 /* TKOpenPlugin60072.h */,
				33575EB52D2E2553009B7B83 /* TKOpenPlugin60072.m */,
			);
			path = TKOpenPlugin60072;
			sourceTree = "<group>";
		};
		338924812AB8261E005C6B5C /* ReadingView */ = {
			isa = PBXGroup;
			children = (
				338924822AB8261E005C6B5C /* TKReadingView.m */,
				338924832AB8261E005C6B5C /* TKReadingView.h */,
			);
			path = ReadingView;
			sourceTree = "<group>";
		};
		338DF1C62B038104005DFCD1 /* include */ = {
			isa = PBXGroup;
			children = (
				338DF1C72B038104005DFCD1 /* AnyChatErrorCode.h */,
				338DF1C82B038104005DFCD1 /* AnyChatPlatform.h */,
				338DF1C92B038104005DFCD1 /* AnyChatDefine.h */,
				338DF1CA2B038104005DFCD1 /* AnyChatObjectDefine.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		33B3653C2D375AB80078392A /* Controller */ = {
			isa = PBXGroup;
			children = (
				33B3653A2D375AB80078392A /* TKNewOneWayVideoViewController.h */,
				33B3653B2D375AB80078392A /* TKNewOneWayVideoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		33B365412D375AB80078392A /* ControlView */ = {
			isa = PBXGroup;
			children = (
				33B3653D2D375AB80078392A /* TKNewOneWayLandScapeControlView.h */,
				33B3653E2D375AB80078392A /* TKNewOneWayLandScapeControlView.m */,
				33B3653F2D375AB80078392A /* TKNewOneWayPlayerControlView.h */,
				33B365402D375AB80078392A /* TKNewOneWayPlayerControlView.m */,
			);
			path = ControlView;
			sourceTree = "<group>";
		};
		33B365462D375AB80078392A /* View */ = {
			isa = PBXGroup;
			children = (
				33B365412D375AB80078392A /* ControlView */,
				33B365422D375AB80078392A /* TKNewOneWayVideoEndView.h */,
				33B365432D375AB80078392A /* TKNewOneWayVideoEndView.m */,
				33B365442D375AB80078392A /* TKNewOneWayVideoView.h */,
				33B365452D375AB80078392A /* TKNewOneWayVideoView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		33B365492D375AB80078392A /* TKOpenPlugin600261 */ = {
			isa = PBXGroup;
			children = (
				33B3653C2D375AB80078392A /* Controller */,
				33B365462D375AB80078392A /* View */,
				33B365472D375AB80078392A /* TKOpenPlugin600261.h */,
				33B365482D375AB80078392A /* TKOpenPlugin600261.m */,
			);
			path = TKOpenPlugin600261;
			sourceTree = "<group>";
		};
		33CB8E372DA67FF6000EA7C4 /* Category */ = {
			isa = PBXGroup;
			children = (
				33CB8E312DA67FF6000EA7C4 /* UIImageView+TKZFCache.h */,
				33CB8E322DA67FF6000EA7C4 /* UIImageView+TKZFCache.m */,
				33CB8E332DA67FF6000EA7C4 /* UIScrollView+TKZFPlayer.h */,
				33CB8E342DA67FF6000EA7C4 /* UIScrollView+TKZFPlayer.m */,
				33CB8E352DA67FF6000EA7C4 /* UIView+TKZFFrame.h */,
				33CB8E362DA67FF6000EA7C4 /* UIView+TKZFFrame.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		33CB8E4C2DA67FF6000EA7C4 /* Orientation */ = {
			isa = PBXGroup;
			children = (
				33CB8E382DA67FF6000EA7C4 /* TKZFLandscapeRotationManager.h */,
				33CB8E392DA67FF6000EA7C4 /* TKZFLandscapeRotationManager.m */,
				33CB8E3A2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS15.h */,
				33CB8E3B2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS15.m */,
				33CB8E3C2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS16.h */,
				33CB8E3D2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS16.m */,
				33CB8E3E2DA67FF6000EA7C4 /* TKZFLandscapeViewController.h */,
				33CB8E3F2DA67FF6000EA7C4 /* TKZFLandscapeViewController.m */,
				33CB8E402DA67FF6000EA7C4 /* TKZFLandscapeViewController_iOS15.h */,
				33CB8E412DA67FF6000EA7C4 /* TKZFLandscapeViewController_iOS15.m */,
				33CB8E422DA67FF6000EA7C4 /* TKZFLandscapeWindow.h */,
				33CB8E432DA67FF6000EA7C4 /* TKZFLandscapeWindow.m */,
				33CB8E442DA67FF6000EA7C4 /* TKZFOrientationObserver.h */,
				33CB8E452DA67FF6000EA7C4 /* TKZFOrientationObserver.m */,
				33CB8E462DA67FF6000EA7C4 /* TKZFPersentInteractiveTransition.h */,
				33CB8E472DA67FF6000EA7C4 /* TKZFPersentInteractiveTransition.m */,
				33CB8E482DA67FF6000EA7C4 /* TKZFPortraitViewController.h */,
				33CB8E492DA67FF6000EA7C4 /* TKZFPortraitViewController.m */,
				33CB8E4A2DA67FF6000EA7C4 /* TKZFPresentTransition.h */,
				33CB8E4B2DA67FF6000EA7C4 /* TKZFPresentTransition.m */,
			);
			path = Orientation;
			sourceTree = "<group>";
		};
		33CB8E4F2DA67FF6000EA7C4 /* Protocols */ = {
			isa = PBXGroup;
			children = (
				33CB8E4D2DA67FF6000EA7C4 /* TKZFPlayerMediaControl.h */,
				33CB8E4E2DA67FF6000EA7C4 /* TKZFPlayerMediaPlayback.h */,
			);
			path = Protocols;
			sourceTree = "<group>";
		};
		33CB8E5C2DA67FF6000EA7C4 /* Utils */ = {
			isa = PBXGroup;
			children = (
				33CB8E502DA67FF6000EA7C4 /* TKZFKVOController.h */,
				33CB8E512DA67FF6000EA7C4 /* TKZFKVOController.m */,
				33CB8E522DA67FF6000EA7C4 /* TKZFNetworkSpeedMonitor.h */,
				33CB8E532DA67FF6000EA7C4 /* TKZFNetworkSpeedMonitor.m */,
				33CB8E542DA67FF6000EA7C4 /* TKZFPlayerLogManager.h */,
				33CB8E552DA67FF6000EA7C4 /* TKZFPlayerLogManager.m */,
				33CB8E562DA67FF6000EA7C4 /* TKZFPlayerNotification.h */,
				33CB8E572DA67FF6000EA7C4 /* TKZFPlayerNotification.m */,
				33CB8E582DA67FF6000EA7C4 /* TKZFReachabilityManager.h */,
				33CB8E592DA67FF6000EA7C4 /* TKZFReachabilityManager.m */,
				33CB8E5A2DA67FF6000EA7C4 /* TKZFUtilities.h */,
				33CB8E5B2DA67FF6000EA7C4 /* TKZFUtilities.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		33CB8E632DA67FF6000EA7C4 /* ControlView */ = {
			isa = PBXGroup;
			children = (
				33CB8E5D2DA67FF6000EA7C4 /* TKPlayerControlView.h */,
				33CB8E5E2DA67FF6000EA7C4 /* TKPlayerControlView.m */,
				33CB8E5F2DA67FF6000EA7C4 /* TKZFLandScapeControlView.h */,
				33CB8E602DA67FF6000EA7C4 /* TKZFLandScapeControlView.m */,
				33CB8E612DA67FF6000EA7C4 /* TKZFPortraitControlView.h */,
				33CB8E622DA67FF6000EA7C4 /* TKZFPortraitControlView.m */,
			);
			path = ControlView;
			sourceTree = "<group>";
		};
		33CB8E682DA67FF6000EA7C4 /* FloatView */ = {
			isa = PBXGroup;
			children = (
				33CB8E642DA67FF6000EA7C4 /* TKZFFloatView.h */,
				33CB8E652DA67FF6000EA7C4 /* TKZFFloatView.m */,
				33CB8E662DA67FF6000EA7C4 /* TKZFSmallFloatControlView.h */,
				33CB8E672DA67FF6000EA7C4 /* TKZFSmallFloatControlView.m */,
			);
			path = FloatView;
			sourceTree = "<group>";
		};
		33CB8E6F2DA67FF6000EA7C4 /* FragmentView */ = {
			isa = PBXGroup;
			children = (
				33CB8E692DA67FF6000EA7C4 /* TKFragmentTableViewCell.h */,
				33CB8E6A2DA67FF6000EA7C4 /* TKFragmentTableViewCell.m */,
				33CB8E6B2DA67FF6000EA7C4 /* TKFragmentVideoView.h */,
				33CB8E6C2DA67FF6000EA7C4 /* TKFragmentVideoView.m */,
				33CB8E6D2DA67FF6000EA7C4 /* TKVideoFragmentModel.h */,
				33CB8E6E2DA67FF6000EA7C4 /* TKVideoFragmentModel.m */,
			);
			path = FragmentView;
			sourceTree = "<group>";
		};
		33CB8E742DA67FF6000EA7C4 /* LoadingView */ = {
			isa = PBXGroup;
			children = (
				33CB8E702DA67FF6000EA7C4 /* TKZFLoadingView.h */,
				33CB8E712DA67FF6000EA7C4 /* TKZFLoadingView.m */,
				33CB8E722DA67FF6000EA7C4 /* TKZFSpeedLoadingView.h */,
				33CB8E732DA67FF6000EA7C4 /* TKZFSpeedLoadingView.m */,
			);
			path = LoadingView;
			sourceTree = "<group>";
		};
		33CB8E772DA67FF6000EA7C4 /* SliderView */ = {
			isa = PBXGroup;
			children = (
				33CB8E752DA67FF6000EA7C4 /* TKZFSliderView.h */,
				33CB8E762DA67FF6000EA7C4 /* TKZFSliderView.m */,
			);
			path = SliderView;
			sourceTree = "<group>";
		};
		33CB8E7A2DA67FF6000EA7C4 /* SpeedSelectView */ = {
			isa = PBXGroup;
			children = (
				33CB8E782DA67FF6000EA7C4 /* TKSpeedSelectView.h */,
				33CB8E792DA67FF6000EA7C4 /* TKSpeedSelectView.m */,
			);
			path = SpeedSelectView;
			sourceTree = "<group>";
		};
		33CB8E7D2DA67FF6000EA7C4 /* StatusBar */ = {
			isa = PBXGroup;
			children = (
				33CB8E7B2DA67FF6000EA7C4 /* TKZFPlayerStatusBar.h */,
				33CB8E7C2DA67FF6000EA7C4 /* TKZFPlayerStatusBar.m */,
			);
			path = StatusBar;
			sourceTree = "<group>";
		};
		33CB8E802DA67FF6000EA7C4 /* VolumeBrightness */ = {
			isa = PBXGroup;
			children = (
				33CB8E7E2DA67FF6000EA7C4 /* TKZFVolumeBrightnessView.h */,
				33CB8E7F2DA67FF6000EA7C4 /* TKZFVolumeBrightnessView.m */,
			);
			path = VolumeBrightness;
			sourceTree = "<group>";
		};
		33CB8E832DA67FF6000EA7C4 /* View */ = {
			isa = PBXGroup;
			children = (
				33CB8E632DA67FF6000EA7C4 /* ControlView */,
				33CB8E682DA67FF6000EA7C4 /* FloatView */,
				33CB8E6F2DA67FF6000EA7C4 /* FragmentView */,
				33CB8E742DA67FF6000EA7C4 /* LoadingView */,
				33CB8E772DA67FF6000EA7C4 /* SliderView */,
				33CB8E7A2DA67FF6000EA7C4 /* SpeedSelectView */,
				33CB8E7D2DA67FF6000EA7C4 /* StatusBar */,
				33CB8E802DA67FF6000EA7C4 /* VolumeBrightness */,
				33CB8E812DA67FF6000EA7C4 /* TKZFPlayerView.h */,
				33CB8E822DA67FF6000EA7C4 /* TKZFPlayerView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		33CB8E8B2DA67FF6000EA7C4 /* Player */ = {
			isa = PBXGroup;
			children = (
				33CB8E372DA67FF6000EA7C4 /* Category */,
				33CB8E4C2DA67FF6000EA7C4 /* Orientation */,
				33CB8E4F2DA67FF6000EA7C4 /* Protocols */,
				33CB8E5C2DA67FF6000EA7C4 /* Utils */,
				33CB8E832DA67FF6000EA7C4 /* View */,
				33CB8E842DA67FF6000EA7C4 /* TKPlayer.h */,
				33CB8E852DA67FF6000EA7C4 /* TKPlayer.m */,
				33CB8E862DA67FF6000EA7C4 /* TKZFPlayerConst.h */,
				33CB8E872DA67FF6000EA7C4 /* TKZFPlayerController.h */,
				33CB8E882DA67FF6000EA7C4 /* TKZFPlayerController.m */,
				33CB8E892DA67FF6000EA7C4 /* TKZFPlayerGestureControl.h */,
				33CB8E8A2DA67FF6000EA7C4 /* TKZFPlayerGestureControl.m */,
			);
			path = Player;
			sourceTree = "<group>";
		};
		33F248CD29713A4E00547805 /* view */ = {
			isa = PBXGroup;
			children = (
				33F248CE29713BA900547805 /* TKCardPreview.h */,
				33F248CF29713BA900547805 /* TKCardPreview.m */,
			);
			path = view;
			sourceTree = "<group>";
		};
		33F50ECB2A92F52900246237 /* TKOpenPlugin60005 */ = {
			isa = PBXGroup;
			children = (
				33F50ECC2A92F52900246237 /* TKOpenPlugin60005.m */,
				33F50ECD2A92F52900246237 /* SmartCtr */,
				33F50ED02A92F52900246237 /* Resources */,
				33F50ED82A92F52900246237 /* TKChatService.m */,
				33F50ED92A92F52900246237 /* Controller */,
				33F50EE02A92F52900246237 /* TKOpenPlugin60005.h */,
				33F50EE12A92F52900246237 /* View */,
				33F50EEA2A92F52900246237 /* TKChatServiceDelegate.h */,
				33F50EEB2A92F52900246237 /* TKChatService.h */,
			);
			path = TKOpenPlugin60005;
			sourceTree = "<group>";
		};
		33F50ECD2A92F52900246237 /* SmartCtr */ = {
			isa = PBXGroup;
			children = (
				33F50ECE2A92F52900246237 /* TKSmartTwoVideoController.m */,
				33F50ECF2A92F52900246237 /* TKSmartTwoVideoController.h */,
			);
			path = SmartCtr;
			sourceTree = "<group>";
		};
		33F50ED02A92F52900246237 /* Resources */ = {
			isa = PBXGroup;
			children = (
				33F50F102A92F60200246237 /* chat_cancel_btn.png */,
				33F50F0F2A92F60200246237 /* kefu_bg_img.png */,
				33F50F0D2A92F60200246237 /* page_bg.jpg */,
				33F50F132A92F60200246237 /* tk_video_icon_iphone.png */,
				33F50F112A92F60200246237 /* TKAnyChatViewController.xib */,
				33F50F0E2A92F60200246237 /* TKAnyChatViewController4.xib */,
				33F50F122A92F60200246237 /* TKVideoWitnessViewController.xib */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		33F50ED92A92F52900246237 /* Controller */ = {
			isa = PBXGroup;
			children = (
				33F50EDA2A92F52900246237 /* TKVideoWitnessViewController+CommonKit.h */,
				33F50EDB2A92F52900246237 /* TKVideoWitnessViewController+AnyChat.h */,
				33F50EDC2A92F52900246237 /* TKVideoWitnessViewController.h */,
				33F50EDD2A92F52900246237 /* TKVideoWitnessViewController+CommonKit.m */,
				33F50EDE2A92F52900246237 /* TKVideoWitnessViewController+AnyChat.m */,
				33F50EDF2A92F52900246237 /* TKVideoWitnessViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		33F50EE12A92F52900246237 /* View */ = {
			isa = PBXGroup;
			children = (
				33F50EE22A92F52900246237 /* TKOpenQueueView.h */,
				33F50EE32A92F52900246237 /* TKOpenVideoChatView.h */,
				33F50EE42A92F52900246237 /* TKVideoReadAgreeView.h */,
				33F50EE52A92F52900246237 /* TKVideoMsgTableViewCell.h */,
				33F50EE62A92F52900246237 /* TKOpenVideoChatView.m */,
				33F50EE72A92F52900246237 /* TKOpenQueueView.m */,
				33F50EE82A92F52900246237 /* TKVideoReadAgreeView.m */,
				33F50EE92A92F52900246237 /* TKVideoMsgTableViewCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		33F50EEC2A92F52900246237 /* TKOpenPlugin60034 */ = {
			isa = PBXGroup;
			children = (
				33F50EED2A92F52900246237 /* TKOpenPlugin60034.m */,
				33F50EEE2A92F52900246237 /* Controller */,
				33F50EF12A92F52900246237 /* TKOpenPlugin60034.h */,
				33F50EF22A92F52900246237 /* View */,
				33F50EF52A92F52900246237 /* Helper */,
			);
			path = TKOpenPlugin60034;
			sourceTree = "<group>";
		};
		33F50EEE2A92F52900246237 /* Controller */ = {
			isa = PBXGroup;
			children = (
				33F50EEF2A92F52900246237 /* TKDirectVideoViewController.m */,
				33F50EF02A92F52900246237 /* TKDirectVideoViewController.h */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		33F50EF22A92F52900246237 /* View */ = {
			isa = PBXGroup;
			children = (
				33F50EF32A92F52900246237 /* TKDirectVideoChatView.m */,
				33F50EF42A92F52900246237 /* TKDirectVideoChatView.h */,
			);
			path = View;
			sourceTree = "<group>";
		};
		33F50EF52A92F52900246237 /* Helper */ = {
			isa = PBXGroup;
			children = (
				33F50EF62A92F52900246237 /* TKDirectVideoModel.h */,
				33F50EF72A92F52900246237 /* TKDirectVideoModel.m */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		33F50F262A92F8C400246237 /* Statistic */ = {
			isa = PBXGroup;
			children = (
				33F50F272A92F8C400246237 /* TKStatisticEventHelper.h */,
				33F50F282A92F8C400246237 /* TKStatisticEventHelperPrivate.h */,
				33F50F292A92F8C400246237 /* TKStatisticEventHelper.m */,
			);
			path = Statistic;
			sourceTree = "<group>";
		};
		B32E91C41F18C95E005A6B6D /* TChat */ = {
			isa = PBXGroup;
			children = (
				B32E91C51F18C95E005A6B6D /* SDK */,
				B32E91C71F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.h */,
				B32E91C81F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.m */,
			);
			path = TChat;
			sourceTree = "<group>";
		};
		B32E91C51F18C95E005A6B6D /* SDK */ = {
			isa = PBXGroup;
			children = (
				33294B102A0A469800A72052 /* TChat.framework */,
			);
			path = SDK;
			sourceTree = "<group>";
		};
		B35569F41DDBF96B00C76E7C /* Plugins */ = {
			isa = PBXGroup;
			children = (
				33B365492D375AB80078392A /* TKOpenPlugin600261 */,
				33575EB62D2E2553009B7B83 /* TKOpenPlugin60072 */,
				33F50ECB2A92F52900246237 /* TKOpenPlugin60005 */,
				33F50EEC2A92F52900246237 /* TKOpenPlugin60034 */,
				048DC98E2407E66400A6F3EC /* TKOpenPlugin60030 */,
				18C318EF25F74AAD003B36C7 /* TKOpenPlugin60026 */,
				182F804425DFCDC900505CDE /* TKOpenPlugin60049 */,
				182CA7DB25BA6B820084C9F8 /* TKOpenPlugin60044 */,
				1813893D244B269A007F96FA /* TKOpenPlugin60037 */,
				045EF85F241BA74F00032B22 /* TKOpenPlugin60032 */,
				043015E823FCC755004F0C17 /* TKOpenPlugin60028 */,
				0472CA7A23FB9DD4000B5EB7 /* TKOpenPlugin60007 */,
				0465234B212E84C000F8C0D0 /* TKOpenPlugin60022 */,
				B35569F51DDBF96B00C76E7C /* TKOpenPlugin60000 */,
				B35569F81DDBF96B00C76E7C /* TKOpenPlugin60001 */,
				B35569FB1DDBF96B00C76E7C /* TKOpenPlugin60002 */,
				B3556A011DDBF96B00C76E7C /* TKOpenPlugin60003 */,
				B3556A041DDBF96B00C76E7C /* TKOpenPlugin60004 */,
				B3556A3F1DDBF96B00C76E7C /* TKOpenPlugin60010 */,
				B3556A421DDBF96B00C76E7C /* TKOpenPlugin60013 */,
				B3556A881DDBF96B00C76E7C /* TKOpenPlugin60017 */,
				B3556A8B1DDBF96B00C76E7C /* TKOpenPlugin60018 */,
				B386BA0B1F947E6C001CBD13 /* TKOpenPlugin60099 */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		B35569F51DDBF96B00C76E7C /* TKOpenPlugin60000 */ = {
			isa = PBXGroup;
			children = (
				B35569F61DDBF96B00C76E7C /* TKOpenPlugin60000.h */,
				B35569F71DDBF96B00C76E7C /* TKOpenPlugin60000.m */,
			);
			path = TKOpenPlugin60000;
			sourceTree = "<group>";
		};
		B35569F81DDBF96B00C76E7C /* TKOpenPlugin60001 */ = {
			isa = PBXGroup;
			children = (
				B35569F91DDBF96B00C76E7C /* TKOpenPlugin60001.h */,
				B35569FA1DDBF96B00C76E7C /* TKOpenPlugin60001.m */,
			);
			path = TKOpenPlugin60001;
			sourceTree = "<group>";
		};
		B35569FB1DDBF96B00C76E7C /* TKOpenPlugin60002 */ = {
			isa = PBXGroup;
			children = (
				B35569FC1DDBF96B00C76E7C /* TKOpenPlugin60002.h */,
				B35569FD1DDBF96B00C76E7C /* TKOpenPlugin60002.m */,
				B35569FE1DDBF96B00C76E7C /* views */,
			);
			path = TKOpenPlugin60002;
			sourceTree = "<group>";
		};
		B35569FE1DDBF96B00C76E7C /* views */ = {
			isa = PBXGroup;
			children = (
				B35569FF1DDBF96B00C76E7C /* MTakeCardViewController.h */,
				B3556A001DDBF96B00C76E7C /* MTakeCardViewController.m */,
			);
			path = views;
			sourceTree = "<group>";
		};
		B3556A011DDBF96B00C76E7C /* TKOpenPlugin60003 */ = {
			isa = PBXGroup;
			children = (
				B3556A021DDBF96B00C76E7C /* TKOpenPlugin60003.h */,
				B3556A031DDBF96B00C76E7C /* TKOpenPlugin60003.m */,
			);
			path = TKOpenPlugin60003;
			sourceTree = "<group>";
		};
		B3556A041DDBF96B00C76E7C /* TKOpenPlugin60004 */ = {
			isa = PBXGroup;
			children = (
				B3556A051DDBF96B00C76E7C /* TKOpenPlugin60004.h */,
				B3556A061DDBF96B00C76E7C /* TKOpenPlugin60004.m */,
			);
			path = TKOpenPlugin60004;
			sourceTree = "<group>";
		};
		B3556A3F1DDBF96B00C76E7C /* TKOpenPlugin60010 */ = {
			isa = PBXGroup;
			children = (
				B3556A401DDBF96B00C76E7C /* TKOpenPlugin60010.h */,
				B3556A411DDBF96B00C76E7C /* TKOpenPlugin60010.m */,
			);
			path = TKOpenPlugin60010;
			sourceTree = "<group>";
		};
		B3556A421DDBF96B00C76E7C /* TKOpenPlugin60013 */ = {
			isa = PBXGroup;
			children = (
				33F248CD29713A4E00547805 /* view */,
				B3556A431DDBF96B00C76E7C /* MTakePhoto */,
				B3556A4A1DDBF96B00C76E7C /* TKOpenPlugin60013.h */,
				B3556A4B1DDBF96B00C76E7C /* TKOpenPlugin60013.m */,
			);
			path = TKOpenPlugin60013;
			sourceTree = "<group>";
		};
		B3556A431DDBF96B00C76E7C /* MTakePhoto */ = {
			isa = PBXGroup;
			children = (
				B3556A461DDBF96B00C76E7C /* MTakeBigPictureViewController.h */,
				B3556A471DDBF96B00C76E7C /* MTakeBigPictureViewController.m */,
				B3556A481DDBF96B00C76E7C /* MTakePhotoViewController.h */,
				B3556A491DDBF96B00C76E7C /* MTakePhotoViewController.m */,
			);
			path = MTakePhoto;
			sourceTree = "<group>";
		};
		B3556A881DDBF96B00C76E7C /* TKOpenPlugin60017 */ = {
			isa = PBXGroup;
			children = (
				B3556A891DDBF96B00C76E7C /* TKOpenPlugin60017.h */,
				B3556A8A1DDBF96B00C76E7C /* TKOpenPlugin60017.m */,
			);
			path = TKOpenPlugin60017;
			sourceTree = "<group>";
		};
		B3556A8B1DDBF96B00C76E7C /* TKOpenPlugin60018 */ = {
			isa = PBXGroup;
			children = (
				B3556A8C1DDBF96B00C76E7C /* TKOpenPlugin60018.h */,
				B3556A8D1DDBF96B00C76E7C /* TKOpenPlugin60018.m */,
			);
			path = TKOpenPlugin60018;
			sourceTree = "<group>";
		};
		B35EBF3C1C43942F0059F885 /* MDRadialProgress */ = {
			isa = PBXGroup;
			children = (
				B35EBF3D1C43942F0059F885 /* MDRadialProgressLabel.h */,
				B35EBF3E1C43942F0059F885 /* MDRadialProgressLabel.m */,
				B35EBF3F1C43942F0059F885 /* MDRadialProgressTheme.h */,
				B35EBF401C43942F0059F885 /* MDRadialProgressTheme.m */,
				B35EBF411C43942F0059F885 /* MDRadialProgressView.h */,
				B35EBF421C43942F0059F885 /* MDRadialProgressView.m */,
			);
			path = MDRadialProgress;
			sourceTree = "<group>";
		};
		B386BA0B1F947E6C001CBD13 /* TKOpenPlugin60099 */ = {
			isa = PBXGroup;
			children = (
				B386BA0C1F947E6C001CBD13 /* TKOpenPlugin60099.h */,
				B386BA0D1F947E6C001CBD13 /* TKOpenPlugin60099.m */,
			);
			path = TKOpenPlugin60099;
			sourceTree = "<group>";
		};
		B3AB88921C15704600571BE6 /* TKFMWK */ = {
			isa = PBXGroup;
			children = (
				045ACEA522A4FC30004D8557 /* TKWebViewApp.framework */,
				049A82E7211ECD6B00AA2048 /* THFMWK */,
				049A82E6211ECD6A00AA2048 /* TKAsset.bundle */,
				B3AB8AF91C15708900571BE6 /* TKOpenResource.bundle */,
			);
			path = TKFMWK;
			sourceTree = "<group>";
		};
		B3BF85B21F4D5D3A0060B3E9 /* AnyChat */ = {
			isa = PBXGroup;
			children = (
				338DF1C52B038104005DFCD1 /* AnyChatSDK.framework */,
				338DF1C62B038104005DFCD1 /* include */,
			);
			path = AnyChat;
			sourceTree = "<group>";
		};
		B3E1B30C1B8DB80800CDD258 /* TKOpenResource */ = {
			isa = PBXGroup;
			children = (
				048CB77824398BA70050B550 /* OpenInfo.xml */,
				043884B823B05A240009F14D /* KeyBoard.xml */,
				04D9604822C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml */,
				B3FF6E391EC5B2A0009FAF95 /* tk_open */,
				B32C870B1D87E96E00373C19 /* config.plist */,
				B30DC5061BCFC873007072FB /* Configuration.xml */,
				B30DC5071BCFC873007072FB /* OpenPlugin.xml */,
				B30DC5081BCFC873007072FB /* SystemPlugin.xml */,
			);
			path = TKOpenResource;
			sourceTree = "<group>";
		};
		B3FACBA81AFDDF0E0088CDF1 = {
			isa = PBXGroup;
			children = (
				B3E1B2EF1B8D9FEF00CDD258 /* Resources */,
				DCBFCEFC2A122B5600418EFE /* www */,
				BE6CCF3A1B5000B6003DCF98 /* Thinkive */,
				B3E1B30C1B8DB80800CDD258 /* TKOpenResource */,
				B3FACBDB1AFDE2590088CDF1 /* Frameworks */,
				B3FACBB31AFDDF0E0088CDF1 /* TKOpenAccount-Standard */,
				B3FACBB21AFDDF0E0088CDF1 /* Products */,
				DCC0429329434CF900BDF14D /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		B3FACBB21AFDDF0E0088CDF1 /* Products */ = {
			isa = PBXGroup;
			children = (
				B3FACBB11AFDDF0E0088CDF1 /* 思迪TChatRtc.app */,
				B3E1B30B1B8DB80800CDD258 /* TKOpenResource.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B3FACBB31AFDDF0E0088CDF1 /* TKOpenAccount-Standard */ = {
			isa = PBXGroup;
			children = (
				B3FACBB81AFDDF0E0088CDF1 /* AppDelegate.h */,
				B3FACBB91AFDDF0E0088CDF1 /* AppDelegate.m */,
				B3FACBBB1AFDDF0E0088CDF1 /* ViewController.h */,
				B3FACBBC1AFDDF0E0088CDF1 /* ViewController.m */,
				E22996F326AABD490039F6E9 /* SelectViewController.h */,
				E22996F426AABD490039F6E9 /* SelectViewController.m */,
				DCC7E86A262D7133006D378F /* TKTempService.h */,
				DCC7E868262D7133006D378F /* TKTempService.m */,
				1895FCAF26959A9B00513E5F /* TKOpenPrivacyAgreementView.h */,
				1895FCB126959A9B00513E5F /* TKOpenPrivacyAgreementView.m */,
				189F778726D8E6AD00F4089E /* TTTAttributedLabel.h */,
				189F778826D8E6AE00F4089E /* TTTAttributedLabel.m */,
				B3FACBBE1AFDDF0E0088CDF1 /* Main.storyboard */,
				B3FACBC11AFDDF0E0088CDF1 /* Images.xcassets */,
				B3FACBC31AFDDF0E0088CDF1 /* LaunchScreen.xib */,
				B3FACBB41AFDDF0E0088CDF1 /* Supporting Files */,
			);
			path = "TKOpenAccount-Standard";
			sourceTree = "<group>";
		};
		B3FACBB41AFDDF0E0088CDF1 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				B3FACBB51AFDDF0E0088CDF1 /* Info.plist */,
				B3FACBB61AFDDF0E0088CDF1 /* main.m */,
				B3FACCBC1AFDF2F80088CDF1 /* TKOpenAccount.pch */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		B3FACBDB1AFDE2590088CDF1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				18BBEF4E28A24AC100A26825 /* WebKit.framework */,
				18C3192525F74AE2003B36C7 /* CallKit.framework */,
				187942C7255CD0AC0067C701 /* AdSupport.framework */,
				040B88412148BC19008DBE05 /* VideoToolbox.framework */,
				B39E9312208F05CD006EC8A3 /* Accelerate.framework */,
				B39E9310208F054A006EC8A3 /* MessageUI.framework */,
				B39E930E208F0532006EC8A3 /* CoreMotion.framework */,
				B379D392206CD57000D1440E /* CoreTelephony.framework */,
				B33FDC111F692BE400C644F2 /* libresolv.tbd */,
				B33FDC0F1F692BCB00C644F2 /* libxml2.tbd */,
				B3C6ADC81C92ADEA00FE95F0 /* CoreMedia.framework */,
				B3C6ADC61C92ADDD00FE95F0 /* AssetsLibrary.framework */,
				B37F84281BE365230016F93D /* OpenGLES.framework */,
				B3FEEB351B845D6D00468924 /* AVFoundation.framework */,
				B39BADCF1B4D524F00F071D4 /* CoreLocation.framework */,
				B3B108831B44DF2800546D96 /* libiconv.dylib */,
				B3FACBF21AFDE3940088CDF1 /* libc++.dylib */,
				B3FACBF01AFDE3800088CDF1 /* libz.1.2.5.dylib */,
				B3FACBEE1AFDE3750088CDF1 /* libsqlite3.dylib */,
				B3FACBEC1AFDE35E0088CDF1 /* libstdc++.6.0.9.dylib */,
				B3FACBEA1AFDE3460088CDF1 /* CoreGraphics.framework */,
				B3FACBE81AFDE33A0088CDF1 /* Foundation.framework */,
				B3FACBE61AFDE31A0088CDF1 /* SystemConfiguration.framework */,
				B3FACBE41AFDE30B0088CDF1 /* CFNetwork.framework */,
				B3FACBE21AFDE3010088CDF1 /* MobileCoreServices.framework */,
				B3FACBE01AFDE2F40088CDF1 /* Security.framework */,
				B3FACBDE1AFDE2E50088CDF1 /* QuartzCore.framework */,
				B3FACBDC1AFDE2D10088CDF1 /* UIKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B3FB08B31F09EA5C00EC895E /* Modules */ = {
			isa = PBXGroup;
			children = (
				18FF460A28E1B7AE0026440D /* libSTSilentLivenessController */,
				B3FB08B41F09EA5C00EC895E /* Video */,
				DC9BF9332617254400A37295 /* Ifly */,
			);
			path = Modules;
			sourceTree = "<group>";
		};
		B3FB08B41F09EA5C00EC895E /* Video */ = {
			isa = PBXGroup;
			children = (
				B3BF85B21F4D5D3A0060B3E9 /* AnyChat */,
				B32E91C41F18C95E005A6B6D /* TChat */,
			);
			path = Video;
			sourceTree = "<group>";
		};
		BE4F17172E1D265D009DCF6C /* SubtitlesRenderer */ = {
			isa = PBXGroup;
			children = (
				BE4F17152E1D265D009DCF6C /* TKSubtitlesRenderer.h */,
				BE4F17162E1D265D009DCF6C /* TKSubtitlesRenderer.m */,
			);
			path = SubtitlesRenderer;
			sourceTree = "<group>";
		};
		BE4F171A2E1D265D009DCF6C /* TTSCloud */ = {
			isa = PBXGroup;
			children = (
				BE4F17182E1D265D009DCF6C /* TKTTSCloudPlayerManager.h */,
				BE4F17192E1D265D009DCF6C /* TKTTSCloudPlayerManager.mm */,
			);
			path = TTSCloud;
			sourceTree = "<group>";
		};
		BE6CCF3A1B5000B6003DCF98 /* Thinkive */ = {
			isa = PBXGroup;
			children = (
				047AF9672137BF5D003B1366 /* version.txt */,
				BE6CCFB41B5000B6003DCF98 /* Classes */,
				B3FB08B31F09EA5C00EC895E /* Modules */,
				B35569F41DDBF96B00C76E7C /* Plugins */,
				B3AB88921C15704600571BE6 /* TKFMWK */,
			);
			path = Thinkive;
			sourceTree = "<group>";
		};
		BE6CCFB41B5000B6003DCF98 /* Classes */ = {
			isa = PBXGroup;
			children = (
				DC84D1542857290700941BF5 /* Models */,
				DC84D1572857290700941BF5 /* Protocols */,
				BE6CCFB51B5000B6003DCF98 /* Controllers */,
				BE6CCFB81B5000B6003DCF98 /* Services */,
				BE6CCFBC1B5000B6003DCF98 /* Utils */,
				BE6CCFBF1B5000B6003DCF98 /* Vendors */,
				DCD47D3028E18E1600AE44C6 /* Views */,
			);
			path = Classes;
			sourceTree = "<group>";
		};
		BE6CCFB51B5000B6003DCF98 /* Controllers */ = {
			isa = PBXGroup;
			children = (
				B35EBF391C4393770059F885 /* MNavViewController.h */,
				B35EBF3A1C4393770059F885 /* MNavViewController.m */,
				B38D07B61FC402EC0013DA81 /* TKMAlertViewController.h */,
				B38D07B51FC402EC0013DA81 /* TKMAlertViewController.m */,
				BE6CCFB61B5000B6003DCF98 /* TKOpenController.h */,
				BE6CCFB71B5000B6003DCF98 /* TKOpenController.m */,
				B3E9E0D91C1170DC0012E37D /* TKOpenDelegate.h */,
				B30FD8A51EF1063F000D3E94 /* UIViewController+TKAuthorityKit.h */,
				B30FD8A61EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m */,
				0441008A23C43BD1005B9D05 /* TKFxcAccountInfoType.h */,
				0441008B23C43BD1005B9D05 /* TKFxcAccountInfoType.m */,
				************************ /* TKBaseVideoRecordViewController.h */,
				************************ /* TKBaseVideoRecordViewController.m */,
			);
			path = Controllers;
			sourceTree = "<group>";
		};
		BE6CCFB81B5000B6003DCF98 /* Services */ = {
			isa = PBXGroup;
			children = (
				BE6CCFB91B5000B6003DCF98 /* OpenAccount */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		BE6CCFB91B5000B6003DCF98 /* OpenAccount */ = {
			isa = PBXGroup;
			children = (
				BE6CCFBA1B5000B6003DCF98 /* TKOpenAccountService.h */,
				BE6CCFBB1B5000B6003DCF98 /* TKOpenAccountService.m */,
			);
			path = OpenAccount;
			sourceTree = "<group>";
		};
		BE6CCFBC1B5000B6003DCF98 /* Utils */ = {
			isa = PBXGroup;
			children = (
				33CB8E8B2DA67FF6000EA7C4 /* Player */,
				33F50F262A92F8C400246237 /* Statistic */,
				DCECF95A292F5EBB00A802EE /* SVG */,
				DCD47D2928E18CB800AE44C6 /* VideoRecord */,
				18F129E725FEF96000F26F5C /* FaceDetect */,
				B3386D691F271753006EF60A /* TKAVCaptureManager.h */,
				B3386D6A1F271753006EF60A /* TKAVCaptureManager.m */,
				B3FF6E3B1EC5B4F0009FAF95 /* TKYKHEmbedHelper.h */,
				B3FF6E3C1EC5B4F0009FAF95 /* TKYKHEmbedHelper.m */,
				B3A2DD311EA461A700B18842 /* TKCommonUtil.h */,
				B3A2DD321EA461A700B18842 /* TKCommonUtil.m */,
				B384CDC81B8177B400AFD817 /* TKButton.h */,
				B384CDC91B8177B400AFD817 /* TKButton.m */,
				B384CDCA1B8177B400AFD817 /* TKCameraTools.h */,
				B384CDCB1B8177B400AFD817 /* TKCameraTools.m */,
				187CC37028F15C5E003442D6 /* TKOpenViewStyleHelper.h */,
				187CC37128F15C5E003442D6 /* TKOpenViewStyleHelper.m */,
				18C46C35291A54A10076BAC0 /* TKChatTokenHelper.h */,
				18C46C36291A54A10076BAC0 /* TKChatTokenHelper.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		BE6CCFBF1B5000B6003DCF98 /* Vendors */ = {
			isa = PBXGroup;
			children = (
				B35EBF3C1C43942F0059F885 /* MDRadialProgress */,
				BE6CCFC31B5000B6003DCF98 /* YLProgressBar */,
			);
			path = Vendors;
			sourceTree = "<group>";
		};
		BE6CCFC31B5000B6003DCF98 /* YLProgressBar */ = {
			isa = PBXGroup;
			children = (
				BE6CCFC41B5000B6003DCF98 /* YLProgressBar.h */,
				BE6CCFC51B5000B6003DCF98 /* YLProgressBar.m */,
			);
			path = YLProgressBar;
			sourceTree = "<group>";
		};
		DC84D1542857290700941BF5 /* Models */ = {
			isa = PBXGroup;
			children = (
				33EA39DA2B4D24000038FBD6 /* TKPlayRangeModel.h */,
				33EA39DB2B4D24000038FBD6 /* TKPlayRangeModel.m */,
				************************ /* TKSmartQuestionModel.m */,
				DC84D1562857290700941BF5 /* TKSmartQuestionModel.h */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		DC84D1572857290700941BF5 /* Protocols */ = {
			isa = PBXGroup;
			children = (
				DC84D1582857290700941BF5 /* TKRecordManagerProtocol.h */,
				DC84D1592857290700941BF5 /* TKBaseVideoRecordViewProtocol.h */,
				DC84D15A2857290700941BF5 /* TKBaseVideoRecordEndViewProtocol.h */,
			);
			path = Protocols;
			sourceTree = "<group>";
		};
		DC9BF9332617254400A37295 /* Ifly */ = {
			isa = PBXGroup;
			children = (
				1878E03C2888F1E200C25151 /* AIPIFlyMSC.framework */,
			);
			path = Ifly;
			sourceTree = "<group>";
		};
		DCC0429329434CF900BDF14D /* Recovered References */ = {
			isa = PBXGroup;
			children = (
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		DCCDDA18295C2AC5002F268B /* FaceDetectTipView */ = {
			isa = PBXGroup;
			children = (
				DCCDDA19295C2AF5002F268B /* TKFaceDectTipView.h */,
				DCCDDA1A295C2AF5002F268B /* TKFaceDectTipView.m */,
			);
			path = FaceDetectTipView;
			sourceTree = "<group>";
		};
		DCD47D2928E18CB800AE44C6 /* VideoRecord */ = {
			isa = PBXGroup;
			children = (
				33B365372D375AA40078392A /* TKChatLocalVideoRecordManager.h */,
				33B365382D375AA40078392A /* TKChatLocalVideoRecordManager.mm */,
				3389246A2AB825B9005C6B5C /* TKSampleBufferConverter.h */,
				338924692AB825B9005C6B5C /* TKSampleBufferConverter.m */,
				338924662AB825B3005C6B5C /* TKVideoRecordManager.mm */,
				DCD47D2D28E18CB800AE44C6 /* TKVideoRecordManager.h */,
				33F50F302A92F8D200246237 /* TKAnyChatSmartTwoVideoManager.h */,
				33F50F2B2A92F8D200246237 /* TKAnyChatSmartTwoVideoManager.m */,
				33F50F2D2A92F8D200246237 /* TKSmartTwoVideoManager.h */,
				33F50F312A92F8D200246237 /* TKSmartTwoVideoManager.m */,
				33F50F2E2A92F8D200246237 /* TKSmartTwoVideoManagerProtocol.h */,
				33F50F2C2A92F8D200246237 /* TKTChatSmartTwoVideoManager.h */,
				33F50F2F2A92F8D200246237 /* TKTChatSmartTwoVideoManager.m */,
				33575EBB2D2E26D3009B7B83 /* TKChatVideoRecordManager.h */,
				33575EBC2D2E26D3009B7B83 /* TKChatVideoRecordManager.m */,
			);
			path = VideoRecord;
			sourceTree = "<group>";
		};
		DCD47D3028E18E1600AE44C6 /* Views */ = {
			isa = PBXGroup;
			children = (
				338924812AB8261E005C6B5C /* ReadingView */,
				DCCDDA18295C2AC5002F268B /* FaceDetectTipView */,
				DCD47D3328E18E1600AE44C6 /* TKBaseVideoRecordEndView.h */,
				DCD47D3128E18E1600AE44C6 /* TKBaseVideoRecordEndView.m */,
				DCD47D3428E18E1600AE44C6 /* TKBaseVideoRecordView.h */,
				DCD47D3228E18E1600AE44C6 /* TKBaseVideoRecordView.m */,
				DCC262C0291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.h */,
				DCC262C1291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m */,
				************************ /* TKBaseVideoRecordEndLandscapeView.h */,
				************************ /* TKBaseVideoRecordEndLandscapeView.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		DCECF95A292F5EBB00A802EE /* SVG */ = {
			isa = PBXGroup;
			children = (
				DCECF95D292F5EF000A802EE /* TKSVGBezierPath.h */,
				DCECF95E292F5EF000A802EE /* TKSVGBezierPath.mm */,
				DCECF961292F5EF000A802EE /* TKSVGEngine.h */,
				DCECF95B292F5EEF00A802EE /* TKSVGEngine.mm */,
				DCECF963292F5EF000A802EE /* TKSVGImageView.h */,
				DCECF964292F5EF000A802EE /* TKSVGImageView.m */,
				DCECF966292F5EF000A802EE /* TKSVGLayer.h */,
				DCECF95C292F5EF000A802EE /* TKSVGLayer.m */,
				DCECF96C292F628A00A802EE /* TKSVGImage.h */,
				DCECF96D292F628A00A802EE /* TKSVGImage.m */,
			);
			path = SVG;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B3E1B30A1B8DB80800CDD258 /* TKOpenResource */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B3E1B30F1B8DB80800CDD258 /* Build configuration list for PBXNativeTarget "TKOpenResource" */;
			buildPhases = (
				B3E1B3091B8DB80800CDD258 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = TKOpenResource;
			productName = TKOpenResource;
			productReference = B3E1B30B1B8DB80800CDD258 /* TKOpenResource.bundle */;
			productType = "com.apple.product-type.bundle";
		};
		B3FACBB01AFDDF0E0088CDF1 /* TKOpenAccount-Standard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B3FACBD41AFDDF0E0088CDF1 /* Build configuration list for PBXNativeTarget "TKOpenAccount-Standard" */;
			buildPhases = (
				B3FACBAD1AFDDF0E0088CDF1 /* Sources */,
				B3FACBAE1AFDDF0E0088CDF1 /* Frameworks */,
				B3FACBAF1AFDDF0E0088CDF1 /* Resources */,
				04EE357922732298009EA9D1 /* Embed Frameworks */,
				33294B0A2A0A453500A72052 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				B3AA8EC31EBB2B0400255737 /* PBXTargetDependency */,
			);
			name = "TKOpenAccount-Standard";
			productName = "TKOpenAccount-Standard";
			productReference = B3FACBB11AFDDF0E0088CDF1 /* 思迪TChatRtc.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B3FACBA91AFDDF0E0088CDF1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1230;
				LastUpgradeCheck = 0630;
				ORGANIZATIONNAME = thinkive;
				TargetAttributes = {
					B3D794591B8EA14F00768134 = {
						CreatedOnToolsVersion = 6.4;
					};
					B3E1B30A1B8DB80800CDD258 = {
						CreatedOnToolsVersion = 6.4;
						DevelopmentTeam = HCXVYWN652;
						ProvisioningStyle = Automatic;
					};
					B3FACBB01AFDDF0E0088CDF1 = {
						CreatedOnToolsVersion = 6.3;
					};
				};
			};
			buildConfigurationList = B3FACBAC1AFDDF0E0088CDF1 /* Build configuration list for PBXProject "TKOpenAccount-Standard" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = B3FACBA81AFDDF0E0088CDF1;
			productRefGroup = B3FACBB21AFDDF0E0088CDF1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B3FACBB01AFDDF0E0088CDF1 /* TKOpenAccount-Standard */,
				B3E1B30A1B8DB80800CDD258 /* TKOpenResource */,
				B3D794591B8EA14F00768134 /* build_TKOpenResource */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B3E1B3091B8DB80800CDD258 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				048CB77924398BA70050B550 /* OpenInfo.xml in Resources */,
				33F50F162A92F60200246237 /* kefu_bg_img.png in Resources */,
				04D9604922C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml in Resources */,
				33F50F172A92F60200246237 /* chat_cancel_btn.png in Resources */,
				B30DC50A1BCFC873007072FB /* Configuration.xml in Resources */,
				33F50F142A92F60200246237 /* page_bg.jpg in Resources */,
				B3E1B3121B8DB81400CDD258 /* Resources in Resources */,
				33F50F152A92F60200246237 /* TKAnyChatViewController4.xib in Resources */,
				33F50F182A92F60200246237 /* TKAnyChatViewController.xib in Resources */,
				33F50F192A92F60200246237 /* TKVideoWitnessViewController.xib in Resources */,
				33F50F1A2A92F60200246237 /* tk_video_icon_iphone.png in Resources */,
				B30DC50B1BCFC873007072FB /* OpenPlugin.xml in Resources */,
				B32C870C1D87E96E00373C19 /* config.plist in Resources */,
				043884B923B05A240009F14D /* KeyBoard.xml in Resources */,
				B3FF6E3A1EC5B2A0009FAF95 /* tk_open in Resources */,
				B30DC50C1BCFC873007072FB /* SystemPlugin.xml in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B3FACBAF1AFDDF0E0088CDF1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				18FF460F28E1B7AE0026440D /* STLivenessModel.bundle in Resources */,
				DCBFCEFD2A122B5600418EFE /* www in Resources */,
				B3FCF6131BAFB2F3009AB7C0 /* LaunchScreen.xib in Resources */,
				B3AB8AFA1C15708900571BE6 /* TKOpenResource.bundle in Resources */,
				B3FACBC21AFDDF0E0088CDF1 /* Images.xcassets in Resources */,
				047AF9682137BF5E003B1366 /* version.txt in Resources */,
				E22996F626AABFB90039F6E9 /* Main.storyboard in Resources */,
				18FF460E28E1B7AE0026440D /* SenseID_Liveness_Silent.lic in Resources */,
				049A8350211ECD6B00AA2048 /* TKAsset.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		33294B0A2A0A453500A72052 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "APP_PATH=\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\"\n\n# This script loops through the frameworks embedded in the application and\n\n# removes unused architectures.\n\nfind \"$APP_PATH\" -name '*.framework' -type d | while read -r FRAMEWORK\n\ndo\n\nFRAMEWORK_EXECUTABLE_NAME=$(defaults read \"$FRAMEWORK/Info.plist\" CFBundleExecutable)\n\nFRAMEWORK_EXECUTABLE_PATH=\"$FRAMEWORK/$FRAMEWORK_EXECUTABLE_NAME\"\n\necho \"Executable is $FRAMEWORK_EXECUTABLE_PATH\"\n\nEXTRACTED_ARCHS=()\n\nfor ARCH in $ARCHS\n\ndo\n\necho \"Extracting $ARCH from $FRAMEWORK_EXECUTABLE_NAME\"\n\nlipo -extract \"$ARCH\" \"$FRAMEWORK_EXECUTABLE_PATH\" -o \"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\"\n\nEXTRACTED_ARCHS+=(\"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\")\n\ndone\n\necho \"Merging extracted architectures: ${ARCHS}\"\n\nlipo -o \"$FRAMEWORK_EXECUTABLE_PATH-merged\" -create \"${EXTRACTED_ARCHS[@]}\"\n\nrm \"${EXTRACTED_ARCHS[@]}\"\n\necho \"Replacing original executable with thinned version\"\n\nrm \"$FRAMEWORK_EXECUTABLE_PATH\"\n\nmv \"$FRAMEWORK_EXECUTABLE_PATH-merged\" \"$FRAMEWORK_EXECUTABLE_PATH\"\n\ndone\n\n";
		};
		B3D7945D1B8EA15400768134 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Sets the target folders and the final framework product.\nFMK_NAME=TKOpenResource\n# Install dir will be the final output to the framework.\n# The following line create it in the root folder of the current project.\nINSTALL_DIR=${SRCROOT}/Thinkive/TKFMWK/${FMK_NAME}.bundle\n\n# Working dir will be deleted after the framework creation.\nWRK_DIR=build\nDEVICE_DIR=${WRK_DIR}/Release-iphoneos/${FMK_NAME}.bundle\n#SIMULATOR_DIR=${WRK_DIR}/Release-iphonesimulator/${FMK_NAME}.bundle\n# Building both architectures.\nxcodebuild -configuration \"Release\" -target \"${FMK_NAME}\" -sdk iphoneos\n#xcodebuild -configuration \"Release\" -target \"${FMK_NAME}\" -sdk iphonesimulator\n# Cleaning the oldest.\nif [ -d \"${INSTALL_DIR}\" ]\nthen\nrm -rf \"${INSTALL_DIR}\"\nfi\n# Creates and renews the final product folder.\nmkdir -p \"${INSTALL_DIR}\"\n# Copies the headers and resources files to the final product folder.\ncp -R \"${DEVICE_DIR}/\" \"${INSTALL_DIR}/\"\n\n# Cleaning the oldest.\nrm -r \"${WRK_DIR}\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B3FACBAD1AFDDF0E0088CDF1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				33EA39DC2B4D24000038FBD6 /* TKPlayRangeModel.m in Sources */,
				043015EC23FCC811004F0C17 /* TKOpenPlugin60028.m in Sources */,
				33F50F022A92F52A00246237 /* TKVideoWitnessViewController+CommonKit.m in Sources */,
				0472CA8623FB9DD4000B5EB7 /* TKOpenPlugin60007.m in Sources */,
				B30FD8A71EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m in Sources */,
				B3556A8E1DDBF96B00C76E7C /* TKOpenPlugin60000.m in Sources */,
				B384CDCC1B8177B400AFD817 /* TKButton.m in Sources */,
				B3556AB01DDBF96B00C76E7C /* MTakeBigPictureViewController.m in Sources */,
				18F129F825FF231600F26F5C /* TKOpenTipView.m in Sources */,
				0472CA8B23FB9EC4000B5EB7 /* TKLiveFaceViewController.m in Sources */,
				048DC9962407E66400A6F3EC /* TKOrdinaryOneVideoView.m in Sources */,
				18C3191F25F74AAD003B36C7 /* TKOneWayVideoViewController.mm in Sources */,
				189F778926D8E6AE00F4089E /* TTTAttributedLabel.m in Sources */,
				B35EBF451C43942F0059F885 /* MDRadialProgressView.m in Sources */,
				33F50F0B2A92F52A00246237 /* TKDirectVideoChatView.m in Sources */,
				33F50F2A2A92F8C400246237 /* TKStatisticEventHelper.m in Sources */,
				B35EBF3B1C4393770059F885 /* MNavViewController.m in Sources */,
				18C3192025F74AAD003B36C7 /* TKOpenPlugin60026.m in Sources */,
				33F50EF92A92F52900246237 /* TKSmartTwoVideoController.m in Sources */,
				DCC7E86B262D7134006D378F /* TKTempService.m in Sources */,
				182CA7DE25BA6B820084C9F8 /* TKOpenPlugin60044.m in Sources */,
				B3FACBBD1AFDDF0E0088CDF1 /* ViewController.m in Sources */,
				33F50F332A92F8D200246237 /* TKTChatSmartTwoVideoManager.m in Sources */,
				33575EBA2D2E2553009B7B83 /* TKOpenPlugin60072.m in Sources */,
				B384CDCD1B8177B400AFD817 /* TKCameraTools.m in Sources */,
				043015EF23FCC936004F0C17 /* TKFaceImageViewController.m in Sources */,
				BE6CD0471B5000B7003DCF98 /* YLProgressBar.m in Sources */,
				DCECF96E292F628A00A802EE /* TKSVGImage.m in Sources */,
				33B365392D375AA40078392A /* TKChatLocalVideoRecordManager.mm in Sources */,
				DCECF96B292F5EF000A802EE /* TKSVGImageView.m in Sources */,
				18138943244B269A007F96FA /* TKOpenPlugin60037.m in Sources */,
				33F50F082A92F52A00246237 /* TKVideoMsgTableViewCell.m in Sources */,
				33F50F012A92F52A00246237 /* TKChatService.m in Sources */,
				B3556AAE1DDBF96B00C76E7C /* TKOpenPlugin60010.m in Sources */,
				04652354212E84C100F8C0D0 /* TKSignatureController.m in Sources */,
				33575EB92D2E2553009B7B83 /* TKSmartVirtualManVideoView.m in Sources */,
				************************ /* TKVideoWitnessViewController+TChat.m in Sources */,
				33575EB72D2E2553009B7B83 /* TKSmartVirtualManViewController.m in Sources */,
				18C3192225F74AAD003B36C7 /* TKOneWayVideoView.m in Sources */,
				18C3191B25F74AAD003B36C7 /* TKSpeechRecognizeManager.m in Sources */,
				043015F323FCCDBF004F0C17 /* TKFaceImageView.m in Sources */,
				1876A14E2949CF57007F0500 /* TKTakeIDPhotoViewController.m in Sources */,
				E22996F526AABD490039F6E9 /* SelectViewController.m in Sources */,
				33F50F092A92F52A00246237 /* TKOpenPlugin60034.m in Sources */,
				B3556AB21DDBF96B00C76E7C /* TKOpenPlugin60013.m in Sources */,
				33F50F0C2A92F52A00246237 /* TKDirectVideoModel.m in Sources */,
				33575EB82D2E2553009B7B83 /* TKSmartVirtualManVideoEndView.m in Sources */,
				DCC262C2291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m in Sources */,
				048DC9992407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m in Sources */,
				DCD47D3528E18E1600AE44C6 /* TKBaseVideoRecordEndView.m in Sources */,
				181D4CA1286E94F600679EB3 /* TKVideoAlertView.m in Sources */,
				B3556A931DDBF96B00C76E7C /* TKOpenPlugin60004.m in Sources */,
				B3556AD91DDBF96B00C76E7C /* TKOpenPlugin60017.m in Sources */,
				B3386D6B1F271753006EF60A /* TKAVCaptureManager.m in Sources */,
				182F804725DFCDC900505CDE /* TKOpenPlugin60049.m in Sources */,
				18C3192125F74AAD003B36C7 /* TKOneWayVideoEndView.m in Sources */,
				B35EBF431C43942F0059F885 /* MDRadialProgressLabel.m in Sources */,
				18F12A1425FF24E000F26F5C /* TKOneWayVideoAlertTipView.m in Sources */,
				187F360026AAB4530046E9E3 /* TKFaceImageLandscapeView.m in Sources */,
				18034324291CD426002C8E2C /* TKPlayerToolView.m in Sources */,
				B3FACBBA1AFDDF0E0088CDF1 /* AppDelegate.m in Sources */,
				33F50F072A92F52A00246237 /* TKVideoReadAgreeView.m in Sources */,
				B35EBF441C43942F0059F885 /* MDRadialProgressTheme.m in Sources */,
				DCECF967292F5EF000A802EE /* TKSVGEngine.mm in Sources */,
				************************ /* TKBaseVideoRecordViewController.m in Sources */,
				04652355212E84C100F8C0D0 /* TKOpenPlugin60022.m in Sources */,
				33F50F342A92F8D200246237 /* TKSmartTwoVideoManager.m in Sources */,
				B3556A901DDBF96B00C76E7C /* TKOpenPlugin60002.m in Sources */,
				0441008C23C43BD1005B9D05 /* TKFxcAccountInfoType.m in Sources */,
				33F50F032A92F52A00246237 /* TKVideoWitnessViewController+AnyChat.m in Sources */,
				3389246D2AB825B9005C6B5C /* TKSampleBufferConverter.m in Sources */,
				DCECF968292F5EF000A802EE /* TKSVGLayer.m in Sources */,
				DCECF969292F5EF000A802EE /* TKSVGBezierPath.mm in Sources */,
				DCCDDA1B295C2AF5002F268B /* TKFaceDectTipView.m in Sources */,
				33F50F322A92F8D200246237 /* TKAnyChatSmartTwoVideoManager.m in Sources */,
				04652356212E84C100F8C0D0 /* TKTouchView.m in Sources */,
				************************ /* TKBaseVideoRecordEndLandscapeView.m in Sources */,
				18C46C37291A54A10076BAC0 /* TKChatTokenHelper.m in Sources */,
				18C3191725F74AAD003B36C7 /* TKSpeechSynthesisManager.m in Sources */,
				3389248A2AB827AD005C6B5C /* TKringBuf.cpp in Sources */,
				33F50EF82A92F52900246237 /* TKOpenPlugin60005.m in Sources */,
				BE6CD0441B5000B7003DCF98 /* TKOpenAccountService.m in Sources */,
				18F129EB25FEF96000F26F5C /* TKFaceDetectManager.m in Sources */,
				33575EBD2D2E26D3009B7B83 /* TKChatVideoRecordManager.m in Sources */,
				DCD47D3628E18E1600AE44C6 /* TKBaseVideoRecordView.m in Sources */,
				B38D07B71FC402EC0013DA81 /* TKMAlertViewController.m in Sources */,
				18138944244B269A007F96FA /* TKTakeBankPhotoViewController.m in Sources */,
				B3556A8F1DDBF96B00C76E7C /* TKOpenPlugin60001.m in Sources */,
				33F248D029713BA900547805 /* TKCardPreview.m in Sources */,
				338924672AB825B3005C6B5C /* TKVideoRecordManager.mm in Sources */,
				045EF862241BA81000032B22 /* TKOpenPlugin60032.m in Sources */,
				33F50F042A92F52A00246237 /* TKVideoWitnessViewController.m in Sources */,
				1895FCB326959A9B00513E5F /* TKOpenPrivacyAgreementView.m in Sources */,
				187F35FB26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m in Sources */,
				3389248D2AB827FF005C6B5C /* TKNLSPlayAudio.mm in Sources */,
				33CB8E8C2DA67FF6000EA7C4 /* TKZFKVOController.m in Sources */,
				33CB8E8D2DA67FF6000EA7C4 /* UIScrollView+TKZFPlayer.m in Sources */,
				33CB8E8E2DA67FF6000EA7C4 /* TKZFPortraitViewController.m in Sources */,
				33CB8E8F2DA67FF6000EA7C4 /* TKZFLandscapeRotationManager.m in Sources */,
				33CB8E902DA67FF6000EA7C4 /* UIView+TKZFFrame.m in Sources */,
				33CB8E912DA67FF6000EA7C4 /* TKZFPlayerNotification.m in Sources */,
				33CB8E922DA67FF6000EA7C4 /* TKZFLandscapeViewController.m in Sources */,
				33CB8E932DA67FF6000EA7C4 /* TKZFSmallFloatControlView.m in Sources */,
				33CB8E942DA67FF6000EA7C4 /* TKZFNetworkSpeedMonitor.m in Sources */,
				BE4F171B2E1D265D009DCF6C /* TKTTSCloudPlayerManager.mm in Sources */,
				BE4F171C2E1D265D009DCF6C /* TKSubtitlesRenderer.m in Sources */,
				33CB8E952DA67FF6000EA7C4 /* TKZFFloatView.m in Sources */,
				33CB8E962DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS15.m in Sources */,
				33CB8E972DA67FF6000EA7C4 /* TKZFPlayerStatusBar.m in Sources */,
				33CB8E982DA67FF6000EA7C4 /* TKZFPersentInteractiveTransition.m in Sources */,
				33CB8E992DA67FF6000EA7C4 /* TKPlayerControlView.m in Sources */,
				33CB8E9A2DA67FF6000EA7C4 /* TKZFReachabilityManager.m in Sources */,
				33CB8E9B2DA67FF6000EA7C4 /* TKFragmentVideoView.m in Sources */,
				33CB8E9C2DA67FF6000EA7C4 /* TKZFUtilities.m in Sources */,
				33CB8E9D2DA67FF6000EA7C4 /* TKPlayer.m in Sources */,
				33CB8E9E2DA67FF6000EA7C4 /* TKZFPresentTransition.m in Sources */,
				33CB8E9F2DA67FF6000EA7C4 /* TKZFLandscapeWindow.m in Sources */,
				33CB8EA02DA67FF6000EA7C4 /* TKZFLandscapeRotationManager_iOS16.m in Sources */,
				33CB8EA12DA67FF6000EA7C4 /* TKZFPlayerLogManager.m in Sources */,
				33CB8EA22DA67FF6000EA7C4 /* TKZFPlayerController.m in Sources */,
				33CB8EA32DA67FF6000EA7C4 /* TKZFSpeedLoadingView.m in Sources */,
				33CB8EA42DA67FF6000EA7C4 /* TKSpeedSelectView.m in Sources */,
				33CB8EA52DA67FF6000EA7C4 /* TKZFVolumeBrightnessView.m in Sources */,
				33CB8EA62DA67FF6000EA7C4 /* TKZFPortraitControlView.m in Sources */,
				33CB8EA72DA67FF6000EA7C4 /* TKZFLandScapeControlView.m in Sources */,
				33CB8EA82DA67FF6000EA7C4 /* TKFragmentTableViewCell.m in Sources */,
				33CB8EA92DA67FF6000EA7C4 /* TKVideoFragmentModel.m in Sources */,
				33CB8EAA2DA67FF6000EA7C4 /* TKZFPlayerGestureControl.m in Sources */,
				33CB8EAB2DA67FF6000EA7C4 /* TKZFLoadingView.m in Sources */,
				33CB8EAC2DA67FF6000EA7C4 /* TKZFLandscapeViewController_iOS15.m in Sources */,
				33CB8EAD2DA67FF6000EA7C4 /* TKZFOrientationObserver.m in Sources */,
				33CB8EAE2DA67FF6000EA7C4 /* TKZFSliderView.m in Sources */,
				33CB8EAF2DA67FF6000EA7C4 /* UIImageView+TKZFCache.m in Sources */,
				33CB8EB02DA67FF6000EA7C4 /* TKZFPlayerView.m in Sources */,
				************************ /* TKSmartQuestionModel.m in Sources */,
				B3556ADA1DDBF96B00C76E7C /* TKOpenPlugin60018.m in Sources */,
				B3A2DD331EA461A700B18842 /* TKCommonUtil.m in Sources */,
				B3556A911DDBF96B00C76E7C /* MTakeCardViewController.m in Sources */,
				0472CA8E23FBA05F000B5EB7 /* TKLiveFaceView.m in Sources */,
				DC9BF93A261C06AB00A37295 /* TKiflySpeechRecognizeManager.m in Sources */,
				042CE7052408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m in Sources */,
				338924842AB8261E005C6B5C /* TKReadingView.m in Sources */,
				DC9BF92F2617249200A37295 /* TKiflySpeechSynthesisManager.m in Sources */,
				33F50F052A92F52A00246237 /* TKOpenVideoChatView.m in Sources */,
				B3556A921DDBF96B00C76E7C /* TKOpenPlugin60003.m in Sources */,
				048DC9952407E66400A6F3EC /* TKOpenPlugin60030.m in Sources */,
				BE6CD0431B5000B7003DCF98 /* TKOpenController.m in Sources */,
				B3FACBB71AFDDF0E0088CDF1 /* main.m in Sources */,
				B386BA0E1F947E6C001CBD13 /* TKOpenPlugin60099.m in Sources */,
				33F50F0A2A92F52A00246237 /* TKDirectVideoViewController.m in Sources */,
				33B3654A2D375AB80078392A /* TKNewOneWayPlayerControlView.m in Sources */,
				33B3654B2D375AB80078392A /* TKOpenPlugin600261.m in Sources */,
				33B3654C2D375AB80078392A /* TKNewOneWayVideoViewController.m in Sources */,
				33B3654D2D375AB80078392A /* TKNewOneWayLandScapeControlView.m in Sources */,
				33B3654E2D375AB80078392A /* TKNewOneWayVideoEndView.m in Sources */,
				33B3654F2D375AB80078392A /* TKNewOneWayVideoView.m in Sources */,
				33F50F062A92F52A00246237 /* TKOpenQueueView.m in Sources */,
				B3556AB11DDBF96B00C76E7C /* MTakePhotoViewController.m in Sources */,
				187CC37228F15C5E003442D6 /* TKOpenViewStyleHelper.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B3AA8EC31EBB2B0400255737 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B3D794591B8EA14F00768134 /* build_TKOpenResource */;
			targetProxy = B3AA8EC21EBB2B0400255737 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		B3FACBBE1AFDDF0E0088CDF1 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				B3FACBBF1AFDDF0E0088CDF1 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		B3FACBC31AFDDF0E0088CDF1 /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				B3FACBC41AFDDF0E0088CDF1 /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		B3D7945B1B8EA14F00768134 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		B3D7945C1B8EA14F00768134 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		B3E1B3101B8DB80800CDD258 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = HCXVYWN652;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = TKOpenResource/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		B3E1B3111B8DB80800CDD258 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = HCXVYWN652;
				INFOPLIST_FILE = TKOpenResource/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		B3FACBD21AFDDF0E0088CDF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B3FACBD31AFDDF0E0088CDF1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B3FACBD51AFDDF0E0088CDF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer: Pengmin Liu (56K32XM5GT)";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = R4QUJ9NZUW;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Thinkive/TKFMWK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/IDCadrSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60016/BankCardSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60016/BankCardSDK/ISOpenSDKFoundation.embeddedframework",
					"$(PROJECT_DIR)/libs",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/TChat/SDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/Required",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/Optional",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformConnector",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformSDK/QQSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/Required",
					"$(PROJECT_DIR)/Thinkive/Modules/AliyunNlsSdk",
					"$(PROJECT_DIR)/Thinkive/Modules/TencentASR",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloudSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60007/SDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60039/ZXJTVideoSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/AliNUI",
					"$(PROJECT_DIR)/Thinkive/Modules/Ifly",
					"$(PROJECT_DIR)/Thinkive/Modules/TKAppletPlugin",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/HHKVOCR/ISOpenSDKFoundation.embeddedframework",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/HHKVOCR",
					"$(PROJECT_DIR)/Thinkive/Modules/THS/SDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS_TTS",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_PREFIX_HEADER = "TKOpenAccount-Standard/TKOpenAccount.pch";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/OpensslSDK/include",
					"\"$(SRCROOT)\"/Thinkive/3Lib/AnychatSDK/include",
				);
				INFOPLIST_FILE = "TKOpenAccount-Standard/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Thinkive/3Lib/AnychatSDK/libs/audioprocess",
					"$(PROJECT_DIR)/Thinkive/3Lib/AnychatSDK/libs",
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/appbase/ZBarSDK",
					"$(PROJECT_DIR)/Thinkive/3Lib/TChatSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/SDK/libs",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/AnychatSDK/libs/audioprocess",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/AnychatSDK/libs",
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/OpensslSDK/libs",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformSDK/SinaWeiboSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformSDK/WeChatSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/libSTSilentLivenessController",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloudSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloud_TTS",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/IDCadrSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS/SDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS_TTS",
				);
				MARKETING_VERSION = 5.5.2;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "";
				OTHER_CODE_SIGN_FLAGS = "";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.thinkive.openaccountAdhoc;
				PRODUCT_NAME = "思迪TChatRtc";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = iOSDev;
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_WORKSPACE = NO;
			};
			name = Debug;
		};
		B3FACBD61AFDDF0E0088CDF1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CODE_SIGN_ENTITLEMENTS = "";
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer: Pengmin Liu (56K32XM5GT)";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = R4QUJ9NZUW;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = R4QUJ9NZUW;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Thinkive/TKFMWK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/IDCadrSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60016/BankCardSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60016/BankCardSDK/ISOpenSDKFoundation.embeddedframework",
					"$(PROJECT_DIR)/libs",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/TChat/SDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/Required",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/Optional",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformConnector",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformSDK/QQSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/Required",
					"$(PROJECT_DIR)/Thinkive/Modules/AliyunNlsSdk",
					"$(PROJECT_DIR)/Thinkive/Modules/TencentASR",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloudSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60007/SDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60039/ZXJTVideoSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/AliNUI",
					"$(PROJECT_DIR)/Thinkive/Modules/Ifly",
					"$(PROJECT_DIR)/Thinkive/Modules/TKAppletPlugin",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/HHKVOCR/ISOpenSDKFoundation.embeddedframework",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/HHKVOCR",
					"$(PROJECT_DIR)/Thinkive/Modules/THS/SDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS_TTS",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_PREFIX_HEADER = "TKOpenAccount-Standard/TKOpenAccount.pch";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/OpensslSDK/include",
					"\"$(SRCROOT)\"/Thinkive/3Lib/AnychatSDK/include",
				);
				INFOPLIST_FILE = "TKOpenAccount-Standard/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Thinkive/3Lib/AnychatSDK/libs/audioprocess",
					"$(PROJECT_DIR)/Thinkive/3Lib/AnychatSDK/libs",
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/appbase/ZBarSDK",
					"$(PROJECT_DIR)/Thinkive/3Lib/TChatSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/SDK/libs",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/AnychatSDK/libs/audioprocess",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/AnychatSDK/libs",
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/OpensslSDK/libs",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformSDK/SinaWeiboSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformSDK/WeChatSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/libSTSilentLivenessController",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloudSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloud_TTS",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/IDCadrSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS/SDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS_TTS",
				);
				MARKETING_VERSION = 5.5.2;
				OTHER_CFLAGS = "";
				OTHER_CODE_SIGN_FLAGS = "";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.thinkive.kh.demo;
				PRODUCT_NAME = "思迪TChatRtc";
				PROVISIONING_PROFILE = "55d7de79-fb23-49f2-97df-d8ac728cfcdf";
				PROVISIONING_PROFILE_SPECIFIER = iOSDev;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = iOSDev;
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_WORKSPACE = NO;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B3D7945A1B8EA14F00768134 /* Build configuration list for PBXAggregateTarget "build_TKOpenResource" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B3D7945B1B8EA14F00768134 /* Debug */,
				B3D7945C1B8EA14F00768134 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B3E1B30F1B8DB80800CDD258 /* Build configuration list for PBXNativeTarget "TKOpenResource" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B3E1B3101B8DB80800CDD258 /* Debug */,
				B3E1B3111B8DB80800CDD258 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B3FACBAC1AFDDF0E0088CDF1 /* Build configuration list for PBXProject "TKOpenAccount-Standard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B3FACBD21AFDDF0E0088CDF1 /* Debug */,
				B3FACBD31AFDDF0E0088CDF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B3FACBD41AFDDF0E0088CDF1 /* Build configuration list for PBXNativeTarget "TKOpenAccount-Standard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B3FACBD51AFDDF0E0088CDF1 /* Debug */,
				B3FACBD61AFDDF0E0088CDF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B3FACBA91AFDDF0E0088CDF1 /* Project object */;
}
