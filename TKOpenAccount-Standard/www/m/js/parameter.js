var app = new Vue({
	el: '#app',
	data(){
		return {
			show_pop_select: false,
			currentInfo: JSON.parse(sessionStorage.getItem('current')),
			pageDate: null,
			pop_title: '',
			selectObj:{},
			select_current:{},
			intoParams:{},
			defaultValues:{}, // 参数是值 不是文本的
			switchParams: {},
      show_pop_json: false,
      json_title: '',
      current_edit_params: {},
      current_edit_json: '',
      current_json_key: '',
      authorization: sessionStorage.getItem('authorization'),
			callbackParams: {},
			showForm1: true,
			showForm2: true,
		}
	},
	computed:{
		title(){
			return this.currentInfo.funNo + this.currentInfo.funName
		}
	},
	methods: {
		getshowDate(){
			DATABASE.forEach((ele,i) => {
				if(ele.funNo == this.currentInfo.funNo){
					this.pageDate = DATABASE[i];
					console.log(this.pageDate)
				}
			});
			this.pageDate.invokeParams.forEach(item=>{
				if(item.inputType == 'switch'){
					this.switchParams[item.paramKey] = item.select
				}
        if(item.inputType == 'select'){
          let defv = [], def = []
          this.selectObj[item.paramKey] = {
            items: item.select,
            isMMulti: item.isMultiSel,
            paramKey: item.paramKey
          }
          if(item.select.length>0){
            item.select.forEach(li=>{
              if(li.selected){
                defv.push(li.name)
                def.push(li.value)
              }
            })
          }
          item.defValue = defv.join('、')
          item.defValue_nums = def.join(',')
          this.defaultValues[item.paramKey] = item.defValue_nums
        }
        if(item.defValue && (item.paramKey=='input' || item.paramKey=='json')) {
          if((item.defValue).indexOf('$(jwt-token)') != -1){
            item.defValue = item.defValue.replace("$(jwt-token)",this.authorization)
          }
        }
        this.intoParams[item.paramKey] = item.defValue;
				console.log(this.intoParams)
      })
			this.pageDate.callbackParams.forEach(item=>{
				this.callbackParams[item.paramKey] = ''
			})
		},
		showselect(key, name){
			this.select_current = this.selectObj[key];
			this.pop_title = name;
			this.show_pop_select = true
		},
		selectLi(i){
			if(!this.select_current.isMMulti){
				this.select_current.items.forEach(item=>{
					item.selected = false
				})
			}
			this.select_current.items[i].selected = !this.select_current.items[i].selected 
		},
		closeSelect(){
			let names = [], values= [];
			this.select_current.items.forEach(item=>{
				if(item.selected){
					names.push(item.name)
					values.push(item.value)
				} 
			})
			this.intoParams[this.select_current.paramKey] = names.join('、')
			this.defaultValues[this.select_current.paramKey] = values.join(',')
			this.show_pop_select = false
		},
    show_edit_json(params, key, name){
      this.current_edit_params = params;
      this.json_title = name;
      this.current_json_key = key
      this.current_edit_json = this.current_edit_params[key]
      this.show_pop_json = true;
    },
    closeJson(params){
      this.current_edit_params[this.current_json_key] = this.current_edit_json
      this.show_pop_json = false;
    },
		start(){
			let _this = this;
			let callbackFunNo = 'function'+this.pageDate.callbackFunNo;
			let iParams = JSON.parse(JSON.stringify(this.intoParams))
			let param = Object.assign(iParams, this.defaultValues);
      for(let k in param){
        if(this.switchParams[k]){
            param[k] = param[k] ? this.switchParams[k][1] : this.switchParams[k][0]
        }
      }
      param.funcNo = this.currentInfo.funNo;
			window[callbackFunNo]= function (param){
				for(let k in _this.callbackParams){
					_this.callbackParams[k] = param[k]
				}
				_this.callbackParams = {..._this.callbackParams}
			}
     	let result =  callMessageNative(param);
        if(result.error_no != 0){
            alert(JSON.stringify(result))
        }
		}
	},
	created() {
		this.getshowDate();
	},
})