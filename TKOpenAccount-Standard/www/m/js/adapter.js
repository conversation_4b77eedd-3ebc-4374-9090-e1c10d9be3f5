(function (doc, win) {
		  var docEl = doc.documentElement,
			resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
			recalc = function () {
			  var clientWidth = docEl.clientWidth;
			  if (!clientWidth) return;
			  if (clientWidth >= 375){
			  	clientWidth = 375;
			  }
			  docEl.style.fontSize = 100 * (clientWidth / 375) + 'px';
			};

		  // Abort if browser does not support addEventListener
		  if (!doc.addEventListener) return;
		  win.addEventListener(resizeEvt, recalc, false);
		  doc.addEventListener('DOMContentLoaded', recalc, false);
})(document, window);