$(function(){
  let init = {
    currentInfo: {},
    pageDate: [],
    selectObj: {},
    load(){
      this.currentInfo = JSON.parse(sessionStorage.getItem('current'));
      $('.title').text(`${this.currentInfo.funNo}${this.currentInfo.funName}`)
      DATABASE.forEach((ele,i) => {
        if(ele.funNo == this.currentInfo.funNo){
            this.pageDate = DATABASE[i];
          
          }
        });
        this.pageDate.invokeParams.forEach(item=>{
          if(item.inputType == 'select'){
            let defv = []
            this.selectObj[item.paramKey] = {
              items: item.select,
              isMMulti: item.Multisel
            }
            if(item.select.length>0){
              item.select.forEach(li=>{
                if(li.selected){
                  defv.push(li.name)
                }
              })
            }
            item.defValue = defv.join(',')
          }
        })
        console.log(this.selectObj)
        this.loadDom()
    },
    loadDom(){
      let _html = ''; // 显示插件调佣入参
      let _html_back = '' // 显示插件返回结果
      console.log(this.pageDate.invokeParams)
      this.pageDate.invokeParams.forEach(item=>{
        if(item.inputType == 'select'){
          _html += `<div class="input_text text">
            <span class="tit">${item.paramName}</span>
            <div class="dropdown canclick"  data-key=${item.paramKey} placeholder="${item.inputHit}">${item.defValue || ''}</div>
          </div>`
        } else if(item.inputType == 'switch'){
          _html += `<div class="input_text">
            <div class="switch_info">
              <p>${item.paramName}</p>
              <div class="switch" data-key=${item.paramKey}>
                <input type="checkbox" />
                <div class="switch-inner">
                  <div class="switch-arrow"></div>
                </div>
              </div>
            </div>
          </div>`
        } else if(item.inputType == 'input'){
          _html += `<div class="input_text text">
            <span class="tit">${item.paramName}</span>
            <input class="t1" type="text" data-key=${item.paramKey} placeholder="${item.inputHit}"  value="${item.defValue || ''}"/>
          </div>`
        } else if(item.inputType == 'json'){
          _html += `<div class="input_text text">
          <span class="tit">${item.paramName}</span>
          <div class="tarea1 canclick" data-key=${item.paramKey} data-value='${item.defValue}'>${item.defValue || ''}</div>
        </div>` 
        }
      })

      this.pageDate.callbackParams.forEach(item=>{
        console.log(item)
        if(item.paramType == 'string'){
          _html_back +=  `<div class="input_text text">
            <span class="tit">${item.paramName}</span>
            <input class="t1" type="text" data-key=${item.paramKey} value="${item.defValue || ''}" />
          </div>`
        } else if(item.paramType == 'image'){
          _html_back += `<div class="input_text text">
            <span class="tit">${item.paramName}</span>
            <img class="form_img" data-key=${item.paramKey} src="" alt="">
          </div>`
        } else if(item.paramType == 'json'){
          _html_back += `<div class="input_text text">
          <span class="tit">${item.paramName}</span>
          <div class="tarea1 needsclick" data-key=${item.paramKey} contenteditable="true" placeholder="${item.inputHit}</div>
        </div>` 
        }
      })
      $('.input_form1').html(_html)
      $('.input_form2').html(_html_back)
    },
    loadSelectPop(title, key){
     let obj = this.selectObj[key]
      $('.dialog_overlay, .popup_layer').show()
      $('.popup_lytit h3').text(title);
      $('.select_list').attr('data-ismulti',obj.isMMulti);
      let lists = '';
      obj.items.forEach(item=>{
        let a = item.selected? 'active' :'';
        lists+= `<li class='${a}' data-value='${item.value}'><span>${item.name}</span></li>`
      })
      $('.select_list').html(lists)
    },
  }
  init.load();
  $('.input_form1').on('click','.canclick',function(){
    let k = $(this).data('key');
    let t = $(this).prev().text();
    if($(this).hasClass('dropdown')){ // 表示下拉框
      init.loadSelectPop(t, k)
    } else { // 表示下拉输入框

    }
  })
  $('.select_list').on('click','li',function(){
    let ismulti = $(this).parent().data('ismulti')
    console.log(ismulti)
    if(ismulti){ // 多选
      if($(this).hasClass('active')){
        $(this).removeClass('active')
      } else {
        $(this).addClass('active')
      }
    } else {
      if($(this).hasClass('active')){
        $(this).removeClass('active')
      } else {
        $(this).parent().find('li').removeClass('active')
        $(this).addClass('active')

      }
    }
   
  })
  $('.cancel').click(function(){
    $('.dialog_overlay, .popup_layer').hide()
  })
  
})