DATABASE = [
               {
                   "funNo": "60014",
                   "funName": "身份证OCR",
                   "invokeParams": [
                       {
                           "paramName": "图片类型",
                           "inputHit": "请选择图片类型",
                           "paramKey": "imgType",
                           "paramType": "string",
                           "inputType": "select",
                           "isMultiSel": true,
                           "select": [
                               {
                                   "name": "身份证人像面",
                                   "value": "4",
                                   "selected": true
                               },
                               {
                                   "name": "身份证国徽面",
                                   "value": "5",
                                   "selected": false
                               }
                           ]
                       },
                       {
                           "paramName": "是否支持相册",
                           "paramKey": "isAlbum",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": true
                       },
                       {
                           "paramName": "是否支持拍照",
                           "paramKey": "isTake",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": true
                       },
                       {
                           "paramName": "是否上传图片",
                           "paramKey": "isUpload",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": false
                       },
                       {
                           "paramName": "请求参数",
                           "inputHit": "请输入请求参数",
                           "paramKey": "requestParam",
                           "paramType": "string",
                           "inputType": "input"
                       },
                       {
                           "paramName": "请求地址",
                           "inputHit": "请输入请求地址",
                           "paramKey": "url",
                           "paramType": "string",
                           "inputType": "input"
                       },
                       {
                           "paramName": "图片压缩大小",
                           "inputHit": "请输入图片压缩大小",
                           "paramKey": "compressSize",
                           "paramType": "string",
                           "inputType": "input",
                           "defValue": "200"
                       },
                       {
                           "paramName": "模块名",
                           "inputHit": "请输入模块名",
                           "paramKey": "moduleName",
                           "paramType": "string",
                           "inputType": "input",
                           "defValue": "open"
                       },
                       {
                           "paramName": "是否显示确认页面",
                           "paramKey": "isNeedOcrAffirmView",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": true
                       },
                       {
                           "paramName": "扫描超时时间",
                           "inputHit": "请输入超时时间",
                           "paramKey": "timeOut",
                           "paramType": "string",
                           "inputType": "input",
                           "defValue": "30"
                       },
                       {
                           "paramName": "主题颜色",
                           "inputHit": "请输入主题颜色",
                           "paramKey": "mainColor",
                           "paramType": "string",
                           "inputType": "input"
                       },
                       {
                           "paramName": "是否校验裁边",
                           "paramKey": "isCheckIncomplete",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": false
                       },
                       {
                           "paramName": "是否校验完整性",
                           "paramKey": "isCheckComplete",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": false
                       },
                       {
                           "paramName": "是否微服务请求",
                           "paramKey": "isRestFull",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": false
                       },
                       {
                           "paramName": "是否显示示例图",
                           "paramKey": "isNeedSample",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": false
                       },
                       {
                           "paramName": "是否正反面同时返回",
                           "paramKey": "isNeedFullResults",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": true
                       },
                       {
                           "paramName": "是否易道四边定位版",
                           "paramKey": "isExQuard",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": false
                       },
                       {
                           "paramName": "身份证正面预览图",
                           "paramKey": "previewSamplePicFront",
                           "paramType": "string",
                           "inputType": "json"
                       }
                   ],
                   "callbackFunNo": "60050",
                   "callbackParams": [
                       {
                           "paramName": "错误码",
                           "paramKey": "error_no",
                           "paramType": "string"
                       },
                       {
                           "paramName": "身份证号码",
                           "paramKey": "idNo",
                           "paramType": "string"
                       },
                       {
                           "paramName": "姓名",
                           "paramKey": "custName",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件地址",
                           "paramKey": "native",
                           "paramType": "string"
                       },
                       {
                           "paramName": "民族",
                           "paramKey": "ethnicName",
                           "paramType": "string"
                       },
                       {
                           "paramName": "出生日期",
                           "paramKey": "birthday",
                           "paramType": "string"
                       },
                       {
                           "paramName": "签发机构",
                           "paramKey": "policeOrg",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件开始日期",
                           "paramKey": "idbeginDate",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件结束日期",
                           "paramKey": "idendDate",
                           "paramType": "string"
                       },
                       {
                           "paramName": "用户性别",
                           "paramKey": "userSex",
                           "paramType": "string"
                       },
                       {
                           "paramName": "身份证正面",
                           "paramKey": "frontBase64",
                           "paramType": "image"
                       },
                       {
                           "paramName": "身份证反面",
                           "paramKey": "backBase64",
                           "paramType": "image"
                       }
                   ]
               }
               ,
               {
                   "funNo": "60074",
                   "funName": "港澳台证件OCR",
                   "invokeParams": [
                       {
                           "paramName": "图片类型",
                           "inputHit": "请选择图片类型",
                           "paramKey": "imgType",
                           "paramType": "string",
                           "inputType": "select",
                           "isMultiSel": true,
                           "select": [
                               {
                                   "name": "港澳台通行证正面",
                                   "value": "60",
                                   "selected": true
                               },
                               {
                                   "name": "港澳台通行证反面",
                                   "value": "61",
                                   "selected": false
                               },
                               {
                                   "name": "港澳台居住证正面",
                                   "value": "70",
                                   "selected": false
                               },
                               {
                                   "name": "港澳台居住证反面",
                                   "value": "71",
                                   "selected": false
                               },
                               {
                                   "name": "外国人居留证正面",
                                   "value": "80",
                                   "selected": false
                               }
                           ]
                       },
                       {
                           "paramName": "是否显示相册按钮",
                           "paramKey": "isAlbum",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": true
                       },
                       {
                           "paramName": "是否显示拍照按钮",
                           "paramKey": "isTake",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": true
                       },
                       {
                           "paramName": "图片压缩大小",
                           "inputHit": "请输入图片压缩大小",
                           "paramKey": "compressSize",
                           "paramType": "string",
                           "inputType": "input",
                           "defValue": "200"
                       },
                       {
                           "paramName": "模块名",
                           "inputHit": "请输入模块名",
                           "paramKey": "moduleName",
                           "paramType": "string",
                           "inputType": "input",
                           "defValue": "open"
                       },
                       {
                           "paramName": "是否显示确认页面",
                           "paramKey": "isNeedOcrAffirmView",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": true
                       },
                       {
                           "paramName": "扫描超时时间",
                           "inputHit": "请输入超时时间",
                           "paramKey": "timeOut",
                           "paramType": "string",
                           "inputType": "input",
                           "defValue": "30"
                       },
                       {
                           "paramName": "主题颜色",
                           "inputHit": "请输入主题颜色",
                           "paramKey": "mainColor",
                           "paramType": "string",
                           "inputType": "input"
                       },
                       {
                           "paramName": "是否校验裁边",
                           "paramKey": "isCheckIncomplete",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": false
                       },
                       {
                           "paramName": "是否校验完整性",
                           "paramKey": "isCheckComplete",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": false
                       }
                   ],
                   "callbackFunNo": "60075",
                   "callbackParams": [
                       {
                           "paramName": "错误码",
                           "paramKey": "error_no",
                           "paramType": "string"
                       },
                       {
                           "paramName": "身份证号码",
                           "paramKey": "idNo",
                           "paramType": "string"
                       },
                       {
                           "paramName": "姓名",
                           "paramKey": "custName",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件地址",
                           "paramKey": "native",
                           "paramType": "string"
                       },
                       {
                           "paramName": "民族",
                           "paramKey": "ethnicName",
                           "paramType": "string"
                       },
                       {
                           "paramName": "出生日期",
                           "paramKey": "birthday",
                           "paramType": "string"
                       },
                       {
                           "paramName": "签发机构",
                           "paramKey": "policeOrg",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件开始日期",
                           "paramKey": "idbeginDate",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件结束日期",
                           "paramKey": "idendDate",
                           "paramType": "string"
                       },
                       {
                           "paramName": "用户性别",
                           "paramKey": "userSex",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件号码",
                           "paramKey": "cardNo",
                           "paramType": "string"
                       },
                       {
                           "paramName": "英文名",
                           "paramKey": "englishName",
                           "paramType": "string"
                       },
                       {
                           "paramName": "国籍",
                           "paramKey": "nationality",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件人像面",
                           "paramKey": "frontBase64",
                           "paramType": "image"
                       },
                       {
                           "paramName": "证件国徽面",
                           "paramKey": "backBase64",
                           "paramType": "image"
                       }
                   ]
               }
               ,
               {
                   "funNo": "60032",
                   "funName": "港澳台证件拍照",
                   "invokeParams": [
                       {
                           "paramName": "图片类型",
                           "inputHit": "请选择图片类型",
                           "paramKey": "imgType",
                           "paramType": "string",
                           "inputType": "select",
                           "isMultiSel": true,
                           "select": [
                               {
                                   "name": "港澳台通行证正面",
                                   "value": "60",
                                   "selected": true
                               },
                               {
                                   "name": "港澳台通行证反面",
                                   "value": "61",
                                   "selected": false
                               },
                               {
                                   "name": "港澳台居住证正面",
                                   "value": "70",
                                   "selected": false
                               },
                               {
                                   "name": "港澳台居住证反面",
                                   "value": "71",
                                   "selected": false
                               },
                               {
                                   "name": "外国人居留证正面",
                                   "value": "80",
                                   "selected": false
                               }
                           ]
                       },
                       {
                           "paramName": "动作类型",
                           "inputHit": "请选择动作类型",
                           "paramKey": "action",
                           "paramType": "string",
                           "inputType": "select",
                           "isMultiSel": false,
                           "select": [
                               {
                                   "name": "拍照",
                                   "value": "pai",
                                   "selected": true
                               },
                               {
                                   "name": "相册",
                                   "value": "phone",
                                   "selected": false
                               }
                           ]
                       },
                       {
                           "paramName": "是否显示相册按钮",
                           "paramKey": "isAlbum",
                           "paramType": "string",
                           "inputType": "switch",
                           "select": [
                               "0",
                               "1"
                           ],
                           "defValue": true
                       },
                       {
                           "paramName": "图片压缩大小",
                           "inputHit": "请输入图片压缩大小",
                           "paramKey": "compressSize",
                           "paramType": "string",
                           "inputType": "input",
                           "defValue": "200"
                       },
                       {
                           "paramName": "模块名",
                           "inputHit": "请输入模块名",
                           "paramKey": "moduleName",
                           "paramType": "string",
                           "inputType": "input",
                           "defValue": "open"
                       },
                       {
                           "paramName": "顶部提示语",
                           "inputHit": "请输入顶部提示语",
                           "paramKey": "topTips",
                           "paramType": "string",
                           "inputType": "input"
                       },
                       {
                           "paramName": "主题颜色",
                           "inputHit": "请输入主题颜色",
                           "paramKey": "mainColor",
                           "paramType": "string",
                           "inputType": "input"
                       }
                   ],
                   "callbackFunNo": "60050",
                   "callbackParams": [
                       {
                           "paramName": "错误码",
                           "paramKey": "error_no",
                           "paramType": "string"
                       },
                       {
                           "paramName": "证件人像面",
                           "paramKey": "base64",
                           "paramType": "image"
                       },
                       {
                           "paramName": "多个证件照回调",
                           "paramKey": "base64Arr",
                           "paramType": "json"
                       }
                   ]
               }
           ]