<!DOCTYPE html>
<html>

<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
<meta name="format-detection" content="telephone=no" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="cache-control" content="no-cache">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="msapplication-tap-highlight" content="no">
<title></title>
<link href="css/style.css" rel="stylesheet" />
<script type="text/javascript" src="js/adapter.js"></script>
<script type="text/javascript" src="js/index.js"></script>
</head>

<body>
<section class="main fixed" data-page="home">
	<header class="header">
		<div class="header_inner">
	    	<a class="icon_back" href="./login.html"></a>
			<h1 class="title">上传身份证</h1>
		</div>
	</header>
	<article class="content">
		<h5 class="com_title">拍摄并上传本人二代身份证</h5>
		<div class="upload_cm_wrap">
			<div class="upload_cont">
				<div class="upload_pic">
					<div class="pic"><img class="showImgs" src="images/idcard_ic01.png"></div>
					<input  class="upload" style="display: none;" type="file" accept="image/*" />
					<a class="btn uploadImg"  href="javascript:void(0);">上传/拍摄人像面</a>
				</div>
			</div>
		</div>
		<div class="photo_tips">
			<h5 class="title"><span>请注意照片拍摄规范</span></h5>
			<ul class="list">
				<li>
					<div class="pic"><img src="images/ptoto_tip01_1.png" /></div>
					<span class="ok">合规照片</span>
				</li>
				<li>
					<div class="pic"><img src="images/ptoto_tip01_2.png" /></div>
					<span class="error">不可缺角</span>
				</li>
				<li>
					<div class="pic"><img src="images/ptoto_tip01_3.png" /></div>
					<span class="error">不能模糊</span>
				</li>
				<li>
					<div class="pic"><img src="images/ptoto_tip01_4.png" /></div>
					<span class="error">不要反光</span>
				</li>
			</ul>
		</div>
		<div class="ce_btn mt30">
			<a class="p_button disabled" href="javascript:void(0);">下一步</a>
		</div>
	</article>
</section>

</body>
<script src="./js/jquery-2.1.4.min.js"></script>
<script>
$(function(){
	$('.uploadImg').on('click',function(){
		$('.upload').click()
	})
	$(".upload").change(function(){
		let myfrom = new FormData();
		let file = $('.upload')[0].files[0];
		let reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = function(e){
			let base64 = e.target.result;
			let idCardFrontData = (base64.split(','))[1]
			$('.showImgs').attr('src', base64)
			let data = {
				gaCheck: '0',
				idCardFrontData
			}
			$.ajax({
				url: "https://opt-dev.thinkive.com:15149/auth-common-server/user/addAuthFace",
				type: "post",
				data: JSON.stringify(data),
				contentType: "application/json; charset=utf-8",
				headers:{
					'tk-jwt-authorization':`Bearer ${sessionStorage.getItem('authorization')}`
				},
				success: function (res) {
					if(res.code == 0) {
						$('.p_button').removeClass('disabled')
					}
				}
			});
		}
	});
	$('.p_button').click(function(){
		if($(this).hasClass('disabled')){
			return false
		}
		window.location.href='./setting.html'
	})
})
</script>
</html>