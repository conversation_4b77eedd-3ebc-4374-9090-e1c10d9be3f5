@charset "utf-8";

/* reset.css */

body {
	position: relative;
	overflow-y: visible !important;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: moz-none;
	user-select: none;
	-webkit-overflow-scrolling: auto;
}

body * {
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	/* Firefox */
	-webkit-box-sizing: border-box;
	/* Safari */
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/** html4 reset **/

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td {
	margin: 0;
	padding: 0;
}

fieldset,
img {
	border: 0 none;
}

address,
caption,
cite,
code,
dfn,
em,
th,
var,
b,
h1,
h2,
h3 {
	font-style: normal;
	font-weight: normal;
}

ol,
ul,
li {
	list-style-type: none;
}

q:before,
q:after {
	content: '';
}

abbr,
acronym {
	border: 0;
	font-variant: normal;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

th,
td,
caption {
	vertical-align: top;
	text-align: left;
}

input[type='text'],
input[type='email'],
input[type='search'],
input[type='password'],
input[type='date'],
input[type='month'],
input[type='tel'],
input[type='radio'],
input[type='checkbox'],
button,
textarea {
	-webkit-appearance: none;
	-moz-appearance: none;
	-moz-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	-moz-tap-highlight-color: rgba(0, 0, 0, 0);
}

input[type='search'] {
	-webkit-appearance: textfield;
}

input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-decoration {
	-webkit-appearance: none;
}

img {
	vertical-align: middle;
	font-size: 0;
}

h1 {
	font-size: 0.24rem;
}

h2 {
	font-size: 0.2rem;
}

h3 {
	font-size: 0.18rem;
}

h4 {
	font-size: 0.16rem;
}

h5 {
	font-size: 0.14rem;
}

/** html5 reset **/

header,
footer,
section,
nav,
menu,
details,
hgroup,
figure,
figcaption,
article,
aside {
	margin: 0;
	padding: 0;
	display: block;
}

input::-moz-placeholder {
	color: #BBBBBB;
}

input::-webkit-input-placeholder {
	color: #BBBBBB;
}

textarea::-moz-placeholder {
	color: #BBBBBB;
}

textarea::-webkit-input-placeholder {
	color: #BBBBBB;
}

input,
textarea {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

a {
	text-decoration: none;
	outline: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

a:hover {
	opacity: 1;
}

.boost {
	-webkit-overflow-scrolling: auto;
	-webkit-transform: translate3d(0, 0, 0);
	-moz-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	-webkit-backface-visibility: hidden;
	-webkit-perspective: 1000;
	-moz-backface-visibility: hidden;
	-moz-perspective: 1000;
	backface-visibility: hidden;
	perspective: 1000;
	zoom: 1;
}

/** Body, links, basics **/

/** Body, links, basics **/

body,
html {
	font-size: 100px;
	width: 100%;
	height: 100%;
	overflow: hidden;
	box-sizing: border-box;
}

body {
	font-size: 0.14rem;
	line-height: 0.2rem;
	font-family: -apple-system-font, "Helvetica Neue", sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-rendering: optimizeLegibility;
	color: #333333;
	background: #F8F8F8;
}

/*-- 布局grid --*/

.main.fixed {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-orient: vertical;
	box-orient: vertical;
	-webkit-flex-direction: column;
	flex-direction: column;
	height: 100%;
	width: 100%;
	position: absolute;
	left: 0;
	top: 0;
}

.main.fixed article.content {
	-moz-box-flex: 1;
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
	overflow-x: hidden;
	overflow-y: auto;
	height: 100%;
	-webkit-overflow-scrolling: auto;
	position: relative;
}

section.main.fixed article.content::-webkit-scrollbar {
	width: 0;
	display: none;
}

/*-- 表单 form --*/

.input_form {
	background: #ffffff;
	padding: 0 0.16rem;
}


.input_text {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	position: relative;
	-moz-box-sizing: content-box;
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	min-height: 0.56rem;
}

.input_text:last-child {
	border-bottom: 0 none;
}

.input_text .tit {
	font-size: 0.16rem;
	line-height: 0.24rem;
	padding: 0.16rem 0;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 10;
}

.input_text .t1 {
	display: block;
	width: 100%;
	height: 0.56rem;
	padding: 0.16rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	border: 0 none;
	outline: none;
	background: none;
	font-family: -apple-system-font, "Helvetica Neue", sans-serif;
	color: #333333;
}

.input_text .t1.disabled {
	color: #999999;
}

.input_text .dropdown {
	min-height: 0.56rem;
	line-height: 0.24rem;
	padding: 0.16rem 0.24rem 0.16rem 0;
	font-size: 0.16rem;
	color: #333333;
	position: relative;
}

.input_text .dropdown:after {
	content: '';
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	right: 0;
}

.input_text .dropdown:empty:before {
	content: attr(placeholder);
	color: #BBBBBB;
}

.input_text .dropdown img {
	width: 0.24rem;
	height: 0.24rem;
	float: left;
	margin-right: 0.1rem;
}
.input_text  .form_img {
    width: 100px;
		margin: 10px 10px 10px 70%;
}

.input_text .tarea1 {
	padding: 0.16rem 0;
	line-height: 0.24rem;
	min-height: 0.56rem;
	width: 100%;
	font-family: -apple-system-font, "Helvetica Neue", sans-serif;
	color: #333333;
	font-size: 0.16rem;
	outline: none;
	border: 0 none;
	background: none;
	-webkit-user-select: auto;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	display: block;
}

.input_text .tarea1:empty:before {
	content: attr(placeholder);
	color: #BBBBBB;
}

.input_text.text .t1,
.input_text.text .dropdown,
.input_text.text .tarea1 {
	padding-left: 0.88rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 0.56rem;
}

.input_text.error .t1,
.input_text.error .dropdown,
.input_text.error .tarea1 {
	color: #ff4951;
}

.error_tips {
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/reject_icon.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
	z-index: 50;
}

.code_img {
	width: 0.8rem;
	height: 0.28rem;
	position: absolute;
	top: 50%;
	margin-top: -0.14rem;
	right: 0;
	z-index: 50;
}

.code_img img {
	display: block;
	width: 100%;
	height: 100%;
}

.code_btn {
	line-height: 0.24rem;
	background: #ffffff;
	text-align: center;
	font-size: 0.16rem;
	color: #F88939;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
	z-index: 50;
}

.code_btn.time {
	color: #BBBBBB;
}

.txt_close {
	width: 0.24rem;
	height: 0.24rem;
	background: #ffffff url(../images/txt_close.png) no-repeat center;
	background-size: 0.16rem;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
	z-index: 50;
}

.input_text.code .txt_close {
	right: 1rem;
}

.input_text.pword .txt_close {
	right: 0.3rem;
}

/*-- 单选、复选框 radio checkbox --*/
.icon_check {
	display: inline-block;
	width: 0.24rem;
	height: 0.24rem;
	position: relative;
}

.icon_check:before {
	content: '';
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 0.34rem;
	height: 0.34rem;
	border: 0.03rem solid #BBBBBB;
	border-radius: 0.04rem;
	-webkit-transform: scale(0.5);
	transform: scale(0.5);
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -0.17rem 0 0 -0.17rem;
}

.icon_check.checked:before {
	border-color: #BD321D;
	background: #BD321D url(../images/icon_check01.png) no-repeat center;
	background-size: 0.37rem;
}

.icon_check.disabled:before {
	border-color: #DDDDDD;
	background-color: rgba(221, 221, 221, 0.5);
}

.icon_radio {
	display: inline-block;
	padding-left: 0.24rem;
	line-height: 0.24rem;
	position: relative;
}

.icon_radio:before {
	content: '';
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 0.2rem;
	height: 0.2rem;
	border: 1px solid #BBBBBB;
	border-radius: 50%;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -0.1rem 0 0 -0.1rem;
}

.icon_radio.checked:before {
	border-color: #BD321D;
	background: #BD321D url(../images/icon_check02.png) no-repeat center;
	background-size: 0.2rem;
}




/*-- 开关 switch --*/

.switch {
	height: 0.24rem;
	width: 0.45rem;
	position: relative;
}

.switch>input[type='checkbox'] {
	width: 0.45rem;
	height: 0.24rem;
	position: absolute;
	left: 0;
	top: 0;
	-moz-opacity: 0;
	-webkit-opacity: 0;
	opacity: 0;
	cursor: pointer;
	z-index: 100;
	outline: none;
}

.switch>.switch-inner {
	height: 0.24rem;
	position: relative;
	background: #E9E9EB;
	-moz-border-radius: 1rem;
	-webkit-border-radius: 1rem;
	border-radius: 1rem;
	transition: all 0.1s ease-in;
	-moz-transition: all 0.1s ease-in;
	-webkit-transition: all 0.1s ease-in;
}

.switch>.switch-inner>.switch-arrow {
	height: 0.16rem;
	width: 0.16rem;
	background: #ffffff;
	position: absolute;
	top: 0.04rem;
	right: 0.25rem;
	-moz-border-radius: 1rem;
	-webkit-border-radius: 1rem;
	border-radius: 1rem;
	box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
	transition: all 0.1s ease-in;
	-moz-transition: all 0.1s ease-in;
	-webkit-transition: all 0.1s ease-in;
	z-index: 10;
}

.switch>input[type='checkbox']:checked+.switch-inner {
	background: #1061FF;
}

.switch>input[type='checkbox']:checked+.switch-inner .switch-arrow {
	right: 0.04rem;
}

/*-- 头部header --*/
.header {
	background: #ffffff;
}

.header_inner {
	position: relative;
	height: 0.44rem;
	line-height: 0.44rem;
}

.header_inner>h1.title {
	font-size: 0.16rem;
	font-weight: 500;
	color: #000000;
	position: relative;
	z-index: 0;
	text-align: center;
}

.icon_text {
	position: absolute;
	top: 0;
	right: 0;
	font-size: 0.14rem;
	padding: 0 0.16rem;
	color: #000000;
	z-index: 50;
}

.icon_back {
	width: 0.48rem;
	height: 0.44rem;
	background: url(../images/icon_back.png) no-repeat center;
	background-size: 0.24rem;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
}

/*-- 进度条step  --*/

.step_box {
	padding: 0.07rem 0.2rem 0.25rem;
	overflow: hidden;
	background: #ffffff;
}

.step_box .item {
	height: 4px;
	background: rgba(189, 50, 29, 0.3);
	border-radius: 4px;
}

.step_box .item b {
	display: block;
	height: 4px;
	background: #BD321D;
	border-radius: 4px;
	position: relative;
}

.step_box .item b:before {
	content: '';
	box-sizing: border-box;
	width: 8px;
	height: 8px;
	background: #BD321D;
	border: 1px solid #ffffff;
	border-radius: 8px;
	position: absolute;
	top: -2px;
	right: -4px;
}

.step_box .item b span {
	width: 0.4rem;
	text-align: center;
	line-height: 1;
	font-size: 0.12rem;
	color: #BD321D;
	position: absolute;
	bottom: -0.16rem;
	right: -0.2rem;
}

/*-- 其他公用 public  --*/

.mt10 {
	margin-top: 0.1rem !important;
}

.mt15 {
	margin-top: 0.15rem !important;
}

.mt20 {
	margin-top: 0.2rem !important;
}

.mt30 {
	margin-top: 0.3rem !important;
}

.com_title {
	padding: 0.15rem 0.16rem;
	background: #ededed;
	line-height: 0.18rem;
	font-size: 0.14rem;
	font-weight: 500;
	position: relative;
}
.topderict:after {
	content: '';
	width: 0.35rem;
	height: 0.35rem;
	background: url(../images/a_top.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.18rem;
	right: .2rem;
}
.botderict:after {
	content: '';
	width: 0.35rem;
	height: 0.35rem;
	background: url(../images/a_bot.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.18rem;
	right: .2rem;
}

.dialog_overlay {
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
	position: fixed;
	top: 0;
	left: 0;
	z-index: 5000;
}

.dialog_box {
	width: 3rem;
	background: #fff;
	border-radius: 0.08rem;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -1.5rem;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 6000;
}

.dialog_cont{
	padding: 0.24rem;
}

.dialog_cont h3 {
	font-size: 0.18rem;
	line-height: 0.26rem;
	text-align: center;
	font-weight: 500;
	margin-bottom: 0.08rem;
}

.dialog_cont>div {
	min-height: 0.44rem;
	overflow: auto;
	max-height: 3.6rem;
	text-align: center;
	font-size: 0.14rem;
	line-height: 0.22rem;
	color: #666666;
}

.dialog_btn {
	border-top: 1px solid rgba(229, 229, 229, 0.5);
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
}

.dialog_btn a {
	flex: 1;
	height: 0.56rem;
	line-height: 0.56rem;
	font-size: 0.16rem;
	font-weight: 500;
	text-align: center;
	color: #BD321D;
	border-left: 1px solid rgba(229, 229, 229, 0.5);
}

.dialog_btn a:first-child {
	border-left: 0 none;
}

.dialog_btn a.cancel {
	color: #666666;
	font-weight: normal;
}

.dialog_btn a.disabled {
	opacity: 0.3;
}

.com_link {
	color: #F88939;
}

.txt_center {
	text-align: center !important;
}

.txt_left {
	text-align: left !important;
}

.txt_right {
	text-align: right !important;
}

.available_bklist {
	overflow: hidden;
}

.available_bklist li {
	width: 33.33%;
	float: left;
	line-height: 0.2rem;
	padding: 0.08rem 0;
	color: #999999;
	font-size: 0.14rem;
}

.available_bklist li img {
	width: 0.18rem;
	height: 0.18rem;
	margin-right: 0.08rem;
	vertical-align: middle;
}

.available_bklist li:nth-child(3n) {
	text-align: right;
}

.available_bklist li:nth-child(3n + 2) {
	text-align: center;
}

.p_button {
	display: block;
	height: 0.44rem;
	line-height: 0.44rem;
	text-align: center;
	font-size: 0.16rem;
	background: linear-gradient(90deg, #1061FF 0%, #5A92FF 100%);
	color: #ffffff;
	border-radius: 1.5rem;
	font-weight: 500;
}

.p_button.border {
	background: #FFEAE7;
	color: #BD321D;
}

.p_button.disabled {
	opacity: 0.3;
}

.ce_btn {
	display: flex;
	padding: 0.15rem 0.16rem;
}

.ce_btn a {
	flex: 1;
	margin-left: 0.12rem;
}

.ce_btn a:first-child {
	margin-left: 0;
}

.footer .ce_btn {
	padding: 0.06rem 0.16rem;
}

.popup_layer {
	width: 100%;
	background: #fff;
	border-radius: 0.12rem 0.12rem 0 0;
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 9999;
	-webkit-transform: translateY(110%);
	transform: translateY(110%);
	-webkit-transition: transform 0.2s linear;
	transition: transform 0.2s linear;
}

.popup_layer.show {
	-webkit-transform: translateY(0);
	transform: translateY(0);
}

.popup_lytit {
	height: 0.48rem;
	line-height: 0.48rem;
	text-align: center;
}

.popup_lytit h3 {
	height: 0.48rem;
	font-size: 0.16rem;
	font-weight: 500;
	margin: 0 0.5rem;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.popup_lytit a.cancel {
	width: 0.5rem;
	height: 0.48rem;
	background: url(../images/icon_close.png) no-repeat center;
	background-size: 0.2rem;
	position: absolute;
	top: 0;
	right: 0;
	z-index: 50;
}

.toast_popup {
	width: 2.2rem;
	text-align: center;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -1.1rem;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 1000;
}

.toast_popup p {
	display: inline-block;
	padding: 0.14rem 0.22rem;
	background: rgba(0, 0, 0, 0.7);
	border-radius: 0.08rem;
	color: #ffffff;
	font-size: 0.14rem;
	line-height: 0.2rem;
	min-width: 1.4rem;
}


.sele_lyinfo {
	height: 0.56rem;
	padding: 0.11rem 0 0.11rem 0.16rem;
	font-size: 0.15rem;
	color: #BD321D;
	display: flex;
}

.sele_lyinfo+.popup_lycont,
.sele_lyinfo+.popup_lycont.fixed {
	height: 4.3rem;
}

.sele_lyinfo span {
	display: block;
	margin-right: 0.1rem;
	height: 0.34rem;
	border: 1px solid #E5E5E5;
	padding: 0.06rem 0.07rem;
	font-size: 0.15rem;
	border-radius: 0.02rem;
	line-height: 0.2rem;
	color: #000000;
	min-width: 0.7rem;
	position: relative;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.sele_lyinfo span.active {
	color: #BD321D;
	border-color: #BD321D;
}

.sele_lyinfo span.off {
	padding-right: 0.16rem;
}

.sele_lyinfo span.off:after {
	content: "";
	width: 0.07rem;
	height: 0.07rem;
	border-top: 1px solid #000000;
	border-right: 1px solid #000000;
	position: absolute;
	top: 50%;
	margin-top: -0.04rem;
	right: 0.07rem;
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
}

.popup_lycont {
	min-height: 1.82rem;
	max-height: 4.86rem;
	overflow-x: hidden;
	overflow-y: auto;
}

.popup_lycont.fixed {
	height: 4.86rem;
}

.search_box+.popup_lycont,
.search_box+.popup_lycont.fixed {
	height: 4.42rem;
}

.search_box {
	padding: 0.06rem 0.2rem;
}

.search_input {
	height: 0.32rem;
	position: relative;
}

.search_input .icon {
	display: block;
	width: 0.2rem;
	height: 0.2rem;
	background: url(../images/icon_search2.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.1rem;
	left: 0.1rem;
}

.search_input .t1 {
	display: block;
	width: 100%;
	padding: 0.06rem 0.1rem 0.06rem 0.36rem;
	height: 0.32rem;
	border: 0 none;
	background: #F8F8F8;
	border-radius: 0.5rem;
	outline: none;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #333333;
}

.search_input .t1::-moz-placeholder {
	color: #999999;
}

.search_input .t1::-webkit-input-placeholder {
	color: #999999;
}

.search_input .txt_close {
	background-color: transparent;
	right: 0.1rem;
}

.select_list {
	padding: 0 0.16rem;
}

.select_list li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	position: relative;
}

.select_list li span {
	display: block;
	padding: 0.16rem 0.3rem 0.16rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #333333;
}

.select_list li span em {
	display: block;
	font-size: 0.12rem;
	line-height: 0.16rem;
	margin-top: 0.04rem;
	color: #999;
}

.select_list li.active span,
.select_list li.active span em {
	color: #1061FF;
}

.select_list li.active:after {
	content: '';
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
}



.addr_list {
	height: 4.86rem;
	overflow-x: hidden;
	overflow-y: auto;
	-webkit-overflow-scrolling: auto;
}

.addr_list::-webkit-scrollbar {
	width: 0;
	display: none;
}

.addr_list li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	position: relative;
}

.addr_list li span {
	display: block;
	padding: 0.16rem;
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #333333;
}

.addr_list li.active span {
	background: #F8F8F8;
	color: #BD321D;
}

.addr_list.checked {
	width: 35%;
	float: left;
}

.sub_addr_list {
	float: left;
	width: 65%;
	padding-left: 0.16rem;
	background: #f8f8f8;
	height: 4.86rem;
	overflow-x: hidden;
	overflow-y: auto;
	-webkit-overflow-scrolling: auto;
}

.sub_addr_list::-webkit-scrollbar {
	width: 0;
	display: none;
}

.sub_addr_list li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	position: relative;
}

.sub_addr_list li span {
	display: block;
	padding: 0.16rem 0.3rem 0.16rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #333333;
}

.sub_addr_list li.active:after {
	content: '';
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
}

.sub_addr_list li.active span {
	color: #BD321D;
}

.network_list {
	padding: 0 0.16rem;
}

.network_list li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	position: relative;
	padding: 0.15rem 0.56rem 0.15rem 0;
}

.network_list li h5 {
	padding-left: 0.22rem;
	line-height: 0.24rem;
	font-size: 0.16rem;
	font-weight: normal;
	position: relative;
}

.network_list li h5:before {
	content: '';
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/icon_location01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	left: 0;
}

.network_list li p {
	padding-left: 0.22rem;
	font-size: 0.12rem;
	color: #999999;
	line-height: 0.18rem;
	margin-top: 0.04rem;
}

.network_list li.active:after {
	content: '';
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
}

.network_list li.active h5,
.network_list li.active p {
	color: #BD321D;
}

.network_list li.active h5:before {
	background-image: url(../images/icon_location02.png);
}

.reject_box {
	margin-bottom: 0.1rem;
	background: #FFDEDE;
	position: relative;
	color: #FF4848;
	z-index: 100;
}

.reject_box h5 {
	padding: 0.14rem 0.56rem 0.14rem 0.16rem;
	line-height: 0.2rem;
	font-size: 0.14rem;
	font-weight: 500;
	position: relative;
}

.reject_box h5:after {
	content: '';
	width: 0.14rem;
	height: 0.14rem;
	background: url(../images/arrow04.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.07rem;
	right: 0.2rem;
}

.reject_box h5 span {
	padding-left: 0.28rem;
	position: relative;
}

.reject_box h5 span:before {
	content: '';
	width: 0.2rem;
	height: 0.2rem;
	background: url(../images/reject_icon.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.1rem;
	left: 0;
}

.reject_box h5.on:after {
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
}

.reject_box .cont {
	width: 100%;
	background: #FFDEDE;
	font-weight: 500;
	position: absolute;
	top: 0.48rem;
	left: 0;
	padding: 0 0.2rem 0.14rem 0.44rem;
	line-height: 0.2rem;
	font-size: 0.14rem;
}

.reject_box .cont p {
	padding: 0.02rem 0;
}

.t_border {
	border-top: 1px solid rgba(229, 229, 229, 0.5);
}

.tax_selelist{
	padding: 0 0.16rem;
}

.tax_selelist li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	position: relative;
	padding: 0.12rem 0.36rem 0.12rem 0;
}

.tax_selelist li:last-child {
	border-bottom: 0 none;
}

.tax_selelist li h5 {
	font-size: 0.16rem;
	font-weight: normal;
	color: #333333;
	line-height: 0.24rem;
}

.tax_selelist li p {
	font-size: 0.12rem;
	color: #999999;
	line-height: 0.16rem;
	margin-top: 0.04rem;
}

.tax_selelist li.active:after {
	content: '';
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
}

.tax_selelist li.active h5,
.tax_selelist li.active p {
	color: #BD321D;
}

.rule_check {
	position: relative;
	line-height: 0.2rem;
	font-size: 0.14rem;
	color: #666666;
	background: #ffffff;
	padding: 0.15rem 0.16rem;
	display: flex;
	margin-bottom: 0.08rem;
}

.rule_check label {
	display: block;
	flex: 1;
}

.rule_check a {
	color: #F88939;
	display: block;
	margin-top: 0.15rem;
}

.rule_check .icon_check {
	display: block;
	width: 0.2rem;
	height: 0.2rem;
	margin-right: 0.08rem;
}

.rule_check.spel {
	padding-top: 0.05rem;
}

.rule_check.spel a {
	display: inline;
	margin-top: 0;
}

.white_bg {
	background: #ffffff !important;
}

.protocol_content {
	padding: 0.18rem 0.16rem;
	font-size: 0.16rem;
	line-height: 0.26rem;
}

.protocol_content p {
	margin: 0.06rem 0;
}

.icon_eye {
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_eye01.png) no-repeat center;
	background-size: 0.24rem;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
	z-index: 50;
}

.icon_eye.show {
	background-image: url(../images/icon_eye02.png);
}

.notice_box {
	background: #ffffff;
	padding: 0.34rem 0.16rem 0.2rem;
	min-height: 2.8rem;
	text-align: center;
	color: #999999;
	line-height: 0.2rem;
}

.notice_box .pic {
	width: 1.2rem;
	height: 1.2rem;
	margin: 0 auto 0.04rem;
}

.notice_box .pic img {
	display: block;
	width: 100%;
	height: 100%;
}

.notice_box h5 {
	font-size: 0.24rem;
	font-weight: normal;
	line-height: 0.32rem;
	margin-bottom: 0.12rem;
	color: #333333;
}

.notice_box.spel {
	padding-top: 0.5rem;
}

.notice_box h5.error {
	padding-top: 0.3rem;
	color: #FF4848;
}

.notice_box p a {
	color: #F88939;
}

.fail_reason {
	background: #ffffff;
	border-top: 1px solid rgba(229, 229, 229, 0.5);
	padding: 0.2rem 0;
	margin: 0 0.16rem;
	line-height: 0.22rem;
	font-size: 0.15rem;
	color: #000000;
}

.fail_reason h5 {
	font-weight: normal;
	color: #999999;
	font-size: 0.16rem;
	line-height: 0.24rem;
	margin-bottom: 0.08rem;
}

.fail_reason .cont p {
	padding: 0.03rem 0;
}

/*-- 首页 home  --*/

.fixed_header {
	width: 100%;
	background: none;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
	-webkit-ransition: all 0.3s;
	transition: all 0.3s;
}

.fixed_header .header_inner {
	background: none;
	-webkit-ransition: all 0.3s;
	transition: all 0.3s;
}

.fixed_header .header_inner>h1.title {
	color: #ffffff;
	-webkit-ransition: all 0.3s;
	transition: all 0.3s;
}

.fixed_header .icon_text {
	color: #ffffff;
	-webkit-ransition: all 0.3s;
	transition: all 0.3s;
}

.fixed_header .icon_back {
	background-image: url(../images/icon_back2.png);
	-webkit-ransition: all 0.3s;
	transition: all 0.3s;
}

.fixed_header.active {
	background: #ffffff;
}

.fixed_header.active .header_inner {
	background: #ffffff;
}

.fixed_header.active .header_inner>h1.title {
	color: #000000;
}

.fixed_header.active .icon_text {
	color: #000000;
}

.fixed_header.active .icon_back {
	background-image: url(../images/icon_back.png);
}


.home_page {
	min-height: 100%;
	position: relative;
	padding-bottom: 0.74rem;
	background: #ffffff;
}

.home_banner img {
	display: block;
	width: 100%;
}

.home_btn {
	padding: 0 0.16rem 0.04rem;
}

.home_link {
	text-align: center;
	padding: 0.1rem 0.16rem;
}

.home_link.fixed {
	width: 100%;
	position: absolute;
	bottom: 0.24rem;
	left: 0;
}

.link_right_arrow {
	display: inline-block;
	color: #F88939;
	position: relative;
	padding-right: 0.16rem;
}

.link_right_arrow:after {
	content: '';
	width: 0.12rem;
	height: 0.12rem;
	background: url(../images/arrow02.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	right: 0;
}

.ready_box {
	padding: 0.28rem 0.2rem;
	width: 100%;
}

.ready_box .title {
	line-height: 0.24rem;
	font-size: 0.16rem;
	font-weight: normal;
	margin-bottom: 0.14rem;
}

.ready_box .list li {
	padding: 0.14rem 0 0.14rem 0.56rem;
	position: relative;
}

.ready_box .list li i {
	display: block;
	width: 0.4rem;
	height: 0.4rem;
	position: absolute;
	top: 50%;
	margin-top: -0.2rem;
	left: 0;
}

.ready_box .list li i img {
	display: block;
	width: 100%;
	height: 100%;
}

.ready_box .list li h5 {
	font-size: 0.16rem;
	font-weight: 500;
	line-height: 0.24rem;
}

.ready_box .list li p {
	font-size: 0.14rem;
	line-height: 0.2rem;
	margin-top: 0.04rem;
	color: #999999;
}

.popup_layer .ce_btn {
	padding: 0.06rem 0.16rem;
}

.popup_lycont .available_bklist {
	padding: 0.12rem 0.25rem 0.2rem;
}

.popup_lycont .available_bklist li {
	color: #333333;
}

.recom_list {
	padding: 0 0.2rem;
}

.recom_list li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	position: relative;
	padding: 0.12rem 0.36rem 0.12rem 0;
}

.recom_list li h5 {
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: normal;
}

.recom_list li p {
	font-size: 0.12rem;
	line-height: 0.16rem;
	color: #666666;
	margin-top: 0.04rem;
}

.recom_list li .tel {
	display: inline-block;
	vertical-align: top;
	position: relative;
	top: 0.02rem;
	margin-right: 0.03rem;
	width: 0.12rem;
	height: 0.12rem;
	background: url(../images/icon_tel01.png) no-repeat center;
	background-size: 100%;
}

.recom_list li.active h5,
.recom_list li.active p {
	color: #BD321D;
}

.recom_list li.active .tel {
	background-image: url(../images/icon_tel02.png);
}

.recom_list li.active:after {
	content: "";
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
}

.icon_edit {
	display: inline-block;
	width: 0.2rem;
	height: 0.2rem;
	vertical-align: top;
	margin-left: 0.08rem;
	background: url(../images/icon_edit.png) no-repeat center;
	background-size: 0.16rem;
}

.rule_check label b {
	font-weight: normal;
	color: #333333;
}

.top_border {
	border-top: 1px solid rgba(229, 229, 229, 0.5);
}

/*-- 验证手机 login  --*/
.v_logo {
	width: 0.82rem;
	margin: 0 auto 0.25rem;
}

.v_logo img {
	display: block;
	width: 100%;
}

.login_page {
	padding: 0.48rem 0 0.74rem;
}

.phone_loginbox {
	padding: 0.24rem 0.16rem 0.1rem;
	text-align: center;
}

.phone_loginbox .num {
	font-size: 0.32rem;
	line-height: 0.44rem;
	color: #000000;
	margin-bottom: 0.08rem;
}

.phone_loginbox p {
	font-size: 0.12rem;
	line-height: 0.16rem;
	color: #999999;
}

.input_form.spel .input_text:last-child {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.txt_tips {
	margin: 0.15rem 0.16rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #666666;
}

.txt_tips a {
	margin: 0 0.05rem;
}

.call_numspan {
	display: block;
	color: #F88939;
}


/*-- 选择营业部 branch  --*/
.icon_location {
	line-height: 0.56rem;
	font-size: 0.14rem;
	color: #F88939;
	padding-left: 0.26rem;
	position: absolute;
	top: 0;
	right: 0;
	z-index: 50;
}

.icon_location:before {
	content: '';
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_location03.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	left: 0;
}

.p_r70 {
	padding-right: 0.7rem !important;
}

.imp_c_tips {
	padding: 0.15rem 0;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #FFA900;
}
.imp_c_tips.top_bg{
	background: #FFF4D2;
	padding-left: 0.16rem;
	padding-right: 0.16rem;
}

.imp_c_tips p {
	padding-left: 0.24rem;
	position: relative;
}

.imp_c_tips p:before {
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/icon_imp.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.02rem;
	left: 0;
}

.imp_c_tips .imp {
	color: #FFA900;
}

.qx_tipbox {
	text-align: left;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #666666;
	padding: 0.14rem 0 0.1rem 0.48rem;
	position: relative;
}

.qx_tipbox .icon {
	width: 0.4rem;
	height: 0.4rem;
	position: absolute;
	top: 0.14rem;
	left: 0;
}

.qx_tipbox .icon img {
	display: block;
	width: 100%;
}

.qx_tipbox h5 {
	font-size: 0.16rem;
	line-height: 0.24rem;
	margin-bottom: 0.04rem;
	font-weight: 500;
	color: #000000;
}

.yyb_info_text {
	padding: 0 0.3rem 0.12rem 0.88rem;
	font-size: 0.13rem;
	line-height: 0.18rem;
	color: #666666;
	position: relative;
	top: -0.08rem;
}

.yyb_info_text p {
	margin-top: 0.04rem;
	padding-left: 0.16rem;
	position: relative;
}

.yyb_info_text p:first-child {
	margin-top: 0;
}

.yyb_info_text p i {
	display: block;
	width: 0.11rem;
	height: 0.12rem;
	position: absolute;
	top: 0.03rem;
	left: 0;
}

.yyb_info_text p i.addr {
	background: url(../images/yyb_ic_addr.png) no-repeat center;
	background-size: 100%;
}

.yyb_info_text p i.tel {
	background: url(../images/yyb_ic_tel.png) no-repeat center;
	background-size: 100%;
}

/*-- 上传身份证 idconfirm  --*/

.upload_cm_wrap{
	background: #ffffff;
	padding: 0.2rem 0.16rem 0.16rem;
	display: flex;
}

.upload_cont {
	flex: 1;
	margin-left: 0.17rem;
	background: #fff;
}

.upload_cont:first-child{
	margin-left: 0;
}

.upload_pic {
	position: relative;
}

.upload_pic .pic {
	position: relative;
	background: #F1F6FD;
	border-radius: 0.02rem;
	overflow: hidden;
	padding-top: 67.5%;
}

.upload_pic .pic img {
	display: block;
	width: calc(100% - 0.24rem);
	height: calc(100% - 0.24rem);
	position: absolute;
	top: 0.12rem;
	left: 0.12rem;
}

.upload_pic .pic .watermark {
	width: 0.5rem;
	height: 0.5rem;
	background: url(../images/ic_watermark.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.12rem;
	right: 0.12rem;
	z-index: 50;
}

.upload_pic .btn {
	margin-top: 0.08rem;
	display: block;
	text-align: center;
	font-size: 0.14rem;
	border-radius: 0.02rem;
	line-height: 0.32rem;
	border: 1px solid #1061FF;
	color: #1061FF;
}

.upload_cm_wrap + .photo_tips{
	margin-top: 0;
	padding-top: 0;
}



.reset_btn {
	margin-top: 0.08rem;
	display: block;
	text-align: center;
	font-size: 0.14rem;
	border-radius: 0.02rem;
	line-height: 0.32rem;
	border: 1px solid #BD321D;
	color: #BD321D;
}

.photo_tips {
	background: #ffffff;
	margin: 0.08rem 0;
	padding: 0.16rem;
}

.photo_tips .title {
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #000000;
	font-weight: 500;
	margin-bottom: 0.12rem;
}

.photo_tips p {
	font-size: 0.14rem;
	line-height: 0.24rem;
	color: #666666;
}

.photo_tips .imp {
	color: #F3992D;
}

.photo_tips .list+p {
	margin-top: 0.2rem;
}

.photo_tips .list {
	display: flex;
}

.photo_tips .list li {
	flex: 1;
	margin-left: 0.1rem;
}

.photo_tips .list li:first-child {
	margin-left: 0;
}

.photo_tips .list li .pic img {
	display: block;
	width: 100%;
}

.photo_tips .list li span {
	display: block;
	margin-top: 0.08rem;
	text-align: center;
	color: #999999;
	font-size: 0.14rem;
	line-height: 0.2rem;
}

.photo_tips .list li span.ok:before,
.photo_tips .list li span.error:before {
	content: "";
	display: inline-block;
	vertical-align: top;
	width: 0.12rem;
	height: 0.12rem;
	margin-right: 0.04rem;
	position: relative;
	top: 0.04rem;
}

.photo_tips .list li span.ok:before {
	background: url(../images/ptoto_tip_ok.png) no-repeat center;
	background-size: 100%;
}

.photo_tips .list li span.error:before {
	background: url(../images/ptoto_tip_error.png) no-repeat center;
	background-size: 100%;
}

.com_tips {
	background: #ffffff;
	margin: 0.08rem 0;
	padding: 0.14rem 0.16rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #666666;
}

.upload_select {
	width: 100%;
	padding: 0.16rem;
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 9999;
	-webkit-transform: translateY(110%);
	transform: translateY(110%);
	-webkit-transition: transform 0.2s linear;
	transition: transform 0.2s linear;
}

.upload_select.show {
	-webkit-transform: translateY(0);
	transform: translateY(0);
}

.upload_select h5 {
	background: #fff;
	-moz-border-radius: 0.14rem 0.14rem 0 0;
	-webkit-border-radius: 0.14rem 0.14rem 0 0;
	border-radius: 0.14rem 0.14rem 0 0;
	font-size: 0.14rem;
	font-weight: normal;
	color: #999999;
	text-align: center;
	line-height: 0.44rem;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.upload_select ul {
	background: #fff;
	-moz-border-radius: 0 0 0.14rem 0.14rem;
	-webkit-border-radius: 0 0 0.14rem 0.14rem;
	border-radius: 0 0 0.14rem 0.14rem;
}

.upload_select ul li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.upload_select ul li:last-child {
	border-bottom: 0 none;
}

.upload_select ul li a {
	display: block;
	text-align: center;
	height: 0.56rem;
	line-height: 0.56rem;
	font-size: 0.18rem;
	color: #BD321D;
}

.upload_select .cancel {
	margin-top: 0.06rem;
	display: block;
	height: 0.56rem;
	line-height: 0.56rem;
	text-align: center;
	font-size: 0.18rem;
	color: #BD321D;
	font-weight: 500;
	background: rgba(255, 255, 255, 0.97);
	-moz-border-radius: 0.14rem;
	-webkit-border-radius: 0.14rem;
	border-radius: 0.14rem;
}

.help_txtbox {
	padding: 0.18rem 0.3rem 0.3rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #000000;
}

.help_txtbox:first-child {
	padding-top: 0;
}

.help_txtbox .title {
	position: relative;
	text-align: center;
	font-size: 0.22rem;
	line-height: 0.32rem;
	font-weight: normal;
	margin-bottom: 0.08rem;
	color: #BD321D;
}

.help_txtbox .title:before {
	content: "";
	width: 100%;
	height: 0.12rem;
	border-radius: 0.04rem;
	background: linear-gradient(0deg, rgba(189, 50, 29, 0) 0%, rgba(189, 50, 29, 0.2) 100%);
	position: absolute;
	top: 0.13rem;
	left: 0;
}

.help_txtbox .title span {
	position: relative;
	z-index: 10;
}

.help_txtbox p {
	margin: 0.08rem 0;
}

.help_txtbox .img_item {
	margin-top: 0.16rem;
}

.help_txtbox .img_item img {
	display: block;
	width: 100%;
}

.long_span {
	line-height: 0.24rem;
	font-size: 0.14rem;
	color: #999999;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
	z-index: 50;
}

.long_span .icon_check {
	vertical-align: top;
	margin-right: 0.06rem;
}



/*-- 完善信息 idsupply  --*/
.idsupply_box .input_form {
	margin: 0 0;
}

.idsupply_box .rule_check {
	margin: 0.08rem 0;
}

.idsupply_box .input_text.text .t1,
.idsupply_box .input_text.text .dropdown,
.idsupply_box .input_text.text .tarea1 {
	padding-left: 1.3rem;
	text-align: right;
}

.switch_info {
	padding: 0.16rem 0.16rem 0.16rem 0;
	position: relative;
	font-size: 0.16rem;
	line-height: 0.24rem;
	min-height: 0.56rem;
	color: #333333;
	font-weight: 500;
}

.switch_info .switch {
	position: absolute;
	top: 0.16rem;
	right: 0;
}

.imp_c_tips+.input_text {
	border-top: 1px solid rgba(229, 229, 229, 0.5);
}
.popup_lycont_textarea {
	height: 100%;
    width: 100%;
    padding: 10px;
    border: none;
    outline: none;
}
/* .idsupply_box .input_text.text .t1:focus,
.idsupply_box .input_text.text .tarea1:focus,
.idsupply_box .input_text.text .tarea1.focus {
	text-align: left;
	padding-right: 0.5rem;
	padding-left: 0.88rem;
} */

.imp_c_tips+.query_ctbox {
	border-top: 1px solid rgba(229, 229, 229, 0.5);
}

.query_ctbox{
	padding-bottom: 0.16rem;
}
.query_ctbox .com_title {
	background: none;
	color: #666666;
	line-height: 0.2rem;
	padding: 0.1rem 0;
}

.query_list li {
	padding: 0.02rem 0 0.02rem 0.24rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	position: relative;
	margin-top: 0.1rem;
}

.query_list li:first-child {
	margin-top: 0;
}

.query_list li .ic_dw,
.query_list li .ic_dz {
	display: inline-block;
	width: 0.16rem;
	height: 0.16rem;
	position: absolute;
	top: 0.06rem;
	left: 0;
}

.query_list li .ic_dw {
	background: url(../images/ic_dw.png) no-repeat center;
	background-size: 100%;
}

.query_list li .ic_dz {
	background: url(../images/ic_adr.png) no-repeat center;
	background-size: 100%;
}

.key_word {
	color: #BD321D !important;
}


/*-- 选择账户 stkacct  --*/
.account_box {
	background: #ffffff;
	margin-bottom: 0.08rem;
	padding: 0 0.16rem;
	padding-bottom: 0.16rem;
}

.account_box h3 {
	padding: 0.16rem 0;
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: 500;
}

.account_list li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	position: relative;
	padding: 0.16rem 0 0.16rem 0.32rem;
}

.account_list li:last-child {
	border-bottom: 0 none;
}

.account_list li .icon_check {
	position: absolute;
	top: 0.16rem;
	left: 0;
	z-index: 50;
}

.account_list li h5 {
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: normal;
}

.account_list li p {
	font-size: 0.12rem;
	line-height: 0.16rem;
	margin-top: 0.04rem;
	color: #666666;
}

.full_state{
	height: 0.32rem;
	line-height: 0.32rem;
	font-size: 0.14rem;
	color: #ffffff;
	text-align: center;
	background: #FF8775;
	border-radius: 0 0 0.04rem 0.04rem;
	position: absolute;
	bottom: -1px;
	left: -1px;
	right: -1px;
	z-index: 100;
}

.zh_numspan {
	display: inline-block;
	vertical-align: top;
	margin-left: 0.06rem;
}

.zh_numspan.default {
	color: #BBBBBB;
}

.open_newlist {
	display: flex;
}

.open_newlist .item {
	flex: 1;
	margin-left: 0.16rem;
	border: 1px solid rgba(229, 229, 229, 0.5);
	border-radius: 0.04rem;
	position: relative;
	padding: 0.11rem;
}

.open_newlist .item:first-child {
	margin-left: 0;
}

.open_newlist .item h5 {
	font-size: 0.16rem;
	line-height: 0.24rem;
	padding-left: 0.28rem;
	font-weight: normal;
	position: relative;
}

.open_newlist .item h5 .icon_check {
	position: absolute;
	top: 0;
	left: -0.03rem;
}

.open_newlist .item p {
	margin-top: 0.08rem;
	font-size: 0.13rem;
	line-height: 0.18rem;
	color: #999999;
}

.open_newlist .item .tag {
	display: inline-block;
	vertical-align: top;
	padding: 0 0.02rem;
	font-size: 0.1rem;
	height: 0.14rem;
	line-height: 0.14rem;
	color: #BD321D;
	border-radius: 0.02rem;
	background: #E8F0FF;
	margin-right: 0.06rem;
	position: relative;
	top: 0.02rem;
}

.open_newlist .item.checked {
	border: 0 none;
	padding: 0.12rem;
	background: linear-gradient(180deg, #FFDFDB 0%, #FFF7F6 100%);
}

.account_box .imp_c_tips {
	padding: 0.12rem 0;
	margin-top: 0.06rem;
}

.zh_opea_box {
	font-size: 0.14rem;
	line-height: 0.2rem;
}
.zh_opea_box + .account_list{
	position: relative;
	top: 0.08rem;
}
.zh_link {
	display: inline-block;
	color: #F88939;
	padding-right: 0.16rem;
	position: relative;
}

.zh_link:after {
	content: "";
	width: 0.12rem;
	height: 0.12rem;
	background: url(../images/arrow02.png) no-repeat center;
	background-size: 100%;
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
	position: absolute;
	top: 50%;
	margin-top: -0.06rem;
	right: 0;
}

.zh_link.on:after {
	-webkit-transform: rotate(-90deg);
	transform: rotate(-90deg);
}

.popup_layer.spel {
	position: absolute;
}

.popup_lytit .cancel_lk,
.popup_lytit .sure_lk {
	font-size: 0.16rem;
	line-height: 0.48rem;
	padding: 0 0.16rem;
	color: #BD321D;
	position: absolute;
	top: 0;
	z-index: 50;
}

.popup_lytit .cancel_lk {
	left: 0;
	color: #999999;
}

.popup_lytit .sure_lk {
	right: 0;
}

.zh_formbox {
	padding: 0.2rem 0.16rem;
	border-top: 1px solid rgba(229, 229, 229, 0.5);
}

.zh_input_text {
	background: #F8F8F8;
	border-radius: 0.04rem;
	margin-top: 0.1rem;
	display: flex;
	padding: 0 0.16rem;
}

.zh_input_text:first-child {
	margin-top: 0;
}

.zh_input_text .tit {
	display: block;
	font-size: 0.16rem;
	line-height: 0.56rem;
	padding-right: 0.12rem;
	position: relative;
	margin-right: 0.12rem;
}

.zh_input_text .tit:before {
	content: "";
	width: 1px;
	height: 0.16rem;
	background: #E5E5E5;
	position: absolute;
	right: 0;
	top: 50%;
	margin-top: -0.08rem;
}

.zh_input_text .num_A{
	display: block;
	line-height: 0.56rem;
	margin-right: 0.12rem;
	font-size: 0.16rem;
}

.zh_input_text .ct {
	flex: 1;
}

.zh_input_text .t1 {
	display: block;
	width: 100%;
	height: 0.56rem;
	padding: 0.16rem 0;
	font-size: 0.16rem;
	line-height: 0.24rem;
	outline: none;
	background: none;
	border: 0 none;
	color: #333333;
}

.imp_c_tips p.no_icon {
	padding-left: 0;
}

.imp_c_tips p.no_icon:before {
	display: none;
}

.select_list.sub li span {
	padding-top: 0.12rem;
	padding-bottom: 0.12rem;
}

.popup_layer .protocol_content {
	padding-top: 0.08rem;
	padding-bottom: 0.08rem;
}

.num_ds_span {
	margin-left: 0.06rem;
	color: #999999;
}

.num_ds_span b {
	font-weight: normal;
	color: #BD321D;
}


/*-- 设置密码 setpwd  --*/

.pwors_select {
	background: #ffffff;
	margin: 0.08rem 0;
	padding: 0.16rem 0.7rem 0.16rem 0.16rem;
	line-height: 0.24rem;
	font-size: 0.16rem;
	position: relative;
}

.pwors_select .switch {
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0.16rem;
	z-index: 50;
}

.pword_tips {
	padding: 0.16rem;
	background: #ffffff;
	margin: 0.08rem 0;
	color: #999999;
	line-height: 0.24rem;
}

.pword_tips ul li {
	position: relative;
	padding-left: 0.1rem;
}

.pword_tips ul li:before {
	content: '';
	width: 0.03rem;
	height: 0.03rem;
	background: #999999;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	position: absolute;
	top: 0.1rem;
	left: 0;
}



/*-- 三放存管 tpbank  --*/
.photo_btn {
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_photo02.png) no-repeat center;
	background-size: 0.2rem;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
	z-index: 50;
}

.tpbank_zcbox {
	padding: 0.16rem;
}

.tpbank_zcbox .title {
	line-height: 0.2rem;
	font-size: 0.14rem;
	font-weight: normal;
	color: #999999;
	margin-bottom: 0.08rem;
}

.tpbank_img {
	padding: 0.1rem 0;
}

.tpbank_img img {
	display: block;
	width: 100%;
}

.num_layer {
	position: absolute;
	bottom: 0.58rem;
	left: 0;
	right: 0;
	z-index: 200;
	border: 1px solid #BD321D;
	background: #FFEAE7;
	border-radius: 0.04rem;
	text-align: center;
	padding: 0.11rem;
	line-height: 0.28rem;
	font-size: 0.25rem;
	color: #000000;
	font-weight: 700;
	box-shadow: 0px 0.02rem 0.04rem 0px rgba(0, 0, 0, 0.13);
}

.num_layer:before {
	content: "";
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 6px 6px 0;
	border-color: #BD321D transparent transparent;
	position: absolute;
	bottom: -6px;
	left: 0.85rem;
}

.bank_list {
	padding: 0 0.16rem;
}

.bank_list li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	padding: 0.16rem 0.36rem 0.16rem 0;
	position: relative;
	line-height: 0.24rem;
	font-size: 0.16rem;
	overflow: hidden;
}

.bank_list li img {
	float: left;
	width: 0.24rem;
	height: 0.24rem;
	margin-right: 0.1rem;
}

.bank_list li.active {
	color: #BD321D;
}

.bank_list li.active:after {
	content: '';
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: 0;
}



/*-- 风险测评 test --*/

.test_numbox {
	padding: 0.01rem 0.05rem 0.05rem 0.15rem;
	overflow: hidden;
	font-size: 0;
	position: fixed;
	height: 1rem;
	/* line-height: 0; */
	background: #F8F8F8;
	margin-bottom: 0.1rem;
}

.test_numbox span {
	display: inline-block;
	vertical-align: top;
	width: 0.3rem;
	height: 0.3rem;
	position: relative;
	line-height: 0.3rem;
	text-align: center;
	font-size: 0.14rem;
	color: #666666;
	border-radius: 0.04rem;
	background: #ffffff;
	margin: 0.14rem 0.05rem 0 0;
	overflow: hidden;
}

.test_numbox span.off {
	color: #BD321D;
	background: rgba(189, 50, 29, 0.1);
}

.test_numbox span.off:after {
	content: "";
	width: 0.13rem;
	height: 0.13rem;
	background: url(../images/ion_active02.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	bottom: 0;
	right: 0;
}

.test_list {
	margin: 1rem 0 0 0.15rem;
}

.test_item {
	background: #ffffff;
	margin-top: 0.08rem;
	padding: 0.08rem 0.16rem;
}

.test_item:first-child {
	margin-top: 0;
}

.test_item h5 {
	font-size: 0.16rem;
	line-height: 0.26rem;
	font-weight: 500;
	position: relative;
	padding: 0.04rem 0 0.12rem;
	margin-bottom: 0.08rem;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.test_item ul li {
	padding: 0.1rem 0 0.1rem 0.36rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #666666;
	position: relative;
}

.test_item ul li .icon_radio,
.test_item ul li .icon_check {
	padding: 0;
	width: 0.24rem;
	height: 0.24rem;
	position: absolute;
	top: 0.1rem;
	left: 0;
	z-index: 50;
}

.test_item ul li.checked {
	color: #BD321D;
}

.test_item ul li.disabled {
	color: #999999;
}

.test_item ul li.disabled .icon_radio,
.test_item ul li.disabled .icon_check {
	display: none;
}

.test_item .tips {
	padding: 0.14rem 0.16rem 0.11rem;
	font-size: 0.12rem;
	line-height: 0.16rem;
	color: #FF4848;
}

.test_rsult {
	background: #ffffff;
	padding: 0 0.16rem;
}

.test_infobox {
	padding: 0.15rem 0 0.24rem;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.test_inner {
	position: relative;
	padding-right: 1.65rem;
	min-height: 1rem;
}

.test_inner .info {
	font-size: 0.16rem;
	line-height: 0.22rem;
}

.test_inner .info h5 {
	font-size: 0.3rem;
	font-weight: 500;
	line-height: 0.36rem;
	color: #BD321D;
	margin-bottom: 0.14rem;
}

.test_level {
	width: 1.3rem;
	height: 1rem;
	text-align: center;
	padding-top: 0.3rem;
	overflow: hidden;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 100%;
	position: absolute;
	bottom: 0;
	right: 0;
}

.class01 {
	background-image: url(../images/test01.png);
}

.class02 {
	background-image: url(../images/test02.png);
}

.class03 {
	background-image: url(../images/test03.png);
}

.class04 {
	background-image: url(../images/test04.png);
}

.class05 {
	background-image: url(../images/test05.png);
}

.test_level h5 {
	height: 0.44rem;
	line-height: 0.44rem;
	font-size: 0.14rem;
	font-weight: normal;
	color: #BD321D;
}

.test_level h5 strong {
	font-size: 0.32rem;
	margin-right: 0.02rem;
}

.test_level p {
	font-size: 0.14rem;
	color: #999999;
	line-height: 0.2rem;
}

.appro_info {
	padding: 0.2rem 0;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.appro_info ul li {
	margin-top: 0.2rem;
}

.appro_info ul li:first-child {
	margin-top: 0;
}

.appro_info ul li .tit {
	display: block;
	color: #999999;
	font-size: 0.14rem;
	line-height: 0.2rem;
}

.appro_info ul li p {
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #000000;
}

.appro_tips {
	padding: 0.2rem 0;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	font-size: 0.14rem;
	line-height: 0.22rem;
	color: #999999;
}

.com_span {
	color: #F88939 !important;
}

.appro_item {
	background: #ffffff;
	padding: 0.2rem 0.16rem 0.14rem;
	margin-bottom: 0.08rem;
}

.appro_item .title {
	text-align: center;
	margin-bottom: 0.16rem;
}

.appro_item .title h3 {
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: 500;
	color: #000000;
}

.appro_item .title p {
	color: #666666;
	margin-top: 0.08rem;
	font-size: 0.14rem;
	line-height: 0.22rem;
}

.appro_item .cont {
	font-size: 0.14rem;
	line-height: 0.22rem;
	color: #999999;
}

.appro_item .cont p {
	margin: 0.1rem 0;
}

.appro_detail {
	padding: 0.16rem 0;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.appro_detail p {
	margin: 0 !important;
}

.appro_detail ul {
	margin-top: 0.06rem;
}

.appro_detail ul li {
	display: flex;
}

.appro_detail ul li .tit {
	display: block;
	width: 1.45rem;
}

.appro_detail ul li p {
	flex: 1;
	color: #333333;
	font-weight: 500;
}

.ablack {
	color: #333333 !important;
	font-weight: 500;
}


/*-- 视频见证 witness  --*/

.video_select {
	background: #ffffff;
}

.video_select .com_title {
	background: none;
}

.video_sele_list {
	padding: 0.12rem 0.16rem;
}

.video_sele_list li {
	margin-top: 0.16rem;
	border: 1px solid rgba(229, 229, 229, 0.5);
	border-radius: 0.04rem;
	background: #ffffff;
	padding: 0.26rem 0.16rem 0.22rem 1rem;
	position: relative;
}

.video_sele_list li:first-child {
	margin-top: 0;
}

.video_sele_list li .em_01,
.video_sele_list li .em_02,
.video_sele_list li .em_03 {
	width: 0.5rem;
	height: 0.5rem;
	position: absolute;
	top: 50%;
	margin-top: -0.25rem;
	left: 0.25rem;
}

.video_sele_list li .em_01 {
	background: url(../images/video_icon_01.png) no-repeat center;
	background-size: 100%;
}

.video_sele_list li .em_02 {
	background: url(../images/video_icon_02.png) no-repeat center;
	background-size: 100%;
}

.video_sele_list li .em_03 {
	background: url(../images/video_icon_03.png) no-repeat center;
	background-size: 100%;
}

.video_sele_list li h3 {
	font-size: 0.22rem;
	line-height: 0.3rem;
	font-weight: normal;
}

.video_sele_list li h3 span {
	font-size: 0.14rem;
	margin-left: ;
}

.video_sele_list li h3 span i {
	font-style: normal;
	color: #FF4848;
}

.video_sele_list li p {
	font-size: 0.13rem;
	line-height: 0.18rem;
	margin-top: 0.06rem;
	color: #999999;
}

.video_sele_list li .imp {
	color: #FFA900;
}

.video_sele_list li.active {
	border-color: #BD321D;
}

.witness_box {
	padding: 0.4rem 0.3rem 0.25rem;
	background: #fff;
	margin-bottom: 0.1rem;
	text-align: center;
	line-height: 0.2rem;
	color: #999999;
}

.witness_box h5 {
	font-size: 0.22rem;
	line-height: 0.28rem;
	margin-bottom: 0.12rem;
	font-weight: normal;
	color: #333333;
}

.witness_list {
	margin-top: 0.15rem;
}

.witness_list li {
	width: 100%;
	display: table;
	text-align: left;
	vertical-align: middle;
}

.witness_list li .icon {
	display: table-cell;
	width: 0.44rem;
	vertical-align: middle;
}

.witness_list li .icon img {
	display: block;
	width: 0.44rem;
	height: 0.44rem;
}

.witness_list li p {
	display: table-cell;
	height: 0.84rem;
	padding: 0.1rem 0 0.1rem 0.2rem;
	line-height: 0.2rem;
	font-size: 0.16rem;
	color: #000000;
	vertical-align: middle;
}

.witness_list li p em {
	font-size: 0.14rem;
	display: block;
	font-style: normal;
	color: #FFA900;
	margin-top: 0.02rem;
}

.queue_box {
	background: #ffffff;
	padding: 0.5rem 0.2rem 0.2rem;
	min-height: 3.1rem;
	margin-bottom: 0.08rem;
	text-align: center;
	line-height: 0.2rem;
	color: #999999;
}

.queue_box h5 {
	font-size: 0.24rem;
	line-height: 0.32rem;
	font-weight: normal;
	color: #333333;
	margin-bottom: 0.12rem;
}

.queue_level {
	width: 1.2rem;
	height: 1.2rem;
	margin: 0 auto 0.3rem;
	position: relative;
}

.queue_level .bg {
	display: block;
	width: 100%;
	height: 100%;
	background: url(../images/queue_bg.png) no-repeat center;
	background-size: 100%;
	-webkit-animation: allrotate 1.6s infinite linear;
	animation: allrotate 1.6s infinite linear;
}

.queue_level .pic {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
}

.queue_level .pic img {
	display: block;
	width: 100%;
	height: 100%;
}

@-webkit-keyframes allrotate {
	from {
		-webkit-transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(360deg);
	}
}

@keyframes allrotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

.queue_level .num {
	width: 100%;
	text-align: center;
	height: 1.2rem;
	line-height: 1.2rem;
	font-size: 0.62rem;
	color: #BD321D;
	font-weight: 500;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
}

.video_main {
	width: 100%;
	background: #000000;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	z-index: 100;
}

.common_video {
	width: 100%;
	height: 100%;
	position: relative;
}

.common_video video {
	width: 100%;
	height: 100%;
}

.portrait_icon {
	width: 100%;
	padding-top: 100%;
	background: url(../images/portrait_icon.png) no-repeat center top;
	background-size: 100% auto;
	position: absolute;
	top: 50%;
	left: 0;
	-webkit-transform: translateY(-40%);
	transform: translateY(-40%);
	z-index: 200;
}

.small_video {
	width: 1.2rem;
	height: 0.9rem;
	background: #333333;
	position: absolute;
	top: 0.54rem;
	right: 0.1rem;
	z-index: 300;
}

.small_video video {
	width: 100%;
	height: 100%;
	object-fit: fill;
}

.video_title {
	width: 100%;
	height: 0.44rem;
	padding-top: 0.04rem;
	font-size: 0.14rem;
	color: #fff;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 100;
}

.video_title h3 {
	font-weight: normal;
	text-align: center;
	font-size: 0.14rem;
	line-height: 0.2rem;
}

.video_title .data {
	line-height: 0.16rem;
	font-size: 0.1rem;
	text-align: center;
}

.video_title .data span {
	margin: 0 0.04rem;
}

.video_title .data span em {
	font-style: normal;
}

.hangup_btn {
	height: 0.24rem;
	line-height: 0.24rem;
	padding: 0 0.11rem;
	border-radius: 0.5rem;
	font-size: 0.14rem;
	color: #fff;
	background: #FC5C49;
	position: absolute;
	top: 0.1rem;
	right: 0.15rem;
	z-index: 300;
}

.video_infobox {
	color: #fff;
	font-size: 0.13rem;
	line-height: 0.18rem;
	height: 2.2rem;
	position: absolute;
	top: 0.54rem;
	left: 0.1rem;
	right: 1.46rem;
	overflow: hidden;
	z-index: 200;
}

.video_infobox .wrap {
	width: 100%;
	min-height: 2.2rem;
	position: absolute;
	bottom: 0;
	left: 0;
}
.cs_info{
	background: #51B4FE;
	color: #ffffff;
	width: 1.2rem;
	padding: 0.06rem 0.08rem;
	border-radius: 0 0 0.03rem 0.03rem;
	font-size: 0.12rem;
	line-height: 0.16rem;
	position: absolute;
	top: 1.44rem;
	right: 0.1rem;
	z-index: 300;
}

.cs_msg p {
	margin-top: 0.04rem;
}
.cs_msg p:first-child{
	margin-top: 0;
}
.cs_msg p span {
	display: inline-block;
	padding: 0.06rem;
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #fff;
	background: rgba(0,0,0,0.35);
	border: 1px solid rgba(153,153,153,0.1);
	border-radius: 0.08rem;
}

.cs_msg p span em{
	font-style: normal;
	color: #51B4FE;
}

.video_jztime{
	line-height: 0.36rem;
	text-align: center;
	font-size: 0.23rem;
	color: #ffffff;
}

.video_photobtn{
	display: block;
	width: 0.32rem;
	height: 0.32rem;
	background: rgba(187,187,187,0.35) url(../images/icon_photo05.png) no-repeat center;
	background-size: 0.18rem;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border-radius: 50%;
	position: absolute;
	top: 0.06rem;
	right: 0.1rem;
	z-index: 300;
}

.fc_basebox {
	background: #fff;
	padding: 0.34rem 0.25rem 0.1rem;
	text-align: center;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #999999;
}

.fc_basebox h5 {
	font-size: 0.24rem;
	line-height: 0.28rem;
	font-weight: normal;
	margin-bottom: 0.12rem;
	color: #333333;
}

.fc_basebox .pic {
	margin-top: 0.3rem;
}

.fc_basebox .pic img {
	display: block;
	width: 100%;
}

.lz_tipbox {
	background: #fff;
	padding: 0.1rem 0.25rem;
	margin-bottom: 0.08rem;
}

.lz_tipbox .title {
	margin-bottom: 0.14rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #999999;
	font-weight: normal;
}

.lz_tipbox ul {
	width: 100%;
	display: flex;
}

.lz_tipbox ul li {
	flex: 1;
	text-align: center;
}

.lz_tipbox ul li i {
	display: block;
	width: 0.36rem;
	height: 0.36rem;
	margin: 0 auto 0.05rem;
}

.lz_tipbox ul li i img {
	display: block;
	width: 100%;
	height: 100%;
}

.lz_tipbox ul li span {
	display: block;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #999;
}

.video_flex_wrap {
	width: 100%;
	height: 100%;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-orient: vertical;
	box-orient: vertical;
	-webkit-flex-direction: column;
	flex-direction: column;
	position: fixed;
	top: 0;
	left: 0;
	overflow: hidden;
	z-index: 200;
}

.video_flex_head,
.video_flex_top,
.video_flex_bottom,
.video_flex_middle:before,
.video_flex_middle:after {
	background: rgba(0, 0, 0, 0.7);
}

.video_flex_head {
	display: block;
	height: 0.44rem;
	width: 100%;
	position: relative;
}

.video_flex_top {
	height: 1.5rem;
}

.video_flex_top .table_wrap {
	width: 100%;
	display: table;
	height: 100%;
}

.video_flex_top .table_td {
	height: 100%;
	display: table-cell;
	vertical-align: middle;
	text-align: center;
	padding: 0 0.2rem;
}

.video_flex_middle {
	width: 100%;
	position: relative;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	position: relative;
}

.video_flex_middle:before,
.video_flex_middle:after {
	content: "";
	display: block;
	height: 3.44rem;
	-moz-box-flex: 1;
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
	width: 100%;
}

.portrait_line {
	display: block;
	width: 3.04rem;
	height: 3.44rem;
	background: url(../images/portrait_icon3.png) no-repeat center;
	background-size: 100%;
	position: relative;
}

.portrait_line:before {
	content: "";
	width: 100%;
	height: 100%;
	background: url(../images/portrait_icon4.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0;
	left: 0
}

.video_flex_bottom {
	-moz-box-flex: 1;
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
	width: 100%;
}

.video_flex_bottom .table_wrap {
	height: 100%;
	width: 100%;
	display: table;
}

.video_flex_bottom .table_wrap .table_td {
	height: 100%;
	display: table-cell;
	vertical-align: middle;
	text-align: center;
	padding: 0 0.2rem;
}

.video_errortips {
	width: 100%;
	text-align: center;
	position: fixed;
	bottom: 10%;
	left: 0;
	z-index: 200;
}

.video_errortips span {
	display: inline-block;
	padding: 0.11rem 0.2rem;
	text-align: center;
	background: #FD4D43;
	border-radius: 0.08rem;
	font-size: 0.18rem;
	line-height: 0.24rem;
	color: #fff;
}

.video_flex_middle .video_errortips {
	padding: 0 0.25rem;
	position: absolute;
	bottom: auto;
	top: 50%;
	left: 0;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
}


.back_btn {
	display: block;
	width: 0.32rem;
	height: 0.32rem;
	background: rgba(187,187,187,0.35) url(../images/icon_back4.png) no-repeat center;
	background-size: 0.16rem;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border-radius: 50%;
	position: absolute;
	top: 0.06rem;
	left: 0.1rem;
	z-index: 300;
}

.video_flex_wrap.spel .video_flex_head {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
	background: none;
}

.video_flex_wrap.spel .video_flex_top {
	flex: 1;
	padding-top: 0.44rem;
	height: auto;
}

.v_opea_tips {
	display: block;
	padding: 0.08rem 0.15rem;
	text-align: center;
	color: #ffffff;
	font-size: 0.18rem;
	line-height: 0.24rem;
	background: rgba(12, 52, 88, 0.50);
	border: 1px solid rgba(73, 143, 213, 0.4);
	border-radius: 0.1rem;
}

.v_com_btn {
	padding: 0 0.2rem;
	text-align: center;
}

.v_com_btn a {
	display: inline-block;
	text-align: center;
	vertical-align: top;
	padding: 0 0.3rem;
	min-width: 1.75rem;
	height: 0.44rem;
	line-height: 0.42rem;
	border: 1px solid #2772FE;
	background: #2772FE;
	border-radius: 0.5rem;
	font-size: 0.17rem;
	color: #EAEAEA;
}

.v_com_btn a .begin_icon {
	display: inline-block;
	width: 0.2rem;
	height: 0.2rem;
	background: url(../images/begin_icon2.png) no-repeat center;
	background-size: 100%;
	vertical-align: top;
	margin-right: 0.08rem;
	position: relative;
	top: 0.11rem;
}

.v_com_btn a.finish {
	border: 1px solid #FD4D43;
	background: #FD4D43;
}

.v_com_btn a.disabled {
	border-color: rgba(189, 219, 250, 0.9) !important;
	background: none !important;
	color: #BDDBFA !important;
	opacity: 0.9;
}

.v_com_btn a.disabled .begin_icon {
	background-image: url(../images/begin_icon.png);
}

.v_record_time {
	height: 0.44rem;
	line-height: 0.44rem;
	text-align: center;
	color: #fff;
	font-size: 0.23rem;
}

.v_record_time .ing {
	position: relative;
}

.v_record_time .ing:before {
	content: "";
	width: 0.08rem;
	height: 0.08rem;
	background: #EA4940;
	border-radius: 50%;
	position: absolute;
	top: 50%;
	margin-top: -0.04rem;
	left: -0.18rem;
}

.bc_ingicon {
	height: 0.9rem;
	position: relative;
	margin: 0 auto;
}

.bc_ingicon img {
	display: block;
	height: 100%;
	margin: 0 auto;
}

.bc_wrapbox {
	text-align: center;
	background: rgba(12, 52, 88, 0.50);
	border: 1px solid rgba(73, 143, 213, 0.4);
	border-radius: 0.1rem;
	padding: 0.1rem 0.15rem 0.12rem;
}

.bc_text {
	text-align: center;
}

.bc_text p {
	text-align: left;
	border-radius: 0.1rem;
	color: #51B4FE;
	font-size: 0.18rem;
	line-height: 0.24rem;
}

.bc_text .readed {
	color: #FCC006;
}

.answer_countdown {
	display: block;
	width: 0.8rem;
	height: 0.8rem;
	margin: 0 auto;
	position: relative;
}

.answer_countdown canvas {
	width: 0.8rem;
	height: 0.8rem;
}

.answer_countdown b {
	display: block;
	width: 0.44rem;
	height: 0.44rem;
	background: #EA4940 url(../images/vd_mac.png) no-repeat center;
	background-size: 0.2rem 0.3rem;
	border-radius: 100%;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -0.22rem 0 0 -0.22rem;
	z-index: 50;
}

.answer_text {
	text-align: left;
	margin-top: 0;
	font-size: 0.22rem;
	line-height: 0.28rem;
}

.answer_text .ok,
.answer_text .error {
	position: relative;
	padding-right: 0.28rem;
}

.answer_text .ok {
	color: #2772FE;
}

.answer_text .error {
	color: #EA4940;
}

.answer_text .ok:after,
.answer_text .error:after {
	content: "";
	width: 0.22rem;
	height: 0.22rem;
	position: absolute;
	top: 50%;
	margin-top: -0.11rem;
	right: 0;
}

.answer_text .ok:after {
	background: url(../images/tip_ok2.png) no-repeat center;
	background-size: 100%;
}

.answer_text .error:after {
	background: url(../images/tip_error2.png) no-repeat center;
	background-size: 100%;
}

.bc_wrapbox h5 {
	font-size: 0.16rem;
	line-height: 0.22rem;
	font-weight: normal;
	margin-bottom: 0.1rem;
	color: rgba(255, 255, 255, 0.7);
}

.bc_wrapbox h5 .imp {
	display: inline-block;
	width: 0.18rem;
	height: 0.18rem;
	background: url(../images/tip_impicon.png) no-repeat center;
	background-size: 100%;
	vertical-align: top;
	margin-right: 0.06rem;
	position: relative;
	top: 0.02rem;
}

.face_icon {
	height: 0.9rem;
	position: relative;
	margin: 0 auto;
}

.face_icon img {
	display: block;
	height: 100%;
	margin: 0 auto;
}

.network_state {
	line-height: 0.2rem;
	font-size: 0.12rem;
	color: #ffffff;
	position: absolute;
	top: 0.12rem;
	right: 0.1rem;
	z-index: 200;
}

.network_state .error {
	color: #FF4951;
}

.network_state .warn {
	color: #FFBE00;
}

.network_state .ok {
	color: #17D744;
}

.video_loading {
	width: 1.6rem;
	padding: 0.15rem;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 0.05rem;
	text-align: center;
	font-size: 0.16rem;
	line-height: 0.2rem;
	color: #51B4FE;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -0.8rem;
	-webkit-transform: translateY(-100%);
	transform: translateY(-100%);
	z-index: 200;
}

.video_loading .pic {
	height: 0.72rem;
	margin-bottom: 0.25rem;
}

.video_loading .pic img {
	display: block;
	height: 100%;
	margin: 0 auto;
}

.video_compage {
	min-height: 100%;
	position: relative;
	background: #fff;
	padding-top: 0.32rem;
}

.review_video {
	width: 2.96rem;
	height: 3.94rem;
	margin: 0 auto 0.15rem;
	position: relative;
	box-shadow: 0 0.08rem 0.25rem rgba(0, 0, 0, 0.2);
	border-radius: 0.1rem;
	overflow: hidden;
}

.review_video .pic {
	width: 100%;
	height: 100%;
	background: #eeeeee;
	overflow: hidden;
}

.review_video .pic img {
	display: block;
	width: 100%;
	position: relative;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
}

.vd_result_box {
	background: rgba(0, 0, 0, 0.4);
	text-align: center;
	padding: 0.12rem 0.16rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	min-height: 0.48rem;
	color: #ffffff;
	width: 100%;
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 50;
}

.vd_result_box .ok,
.vd_result_box .error {
	display: inline-block;
	vertical-align: top;
	position: relative;
	padding-left: 0.28rem;
	text-align: left;
}


.vd_result_box .ok:before,
.vd_result_box .error:before {
	content: "";
	width: 0.2rem;
	height: 0.2rem;

	position: absolute;
	top: 0.02rem;
	left: 0;
}

.vd_result_box .ok:before {
	background: url(../images/tip_ok3.png) no-repeat center;
	background-size: 100%;
}

.vd_result_box .error:before {
	background: url(../images/tip_error2.png) no-repeat center;
	background-size: 100%;
}

.review_video .window {
	width: 100%;
	height: 100%;
	position: relative;
	background: #000000;
}

.review_video .window video {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}

.review_video .btn {
	width: 0.64rem;
	text-align: center;
	font-size: 0.16rem;
	line-height: 0.22rem;
	padding-top: 0.6rem;
	color: #fff;
	text-shadow: 0 0.02rem 0.04rem rgba(0, 0, 0, 0.77);
	position: absolute;
	top: 50%;
	left: 50%;
	margin-left: -0.32rem;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 50;
}

.review_video .btn:before {
	content: '';
	width: 0.52rem;
	height: 0.52rem;
	border-radius: 100%;
	background: rgba(0, 0, 0, 0.6) url(../images/review_play.png) no-repeat center;
	background-size: 0.3rem;
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: -0.26rem;
}

.review_video .btn.on:before {
	background-image: url(../images/review_stop.png);
}

.video_info {
	padding: 0.1rem 0.16rem;
}

.video_info h5 {
	text-align: center;
	font-weight: normal;
	font-size: 0.2rem;
	line-height: 0.28rem;
	margin-bottom: 0.16rem;
}

.video_info ul {
	width: 2.96rem;
	margin: 0 auto;
}

.video_info ul li {
	padding: 0.04rem 0 0.04rem 0.24rem;
	position: relative;
	font-size: 0.16rem;
	line-height: 0.22rem;
}

.video_info ul li i {
	width: 0.08rem;
	height: 0.08rem;
	border-radius: 50%;
	background: #BD321D;
	position: absolute;
	top: 0.11rem;
	left: 0.07rem;
}

.video_info ul li.tip_error i,
.video_info ul li.tip_ok i {
	width: 0.16rem;
	height: 0.16rem;
	top: 0.07rem;
	left: 0;
}

.video_info ul li.tip_error i {
	background: url(../images/tip_error.png) no-repeat center;
	background-size: 100%;
}

.video_info ul li.tip_ok i {
	background: url(../images/tip_ok.png) no-repeat center;
	background-size: 100%;
}

.lz_basebox {
	background: #fff;
	padding: 0.2rem 0.25rem 0.1rem;
	text-align: center;
	font-size: 0.16rem;
	line-height: 0.26rem;
}

.lz_basebox h5 {
	font-size: 0.22rem;
	line-height: 0.32rem;
	font-weight: 500;
	color: #FF4951;
	margin-top: 0.1rem;
	text-align: left;
}

.lz_basebox .pic {
	margin-top: 0.28rem;
}

.lz_basebox .pic img {
	display: block;
	margin: 0 auto;
	height: 2.5rem;
}

.bc_rate {
	height: 0.04rem;
	border-radius: 0.04rem;
	background: rgba(189, 219, 250, 0.4);
	position: relative;
	margin: 0 0.4rem;
}

.bc_rate b {
	display: block;
	height: 0.04rem;
	border-radius: 0.04rem;
	background: #2772FE;
}

.inte_bc_img {
	width: 0.36rem;
	height: 0.36rem;
	background: url(../images/bc_ic_bg.png) no-repeat center;
	background-size: 100%;
	float: left;
	position: relative;
	margin-top: 0.05rem;
}

.inte_bc_img img {
	width: 0.26rem;
	height: 0.2rem;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -0.1rem 0 0 -0.13rem;
}

.inte_bc_cont {
	height: 0.84rem;
	overflow: hidden;
	position: relative;
	margin-left: 0.5rem;
	margin-top: 0.03rem;
}

.inte_bc_cont .bc_text {
	width: 100%;
	min-height: 0.84rem;
	position: absolute;
	bottom: 0;
	left: 0;
}

.inte_bc_cont .bc_text p {
	font-size: 0.22rem;
	line-height: 0.28rem;
}

.inte_bc_cont .bc_text .ing {
	color: rgba(255, 255, 255, 0.7);
}

.qx_layer{
	width: 3rem;
	background: #fff url(../images/qx_lybg.png) no-repeat center top;
	background-size: 100% auto;
	border-radius: 0.1rem;
	padding: 0.92rem 0 0.3rem;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -1.5rem;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 6000;
}
.qx_lycont{
	padding: 0 0.35rem;
	text-align: center;
	font-size: 0.18rem;
	line-height: 0.3rem;
	color: #333333;
}
.qx_lybtn{
	padding: 0 0.24rem;
	display: flex;
	margin-top: 0.36rem;
}

.qx_lybtn a {
	display: block;
	width: 1.2rem;
	height: 0.4rem;
	line-height: 0.4rem;
	font-size: 0.14rem;
	text-align: center;
	background: linear-gradient(90deg, #BD321D 0%, #BD321D 100%);
	color: #ffffff;
	border-radius: 0.5rem;
	margin-left: 0.12rem;
}

.qx_lybtn a:first-child {
	margin-left: 0;
}

.qx_lybtn a.cancel {
	background: #E8F0FF;
	color: #BD321D;
}

.qx_lybtn a.disabled {
	opacity: 0.3;
}

.fc_sbbox {
  background: #fff;
  padding: 0.6rem 0.15rem;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #999999;
  text-align: center;
}
.fc_imgbox {
  width: 2.97rem;
  height: 2.97rem;
  margin: 0 auto 0.4rem;
  padding: 0.21rem;
  position: relative;
}
.fc_imgbox:before{
	content: "";
	width: 100%;
	height: 100%;
	background: url(../images/fc_bg01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0;
	left: 0;
}
.fc_imgbox.ing:before{
	-webkit-animation: allrotate 1s infinite linear;
	animation: allrotate 1s infinite linear;
}
.fc_imgbox .pic {
  border-radius: 100%;
  width: 2.55rem;
  height: 2.55rem;
  overflow: hidden;
  position: relative;
}
.fc_imgbox .pic img {
  display: block;
  width: 100%;
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}
.fc_sbbox h5 {
  font-size: 0.24rem;
  line-height: 0.28rem;
  font-weight: normal;
  color: #333;
  margin-bottom: 0.1rem;
}
.upload_progress{
	background: #ffffff;
	padding: 0.8rem 0.15rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #999999;
	text-align: center;
}
.progress_chart{
	width: 1.93rem;
	height: 1.93rem;
	margin: 0 auto 0.75rem;
	background: url(../images/upload_img1.png) no-repeat center;
	background-size: 100%;
	position: relative;
}
.progress_chart:before{
	content: "";
	width: 100%;
	height: 100%;
	background: url(../images/upload_img2.png) no-repeat center;
	background-size: 100%;
	-webkit-animation: allrotate 1s infinite linear;
	animation: allrotate 1s infinite linear;
	position: absolute;
	top: 0;
	left: 0;
}
.progress_chart .img{
	width: 1.2rem;
	height: 1.2rem;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -0.6rem 0 0 -0.6rem;
	background: url(../images/video_notimg.png) no-repeat center;
	background-size: 100%;
}
.upload_progress h5 {
  font-size: 0.24rem;
  line-height: 0.28rem;
  font-weight: normal;
  color: #333;
  margin-bottom: 0.1rem;
}




/*-- 问卷回访 visitsurvey  --*/

.visit_box {
	background: #ffffff;
	margin-bottom: 0.08rem;
}

.visit_box h5 {
	padding: 0.12rem 0 0.12rem 0.24rem;
	margin: 0 0.16rem;
	font-size: 0.16rem;
	line-height: 0.26rem;
	font-weight: normal;
	position: relative;
}

.visit_box h5 .num {
	font-style: normal;
	position: absolute;
	top: 0.12rem;
	left: 0;
}

.visit_box ul {
	border-top: 1px solid rgba(229, 229, 229, 0.5);
	display: flex;
}

.visit_box ul li {
	flex: 1;
	position: relative;
	padding: 0.16rem;
	text-align: center;
	line-height: 0.25rem;
	font-size: 0.16rem;
	color: #666666;
	border-left: 1px solid rgba(229, 229, 229, 0.5);
}

.visit_box ul li:first-child {
	border-left: 0 none;
}

.visit_box ul li span {
	position: relative;
	font-size: 0.16rem;
}

.visit_box ul li.checked span {
	color: #BD321D;
}

.visit_box ul li.checked span:before {
	content: '';
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_active.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	right: -0.28rem;
}

.warn_tipspan {
	display: inline-block;
	padding: 0.07rem 0.12rem;
	border-radius: 0.08rem;
	background: rgba(255, 72, 72, 0.1);
	font-size: 0.14rem;
	line-height: 0.22rem;
	color: #FF4848;
}


/*-- 开户结果 result  --*/

.result_header {
	background: #F7492E;
}

.result_header .header_inner>h1.title {
	color: #ffffff;
}

.result_header .icon_back {
	background-image: url(../images/icon_back2.png);
}

.result_header .icon_text {
	color: #ffffff;
}

.result_page {
	background: #ffffff;
	padding-top: 0.2rem;
}

.result_page:before {
	content: "";
	width: 100%;
	height: 2.13rem;
	background: linear-gradient(180deg, #F7492E 0%, #FF8D7C 100%);
	position: absolute;
	top: 0;
	left: 0;
}

.submit_result {
	background: #fff;
	border-radius: 0.04rem;
	box-shadow: 0 0 10px 0 rgba(104, 138, 184, 0.19);
	margin: 0 0.2rem 0.2rem;
	position: relative;
	z-index: 50;
	overflow: hidden;
}

.result_tips {
	padding: 0.22rem 0.16rem 0.24rem;
	text-align: center;
	line-height: 0.2rem;
	color: #999999;
}

.result_tips .icon_ok {
	width: 1.2rem;
	height: 1.2rem;
	margin: 0 auto 0.02rem;
	background: url(../images/icon_ok.png) no-repeat center;
	background-size: 100%;
}

.result_tips h5 {
	font-size: 0.24rem;
	line-height: 0.32rem;
	font-weight: normal;
	color: #000000;
}

.result_tips p strong {
	color: #FF4951;
	font-weight: 500;
}

.result_tips p a {
	color: #F88939;
}

.step_info {
	padding: 0.2rem 0;
	border-top: 1px dashed #eaeaef;
	position: relative;
}

.step_info:before,
.step_info:after {
	content: '';
	width: 13px;
	height: 13px;
	background: #f3f6f9;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	position: absolute;
	top: -7px;
}

.step_info:before {
	left: -6px;
}

.step_info:after {
	right: -6px;
}

.step_info ul {
	display: flex;
}

.step_info ul li {
	flex: 1;
	position: relative;
	text-align: center;
}

.step_info ul li:before {
	content: '';
	width: 100%;
	height: 1px;
	background: #EAEAEF;
	position: absolute;
	left: -50%;
	top: 0.15rem;
}

.step_info ul li:first-child:before {
	display: none;
}

.step_info ul li i {
	display: block;
	width: 0.3rem;
	height: 0.3rem;
	margin: 0 auto 0.02rem;
	background-repeat: no-repeat;
	background-position: 0 0;
	background-size: 0.3rem 0.68rem;
	background-color: #ffffff;
	-moz-border-radius: 100%;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	position: relative;
	z-index: 100;
}

.step_info ul li.s1 i {
	background-image: url(../images/step_ic01.png);
}

.step_info ul li.s2 i {
	background-image: url(../images/step_ic02.png);
}

.step_info ul li.s3 i {
	background-image: url(../images/step_ic03.png);
}

.step_info ul li.s4 i {
	background-image: url(../images/step_ic04.png);
}

.step_info ul li span {
	display: block;
	line-height: 0.2rem;
	color: #666666;
}

.step_info ul li.off:before,
.step_info ul li.on:before {
	background: #BD321D;
}

.step_info ul li.off i,
.step_info ul li.on i {
	background-position: 0 bottom;
}

.result_info {
	padding: 0 0.2rem;
	position: relative;
	z-index: 50;
}

.result_info ul li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	padding: 0.16rem 0;
	position: relative;
	line-height: 0.24rem;
	font-size: 0.16rem;
	display: flex;
}

.result_info ul li p {
	flex: 1;
	width: 100%;
	min-height: 0.24rem;
	text-align: right;
	color: #999999;
}

.result_info ul li .tit {
	display: block;
	width: 1rem;
	color: #666666;
}

.result_info ul li .link {
	color: #BD321D;
	float: right;
}

.result_info ul.finish li .tit {
	color: #999999;
}

.result_info ul.finish li p {
	text-align: left;
	color: #333333;
}

.adv_banbox {
	margin: 0.2rem;
}

.adv_banbox img {
	display: block;
	width: 100%;
}

.success_box {
	margin-bottom: 0.15rem;
	padding: 0 0.2rem;
	position: relative;
	z-index: 50;
}

.success_box h3 {
	font-size: 0.24rem;
	font-weight: normal;
	line-height: 0.32rem;
	margin-bottom: 0.2rem;
	text-align: center;
	color: #ffffff;
}

.success_box .info {
	background: #fff url(../images/result_card.png) no-repeat center top;
	background-size: 100%;
	box-shadow: 0 0 0.05rem 0 rgba(104, 138, 184, 0.19);
	border-radius: 0.04rem;
	padding: 0.26rem 0.4rem 0.2rem;
	min-height: 1.74rem;
	line-height: 0.2rem;
	position: relative;
}

.success_box .info h5 {
	font-size: 0.14rem;
	font-weight: normal;
	color: #999999;
}

.success_box .info .num {
	height: 0.42rem;
	line-height: 0.42rem;
	margin: 0.05rem 0;
	font-size: 0.3rem;
	color: #BD321D;
	font-weight: 500;
}

.success_box .info p {
	font-size: 0.15rem;
	line-height: 0.21rem;
	margin-bottom: 0.15rem;
	color: #666666;
}

.success_box .info .link {
	color: #BD321D;
	display: inline-block;
	margin-top: 0.15rem;
}

.success_box .info .name {
	font-size: 0.14rem;
	color: #999;
	position: absolute;
	bottom: 0.2rem;
	right: 0.2rem;
}

.cs_infotips {
	padding: 0.17rem 0.2rem;
	font-size: 0.14rem;
	line-height: 0.22rem;
	color: #999999;
}

.cs_infotips p {
	padding-left: 0.24rem;
	position: relative;
}

.cs_infotips p:before {
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/icon_imp.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.03rem;
	left: 0;
}

.spel_input .t1 {
	display: block;
	width: 100%;
	padding: 0.1rem 0.12rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	outline: none;
	border: 0 none;
	background: #F8F8F8;
	color: #333333;
	border-radius: 0.04rem;
}

.protocol_list {
	background: #fff;
	padding: 0 0.16rem;
	margin-bottom: 0.08rem;
}

.protocol_list li {
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.protocol_list li a {
	display: block;
	padding: 0.16rem 0.3rem 0.16rem 0;
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #333333;
	position: relative;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.protocol_list li a:after {
	content: '';
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	right: 0;
}



/*--  港澳台、外国人  --*/
.page_title {
	padding: 0.15rem 0.1rem 0.15rem 0.2rem;
	font-size: 0.24rem;
	font-weight: normal;
	line-height: 0.34rem;
	color: #333;
}

.page_title em {
	display: block;
	font-size: 0.14rem;
	line-height: 0.24rem;
	color: #F3992D;
}

.open_nav {
	margin: 0.1rem 0.2rem;
}

.open_nav .item {
	background: #fff;
	border-radius: 0.04rem;
	box-shadow: 0px 0.04rem 0.15rem 0px rgba(9, 31, 60, 0.08);
	margin-top: 0.2rem;
	padding: 0.27rem 0.15rem 0.27rem 0.8rem;
	position: relative;
}

.open_nav .item:first-child {
	margin-top: 0;
}

.open_nav .item .icon {
	width: 0.4rem;
	height: 0.4rem;
	position: absolute;
	top: 50%;
	margin-top: -0.2rem;
	left: 0.2rem;
}

.open_nav .item .icon img {
	display: block;
	width: 100%;
}

.open_nav .item h5 {
	font-size: 0.2rem;
	line-height: 0.26rem;
	font-weight: normal;
	color: #000;
}

.open_nav .item p {
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #999;
	margin-top: 0.06rem;
}

.ready_detail {
	padding: 0.15rem 0.2rem;
}

.ready_detail .item {
	padding: 0.16rem 0;
	overflow: hidden;
}

.ready_detail .item .pic {
	width: 0.4rem;
	padding: 0.03rem 0;
	float: left;
}

.ready_detail .item .pic img {
	display: block;
	width: 100%;
}

.ready_detail .item .cont {
	margin-left: 0.55rem;
}

.ready_detail .item .cont h5 {
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #000;
	font-weight: 500;
	margin-bottom: 0.02rem;
}

.ready_detail .item .cont p {
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #666;
	padding: 0.02rem 0;
}

.item_form {
	padding: 0 0.2rem;
}

.input_item {
	position: relative;
	padding-top: 0.2rem;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
}

.input_item .tit {
	display: block;
	font-size: 0.12rem;
	line-height: 1;
	color: #999;
}

.input_item .t1 {
	display: block;
	width: 100%;
	height: 0.48rem;
	padding: 0.13rem 0;
	line-height: 0.22rem;
	font-size: 0.16rem;
	color: #333;
	outline: none;
	border: 0 none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.input_item .tarea1 {
	display: block;
	width: 100%;
	min-height: 0.48rem;
	padding: 0.13rem 0;
	line-height: 0.22rem;
	font-size: 0.16rem;
	color: #333;
	outline: none;
	border: 0 none;
	-webkit-user-select: auto;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.input_item .tarea1:empty:before {
	content: attr(placeholder);
	color: #999;
}

.input_item .dropdown {
	display: block;
	width: 100%;
	height: 0.48rem;
	padding-right: 0.3rem;
	position: relative;
}

.input_item .dropdown:after {
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	right: 0.05rem;
}

.date_wrap {
	height: 0.48rem;
	overflow: hidden;
}

.date_wrap .item {
	width: 45%;
	height: 0.48rem;
	position: relative;
	float: left;
}

.date_wrap .item strong {
	display: block;
	height: 0.48rem;
	padding: 0.13rem 0;
	font-size: 0.16rem;
	line-height: 0.22rem;
	font-weight: normal;
	color: #333;
}

.date_wrap .item input {
	width: 100%;
	height: 100%;
	border: 0 none;
	background: none;
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
	z-index: 50;
}

.date_wrap .line {
	width: 10%;
	float: left;
	text-align: center;
	font-size: 0.16rem;
	color: #333;
	line-height: 0.48rem;
}

.date_wrap .item:last-child strong {
	text-align: right;
}

.date_wrap .item strong:empty:before {
  content: attr(placeholder);
  color: #999;
}

.input_item .code_img {
	top: 50%;
	margin-top: -0.19rem;
	right: 0;
}

.input_item .code_btn {
	width: auto;
	padding: 0;
	border: 0 none !important;
	line-height: 0.38rem;
	font-size: 0.16rem;
	color: #F88939;
	top: auto;
	bottom: 0.05rem;
	right: 0;
}

.input_item .code_btn.time {
	color: #999;
}

.input_item .code_btn.time b {
	font-weight: normal;
	color: #F88939;
	margin-right: 0.05rem;
}

.nav_selelist {
	margin: 0.1rem 0.2rem;
}

.nav_selelist .item {
	background: #fff;
	border-radius: 0.04rem;
	box-shadow: 0px 0.04rem 0.15rem 0px rgba(9, 31, 60, 0.08);
	margin-top: 0.2rem;
	padding: 0.27rem 0.15rem 0.27rem 0.2rem;
	position: relative;
}

.nav_selelist .item:first-child {
	margin-top: 0;
}

.nav_selelist .item h5 {
	font-size: 0.2rem;
	line-height: 0.26rem;
	padding-right: 0.3rem;
	margin-bottom: 0.06rem;
	font-weight: normal;
	color: #BD321D;
	position: relative;
}

.nav_selelist .item h5:after {
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/arrow01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.08rem;
	right: 0;
}

.nav_selelist .item p {
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #999;
}

.nav_selelist .item p.p1 {
	padding-left: 0.2rem;
	position: relative;
}

.nav_selelist .item p .num {
	position: absolute;
	top: 0;
	left: 0;
}

.upload_wrap {
	overflow: hidden;
	padding: 0.05rem 0.12rem;
	margin-bottom: 0.15rem;
}

.upload_item {
	width: 50%;
	padding: 0 0.08rem;
	float: left;
	position: relative;
}

.upload_item .pic {
	position: relative;
	overflow: hidden;
	padding-top: 62.5%;
	background: #F1F1F1;
	border-radius: 0.03rem;
}

.upload_item .pic img {
	width: 100%;
	position: absolute;
	top: 50%;
	left: 0;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
}

.upload_item .btn {
	width: 100%;
	padding-top: 35%;
	height: 100%;
	text-align: center;
	font-size: 0.14rem;
	color: #BD321D;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 50;
}

.upload_item .btn:before {
	content: "";
	width: 0.32rem;
	height: 0.28rem;
	background: url(../images/icon_photo04.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: -0.16rem;
	margin-top: 15%;
}

.upload_link {
	padding-top: 62.5%;
	position: relative;
}

.upload_link a {
	display: block;
	width: 0.64rem;
	height: 0.24rem;
	line-height: 0.22rem;
	border: 1px solid #BD321D;
	color: #BD321D;
	font-size: 0.12rem;
	text-align: center;
	border-radius: 0.02rem;
	position: absolute;
	top: 50%;
	margin-top: -0.12rem;
	left: 50%;
	margin-left: -0.32rem;
	z-index: 50;
}

.upload_item .reset_btn {
	padding: 0 0.11rem 0 0.33rem;
	line-height: 0.27rem;
	font-size: 0.13rem;
	color: #fff;
	background: rgba(0, 0, 0, 0.4);
	-moz-border-radius: 0.2rem;
	-webkit-border-radius: 0.2rem;
	border-radius: 0.2rem;
	position: absolute;
	left: 50%;
	bottom: 0.1rem;
	-webkit-transform: translateX(-50%);
	transform: translateX(-50%);
	z-index: 100;
}

.upload_item .reset_btn:before {
	content: '';
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/icon_photo01.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	left: 0.11rem;
	top: 50%;
	margin-top: -0.08rem;
}

.two_input{
	overflow: hidden;
	display: flex;
}
.two_input .input_item{
	flex: 1;
	margin-left: 0.16rem;
}
.two_input .input_item:first-child{
	margin-left: 0;
}

.date_long{
	font-size: 0.12rem;
	padding-left: 0.26rem;
	color: #666;
	position: absolute;
	top: 0.13rem;
	right: 0;
	z-index: 60;
}
.date_long .icon_check{
	position: absolute;
	top: -0.02rem;
	left: 0;
}
.input_tips{
	padding: 0.16rem 0.2rem;
	font-size: 0.14rem;
	line-height: 0.22rem;
	color: #999999;
}
.input_tips .imp{
	color: #F3992D;
}

.file_emp_layer{
	background: #fff;
	padding: 0.45rem 0;
	width: 100%;
	position: absolute;
	top: 0.44rem;
	bottom: 0;
	left: 0;
	z-index: 500;
}

.file_emp_layer h2{
	text-align: center;
	font-size: 0.24rem;
	font-weight: normal;
	color: #333;
	line-height: 0.42rem;
	padding-bottom: 0.2rem;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	margin: 0 10.5% 0.3rem;
}
.file_emp_layer .pic{
	width: 2.95rem;
	margin: 0 auto;
}
.file_emp_layer .pic img{
	display: block;
	width: 100%;
}

.file_emp_layer .close{
	width: 0.32rem;
	height: 0.32rem;
	background: url(../images/icon_close3.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	bottom: 9%;
	left: 50%;
	margin-left: -0.16rem;
	z-index: 50;
}

.area_seleinfo{
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	line-height: 0.48rem;
	display: flex;
}
.area_seleinfo span{
	display: block;
	flex: 1;
	padding: 0 0.05rem;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	height: 0.48rem;
	color: #333333;
	font-size: 0.16rem;
	line-height: 0.48rem;
	position: relative;
	top: 1px;
	text-align: center;
}
.area_seleinfo span.active{
	color: #BD321D;
}
.area_seleinfo span.active:after{
	content: "";
	width: 0.3rem;
	height: 0.02rem;
	border-radius: 0.02rem;
	background: #BD321D;
	position: absolute;
	bottom: 0;
	left: 50%;
	margin-left: -0.15rem;
}

.area_seleinfo + .popup_lycont{
	height: 4.37rem;
}
.tax_formbox .input_form{
	margin-top: 0.08rem;
}

.add_btn{
	margin: 0.16rem 0.16rem;
}
.add_btn a{
	display: block;
	height: 0.4rem;
	line-height: 0.38rem;
	text-align: center;
	background: #fff;
	border: 1px solid #BD321D;
	font-size: 0.16rem;
	color: #BD321D;
	border-radius: 0.5rem;
}
.delete_btn{
	display: block;
	height: 0.48rem;
	text-align: center;
	line-height: 0.48rem;
	font-size: 0.16rem;
	color: #FF4951;
}

.b_border{
	border-bottom: 1px solid rgba(229, 229, 229, 0.5); 
}

.tax_formbox .input_text.text .t1,
.tax_formbox .input_text.text .dropdown,
.tax_formbox .input_text.text .tarea1{
	padding-left: 1.7rem;
}

.tpbank_title{
	background: #fff;
	border-top: 0.08rem solid #F8F8F8;
	border-bottom: 1px solid rgba(229, 229, 229, 0.5);
	padding: 0.16rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #666666;
}
.tpbank_title .imp{
	color: #F3992D;
}
.tpbank_title .icon{
	width: 0.4rem;
	height: 0.4rem;
	float: left;
}
.tpbank_title .icon img{
	display: block;
	width: 100%;
}
.tpbank_title p{
	margin-left: 0.55rem;
}


/*-- other --*/
.hui-dialog-black-mask,
.hui-dialog-white-mask {
  z-index: ******** !important;
}

.one_video_local_div video {
  object-fit: cover;
}

.browser_tip {
  width: 100%;
  min-height: 100%;
  background: #e8e8e8;
  background: rgba(0, 0, 0, 0.8);
  position: absolute;
  z-index: 51;
  top: 0;
  bottom: 0rem;
}
.browser_tip img {
  display: block;
  width: 100%;
}
input[type='tel'].security {
  -ms-text-security: disc;
  -webkit-text-security: disc;
  -moz-text-security: disc;
}

.iosSafeArea .main,
.iosSafeArea .hui-datetime,
.iosSafeArea .popup_layer,
.iosSafeArea .dialog_overlay{
  padding-bottom: 0px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.iosSafeArea .upload_select{
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}
.iosSafeArea .header,
.iosSafeArea .equity_header {
  padding-top: 0px;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}


/*-- 框架弹框样式修改 --*/
.hui-confirm{
	width: 3rem;
	border-radius: 0.08rem;
	background: #ffffff;
	padding: 0;
}
.hui-confirm-inner{
	padding: 0.24rem;
}
.hui-confirm-hd{
	font-size: 0.18rem;
	line-height: 0.26rem;
	margin-bottom: 0.08rem;
	font-weight: 500;
}
.hui-confirm-bd{
	font-size: 0.14rem;
	line-height: 0.22rem;
	min-height: 0.44rem;
	max-height: 3.6rem;
	overflow: auto;
	color: #666666;
}
.hui-confirm-bd a{
	color: #BD321D;
}
.hui-confirm-ft{
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	border-top: 1px solid rgba(229, 229, 229, 0.5);
	margin: 0;
}
.hui-confirm-ft:after{
	display: none;
}
.hui-confirm-ft > a{
	display: block;
	flex: 1;
	height: 0.56rem;
	line-height: 0.56rem;
	font-size: 0.16rem;
	text-align: center;
	color: #BD321D;
	border-radius: 0;
	background: none;
	border-left: 1px solid rgba(229, 229, 229, 0.5);
}
.hui-confirm-ft > a:first-child {
	border-left: 0 none;
}
.hui-confirm-ft > a.default {
	color: #666666;
	font-weight: normal;
}
.hui-confirm-ft > a.primary {
	background: none;
	color: #BD321D;
}
.hui-confirm-ft > a.disabled {
	opacity: 0.3;
}
.hui-confirm-ft>a:after{
	display: none !important;
}


/*-- add 20220816 --*/
.old_tip_dialog{
	width: 3.28rem;
	padding: 0.24rem;
	background: #fff;
	border-radius: 0.08rem;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -1.64rem;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 6000;
}
.dialog_close{
	width: 0.3rem;
	height: 0.3rem;
	background: url(../images/icon_close.png) no-repeat center;
	background-size: 0.2rem;
	position: absolute;
	top: 0.1rem;
	right: 0.1rem;
	z-index: 50;
}
.old_tipbox{
	padding: 0.1rem 0.16rem 0.06rem;
	text-align: center;
	color: #000000;
}
.old_tipbox .icon{
	display: block;
	width: 0.7rem;
	height: 0.7rem;
	margin: 0 auto 0.16rem;
	background: url(../images/heart_icon.png) no-repeat center;
	background-size: 100%;
}
.old_tipbox h3{
	font-size: 0.3rem;
	line-height: 0.44rem;
	font-weight: 500;
}
.old_tipbox p{
	font-size: 0.2rem;
	line-height: 0.3rem;
	margin-top: 0.1rem;
	font-weight: 500;
	letter-spacing: 0.05rem;
}
.old_tipbox .p_button{
	height: 0.5rem;
	line-height: 0.5rem;
	font-size: 0.22rem;
	margin-top: 0.3rem;
}