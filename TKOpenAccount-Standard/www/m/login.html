<!DOCTYPE html>
<html>

<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
<meta name="format-detection" content="telephone=no" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="cache-control" content="no-cache">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="msapplication-tap-highlight" content="no">
<title></title>
<link href="css/style.css" rel="stylesheet" />
</head>

<body>
<section class="main fixed white_bg" data-page="home">
	<header class="header">
		<div class="header_inner">
			<h1 class="title">验证手机号</h1>
		</div>
	</header>
	<article class="content">
		<h5 class="com_title">请填写本人手机号</h5>
		<div class="input_form spel">
			<div class="input_text text">
				<span class="tit">手机号码</span>
				<input class="t1" id="flowNo" maxlength="11" type="number" placeholder="请输入手机号码" value="" />
			</div>
			<div class="input_text text code">
				<span class="tit">用户名</span>
				<input class="t1" id="userName" type="text" placeholder="请输入用户名" value="" />
			</div>
		</div>
		<div class="ce_btn mt30">
			<a class="p_button" href="javascript:void(0);">登录</a>
		</div>
	</article>
</section>

</body>
<script src="./js/jquery-2.1.4.min.js"></script>
<script>
	$(function(){
		$('.p_button').click(function(){
			let flowNo = $('#flowNo').val();
			let userName = $('#userName').val();
			if(!userName || !userName) {
				alert('手机号和用户名都不能为空');
				return false
			}
			let data = {
				flowNo,
				userName,
			}
			$.ajax({
				url: "https://opt-dev.thinkive.com:15149/auth-hello-server/jwt/create",
				type: "get",
				data,
				success: function (res) {
					sessionStorage.setItem('authorization', res.data.authorization)
					window.location.href='./uploadIdcard.html'
				}
			});
		})
	})
</script>
</html>