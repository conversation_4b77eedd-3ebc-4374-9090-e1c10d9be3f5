<!DOCTYPE html>
<html>

<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
<meta name="format-detection" content="telephone=no" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="cache-control" content="no-cache">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="msapplication-tap-highlight" content="no">
<title></title>
<link href="css/style.css" rel="stylesheet" />
</head>

<body>
<section class="main fixed white_bg" data-page="home">
	<header class="header">
		<div class="header_inner">
	    <a class="icon_back" href="./uploadIdcard.html"></a>
			<h1 class="title">插件列表</h1>
		</div>
	</header>
	<article class="content">
		<div class="protocol_page">
			<ul class="protocol_list"></ul>
		</div>
	</article>
</section>

</body>
<script src="js/jquery-2.1.4.min.js"></script>
<script src="./js/data.js"></script>
<script>
	$(function(){
   function settingList(){
      let _html = '';
      DATABASE.forEach(item => {
        _html += `<li data-no='${item.funNo}' data-name='${item.funName}'><a href="./parameter.html">${item.funNo}${item.funName}</a></li>`
      });
      $('.protocol_list').html(_html)
    }
  settingList();
  $('.protocol_list').on('click','a',function(){
    let current = {
      funNo:($(this).parent()).data('no'),
      funName:($(this).parent()).data('name')
    }
    sessionStorage.setItem('current',JSON.stringify(current))
  })
})

</script>
</html>