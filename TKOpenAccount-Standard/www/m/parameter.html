<!DOCTYPE html>
<html>

<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
<meta name="format-detection" content="telephone=no" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="cache-control" content="no-cache">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="msapplication-tap-highlight" content="no">
<title></title>
<link href="css/style.css" rel="stylesheet" />
</head>

<body >
	<div id="app">
		<section class="main fixed" data-page="home">
			<header class="header">
				<div class="header_inner">
					<a class="icon_back" href="./setting.html"></a>
					<h1 class="title">{{title}}</h1>
				</div>
			</header>
			<article class="content">
				<div class="idsupply_box">
					<h5 class="com_title" @click="showForm1=!showForm1" :class="showForm1? 'topderict': 'botderict'">插件调用入参 </h5>
					<div class="input_form" v-show="showForm1">
						<!-- <div>{{pageDate}}</div> -->
						<div class="input_text text" v-for="(item, index) in pageDate.invokeParams" :key="index">
							<!-- {{item}} -->
							<span class="tit">{{item.paramName}}</span>
							<div v-if="item.inputType == 'select'" class="dropdown" @click="showselect(item.paramKey, item.paramName)" >{{intoParams[item.paramKey] || ''}}</div>
							<div v-if="item.inputType == 'switch'" class="switch_info">
								<p>{{item.paramName}}</p>
								<div class="switch">
									<input type="checkbox"  v-model="intoParams[item.paramKey]"/>
									<div class="switch-inner">
										<div class="switch-arrow"></div>
									</div>
								</div>
							</div>
							<input v-if="item.inputType == 'input'" class="t1" type="text"  :placeholder="item.inputHit"  v-model="intoParams[item.paramKey]"/>
							<div v-if="item.inputType == 'json'" class="tarea1 needsclick"  @click="show_edit_json(intoParams, item.paramKey, item.paramName)" >{{intoParams[item.paramKey]}}</div>
						</div>
					</div>
					<h5 class="com_title" @click="showForm2=!showForm2" :class="showForm2? 'topderict': 'botderict'">插件返回结果</h5>
					<div class="input_form" v-show="showForm2">
						<div class="input_text text" v-for="(list, index) in pageDate.callbackParams" :key="index">
							<span class="tit">{{list.paramName}}</span>
							<input v-if="list.paramType == 'string'" class="t1" type="text" readonly v-model="callbackParams[list.paramKey]" />
							<!-- <img v-if="list.paramType == 'image'" class="form_img" src="./images/zb_ic01.png" alt=""> -->
							<img v-if="list.paramType == 'image'" class="form_img" :src="callbackParams[list.paramKey]" alt="">
							<div v-if="list.paramType == 'json'" class="tarea1 needsclick" @click="show_edit_json(callbackParams, list.paramKey, list.paramName)">{{callbackParams[list.paramKey]}}</div>
							<!--  -->
						</div>
					</div>
				</div>
			</article>
			<footer class="footer">
				<div class="ce_btn">

					<a class="p_button" @click="start" href="javascript:void(0);">启动插件</a>
				</div>
			</footer>
		</section>

<!-- 选择弹出层 -->
	<div>
		<div class="dialog_overlay" v-if="show_pop_select"></div>
		<div class="popup_layer show" v-if="show_pop_select">
			<div class="popup_lytit">
				<a class="cancel" href="#" @click='closeSelect'></a>
				<h3>{{pop_title}}</h3>
			</div>
			<div class="popup_lycont fixed">
				<ul class="select_list">
					<li v-for="(item, index) in select_current.items" :class='item.selected? "active":""' :key="index+01" @click="selectLi(index)">
						<span>{{item.name}}</span>
					</li>
				</ul>
			</div>
		</div>
	</div>
<!-- json输入框弹出层 -->
<div>
	<div class="dialog_overlay" v-if="show_pop_json"></div>
	<div class="popup_layer show" v-if="show_pop_json">
		<div class="popup_lytit">
			<a class="cancel" href="#" @click='closeJson'></a>
			<h3>{{json_title}}</h3>
		</div>
		<div class="popup_lycont fixed">
			<textarea class="popup_lycont_textarea" v-model="current_edit_json"></textarea>
		</div>
	</div>
</div>
</div>
</body>
<script src="js/jquery-2.1.4.min.js"></script>
<script src="js/vue.min.js"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14"></script> -->
<!-- <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script> -->
<script src="js/externalAPP.js"></script>
<script src="./js/data.js"></script>
<!-- <script src="./js/parameter1.js"></script> -->
<script src="./js/parameter.js"></script>

</html>
<script>


</script>
