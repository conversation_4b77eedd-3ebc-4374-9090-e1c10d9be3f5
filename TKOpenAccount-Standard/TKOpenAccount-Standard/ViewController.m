//
//  ViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON><PERSON> on 15/5/9.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "ViewController.h"

// 必须导入的文件
#import <TKWebViewApp/TKWebViewApp.h>

// 为仿制而添加的适配器。集成正式的SDK时，可以去掉
#import "TKTempService.h"


#import "TKOpenController.h"


@interface ViewController ()

@property (nonatomic, readwrite, strong) UITextField *textField;
@property (nonatomic, readwrite, strong) UILabel *label;
@property (nonatomic, readwrite, strong) UILabel *label2;


@end

@implementation ViewController

-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];

}


- (void)viewDidLoad {
    [super viewDidLoad];
//    [self createAuth:@"com.hxsc.tzt.cn|com.zztzt.hxsckh"];
    //    [self goOpenView];

}

-(void)goOpenView{
    float left=20;
    UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(left, STATUSBAR_HEIGHT, 100, 30)];
    tipLabel.text=@"目标地址:";
    tipLabel.font=[UIFont systemFontOfSize:20];
    tipLabel.textColor=[UIColor blackColor];
    tipLabel.backgroundColor=[UIColor clearColor];
    tipLabel.textAlignment=NSTextAlignmentLeft;
    [self.view addSubview:tipLabel];
    
    //地址输入框
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    
    NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
    
    NSMutableDictionary *cDic;
    
    if (plistPath) {
        
        cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
    }
    
    
    UITextField *urlTextField=[[UITextField alloc] initWithFrame:CGRectMake(15, tipLabel.TKBottom, self.view.TKWidth-30, 30)];
    urlTextField.text=cDic[@"H5Url"];
    urlTextField.textColor=[UIColor blackColor];
    urlTextField.backgroundColor=[UIColor clearColor];
    urlTextField.tag=1111;
    urlTextField.clearButtonMode=UITextFieldViewModeWhileEditing;
    [self.view addSubview:urlTextField];
    UIView *lineView=[[UIView alloc] initWithFrame:CGRectMake(left, urlTextField.TKBottom, urlTextField.TKWidth, 1)];
    lineView.backgroundColor=[UIColor grayColor];
    [self.view addSubview:lineView];
    
    UIButton *goBtn=[[UIButton alloc] initWithFrame:CGRectMake(left, lineView.TKBottom+10, 100, 50)];
    [goBtn setTitle:@"GO" forState:UIControlStateNormal];
    [goBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [goBtn setBackgroundColor:[UIColor grayColor]];
    [goBtn addTarget:self action:@selector(goOpenCtr) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:goBtn];
}
-(void)goOpenCtr{
    UITextField *urlTextField=(UITextField *)[self.view viewWithTag:1111];
    NSMutableDictionary *cDic=[[NSMutableDictionary alloc] init];
    cDic[@"h5Url"]=urlTextField.text;
    TKOpenController *mCtl=[[TKOpenController alloc] initWithParams:cDic loginInfoParam:@{@"xiaomi_id":@"Cuq55nJEmNioAqC5Bi+8RV9B1PMqG0poFCaU21HnVs5riHcdJm0G+8lhCN22t1kI08t1CwqTJJ6ETxECZf1Rh7XNMSP1TqkpU4SSMz3SlxZw+Ny/baQg2oveCr6IFf5S6RvmN3Uz198rxKC+O9T+yKxmCUuwGW3rSHptvUz+bZU="}];
    [self presentViewController:mCtl animated:YES completion:nil];
}

//结束触摸
- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    //isExclusiveTouch一个布尔值来指示接收机处理触摸事件。
    //没有触摸_textUser进入if内操作
    if (![(UITextField *)[self.view viewWithTag:1111] isExclusiveTouch]) {
        //resignFirstResponder取消第一响应者状态的。如果对textfield使用的话，那么调用这个方法，textfield的第一响应者状态就会取消，然后键盘就消失了。
        [(UITextField *)[self.view viewWithTag:1111] resignFirstResponder];
    }
}


#pragma mark - 授权
///生成凡泰授权，授权内容日志打印
/// @param originAuthString 授权的bundleid，多个以|隔开，不穿就是默认授权
-(void)createAuth:(NSString *)originAuthString{
    //生成授权文件方法
    //授权key
    NSString *tkKey=[TKUUIDHelper uuid];
    NSLog(@"TKOpenAuthorization.lic:tkKey:%@",tkKey);
    //授权key的SM4值
    NSString *sm4Key=[TKSM4Helper stringWithSM4EncryptString:tkKey withKey:[TKPasswordGenerator generatorPassword]];
    //（授权key+框架密钥)的md5值的前32位，
    NSString *md532Lenght=[[TKMd5Helper md5Encrypt:[NSString stringWithFormat:@"%@%@",tkKey,[TKPasswordGenerator generatorPassword]]] substringToIndex:32];
    //需要授权的信息(默认授权内容)
    NSString *authString=[NSString stringWithFormat:@"%@#%@|%@",tkKey,@"com.thinkive.openaccountAdhoc",@"com.thinkive.mobile.account"];
    //传入的授权内容不为空
    if ([TKStringHelper isNotEmpty:originAuthString]) {
        authString=[NSString stringWithFormat:@"%@#%@",tkKey,originAuthString];
    }
    //授权密文
    NSString *authCipherString=[TKSM4Helper stringWithSM4EncryptString:authString withKey:md532Lenght];
    NSLog(@"TKOpenAuthorization.lic授权密文:%@\n%@",sm4Key,authCipherString);
}


- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    NSLog(@"touchesBegan");
}


@end



