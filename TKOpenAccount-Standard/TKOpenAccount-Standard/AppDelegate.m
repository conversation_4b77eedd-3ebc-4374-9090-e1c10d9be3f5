//
//  AppDelegate.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/5/9.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "AppDelegate.h"
#import "TKOpenController.h"
//#import "ModelLoading.h"
#define H5_URL @"H5Url"
#import "MNavViewController.h"
#import <TChat/TChatCore.h>
#import <TChat/TChatDefine.h>
#import "AnyChatDefine.h"
#import "AnyChatErrorCode.h"
#import "AnyChatPlatform.h"


#import "ViewController.h"
#import "TKOpenPrivacyAgreementView.h"
#import "TKOpenAccountService.h"



@interface AppDelegate ()<TKOpenDelegate,TKOpenPrivacyAgreementDelegate>

{
    TKOpenController *mCtl;
    TKOpenPrivacyAgreementView *privacyView;
}

@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    
    [super application:application didFinishLaunchingWithOptions:launchOptions];
    
//    NSString *ocrVersion= [ISIDCardReaderController getSDKVersion];
    
//    [TChatCore InitSDK];
//    int MainVer  =[TChatCore GetSDKOptionInt:TKCC_SO_CORESDK_MAIN_VERSION];
//    int SubVer  =[TChatCore GetSDKOptionInt:TKCC_SO_CORESDK_SUB_VERSION];
//    int StageVer  =[TChatCore GetSDKOptionInt:TKCC_SO_CORESDK_STAGE_VERSION];
//    NSString *versionTime=[TChatCore GetSDKOptionString:TKCC_SO_CORESDK_BUILD_TIME];
    
//    NSString *version=[AnyChatPlatform GetSDKVersion];
    
//    NSString *string=[ISIDCardReaderController getSDKVersion];
//    NSString *bankstring=[ISBankCardController getSDKVersion];

    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    
    NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
    
    NSMutableDictionary *cDic;
    
    if (plistPath) {
        
        cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
    }
    
    cDic[@"h5Url"]=cDic[H5_URL];
    if([TKStringHelper isEmpty:cDic[@"h5Url"]]){
     //原生直接这里改h5地址，打包机走config.plist配置；避免给sdk时候有地址相关配置
//        cDic[@"h5Url"]=@"https://regard01.95579.com:3000/auth-living-view/views/demo/index.html";
        
        cDic[@"h5Url"]=@"https://regard01.95579.com:2000/auth-living-view/views/demo/index.html";
    }
    
    cDic[@"h5Url"]=@"https://regard01.95579.com:18010/mt-react/index.html";

    
//    cDic[@"h5Url"]= @"www/m/login.html";
    
//    NSString *str = [TKAesHelper stringWithAesEncryptString:@"thinkivethinkivethinkive" withKey:[TKPasswordGenerator generatorPassword]];
    
    mCtl=[[TKOpenController alloc] initWithParams:cDic loginInfoParam:nil];
    //     mCtl.isShowLoadingCloseBtn=YES;
//    mCtl.isNoShowLoading=YES;
//    [mCtl loginInfoWithParam:@{@"token":@"123456",@"phoneNo":@"18888888888"}];
//    [mCtl loginOut];

//    [TKOpenController print3libSDKInfoLog];
    
//    mCtl.isImmersion=YES;
//    mCtl.oDelegate=self;
//    mCtl.isNeedTKAuthorIntroduce=YES;
//    mCtl.skinMode=@"1";
    
//    mCtl.loadingBgColorString=@"000000";
//    mCtl.gifImgName=@"photo_face.png";
    
//    //状态栏颜色
//    mCtl.statusBarBgColor = [TKUIHelper colorWithHexString:@"#0354c2"];
    
//    //原生导航头
//    //标题栏颜色
//    mCtl.titleColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
//    //返回按钮显示
//    mCtl.isShowBackBtn = YES;
//    //关闭按钮显示
//    mCtl.isShowCloseBtn = YES;
//    //标题栏文字
//    mCtl.title=@"手机开户";
//    //标题文字是否根据webview的Title而变化
//    mCtl.isChangeTitle = YES;
//    //如果Title不为空,返回或者关闭按钮的模式，默认是文本模式（0：文本，1：图片，2：文本+图片）
//    mCtl.btnMode = @"0";
    
//    mCtl.statusBarStyle = UIStatusBarStyleDefault;
//    mCtl.iphoneXBottomColor=[UIColor blackColor];

    
//    mCtl.progressColor = [UIColor clearColor];
//    [TKOpenViewStyleHelper shareInstance].isElder=YES;
    
    self.window.rootViewController = mCtl;
    
    

//    // 调试代码
//    ViewController *vc = [ViewController new];
//    self.window.rootViewController = vc;
    
    
    [self addPrivacyAgreement];
    return YES;
}
/**
 *  <AUTHOR> 2019年08月23日15:14:58
 *  加载隐私协议展示
 */
-(void)addPrivacyAgreement{
    NSString * agreePolicyFlag =(NSString *)[[TKCacheManager shareInstance] getCacheDataWithKey:@"agreePolicyFlag" cacheType:TKCacheType_File];

    if (![agreePolicyFlag isEqualToString:@"1"]) {
        privacyView=[[TKOpenPrivacyAgreementView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        privacyView.delegate=self;
        [mCtl.view addSubview:privacyView];
    }
}

#pragma TKDFPrivacyAgreementDelegate
-(void)agreePolicy{
    //设置同意协议标识位
    [[TKCacheManager shareInstance] saveCacheData:@"1" cacheType:TKCacheType_File withKey:@"agreePolicyFlag"];
    [privacyView removeFromSuperview];
}

/**
 *
 * @description 开户sdk外部调用回调
 */
- (void)interruptHandleExternalCall:(UIViewController*)hController withParams:(id)params{
    
}

- (void)applicationWillResignActive:(UIApplication *)application {
    // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
    // Use this method to pause ongoing tasks, disable timers, and throttle down OpenGL ES frame rates. Games should use this method to pause the game.
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    // Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
    // If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
    
    TKLogInfo(@"app enter background.");
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
    // Called as part of the transition from the background to the inactive state; here you can undo many of the changes made on entering the background.
    
    TKLogInfo(@"app enter foreground.");

}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    // Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
}

- (void)applicationWillTerminate:(UIApplication *)application {
    // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    
    NSURLCache *cache = [NSURLCache sharedURLCache];
    
    [cache removeAllCachedResponses];
    
    [cache setDiskCapacity:0];
    
    [cache setMemoryCapacity:0];
    
    NSHTTPCookieStorage *storage = [NSHTTPCookieStorage sharedHTTPCookieStorage];
    
    for (NSHTTPCookie *cookie in [storage cookies]) {
        
        [storage deleteCookie:cookie];
    }
}


- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication annotation:(id)annotation{
    
    TKLogInfo(@"返回信息：%@",[[url absoluteString] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding]);
    
    return YES;
}

@end
