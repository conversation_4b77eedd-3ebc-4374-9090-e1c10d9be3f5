1.V1.0.0 2018-08-30
  开户标准版本
2.V1.0.1 2018-09-12
  anychat升级到7.2版本，不依赖std这种旧库
  TKWebViewApp.framework升级到WKWebView版本
3.V1.0.2 2018-10-15
  60013插件照片拍照修改裁剪方式，先等比计算宽高再计算坐标
4.V1.0.3 2018-11-19
  修复TKOpenController获取document路径有空格转URL失败，导致assert断言崩溃的问题
5.V1.0.4 2018-11-23
  增加loadingBgColorString控制loading层的背景色
6.V1.0.5 2018-11-28
  增加gifImgName控制loading图具体用哪个
  增加skinMode控制错误提示页面黑白皮肤
7.V1.0.6 2018-12-03
  60014插件自定义界面退出使用易道api
8.V1.0.7 2018-12-19
  Anychat连接失败重试3次取消，直接报错提示
  易道OCR普通版60014和60013判断c接入的时候，图片转成base64字符串，易道设置为present方式弹出
 易道默认走present方式，易道银行卡扫多返回cardType，validDate参数
 易道普通版60014退出界面使用框架方法获取当前控制器
 增加isImmersion参数控制沉浸式样式
 浏览器底色设置和loading层颜色一致
 增加loginInfoWithParamNOCallJs只存储登录信息不通知js
 退出开户模块不再stop思迪引擎
9.V1.0.8 2019-03-25
将标准版60013,60014,60016插件依赖的图片放进统一的Resource资源文件管理，并修改相关代码（因为60013,60014,60016现在不使用xib文件写界面了，所以可以统一管理用代码读取bundle资源里面的图了;再更新SDK需要一起替换bundle文件）
易道SDK推出只使用dismiss
60013扫描框放大一点
60014，60013插件取景框使用新图片
TKOpenController的gifImgName改为id属性，可以接受图片设置为loading图或者图片名称
60013，60014插件通过isNeedWatermark参数控制是否加水印
10.V1.0.9 2019-06-25
60002快速多次点击相册重复上传问题优化
默认使用启动图展示直到h5加载完毕
连接Anychat连接服务器失败，登陆失败直接报错（Anychat重连不再回调了，所以来重连3次再报错的机制不生效了）
易道OCR版本不用去压缩图片了，给出的裁剪图都是150k左右
易道OCR版本上传等照片处理延迟2秒去掉
60026录制视频单声道，采样率16000
60002插件和android统一回调60050
60026视频上传完直接dismiss掉，不再先移除上传loading层导致界面有闪一下的感觉；
     提交完视频收到人脸识别失败结果原生提示页面和语音识别识别类似；
     不允许使用耳机；接口调整上传照片和视频用二进制流；增加视频过程中音量检测
60014合合ocr版本修复先相册选择照片后重试再用拍照照片展示角度不对的问题
修复网络异常情况情况下loading图不消失的问题
更新支持二维码扫描框架
webview给外部回调，可以用于埋点等操作（只需要h5埋点和原生打通情况）
60016插件增加相册和拍照按钮通过h5控制是否显示
增加思迪大数据埋点配置文件,h5的extParams当做埋点参数，增加挂断视频埋点
60014合合ocr增加isNeedOcrAffirmView参数控制是否扫描结果展示确认页面
MODEL_ST_SILENT_LIVENESS宏定义控制活体走商汤还是腾讯
商汤活体修改为使用离线版本
60006去掉HiraKakuProN-W3用默认字体
60010获取当前控制器方法也放进异步主线程
删除toast分类，使用框架TKLayerView提示
60013,60014上传失败处理\\n换行被转义的问题，替换成\n;并支持文本自适应;提示语和手机方向一致，60014的HUD提示不加载window上解决随手机方向旋转问题
60013和60014增加watermarkImgName来控制水印图选择
4.0视频界面增加isShowHeadRect控制是否展示头像对准框
主要使用插件获取当前控制器都放进主线程
智能单向app切到后台或锁屏提示
60010增加省市街道三个值回调给h5
TKOpenController增加存储或情况登陆信息类方法
60026返回给js增加start_time和video_length参数
60008拍的正面人脸照片调整方向为竖屏方向
11.V1.1.0 2019-09-10
tchat视频切换到后台再返回重新打开摄像头获取视频界面
60013插件改为新版界面
合合版60014扫描插件改为新版界面
易道四边定位版60013改为新版界面
易道扫版60013改为新版界面
易道扫版60014改为新版界面
h5传了视频服务器ip端口的话就用h5传的，h5没穿再用排队bus的
TChat4.0视频增加isShowHeadRect参数控制头像对准框
Anychat老的3.0视频增加isShowHeadRect参数控制头像对准框
60005视频isMsgAppend控制坐席发的话说滚动追加显示
更新框架适配Xocde11，iOS13系统
4.0排队界面设置自己的视频画面放到viewWillAppear执行
4.0排队主动挂断videoFlag修改
4.0视频修改13系统坐席发送文字过长展示不全问题
易道版本拍照插件兼容Xocde11
60008插件字典setObject修改赋值方式避免崩溃
60014合合版本ocr增加扫描超时提示功能
60014易道扫描版本ocr增加扫描超时提示功能
60014扫描插件顶部提示文字逻辑优化
+(void)loginInfoWithParam:(NSDictionary*)loginInfoParam方法增加判断通知js已经登录了
fxcAccountInfo改成存json对象
修复60014插件扫描超时后，扫描线动画停止问题
60014合合扫描超时再扫描出结果，修复超时视图不消失问题
anychat和tchat4.0排队连接视频去掉连接计数直接报错
tchat要是InitSDK直接调用返回失败就提示错误不用等代理回调
修复新版视频界面问题，调通tchat视频
扫描拍照照片展示变更为UIViewContentModeScaleAspectFit模式
统一登录增加api支持多账号登录存储模式
60002,60013,60014,60005接口的-999报错需要回调给h5处理
增加60028（大头照影像采集）和60030（普通单向视频新版UI交互）原生插件

11.V1.1.1 2020-03-21
anychat3.0视频排队level不写死，h5不传再默认1
公版开户SDK增加60032，60033，60034插件
增加公版SDK鉴权功能
4.0新界面增加staffTips判断获取展示逻辑
智能单向TKSmartOpenVolume音量先改为主动调大，不做提示（因为调整音量api还可以使用）
3.0视频增加坐席展示staff_tips逻辑处理
公版视频见证界面同步到新到新版排队的视频界面（TChat必须要5.2.0起步）
60013，60014确认页面去掉示例部分
标准版60013，60014颜色和图片主色mainColor支持h5传参
60006单向视频录制时间在刘海屏上适配问题
增加60037银行卡拍照插件
60026智能单向改为h5上传视频
标准版60016颜色和图片主色mainColor支持h5传参
初始化支持url带设置导航等相关ui参数
更新基础库删除对NOTE_CLOSE_MODULE通知实现，走基础库方法关闭当前
新版视频界面取消排队按钮和排队位置圆圈支持mainColor改变颜色
TKOpenResource.bundle中config.plist可配置没有授权硬件权限提示语
60005双向视频增加USR:1001:协议阅读展示指令
腾讯tts语音播放之前先改AVAudioSessionCategoryOptionDefaultToSpeaker
合合60014，60028大头照，60030普通单向视频新版支持原生直接调用实现代理
新版本视频界面3.0，4.0排队level优先取h5参数，没有默认0
新版视频界面无坐席时候话术默认：无坐席在线
易道四边定位版本增加超时提示机制
身份证拍照扫描增加isNeedSample控制是否显示示例
易道四边定位isCheckComplete启用时候，有遮挡场景下菊花转隐藏
设备权限工具类配置参数使用增加非空判断
智能单向新版普通单向支持h5传视频分辨率（默认240x320）
WKWebView关于jsessionId拼接都删掉
通过isNeedTKAuthorIntroduce参数控制插件调用前控制控制器是否弹出权限列表申请说明，优化逻辑
双向视频针对ipv6地址获取优化处理
智能单向，活体tklayer提示文字改成红色
适配新框架框架同步返回结果的插件设置self.isSyncPlugin=YES
60030，60026增加mainColor控制主色调
60013拍摄大头照调竖立方向

12.V1.1.2 2020-12-03
  增加对url里面的tk_isTKH5解析
  新版双向视频界面增加对文字消息通道USR:1002:类型，支持toast提示2秒消失
  tchat文字通道消息优化:如果是 USR:0: 开头的，USR:0: 去掉，如果不是就不用处理
  腾讯语音走协议实现
  60013界面消失前强制竖屏
  60005双向视频增加USR:1001:协议阅读展示指令增加容错判断避免后台消息协议不对崩溃
  60026音频频谱优化
  60005，60034新版视频界7p机型左下角消息框展示有白底问题修复
  60030增加阅读文本渐变指引
  人脸在框检查类支持h5传headers修改请求头
  插件回调都搞到BasePlugin里面回调
  60030和60026没有人脸检测的结果页走老版本
  60026和60057"tip_title":要是tip_content出现多音字情况下，该字段作为文本展示比如：请问您是自愿在国shèng证券开立账户吗？
  
13.V1.1.3 2021-06-02
  iOS插件请求Service需要回调的弄成一个属性Service
  双向视频4.0新界面支持读取h5的videoServerIP和videoServerPort的逻辑作为视频服务器地址
 60026支持mainColor改主题色
 kTableCustomSelfBgColor增加对华融的适配，（等他们优化后可以删掉）
 60007开始话术播放逻辑优化
 60007（只加腾讯版本只有腾讯要和服务器请求），60026，60030插件增加监听2g和断网提示退出通知h5
 60049增加商汤加固类型
 单向视频音量默认0.7，然后调小音量时候支持h5参数控制
 修复60013，60014拍照界面相册选择横屏状态下的选中后界面错乱问题
 
14.V1.1.4 2021-08-09
 新版单向视频相关UI支持
 修复双向视频3.0新界面的回调和排队问题
 60026，60007，60030，60057，60062支持h5控制默认音量
 60005老的4.0双向视频界面支持isUploadLog控制tchat日志上传服务器

15.V1.1.5 2021-10-09
 修复微服务网络请求数据为空时处理错误的问题
 优化60057音频就绪音频播放沙哑和重复释放闪退的问题
 升级alertView为alertController
 
16.V1.1.6 2021-11-05
  改版单向视频相关UI支持，60028UI调整
  60005，60034新版双向UI支持hangUpText
  60013，60014支持isNeedOcrAffirmView控制是否展示身份证上传确认页面
  阿里语音读取bundle的路径调整，不用再主动添加SDK内部的bundle文件了
  单向录制视频的时候，如果刚好在框检测失败跳转了提示页而此时又可以回答问题，回答是的语音仍能往下播报问题修复
  合合60014连续扫描也支持h5上传照片的方式

17.V1.1.7 2022-3-10
  关于NSMutableDictionary的setValue:value forKey:key方法调用改为setObject:value forKey:key去避免偶现的崩溃问题
  关于NSDateFormatter时间格式转换替换为使用框架的TKDateHelper，避免15.4系统时间格式出现上午下午字样

18.V1.1.8 2022-4-18
    关于NSMutableDictionary的系统setObject方法调整为=方法赋值
    客户端单向视频方向调整自适应
    60057服务器单向视频回看播放逻辑优化
    isShowTruePosition控制排队位置显示是否直接用接口还是只增不减
    佰锐BRAC_SO_NETWORK_P2PPOLITIC设置成0
    双向视频未登录兼容微服务的错误码

19.V5.0.0 2022-7-1
   5.0UI升级
   60028,60016,60013,60014增加0、-1状态码
   isNeedFullResults控制身60013,60014连拍情况下照片返回次数

20.V5.1.0 2022-9-26
   1. 单向60026,60057,60072支持新版参数
   2. 商汤动作活体升级3.0.9版本
   3. 60049关于版本号用宏定义打印的代码移除
   4. 单向支持html字符串
   
21.V5.2.0 2022-10-18
   1.原生主要插件适老化
   2.双向视频支持超限排队人数提示做单向功能
   3.60016合合ocr支持超时提示UI
   4.调整服务器单向和客户端单向编译依赖问题
   5.单向回答太长情况展示不了的话显示宽度固定保证回答旁边图标的显示，文本...省略
   6.60026的单向isSuccessShow失败提示界面原生仍然展示
   7.60026返回文件路径情况，退出不删除本地视频文件；重录时候删除本地视频文件
   8.60034插件改为5.0UI版本

22.V5.2.1 2022-12-02
   1.阿里智能语音单向优化iOS16系统兼容问题
   2.iPhone14机型身份证银行卡拍照扫描插件聚焦问题适配
   3.60044增加useProgress支持回调上传文件百分比
   4.60034支持渐变色
   5.60026支持push方式

23.V5.3.0 2022-12-26
   1、增加横屏智能单向（本地+Tchat）;
   2、视频预览页面UI优化；
   3、智能单向话术支持 “继续播报”
   4、增加横屏朗读录制
   5、支持拍摄营业证照
   6、双向视频支持USR:1000:协议；修改坐席信息背景透明度
   7、60028横屏界面改版，支持切换摄像头
   8、60032支持多拍，60007录制视频不申请麦克风权限

24.V5.4.0 2022-12-27
   1、集成TChatRtc SDK，单双向适配TChat SFU架构
   2、修改人脸检测工具类图片转换逻辑，若要转换成图片，在发送请求前处理，不需要发送的不转换
 
23.V5.4.1 2023-5-4
   1. 修改‘滴’一声默认逻辑
   2. 增加固定时间（可配置）没有回答提示展示文案

24.V5.5.0 2023-2-2
   1、60057、60077、60089滴一声改为基类处理h5参数来整
   2、服务端活体支持TChaRtc
   3、增加预览图设置，避免偶现的本地视频预览第一帧为空的问题和修复卡顿提示展示UI位置
   4、封装质检提示控件和质检提示在横屏录制时有增强效果
   5、增加60093插件控制屏幕常亮
   6、60034异常定义保持和60005一致
   7、 活体不再获取麦克风权限
   8、修复加载视频时动画还是旧版样式的问题
   9、封装60005旧版跳转方式
   10、修改TKOpenController 50114 导航控制器返回逻辑
   11、去掉了关于支付的注释和日志
   12、60013拍照和相册图片选择增加预览页面

25.V5.5.1 2023-4-4
   1、易道扫描身份证ocr，四边定位身份证ocr整合到一个版本；通过isExQuard参数控制走四边定位还是扫描
   2、60049增加港澳台ocr字段返回，微信小程序字段返回；支持通过queryKey直接查询结果
   3、60013相册直接上传压缩支持h5参数
   4、双向视频拍的坐席信息增加一个字段支持显示文字的优先级；新增msgTextSize调整字体大小
   5、60005的3.0视频支持新界面
   6、调整5.0双向视频关于底部取消按钮&自助见证按钮逻辑；部分UI调整
   7、更新61001，61003
   8、60007支持蓝牙耳机，也支持扬声器播放
   9、TKOpenController增加openDelegateActionCallback方法
   10、60032，60014预览无示例图提示文案调整

25.V5.5.2 2023-5-6
   1、排队提示语优先级调整queueLocationMsg > message > 原生默认
   2、增加60087投教视频观看插件
   3、人脸质检工具类增加挂起检测和恢复检测功能
   4、双向视频60005，60034插件升级到5.7埋点版本
   5、图片压缩走基础库方法，系统权限跳转走基础库方法
