//
//  TKOpenPlugin60000.m
//  TKApp
//
//  Created by 叶璐 on 15/4/17.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKOpenPlugin60000.h"

@implementation TKOpenPlugin60000

/**
 *  <AUTHOR> 2014-11-27 12:11:58
 *
 *  功能描述：判断影像文件是否存在
 *  参考插件：对应phoneGap的fileIsExistsPlugin插件								
 *
 *  @param userId       用户ID
 *  @param type         文件类型 （指定证书的厂商，如果mediaId传certificate，此值必传）
 *  @param mediaId      媒体协议ID
 *  @param funcNo       功能号：60000
 *
 *  @return mediaId     如何字段有返回值表示文件存在，否则不存在
 */

-(ResultVo *)serverInvoke:(id)param
{
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    NSString *userId = reqParam[@"userId"];
    if ([param[@"type"] isEqualToString:@"tw_new"]){
        userId = [NSString stringWithFormat:@"%@_tw_new",userId];
    }
    NSString *mediaId = reqParam[@"mediaId"];
    
    ResultVo *resultVo = [[ResultVo alloc]init];
    
    if ([TKStringHelper isEmpty:userId]) {
        resultVo.errorNo = -6000001;
        resultVo.errorInfo = @"用户ID不能为空!";
        
        return resultVo;
    }
    
    if ([TKStringHelper isEmpty:mediaId]) {
        resultVo.errorNo = -6000002;
        resultVo.errorInfo = @"媒体协议ID不能为空!";
        
        return resultVo;
    }
    
    NSString *result = @"";
    NSString *sn = @"";
    
    //读Document路径下的文件
    NSArray* paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString* documentsDirectory = [paths objectAtIndex:0];
    NSString *fileName = @"";
    if([mediaId isEqualToString:@"certificate"])
    {
        // 证书文件
        fileName = [NSString stringWithFormat:@"%@//cert00.cer",userId];
    }
    else
    {
        // 照片文件
        fileName = [NSString stringWithFormat:@"%@_%@.jpg",userId,mediaId];
    }
    
    NSString* fullPathToFile = [documentsDirectory stringByAppendingPathComponent:fileName];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    if ([fileManager fileExistsAtPath:fullPathToFile]) {
        
        TKCertManager *certManager = [[TKCertManager alloc] init];
        TKCertInfo *_certInfo = [certManager toGetX509Info:userId];
        
        if (_certInfo && [_certInfo isValidCert]) {
            if (reqParam[@"certSN"] && [_certInfo.certSerialNumber caseInsensitiveCompare:reqParam[@"certSN"]] != NSOrderedSame) {//证书序列号失效
                [fileManager removeItemAtPath:fullPathToFile error:nil];
                resultVo.errorNo = 0;
                resultVo.errorInfo = @"证书已失效";
            }else{
                result = mediaId;
                sn = _certInfo.certSerialNumber;
                resultVo.errorNo = 0;
            }
        }else{
                [fileManager removeItemAtPath:fullPathToFile error:nil];
                resultVo.errorNo = 0;
                resultVo.errorInfo = @"证书已过期";
        }
    }
    else
    {
        resultVo.errorNo = 0;
        resultVo.errorInfo = @"文件不存在!";
        NSLog(@"%s error:%@", __FILE__, result);
    }
    
    NSMutableDictionary *dataRow = [NSMutableDictionary dictionary];
    [dataRow setString:result withKey:@"mediaId"];
    [dataRow setString:sn withKey:@"certSerialNumber"];
    resultVo.results = dataRow;
    
    return resultVo;
}

@end
