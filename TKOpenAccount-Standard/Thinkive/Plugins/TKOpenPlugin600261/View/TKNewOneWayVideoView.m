//
//  TKNewOneWayVideoView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/8/8.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKNewOneWayVideoView.h"

@interface TKNewOneWayVideoView ()


@property (nonatomic, readwrite, strong) UILabel *titleLabel; // 标题文本
@property (nonatomic, readwrite, strong) UIView *hudView;   // 蒙层
@property (nonatomic, readwrite, strong) UIView *recordTimeBgView;   // 录制时间背景试图

@end

@implementation TKNewOneWayVideoView
@synthesize boxRect = _boxRect;
@synthesize backBtn = _backBtn;
@synthesize avPreviewView = _avPreviewView;
@synthesize bottomShowTipView = _bottomShowTipView;
//@synthesize recordTimeLabel = _recordTimeLabel;
@synthesize badgeView = _badgeView;
@synthesize countDownLabel = _countDownLabel;

- (void)dealloc {
    // 销毁时重置
    self.avPreviewView.layer.borderWidth = 0;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self enableTakeRecord:self.takeBtn.enabled];
    
//    [self.takeBtn removeFromSuperview];
//    self.takeBtn.hidden = YES;
}

- (void)viewInit{
    self.requestParam[@"isShowHeadRect"] = @"0";
    
    [self addSubview:self.hudView];
    [self addSubview:self.recordTimeBgView];
    
    [super viewInit];
    
    [self addSubview:self.titleLabel];
    
    [self.serviceGifView removeFromSuperview];

}


- (void)setButtonBackgroundColor:(UIButton *)button alpha:(CGFloat)alpha
{
    if (button.window) {
        // 先移除.注意不要把button的layer也移除了
        for (int i = 0; i < button.layer.sublayers.count; i++) {
            CALayer *layer = button.layer.sublayers[i];
            if ([layer isKindOfClass:CAGradientLayer.class]) {
                [layer removeFromSuperlayer];
            }
        };
        
        // 再添加
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:alpha].CGColor,(id)[TKUIHelper colorWithHexString:@"#5A92FF" alpha:alpha].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
        btoGradientLayer.frame = button.bounds;
        btoGradientLayer.startPoint = CGPointMake(0, 0.5);
        btoGradientLayer.endPoint = CGPointMake(1, 0.5);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        btoGradientLayer.cornerRadius = button.TKHeight / 2.0f;
        [button.layer insertSublayer:btoGradientLayer atIndex:0]; //设置颜色渐变
        
        button.layer.cornerRadius = button.TKHeight / 2.0f;
    }
}

/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
- (void)takeAction:(UIButton *)sender{
    
    if(!self.isReadVideo){
        self.titleLabel.hidden = YES;
        
        [super takeAction:sender];
    }
}

/**
@Auther Vie 2020年03月10日14:46:52
@隐藏中间准备提示文本
*/
-(void)hideWaitTip{
    [super hideWaitTip];
    
    [self.recordTimeBgView setHidden:NO];//展示录制倒计时
}

/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting forceDisplay:(BOOL)forceDisplay {

    if (!self.faceDectTipView.superview) {
        [self addSubview:self.faceDectTipView];
    }

    NSTimeInterval faceDetectInterval = 1.0;
    if ([self.requestParam[@"faceDetectInterval"] doubleValue] > 0) {
        faceDetectInterval = [self.requestParam[@"faceDetectInterval"] doubleValue];
    }
    
//    CGFloat maxWidth = self.TKWidth - (self.bottomShowTipView.TKLeft) * 2;
    CGFloat maxWidth = self.avPreviewView.TKWidth;
    [self.faceDectTipView showWarning:warningSting forceDisplay:forceDisplay displayTime:faceDetectInterval maxWidth:maxWidth y:20];
    
    if ([TKStringHelper isEmpty:warningSting]) {
        self.avPreviewView.layer.borderWidth = 0;
    } else {
        
        self.avPreviewView.layer.borderColor = [TKUIHelper colorWithHexString:@"#FF4848"].CGColor;
        self.avPreviewView.layer.borderWidth = 3;
    }
}

/// 设置是否可以点击开始录制
/// @param isEnable 是否可以点击
- (void)enableTakeRecord:(BOOL)isEnable {
    
    self.takeBtn.enabled = isEnable;
    if (isEnable) {

        [self setButtonBackgroundColor:self.takeBtn alpha:1.0f];
    } else {
        
        [self setButtonBackgroundColor:self.takeBtn alpha:0.3f];
    }
}

//- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow
//{
//    [super updateTextViewFrame:string textColor:colorString isOneLineShow:isOneLineShow];
//
//    if (isOneLineShow == NO) {
//        self.bottomShowTipView.TKHeight = 90;
//    }
//}

- (void)showNoAnswerPrompt
{
    [super showNoAnswerPrompt];
    self.answerPromptLabel.backgroundColor = [TKUIHelper colorWithHexString:@"#DBECFF99"];
}

- (void)updateCountDownLabelText:(int)answerCount countDownType:(TKCountDownType)countDownType
{
    self.countDownType = countDownType;
    
    [self.bottomShowTipView addSubview:self.countDownLabel];
    
    NSString *tempStr = nil;
    if (countDownType == TKCountDownTypeAnswer) {
        if (self.hasAsrResult == YES) {
            tempStr =[NSString stringWithFormat:@"识别中…(%d)",(int)answerCount] ;
        } else {
            tempStr =[NSString stringWithFormat:@"请回答…(%d)",(int)answerCount] ;
        }
        
        if (answerCount == 0) {
            tempStr =[NSString stringWithFormat:@"识别中…"] ;
        }
    } else {
        tempStr = [NSString stringWithFormat:@"(%d)",(int)answerCount];
    }
    self.countDownLabel.text = tempStr;
    
    [self.countDownLabel sizeToFit];
    self.countDownLabel.TKHeight = 22;
}

/**
<AUTHOR> 2021年07月08日10:30:58
@修改提示语问题话术
@string 文本
@colorString 文本颜色
@cornerRadius 背景框圆角
@flag 是否是播放语音话术（y坐标不一样要调整）
@flag 是否是html文本
@return 顶部遮罩层
*/
-(void)updateTipLabel:(NSString *)string textColor:(NSString *)colorString cornerRadius:(CGFloat)cornerRadius isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed autoScroll:(BOOL)autoScroll {
    
//    NSString *newColorString = [self.mainColorString isEqualToString:@"#2772FE"] ? @"#333333": self.mainColorString;
    NSString *newColorString = @"#333333";
    
    [super updateTipLabel:string textColor:newColorString cornerRadius:cornerRadius isOneLineShow:isOneLineShow isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed autoScroll:(BOOL)autoScroll];
}

- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow
{
    CGFloat bottomShowLabelX = 16;
    CGFloat bottomShowLabelY = 5;
    CGFloat bottomShowLabelWidth = self.bottomShowTipView.TKWidth - bottomShowLabelX * 2;
    CGFloat bottomShowLabelHeight = 0;
    
    // 如果不是播放问题，展示多行问题文本
    if (isOneLineShow == NO) {
        
        BOOL takeButtonIsHiden = self.takeBtn.superview == nil || self.takeBtn.isHidden;
        BOOL nextButtonIsHiden = self.nextBtn.superview == nil || self.nextBtn.isHidden;
        if (takeButtonIsHiden && nextButtonIsHiden) {
            // 采用固定高度，不再动态计算
            self.bottomShowTipView.TKHeight = self.TKHeight - self.bottomShowTipView.TKTop - IPHONEX_BUTTOM_HEIGHT;
        } else {
            if (takeButtonIsHiden == NO) {
                self.bottomShowTipView.TKHeight = self.takeBtn.TKTop - 10;
            }
            if (nextButtonIsHiden == NO) {
                self.bottomShowTipView.TKHeight = self.nextBtn.TKTop - 10;
            }
        }

        //文本左右保持留白15
       self.bottomShowLabel.frame = CGRectMake(bottomShowLabelX, bottomShowLabelY, bottomShowLabelWidth, self.bottomShowTipView.TKHeight - 2 * bottomShowLabelY);
        self.bottomShowLabel.textContainer.maximumNumberOfLines = INT_MAX;
        
    } else {    // 如果是播放问题，需要展示多行（>1  问题文本 + 倒计时文本 + 识别结果文本）
        
        [super updateTextViewFrame:string textColor:colorString isOneLineShow:isOneLineShow];
    }
}

#pragma mark - Setter && Getter
/**
 <AUTHOR> 2019年04月03日10:47:55
 @初始化懒加载人像取景框矩阵
 @return 人像取景框矩阵
 */
-(CGRect)boxRect{
    if (CGRectIsEmpty(_boxRect)) {
        //因为是多问题，对准框需要固定不变位置，按文本4行高度117弄y坐标
        float boxRectX =30;
        float gap=10;
        //5s等小屏幕机型
        if (UISCREEN_HEIGHT < 812) {
            boxRectX=50;
        }
        float boxRectWidth = self.TKWidth - boxRectX * 2.0f;
        float boxRectHeight = boxRectWidth / 335.0f * 434.0f;
        float boxRectY = (STATUSBAR_HEIGHT + NAVBAR_HEIGHT) + gap;
        _boxRect = CGRectMake(boxRectX, boxRectY, boxRectWidth, boxRectHeight);
    }
    return _boxRect;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
- (UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnWidth=24;
        float backBtnHeight=24;
        float backBtnX=12.0f;
        float backBtnY=8+STATUSBAR_HEIGHT;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnHeight)];
        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [_backBtn setTintColor:[TKUIHelper colorWithHexString:@"#000000"]];
        [_backBtn setImage:image forState:UIControlStateNormal];
        
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}

-(UIView *)recordTimeBgView{
    if (!_recordTimeBgView) {
        
        CGFloat y = 6+STATUSBAR_HEIGHT;
        CGRect frame = CGRectMake((self.TKWidth - 92) * 0.5, y, 92, 32);
        _recordTimeBgView = [[UILabel alloc] initWithFrame:frame];
        _recordTimeBgView.backgroundColor = [UIColor colorWithWhite:1 alpha:1];
        _recordTimeBgView.layer.cornerRadius = 15;
        _recordTimeBgView.clipsToBounds = YES;
        _recordTimeBgView.hidden = YES;
    }
    return _recordTimeBgView;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载recordTimeLabel
 @return recordTimeLabel
 */
-(UILabel *)recordTimeLabel{
    if (!_recordTimeLabel) {
        float y=8+STATUSBAR_HEIGHT;
        //        float width=96;
        float width=54;
        float height=28;
        float x=(self.frame.size.width-width)/2 + 10;
        _recordTimeLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _recordTimeLabel.text=@"00:00";
        _recordTimeLabel.textColor=[TKUIHelper colorWithHexString:@"#000000"];
        _recordTimeLabel.textAlignment=NSTextAlignmentCenter;
        _recordTimeLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
        
    }
    return _recordTimeLabel;
}

/**
 <AUTHOR> 2019年12月30日22:05:40
 @初始化懒加载底部文档等提示展示区域
 @return 底部文档等提示展示区域
 */
- (UIView *)bottomShowTipView{
    if (!_bottomShowTipView) {
        
        _bottomShowTipView=[[UIView alloc] init];
        //子视图是否局限于视图的边界。
        _bottomShowTipView.clipsToBounds=YES;
        
        float gap =15;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap = 5;
        }
        
        float bottomShowTipViewX=20;
        if (UISCREEN_HEIGHT < 812) {
            bottomShowTipViewX = 15;
        }
        float bottomShowTipViewY = self.boxRect.origin.y + self.boxRect.size.height + gap;
        float bottomShowTipViewWidth = self.TKWidth - 2 * bottomShowTipViewX; //左右留白15;
        self.bottomShowTipView.frame = CGRectMake(bottomShowTipViewX, bottomShowTipViewY, bottomShowTipViewWidth, 42);
    }
    return _bottomShowTipView;
}

/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载底部文字展示
 @return 底部文字展示
 */
-(UITextView *)bottomShowLabel{

    super.bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
    return super.bottomShowLabel;
}

- (UIView *)badgeView {
    if (!_badgeView) {
        NSInteger const pointWidth = 8; //小红点的宽高
        CGRect frame = CGRectMake(self.recordTimeLabel.TKLeft - pointWidth - 10, self.recordTimeLabel.TKTop + (self.recordTimeLabel.TKHeight - pointWidth) * 0.5, pointWidth, pointWidth);
        _badgeView = [[UILabel alloc] initWithFrame:frame];
        _badgeView.backgroundColor = [TKUIHelper colorWithHexString:@"ff5153"];
        //圆角为宽度的一半
        _badgeView.layer.cornerRadius = pointWidth / 2;
        //确保可以有圆角
        _badgeView.layer.masksToBounds = YES;
        
    }
    
    return _badgeView;
}

/**
 <AUTHOR>
 @初始化标题文本
 @return 返回按钮
 */
- (UILabel *)titleLabel{
    if (!_titleLabel) {
        
        float width = self.TKWidth;
        float height = 24;
        float x = 0;
        float y = 10 + STATUSBAR_HEIGHT;
        _titleLabel= [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _titleLabel.font =  [UIFont fontWithName:@"PingFang SC" size:16];;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.textColor = [TKUIHelper colorWithHexString:@"#000000" alpha:1];
        _titleLabel.text = @"视频录制";
    }
    return _titleLabel;
}


- (UIView *)hudView {
    if (!_hudView) {
        
//        CGFloat y = self.backBtn.TKBottom + 14;
//        CGRect frame = CGRectMake(10, y, self.TKWidth - 10 * 2, self.TKHeight - y - (IPHONEX_BUTTOM_HEIGHT > 0 ? IPHONEX_BUTTOM_HEIGHT : 10));
        CGRect frame = CGRectMake(0, 0, self.TKWidth, self.TKHeight);
        _hudView = [[UILabel alloc] initWithFrame:frame];
//        _hudView.backgroundColor = [TKUIHelper colorWithHexString:@"#E9EDFB"];
        _hudView.backgroundColor = [TKUIHelper colorWithHexString:@"#ffffff"];
//        _hudView.layer.cornerRadius = 13;
        _hudView.clipsToBounds = YES;
    }
    
    return _hudView;
}

- (void)setAvPreviewView:(UIView *)avPreviewView {
    _avPreviewView = avPreviewView;
    [self insertSubview:avPreviewView aboveSubview:self.hudView];
}

/**
 <AUTHOR> 2019年04月26日18:55:02
 @初始化懒加载回答倒计时展示label
 @return 回答倒计时展示label
 */
-(UILabel *)countDownLabel{
    if (!_countDownLabel) {
        float width=self.bottomShowLabel.TKWidth;
        float height=22;
        float x=self.bottomShowLabel.TKLeft;
        float y=40;
        _countDownLabel= [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _countDownLabel.font =  [UIFont fontWithName:@"PingFang SC" size:22];;
        _countDownLabel.textAlignment=NSTextAlignmentLeft;
        _countDownLabel.textColor = [TKUIHelper colorWithHexString:@"#33333399"];
    }
    return _countDownLabel;
}


/**
 <AUTHOR> 2020年08月26日10:52:52
 @初始化懒加载takeBtn
 @return takeBtn
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {
        float height=44;
        float gap=40;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=10;
        }
        
        NSString *btnTitle;
        if(self.isReadVideo){
            btnTitle=@"◉  开始录制";
        }else{
            btnTitle=@"确认放置好，进入下一步";
        }
        
        
        float y=self.TKHeight-height-gap-IPHONEX_BUTTOM_HEIGHT;
        float x = 16;
        float width = UISCREEN_WIDTH - x * 2;
        
        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_takeBtn setTitle:btnTitle forState:UIControlStateNormal];
        
        
        _takeBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
        [_takeBtn addTarget:self action:@selector(takeAction:) forControlEvents:UIControlEventTouchUpInside];
        _takeBtn.layer.cornerRadius=height/2.0f;
        
        // 默认一开始不可用
        [self enableTakeRecord:NO];
    }
    return _takeBtn;
}

/**
 <AUTHOR> 2019年12月30日22:20:29
 @初始化懒加载底部文档等提示展示区域横线
 @return 底部文档等提示展示区域横线
 */
- (UIView *)bottomShowTipLineView{

    return nil;
}

/**
 <AUTHOR> 2019年12月30日22:20:29
 @初始化懒加载bottomShowTipRecordLineView
 @return bottomShowTipRecordLineView
 */
- (UIView *)bottomShowTipRecordLineView{
    return nil;
}

/**
 *  <AUTHOR> 2023年07月10日13:30:12
 *  @初始化懒加载topBgView
 *  @return  topBgView
 */
- (UIView *)topBgView{
    return nil;
}

/**
 *  <AUTHOR> 2023年07月10日13:30:12
 *  @初始化懒加载bottomBgView
 *  @return  bottomBgView
 */
- (UIView *)bottomBgView{
    return nil;
}

@end
