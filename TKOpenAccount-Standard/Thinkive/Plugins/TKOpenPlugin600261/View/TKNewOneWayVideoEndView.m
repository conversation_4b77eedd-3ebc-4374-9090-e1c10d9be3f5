//
//  TKNewOneWayVideoEndView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/8/8.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKNewOneWayVideoEndView.h"

@interface TKNewOneWayVideoEndView ()

//@property (nonatomic, readwrite, strong) UIView *netBadBgView;
//@property (nonatomic, readwrite, strong) UILabel *netLabel;
//@property (nonatomic, readwrite, strong) UIImageView *netErrorImg;
//@property (nonatomic, readwrite, strong) UIImageView *coverImageView;
//@property (nonatomic, readwrite, strong) UILabel *titleLabel; // 标题文本
@property (nonatomic, readwrite, strong) UIView *hudView;   // 蒙层

@end

@implementation TKNewOneWayVideoEndView
@synthesize backBtn = _backBtn;
@synthesize titleLabel = _titleLabel;
/**
 <AUTHOR>
 @初始化单向视频正常走完流程结果页面
 */
-(void)viewInit{
    
    [self addSubview:self.hudView];
    
    [super viewInit];
    
    [self addSubview:self.titleLabel];
    
//    UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/tk_digtal_open_bg",TK_OPEN_RESOURCE_NAME]];
//    self.bgImgView.image = image;
//    [self insertSubview:self.coverImageView aboveSubview:self];
//    [self insertSubview:self.hudView aboveSubview:self.coverImageView];
    
//    self.resetBtn.backgroundColor = [UIColor whiteColor];
    
//    [self netBadTipView];
}

//服务器单向卡顿提示
//-(void)netBadTipView{
//    if ([self.requestParam[@"isMoreMaxCaton"] intValue]!=1) {
//        return;//没有提示卡顿需要
//    }
//
//    float x = self.videoShowImgView.TKLeft;
//    float widht = self.videoShowImgView.TKWidth;
//    float height = self.netLabel.TKHeight + 24;
//    float y = self.videoShowImgView.TKHeight - height;
//    self.netBadBgView.frame=CGRectMake(x, y, widht, height);
//
//    [self.netBadBgView removeFromSuperview];
//
//    [self.videoShowImgView.superview addSubview:self.netBadBgView];
//    [self.netBadBgView addSubview:self.netErrorImg];
//    [self.netBadBgView addSubview:self.netLabel];
//
//    if (self.netBadBgView.TKTop<=self.playTipLabel.TKBottom) {
//        [self.playTipLabel setTKBottom:self.netBadBgView.TKTop-10];
//        [self.playVideoBtn setTKBottom:self.playTipLabel.TKTop-17];
//    }
//}


#pragma mark  set or get

///**
// <AUTHOR>
// @视频展示示例图赋值
// */
//-(void)setVideoShowImg:(UIImage *)videoShowImg{
//    _videoShowImg = videoShowImg;
//    [self.videoShowImgView setImage:videoShowImg];
//    [self netBadTipView];
//}



///// 根据tip更新UI
///// @param tipArr tip数组。格式是：@[@{@"tipImage" : @"", @"tipContent" : @""}];
//- (void)updateTipViewWithTipArr:(NSArray *)tipArr {
//    [super updateTipViewWithTipArr:tipArr];
//
//    [self netBadTipView];
//}


//- (void)playTime:(float)currentTime longestTime:(float)longestTime {
//    [super playTime:currentTime longestTime:longestTime];
//    
//    if (_netBadBgView) {
//        
//        [self.netBadBgView removeFromSuperview];
//        _netBadBgView = nil;
//    }
//}


#pragma mark - Setter && Getter
//- (UIView *)netBadBgView {
//    if (!_netBadBgView) {
//        _netBadBgView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.videoShowImgView.TKWidth, 0)];
//        _netBadBgView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.4f];
//    }
//
//    return _netBadBgView;
//}
//
//- (UILabel *)netLabel {
//    if (!_netLabel) {
//        _netLabel = [[UILabel alloc] init];
//        _netLabel.numberOfLines = 0;
//        _netLabel.backgroundColor = [UIColor clearColor];
//        _netLabel.textColor = [TKUIHelper colorWithHexString:@"#FFFFFF"];
//        _netLabel.text = @"检测到您网络状况不佳，请务必确认影像和声音正确后提交";
//        _netLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
//        float labelX = self.netErrorImg.TKRight + 8;
//        float labelWidth = self.videoShowImgView.TKWidth - labelX - 16;
//        CGSize labelSize = [_netLabel sizeThatFits:CGSizeMake(labelWidth, MAXFLOAT)];
//        _netLabel.frame = CGRectMake(labelX, 12, labelWidth, labelSize.height);
//    }
//
//    return _netLabel;
//}


//- (UIImageView *)netErrorImg {
//    if (!_netErrorImg) {
//        _netErrorImg = [[UIImageView alloc] initWithFrame:CGRectMake(16, 14, 20, 20)];
//        _netErrorImg.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_carton_warning.png", TK_OPEN_RESOURCE_NAME]];//设置图片
//    }
//
//    return _netErrorImg;
//}

//- (UIImageView *)coverImageView {
//    if (!_coverImageView) {
//        _coverImageView = [[UIImageView alloc] initWithFrame:self.bounds];
//        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/tk_digtal_open_bg",TK_OPEN_RESOURCE_NAME]];
//        _coverImageView.image = image;
//    }
//    return _coverImageView;
//}

/**
 <AUTHOR>
 @初始化标题文本
 @return 返回按钮
 */
- (UILabel *)titleLabel{
    if (!_titleLabel) {
        
        float width = self.TKWidth;
        float height = 24;
        float x = 0;
        float y = 10 + STATUSBAR_HEIGHT;
        _titleLabel= [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _titleLabel.font =  [UIFont fontWithName:@"PingFang SC" size:16];;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.textColor = [TKUIHelper colorWithHexString:@"#000000" alpha:1];
        _titleLabel.text = @"视频录制";
    }
    return _titleLabel;
}


- (UIView *)hudView {
    if (!_hudView) {
        
//        CGFloat y = self.backBtn.TKBottom + 14;
//        CGRect frame = CGRectMake(10, y, self.TKWidth - 10 * 2, self.TKHeight - y - (IPHONEX_BUTTOM_HEIGHT > 0 ? IPHONEX_BUTTOM_HEIGHT : 10));
        CGRect frame = CGRectMake(0, 0, self.TKWidth, self.TKHeight);
        _hudView = [[UILabel alloc] initWithFrame:frame];
//        _hudView.backgroundColor = [TKUIHelper colorWithHexString:@"#ECF0FE"];
        _hudView.backgroundColor = [TKUIHelper colorWithHexString:@"#ffffff"];
        _hudView.layer.cornerRadius = 13;
        _hudView.clipsToBounds = YES;
    }
    
    return _hudView;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
- (UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnWidth=24;
        float backBtnHeight=24;
        float backBtnX=12.0f;
        float backBtnY=8+STATUSBAR_HEIGHT;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnHeight)];
        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [_backBtn setTintColor:[TKUIHelper colorWithHexString:@"#000000"]];
        [_backBtn setImage:image forState:UIControlStateNormal];
        
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}

/**
 <AUTHOR> 2019年04月13日14:01:51
 @初始化懒加载重新录制按钮
 @return 重新录制按钮
 */
-(UIButton *)resetBtn{
    if (!_resetBtn) {
        float resetBtnX = 29;
        float resetBtnWidth = (self.TKWidth-3*resetBtnX)/2;
        float resetBtnHeight = 44;
        float resetBtnY = self.TKHeight-resetBtnHeight-20-IPHONEX_BUTTOM_HEIGHT;
        _resetBtn=[[UIButton alloc] initWithFrame:CGRectMake(resetBtnX, resetBtnY, resetBtnWidth, resetBtnHeight)];
        
        [_resetBtn setTitle:@"重新录制" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _resetBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _resetBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }

        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [_resetBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]] forState:UIControlStateNormal];
            [_resetBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"] alpha:0.05f]];
        }else{
            [_resetBtn setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
            [_resetBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
        }
        _resetBtn.layer.cornerRadius=resetBtnHeight/2.0f;
 
        [_resetBtn addTarget:self action:@selector(resetAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _resetBtn;
}

/**
 <AUTHOR> 2019年04月13日14:02:47
 @初始化懒加载确认提交按钮
 @return 确认提交按钮
 */
-(UIButton *)submitBtn{
    if (!_submitBtn) {
        float submitBtnX=self.resetBtn.TKLeft*2+self.resetBtn.TKWidth;
        float submitBtnWidth=self.resetBtn.TKWidth;
        float submitBtnHeight=self.resetBtn.TKHeight;
        float submitBtnY=self.resetBtn.TKTop;
        _submitBtn=[[UIButton alloc] initWithFrame:CGRectMake(submitBtnX, submitBtnY, submitBtnWidth, submitBtnHeight)];

        [_submitBtn setTitle:@"确认提交" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        
        [_submitBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        
        [_submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
        
        _submitBtn.layer.cornerRadius=submitBtnHeight/2.0f;
    }
    return _submitBtn;
}



@end
