//
//  TKNewOneWayPlayerControlView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/9/1.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKNewOneWayPlayerControlView.h"
//#import "TKZFSliderView.h"
#import "UIView+TKZFFrame.h"
#import "TKZFUtilities.h"
#import "UIImageView+TKZFCache.h"


@interface TKNewOneWayPlayerControlView()<TKZFSliderViewDelegate>

/// 竖屏控制层的View
@property (nonatomic, strong) TKZFPortraitControlView *portraitControlView;
/// 横屏控制层的View
@property (nonatomic, strong) TKNewOneWayLandScapeControlView *landScapeControlView;
/// 加载loading
@property (nonatomic, strong) TKZFSpeedLoadingView *activity;
/// 快进快退View
@property (nonatomic, strong) UIView *fastView;
///// 快进快退进度progress
//@property (nonatomic, strong) TKZFSliderView *fastProgressView;
/// 快进快退时间
@property (nonatomic, strong) UILabel *fastTimeLabel;
///// 快进快退ImageView
//@property (nonatomic, strong) UIImageView *fastImageView;
/// 加载失败按钮
@property (nonatomic, strong) UIButton *failBtn;
/// 底部播放进度
@property (nonatomic, strong) TKZFSliderView *bottomPgrogress;
/// 是否显示了控制层
@property (nonatomic, assign, getter=isShowing) BOOL showing;
/// 是否播放结束
@property (nonatomic, assign, getter=isPlayEnd) BOOL playeEnd;

@property (nonatomic, assign) BOOL controlViewAppeared;

@property (nonatomic, assign) NSTimeInterval sumTime;

@property (nonatomic, strong) dispatch_block_t afterBlock;

//@property (nonatomic, strong) TKZFSmallFloatControlView *floatControlView;

//@property (nonatomic, strong) TKZFVolumeBrightnessView *volumeBrightnessView;

@property (nonatomic, strong) UIImageView *bgImgView;

@property (nonatomic, strong) UIView *effectView;

@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数

/// 播放或暂停按钮
@property (nonatomic, strong) UIButton *playOrPauseBtn;

@property (nonatomic, strong) UILabel *playTipLabel; //视频播放按钮底部提示文字

@end


@implementation TKNewOneWayPlayerControlView
@synthesize player = _player;

/// 构造方法
/// @param frame frame
/// @param param 参数
- (instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
    self = [super initWithFrame:frame];
    if (self) {
        self.requestParam = param;
        
//        [self viewInit];
        [self addAllSubViews];
        self.landScapeControlView.hidden = YES;
//        self.floatControlView.hidden = YES;
        self.seekToPlay = YES;
        self.effectViewShow = YES;
        self.horizontalPanShowControlView = YES;
        self.autoFadeTimeInterval = 0.25;
        self.autoHiddenTimeInterval = 2.5;
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(volumeChanged:)
                                                     name:@"AVSystemController_SystemVolumeDidChangeNotification"
                                                   object:nil];
        
        [self.playOrPauseBtn addTarget:self action:@selector(playPauseButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    CGFloat min_x = 0;
    CGFloat min_y = 0;
    CGFloat min_w = 0;
    CGFloat min_h = 0;
    CGFloat min_view_w = self.tkzf_width;
    CGFloat min_view_h = self.tkzf_height;

//    self.portraitControlView.frame = self.bounds;
    if (CGRectEqualToRect(self.landScapeControlViewRect, CGRectZero)) {
        self.landScapeControlView.frame = self.bounds;
    } else {
        self.landScapeControlView.frame = self.landScapeControlViewRect;
//        if (!CGRectEqualToRect(self.landScapeControlViewRect, self.bounds)) {
            self.landScapeControlView.center = self.center;
//        }
    }
//    self.floatControlView.frame = self.bounds;
    self.coverImageView.frame = self.bounds;
    self.bgImgView.frame = self.bounds;
    self.effectView.frame = self.bounds;
    
    min_w = 80;
    min_h = 80;
    self.activity.frame = CGRectMake(min_x, min_y, min_w, min_h);
//    self.activity.tkzf_centerX = self.tkzf_centerX;
    self.activity.tkzf_centerX = self.TKWidth * 0.5;
    self.activity.tkzf_centerY = self.tkzf_centerY + 10;
    
    min_w = 44;
    min_h = min_w;
    min_x = (self.TKWidth - min_h) * 0.5;
    min_y = (self.TKHeight - min_h) * 0.5;
    self.playOrPauseBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = min_view_w;
    min_h = 22;
    min_x = 0;
    min_y = self.playOrPauseBtn.TKBottom + 17;
    self.playTipLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 150;
    min_h = 30;
    self.failBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.failBtn.center = self.center;
    
//    min_w = 140;
    min_w = self.TKWidth;
//    min_h = 80;
    min_h = 45;
    self.fastView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastView.center = self.center;
    
//    min_w = 32;
//    min_x = (self.fastView.tkzf_width - min_w) / 2;
//    min_y = 5;
//    min_h = 32;
//    self.fastImageView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = 0;
//    min_y = self.fastImageView.tkzf_bottom + 2;
    min_y = 0;
    min_w = self.fastView.tkzf_width;
//    min_h = 20;
    min_h = self.fastView.TKHeight;
    self.fastTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastTimeLabel.center = CGPointMake(self.fastView.TKWidth * 0.5, self.fastView.TKHeight * 0.5);
    
//    min_x = 12;
//    min_y = self.fastTimeLabel.tkzf_bottom + 5;
//    min_w = self.fastView.tkzf_width - 2 * min_x;
//    min_h = 10;
//    self.fastProgressView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = 0;
    min_y = min_view_h - 1;
    min_w = min_view_w;
    min_h = 1;
    self.bottomPgrogress.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
//    min_x = 0;
//    min_y = iPhoneX ? 54 : 30;
//    min_w = 170;
//    min_h = 35;
//    self.volumeBrightnessView.frame = CGRectMake(min_x, min_y, min_w, min_h);
//    self.volumeBrightnessView.tkzf_centerX = self.tkzf_centerX;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"AVSystemController_SystemVolumeDidChangeNotification" object:nil];
    [self cancelAutoFadeOutControlView];
}

/// 添加所有子控件
- (void)addAllSubViews {
//    [self addSubview:self.floatControlView];
    [self addSubview:self.activity];
    [self addSubview:self.failBtn];
    [self addSubview:self.fastView];
//    [self.fastView addSubview:self.fastImageView];
    [self.fastView addSubview:self.fastTimeLabel];
//    [self.fastView addSubview:self.fastProgressView];
    [self addSubview:self.bottomPgrogress];
//    [self addSubview:self.volumeBrightnessView];
    [self addSubview:self.playOrPauseBtn];
    [self addSubview:self.playTipLabel];
//    [self addSubview:self.portraitControlView];
    [self addSubview:self.landScapeControlView];
}

- (void)playPauseButtonClickAction:(UIButton *)sender {
    [self playOrPause];
}

/// 根据当前播放状态取反
- (void)playOrPause {

    [self.player enterFullScreen:YES animated:NO];
    
    [self.landScapeControlView playBtnSelectedState:YES];
    [self.player.currentPlayerManager play];
}

- (void)autoFadeOutControlView {
    self.controlViewAppeared = YES;
    [self cancelAutoFadeOutControlView];
    @tkzf_weakify(self)
    self.afterBlock = dispatch_block_create(0, ^{
        @tkzf_strongify(self)
        [self hideControlViewWithAnimated:YES];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.autoHiddenTimeInterval * NSEC_PER_SEC)), dispatch_get_main_queue(),self.afterBlock);
}

/// 取消延时隐藏controlView的方法
- (void)cancelAutoFadeOutControlView {
    if (self.afterBlock) {
        dispatch_block_cancel(self.afterBlock);
        self.afterBlock = nil;
    }
}

/// 隐藏控制层
- (void)hideControlViewWithAnimated:(BOOL)animated {
    self.controlViewAppeared = NO;
    if (self.controlViewAppearedCallback) {
        self.controlViewAppearedCallback(NO);
    }
    [UIView animateWithDuration:animated ? self.autoFadeTimeInterval : 0 animations:^{
        if (self.player.isFullScreen) {
            [self.landScapeControlView hideControlView];
        } else {
            if (!self.player.isSmallFloatViewShow) {
//                [self.portraitControlView hideControlView];
            }
        }
    } completion:^(BOOL finished) {
//        self.bottomPgrogress.hidden = NO;
    }];
}

/// 显示控制层
- (void)showControlViewWithAnimated:(BOOL)animated {
    self.controlViewAppeared = YES;
    if (self.controlViewAppearedCallback) {
        self.controlViewAppearedCallback(YES);
    }
    [self autoFadeOutControlView];
    [UIView animateWithDuration:animated ? self.autoFadeTimeInterval : 0 animations:^{
        if (self.player.isFullScreen) {
            [self.landScapeControlView showControlView];
        } else {
            if (!self.player.isSmallFloatViewShow) {
//                [self.portraitControlView showControlView];
            }
        }
    } completion:^(BOOL finished) {
//        self.bottomPgrogress.hidden = YES;
    }];
}

/// 音量改变的通知
- (void)volumeChanged:(NSNotification *)notification {
//    NSDictionary *userInfo = notification.userInfo;
//    NSString *reasonstr = userInfo[@"AVSystemController_AudioVolumeChangeReasonNotificationParameter"];
//    if ([reasonstr isEqualToString:@"ExplicitVolumeChange"]) {
//        float volume = [ userInfo[@"AVSystemController_AudioVolumeNotificationParameter"] floatValue];
//        if (self.player.isFullScreen) {
//            [self.volumeBrightnessView updateProgress:volume withVolumeBrightnessType:TKZFVolumeBrightnessTypeVolume];
//        } else {
//            [self.volumeBrightnessView addSystemVolumeView];
//        }
//    }
}

- (void)updatePlayTipLabel:(NSTimeInterval)currentTime totalTime:(NSTimeInterval)totalTime
{
    int recordTime = (int)(totalTime - currentTime);
    NSDate *d = [NSDate dateWithTimeIntervalSince1970:recordTime];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
    if (recordTime/3600 >= 1) {
        [formatter setDateFormat:@"HH:mm:ss"];
    } else {
          [formatter setDateFormat:@"mm:ss"];
    }
    if (self.player.currentPlayerManager.isPlaying) {
        self.playTipLabel.text=[NSString stringWithFormat:@"点击暂停 %@",[formatter stringFromDate:d]] ;
    }else{
        self.playTipLabel.text=[NSString stringWithFormat:@"点击预览 %@",[formatter stringFromDate:d]] ;
    }
}

#pragma mark - Public Method

/// 重置控制层
- (void)resetControlView {
    self.playOrPauseBtn.hidden = NO;
    self.playTipLabel.hidden = NO;
    [self insertSubview:self.bgImgView belowSubview:self.playOrPauseBtn];
    
//    [self.portraitControlView resetControlView];
    [self.landScapeControlView resetControlView];
    [self cancelAutoFadeOutControlView];
    self.bottomPgrogress.value = 0;
    self.bottomPgrogress.bufferValue = 0;
//    self.floatControlView.hidden = YES;
    self.failBtn.hidden = YES;
//    self.volumeBrightnessView.hidden = YES;
//    self.portraitControlView.hidden = self.player.isFullScreen;
    self.landScapeControlView.hidden = !self.player.isFullScreen;
    if (self.controlViewAppeared) {
        [self showControlViewWithAnimated:NO];
    } else {
        [self hideControlViewWithAnimated:NO];
    }
}

/// 设置标题、封面、全屏模式
- (void)showTitle:(NSString *)title coverURLString:(NSString *)coverUrl fullScreenMode:(TKZFFullScreenMode)fullScreenMode {
    UIImage *placeholder = [TKZFUtilities imageWithColor:[UIColor colorWithRed:220/255.0 green:220/255.0 blue:220/255.0 alpha:1] size:self.bgImgView.bounds.size];
    [self showTitle:title coverURLString:coverUrl placeholderImage:placeholder fullScreenMode:fullScreenMode];
}

/// 设置标题、封面、默认占位图、全屏模式
- (void)showTitle:(NSString *)title coverURLString:(NSString *)coverUrl placeholderImage:(UIImage *)placeholder fullScreenMode:(TKZFFullScreenMode)fullScreenMode {
    [self resetControlView];
    [self layoutIfNeeded];
    [self setNeedsDisplay];
//    [self.portraitControlView showTitle:title fullScreenMode:fullScreenMode];
    [self.landScapeControlView showTitle:title fullScreenMode:fullScreenMode];
    /// 这里直接设置播放器视图里的coverImageView
    [self.player.currentPlayerManager.view.coverImageView setImageWithURLString:coverUrl placeholder:placeholder];
    [self.bgImgView setImageWithURLString:coverUrl placeholder:placeholder];
    if (self.prepareShowControlView) {
        [self showControlViewWithAnimated:NO];
    } else {
        [self hideControlViewWithAnimated:NO];
    }
}

/// 设置标题、UIImage封面、全屏模式
- (void)showTitle:(NSString *)title coverImage:(UIImage *)image fullScreenMode:(TKZFFullScreenMode)fullScreenMode {
    [self resetControlView];
    [self layoutIfNeeded];
    [self setNeedsDisplay];
//    [self.portraitControlView showTitle:title fullScreenMode:fullScreenMode];
    [self.landScapeControlView showTitle:title fullScreenMode:fullScreenMode];
    self.coverImageView.image = image;
    self.bgImgView.image = image;
    if (self.prepareShowControlView) {
        [self showControlViewWithAnimated:NO];
    } else {
        [self hideControlViewWithAnimated:NO];
    }
}

#pragma mark - TKZFPlayerControlViewDelegate

/// 手势筛选，返回NO不响应该手势
- (BOOL)gestureTriggerCondition:(TKZFPlayerGestureControl *)gestureControl gestureType:(TKZFPlayerGestureType)gestureType gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer touch:(nonnull UITouch *)touch {
    
    CGPoint point = [touch locationInView:self];
    if (self.player.isSmallFloatViewShow && !self.player.isFullScreen && gestureType != TKZFPlayerGestureTypeSingleTap) {
        return NO;
    }
    if (self.player.isFullScreen) {
        if (!self.customDisablePanMovingDirection) {
            /// 不禁用滑动方向
            self.player.disablePanMovingDirection = TKZFPlayerDisablePanMovingDirectionNone;
        }
        return [self.landScapeControlView shouldResponseGestureWithPoint:point withGestureType:gestureType touch:touch];
    } else {
        if (!self.customDisablePanMovingDirection) {
            if (self.player.scrollView) {  /// 列表时候禁止上下滑动（防止和列表滑动冲突）
                self.player.disablePanMovingDirection = TKZFPlayerDisablePanMovingDirectionVertical;
            } else { /// 不禁用滑动方向
                self.player.disablePanMovingDirection = TKZFPlayerDisablePanMovingDirectionNone;
            }
        }
//        return [self.portraitControlView shouldResponseGestureWithPoint:point withGestureType:gestureType touch:touch];
        return NO;
    }
}

/// 单击手势事件
- (void)gestureSingleTapped:(TKZFPlayerGestureControl *)gestureControl {
    if (!self.player) return;
    if (self.player.isSmallFloatViewShow && !self.player.isFullScreen) {
        [self.player enterFullScreen:YES animated:YES];
    } else {
//        if (self.player.currentPlayerManager.isPlaying == NO) return;
        if (self.controlViewAppeared) {
            [self hideControlViewWithAnimated:YES];
        } else {
            [self showControlViewWithAnimated:YES];
        }
    }
}

/// 双击手势事件
- (void)gestureDoubleTapped:(TKZFPlayerGestureControl *)gestureControl {
    if (self.player.isFullScreen) {
        [self.landScapeControlView playOrPause];
    } else {
//        [self.portraitControlView playOrPause];
        [self playOrPause];
    }
}

/// 开始滑动手势事件
- (void)gestureBeganPan:(TKZFPlayerGestureControl *)gestureControl panDirection:(TKZFPanDirection)direction panLocation:(TKZFPanLocation)location {
    if (direction == TKZFPanDirectionH) {
        self.sumTime = self.player.currentTime;
    }
}

/// 滑动中手势事件
- (void)gestureChangedPan:(TKZFPlayerGestureControl *)gestureControl panDirection:(TKZFPanDirection)direction panLocation:(TKZFPanLocation)location withVelocity:(CGPoint)velocity {
    if (direction == TKZFPanDirectionH) {
        // 每次滑动需要叠加时间
        self.sumTime += velocity.x / 200;
        // 需要限定sumTime的范围
        NSTimeInterval totalMovieDuration = self.player.totalTime;
        if (totalMovieDuration == 0) return;
        if (self.sumTime > totalMovieDuration) self.sumTime = totalMovieDuration;
        if (self.sumTime < 0) self.sumTime = 0;
        BOOL style = NO;
        if (velocity.x > 0) style = YES;
        if (velocity.x < 0) style = NO;
        if (velocity.x == 0) return;
        [self sliderValueChangingValue:self.sumTime/totalMovieDuration isForward:style];
    }
//    else if (direction == TKZFPanDirectionV) {
//        if (location == TKZFPanLocationLeft) { /// 调节亮度
//            self.player.brightness -= (velocity.y) / 10000;
//            [self.volumeBrightnessView updateProgress:self.player.brightness withVolumeBrightnessType:TKZFVolumeBrightnessTypeumeBrightness];
//        } else if (location == TKZFPanLocationRight) { /// 调节声音
//            self.player.volume -= (velocity.y) / 10000;
//            if (self.player.isFullScreen) {
//                [self.volumeBrightnessView updateProgress:self.player.volume withVolumeBrightnessType:TKZFVolumeBrightnessTypeVolume];
//            }
//        }
//    }
}

/// 滑动结束手势事件
- (void)gestureEndedPan:(TKZFPlayerGestureControl *)gestureControl panDirection:(TKZFPanDirection)direction panLocation:(TKZFPanLocation)location {
    @tkzf_weakify(self)
    if (direction == TKZFPanDirectionH && self.sumTime >= 0 && self.player.totalTime > 0) {
        [self.player seekToTime:self.sumTime completionHandler:^(BOOL finished) {
            if (finished) {
                @tkzf_strongify(self)
                /// 左右滑动调节播放进度
//                [self.portraitControlView sliderChangeEnded];
                [self.landScapeControlView sliderChangeEnded];
                self.bottomPgrogress.isdragging = NO;
                if (self.controlViewAppeared) {
                    [self autoFadeOutControlView];
                }
            }
        }];
        if (self.seekToPlay) {
            [self.player.currentPlayerManager play];
        }
        self.sumTime = 0;
    }
}

/// 捏合手势事件，这里改变了视频的填充模式
- (void)gesturePinched:(TKZFPlayerGestureControl *)gestureControl scale:(float)scale {
    if (scale > 1) {
        self.player.currentPlayerManager.scalingMode = TKZFPlayerScalingModeAspectFill;
    } else {
        self.player.currentPlayerManager.scalingMode = TKZFPlayerScalingModeAspectFit;
    }
}

- (void)longPressed:(TKZFPlayerGestureControl *)gestureControl state:(TKZFLongPressGestureRecognizerState)state {
    
}

/// 准备播放
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer prepareToPlay:(NSURL *)assetURL {
    [self hideControlViewWithAnimated:NO];
}

/// 播放状态改变
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer playStateChanged:(TKZFPlayerPlaybackState)state {
    if (state == TKZFPlayerPlayStatePlaying) {
//        [self.portraitControlView playBtnSelectedState:YES];
        [self.landScapeControlView playBtnSelectedState:YES];
        
        self.failBtn.hidden = YES;
        /// 开始播放时候判断是否显示loading
        if (videoPlayer.currentPlayerManager.loadState == TKZFPlayerLoadStateStalled && !self.prepareShowLoading) {
            [self.activity startAnimating];
        } else if ((videoPlayer.currentPlayerManager.loadState == TKZFPlayerLoadStateStalled || videoPlayer.currentPlayerManager.loadState == TKZFPlayerLoadStatePrepare) && self.prepareShowLoading) {
            [self.activity startAnimating];
        }
    } else if (state == TKZFPlayerPlayStatePaused) {
//        [self.portraitControlView playBtnSelectedState:NO];
        [self.landScapeControlView playBtnSelectedState:NO];
        /// 暂停的时候隐藏loading
        [self.activity stopAnimating];
        self.failBtn.hidden = YES;
    } else if (state == TKZFPlayerPlayStatePlayFailed) {
        self.failBtn.hidden = NO;
        [self bringSubviewToFront:self.failBtn];
        [self.activity stopAnimating];
        [self hideControlViewWithAnimated:NO];
//        self.portraitControlView.bottomToolView.hidden = YES;   // 进度条也要隐藏
        self.userInteractionEnabled = YES;
        self.playOrPauseBtn.hidden = YES;
        self.playTipLabel.hidden = YES;
        self.bgImgView.hidden = YES;
    }
}

/// 加载状态改变
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer loadStateChanged:(TKZFPlayerLoadState)state {
    if (state == TKZFPlayerLoadStatePrepare) {
        self.coverImageView.hidden = NO;
//        [self.portraitControlView playBtnSelectedState:videoPlayer.currentPlayerManager.shouldAutoPlay];
        [self.landScapeControlView playBtnSelectedState:videoPlayer.currentPlayerManager.shouldAutoPlay];
        self.userInteractionEnabled = NO;
    } else if (state == TKZFPlayerLoadStatePlaythroughOK || state == TKZFPlayerLoadStatePlayable) {
//        [self.portraitControlView updateVideoFragmentProgress];
        [self.landScapeControlView updateVideoFragmentProgress];
        
        self.coverImageView.hidden = YES;
        if (self.effectViewShow) {
            self.effectView.hidden = NO;
        } else {
            self.effectView.hidden = YES;
            self.player.currentPlayerManager.view.backgroundColor = [UIColor blackColor];
        }
        [self showControlViewWithAnimated:NO];
//        self.portraitControlView.bottomToolView.hidden = NO;
        self.userInteractionEnabled = YES;
    }
    if (state == TKZFPlayerLoadStateStalled && videoPlayer.currentPlayerManager.isPlaying && !self.prepareShowLoading) {
        [self.activity startAnimating];
    } else if ((state == TKZFPlayerLoadStateStalled || state == TKZFPlayerLoadStatePrepare) && videoPlayer.currentPlayerManager.isPlaying && self.prepareShowLoading) {
        [self.activity startAnimating];
    } else {
        [self.activity stopAnimating];
    }
}

/// 播放进度改变回调
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer currentTime:(NSTimeInterval)currentTime totalTime:(NSTimeInterval)totalTime {
//    [self.portraitControlView videoPlayer:videoPlayer currentTime:currentTime totalTime:totalTime];
    [self.landScapeControlView videoPlayer:videoPlayer currentTime:currentTime totalTime:totalTime];
    if (!self.bottomPgrogress.isdragging) {
        self.bottomPgrogress.value = videoPlayer.progress;
    }
    
    [self updatePlayTipLabel:currentTime totalTime:totalTime];
}


/// 缓冲改变回调
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer bufferTime:(NSTimeInterval)bufferTime {
//    [self.portraitControlView videoPlayer:videoPlayer bufferTime:bufferTime];
    [self.landScapeControlView videoPlayer:videoPlayer bufferTime:bufferTime];
    self.bottomPgrogress.bufferValue = videoPlayer.bufferProgress;
}

- (void)videoPlayer:(TKZFPlayerController *)videoPlayer presentationSizeChanged:(CGSize)size {
    [self.landScapeControlView videoPlayer:videoPlayer presentationSizeChanged:size];
}

/// 视频view即将旋转
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer orientationWillChange:(TKZFOrientationObserver *)observer {
    
    if (observer.isFullScreen) {
        [self.player.currentPlayerManager.view insertSubview:self.bgImgView atIndex:0];
        self.effectView.hidden = NO;
        self.playOrPauseBtn.hidden = YES;
        self.playTipLabel.hidden = YES;
    } else {
        [self insertSubview:self.bgImgView belowSubview:self.playOrPauseBtn];
        self.effectView.hidden = YES;
        self.playOrPauseBtn.hidden = NO;
        self.playTipLabel.hidden = NO;
        [self.player.currentPlayerManager pause];
    }
    
//    self.portraitControlView.hidden = observer.isFullScreen;
    self.landScapeControlView.hidden = !observer.isFullScreen;
    if (videoPlayer.isSmallFloatViewShow) {
//        self.floatControlView.hidden = observer.isFullScreen;
//        self.portraitControlView.hidden = YES;
        if (observer.isFullScreen) {
            self.controlViewAppeared = NO;
            [self cancelAutoFadeOutControlView];
        }
    }
    if (self.controlViewAppeared) {
        [self showControlViewWithAnimated:NO];
    } else {
        [self hideControlViewWithAnimated:NO];
    }
    
//    if (observer.isFullScreen) {
//        [self.volumeBrightnessView removeSystemVolumeView];
//    } else {
//        [self.volumeBrightnessView addSystemVolumeView];
//    }
    [self.landScapeControlView videoPlayer:videoPlayer orientationWillChange:observer];
}

/// 锁定旋转方向
- (void)lockedVideoPlayer:(TKZFPlayerController *)videoPlayer lockedScreen:(BOOL)locked {
    [self showControlViewWithAnimated:YES];
}

///// 列表滑动时视频view已经显示
//- (void)playerDidAppearInScrollView:(TKZFPlayerController *)videoPlayer {
//    if (!self.player.stopWhileNotVisible && !videoPlayer.isFullScreen) {
////        self.floatControlView.hidden = YES;
////        self.portraitControlView.hidden = NO;
//    }
//}

/// 列表滑动时视频view已经消失
//- (void)playerDidDisappearInScrollView:(TKZFPlayerController *)videoPlayer {
//    if (!self.player.stopWhileNotVisible && !videoPlayer.isFullScreen) {
////        self.floatControlView.hidden = NO;
////        self.portraitControlView.hidden = YES;
//    }
//}
//
//- (void)videoPlayer:(TKZFPlayerController *)videoPlayer floatViewShow:(BOOL)show {
////    self.floatControlView.hidden = !show;
////    self.portraitControlView.hidden = show;
//}

#pragma mark - Private Method

- (void)sliderValueChangingValue:(CGFloat)value isForward:(BOOL)forward {
    if (self.horizontalPanShowControlView) {
        /// 显示控制层
        [self showControlViewWithAnimated:NO];
        [self cancelAutoFadeOutControlView];
        
    }
    
//    self.fastProgressView.value = value;
    
//    self.portraitControlView.playOrPauseBtn.hidden = YES;
    self.landScapeControlView.playOrPauseBtn.hidden = YES;
    
//    if (forward) {
//        self.fastImageView.image = TKZFPlayer_Image(@"ZFPlayer_fast_forward");
//    } else {
//        self.fastImageView.image = TKZFPlayer_Image(@"ZFPlayer_fast_backward");
//    }
    
    NSString *draggedTime = [TKZFUtilities convertTimeSecond:self.player.totalTime*value];
    NSString *totalTime = [TKZFUtilities convertTimeSecond:self.player.totalTime];
//    self.fastTimeLabel.text = [NSString stringWithFormat:@"%@ / %@",draggedTime,totalTime];
    self.fastTimeLabel.attributedText = [self convertTextToHtmlString:draggedTime totalTime:totalTime draggedTimeColor:@"#387CFF" totalTimeColor:@"#ffffff"];
    [self.fastTimeLabel sizeToFit];
    self.fastTimeLabel.center = CGPointMake(self.fastView.TKWidth * 0.5, self.fastView.TKHeight * 0.5);
    
    self.fastView.hidden = NO;
    self.fastView.alpha = 1;
    
    /// 更新滑杆
//    [self.portraitControlView sliderValueChanged:value currentTimeString:draggedTime];
    [self.landScapeControlView sliderValueChanged:value currentTimeString:draggedTime];
    self.bottomPgrogress.isdragging = YES;
    self.bottomPgrogress.value = value;

    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(hideFastView) object:nil];
    [self performSelector:@selector(hideFastView) withObject:nil afterDelay:0.1];
    
    if (self.fastViewAnimated) {
        [UIView animateWithDuration:0.4 animations:^{
            self.fastView.transform = CGAffineTransformMakeTranslation(forward?8:-8, 0);
        }];
    }
}

- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)draggedTime totalTime:(NSString *)totalTime draggedTimeColor:(NSString *)draggedTimeColor totalTimeColor:(NSString *)totalTimeColor
{
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC;\">%@<span style=\"color:%@;\">/%@</span></span>", (int)self.fastTimeLabel.font.pointSize, draggedTimeColor, draggedTime, totalTimeColor, totalTime];
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

/// 隐藏快进视图
- (void)hideFastView {
    [UIView animateWithDuration:0.4 animations:^{
        self.fastView.transform = CGAffineTransformIdentity;
        self.fastView.alpha = 0;
    } completion:^(BOOL finished) {
        self.fastView.hidden = YES;
        
//        self.portraitControlView.playOrPauseBtn.hidden = NO;
        self.landScapeControlView.playOrPauseBtn.hidden = NO;
    }];
}

/// 加载失败
- (void)failBtnClick:(UIButton *)sender {
    [self.player.currentPlayerManager reloadPlayer];
}

#pragma mark - setter

- (void)setPlayer:(TKZFPlayerController *)player {
    _player = player;
    self.landScapeControlView.player = player;
//    self.portraitControlView.player = player;
    /// 解决播放时候黑屏闪一下问题
    [player.currentPlayerManager.view insertSubview:self.bgImgView atIndex:0];
    [self.bgImgView addSubview:self.effectView];
    self.bgImgView.frame = player.currentPlayerManager.view.bounds;
    self.bgImgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    self.effectView.frame = self.bgImgView.bounds;
    self.effectView.hidden = !self.effectViewShow;
}

- (void)setSeekToPlay:(BOOL)seekToPlay {
    _seekToPlay = seekToPlay;
//    self.portraitControlView.seekToPlay = seekToPlay;
    self.landScapeControlView.seekToPlay = seekToPlay;
}

- (void)setEffectViewShow:(BOOL)effectViewShow {
    _effectViewShow = effectViewShow;
//    if (effectViewShow) {
//        self.bgImgView.hidden = NO;
//    } else {
//        self.bgImgView.hidden = YES;
//    }
}

- (void)setFullScreenMode:(TKZFFullScreenMode)fullScreenMode {
    _fullScreenMode = fullScreenMode;
//    self.portraitControlView.fullScreenMode = fullScreenMode;
    self.landScapeControlView.fullScreenMode = fullScreenMode;
    self.player.orientationObserver.fullScreenMode = fullScreenMode;
}

- (void)setShowCustomStatusBar:(BOOL)showCustomStatusBar {
    _showCustomStatusBar = showCustomStatusBar;
    self.landScapeControlView.showCustomStatusBar = showCustomStatusBar;
}

#pragma mark - getter

- (UIImageView *)bgImgView {
    if (!_bgImgView) {
        _bgImgView = [[UIImageView alloc] init];
        _bgImgView.userInteractionEnabled = YES;
    }
    return _bgImgView;
}

- (UIView *)effectView {
    if (!_effectView) {
//        if (@available(iOS 8.0, *)) {
//            UIBlurEffect *effect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
//            _effectView = [[UIVisualEffectView alloc] initWithEffect:effect];
//        } else {
//            UIToolbar *effectView = [[UIToolbar alloc] init];
//            effectView.barStyle = UIBarStyleBlackTranslucent;
//            _effectView = effectView;
//        }
        _effectView = [[UIView alloc] init];
        _effectView.backgroundColor = UIColor.blackColor;
    }
    return _effectView;
}

- (TKZFPortraitControlView *)portraitControlView {
//    if (!_portraitControlView) {
//        @tkzf_weakify(self)
//        _portraitControlView = [[TKZFPortraitControlView alloc] init];
////        _portraitControlView = [[TKZFPortraitControlView alloc] initWithFrame:self.bounds withParam:self.requestParam];
//        _portraitControlView.sliderValueChanging = ^(CGFloat value, BOOL forward) {
//            @tkzf_strongify(self)
//            NSString *draggedTime = [TKZFUtilities convertTimeSecond:self.player.totalTime*value];
//            /// 更新滑杆和时间
//            [self.landScapeControlView sliderValueChanged:value currentTimeString:draggedTime];
////            self.fastProgressView.value = value;
//            self.bottomPgrogress.isdragging = YES;
//            self.bottomPgrogress.value = value;
//            [self cancelAutoFadeOutControlView];
//        };
//        _portraitControlView.sliderValueChanged = ^(CGFloat value) {
//            @tkzf_strongify(self)
//            [self.landScapeControlView sliderChangeEnded];
////            self.fastProgressView.value = value;
//            self.bottomPgrogress.isdragging = NO;
//            self.bottomPgrogress.value = value;
//            [self autoFadeOutControlView];
//        };
//    }
//    return _portraitControlView;
    return nil;
}

- (TKNewOneWayLandScapeControlView *)landScapeControlView {
    if (!_landScapeControlView) {
        @tkzf_weakify(self)
        _landScapeControlView = [[TKNewOneWayLandScapeControlView alloc] init];
        _landScapeControlView.sliderValueChanging = ^(CGFloat value, BOOL forward) {
            @tkzf_strongify(self)
//            NSString *draggedTime = [TKZFUtilities convertTimeSecond:self.player.totalTime*value];
            /// 更新滑杆和时间
//            [self.portraitControlView sliderValueChanged:value currentTimeString:draggedTime];
//            self.fastProgressView.value = value;
            self.bottomPgrogress.isdragging = YES;
            self.bottomPgrogress.value = value;
            [self cancelAutoFadeOutControlView];
        };
        _landScapeControlView.sliderValueChanged = ^(CGFloat value) {
            @tkzf_strongify(self)
//            [self.portraitControlView sliderChangeEnded];
//            self.fastProgressView.value = value;
            self.bottomPgrogress.isdragging = NO;
            self.bottomPgrogress.value = value;
            [self autoFadeOutControlView];
        };
    }
    return _landScapeControlView;
}

- (TKZFSpeedLoadingView *)activity {
    if (!_activity) {
        _activity = [[TKZFSpeedLoadingView alloc] init];
    }
    return _activity;
}

- (UIView *)fastView {
    if (!_fastView) {
        _fastView = [[UIView alloc] init];
//        _fastView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.7];
//        _fastView.layer.cornerRadius = 4;
//        _fastView.layer.masksToBounds = YES;
        _fastView.hidden = YES;
    }
    return _fastView;
}

//- (UIImageView *)fastImageView {
//    if (!_fastImageView) {
//        _fastImageView = [[UIImageView alloc] init];
//    }
//    return _fastImageView;
//}

- (UILabel *)fastTimeLabel {
    if (!_fastTimeLabel) {
        _fastTimeLabel = [[UILabel alloc] init];
        _fastTimeLabel.textColor = [UIColor whiteColor];
        _fastTimeLabel.textAlignment = NSTextAlignmentCenter;
//        _fastTimeLabel.font = [UIFont systemFontOfSize:14.0];
        _fastTimeLabel.font = [UIFont systemFontOfSize:32.0];
//        _fastTimeLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _fastTimeLabel;
}

//- (TKZFSliderView *)fastProgressView {
//    if (!_fastProgressView) {
//        _fastProgressView = [[TKZFSliderView alloc] init];
//        _fastProgressView.maximumTrackTintColor = [[UIColor lightGrayColor] colorWithAlphaComponent:0.4];
//        _fastProgressView.minimumTrackTintColor = [UIColor whiteColor];
//        _fastProgressView.sliderHeight = 2;
//        _fastProgressView.isHideSliderBlock = NO;
//    }
//    return _fastProgressView;
//}

- (UIButton *)failBtn {
    if (!_failBtn) {
        _failBtn = [UIButton buttonWithType:UIButtonTypeSystem];
        [_failBtn setTitle:@"加载失败,点击重试" forState:UIControlStateNormal];
        [_failBtn addTarget:self action:@selector(failBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        [_failBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _failBtn.titleLabel.font = [UIFont systemFontOfSize:14.0];
        _failBtn.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.7];
        _failBtn.hidden = YES;
    }
    return _failBtn;
}

- (TKZFSliderView *)bottomPgrogress {
    if (!_bottomPgrogress) {
        _bottomPgrogress = [[TKZFSliderView alloc] init];
        _bottomPgrogress.maximumTrackTintColor = [UIColor clearColor];
        _bottomPgrogress.minimumTrackTintColor = [UIColor whiteColor];
        _bottomPgrogress.bufferTrackTintColor  = [UIColor colorWithRed:1 green:1 blue:1 alpha:0.5];
        _bottomPgrogress.sliderHeight = 1;
        _bottomPgrogress.isHideSliderBlock = NO;
        _bottomPgrogress.hidden = YES;  // 默认隐藏
    }
    return _bottomPgrogress;
}

//- (TKZFSmallFloatControlView *)floatControlView {
//    if (!_floatControlView) {
//        _floatControlView = [[TKZFSmallFloatControlView alloc] init];
//        @tkzf_weakify(self)
//        _floatControlView.closeClickCallback = ^{
//            @tkzf_strongify(self)
//            if (self.player.containerType == TKZFPlayerContainerTypeCell) {
//                [self.player stopCurrentPlayingCell];
//            } else if (self.player.containerType == TKZFPlayerContainerTypeView) {
//                [self.player stopCurrentPlayingView];
//            }
//            [self resetControlView];
//        };
//    }
//    return _floatControlView;
//}

//- (TKZFVolumeBrightnessView *)volumeBrightnessView {
//    if (!_volumeBrightnessView) {
//        _volumeBrightnessView = [[TKZFVolumeBrightnessView alloc] init];
//        _volumeBrightnessView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.7];
//        _volumeBrightnessView.hidden = YES;
//    }
//    return _volumeBrightnessView;
//}

- (void)setBackBtnClickCallback:(void (^)(void))backBtnClickCallback {
    _backBtnClickCallback = [backBtnClickCallback copy];
    self.landScapeControlView.backBtnClickCallback = _backBtnClickCallback;
}


- (void)setNeedShowForwardView:(BOOL)needShowForwardView {
    _needShowForwardView = needShowForwardView;
//    self.portraitControlView.needShowForwardView = needShowForwardView;
    self.landScapeControlView.needShowForwardView = needShowForwardView;
}

- (UIButton *)playOrPauseBtn {
    if (!_playOrPauseBtn) {
        _playOrPauseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_playOrPauseBtn setFrameY:(self.TKHeight - 44) / 2.0f];
        [_playOrPauseBtn setFrameX:(self.TKWidth - 44) / 2.0f];
        [_playOrPauseBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play1.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_playOrPauseBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_stop1.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateSelected];
    }
    return _playOrPauseBtn;
}

- (UILabel *)playTipLabel{
    if (!_playTipLabel) {
        _playTipLabel=[[UILabel alloc] init];
        _playTipLabel.text = @"点击预览";
        _playTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        _playTipLabel.textColor = [UIColor colorWithWhite:1 alpha:1];
        _playTipLabel.textAlignment=NSTextAlignmentCenter;
        _playTipLabel.layer.zPosition = MAXFLOAT;//总是保持在最上面避免被播放视频的view挡住
    }
    return _playTipLabel;
}

@end
