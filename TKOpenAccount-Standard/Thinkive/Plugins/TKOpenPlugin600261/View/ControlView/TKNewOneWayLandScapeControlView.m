//
//  TKNewOneWayLandScapeControlView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/9/19.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKNewOneWayLandScapeControlView.h"
#import "UIView+TKZFFrame.h"

@interface TKNewOneWayLandScapeControlView()

@property (nonatomic, readwrite, strong) UILabel *timeSeptateLabel;


@end

@implementation TKNewOneWayLandScapeControlView
@synthesize playOrPauseBtn = _playOrPauseBtn;
@synthesize backBtn = _backBtn;
@synthesize bottomToolView = _bottomToolView;

#pragma mark - Init && Dealloc
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        [self.bottomToolView addSubview:self.timeSeptateLabel];
        [self insertSubview:self.playOrPauseBtn aboveSubview:self.bottomToolView];
    }
    return self;
}

#pragma mark - Selector
- (void)layoutSubviews {
    [super layoutSubviews];
    
    CGFloat min_x = 0;
    CGFloat min_y = 0;
    CGFloat min_w = 0;
    CGFloat min_h = 0;
    CGFloat min_view_w = self.bounds.size.width;
    CGFloat min_view_h = self.bounds.size.height;
    
    CGFloat min_margin = 9;
    
    min_x = 0;
    min_y = 0;
    min_w = min_view_w;
    min_h = ISIPHONEX ? 110 : 80;
    self.topToolView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = 0;
    min_y = 0;
    min_w = min_view_w;
    min_h = 20;
    self.statusBarView.frame = CGRectMake(min_x, min_y, min_w, min_h);

//    min_x = (ISIPHONEX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 44: 15;
    min_x = self.isLandscapeRecordPreview ? 53 : 9;
    if (@available(iOS 13.0, *)) {
        if (self.showCustomStatusBar) {
            min_y = self.statusBarView.TKBottom;
        } else {
            min_y = UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation) ? 10 : (ISIPHONEX ? 40 : 20);
        }
    } else {
        min_y = (ISIPHONEX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 10: (ISIPHONEX ? 40 : 20);
    }
    min_w = 32;
    min_h = 32;
    self.backBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.backBtn.layer.cornerRadius = min_h * 0.5;
    
//    min_x = self.backBtn.tkzf_right + 5;
//    min_y = 0;
//    min_w = min_view_w - min_x - 15 ;
//    min_h = 30;
//    self.titleLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
//    self.titleLabel.tkzf_centerY = self.backBtn.tkzf_centerY;
    
//    min_w = 158;
//    min_h = 32;
//    min_x = self.topToolView.TKRight - min_w - (self.isLandscapeRecordPreview ? 53 : 9);
//    min_y = 0;
//    self.selectFragmentBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
//    self.selectFragmentBtn.tkzf_centerY = self.backBtn.tkzf_centerY;
    
    min_w = 32 + self.playOrPauseBtn.contentEdgeInsets.top + self.playOrPauseBtn.contentEdgeInsets.bottom;
    min_h = 32 + self.playOrPauseBtn.contentEdgeInsets.left + self.playOrPauseBtn.contentEdgeInsets.right;
    min_x = self.backBtn.TKLeft - self.playOrPauseBtn.contentEdgeInsets.right;
    min_y = self.TKHeight - min_h - 10 + self.playOrPauseBtn.contentEdgeInsets.bottom;
    self.playOrPauseBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_h = ISIPHONEX ? 100 : 73;
    min_x = 0;
    min_y = min_view_h - min_h;
    min_w = min_view_w;
    self.bottomToolView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 40;
    min_h = 20;
    min_x = self.playOrPauseBtn.TKRight + 6 - self.playOrPauseBtn.contentEdgeInsets.right;
    min_y = self.bottomToolView.TKHeight - min_h - 16;
    self.currentTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 5;
    min_h = self.currentTimeLabel.TKHeight;
    min_x = self.currentTimeLabel.TKRight - 1;
    min_y = self.currentTimeLabel.TKTop;
    self.timeSeptateLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = self.currentTimeLabel.TKWidth;
    min_x = self.timeSeptateLabel.TKRight + 1;
    min_y = self.currentTimeLabel.TKTop;
    min_h = self.currentTimeLabel.TKHeight;
    self.totalTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = self.totalTimeLabel.TKRight + 10;
    min_y = 0;
    min_w = min_view_w - 20 - min_x;
    min_h = 12;
    CGRect sliderRect = CGRectMake(min_x, min_y, min_w, min_h);
    self.slider.frame = sliderRect;
    self.slider.tkzf_centerY = self.currentTimeLabel.tkzf_centerY;
    
    min_w = 32;
    min_h = 20;
    min_x = min_view_w - 20 - min_w;
    min_y = self.slider.TKTop - 4 - min_h;
    self.rateBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 24;
    min_h = min_w;
    min_x = self.rateBtn.TKLeft - min_w - 20;
    min_y = 0;
    self.fastForwardBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastForwardBtn.tkzf_centerY = self.rateBtn.tkzf_centerY;
    
    min_w = 24;
    min_h = min_w;
    min_x = self.fastForwardBtn.TKLeft - min_w - 20;
    min_y = 0;
    self.fastBackwardBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastBackwardBtn.tkzf_centerY = self.rateBtn.tkzf_centerY;
}

- (void)showControlView {
    [super showControlView];
    
    self.bottomToolView.alpha = 1;
}

- (void)hideControlView {
    
    [super hideControlView];
    
    self.bottomToolView.alpha = 0;
}

- (BOOL)shouldResponseGestureWithPoint:(CGPoint)point withGestureType:(TKZFPlayerGestureType)type touch:(nonnull UITouch *)touch {
    
    CGRect playOrPauseRect = [self convertRect:self.playOrPauseBtn.frame toView:self];
    if (CGRectContainsPoint(playOrPauseRect, point)) {
        return NO;
    }

    return [super shouldResponseGestureWithPoint:point withGestureType:type touch:touch];
}

#pragma mark - Setter && Getter
- (UIButton *)playOrPauseBtn {
    
    if (!_playOrPauseBtn) {
        _playOrPauseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_playOrPauseBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play1.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_playOrPauseBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_stop1.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateSelected];
        // 增大上方的可点击区域
        _playOrPauseBtn.contentEdgeInsets = UIEdgeInsetsMake(20, 20, 20, 20);
    }
    return _playOrPauseBtn;
}

- (UIButton *)backBtn {
    if (!_backBtn) {
        _backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_full_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        _backBtn.backgroundColor = [TKUIHelper colorWithHexString:@"#DDDDDD40"];
        _backBtn.layer.masksToBounds = YES;
    }
    return _backBtn;
}

- (UILabel *)timeSeptateLabel {
    if (!_timeSeptateLabel) {
        _timeSeptateLabel = [[UILabel alloc] init];
        _timeSeptateLabel.textColor = [UIColor whiteColor];
        _timeSeptateLabel.font = [UIFont systemFontOfSize:14.0f];
        _timeSeptateLabel.textAlignment = NSTextAlignmentCenter;
        _timeSeptateLabel.text = @"/";
    }
    return _timeSeptateLabel;
}

- (UIView *)bottomToolView {
    if (!_bottomToolView) {
        _bottomToolView = [[UIView alloc] init];
        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_bottom_shadow.png", TK_OPEN_RESOURCE_NAME]];
        _bottomToolView.layer.contents = (id)image.CGImage;
    }
    return _bottomToolView;
}

@end
