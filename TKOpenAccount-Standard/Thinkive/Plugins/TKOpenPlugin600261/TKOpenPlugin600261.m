//
//  TKOpenPlugin600261.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/8/8.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKOpenPlugin600261.h"
#import "TKVideoAlertView.h"
#import "TKNewOneWayVideoViewController.h"
#import <MediaPlayer/MediaPlayer.h>

@interface TKOpenPlugin600261 ()<TKNewOneWayVideoViewControllerDelegate,TKVideoAlertViewDelegate>
@property (nonatomic, strong) NSMutableDictionary *requestParam;//h5带过来的参数

@property (nonatomic, readwrite, assign) BOOL isInvokeing; // 是否正在被调用
@property (nonatomic, strong) TKVideoAlertView *videoAlertView;//视频挂断提示框

@end

@implementation TKOpenPlugin600261

- (ResultVo *)serverInvoke:(id)param{
    
    ResultVo *resultVo = [[ResultVo alloc]init];

    NSDictionary *requestHeaders = (NSDictionary *)[param getObjectWithKey:@"requestHeaders"];
    if ([requestHeaders isKindOfClass:NSString.class]) requestHeaders = [TKDataHelper jsonToDictionary:(NSString *)requestHeaders];
    param[@"requestHeaders"] = requestHeaders;
    
    param[@"enableHeadphone"] = @"1";   // 支持蓝牙耳机录制
    self.requestParam = param;
    
//    TKLogInfo(@"静音检查，准备走流程打开视频录制页面");
    
    NSArray *questionJson = (NSArray *)[param getObjectWithKey:@"questionJson"];
    NSArray *questionArray = nil;
    if ([[param getObjectWithKey:@"questionArray"] isKindOfClass:NSArray.class]) {
        questionArray = (NSArray *)[param getObjectWithKey:@"questionArray"];
    } else {
        questionArray = [TKDataHelper jsonToArray:[param getStringWithKey:@"questionArray"]];
    }
    
    BOOL questionJsonIsNotEmpty = [questionJson isKindOfClass:NSArray.class] && questionJson.count > 0;
    BOOL questionArrayIsNotEmpty = [questionArray isKindOfClass:NSArray.class] && questionArray.count > 0;
    
    // 非空判断
    if (!questionJsonIsNotEmpty && !questionArrayIsNotEmpty) {
        resultVo.errorNo = -60026107;
        resultVo.errorInfo = @"问题对象json数组|questionArray不能为空";
        return resultVo;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //是否在调用插件前展示介绍页面
        if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
            [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Camera),@(TKAuthorizationType_Audio)] authCallBacks:nil btnCallBack:^{
                [self.currentViewCtrl tkIsMicrophonePermissions:^{
                                        
                    [self.currentViewCtrl tkIsCameraPermissions:^{
                        [self handleAuthorized:param];
                    }];
                                                
                }];
            }];
        }else{
            [self.currentViewCtrl tkIsMicrophonePermissions:^{
                                    
                [self.currentViewCtrl tkIsCameraPermissions:^{
                    [self handleAuthorized:param];
                }];
                                            
            }];

        }

    });
    
     return resultVo;
}


- (void)handleAuthorized:(id)param
{
    // 已在调用，拦截重复调用
    if (self.isInvokeing == YES) {
        TKLogInfo(@"拦截重复调用");
        return ;
    };
    
    // 标记正在调用
    self.isInvokeing = YES;
    
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [[AVAudioSession sharedInstance] setActive:YES error:nil];

    CGFloat volume = audioSession.outputVolume;
    //默认音量调整支持h5控制
    float defaultVolume=TKSmartOpenVolume;
    if (self.requestParam[@"defaultVolume"]) {
        defaultVolume=[self.requestParam[@"defaultVolume"] intValue]/100.00f;
    }
    if ((volume+0.05)<defaultVolume) {
        //直接调整音量api还能使用，先改音量不提示用户
        MPMusicPlayerController *mp=[MPMusicPlayerController applicationMusicPlayer];
        mp.volume=defaultVolume;//0为最小1为最大
    }
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    TKNewOneWayVideoViewController *oneWayCtr=[[TKNewOneWayVideoViewController alloc] initWithParam:self.requestParam];
    oneWayCtr.delegate=self;
    
    if ([self.requestParam[@"isPush"] intValue]==1) {
        oneWayCtr.isHideSystemNavigationBar=@"1";
        [self.currentViewCtrl.navigationController pushViewController:oneWayCtr animated:YES];
    }else{
        [self.currentViewCtrl presentViewController:oneWayCtr animated:YES completion:nil];
    }
}

//结果信息
- (void)tkNewSmartOneWayVideoDidComplete:(NSMutableDictionary *)result{
    [self iosCallJsWithParam:result];
    
    // 重置标志位
    self.isInvokeing = NO;
    TKLogInfo(@"重置标志位");
}

#pragma mark lazyloading
/**
 <AUTHOR> 2022年05月25日09:43:41
 @初始化懒加载videoAlertView
 @return videoAlertView
 */
- (TKVideoAlertView *)videoAlertView{
    if (!_videoAlertView) {
        _videoAlertView=[[TKVideoAlertView alloc] initWithFrame:self.currentViewCtrl.view.frame requestParams:self.requestParam];
        _videoAlertView.delegate=self;
    }
    return _videoAlertView;
}

#pragma mark TKVideoAlertViewDelegate


//取消按钮事件
- (void)cancelVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}

//继续按钮事件
- (void)takeVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}

//独立按钮事件
- (void)onlyVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}
@end
