//
//  TKNewOneWayVideoViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/8/8.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKNewOneWayVideoViewController.h"
#import "TKNewOneWayVideoEndView.h"
#import "TKNewOneWayVideoView.h"
#import "TKVideoRecordManager.h"
#import "TKChatLocalVideoRecordManager.h"
#import "TKNewOneWayPlayerControlView.h"
#import "TKZFPortraitViewController.h"

@interface TKNewOneWayVideoViewController ()

@property (nonatomic, readwrite, strong) TKNewOneWayPlayerControlView *_Nonnull playerControlView;    //播放视频工具视图
@property (nonatomic, readwrite, strong) UIImage *_Nonnull videoFirstImage;    // 录制视频首帧图片
@property (nonatomic, readwrite, strong) TKChatLocalVideoRecordManager * _Nonnull recordManager; // 录制管理者
@property (nonatomic, readwrite, strong) NSTimer *recogniceTimeoutTimer; // 识别等待定时器

@end

@implementation TKNewOneWayVideoViewController

@synthesize faceDetectManager = _faceDetectManager;
@synthesize playerControlView = _playerControlView;
@synthesize playerController = _playerController;
@synthesize recordManager = _recordManager;

//faceDetectManager

- (void)viewDidLoad {
    
    self.requestParam[@"startAutoRecordTime"] = @"3";
    
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.statusBarStyle = TKUIStatusBarStyleDefault;
}



#pragma mark - Selector
- (void)stopRecordingAction {

    [super stopRecordingAction];
    
}

/**
 <AUTHOR> 2019年04月08日19:31:25
 @重新开始单向视频流程
 */
- (void)restartOneVideo {
    
//    if ([TKCommonUtil isHeadsetPluggedIn]) {
//        [self showAlertView:@"提示" message:@"您当前在使用耳机，请移除耳机后重试" tag:7001];
//        return;
//    }
    
    [super restartOneVideo];
    
//    self.isEndOneWayVideo = NO;
    
    self.statusBarStyle = TKUIStatusBarStyleDefault;
    
    self.videoFirstImage = nil;
}


- (NSString *)pluginCallBackfuncNo {
    
    return @"60027";
}


- (void)sendCallBack:(NSMutableDictionary *)callJsParam {
    if (self.delegate && [self.delegate respondsToSelector:@selector(tkNewSmartOneWayVideoDidComplete:)]) {
       [self.delegate tkNewSmartOneWayVideoDidComplete:callJsParam];
    }else{
        [super sendCallBack:callJsParam];
    }
}

//- (void)animateToResultPage
//{
//    [self.tkOneEndView setFrameX:self.view.frame.size.width];
//    [self.view addSubview:self.tkOneEndView];
//
//    [UIView animateWithDuration:0.3f animations:^{
//        [self.tkOneEndView setFrameX:self.view.frame.origin.x];
//    } completion:^(BOOL finished) {
//        self.statusBarStyle = TKUIStatusBarStyleDefault;
//
//        self.tkOneEndView.videoShowImg = self.videoFirstImage;
//        [self.playerControlView showTitle:@"" coverImage:self.tkOneEndView.videoShowImg fullScreenMode:TKZFFullScreenModePortrait];
//    }];
//
//    //    if (self.isLandscape) {
//    //        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
//    //
//    //        self.tkOneEndView.transform = transform;
//    //        self.tkOneEndView.frame = CGRectMake(0, 0, self.tkOneEndView.TKWidth, self.tkOneEndView.TKHeight);
//    //
//    ////        self.tkOneEndView.frame = CGRectMake(self.view.TKLeft, self.view.TKTop, MAX(UISCREEN_WIDTH, UISCREEN_HEIGHT), MIN(UISCREEN_WIDTH, UISCREEN_HEIGHT));
//    //    }
//}

- (void)previewVideo
{
    // 这里有个坑，需要先updateTipViewWithTipArr再加载url。因为结果页播放图层提前加载，updateTipViewWithTipArr会移除掉播放图层
    // 视频录制结束才抓取url图片
//        self.player.contentView = self.tkOneEndView.videoShowImgView; // 每次重新录制都是新创建view.因此要重新设置
    
    if (self.isLocalRecord) {
//            self.player.assetURL = [NSURL fileURLWithPath:[self fullVideoFileURLString]];
        self.playerController.assetURL = [NSURL fileURLWithPath:[self fullVideoFileURLString]];
    } else {
//            self.player.assetURL = [NSURL URLWithString:[self fullVideoFileURLString]];
        self.playerController.assetURL = [NSURL URLWithString:[self fullVideoFileURLString]];
    }
//    self.playerControlView.portraitControlView.fragmentModelList = self.fragmentModelList;
//    self.playerControlView.landScapeControlView.fragmentModelList = self.fragmentModelList; // 设置分段播放列表
}

/// 视频流回调
- (void)OnVideoDataCallBack:(UIImage *)image {
    
    if (self.isRecording && self.videoFirstImage == nil) {
        self.videoFirstImage = image;
    }

    [self detectFace:image pixelBuffer:nil];
}

- (BOOL)isUseTChat {
    return YES;
}

//回答问题倒计时结束
- (void)answerCountDownEnd {
    
    if (!self.isAlertViewShow && !self.forcedEndOneWayVideo) {
        
        NSTimeInterval time = 10;
        if (self.requestParam[@"additionalRecognitionTime"]) {
            time = [self.requestParam getDoubleWithKey:@"additionalRecognitionTime"];
        }
        if (time > 0) {
            // 到时间停止收音，但继续识别之前的结果
            [self.recordManager continueRecognizeButNotRecordVoice];
            // 创建超时定时器，等待识别结果
            [self createRecogniceTimeoutTimer:time];
        } else {
            
            [self stopAsr];
            [self recogniceTimeoutBreakLink:nil];
        }
        
        return;
    }
    
    // 已经弹窗，不再继续流程
    TKLogInfo(@"思迪录制日志：已弹出弹窗或已强制结束，不再处理其他任务");
}

- (void)createRecogniceTimeoutTimer:(NSTimeInterval)timeoutInterval
{
    // 设置开始录制超时定时器
    self.recogniceTimeoutTimer = [NSTimer timerWithTimeInterval:timeoutInterval target:self selector:@selector(recogniceTimeoutBreakLink:) userInfo:nil repeats:NO];
    [[NSRunLoop mainRunLoop] addTimer:self.recogniceTimeoutTimer forMode:NSRunLoopCommonModes];
}

- (void)recogniceTimeoutBreakLink:(NSTimer *)timer
{
    TKLogInfo(@"思迪录制日志：5s后，之前的收音结果都识别完毕");
        
    [self stopRecogniceTimeoutTimer];
        
    // 等待5s，等之前的结果都处理完毕，若还未能正常识别，走下一个流程
    if (self.transcriberStarted == YES) {
        TKLogInfo(@"思迪录制日志：识别超时，走下一个流程");
        
        // 标记没有结果
        self.currentModel.asrResult = TKAsrResultNoIdentify;
        
        // 设置流程节点
        self.playQuessionProcess = TKPlayQuessionProcessAsrEnd;
        [self nextPlayQuestionProcess]; // 按流程处理
    }
}

- (void)stopRecogniceTimeoutTimer
{
    if (self.recogniceTimeoutTimer) {
        [self.recogniceTimeoutTimer invalidate];
        self.recogniceTimeoutTimer = nil;
    }
}


- (void)nextPlayQuestionProcess {
    [super nextPlayQuestionProcess];
    
    // 识别成功或者失败，关闭倒计时定时器
    if (self.playQuessionProcess == TKPlayQuessionProcessAsrEnd) {
        [self stopRecogniceTimeoutTimer];
    }
}


#pragma mark - TKRecordManagerDelegate
/// 多段语音合成播放结束回调
/// - Parameter index: 当前已合成的语音索引
- (void)speechSynthesisDidPlayWithStartIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration {
    
//    NSLog(@"思迪文案滚动动画：播放语音开始角标=%i, 播放语音结束角标=%i, 播放用时=%.2f", startIndex, endIndex, duration);
    [self.tkOneView scrollBottomShowLabel:startIndex endIndex:endIndex duration:duration];
}

#pragma mark - Setter && Getter
/**
 <AUTHOR> 2019年04月03日10:09:41
 @初始化懒加载单向视频界面
 @return 单向视频界面
 */
-(UIView<TKBaseVideoRecordViewProtocol> *)tkOneView{
    if (!super.tkOneView) {
        super.tkOneView = [[TKNewOneWayVideoView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        super.tkOneView.delegate = self;
        super.tkOneView.avPreviewView = self.avPreviewView;
    }
    return super.tkOneView;
}

/**
 <AUTHOR>
 @初始化单向视频结果页面
 */
- (UIView<TKBaseVideoRecordEndViewProtocol>  *)tkOneEndView{
    if (!super.tkOneEndView) {
        super.tkOneEndView = [[TKNewOneWayVideoEndView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        super.tkOneEndView.delegate = self;
    }
    return super.tkOneEndView;
}


/// 视频录制工具类
- (TKChatLocalVideoRecordManager *)recordManager {
    if (!_recordManager) {
        _recordManager = [[TKChatLocalVideoRecordManager alloc] initWithConfig:self.requestParam];
        _recordManager.delegate = self;
//        _recordManager.contentView = self.tkOneView.avPreviewView;
        _recordManager.contentView = self.avPreviewView;
        _recordManager.videoFilePath = [self getLocalOneWayVideoPath];
    }
    return (TKChatLocalVideoRecordManager * )_recordManager;
}

/**
 <AUTHOR> 2019年03月02日10:39:21
 @懒加载初始化视频预览视图
 @return UIView
 */
- (UIView *)avPreviewView{
    if (!_avPreviewView) {
//        CGFloat y = 10;
//        _avPreviewView = [[UIView alloc] initWithFrame:CGRectMake(10, y, self.view.TKWidth - 10 * 2, self.view.TKHeight - y -  (IPHONEX_BUTTOM_HEIGHT > 0 ? IPHONEX_BUTTOM_HEIGHT : 10))];
//        _avPreviewView = [[UIView alloc] initWithFrame:self.view.bounds];
        _avPreviewView = [[UIView alloc] initWithFrame:self.tkOneView.boxRect];
        _avPreviewView.backgroundColor = [TKUIHelper colorWithHexString:@"#979797"];
        _avPreviewView.layer.cornerRadius = 10;
        _avPreviewView.clipsToBounds = YES;
        _avPreviewView.layer.borderWidth = 1;
        _avPreviewView.layer.borderColor = [TKUIHelper colorWithHexString:@"#979797"].CGColor;
    }
    return _avPreviewView;
}

/// 人脸在框检测工具类
- (TKFaceDetectManager *)faceDetectManager {
    if (!_faceDetectManager) {
        NSMutableDictionary *tempDic = self.requestParam.mutableCopy;
//        tempDic[@"url"] = @"";
        _faceDetectManager = [[TKFaceDetectManager alloc] initWithConfig:tempDic];
//        _faceDetectManager = [[TKFaceDetectManager alloc] initWithConfig:self.requestParam];
        _faceDetectManager.delegate = self;
        _faceDetectManager.targetRect = self.tkOneView.boxRect;
        _faceDetectManager.avPreviewSize = self.tkOneView.boxRect.size;
//        _faceDetectManager.detectUseLocalService = YES;
        _faceDetectManager.isFullImgDetect = YES;
//        NSLog(@"TKFaceDetectManager = %@", _faceDetectManager);
    }
    return _faceDetectManager;
}

/// 播放视频工具视图
- (TKNewOneWayPlayerControlView *)playerControlView{
    if (!_playerControlView) {

        _playerControlView = [[TKNewOneWayPlayerControlView alloc] initWithFrame:self.tkOneEndView.videoShowImgView.frame withParam:self.requestParam];
        _playerControlView.autoHiddenTimeInterval = 5;
        _playerControlView.autoFadeTimeInterval = 0.5;
        _playerControlView.prepareShowLoading = YES;
        _playerControlView.prepareShowControlView = YES;
        _playerControlView.landScapeControlViewRect = CGRectMake(0, 0, MAX(self.view.TKWidth, self.view.TKHeight), MIN(self.view.TKWidth, self.view.TKHeight));
        _playerControlView.needShowForwardView = YES;
        _playerControlView.landScapeControlView.isLandscapeRecordPreview = YES;
        _playerControlView.effectViewShow = NO;
    }
    return _playerControlView;
}

- (TKZFPlayerController *)playerController {
    if (!_playerController) {

        _playerController = [TKZFPlayerController playerWithPlayerManager:self.player containerView:self.tkOneEndView.videoShowImgView];
        _playerController.customAudioSession = YES;
        _playerController.controlView = self.playerControlView;

        /// 播放完成
        @tkzf_weakify(self)
        self.playerController.playerDidToEnd = ^(id  _Nonnull asset) {
            @tkzf_strongify(self)

            [self.player seekToTime:0 completionHandler:nil];
            [self.playerControlView.landScapeControlView playBtnSelectedState:NO];  // 默认播放，这里要修改为暂停状态
//            [self.playerController stop];
        };

        self.playerController.playerPrepareToPlay = ^(id<TKZFPlayerMediaPlayback>  _Nonnull asset, NSURL * _Nonnull assetURL) {
            @tkzf_strongify(self)
            self.playerController.disableGestureTypes = TKZFPlayerDisableGestureTypesNone;
        };

        self.playerController.playerPlayFailed = ^(id<TKZFPlayerMediaPlayback>  _Nonnull asset, id  _Nonnull error) {
            @tkzf_strongify(self)
            self.playerController.disableGestureTypes = TKZFPlayerDisableGestureTypesAll;
        };

        // 横屏预览全屏页面
        self.playerController.orientationWillChange = ^(TKZFPlayerController * _Nonnull player, BOOL isFullScreen) {
            @tkzf_strongify(self)

            if (isFullScreen) {
                CGFloat angle = (90.0f * M_PI) / 180.0f; // 旋转90度，顺时针方向
                CGAffineTransform transform = CGAffineTransformMakeRotation(angle);
                
                self.playerController.currentPlayerManager.view.transform = CGAffineTransformIdentity; // 先重置偏移
                self.playerController.currentPlayerManager.view.transform = transform;
            } else {
                self.playerController.currentPlayerManager.view.transform = CGAffineTransformIdentity; // 先重置偏移
            }
        };
        self.playerController.orientationDidChanged = ^(TKZFPlayerController * _Nonnull player, BOOL isFullScreen) {
            @tkzf_strongify(self)

            if (isFullScreen) {
            
                CGFloat width = MIN(self.view.TKWidth, self.view.TKHeight);
                CGFloat height = MAX(self.view.TKWidth, self.view.TKHeight);
                self.playerController.currentPlayerManager.view.frame = CGRectMake(25, 0, width - 25 * 2, height - 0 * 2);
                
                self.playerControlView.effectViewShow = YES;
            } else {
//                self.playerController.currentPlayerManager.view.transform = CGAffineTransformIdentity; // 先重置偏移
                
                self.playerControlView.effectViewShow = NO;
            }
        };
    }
    return _playerController;
}

- (UIImage *)videoFirstImage {
    
    return _videoFirstImage;
}

@end
