//
//  TKOpenPlugin60044.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/11/16.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKOpenPlugin60044.h"
#import "TKOpenAccountService.h"
@interface TKOpenPlugin60044 ()<TKUploadDelegate>
@property (nonatomic, strong) NSMutableDictionary *requestParam;//h5带过来的参数
@property (nonatomic, strong) TKOpenAccountService *progressService;//处理上传进度通知
@end

@implementation TKOpenPlugin60044
-(ResultVo *)serverInvoke:(id)param{
    ResultVo *resultVo = [[ResultVo alloc]init];
    
    self.progressService=[[TKOpenAccountService alloc] init];
    
    self.requestParam=param;
    
    if ([TKStringHelper isEmpty:self.requestParam[@"urlAddress"]]) {
        resultVo.errorNo = -6004401;
        resultVo.errorInfo = @"服务端接口地址不能为空";
        return resultVo;
    }
    
    if (self.requestParam[@"files"]) {
        NSMutableDictionary *filesDic=self.requestParam[@"files"];
        //遍历文件路径存储字典
        for (id key in filesDic) {
            NSString *strKey=key;
            //判断文件路径下文件是否存在
            NSFileManager *fm = [[NSFileManager alloc] init];
            if (![fm fileExistsAtPath:filesDic[strKey]])
            {
                resultVo.errorNo = -6004402;
                resultVo.errorInfo =[NSString stringWithFormat:@"%@文件路径不存在",strKey];
                return resultVo;
            }
        }
    }else{
        resultVo.errorNo = -6004402;
        resultVo.errorInfo = @"文件路径不能为空";
        return resultVo;
    }
    
    [self uploadFiles];
    return resultVo;
}


/**
<AUTHOR>
@上传文件
*/
-(void)uploadFiles{
    NSMutableDictionary *params=[[NSMutableDictionary alloc] init];
    if (self.requestParam[@"params"]) {
        [params addEntriesFromDictionary:self.requestParam[@"params"]];
    }
    params[@"timeout"]=self.requestParam[@"timeout"]?self.requestParam[@"timeout"]:@"30";
    params[@"requestHeaders"] = self.requestParam[@"requestHeaders"]?self.requestParam[@"requestHeaders"]:nil;
    NSMutableDictionary *filesDic=self.requestParam[@"files"];
    //遍历文件路径存储字典
    for (id key in filesDic) {
        NSString *strKey=key;
        //判断文件路径下文件是否存在
        NSFileManager *fm = [[NSFileManager alloc] init];
        if ([fm fileExistsAtPath:filesDic[strKey]])
        {
            NSString *strFileKey=[NSString stringWithFormat:@"%@@@F",strKey];
            params[strFileKey]=[NSData dataWithContentsOfFile:filesDic[strKey]];
        }
    }


    [self.progressService uploadFileWithURL:self.requestParam[@"urlAddress"] delegate:self param:params callBackFunc:^(ResultVo *resultVo) {
        NSMutableDictionary *jsParam=[[NSMutableDictionary alloc] init];
        jsParam[@"funcNo"]=@"60045";
        jsParam[@"error_no"]=@(resultVo.errorNo);
        jsParam[@"error_info"]=resultVo.errorInfo;
        //上传结束
    
        
        NSMutableDictionary *resultDic = nil;
        if (!resultVo.isStandardResult)
        {
            NSArray *resultsAry = (NSArray *)[resultVo results:@"@@DATA"];
            if(!resultsAry)
            {
                resultsAry = (NSArray *)[resultVo results];
            }
            if (resultsAry && resultsAry.count > 0)
            {
                resultDic = [[resultsAry firstObject]mutableCopy];
            }
            else
            {
                resultDic = [NSMutableDictionary dictionary];
            }
        }
        else
        {
            resultVo.reqParamVo = nil;
            resultDic = resultVo.dictionary;
            //行情兼容处理
            if (![resultDic getObjectWithKey:@"errorNo"])
            {
                [resultDic setInteger:resultVo.errorType withKey:@"errorType"];
                [resultDic setInteger:resultVo.errorNo withKey:@"errorNo"];
                [resultDic setString:resultVo.errorInfo withKey:@"errorInfo"];
            }
        }
        jsParam[@"result"]=resultDic;
        TKLogInfo(@"文件上传结束回调60045");
        [self iosCallJsWithParam:jsParam];
    }];
}

#pragma mark TKUploadDelegate
/**
 *  <AUTHOR> 2015-09-08 20:09:03
 *
 *  显示进度百分比
 *
 *  @param newProgress
 */
- (void)showProgress:(float)newProgress{
    //需要进度百分比回调再给
    if([self.requestParam[@"useProgress"] intValue]==1){
        NSMutableDictionary *jsParam=[NSMutableDictionary dictionary];
        jsParam[@"funcNo"]=@"60045";
        jsParam[@"error_no"]=@"1";
        jsParam[@"progress"]=@(newProgress*100.00f);
        
        TKLogInfo(@"文件上传进度百分比%f",newProgress*100.00f);
        if (self.requestParam[@"tkuuid"]) {
            jsParam[@"tkuuid"] = self.requestParam[@"tkuuid"];
        }else{
            jsParam[@"moduleName"] = self.requestParam[@"moduleName"]? self.requestParam[@"moduleName"]:@"open";
        }
        [self.progressService iosCallJsWithDic:jsParam callBackFunc:nil];
    }
    

}
@end
