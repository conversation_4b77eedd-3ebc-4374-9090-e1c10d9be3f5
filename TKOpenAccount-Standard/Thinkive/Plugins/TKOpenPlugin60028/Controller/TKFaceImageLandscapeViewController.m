//
//  TKFaceImageViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/2/19.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKFaceImageLandscapeViewController.h"
#import "TKFaceImageLandscapeView.h"
#import "TKAVCaptureManager.h"
#import "TKOpenAccountService.h"

@interface TKFaceImageLandscapeViewController ()<TKFaceImageLandscapeViewDelegate>
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, strong) UIView *previewView;//预览View
@property (nonatomic, strong) TKFaceImageLandscapeView *faceImgView;//人脸影像采集界面
@property (nonatomic, strong) TKAVCaptureManager* captureManager;//视频工具类
@property (nonatomic, strong) UIImage *headImg;//头像图
@property (nonatomic, assign) BOOL  isFrontCamera;//是否是前置相机
@end

@implementation TKFaceImageLandscapeViewController

#pragma mark - life cycle

- (instancetype)initWithParam:(NSMutableDictionary *)param{
    self = [super init];
    if (self) {
        self.requestParam = param;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [TKUIHelper colorWithHexString:@"#000000"];
    
    if (self.captureManager == nil) {
        
        self.previewView.frame =self.faceImgView.boxRect;
        [self.view addSubview:self.previewView];
        self.isFrontCamera=YES;
        self.captureManager =  [[TKAVCaptureManager alloc] initWithPreviewView:self.previewView withCameraPosition:AVCaptureDevicePositionFront withCamraOrientation:AVCaptureVideoOrientationLandscapeRight handleBusinessType:TK_VIDEO_TAKE_PHOTO];
    } else {
         [self.captureManager reTakePicture];
    }
    [self.view addSubview:self.faceImgView];
    
    CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
    self.view.transform = transform;
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];

}

#pragma mark - Orientations

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}


- (BOOL)shouldAutorotate
{
    return NO;
}

#pragma mark TKFaceImageViewDelegate
/**
 <AUTHOR> 2019年04月13日14:18:27
 @单向视频结果页返回
 */
-(void)goBack{
    [self dismissViewControllerAnimated:YES completion:^{
        TKOpenAccountService *errorService = [[TKOpenAccountService alloc] init];
        NSMutableDictionary *callJsParam = [NSMutableDictionary dictionary];
        callJsParam[@"funcNo"] = @"60029";
        callJsParam[@"error_no"] = @"-1";
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkFaceImageDidComplete:)]) {
            [self.delegate tkFaceImageDidComplete:callJsParam];
        }else{
            [errorService iosCallJsWithDic:callJsParam callBackFunc:nil];
        }
    }];
}

/**
 <AUTHOR> 2019年04月08日10:38:08
 @拍照代理
 */
- (void)takePicture{
    [self.captureManager takePicture:^(id hResult) {
        if ([hResult isKindOfClass:[UIImage class]]) {
            UIImage *image = [self convertImage:hResult];
            NSData *imgData = UIImageJPEGRepresentation([TKCommonUtil imageByScalingNotCroppingForSize:image toSize:CGSizeMake(480, 640)], 1);
            self.headImg = [UIImage imageWithData:imgData] ;
            [self.faceImgView showHeadPicture:self.headImg];
            
        }else{
            TKLayerView  *layerView=[[TKLayerView alloc] initContentView:self.view withBtnTextColor:nil];
            [layerView showTip:@"请重新拍照" position:TKLayerPosition_Center];
            [self.captureManager reTakePicture];
        }
    }];
}

- (UIImage *)convertImage:(UIImage *)image {
    UIImage *tempImage = image;
    if(self.isFrontCamera){
        //前置横屏摄像头拍的照片要调整旋转角度
        tempImage = [UIImage imageWithCGImage:tempImage.CGImage scale:1.0f orientation:UIImageOrientationRight];
    }
    
    return tempImage;
}

/**
 <AUTHOR> 2020年02月27日19:23:41
 @重新拍照代理
 */
-(void)retryPicture{
    [self.captureManager reTakePicture];
}

/**
 <AUTHOR> 2020年02月27日19:23:50
 @提交代理
 */
-(void)submitPicture{
    [self dismissViewControllerAnimated:YES completion:^{
        TKOpenAccountService *errorService = [[TKOpenAccountService alloc] init];
        NSMutableDictionary *callJsParam = [NSMutableDictionary dictionary];
        NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:UIImageJPEGRepresentation(self.headImg, 1)];
        NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
        
        callJsParam[@"base64"] = base64;
        callJsParam[@"funcNo"] = @"60029";
        callJsParam[@"error_no"] = @"0";
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkFaceImageDidComplete:)]) {
            [self.delegate tkFaceImageDidComplete:callJsParam];
        }else{
            [errorService iosCallJsWithDic:callJsParam callBackFunc:nil];
        }
    }];
}
/**
 <AUTHOR> 2022年12月22日13:15:21
 @切换摄像头代理
 */
-(void)switchTakeCamera{
    //移除视频残留界面
    if ([self.previewView.layer.sublayers[0] isKindOfClass:[AVCaptureVideoPreviewLayer class]]) {
        [self.previewView.layer.sublayers[0] removeFromSuperlayer];
    }

    self.captureManager=nil;
    if(self.isFrontCamera){
        self.captureManager =  [[TKAVCaptureManager alloc] initWithPreviewView:self.previewView withCameraPosition:AVCaptureDevicePositionBack withCamraOrientation:AVCaptureVideoOrientationLandscapeRight handleBusinessType:TK_VIDEO_TAKE_PHOTO];
    }else{
        self.captureManager =  [[TKAVCaptureManager alloc] initWithPreviewView:self.previewView withCameraPosition:AVCaptureDevicePositionFront withCamraOrientation:AVCaptureVideoOrientationLandscapeRight handleBusinessType:TK_VIDEO_TAKE_PHOTO];
    }
    self.isFrontCamera=!_isFrontCamera;
}

#pragma mark lazyloading

- (UIView *)previewView {
    if (!_previewView) {
        _previewView = [[UIView alloc] init];
    }
    return _previewView;
}

/**
 <AUTHOR> 2019年04月03日10:09:41
 @初始化懒加载单向视频界面
 @return 单向视频界面
 */
-(TKFaceImageLandscapeView *)faceImgView{
    if (!_faceImgView) {
        _faceImgView = [[TKFaceImageLandscapeView alloc] initWithFrame:CGRectMake(0, 0, self.view.TKHeight, self.view.TKWidth) requestParams:self.requestParam];
        _faceImgView.delegate = self;
    }
    return _faceImgView;
}
@end
