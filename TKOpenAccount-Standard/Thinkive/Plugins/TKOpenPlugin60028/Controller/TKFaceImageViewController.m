//
//  TKFaceImageViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/2/19.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKFaceImageViewController.h"
#import "TKFaceImageView.h"
#import "TKAVCaptureManager.h"
#import "TKOpenAccountService.h"

@interface TKFaceImageViewController ()<TKFaceImageViewDelegate>
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, strong) TKFaceImageView *faceImgView;//人脸影像采集界面
@property (nonatomic, strong) TKAVCaptureManager* captureManager;//视频工具类
@property (nonatomic, strong) UIImage *headImg;//头像图
@property (nonatomic, strong) TKOpenAccountService *mService;
@end

@implementation TKFaceImageViewController

-(instancetype)initWithParam:(NSMutableDictionary *)param{
    self=[super init];
    if (self) {
        self.requestParam=param;
        self.mService=[[TKOpenAccountService alloc] init];
    }
    return self;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor grayColor];
    
    if (self.captureManager == nil) {
        
        UIView *cView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, self.view.frame.size.height)];
        
        self.view.frame = CGRectMake(0, 0, self.view.frame.size.width, self.view.frame.size.height);
        
        [self.view addSubview:cView];
        
        self.captureManager =  [[TKAVCaptureManager alloc] initWithPreviewView:cView withCameraPosition:2 withCamraOrientation:AVCaptureVideoOrientationPortrait handleBusinessType:TK_VIDEO_TAKE_PHOTO];
    }else{
    
         [self.captureManager reTakePicture];
    }
    
    [self.view addSubview:self.faceImgView];
}

#pragma mark TKFaceImageViewDelegate
/**
 <AUTHOR> 2019年04月13日14:18:27
 @单向视频结果页返回
 */
-(void)goBack{
    [self dismissViewControllerAnimated:YES completion:^{
        TKOpenAccountService *errorService = [[TKOpenAccountService alloc] init];
        NSMutableDictionary *callJsParam = [NSMutableDictionary dictionary];
        callJsParam[@"funcNo"] = @"60029";
        callJsParam[@"error_no"] = @"-1";
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkFaceImageDidComplete:)]) {
            [self.delegate tkFaceImageDidComplete:callJsParam];
        }else{
            [errorService iosCallJsWithDic:callJsParam callBackFunc:nil];
        }
    }];
}

/**
 <AUTHOR> 2019年04月08日10:38:08
 @拍照代理
 */
-(void)takePicture{
    [self.captureManager takePicture:^(id hResult) {
        if (hResult) {
         NSData *imgData=  UIImageJPEGRepresentation([TKCommonUtil imageByScalingNotCroppingForSize:hResult toSize:CGSizeMake(480, 640)], 0.8);
            self.headImg =[UIImage imageWithData:imgData] ;
            if (self.requestParam[@"isNeedPreview"] &&[self.requestParam[@"isNeedPreview"] intValue] == 0) {
                [self submitPicture];
            }else{
                [self.faceImgView showHeadPicture:self.headImg];
            }
            
            
        }else{
            TKLayerView  *layerView=[[TKLayerView alloc] initContentView:self.view withBtnTextColor:nil];
            [layerView showTip:@"请重新拍照" position:TKLayerPosition_Center];
            [self.captureManager reTakePicture];
        }
    }];
}

/**
 <AUTHOR> 2020年02月27日19:23:41
 @重新拍照代理
 */
-(void)retryPicture{
    [self.captureManager reTakePicture];
}

/**
 <AUTHOR> 2020年02月27日19:23:50
 @提交代理
 */
-(void)submitPicture{
    [self dismissViewControllerAnimated:YES completion:^{
        
        NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
        NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:UIImageJPEGRepresentation(self.headImg, 1)];
        NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
        
        callJsParam[@"base64"]=base64;
        callJsParam[@"funcNo"]=@"60029";
        callJsParam[@"error_no"]=@"0";
        callJsParam[@"moduleName"]=self.requestParam[@"moduleName"];
        callJsParam[@"tkuuid"]=self.requestParam[@"tkuuid"];
        
        if (self.delegate&&[self.delegate respondsToSelector:@selector(tkFaceImageDidComplete:)]) {
            [self.delegate tkFaceImageDidComplete:callJsParam];
        }else{
            [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
        }
        
        
    }];

}

#pragma mark lazyloading
/**
 <AUTHOR> 2019年04月03日10:09:41
 @初始化懒加载单向视频界面
 @return 单向视频界面
 */
-(TKFaceImageView *)faceImgView{
    if (!_faceImgView) {
        _faceImgView=[[TKFaceImageView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        _faceImgView.delegate=self;
    }
    return _faceImgView;
}
@end
