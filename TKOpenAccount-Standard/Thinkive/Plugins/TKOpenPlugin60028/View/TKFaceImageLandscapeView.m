//
//  TKFaceImageView.m
//  OneWayVideo
//
//  Created by <PERSON>ie on 2019/4/3.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKFaceImageLandscapeView.h"

#define TK_LIVEFACE_PIECE_COLOR  [UIColor colorWithRed:0/255.0 green:13/255.0 blue:41/255.0 alpha:0.85/1.0]
#define TK_LIVEFACE_TIP_LABEL_COLOR [TKUIHelper colorWithHexString:@"#FFFFFF"]
#define TK_WAIT_COUNT_DOWN 0.9f

@interface TKFaceImageLandscapeView()
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, strong) UIButton *backBtn;//返回按钮

@property (nonatomic, strong) UIView *showTipView;//底部文档等提示展示区域
@property (nonatomic, strong) UILabel *showLabel;//底部文字展示
@property (nonatomic, strong) TKLayerView  *layerView;//提示layer

@property (nonatomic, strong) UIButton *takeBtn;//拍照按钮
@property (nonatomic, strong) UIView *bgView;//拍照预览背景图
@property (nonatomic, strong) UIImageView *headImgView;//头像展示背景
@property (nonatomic, strong) UIView *bottomBtnBgView;//底部按钮区域底图
@property (nonatomic, strong) UIButton *previewBackBtn;//预览返回按钮
@property (nonatomic, strong) UIButton *switchCameraBtn;//切换摄像头按钮
@end

@implementation TKFaceImageLandscapeView

#pragma mark - life cycle
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self = [super initWithFrame:frame];
    if (self) {
        self.requestParam=requestParams;
        if ([TKStringHelper isEmpty:self.requestParam[@"mainColor"]]) {
            self.requestParam[@"mainColor"]=nil;
        }
        [self viewInit];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];

    
}

#pragma mark - public
/**
 <AUTHOR> 2020年02月27日18:37:02
 @展示拍照人像图
 */
- (void)showHeadPicture:(UIImage *)headImg {
    self.bgView=[[UIView alloc] initWithFrame:self.bounds];
    [self.bgView setBackgroundColor:[TKUIHelper colorWithHexString:@"#232323"]];
    [self addSubview:self.bgView];
    
    float cardLabelWdith=108;
    float cardLabelHeight=25;
    float cardLabelX=(self.TKWidth-cardLabelWdith)/2.0f;
    float cardLabelY=24;
    
    UILabel *cardLabel = [[UILabel alloc] init];
    cardLabel.frame = CGRectMake(cardLabelX, cardLabelY, cardLabelWdith, cardLabelHeight);
    cardLabel.text = @"请您核对照片";
    cardLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
    cardLabel.textColor = [UIColor whiteColor];
    [self.bgView addSubview:cardLabel];
    
    [self.bgView addSubview:self.headImgView];
    [self.bgView addSubview:self.previewBackBtn];
    
    [self.bgView addSubview:self.bottomBtnBgView];
    
    float headImgViewWidth=self.TKWidth;
    float headImgViewY=cardLabel.TKBottom+5;
    float headImgViewX=0;
    float headImgViewHeight=self.bottomBtnBgView.TKTop-headImgViewY-10.0f;
    self.headImgView.frame = CGRectMake(headImgViewX, headImgViewY, headImgViewWidth, headImgViewHeight);
    [self.headImgView setImage:headImg];
    
    
}

#pragma mark - private

/**
 <AUTHOR> 2019年04月03日09:13:34
 @初始化单向视频默认界面
 */
-(void)viewInit{
    
    [self addSubview:self.showTipView];
    [self.showTipView addSubview:self.showLabel];
    [self addSubview:self.takeBtn];
    [self addSubview:self.backBtn];
    [self addSubview:self.switchCameraBtn];
    
    CGFloat viewWidth = self.TKWidth;
    CGFloat viewHeight = self.TKHeight;
    if (viewWidth<viewHeight) {
        viewWidth=self.TKHeight;
        viewHeight=self.TKWidth;
    }
    

    


    //计算frame
    CGFloat bottomWidth = self.boxRect.size.width - 20 * 2;
    CGFloat showLabelX=20;
    CGSize labelSize=[self.showLabel sizeThatFits:CGSizeMake(bottomWidth-2*showLabelX, MAXFLOAT)];
    self.showLabel.frame = CGRectMake(20, 10, labelSize.width, labelSize.height);
    
    CGFloat bottomHeight = labelSize.height+20;
    CGFloat bottomX = self.boxRect.origin.x + (self.boxRect.size.width - bottomWidth) * 0.5;
    CGFloat bottomY = 20;
    self.showTipView.frame = CGRectMake(bottomX, bottomY, bottomWidth, bottomHeight);
    
    if ([TKStringHelper isEmpty:self.requestParam[@"takeTipHtml"]]) {
        [self.showTipView setHidden:YES];
        
    }
}

#pragma mark 事件方法
/**
 @Auther Vie 2022年12月22日13:13:50
 @param sender 切换摄像头点击事件
 */
-(void)switchCameraAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(switchTakeCamera)]) {
        [self.delegate switchTakeCamera];
    }
}

/**
 @Auther Vie 2019年04月08日10:46:51
 @param sender 返回按钮点击事件
 */
- (void)backAction:(UIButton *)sender {
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
        [self.delegate goBack];
    }
}

/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
- (void)takeAction:(UIButton *)sender {
    if (self.delegate && [self.delegate respondsToSelector:@selector(takePicture)]) {
        [self.delegate takePicture];
    }
}

/**
 @Auther Vie 2020年02月27日19:22:13
 @param sender 重拍事件
 */
- (void)retryAction:(UIButton *)sender {
    [self.bgView removeFromSuperview];
    if (self.delegate&&[self.delegate respondsToSelector:@selector(retryPicture)]) {
        [self.delegate retryPicture];
    }
}

/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 提交事件
 */
- (void)submitAction:(UIButton *)sender {
    if (self.delegate&&[self.delegate respondsToSelector:@selector(submitPicture)]) {
        [self.delegate submitPicture];
    }
}


#pragma mark lazyloading

/**
 <AUTHOR> 2019年04月03日10:47:55
 @初始化懒加载人像取景框矩阵
 @return 人像取景框矩阵
 */
-(CGRect)boxRect{
    if (CGRectIsEmpty(_boxRect)) {
        float height=self.TKHeight;
        float width=height*4.0f/3.0f;
        float x=0;
        float gapWidth=self.takeBtn.TKLeft-self.backBtn.TKRight;
        if(width<=gapWidth){
            x=(gapWidth-width)/2.0f+self.backBtn.TKRight;
        }
        _boxRect = CGRectMake(x, 0, width, height);
    }
    return _boxRect;
}


/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnWidth=32;
        float backBtnheight=32;
        float backBtnX=STATUSBAR_HEIGHT;
        float backBtnY=20;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        _backBtn.clipsToBounds = YES;
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来
        
        [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
        _backBtn.layer.cornerRadius = backBtnWidth/2.0f;
        
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}
/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)previewBackBtn{
    if (!_previewBackBtn) {
        
        float backBtnWidth=32;
        float backBtnheight=32;
        float backBtnX=STATUSBAR_HEIGHT;
        float backBtnY=20;
        _previewBackBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        _previewBackBtn.clipsToBounds = YES;
        [_previewBackBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_previewBackBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来

        
        [_previewBackBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _previewBackBtn;
}


/**
 <AUTHOR> 2019年12月30日22:05:40
 @初始化懒加载底部文档等提示展示区域
 @return 底部文档等提示展示区域
 */
-(UIView *)showTipView{
    if (!_showTipView) {
        _showTipView = [[UIView alloc] init];
        _showTipView.backgroundColor = [TKUIHelper colorWithHexString:@"#333333" alpha:0.8];
        //子视图是否局限于视图的边界。
        _showTipView.clipsToBounds=YES;
        _showTipView.layer.cornerRadius =8.0f;
    }
    return _showTipView;
}

/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载底部文字展示
 @return 底部文字展示
 */
- (UILabel *)showLabel{
    if (!_showLabel) {
        _showLabel = [[UILabel alloc] init];
        
        _showLabel.backgroundColor=[UIColor clearColor];
        _showLabel.numberOfLines=0;
        if ([TKStringHelper isNotEmpty:self.requestParam[@"takeTipHtml"]]) {
            NSData *data = [self.requestParam[@"takeTipHtml"] dataUsingEncoding:NSUnicodeStringEncoding];
            NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType};
            NSMutableAttributedString *html = [[NSMutableAttributedString alloc]initWithData:data options:options documentAttributes:nil error:nil];
            _showLabel.attributedText = html;
        }
        _showLabel.textAlignment=NSTextAlignmentCenter;
    }
    return _showLabel;
}

/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载提示layer
 @return 提示layer
 */
-(TKLayerView *)layerView{
    if (!_layerView) {
        _layerView=[[TKLayerView alloc] initContentView:self withBtnTextColor:nil];
        [_layerView setShowTipDuration:0.6];
    }
    return _layerView;
}



/**
 <AUTHOR> 2019年04月09日16:00:05
 @初始化懒加载活体界面提示gif图
 @return 活体界面提示gif图
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {
        _takeBtn = [[UIButton alloc] init];
        
        [_takeBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
           
        [_takeBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_selected_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateHighlighted];
        [_takeBtn addTarget:self action:@selector(takeAction:) forControlEvents:UIControlEventTouchUpInside];
        
        CGFloat takeBtnWidth = 62;
        CGFloat takeRightSpace = ISIPHONEX ? 48 : 8;
        CGFloat takeBtnX = self.TKWidth - takeBtnWidth - takeRightSpace;
        CGFloat takeBtnY = self.TKHeight / 2.f - takeBtnWidth / 2.f;
        _takeBtn.frame = CGRectMake(takeBtnX, takeBtnY, takeBtnWidth, takeBtnWidth);
    }
    return _takeBtn;
}

/**
 *  <AUTHOR> 2019年09月08日14:32:34
 *  @初始化懒加载switchCameraBtn
 *  @return  switchCameraBtn
 */
-(UIButton *)switchCameraBtn{
    if (!_switchCameraBtn) {
        float width=40;
        float height=40;
        float x=self.takeBtn.TKLeft+(self.takeBtn.TKWidth-width)/2.0f;
        float y=20;
        _switchCameraBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _switchCameraBtn.clipsToBounds = YES;
        [_switchCameraBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_switchCamera_btn.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_switchCameraBtn setImageEdgeInsets:UIEdgeInsetsMake(10, 10, 12, 10)];

        [_switchCameraBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
        _switchCameraBtn.layer.cornerRadius = width/2.0f;
        
        [_switchCameraBtn addTarget:self action:@selector(switchCameraAction)
          forControlEvents:UIControlEventTouchUpInside];
    }
    return _switchCameraBtn;
}


/**
 <AUTHOR> 2020年02月27日18:54:39
 @初始化懒加headImgView
 @return 拍照取景头像
 */
-(UIImageView *)headImgView{
    if (!_headImgView) {
        _headImgView = [[UIImageView alloc] init];
        [_headImgView setBackgroundColor:[TKUIHelper colorWithHexString:@"#232323"]];
        _headImgView.contentMode = UIViewContentModeScaleAspectFit;
        _headImgView.userInteractionEnabled = YES;
    }
    return _headImgView;
}



/**
 <AUTHOR> 2020年02月27日18:54:43
 @初始化懒加载bottomBtnBgView
 @return 底部按钮图层
 */
-(UIView *)bottomBtnBgView{
    if (!_bottomBtnBgView) {
        float height = 44;
        float y = self.TKHeight - height - 3.0f-IPHONEX_BUTTOM_HEIGHT;
        float width = 324.f;

        float x= (self.TKWidth-width)/2.0f;
        _bottomBtnBgView = [[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
        float btnGap = 0.0f;
        float btnWidth = 152.0f;
        float btnY = 0.0f;
        float btnHeight = 44;
        //重试按钮
        UIButton *retryBtn = [[UIButton alloc] initWithFrame:CGRectMake(btnGap, btnY, btnWidth, btnHeight)];
        [retryBtn setTitle:@"重拍" forState:UIControlStateNormal];
        [retryBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        retryBtn.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.3f];
        retryBtn.layer.cornerRadius =btnHeight/2.0f;
        retryBtn.layer.borderWidth = 1.0f;
        retryBtn.layer.borderColor = [TKUIHelper colorWithHexString:@"#FFFFFF"].CGColor;
        
        
        [retryBtn addTarget:self action:@selector(retryAction:) forControlEvents:UIControlEventTouchUpInside];
        [_bottomBtnBgView addSubview:retryBtn];
        
        //提交按钮
        UIButton *submitBtn = [[UIButton alloc] initWithFrame:CGRectMake(retryBtn.TKRight+20.0f, btnY, btnWidth, btnHeight)];
        [submitBtn setTitle:@"提交" forState:UIControlStateNormal];
        [submitBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        [submitBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]?self.requestParam[@"mainColor"]:@"#387CFF"]];
        submitBtn.layer.cornerRadius = btnHeight/2.0f;
        [submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
        [_bottomBtnBgView addSubview:submitBtn];
    }
    return _bottomBtnBgView;
}

@end
