//
//  TKFaceImageView.h
//  OneWayVideo
//  
//  Created by <PERSON>ie on 2019/4/3.
//  Copyright © 2019 Vie. All rights reserved.
//


//单向视频视图代理，按钮等事件
@protocol TKFaceImageViewDelegate <NSObject>


/**
 <AUTHOR> 2019年04月08日10:38:08
 @返回代理
 */
-(void)goBack;

/**
 <AUTHOR> 2019年04月08日10:38:08
 @拍照代理
 */
-(void)takePicture;

/**
 <AUTHOR> 2020年02月27日19:23:41
 @重新拍照代理
 */
-(void)retryPicture;

/**
 <AUTHOR> 2020年02月27日19:23:50
 @提交代理
 */
-(void)submitPicture;

@end

@interface TKFaceImageView : UIView
@property (nonatomic, assign) CGRect boxRect;//人像取景框矩阵
@property (nonatomic, weak) id<TKFaceImageViewDelegate> delegate;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数

-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param;

/**
 <AUTHOR> 2020年02月27日18:37:02
 @展示拍照人像图
 */
-(void)showHeadPicture:(UIImage *)headImg;

@end


