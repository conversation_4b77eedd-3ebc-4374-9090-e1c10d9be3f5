//
//  TKFaceImageView.m
//  OneWayVideo
//
//  Created by <PERSON>ie on 2019/4/3.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKFaceImageView.h"

#define TK_LIVEFACE_PIECE_COLOR  [UIColor colorWithRed:0/255.0 green:13/255.0 blue:41/255.0 alpha:0.85/1.0]
#define TK_LIVEFACE_TIP_LABEL_COLOR [TKUIHelper colorWithHexString:@"#FFFFFF"]
#define TK_WAIT_COUNT_DOWN 0.9f

@interface TKFaceImageView()
@property (nonatomic, strong) UIImageView *boxImgView;//人像取景框
@property (nonatomic, strong) UIImageView *boxImgBackgroundView;//人像取景背景框
@property (nonatomic, strong) UIView *topView,*bottomView,*leftView,*rightView;//顶部遮罩层,底部遮罩层,左部遮罩层，右部遮罩层
@property (nonatomic, strong) UIView *topGradientView,*bottomGradientView;//没对准框时候头部渐变色，和底部渐变色


@property (nonatomic, strong) UIButton *backBtn;//返回按钮


@property (nonatomic, strong) UILabel *bottomShowLabel;//底部文字展示
@property (nonatomic, strong) TKLayerView  *layerView;//提示layer

@property (nonatomic, strong) UIButton *takeBtn;//拍照按钮

@property (nonatomic, strong) UIView *photoBgView;//拍照后的背景图
@property (nonatomic, strong) UIButton *photoBgBackBtn;//返回按钮
@property (nonatomic, strong) UIView *videoShowBgView;//视频展示人像视图背景
@property (nonatomic, strong) UIImageView *headImgView;//头像展示背景
@property (nonatomic, strong) UIView *bottomBtnBgView;//底部按钮区域底图
@property (nonatomic, strong) UILabel *bigTipLabel;//大字的提示语
@property (nonatomic, strong) UILabel *smallTipLabel;//小字的提示语

@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@end

@implementation TKFaceImageView

-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        self.requestParam=param;
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2F85FF";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
        [self viewInit];
    }
    return self;
}

/**
 <AUTHOR> 2019年04月03日09:13:34
 @初始化单向视频默认界面
 */
-(void)viewInit{

    if ([self.requestParam[@"takeType"] intValue]==2) {
        //不需要对准框的时候走渐变色
        [self addSubview:self.topGradientView];
        [self addSubview:self.bottomGradientView];
    }else{
        [self addSubview:self.boxImgBackgroundView];
        [self addSubview:self.boxImgView];
        [self addSubview:self.topView];
        [self addSubview:self.bottomView];
        [self addSubview:self.leftView];
        [self addSubview:self.rightView];
    }

    
    [self addSubview:self.backBtn];
    

    [self addSubview:self.bottomShowLabel];
    [self addSubview:self.takeBtn];
    
}

/**
 <AUTHOR> 2020年02月27日18:37:02
 @展示拍照人像图
 */
-(void)showHeadPicture:(UIImage *)headImg{

    [self addSubview:self.photoBgView];
    [self.photoBgView addSubview:self.photoBgBackBtn];
    [self.photoBgView addSubview:self.videoShowBgView];
    [self.headImgView setImage:headImg];
    [self.photoBgView addSubview:self.bottomBtnBgView];
    [self.photoBgView addSubview:self.smallTipLabel];
    [self.photoBgView addSubview:self.bigTipLabel];
}

#pragma mark 事件方法

/**
 @Auther Vie 2019年04月08日10:46:51
 @param sender 返回按钮点击事件
 */
-(void)backAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
        [self.delegate goBack];
    }
}



/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
-(void)takeAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(takePicture)]) {
        [self.delegate takePicture];
    }
}

/**
 @Auther Vie 2020年02月27日19:22:13
 @param sender 重拍事件
 */
-(void)retryAction:(UIButton *)sender{
    [self.photoBgView removeFromSuperview];
    if (self.delegate&&[self.delegate respondsToSelector:@selector(retryPicture)]) {
        [self.delegate retryPicture];
    }
}

/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 提交事件
 */
-(void)submitAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(submitPicture)]) {
        [self.delegate submitPicture];
    }
}


//更新文字提示
-(void)updateTipLabel:(NSString *)string{
    self.bottomShowLabel.text = string;
    float height=42;
    float x=20;
    float width=self.frame.size.width-2*x;//左右保持留白15
    
    CGSize lableSize = [_bottomShowLabel sizeThatFits:CGSizeMake(width, MAXFLOAT)];

    float gap =40;
    //5s等小屏幕机型
    if (UISCREEN_WIDTH==320) {
        gap=28;
    }
    float y=self.boxRect.origin.y-height-gap;
    self.bottomShowLabel.frame=CGRectMake(x, y, width, lableSize.height+20);
}

#pragma mark lazyloading

/**
 <AUTHOR> 2019年04月03日10:47:55
 @初始化懒加载人像取景框矩阵
 @return 人像取景框矩阵
 */
-(CGRect)boxRect{
    if (CGRectIsEmpty(_boxRect)) {
        //活体对准框要居中
        float boxRectX =38;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            boxRectX=32;
        }
        float boxRectWidth = self.TKWidth-boxRectX*2.0f;
        float boxRectHeight = boxRectWidth / 300.0f * 340.0f; // 图片宽高是300 * 340
        float boxRectY = (self.TKHeight-boxRectHeight)/2.0f;
        _boxRect=CGRectMake(boxRectX, boxRectY, boxRectWidth, boxRectHeight);
        
    }
    return _boxRect;
}


/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加载顶部遮罩层
 @return 顶部遮罩层
 */
-(UIView *)topView{
    if (!_topView) {
        _topView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, self.TKWidth, self.boxRect.origin.y)];
        _topView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _topView;
}


/**
 <AUTHOR> 2019年04月03日11:20:41
 @初始化懒加载底部遮罩层
 @return 底部遮罩层
 */
-(UIView *)bottomView{
    if (!_bottomView) {
        _bottomView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.size.height+self.boxRect.origin.y, self.TKWidth, self.TKHeight-self.boxRect.origin.y-self.boxRect.size.height)];
        _bottomView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _bottomView;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加左部遮罩层
 @return 左部遮罩层
 */
-(UIView *)leftView{
    if (!_leftView) {
        _leftView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.origin.y, self.boxRect.origin.x, self.boxRect.size.height)];
        _leftView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _leftView;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加右部遮罩层
 @return 右部遮罩层
 */
-(UIView *)rightView{
    if (!_rightView) {
        _rightView=[[UIView alloc] initWithFrame:CGRectMake(self.boxRect.origin.x+self.boxRect.size.width, self.boxRect.origin.y, self.boxRect.origin.x, self.boxRect.size.height)];
        _rightView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _rightView;
}




/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载底部文字展示
 @return 底部文字展示
 */
-(UILabel *)bottomShowLabel{
    if (!_bottomShowLabel) {

        _bottomShowLabel=[[UILabel alloc] init];
        
        _bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
        _bottomShowLabel.textAlignment=NSTextAlignmentCenter;
        _bottomShowLabel.textColor =[TKUIHelper colorWithHexString:@"#FFFFFF"];
        _bottomShowLabel.numberOfLines=0;


        _bottomShowLabel.layer.borderWidth=1.5f;

        if (![self.mainColorString isEqualToString:@"#2F85FF"]) {
            _bottomShowLabel.layer.borderColor=[TKUIHelper colorWithHexString:self.mainColorString alpha:0.2f].CGColor;
        }else{
            _bottomShowLabel.layer.borderColor=[TKUIHelper colorWithHexString:@"#498FD5" alpha:0.2f].CGColor;
        }
        [_bottomShowLabel setBackgroundColor:[TKUIHelper colorWithHexString:@"#040D16" alpha:0.5f]];
        _bottomShowLabel.layer.cornerRadius=8.0f;

        _bottomShowLabel.clipsToBounds=YES;//子视图是否局限于视图的边界。
        if ([TKStringHelper isEmpty:self.requestParam[@"takeTip"]]) {
            [self updateTipLabel:@"请您保持全脸在人像框内。"];
        }else{
            [self updateTipLabel:self.requestParam[@"takeTip"]];
        }
       

        
    }
    return _bottomShowLabel;
}



/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载提示layer
 @return 提示layer
 */
-(TKLayerView *)layerView{
    if (!_layerView) {
        _layerView=[[TKLayerView alloc] initContentView:self withBtnTextColor:nil];
        [_layerView setShowTipDuration:0.6];
    }
    return _layerView;
}




/**
 <AUTHOR> 2019年04月01日14:48:01
 @初始化懒加载UIImageView人像取景框
 @return UIImageView人像取景框
 */
-(UIImageView *)boxImgView{
    if (!_boxImgView) {
       
        _boxImgView=[[UIImageView alloc] initWithFrame:self.boxRect];
        _boxImgView=[[UIImageView alloc] initWithFrame:self.boxRect];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_default_box.png", TK_OPEN_RESOURCE_NAME]];

//        if (![self.mainColorString isEqualToString:@"#2F85FF"]) {
//            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
//            [_boxImgView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
//        }
        [_boxImgView setImage:img];
    }
    return _boxImgView;
}

/**
 @初始化懒加载UIImageView人像取景背景框(外层的黑色边框)
 @return UIImageView人像取景背景框
 */
-(UIImageView *)boxImgBackgroundView{
    if (!_boxImgBackgroundView) {
       
        _boxImgBackgroundView = [[UIImageView alloc] initWithFrame:self.boxRect];
        [_boxImgBackgroundView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/<EMAIL>", TK_OPEN_RESOURCE_NAME]]];
    }
    return _boxImgBackgroundView;
}


/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnWidth=32;
        float backBtnheight=32;
        float backBtnX=20.0f;
        float backBtnY=6+STATUSBAR_HEIGHT;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        _backBtn.clipsToBounds = YES;
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来

        [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
        _backBtn.layer.cornerRadius = backBtnWidth/2.0f;

        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}



/**
 <AUTHOR> 2019年04月09日16:00:05
 @初始化懒加载活体界面提示gif图
 @return 活体界面提示gif图
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {
        float takeBtnWidth=62;
        float takeBtnX=(self.TKWidth-takeBtnWidth)/2;
        float takeBtnY=self.TKHeight-takeBtnWidth-IPHONEX_BUTTOM_HEIGHT-30;
        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(takeBtnX, takeBtnY, takeBtnWidth, takeBtnWidth)];
        
        [_takeBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
           
        [_takeBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_selected_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateHighlighted];
        [_takeBtn addTarget:self action:@selector(takeAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _takeBtn;
}

/**
 <AUTHOR> 2021年11月11日15:53:31
 @初始化懒加载topGradientView
 @return topGradientView
 */
-(UIView *)topGradientView{
    if (!_topGradientView) {
        float height=self.backBtn.TKBottom+38;
        _topGradientView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, self.TKWidth, height)];
        
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.frame = _topGradientView.bounds;
        // 渐变色颜色数组,可多个
        gradientLayer.colors = [NSArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.7f].CGColor, (id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.0f].CGColor, nil];
        gradientLayer.startPoint = CGPointMake(0.5, 0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        [_topGradientView.layer addSublayer:gradientLayer];
    }
    return _topGradientView;
}

/**
 <AUTHOR> 2021年11月11日16:07:26
 @初始化懒加载bottomGradientView
 @return bottomGradientView
 */
-(UIView *)bottomGradientView{
    if (!_bottomGradientView) {
        float height=self.TKHeight-self.TKTop+54;
        float y=self.TKHeight-height;
        _bottomGradientView = [[UIView alloc]initWithFrame:CGRectMake(0, y, self.TKWidth, height)];
        
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.frame = _bottomGradientView.bounds;
        // 渐变色颜色数组,可多个
        gradientLayer.colors = [NSArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.0f].CGColor, (id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.7f].CGColor, nil];
        gradientLayer.startPoint = CGPointMake(0.5, 0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        [_bottomGradientView.layer addSublayer:gradientLayer];
    }
    return _bottomGradientView;
}


/**
 <AUTHOR> 2020年02月27日18:54:39
 @初始化懒加headImgView
 @return 拍照取景头像
 */
-(UIView *)photoBgView{
    if (!_photoBgView) {
        _photoBgView=[[UIImageView alloc] initWithFrame:self.frame];
        [_photoBgView setBackgroundColor:[TKUIHelper colorWithHexString:@"#FFFFFF"]];
        _photoBgView.userInteractionEnabled=YES;
    }
    return _photoBgView;
}

/**
 <AUTHOR> 2019年04月13日11:36:47
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)photoBgBackBtn{
    if (!_photoBgBackBtn) {
        float backBtnX=15;
        float backBtnY=10+STATUSBAR_HEIGHT;
        float backBtnWidth=24;
        float backBtnheight=24;
        _photoBgBackBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_end_back.png", TK_OPEN_RESOURCE_NAME]];
        [_photoBgBackBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来
        
        //设计稿返回按钮颜色固定
        img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [_photoBgBackBtn setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        [_photoBgBackBtn setImage:img forState:UIControlStateNormal];

        _photoBgBackBtn.titleLabel.font=[UIFont fontWithName:@"PingFangSC-Medium" size:17];
        [_photoBgBackBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _photoBgBackBtn;
}

/**
 <AUTHOR> 2019年04月17日20:28:31
 @初始化懒加载视频展示人像视图背景
 @return 视频展示人像视图背景
 */
-(UIView *)videoShowBgView{
    if (!_videoShowBgView) {
        float videoShowBgViewX=20;
        float videoShowBgViewWidth=self.TKWidth-videoShowBgViewX*2;
        float videoShowBgViewY=self.backBtn.TKBottom+32;
        float videoShowBgViewHeight=self.bigTipLabel.TKTop-videoShowBgViewY-10;
        
        _videoShowBgView=[[UIView alloc] initWithFrame:CGRectMake(videoShowBgViewX, videoShowBgViewY, videoShowBgViewWidth, videoShowBgViewHeight)];
        _videoShowBgView.layer.cornerRadius=8.0f;
        _videoShowBgView.backgroundColor=[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.08];
        
        float imgWidth;
        float imgHeight;
        float aspectRatio=videoShowBgViewHeight/videoShowBgViewWidth;//高除以宽的比例
        float ratioRequirements=4.0f/3.0f;//高除以宽的要求比例
        if (aspectRatio>ratioRequirements) {
            imgWidth=videoShowBgViewWidth;
            imgHeight=imgWidth*4/3;
        }else{
            imgHeight=videoShowBgViewHeight;
            imgWidth=imgHeight/4*3;
        }
        float imgX=(videoShowBgViewWidth-imgWidth)/2;
        float imgY=(videoShowBgViewHeight-imgHeight)/2;
        UIImageView *imgView=[[UIImageView alloc] initWithFrame:CGRectMake(imgX, imgY, imgWidth, imgHeight)];
        imgView.layer.cornerRadius=10.0f;
        imgView.layer.masksToBounds = YES;
        
        UIView *shadowView=[[UIView alloc] initWithFrame:imgView.frame];
        shadowView.backgroundColor =[TKUIHelper colorWithHexString:@"#FFFFFF"];
        shadowView.layer.cornerRadius=10.0f;

        //添加四个边阴影
        shadowView.layer.shadowColor = [UIColor grayColor].CGColor;//阴影颜色
        shadowView.layer.shadowOffset = CGSizeMake(0, 0);//偏移距离
        shadowView.layer.shadowOpacity = 0.5;//不透明度
        shadowView.layer.shadowRadius = 5.0;//半径
        
        [_videoShowBgView addSubview:shadowView];
        [_videoShowBgView addSubview:imgView];
        self.headImgView=imgView;
        
    }
    return _videoShowBgView;
}




/**
 <AUTHOR> 2019年04月13日13:40:45
 @初始化懒加载大字的提示语
 @return 大字的提示语
 */
-(UILabel *)bigTipLabel{
    if (!_bigTipLabel) {
        _bigTipLabel=[[UILabel alloc] init];
        _bigTipLabel.text = @"请确认影像和声音正确后确认提交";
        _bigTipLabel.font = [UIFont fontWithName:@"PingFangSC-Semibold" size:20];
        _bigTipLabel.textColor = [TKUIHelper colorWithHexString:@"#333333"];
        
        CGSize labelSize=[_bigTipLabel sizeThatFits:CGSizeMake(self.TKWidth, MAXFLOAT)];
        float bigTipLabelX=(self.TKWidth-labelSize.width)/2;
        float bigTipLabelY=self.smallTipLabel.TKTop-20-labelSize.height;
        _bigTipLabel.frame=CGRectMake(bigTipLabelX, bigTipLabelY, labelSize.width, labelSize.height);
    }
    return _bigTipLabel;
}

/**
 <AUTHOR> 2019年04月13日13:49:36
 @初始化懒加载小字的提示语
 @return 小字的提示语
 */
-(UILabel *)smallTipLabel{
    if (!_smallTipLabel) {
        _smallTipLabel=[[UILabel alloc] init];
        NSString *textString;
        if ([TKStringHelper isEmpty:self.requestParam[@"previewDetailTip"]]) {
            textString=@"•  人脸和证件清晰且完整\n•  证件未遮挡脸部\n";
        }else{
            textString=self.requestParam[@"previewDetailTip"];
        }
        
        NSMutableAttributedString * attributedString = [[NSMutableAttributedString alloc] initWithString:textString];
        NSMutableParagraphStyle * paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:8];
        
        [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, [textString length])];
        
        [attributedString addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#333333"] range:NSMakeRange(0, textString.length)];
        [attributedString addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:20] range:NSMakeRange(0, textString.length)];
        
        _smallTipLabel.attributedText=attributedString;
        _smallTipLabel.numberOfLines=0;
        CGSize labelSize=[_smallTipLabel sizeThatFits:CGSizeMake(self.TKWidth, MAXFLOAT)];
        float smallTipLabelX=(self.TKWidth-labelSize.width)/2;
        float smallTipLabelY=self.bottomBtnBgView.TKTop-11-labelSize.height;

        _smallTipLabel.frame=CGRectMake(smallTipLabelX, smallTipLabelY, labelSize.width, labelSize.height);
    }
    return _smallTipLabel;
}

/**
 <AUTHOR> 2020年02月27日18:54:43
 @初始化懒加载bottomBtnBgView
 @return 底部按钮图层
 */
-(UIView *)bottomBtnBgView{
    if (!_bottomBtnBgView) {
        float height=44;
        float y=self.frame.size.height-height-20;
        float width=self.frame.size.width;
        if (ISIPHONEX) {
            y=y-34;
        }
        _bottomBtnBgView=[[UIView alloc] initWithFrame:CGRectMake(0, y, width, height)];
        
        float btnGap=20;
        float btnWidth=(width-btnGap*3)/2;
        
        //重试按钮
        UIButton *retryBtn=[[UIButton alloc] initWithFrame:CGRectMake(btnGap, 0, btnWidth, height)];
        [retryBtn setTitle:@"重新拍摄" forState:UIControlStateNormal];
//        retryBtn.layer.borderWidth=1.0f;
//        retryBtn.layer.borderWidth=1.0f;
//        retryBtn.layer.borderColor=[TKUIHelper colorWithHexString:self.mainColorString].CGColor;
//        [retryBtn setTitleColor:[TKUIHelper colorWithHexString:self.mainColorString] forState:UIControlStateNormal];
//        retryBtn.layer.cornerRadius=5.0f;
        

        retryBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];


        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [retryBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]] forState:UIControlStateNormal];
            [retryBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"] alpha:0.05f]];
        }else{
            [retryBtn setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
            [retryBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
        }
        
        retryBtn.layer.cornerRadius=height/2.0f;
        
        [retryBtn addTarget:self action:@selector(retryAction:) forControlEvents:UIControlEventTouchUpInside];
        [_bottomBtnBgView addSubview:retryBtn];
        
        //提交按钮
        UIButton *submitBtn=[[UIButton alloc] initWithFrame:CGRectMake(width-btnWidth-btnGap, 0, btnWidth, height)];
//        [submitBtn setTitle:@"确认提交" forState:UIControlStateNormal];
//        [submitBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString]];
//        [submitBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
//        submitBtn.layer.cornerRadius=5.0f;
//        
        
        
        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [submitBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]]];
        }else{
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = CGRectMake(0, 0, btnWidth, height);
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius=height/2.0f;
            [submitBtn.layer addSublayer:btoGradientLayer]; //设置颜色渐变
        }

        [submitBtn setTitle:@"确认提交" forState:UIControlStateNormal];
        submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        
        [submitBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        submitBtn.layer.cornerRadius=height/2.0f;
        
        [submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
        [_bottomBtnBgView addSubview:submitBtn];
    }
    return _bottomBtnBgView;
}

@end
