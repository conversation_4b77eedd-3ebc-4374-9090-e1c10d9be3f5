//
//  TKOpenPlugin60028.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/2/19.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKOpenPlugin60028.h"
#import "TKFaceImageViewController.h"
#import "TKFaceImageLandscapeViewController.h"
@interface TKOpenPlugin60028()<TKFaceImageLandscapeResultDelegate,TKFaceImageResultDelegate>

@end

@implementation TKOpenPlugin60028
- (ResultVo *)serverInvoke:(id)param{
    
     dispatch_async(dispatch_get_main_queue(), ^{
         
         //是否在调用插件前展示介绍页面
         if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
             [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Camera)] authCallBacks:nil btnCallBack:^{
                 [self.currentViewCtrl tkIsCameraPermissions:^{
                     UIViewController *viewCtr = [self fetchController:param];
                     [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:viewCtr animated:YES completion:nil];
                 }];
             }];
         }else{
             [self.currentViewCtrl tkIsCameraPermissions:^{
                 UIViewController *viewCtr = [self fetchController:param];
                 [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:viewCtr animated:YES completion:nil];
             }];
         }
     });
    
    
    return nil;
}

#pragma mark - private

- (UIViewController *)fetchController:(NSDictionary *)param {
    NSDictionary *tempParam = (NSDictionary *)param;
    if (![tempParam isKindOfClass:[NSDictionary class]]) {
        tempParam = @{};
    }
    UIViewController *controller = nil;
    if ([[tempParam getStringWithKey:@"takeType"] isEqualToString:@"1"]) {
        controller = [[TKFaceImageLandscapeViewController alloc] initWithParam:[tempParam mutableCopy]];
        ((TKFaceImageLandscapeViewController *)controller).delegate=self;
    } else {
        controller = [[TKFaceImageViewController alloc] initWithParam:[tempParam mutableCopy]];
        ((TKFaceImageViewController *)controller).delegate=self;
    }
   
    return controller;
}

#pragma mark TKFaceImageResultDelegate
//结果信息
-(void)tkFaceImageDidComplete:(NSMutableDictionary *)result{
    [self iosCallJsWithParam:result];
}
@end
