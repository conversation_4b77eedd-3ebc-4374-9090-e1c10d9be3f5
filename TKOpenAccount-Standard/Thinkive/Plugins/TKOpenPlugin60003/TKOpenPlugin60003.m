//
//  TKOpenPlugin60003.m
//  TKApp
//
//  Created by 叶璐 on 15/4/17.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKOpenPlugin60003.h"


@implementation TKOpenPlugin60003

/**
 *  <AUTHOR> 2014-11-27 12:11:58
 *
 *  功能描述：创建公私钥
 *  参考插件：对应phoneGap的createKeyPlugin插件				
 *
 *  @param rodam       10位随机数
 *  @param key         通讯唯一标识
 *  @param userId      用户ID
 *
 *  @return 
 *      code     通讯密码
 *      pkcs10   pkcs10字串
 *      r        生成CODE用到的随机数
 */
-(ResultVo *)serverInvoke:(id)param
{
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    NSString *rodam = reqParam[@"rodam"];
    NSString *key = reqParam[@"key"];
    NSString *userid = reqParam[@"userId"];
    if ([param[@"type"] isEqualToString:@"tw_new"]){
        userid = [NSString stringWithFormat:@"%@_tw_new",userid];
    }
    int keyLength = [reqParam getIntWithKey:@"keyLength"];
    
    ResultVo *resultVo = [[ResultVo alloc]init];
    
    if ([TKStringHelper isEmpty:rodam]) {
        resultVo.errorNo = -6000301;
        resultVo.errorInfo = @"随机数不能为空!";
        return resultVo;
    }
    
    if ([TKStringHelper isEmpty:key]) {
        resultVo.errorNo = -6000302;
        resultVo.errorInfo = @"通讯唯一标识不能为空!";
        return resultVo;
    }
    

    NSLog(@"开始创建私钥以及CSR");
    
    //计算的结果
    NSString *code =  [TKMd5Helper md5Encrypt:key];
    NSMutableString *pkcs10 = [NSMutableString string];
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    int ret;
    
    if(keyLength==2048){
        ret =[TKCertLibHelper createPKCS10:pkcs10 pwd:@"123456" userId:userid keyLength:keyLength];
    }else{
        ret =[TKCertLibHelper createPKCS10:pkcs10 pwd:@"123456" userId:userid];
    }
    
    if (ret == TK_ERR_CREATEP10_OK) {
        [dic setValue:code forKey:@"code"];
        [dic setValue:pkcs10 forKey:@"pkcs10"];
        [dic setValue:rodam forKey:@"r"];
        resultVo.errorNo = 0;
        resultVo.results = dic;
    }
    else
    {
        resultVo.errorNo = -6000303;
        resultVo.errorInfo = @"创建公私钥失败!";
        resultVo.results = dic;
    }
    
    NSLog(@"-----------------pkcs10:%@", pkcs10);
    return resultVo;
}

@end
