//
//  TKOpenPlugin60049.m
//  TKApp_H5SHARE
//
//  Created by <PERSON> on 2020/11/30.
//  Copyright © 2020 liubao. All rights reserved.
//

#import "TKOpenPlugin60049.h"


// 腾讯语音合成
#if __has_include(<QCloudTTS.h>)
#define IsImportQCloudTTS 1
#import <QCloudTTS.h>
#elif __has_include("QCloudTTS.h")
#define IsImportQCloudTTS 1
#import "QCloudTTS.h"
#else
#define IsImportQCloudTTS 0
#endif

// 腾讯语音识别
#if __has_include(<QCloudSDK/QCloudSDK.h>)
#define IsImportQCloudSDK 1
#import <QCloudSDK/QCloudSDK.h>
#else
#define IsImportQCloudSDK 0
#endif

// 百度语音合成
#if __has_include(<BaiduASR/BaiduASR.h>)
#define IsImportBaiduASR 1
#import <BaiduASR/BaiduASR.h>
#else
#define IsImportBaiduASR 0
#endif

// 百度语音识别
#if __has_include(<BaiduTTS/BaiduTTS.h>)
#define IsImportBaiduTTS 1
#import <BaiduTTS/BaiduTTS.h>
#else
#define IsImportBaiduTTS 0
#endif

// 同花顺语音合成
#if __has_include(<THSSpeechSynthesize/THSSpeechSynthesizeDefine.h>)
#define IsImportTHSSpeechSynthesizeDefine 1
#import <THSSpeechSynthesize/THSSpeechSynthesizeDefine.h>
#else
#define IsImportTHSSpeechSynthesizeDefine 0
#endif

// 同花顺语音识别
#if __has_include(<THSVoiceRecognition/THSVoiceRecognitionDefine.h>)
#define IsImportTHSVoiceRecognitionDefine 1
#import <THSVoiceRecognition/THSVoiceRecognitionDefine.h>
#else
#define IsImportTHSVoiceRecognitionDefine 0
#endif


@implementation TKOpenPlugin60049

- (ResultVo *)serverInvoke:(id)param{
    ResultVo *resultVo = [[ResultVo alloc]init];
    self.isSyncPlugin=YES;//插件是同步回调结果
    // 打印sdk信息
    [self.class printSDKVersion];
    
    // 分享
    NSArray *classNameArray;
    NSArray *numberIndexArray;
    param[@"canTKShare"] = [self judgeWhetherCanHandleWithClassNameArray:@[@"TKShareManager"]];

    

    param[@"canTKPay"] = [self judgeWhetherCanHandleWithClassNameArray:@[@"TKH5PayManager"]];

    
    // 身份证OCR
    classNameArray = @[@"TKOpenPlugin60014"];
    param[@"canIDCardOcr"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    classNameArray = @[@"ISIDCardReaderController",
                       @"EXOCRCardEngineManager"];
    numberIndexArray = @[@"1", // 合合
                         @"2"]; // 易道
    param[@"IDCardOSDKType"] = [self convertToSDKTypeWithClassNameArray:classNameArray
                                                       numberIndexArray:numberIndexArray];
    
    // 银行卡OCR
    classNameArray = @[@"TKOpenPlugin60016"];
    param[@"canBankCardOcr"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    classNameArray = @[@"ISBankCardController",
                       @"EXOCRBankRecoManager"];
    numberIndexArray = @[@"1", // 合合
                         @"2"]; // 易道
    param[@"bankCardSDKType"] = [self convertToSDKTypeWithClassNameArray:classNameArray
                                                        numberIndexArray:numberIndexArray];
    
    // Chat SDK
    classNameArray = @[@"AnyChatPlatform",
                       @"TChatCore"];
    numberIndexArray = @[@"1", // AnyChat
                         @"2"]; // TChat
    param[@"canChat"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    param[@"chatSDKType"] = [self convertToSDKTypeWithClassNameArray:classNameArray
                                                        numberIndexArray:numberIndexArray];
    
    // Live Detect SDK
    classNameArray = @[@"HTJCFaceLiveDetectManager",
                       @"STSilentLivenessDetector",
                       @"YtSDKKitFramework",
                       @"FaceSDKManager",
                       @"MGCustomLivenessDetector",
                       @"STLivenessDetector"];
    numberIndexArray = @[@"1", // 易道动作活体
                         @"2", // 商汤静默活体
                         @"3", // 腾讯静默活体
                         @"4", // 百度动作活体
                         @"5", // 旷视动作活体
                         @"6", // 商汤动作活体
    ];
    param[@"canLiveDetect"] = [self judgeWhetherCanHandleWithClassNameArray:@[@"TKOpenPlugin60007"]];
    param[@"liveDetectSDKType"] = [self convertToSDKTypeWithClassNameArray:classNameArray
                                                        numberIndexArray:numberIndexArray];
    //判断是否支持
    Class clazzST = NSClassFromString(@"STResultImage");
    
    if (clazzST) {
        NSObject *obj = [[clazzST alloc] init];
        //支持商汤鉴权
        if ( [obj respondsToSelector: @selector(signature)] == YES ) {
            param[@"liveDetectSDKType"] =[NSString stringWithFormat:@"%@|7",param[@"liveDetectSDKType"]];
        }
    }
    
    // Speech SDK
    classNameArray = @[@"QCloudTTS", // QCloudSDK
                       @"NeoNui",
                       @"BDTTSInstance", // BDASRInstance
                       @"THSSpeechSynthesize", // THSVoiceRecognition
                       @"IFlySetting"];
    numberIndexArray = @[@"1", // 腾讯
                         @"2", // 阿里
                         @"3", // 百度
                         @"4", // 同花顺
                         @"5"]; // 讯飞
    param[@"canSpeech"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    param[@"speechSDKType"] = [self convertToSDKTypeWithClassNameArray:classNameArray
                                                        numberIndexArray:numberIndexArray];
    
    // 双向视频（要排队）
    classNameArray = @[@"TKOpenPlugin60005"];
    param[@"canUseTwoWayVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // 智能单向视频
    classNameArray = @[@"TKOpenPlugin60026"];
    param[@"canUseIntelligentOneWayVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // 新版普通单向视频
    classNameArray = @[@"TKOpenPlugin60030"];
    param[@"canUseNewOneWayVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // 新版双向视频（无排队）
    classNameArray = @[@"TKOpenPlugin60034"];
    param[@"canUseNewTwoWayVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // tchat服务端单向视频
    classNameArray = @[@"TKOpenPlugin60057"];
    param[@"canUseTChatOneWayVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // tchat服务端活体
    classNameArray = @[@"TKOpenPlugin60059"];
    param[@"canUseTChatLiveDetect"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // tchat数字人单向
    classNameArray = @[@"TKOpenPlugin60072"];
    param[@"canUseTChatVirtualManVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // 是否支持使用新版横屏普通单向视频
    classNameArray = @[@"TKOpenPlugin60091"];
    param[@"canUseNewOneWayLandscapeVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // 是否支持使用横屏智能单向视频
    classNameArray = @[@"TKOpenPlugin60089"];
    param[@"canUseIntelligentOneWayLandscapeVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // 是否支持tchat横屏服务端录制
    classNameArray = @[@"TKOpenPlugin60077"];
    param[@"canUseTChatOneWayLandscapeVideo"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    // 是否支持港澳台证件本地ocr识别
    classNameArray = @[@"TKOpenPlugin60074"];
    param[@"canUseGATCardOCR"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    //是否支持微信小程序&企业微信小程序60076插件
    classNameArray = @[@"TKPlugin60076"];
    param[@"canUseWXSmallProgram"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    //是否支持一键登录60041插件
    classNameArray = @[@"TKOpenPlugin60041"];
    param[@"canUseQuickLogin"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];

    //是否支持TChatRtc信创视频
    classNameArray = @[@"RTCCameraPreviewView"];
    param[@"canUseTChatRtc"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    //是否支持原生公版SDK拍照方式（60032）
    classNameArray = @[@"TKOpenPlugin60032"];
    param[@"canUsePublicTakePhoto"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    //是否支持原生公版SDK拍照方式（60032）
    classNameArray = @[@"TKOpenPlugin60800"];
    param[@"canUseDigitalOpen"] = [self judgeWhetherCanHandleWithClassNameArray:classNameArray];
    
    if ([TKStringHelper isEmpty:param[@"queryKey"]]) {
        resultVo.results = param;
    }else{
        //传了查询key，就只有对应结果字符串了，没有字典对象
        NSString *quaryResult=param[param[@"queryKey"]];
        //为空就给空字符串，免得js报undefine
        if ([TKStringHelper isEmpty:quaryResult]) {
            resultVo.results = @"";
        }else{
            resultVo.results = quaryResult;
        }
       
    }
    
    
    return resultVo;
}


+ (void)printSDKVersion
{
// Chat SDK
    if ([self checkClazzIsValid:@"TChatCore"])
        TKLogInfo(@"思迪sdk信息打印：TChat的版本是%@",
              [self TChatVersion]);
    
    if ([self checkClazzIsValid:@"AnyChatPlatform"])
        TKLogInfo(@"思迪sdk信息打印：AnyChat的版本是%@",
              [self AnyChatVersion]);
    
// Video SDK
    if ([self checkClazzIsValid:@"CloudroomVideoSDK"])
        TKLogInfo(@"思迪sdk信息打印：云屋视频SDK的版本是%@",
              [self CloudroomVideoVersion]);
    
// OCR SDK
    if ([self checkClazzIsValid:@"ISIDCardReaderController"])
        TKLogInfo(@"思迪sdk信息打印：合合身份证OCR的版本是%@",
              [self HHIDCardOCRVersion]);
        
    if ([self checkClazzIsValid:@"ISBankCardController"])
        TKLogInfo(@"思迪sdk信息打印：合合银行卡OCR的版本是%@",
              [self HHBankCardOCRVersion]);
    
    if ([self checkClazzIsValid:@"EXOCRCardEngineManager"])
        TKLogInfo(@"思迪sdk信息打印：易道身份证OCR的版本是%@, 易道身份证OCR的kernel Version是%@",
              [self YDICCardOCRVersion],
              [self YDICCardOCRKernelVersion]);
    
    if ([self checkClazzIsValid:@"EXOCRBankRecoManager"])
        TKLogInfo(@"思迪sdk信息打印：易道银行卡OCR的版本是%@, 易道银行卡OCR的kernel Version是%@",
              [self YDBankCardOCRVersion],
              [self YDBankCardOCRKernelVersion]);

// Liveness SDK
    if ([self checkClazzIsValid:@"HTJCFaceLiveDetectManager"])
        TKLogInfo(@"思迪sdk信息打印：易道动作活体的版本是%@,易道动作活体的bundle version是%@",
              [self YDLivenessVersion],
              [self YDLivenessBundleVersion]);
     
    if ([self checkClazzIsValid:@"STLivenessDetector"])
        TKLogInfo(@"思迪sdk信息打印：商汤静默活体的版本是%@,商汤静默活体 library version是%@",
              [self STSilentLivenessVersion],
              [self STSilentLivenessLibraryVersion]);
    
    if ([self checkClazzIsValid:@"YtSDKKitFramework"])
        TKLogInfo(@"思迪sdk信息打印：腾讯静默活体的版本是%@",
              [self TXSilentLivenessVersion]);
     
    if ([self checkClazzIsValid:@"FaceSDKManager"])
        TKLogInfo(@"思迪sdk信息打印：百度动作活体的版本是%@",
              [self BDSilentLivenessVersion]);
    
    if ([self checkClazzIsValid:@"MGCustomLivenessDetector"])
        TKLogInfo(@"思迪sdk信息打印：旷视动作活体的版本是%@,旷视动作活体auth version是%@",
              [self KSSilentLivenessVersion],
              [self KSSilentLivenessAuthVersion]);

// Speech SDK
    if ([self checkClazzIsValid:@"NeoNui"])
        TKLogInfo(@"思迪sdk信息打印：阿里语音合成的版本是%@，阿里语音识别的版本是%@",
              [self ALSpeechSynthesisVersion],
              [self ALSpeechRecognitionVersion]);
     
    if ([self checkClazzIsValid:@"IFlySetting"])
        TKLogInfo(@"思迪sdk信息打印：讯飞语音合成的版本是%@，讯飞语音识别的版本是%@",
              [self XFSpeechSynthesisVersion],
              [self XFSpeechRecognitionVersion]);

    if ([self checkClazzIsValid:@"QCloudTTS"] && [self checkClazzIsValid:@"QCloudBaseRecognizer"])
        TKLogInfo(@"思迪sdk信息打印：腾讯语音合成的版本是%@，腾讯语音识别的版本是%@",
              [self TXSpeechSynthesisVersion],
              [self TXSpeechRecognitionVersion]);
    
    if ([self checkClazzIsValid:@"BDTTSInstance"] && [self checkClazzIsValid:@"BDASRInstance"])
        TKLogInfo(@"思迪sdk信息打印：百度语音合成的版本是%@，百度语音识别的版本是%@",
              [self BDSpeechSynthesisVersion],
              [self BDSpeechRecognitionVersion]);
        
    if ([self checkClazzIsValid:@"THSSpeechSynthesize"] && [self checkClazzIsValid:@"THSVoiceRecognition"])
        TKLogInfo(@"思迪sdk信息打印：同花顺语音合成的版本是%@，同花顺语音识别的版本是%@",
              [self THSSpeechSynthesisVersion],
              [self THSSpeechRecognitionVersion]);

}

#pragma mark - Chat SDK
+ (NSString *)TChatVersion
{
    NSString *className = nil;
    className = @"TChatCore";
    Class clazz = NSClassFromString(className);
    SEL sel = NSSelectorFromString(@"InitSDK");
    
    if (clazz) {
        if ([clazz respondsToSelector:sel]){
            // InitSDK
            IMP imp = [clazz methodForSelector:sel];
            void (*function)(id, SEL) = (void *)imp;
            function(clazz, sel);
            
            int MainVer = 0;
            int SubVer = 0;
            int StageVer = 0;
            NSString *versionTime = @"";
            
            // version
            sel = NSSelectorFromString(@"GetSDKOptionInt:");
            if ([clazz respondsToSelector:sel]){
                
                IMP imp = [clazz methodForSelector:sel];
                int (*function)(id, SEL, int) = (void *)imp; // 带int 参数
                MainVer = function(clazz, sel, 1); // TKCC_SO_CORESDK_MAIN_VERSION
                SubVer = function(clazz, sel, 2); // TKCC_SO_CORESDK_SUB_VERSION
                StageVer = function(clazz, sel, 39); // TKCC_SO_CORESDK_STAGE_VERSION
            }
            
            // versionTime
            sel = NSSelectorFromString(@"GetSDKOptionString:");
            if ([clazz respondsToSelector:sel]){
                IMP imp = [clazz methodForSelector:sel];
                NSString *(*function)(id, SEL, int) = (void *)imp; // 带int 参数
                versionTime = function(clazz, sel, 3); // TKCC_SO_CORESDK_BUILD_TIME
            }
            
            return [NSString stringWithFormat:@"%i.%i.%i-%@", MainVer, SubVer, StageVer, versionTime];
        }
    }
    
    return @"";
}

+ (NSString *)AnyChatVersion
{
    return [self getSDKVersionWithClass:@"AnyChatPlatform" selectorName:@"GetSDKVersion"];
}

#pragma mark - Video SDK
+ (NSString *)CloudroomVideoVersion
{
    return [self getSDKVersionWithClass:@"CloudroomVideoSDK" selectorName:@"getCloudroomVideoSDKVer"];
}

#pragma mark - OCR SDK
+ (NSString *)HHIDCardOCRVersion
{
    return  [self getSDKVersionWithClass:@"ISIDCardReaderController" selectorName:@"getSDKVersion"];
}

+ (NSString *)HHBankCardOCRVersion
{
    return  [self getSDKVersionWithClass:@"ISBankCardController" selectorName:@"getSDKVersion"];
}

+ (NSString *)YDICCardOCRVersion
{
    return  [self getSDKVersionWithClass:@"EXOCRCardEngineManager" selectorName:@"getSDKVersion"];
}

+ (NSString *)YDICCardOCRKernelVersion
{
    return [self getSDKVersionWithClass:@"EXOCRCardEngineManager" selectorName:@"getKernelVersion"];
}

+ (NSString *)YDBankCardOCRVersion
{
    return [self getSDKVersionWithClass:@"EXOCRBankRecoManager" selectorName:@"getSDKVersion"];
}

+ (NSString *)YDBankCardOCRKernelVersion
{
    return [self getSDKVersionWithClass:@"EXOCRBankRecoManager" selectorName:@"getKernelVersion"];
}

#pragma mark - Liveness SDK
+ (NSString *)YDLivenessVersion
{
    // 易道
    Class clazz = NSClassFromString(@"HTJCFaceLiveDetectManager");
    SEL sel = NSSelectorFromString(@"sharedManager:");
    
    if (clazz) {
        if ([clazz respondsToSelector:sel]){
            // init
            IMP imp = [clazz methodForSelector:sel];
            id (*function)(id, SEL, UIViewController *) = (void *)imp; // 带UIViewController * 参数
            id instance = function(clazz, sel, [TKAppEngine shareInstance].rootViewCtr.currentViewCtrl);
            
            // get version
            sel = NSSelectorFromString(@"getSDKVersion");
            if (instance && [instance respondsToSelector:sel]) {
                IMP imp = [instance methodForSelector:sel];
                NSString *(*function)(id, SEL) = (void *)imp;
                NSString *version = function(clazz, sel);
                
                return version;
            }
        }
    }
    
    return @"";
}

+ (NSString *)YDLivenessBundleVersion
{
    // 易道
    Class clazz = NSClassFromString(@"HTJCFaceLiveDetectManager");
    SEL sel = NSSelectorFromString(@"sharedManager:");
    
    if (clazz) {
        if ([clazz respondsToSelector:sel]){
            // init
            IMP imp = [clazz methodForSelector:sel];
            id (*function)(id, SEL, UIViewController *) = (void *)imp; // 带UIViewController * 参数
            id instance = function(clazz, sel, [TKAppEngine shareInstance].rootViewCtr.currentViewCtrl);
            
            // get version
            sel = NSSelectorFromString(@"getBundleVersion");
            if (instance && [instance respondsToSelector:sel]) {
                IMP imp = [instance methodForSelector:sel];
                NSString *(*function)(id, SEL) = (void *)imp;
                NSString *version = function(clazz, sel);
                
                return version;
            }
        }
    }
    
    return @"";
}

+ (NSString *)STSilentLivenessVersion
{
    // 商汤
    return [self getSDKVersionWithClass:@"STLivenessDetector" selectorName:@"getVersion"]; //获取当前 SDK 版本号号
}

+ (NSString *)STSilentLivenessLibraryVersion
{
    // 商汤
    return [self getSDKVersionWithClass:@"STLivenessDetector" selectorName:@"getLibraryVersion"]; //获取当前 SDK 版本号号
}

+ (NSString *)TXSilentLivenessVersion
{
    // 腾讯
    return [self getSDKVersionWithClass:@"YtSDKKitFramework" initSelectorName:@"sharedInstance" selectorName:@"getVersion"]; //获取当前 SDK 版本号号
}

+ (NSString *)BDSilentLivenessVersion
{
    // 百度
    return [self getSDKVersionWithClass:@"FaceSDKManager" initSelectorName:@"sharedInstance" selectorName:@"getVersion"];
}

+ (NSString *)KSSilentLivenessVersion
{
    // 旷视
    return [self getSDKVersionWithClass:@"MGCustomLivenessDetector" selectorName:@"getVersion"];
}

+ (NSString *)KSSilentLivenessAuthVersion
{
    // 旷视
    return [self getSDKVersionWithClass:@"MGCustomLivenessDetector" selectorName:@"getAuthVersion"];
}

#pragma mark - Speech SDK
+ (NSString *)ALSpeechSynthesisVersion
{
    
    // 阿里语音合成、识别
    Class clazz = NSClassFromString(@"NeoNui");
    SEL sel = NSSelectorFromString(@"new");
    
    if (clazz) {
        if ([clazz respondsToSelector:sel]){
            // init
            IMP imp = [clazz methodForSelector:sel];
            id (*function)(id, SEL) = (void *)imp;
            id instance = function(clazz, sel);
            
            // get version
            sel = NSSelectorFromString(@"nui_get_version");
            if (instance && [instance respondsToSelector:sel]) {
                IMP imp = [instance methodForSelector:sel];
                const char *(*function)(id, SEL) = (void *)imp;
                const char *version = function(clazz, sel); // 返回值类型是
                
                return [NSString stringWithFormat:@"%s", version];
            }
        }
    }
    
    return @"";
}

+ (NSString *)ALSpeechRecognitionVersion
{
    // 阿里语音合成、识别
    return [self ALSpeechSynthesisVersion];
}

+ (NSString *)XFSpeechSynthesisVersion
{
    // 讯飞语音合成、识别
    return [self getSDKVersionWithClass:@"IFlySetting" selectorName:@"getVersion"];
}

+ (NSString *)XFSpeechRecognitionVersion
{
    // 讯飞语音合成、识别
    return [self XFSpeechSynthesisVersion];
}

+ (NSString *)TXSpeechSynthesisVersion
{
    // 腾讯语音合成
//#if IsImportQCloudTTS
//    return libQCloudTTSSDKVersionString;
//#endif
    return @"";
}

+ (NSString *)TXSpeechRecognitionVersion
{
    // 腾讯语音识别
//#if IsImportQCloudSDK
//    return [NSString stringWithFormat:@"%f", QCloudSDKVersionNumber];
//#endif
    return @"";
}

+ (NSString *)BDSpeechSynthesisVersion
{
    // 百度语音
//#if IsImportBaiduTTS
//    return [NSString stringWithFormat:@"%f", BaiduTTSVersionNumber];
//#endif
    return @"";
}

+ (NSString *)BDSpeechRecognitionVersion
{
//#if IsImportBaiduASR
//    return [NSString stringWithFormat:@"%f", BaiduASRVersionNumber];
//#endif
    return @"";
}

+ (NSString *)THSSpeechSynthesisVersion
{
//#if IsImportTHSSpeechSynthesizeDefine
//    return [NSString stringWithFormat:@"%@", TTSVersionName];
//#endif
    return @"";
}

+ (NSString *)THSSpeechRecognitionVersion
{
//#if IsImportTHSVoiceRecognitionDefine
//    return ShortASRVersionName;
//#endif
    return @"";
}





#pragma mark - Private
// 类方法获取版本信息
+ (NSString *)getSDKVersionWithClass:(NSString *)className selectorName:(NSString *)selectorName
{
    Class clazz = NSClassFromString(className);
    SEL sel = NSSelectorFromString(selectorName);
    
    if (clazz) {
        if ([clazz respondsToSelector:sel]){
            IMP imp = [clazz methodForSelector:sel];
            NSString *(*function)(id, SEL) = (void *)imp;
            NSString *versionTime = function(clazz, sel);
            
            return versionTime;
        }
    }
    
    return @"";
}

// 对象方法获取版本
+ (NSString *)getSDKVersionWithClass:(NSString *)className initSelectorName:(NSString *)initSelectorName selectorName:(NSString *)selectorName
{
    Class clazz = NSClassFromString(className);
    SEL sel = NSSelectorFromString(initSelectorName);
    
    if (clazz) {
        if ([clazz respondsToSelector:sel]){
            // init
            IMP imp = [clazz methodForSelector:sel];
            id (*function)(id, SEL) = (void *)imp;
            id instance = function(clazz, sel);
            
            // get version
            sel = NSSelectorFromString(selectorName);
            if (instance && [instance respondsToSelector:sel]) {
                IMP imp = [instance methodForSelector:sel];
                NSString *(*function)(id, SEL) = (void *)imp;
                NSString *version = function(clazz, sel); // 返回值是NSString *
                
                return version;
            }
        }
    }
    
    return @"";
}

// 检查类是否可用
+ (BOOL)checkClazzIsValid:(NSString *)className
{
    Class clazz = NSClassFromString(className);
    return clazz != nil;
}

/// 是否能够处理某个业务。返回 0-不能  1-能
/// @param array 处理某个业务的sdk主要类文件名数组
- (NSString *)judgeWhetherCanHandleWithClassNameArray:(NSArray *)array
{
    BOOL canHandle = NO;
    for (NSString *className in array) {
        if ([className isKindOfClass:NSString.class]) {
            canHandle = [self.class checkClazzIsValid:className];
            
            if (canHandle) break; // 一旦成功跳出循环
        }
    }
    return canHandle ? @"1" : @"0";
}

/// 转换成  1 | 2 | 3 样式。1、2、3实际上指某个具体的sdk。比如腾讯活体 | 商汤活体 | 易道活体
/// @param array 处理某个业务的sdk主要类文件名数组。
/// @param numberIndexArray 对应的数字索引的数组
- (NSString *)convertToSDKTypeWithClassNameArray:(NSArray *)array numberIndexArray:(NSArray *)numberIndexArray
{
    if (array.count != numberIndexArray.count) {
        TKLogDebug(@"convertToSDKTypeWithClassNameArray 传入的数组数量不一致");
        return @"0";
    }
    
    NSString *tempStr = @"";
    NSString *className = nil;
    for (int i = 0; i < array.count; i++) {
        className = array[i];
        if ([className isKindOfClass:NSString.class]) {
            if ([self.class checkClazzIsValid:className]) { // 该sdk存在
                if ([TKStringHelper isEmpty:tempStr]) { // 第一次拼接
                    tempStr = [NSString stringWithFormat:@"%@", numberIndexArray[i]];
                } else {    // 已拼结
                    tempStr = [tempStr stringByAppendingFormat:@"|%@", numberIndexArray[i]];
                }
            }
        }
    }

    return [TKStringHelper isEmpty:tempStr] ? @"0" : tempStr;
}

@end


/*
 anychat获取版本号：
    NSString *version=[AnyChatPlatform GetSDKVersion];

 Tchat获取版本号（必须先初始化，下班是主板吧从版本小版本时间可以拼接组合）：

     [TChatCore InitSDK];
     int MainVer  =[TChatCore GetSDKOptionInt:1];
     int SubVer  =[TChatCore GetSDKOptionInt:2];
     int StageVer  =[TChatCore GetSDKOptionInt:39];
     NSString *versionTime=[TChatCore GetSDKOptionString:3];

 云屋视频获取版本号：
     //获取当前 SDK 版本号号
     NSString * bundleVersion =[CloudroomVideoSDK getCloudroomVideoSDKVer];

 合合身份证获取版本号：
     NSString * version =[ISIDCardReaderController getSDKVersion];

 合合银行卡获取版本号：
     NSString * version =[ISBankCardController getSDKVersion];

 易道身份证获取版本号：
    //SDK版本号
    NSString * version =[EXOCRCardEngineManager getSDKVersion];
    //识别核心版本号
    NSString * kernelVersion =[EXOCRCardEngineManager getKernelVersion];

 易道银行卡获取版本号：
    //SDK版本号
    NSString * version =[EXOCRBankRecoManager getSDKVersion];
    //识别核心版本号
    NSString * kernelVersion =[EXOCRBankRecoManager getKernelVersion];

 易道动作活体获取版本号：
    HTJCFaceLiveDetectManager *manager = [HTJCFaceLiveDetectManager sharedManager:[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl];
     //获取算法SDK版本号
     NSString * bundleVersion =[manager getBundleVersion];
     //获取framework版本号
     NSString * SDKVersion =[manager getSDKVersion];

 商汤静默活体获取版本号：
     //获取当前 SDK 版本号号
     NSString * version =[STSilentLivenessDetector getVersion];
     //获取底层库版本号
     NSString * libraryVersion =[STSilentLivenessDetector getLibraryVersion];

 腾讯静默活体获取版本号：
     //获取当前 SDK 版本号号
     NSString * version =[[YtSDKKitFramework sharedInstance] getVersion];

 百度动作活体获取版本号：
     //获取当前 SDK 版本号号
     NSString * version =[[FaceSDKManager sharedInstance] getVersion];

 旷视动作活体获取版本号：
     //获取版本信息
     NSString * version =[MGCustomLivenessDetector getVersion];
     NSString * authVersion=[MGCustomLivenessDetector getAuthVersion];

 腾讯语音合成获取版本号：
    //没有获取版本号方法，只有一个变量取值
    QCloudTTS：libQCloudTTSSDKVersionString

 阿里语音合成获取版本号：
    //获取SDK版本号
     NeoNui  *nui = [[NeoNui alloc] init];
     const char *version = [nui nui_get_version]    ;
 讯飞语音合成获取版本号：
    //获取SDK版本号
    NSString * version =[IFlySetting getVersion];

 百度语音合成获取版本号：
    //没有获取版本号方法，只有一个变量取值
    BDTTSInstance：BaiduTTSVersionNumber

 同花顺语音合成获取版本号：
    //没有获取版本号方法，只有一个变量取值
    THSSpeechSynthesizeDefine：TTSVersionName

 腾讯语音识别获取版本号：
     //没有获取版本号方法，只有一个变量取值
     QCloudSDK：QCloudSDKShortVersionString

 阿里语音识别获取版本号：
    //获取SDK版本号
     NeoNui  *nui = [[NeoNui alloc] init];
     [nui nui_get_version];

 讯飞语音识别获取版本号：
    //获取SDK版本号
    NSString * version =[IFlySetting getVersion];

 百度语音识别获取版本号：
    //没有获取版本号方法，只有一个变量取值
    BDASRInstance：BaiduASRVersionNumber

 同花顺语音识别获取版本号：
    //没有获取版本号方法，只有一个变量取值
    THSVoiceRecognitionDefine：ShortASRVersionName
 */

/*
 思迪sdk信息打印：TChat的版本是5.2.0-Mar 19 2020
 思迪sdk信息打印：AnyChat的版本是V7.3 Build time:Feb 18 2019 10:30:51
 思迪sdk信息打印：云屋视频SDK的版本是3.10.5
 思迪sdk信息打印：合合身份证OCR的版本是IntSig_iOS_ISIDReaderPreviewSDK_v6.5.0.************.5635
 思迪sdk信息打印：合合银行卡OCR的版本是IntSig_iOS_ISBankCardSDK_v3.3.2.************.4793
 思迪sdk信息打印：易道身份证OCR的版本是SDKVersion:4.2.13, 易道身份证OCR的kernel Version是KernelVersion:4.0.3.1;[LTD:2018-12-31];[AUZ:易道博识身份证识别Android/IOS/服务器平台演示版本]
 思迪sdk信息打印：易道银行卡OCR的版本是SDKVersion:3.3.2, 易道银行卡OCR的kernel Version是KernelVersion:4.0.1.21;[LTD:2048-12-31];[AUZ:copyright.exocr.com.]
 思迪sdk信息打印：易道动作活体的版本是S1.0.1.5746,易道动作活体的bundle version是1.3.1
 思迪sdk信息打印：商汤静默活体的版本是1.0.6(1),商汤静默活体 library version是0.4.7(8c68278)
 思迪sdk信息打印：腾讯静默活体的版本是1.0.9.14
 思迪sdk信息打印：百度动作活体的版本是4.0
 思迪sdk信息打印：旷视动作活体的版本是MegLive v2.4.7I,2100-12-31,旷视动作活体auth version是
 思迪sdk信息打印：阿里语音合成的版本是V2.5.0-01B-********，阿里语音识别的版本是V2.5.0-01B-********
 思迪sdk信息打印：讯飞语音合成的版本是1.0.1227.1180，讯飞语音识别的版本是1.0.1227.1180
 思迪sdk信息打印：百度语音合成的版本是1.000000，百度语音识别的版本是0.000000
 思迪sdk信息打印：腾讯语音合成的版本是v1.2.8，腾讯语音识别的版本是1.000000
 思迪sdk信息打印：同花顺语音合成的版本是2.5.1.1，同花顺语音识别的版本是3.2.0
 */
