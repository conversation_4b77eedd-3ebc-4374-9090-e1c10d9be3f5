//
//  TKOpenPlugin60032.m
//  TKOpenAccount-Standard
//
//  Created by Vie on 2020/3/13.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKOpenPlugin60032.h"
#import "TKTakeIDPhotoViewController.h"
#import "TKCommonUtil.h"
#import <Photos/Photos.h>
#import <MobileCoreServices/UTCoreTypes.h>


@interface TKOpenPlugin60032()<UINavigationControllerDelegate,UIImagePickerControllerDelegate,TKTakeIDPhotoResultDelegate>
{
     ResultVo *_resultVo;// 返回的结果
    
     NSMutableDictionary *h5Params;
}

@end

@implementation TKOpenPlugin60032


- (ResultVo *)serverInvoke:(id)param{
    
 
    
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    reqParam[@"isUpload"]=@"0";

    h5Params = reqParam;
    
    // 检测入参是否有误
    if ([self IsErrorParam:reqParam]) {
        return _resultVo;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        //是否在调用插件前展示介绍页面
        if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
            NSMutableArray *authArray=[[NSMutableArray alloc] init];
            [authArray addObject:@(TKAuthorizationType_Camera)];
            if ([param[@"isAlbum"] integerValue] != 0||[TKStringHelper isEmpty:param[@"isAlbum"]]) {
                [authArray addObject:@(TKAuthorizationType_Photo)];
            }
            [TKAuthorizationHelper requestAuthorization:authArray authCallBacks:nil btnCallBack:^{
                if ([@"phone" isEqualToString:param[@"action"]]) {
                    
                    [self.currentViewCtrl tkIsPhotoLibraryPermissions:^{
                        [self openAlbum];
                    }];
                    
                    
                }else{
                    [self.currentViewCtrl tkIsCameraPermissions:^{
                        [self handleAuthorized:h5Params];
                    }];
                }
            }];
        }else{
            if ([@"phone" isEqualToString:param[@"action"]]) {
                
                [self.currentViewCtrl tkIsPhotoLibraryPermissions:^{
                    [self openAlbum];
                }];
                
                
            }else{
                [self.currentViewCtrl tkIsCameraPermissions:^{
                    [self handleAuthorized:h5Params];
                }];
            }
        }
        
        
        
    });
    
    
    
    return _resultVo;
}

- (BOOL)IsErrorParam:(NSMutableDictionary *)reqParam
{
    ResultVo *resultVo = [[ResultVo alloc] init];
    
    _resultVo = resultVo;

    if ([TKStringHelper isEmpty:@"imgType"]) {
        
        resultVo.errorNo = -6003201;
        resultVo.errorInfo = @"证件类型不能为空";
        return YES;
        
    }
    return NO;
}

- (void)handleAuthorized:(id)param
{
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    
    NSArray* tImageType;
    
    if ([[NSString stringWithFormat:@"%@",param[@"imgType"]] rangeOfString:@","].location != NSNotFound) {
        
        tImageType = [param[@"imgType"] componentsSeparatedByString:@","];
        
    }else{
        
        tImageType = @[[NSString stringWithFormat:@"%@",param[@"imgType"]]];
    }
    
  
    
    TKTakeIDPhotoViewController *tCtl = [[TKTakeIDPhotoViewController alloc] init];
    
    tCtl.param = param;
    tCtl.delegate=self;
 
    [self.currentViewCtrl presentViewController:tCtl animated:YES completion:nil];
        
   
}

/**
 *  Description 打开相册
 */
- (void)openAlbum{
    
    UIImagePickerController* cameraPicker = [[UIImagePickerController alloc] init];
    cameraPicker.delegate = self;
    cameraPicker.allowsEditing = NO; // 不需要编辑图片
    cameraPicker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    NSArray* mediaArray = [NSArray arrayWithObjects:(NSString*)kUTTypeImage, nil];
    cameraPicker.mediaTypes = mediaArray;
    cameraPicker.modalPresentationStyle=UIModalPresentationFullScreen;
    [self.currentViewCtrl presentViewController:cameraPicker animated:YES completion:nil];

}

#pragma mark - ImagePicker代理
//  成功获得相片还是视频后的回调
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info
{
    [picker dismissViewControllerAnimated:YES completion:^{
        // IMAGE TYPE  仅仅允许图片。。
        NSString* mediaType = [info objectForKey:UIImagePickerControllerMediaType];
        
        if ([mediaType isEqualToString:(NSString*)kUTTypeImage])
        {
            // get the image
            UIImage* image = [info objectForKey:UIImagePickerControllerOriginalImage];
            if (picker.allowsEditing && [info objectForKey:UIImagePickerControllerEditedImage]) {// 可编辑的图片
                image = [info objectForKey:UIImagePickerControllerEditedImage];
            }
            
            NSData* data =[TKImageHelper compressImageData:image toByte:(h5Params[@"compressSize"]? [h5Params[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];

                
                [self showImage:data];
            
        }
    }];
}
- (void)showImage:(NSData *)imageData
{
    NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:imageData];
    NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
    reqParam[@"funcNo"]=@"60050";
    reqParam[@"error_no"]=@"0";
    reqParam[@"tkuuid"]=h5Params[@"tkuuid"];
    if ([@"4" isEqualToString:[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]]){
        reqParam[@"frontBase64"]=base64;
    }else{
        reqParam[@"backBase64"]=base64;
    }
    
    [self iosCallJsWithParam:reqParam];
}

//取消照相机的回调
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [self.currentViewCtrl dismissViewControllerAnimated:YES completion:nil];
    
    TKLogInfo(@"取消照相机的回调");
}

- (void)tkTakeIDCardDidComplete:(id)tResult
{
    TKLogInfo(@"拍照回调h5");

    if (tResult) {
        [self iosCallJsWithParam:tResult];
    }
}
@end

