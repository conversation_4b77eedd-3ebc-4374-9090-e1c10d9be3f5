//
//  MTakePhotoViewController.h
//  iosOpenP
//
//  Created by <PERSON>lover on 15/7/23.
//
//

#import <UIKit/UIKit.h>

@protocol TKTakeIDPhotoResultDelegate <NSObject>

- (void)tkTakeIDCardDidComplete:(id)tResult;

@end
/**
 *  Description 拍摄身份证正反面（上传服务器进行OCR识别）
 */
@interface TKTakeIDPhotoViewController : TKBaseViewController

@property (nonatomic, assign) id<TKTakeIDPhotoResultDelegate> delegate;

@property (nonatomic, assign) NSInteger tType;

@property (nonatomic, retain) NSMutableDictionary *param;

- (IBAction)btnOnClicked:(id)sender;

@end
