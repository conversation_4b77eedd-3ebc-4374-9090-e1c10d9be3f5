//
//  TKTakeIDPhotoViewController.m
//  iosOpenP
//
//  Created by <PERSON><PERSON> on 15/7/23.
//
//

#import "TKTakeIDPhotoViewController.h"
#import <MobileCoreServices/UTCoreTypes.h>
#import "TKOpenAccountService.h"
#import <AssetsLibrary/AssetsLibrary.h>
#import <Photos/Photos.h>
#import "UIViewController+TKAuthorityKit.h"
#import "TKAVCaptureManager.h"
#import "TKCommonUtil.h"

#define kParam_uuid       @"uuid"
#define kParam_userId     @"userId"
#define kParam_r          @"r"
#define kParam_imgType    @"imgType"
#define kParam_funcNum    @"funcNum"
#define kParam_photoType  @"photoType"
#define kParam_action     @"action"
#define kParam_url        @"url"
#define kParam_clientInfo @"clientInfo"
#define kParam_jsessionId @"jsessionId"
#define kParam_key        @"key"

#define TKCARD_AspectRatio 0.631915 //身份证高/宽的比例

@interface TKTakeIDPhotoViewController ()<UINavigationControllerDelegate,UIImagePickerControllerDelegate,TKMBProgressHUDDelegate>
{
    TKAVCaptureManager *captureManager;
    
    BOOL isTakePhoto , flag, isRotate, isReject, isPhotoAlbum;
    
    UIView *tCView, *pCView;
    
    TKOpenAccountService *mService;
    
    UIImage *uImage;
    
    TKMBProgressHUD *mHUD;
    
    NSArray *tImageType;
    
    NSMutableDictionary *reqParam;
    
    NSInteger hStep;
    
    NSMutableArray *base64Arr;//多拍的图片数组
}
@property(nonatomic,assign) CGRect  idRect,windoRect;//照片裁剪指定卡片区域,手机屏幕区域
@property(nonatomic,strong) UIView *topView,*leftView,*rightView,*bottomView;//顶部遮罩层,左侧遮罩层,右侧遮罩层,底部遮罩层
@property(nonatomic,strong) UIImageView *idAreaView;//指定扫描区域视图
@property (nonatomic, assign) BOOL isTakeClick;//是否点击过拍照
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）

@end

@implementation TKTakeIDPhotoViewController



- (void)viewDidLoad {
    
    [super viewDidLoad];
    
    [[UIApplication sharedApplication] setStatusBarHidden:YES];
    self.windoRect=CGRectMake(0, 0, self.view.TKWidth, self.view.TKHeight);
    
    mService = [[TKOpenAccountService alloc] init];
    
    reqParam = [NSMutableDictionary dictionary];
    
    base64Arr=[[NSMutableArray alloc] init];
    
    hStep=0;
    if (_param) {
        
        if ([TKStringHelper isEmpty:_param[@"mainColor"]]) {
            self.mainColorString=TKCARD_MAIN_COLOR;
        }else{
            self.mainColorString=_param[@"mainColor"];
        }
        
        NSRange range = [[NSString stringWithFormat:@"%@",_param[@"imgType"]] rangeOfString:@","];

        if (range.length  > 0) {

            tImageType = [_param[@"imgType"] componentsSeparatedByString:@","];

        }else{

            tImageType = @[[NSString stringWithFormat:@"%@",_param[@"imgType"]]];

        }
        
    }
    
    [self initTakeCardViewNew];
    

}

- (BOOL)prefersStatusBarHidden
{
    return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    if ([self respondsToSelector:@selector(setNeedsStatusBarAppearanceUpdate)]) {
        
        [self prefersStatusBarHidden];
        
        [self performSelector:@selector(setNeedsStatusBarAppearanceUpdate)];
        
    }
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
}


#pragma 初始化拍照界面
- (void)initTakeCardViewNew{
    self.isTakeClick=NO;
    int h, w;
    
    w = self.view.TKWidth;
    
    h  = self.view.TKHeight;
    
    CGRect tFrame = CGRectMake(0, 0, w, h);
    
    tCView = [[UIView alloc] initWithFrame:tFrame];
    
    float areaX=30;//卡片指定区域X坐标
    //这里因为是竖屏布局，所以宽高比反着来
    float areaWidth=self.view.TKWidth-areaX*2;//卡片指定区域宽度
    float areaHeight=areaWidth/TKCARD_AspectRatio;//卡片指定区域高度,银行卡高/宽=0.63060748
    float areaY=(self.view.TKHeight-areaHeight)/2;//卡片指定区域Y坐标
    float viewAlpha=0.5;//遮罩透明度
    self.idRect=CGRectMake(areaX, areaY, areaWidth, areaHeight);
    
    self.idAreaView=[[UIImageView alloc] initWithFrame:self.idRect];
    UIImage *idAreaImg;
    if ([[tImageType objectAtIndex:hStep] intValue]==5) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_card_frame_new_portrait_new_reverse.png", TK_OPEN_RESOURCE_NAME]];
    }else  if ([[tImageType objectAtIndex:hStep] intValue]==60) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60032/tk_card_gat_front.png", TK_OPEN_RESOURCE_NAME]];
    }else  if ([[tImageType objectAtIndex:hStep] intValue]==61) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60032/tk_card_gat_back.png", TK_OPEN_RESOURCE_NAME]];
    }else  if ([[tImageType objectAtIndex:hStep] intValue]==70) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60032/tk_card_gat_front.png", TK_OPEN_RESOURCE_NAME]];
    }else  if ([[tImageType objectAtIndex:hStep] intValue]==71) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60032/tk_card_gat_back.png", TK_OPEN_RESOURCE_NAME]];
    }else  if ([[tImageType objectAtIndex:hStep] intValue]==80) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60032/tk_card_gat_front.png", TK_OPEN_RESOURCE_NAME]];
    }else  if ([[tImageType objectAtIndex:hStep] intValue]==81) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60032/tk_card_gat_back.png", TK_OPEN_RESOURCE_NAME]];
    }else  if ([[tImageType objectAtIndex:hStep] intValue]==9) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60032/tk_card_gat_back.png", TK_OPEN_RESOURCE_NAME]];
    }else{
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_card_frame_new_portrait_new_front.png", TK_OPEN_RESOURCE_NAME]];
    }
    if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
        idAreaImg = [idAreaImg imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [self.idAreaView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
    }
    [self.idAreaView setImage:idAreaImg];
    
    self.topView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.TKWidth, areaY)];
    self.topView.backgroundColor=[UIColor blackColor];
    self.topView.alpha=viewAlpha;
    
    self.leftView=[[UIView alloc] initWithFrame:CGRectMake(0, areaY, areaX, areaHeight)];
    self.leftView.backgroundColor=[UIColor blackColor];
    self.leftView.alpha=viewAlpha;
    
    self.rightView=[[UIView alloc] initWithFrame:CGRectMake(areaWidth+areaX, areaY, areaX, areaHeight)];
    self.rightView.backgroundColor=[UIColor blackColor];
    self.rightView.alpha=viewAlpha;
    
    self.bottomView=[[UIView alloc] initWithFrame:CGRectMake(0, areaHeight+areaY, self.view.TKWidth, self.view.TKHeight-areaY-areaHeight)];
    self.bottomView.backgroundColor=[UIColor blackColor];
    self.bottomView.alpha=viewAlpha;
    
    [tCView addSubview:self.idAreaView];
    [tCView addSubview:self.topView];
    [tCView addSubview:self.leftView];
    [tCView addSubview:self.rightView];
    [tCView addSubview:self.bottomView];
    
    CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
    
    
    //提示lable
    UILabel *uLable =[[UILabel alloc] init];
    
    uLable.textColor = [UIColor whiteColor];
    NSString *tipStr;
    NSRange tipRange;
    
    if ([[tImageType objectAtIndex:hStep] intValue]==5) {
        tipStr = @"将线框对准 身份证国徽面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"身份证国徽面"];
    }else if ([[tImageType objectAtIndex:hStep] intValue]==60) {
        tipStr = @"将线框对准 通行人像面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"通行人像面"];
    }else if ([[tImageType objectAtIndex:hStep] intValue]==61) {
        tipStr = @"将线框对准 通行证背面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"通行证背面"];
    }else if ([[tImageType objectAtIndex:hStep] intValue]==70) {
        tipStr = @"将线框对准 居住证人像面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"居住证人像面"];
    }else if ([[tImageType objectAtIndex:hStep] intValue]==71) {
        tipStr = @"将线框对准 居住证国徽面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"居住证国徽面"];
    }else if ([[tImageType objectAtIndex:hStep] intValue]==80) {
        tipStr = @"将线框对准 居留证人像面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"居留证人像面"];
    }else if ([[tImageType objectAtIndex:hStep] intValue]==81) {
        tipStr = @"将线框对准 居留证国徽面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"居留证国徽面"];
    }else if ([[tImageType objectAtIndex:hStep] intValue]==9) {
        tipStr = @"将线框对准 营业执照 再点击拍照";
        tipRange=[tipStr rangeOfString:@"营业执照"];
    }else{

        tipStr= @"将线框对准 身份证人像面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"身份证人像面"];
    }
    
    //顶部提示用富文本
    NSMutableAttributedString *tipAttribut=[[NSMutableAttributedString alloc] initWithString:tipStr];
    
    // 2.添加属性
    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        [tipAttribut addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:20.0f] range:NSMakeRange(0, tipStr.length)];
    }else{
        [tipAttribut addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:18.0f] range:NSMakeRange(0, tipStr.length)];
    }
    
    uLable.attributedText=tipAttribut;
    
    if (self.param[@"topTips"]) {
        
        NSString *divString=[NSString stringWithFormat:@"<span style=\"font-size:18px;color:#ffffff;font-family:PingFang SC\">%@</span>",self.param[@"topTips"]];
       
       NSData *data = [divString dataUsingEncoding:NSUnicodeStringEncoding];
       NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                                 NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
       };
        uLable.attributedText = [[NSMutableAttributedString alloc]initWithData:data options:options documentAttributes:nil error:nil];
    }
    
    uLable.textAlignment = NSTextAlignmentCenter;
    
    CGSize uLableSize = [uLable sizeThatFits:CGSizeMake(areaHeight, areaX)];
    uLable.frame=CGRectMake(0, 0, uLableSize.width, uLableSize.height);
    
    uLable.transform = transform;
    
    [uLable setFrameX:(tCView.TKWidth-6-uLableSize.height)];
    [uLable setFrameY:((tCView.TKHeight-uLableSize.width)/2)];
    [tCView addSubview:uLable];
    
    
    //拍照按钮
    UIButton *tpBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    tpBtn.tag = 101;
    
    [tpBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
    
    [tpBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_selected_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateHighlighted];
    
    float tpBtnWidth=62;
    tpBtn.frame = CGRectMake((w - tpBtnWidth)/2, h-(self.idRect.origin.y-tpBtnWidth)/2-tpBtnWidth, tpBtnWidth, tpBtnWidth);
    
    [tpBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [tpBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [tpBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [tCView addSubview:tpBtn];
    
    
    //相册按钮
    UIButton *aBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    aBtn.tag = 100;
    
    
    [aBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/album_btn_icon.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
    [aBtn setImageEdgeInsets:UIEdgeInsetsMake(11, 12, 11, 12)];
    
    float aBtnWidth=48;
    aBtn.frame = CGRectMake(20+areaX+6, (areaY-aBtnWidth)/2, aBtnWidth, aBtnWidth);
    aBtn.layer.cornerRadius=aBtn.TKWidth/2;
    [aBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f]];
    
    [aBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [aBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [aBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [tCView addSubview:aBtn];
    
    //相册按钮的文字提示
    UILabel *aLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, 28, 20)];;
    aLabel.text = @"相册";
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        [aLabel setTKHeight:28];
        [aLabel setTKWidth:40];
        aLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
    }else{
        aLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
    }
    
    aLabel.textColor=[UIColor whiteColor];
    aLabel.transform=transform;
    [aLabel setFrameX:self.idRect.origin.x];
    [aLabel setFrameY:((self.idRect.origin.y-aLabel.TKHeight)/2)];
    [tCView addSubview:aLabel];
    
    if (_param[@"isAlbum"] && [_param[@"isAlbum"] integerValue] == 0) {
        [aLabel setHidden:YES];
        [aBtn setHidden:YES];
    }else{
        
        [aLabel setHidden:NO];
        [aBtn setHidden:NO];
    }
    
    
    //返回按钮
    UIButton *bBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    bBtn.tag = 102;
    
    [bBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/back_btn_icon.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
    
    [bBtn setImageEdgeInsets:UIEdgeInsetsMake(14, 12, 19, 12)];
    
    float bBtnWidth=48;
    bBtn.frame = CGRectMake(w-areaX-bBtnWidth, (areaY-bBtnWidth)/2, bBtnWidth, bBtnWidth);
    bBtn.layer.cornerRadius=bBtn.frame.size.width/2;
    [bBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f]];
    
    [bBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [bBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [bBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [tCView addSubview:bBtn];
    
    //返回按钮的文字提示
    UILabel *bLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, 28, 20)];
    bLabel.text = @"返回";
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        [bLabel setTKHeight:28];
        [bLabel setTKWidth:40];
        bLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
    }else{
        bLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
    }
    
    bLabel.textColor=[UIColor whiteColor];
    bLabel.transform=transform;
    [bLabel setFrameX:(tCView.TKWidth-self.idRect.origin.x-bBtnWidth-6-bLabel.TKWidth)];
    [bLabel setFrameY:((self.idRect.origin.y-bLabel.TKHeight)/2)];
    [tCView addSubview:bLabel];
    
    
    captureManager = [[TKAVCaptureManager alloc] initWithPreviewView:tCView withCameraPosition:AVCaptureDevicePositionBack withCamraOrientation:AVCaptureVideoOrientationPortrait handleBusinessType:TK_VIDEO_TAKE_PHOTO];
    
    
    [captureManager reTakePicture];
    
    
    [UIView transitionWithView:self.view duration:0.3 options:UIViewAnimationOptionTransitionFlipFromTop animations:^{
        
        [self.view addSubview:tCView];
        
    } completion:^(BOOL finished) {
        
        if (pCView) {
            
            [pCView removeFromSuperview];
            
            pCView = nil;
        }
        
    }];
    

}

#pragma mark -初始化证件预览界面
- (void)initPreviewCardViewNew:(UIImage*)cImage{
    
    int h, w;
    
    CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
    
    self.view.backgroundColor = [TKUIHelper colorWithHexString:@"#232323"];
    
    w =UISCREEN_WIDTH;

    
    h =UISCREEN_HEIGHT;
    
    CGRect fFrame = CGRectMake(0, 0, w, h);
    
    flag = NO;
    
    pCView = [[UIView alloc] initWithFrame:fFrame];
    UIImageView *cardIV;
    
    float leftWidth=20;
    float centerHeight=20;
    if (ISIPHONEX) {
        leftWidth=60;
        centerHeight=49;
    }
    
    if (cImage) {
        if (!isPhotoAlbum) {//裁剪拍照获得的证件照
            float imgHeigt;
            float imgWidth;
            if (cImage.size.width>cImage.size.height) {
                imgWidth=cImage.size.width;
                imgHeigt=cImage.size.height;
            }else{
                imgWidth=cImage.size.height;
                imgHeigt=cImage.size.width;
            }
            CGRect changRect= [self getCuttingArea:CGSizeMake(imgWidth, imgHeigt)];
            //扩大裁剪区域容错
            changRect=CGRectMake(changRect.origin.x-60, changRect.origin.y-60, changRect.size.width+120, changRect.size.height+120);
            cImage=[UIImage imageWithCGImage:CGImageCreateWithImageInRect(cImage.CGImage,changRect)];
        }
        uImage = cImage;
        
        float cardIVWidth=pCView.TKHeight-leftWidth*2;
        float cardIVHeight=pCView.TKWidth-44-13*2-7*2-25;
               
               
        cardIV =[[UIImageView alloc] init];
        cardIV.frame=CGRectMake(0, 0, cardIVWidth, cardIVHeight);
               
               
        cardIV.contentMode = UIViewContentModeScaleAspectFit;
               
        [cardIV setImage:uImage];
               
        cardIV.transform = transform;
        [cardIV setFrameY:leftWidth];
        [cardIV setFrameX:(pCView.TKWidth-40-cardIV.TKWidth)];
        [pCView addSubview:cardIV];
        
    }
    
    
    float cardLabelWdith=300;
    float cardLabelHeight=25;
    
    UILabel *cardLabel = [[UILabel alloc] init];
    cardLabel.frame = CGRectMake(0, 0, cardLabelWdith, cardLabelHeight);
    NSString *tipStr= @"请确保证件照片 文字清晰、边角完整";

    NSRange tipRange=[tipStr rangeOfString:@"文字清晰、边角完整"];
    //顶部提示用富文本
    NSMutableAttributedString *tipAttribut=[[NSMutableAttributedString alloc] initWithString:tipStr];
    // 2.添加属性
    [tipAttribut addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:18] range:NSMakeRange(0, tipStr.length)];
    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#ffffff"] range:NSMakeRange(0, tipStr.length)];
    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
    cardLabel.attributedText=tipAttribut;
    

    cardLabel.transform=transform;
    cardLabel.textAlignment = NSTextAlignmentCenter;
    [cardLabel setFrameY:((cardIV.TKHeight-cardLabelWdith)/2+cardIV.TKTop)];
    [cardLabel setFrameX:(pCView.TKWidth-cardLabelHeight-8)];
    [pCView addSubview:cardLabel];

    
    float btnX=13;
    float btnWidht=214;
    float centerGap=15;//按钮距离垂直中心的距离
    float btnHeight=44;
    
    //提交按钮
    UIButton *submitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    submitBtn.tag = 104;
    
    
    [submitBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    
    [submitBtn setTitle:@"提交" forState:UIControlStateNormal];
    [submitBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString]];
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
    }else{
        submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    }
    [submitBtn.layer setCornerRadius:btnHeight/2.0f];
    
    
    submitBtn.frame =CGRectMake(0, 0, btnWidht,btnHeight);
    
    [submitBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [submitBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [submitBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    submitBtn.transform = transform;
    [submitBtn setFrameX:btnX];
    [submitBtn setFrameY:(pCView.TKHeight/2+centerGap)];
    [pCView addSubview:submitBtn];
    
    //重拍按钮
    UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    cancelBtn.tag = 103;
    
    if (isPhotoAlbum) {
        [cancelBtn setTitle:@"重选" forState:UIControlStateNormal];
    }else{
        [cancelBtn setTitle:@"重拍" forState:UIControlStateNormal];
    }
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
    }else{
        cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    }
    [cancelBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    
    [cancelBtn.layer setBorderWidth:1.0f];
    [cancelBtn.layer setBorderColor:[UIColor whiteColor].CGColor];
    [cancelBtn.layer setCornerRadius:btnHeight/2.0f];
    
    cancelBtn.frame =CGRectMake(0, 0, btnWidht,btnHeight);
    
    [cancelBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [cancelBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [cancelBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    cancelBtn.transform = transform;
    [cancelBtn setFrameX:btnX];
    [cancelBtn setFrameY:(pCView.TKHeight/2-centerGap-btnWidht)];
    
    [pCView addSubview:cancelBtn];
    
    pCView.frame = CGRectOffset(fFrame, 0, -fFrame.size.height);
    [UIView animateWithDuration:0.3f delay:0.0f options:UIViewAnimationOptionCurveEaseInOut animations:^{
        
        [self.view addSubview:pCView];
        
        pCView.center = CGPointMake(pCView.center.x , pCView.center.y + fFrame.size.height);
        
        
    } completion:^(BOOL finished) {
        
        if (tCView) {
            
            [tCView removeFromSuperview];
            
            tCView = nil;
            
        }
        
    }];
}


#pragma mark -初始化证件预览界面带有示例模块
- (void)initPreviewCardNeedSampleViewNew:(UIImage*)cImage{
        
        int h, w;
        
        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
        
        self.view.backgroundColor = [TKUIHelper colorWithHexString:@"#232323"];
        
        w =UISCREEN_WIDTH;

    
        h =UISCREEN_HEIGHT;
        
        CGRect fFrame = CGRectMake(0, 0, w, h);
        
        flag = NO;
        
        pCView = [[UIView alloc] initWithFrame:fFrame];
        UIImageView *cardIV;
        
        float leftWidth=20;
        float centerHeight=20;
        if (ISIPHONEX) {
            leftWidth=60;
            centerHeight=49;
        }
        
        if (cImage) {
            if (!isPhotoAlbum) {//裁剪拍照获得的证件照
                float imgHeigt;
                float imgWidth;
                if (cImage.size.width>cImage.size.height) {
                    imgWidth=cImage.size.width;
                    imgHeigt=cImage.size.height;
                }else{
                    imgWidth=cImage.size.height;
                    imgHeigt=cImage.size.width;
                }
                CGRect changRect= [self getCuttingArea:CGSizeMake(imgWidth, imgHeigt)];
                //扩大裁剪区域容错
                changRect=CGRectMake(changRect.origin.x-60, changRect.origin.y-60, changRect.size.width+120, changRect.size.height+120);
                cImage=[UIImage imageWithCGImage:CGImageCreateWithImageInRect(cImage.CGImage,changRect)];
            }
            uImage = cImage;
            
            float cardIVWidth=pCView.TKHeight-leftWidth*2-225-centerHeight;
            float cardIVHeight=pCView.TKWidth-44-13*2-7*2-25;
                   
                   
            cardIV =[[UIImageView alloc] init];
            cardIV.frame=CGRectMake(0, 0, cardIVWidth, cardIVHeight);
                   
                   
            cardIV.contentMode = UIViewContentModeScaleAspectFit;
                   
            [cardIV setImage:uImage];
                   
            cardIV.transform = transform;
            [cardIV setFrameY:leftWidth];
            [cardIV setFrameX:(pCView.TKWidth-40-cardIV.TKWidth)];
            [pCView addSubview:cardIV];
            
        }
        

        
        float cardLabelWdith=108;
        float cardLabelHeight=25;
        
        UILabel *cardLabel = [[UILabel alloc] init];
        cardLabel.frame = CGRectMake(0, 0, cardLabelWdith, cardLabelHeight);
        cardLabel.text = @"请您核对照片";
        cardLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
        cardLabel.textColor = [UIColor whiteColor];
        cardLabel.transform=transform;
        [cardLabel setFrameY:((cardIV.TKHeight-cardLabelWdith)/2+cardIV.TKTop)];
        [cardLabel setFrameX:(pCView.TKWidth-cardLabelHeight-8)];
        [pCView addSubview:cardLabel];
        
        
        float tipImgHeight=230;
        float tipImgWidth=tipImgHeight*TKCARD_AspectRatio;
        float tipImgY=pCView.TKHeight-leftWidth-tipImgHeight;
        float tipImgX=pCView.TKWidth-tipImgWidth-49;
        UIImageView *tipImgView=[[UIImageView alloc] initWithFrame:CGRectMake(tipImgX, tipImgY, tipImgWidth, tipImgHeight)];
        if ([[tImageType objectAtIndex:hStep] intValue]==5) {
            //是否复印件示例图
            if (_param[@"isCopies"] && ![_param[@"isCopies"] integerValue]) {//提交证件图给H5上传
                [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/reverseCopies_tip_icon.png", TK_OPEN_RESOURCE_NAME]]];
            }else{
                [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/reverse_tip_icon.png", TK_OPEN_RESOURCE_NAME]]];
            }

        }else if ([[tImageType objectAtIndex:hStep] intValue]==9) {
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60032/yyzz_tip_icon.png", TK_OPEN_RESOURCE_NAME]]];
        }else{

            //是否复印件示例图
            if (_param[@"isCopies"] && ![_param[@"isCopies"] integerValue]) {//提交证件图给H5上传
                [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/frontCopies_tip_icon.png", TK_OPEN_RESOURCE_NAME]]];
            }else{
                [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/front_tip_icon.png", TK_OPEN_RESOURCE_NAME]]];
            }
        }
        tipImgView.contentMode = UIViewContentModeScaleToFill;
        [pCView addSubview:tipImgView];
        



        //检查照片的提示语
        UILabel *checkTipLabel = [[UILabel alloc] init];
        checkTipLabel.numberOfLines=0;
        checkTipLabel.backgroundColor=[UIColor clearColor];
        float checkTipLabelWidth=tipImgView.TKHeight;
        float checkTipLabelHeight=48;
        checkTipLabel.frame = CGRectMake(0,0, checkTipLabelWidth, checkTipLabelHeight);
        NSString *tipStr= @"请参考样例，确保身份证照片\n文字清晰、边角完整。";
    
        if ([[tImageType objectAtIndex:hStep] intValue]==9) {
            tipStr= @"请参考样例，确保营业执照照片\n文字清晰、边角完整。";
        }
    
        NSRange tipRange=[tipStr rangeOfString:@"文字清晰、边角完整。"];
        //顶部提示用富文本
        NSMutableAttributedString *tipAttribut=[[NSMutableAttributedString alloc] initWithString:tipStr];
        // 2.添加属性
        [tipAttribut addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, tipStr.length)];
        [tipAttribut addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#BBBBBB"] range:NSMakeRange(0, tipStr.length)];
        [tipAttribut addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
        checkTipLabel.attributedText=tipAttribut;
        checkTipLabel.textAlignment = NSTextAlignmentLeft;
        checkTipLabel.transform=transform;
        [checkTipLabel setFrameY:tipImgView.TKTop];
        [checkTipLabel setFrameX:tipImgView.TKLeft-checkTipLabelHeight-5.0F];
        [pCView addSubview:checkTipLabel];

    
    float btnX=13;
    float btnWidht=214;
    float centerGap=15;//按钮距离垂直中心的距离
    float btnHeight=44;
        //提交按钮
        UIButton *submitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        
        submitBtn.tag = 104;
        
        
        [submitBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        
        
        [submitBtn setTitle:@"提交" forState:UIControlStateNormal];
        [submitBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString]];
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
    }else{
        submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    }
        [submitBtn.layer setCornerRadius:btnHeight/2.0f];
        
        submitBtn.frame =CGRectMake(0, 0, btnWidht,btnHeight);
        
        [submitBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        
        [submitBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        
        [submitBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
        
        submitBtn.transform = transform;
        [submitBtn setFrameX:btnX];
        [submitBtn setFrameY:(pCView.frame.size.height/2+centerGap)];
        [pCView addSubview:submitBtn];
        
        //重拍按钮
        UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        
        cancelBtn.tag = 103;
    
        if (isPhotoAlbum) {
            [cancelBtn setTitle:@"重选" forState:UIControlStateNormal];
        }else{
            [cancelBtn setTitle:@"重拍" forState:UIControlStateNormal];
        }
        
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
    }else{
        cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    }
        [cancelBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        
        
        [cancelBtn.layer setBorderWidth:1.0f];
        [cancelBtn.layer setBorderColor:[UIColor whiteColor].CGColor];
        [cancelBtn.layer setCornerRadius:btnHeight/2.0f];
        
        cancelBtn.frame =CGRectMake(0, 0, btnWidht,btnHeight);
        
        [cancelBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        
        [cancelBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        
        [cancelBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
        
        cancelBtn.transform = transform;
        [cancelBtn setFrameX:btnX];
        [cancelBtn setFrameY:(pCView.TKHeight/2-centerGap-btnWidht)];
        
        [pCView addSubview:cancelBtn];
        
        pCView.frame = CGRectOffset(fFrame, 0, -fFrame.size.height);
        [UIView animateWithDuration:0.3f delay:0.0f options:UIViewAnimationOptionCurveEaseInOut animations:^{
            
            [self.view addSubview:pCView];
            
            pCView.center = CGPointMake(pCView.center.x , pCView.center.y + fFrame.size.height);
            
            
        } completion:^(BOOL finished) {
            
            if (tCView) {
                
                [tCView removeFromSuperview];
                
                tCView = nil;
                
            }
            
        }];
        
}

/**
 *  <AUTHOR>
 *  获得照片裁剪中心区域矩阵
 *  照片是横的，然后布局坐标是竖屏的。这里要转换一下以横屏来计算坐标
 */
-(CGRect)getCuttingArea:(CGSize)imgSize{
    CGRect rect;
    
    //等比计算一个中间银行卡大小矩形
    
    float areaHeigth=self.idRect.size.width*imgSize.height/self.windoRect.size.width;
    float areaY=(imgSize.height-areaHeigth)/2;
    
    float areaWidth=self.idRect.size.height*imgSize.width/self.windoRect.size.height;
    float areaX=(imgSize.width-areaWidth)/2;
    
    rect=CGRectMake(areaX, areaY, areaWidth, areaHeigth);
    
    return rect;
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}


- (IBAction)btnOnClicked:(id)sender {
    
    UIButton *btn = (UIButton*)sender;
    
    if (btn.tag == 100) {//打开相册
        
        
        [self tkIsPhotoLibraryPermissions:^{
            [self openAlbum];
        }];
        
    }else if(btn.tag == 101){//拍证件照
        //防止连续点击拍照
        if (self.isTakeClick) {
            return;
        }
        self.isTakeClick=YES;
        
        if (captureManager) {
            
            [captureManager takePicture:^(id hResult) {
                
                isPhotoAlbum = NO;
                
                uImage = hResult;
                if (uImage) {
                    [self getImgGoPreview:uImage];
                }

                
            }];
        }
        
    }else if(btn.tag == 102){//返回

        //处理通达信里面会被横屏问题
        NSNumber *orientationTarget = [NSNumber numberWithInt:UIInterfaceOrientationPortrait];
        [[UIDevice currentDevice] setValue:orientationTarget forKey:@"orientation"];
        
        [self dismissViewControllerAnimated:YES completion:^{
            NSMutableDictionary *jsParam=[[NSMutableDictionary alloc] init];
            jsParam[@"funcNo"]=@"60050";
            jsParam[@"error_no"]=@"-1";
    
            if (self.delegate&&[self.delegate respondsToSelector:@selector(tkTakeIDCardDidComplete:)]) {
                [self.delegate tkTakeIDCardDidComplete:jsParam];
            }else{
                [mService iosCallJsWithDic:jsParam callBackFunc:^(ResultVo *resultVo) {}];
            }
        }];
        
    }else if(btn.tag == 103){//重拍
        
        [self initTakeCardViewNew];
        
    }else if(btn.tag == 104){
        
        [self affirmAction];
    }
}


//跳转预览页或者直接结果给h5
-(void)getImgGoPreview:(UIImage *)img{
    if ([self.param[@"isNeedOcrAffirmView"] isEqualToString:@"0"]) {
        [self dismissViewControllerAnimated:YES completion:^{
            [self affirmAction];
        }];
    }else{
        if ([self.param[@"isNeedSample"] isEqualToString:@"1"]) {
            [self initPreviewCardNeedSampleViewNew:img];
        }else{
            [self initPreviewCardViewNew:img];
        }
    }
}

/**
 <AUTHOR> 2019年06月28日19:38:06
 @照片确认上传事件
 */
-(void)affirmAction{
    //        isPhotoAlbum  = NO;
    if ([_param[@"isNeedWatermark"] isEqualToString:@"1"]) {
        NSString *logoName=_param[@"watermarkImgName"]?_param[@"watermarkImgName"]:@"sd_logo_new.png";
        uImage = [self imageWithTransImage:uImage addtransparentImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/%@", TK_OPEN_RESOURCE_NAME,logoName]]];
    }
    
    
    NSData* data =[TKImageHelper compressImageData:uImage toByte:(_param[@"compressSize"]? [_param[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];

        
    [self showImage:data];
}

- (void)showImage:(NSData *)imageData
{
    NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:imageData];
    NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
    hStep ++;
    reqParam[@"funcNo"]=@"60050";
    reqParam[@"error_no"]=@"0";
    reqParam[@"imgType"]=_param[@"imgType"];
    
    if (tImageType.count>1) {
        if ([[tImageType objectAtIndex:hStep-1] intValue]==4) {
            reqParam[@"frontBase64"]=base64;
        }else if([[tImageType objectAtIndex:hStep-1] intValue]==5){
            reqParam[@"backBase64"]=base64;
        }else{
            [base64Arr addObject:base64];
        }
        
        if (tImageType.count>hStep) {
            [self initTakeCardViewNew];
        }else{
            reqParam[@"base64Arr"]=base64Arr;
            [self exitWithCallBack];
        }
    }else{
        
        reqParam[@"frontBase64"]=base64;//默认身份证正面
        
        if ([[tImageType objectAtIndex:hStep-1] intValue]==5) {
            
            TKLogInfo(@"身份证反面上传成功");
            reqParam[@"backBase64"]=base64;
        }else if([[tImageType objectAtIndex:hStep-1] intValue]!=4){
            reqParam[@"base64"]=base64;
        }
        
        [self exitWithCallBack];
    }
}

-(void)exitWithCallBack{
    //处理通达信里面会被横屏问题
    NSNumber *orientationTarget = [NSNumber numberWithInt:UIInterfaceOrientationPortrait];
    [[UIDevice currentDevice] setValue:orientationTarget forKey:@"orientation"];
    //识别结束
    [self dismissViewControllerAnimated:YES completion:^{
        if (_delegate && [_delegate respondsToSelector:@selector(tkTakeIDCardDidComplete:)]) {
            
            [_delegate tkTakeIDCardDidComplete:reqParam];
        }else{
            [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
        }
    }];
}

#pragma mark －打开相册
- (void)openAlbum{
    
    UIImagePickerController* cameraPicker = [[UIImagePickerController alloc] init];
    
    cameraPicker.delegate = self;
    
    cameraPicker.allowsEditing = NO;
    
    cameraPicker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    
    NSArray* mediaArray = [NSArray arrayWithObjects:(NSString*)kUTTypeImage, nil];
    
    cameraPicker.mediaTypes = mediaArray;
    cameraPicker.modalPresentationStyle=UIModalPresentationFullScreen;
    [self presentViewController:cameraPicker animated:YES completion:^{
        
        if (captureManager) {
            
            [captureManager cancelTakePicture];
        }
        
    }];
    
}

#pragma mark - implement UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info
{
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    NSString* mediaType = [info objectForKey:UIImagePickerControllerMediaType];
    
    if ([mediaType isEqualToString:(NSString*)kUTTypeImage])
    {
        // get the image
        UIImage* image = [info objectForKey:UIImagePickerControllerOriginalImage];
        
        if (picker.allowsEditing && [info objectForKey:UIImagePickerControllerEditedImage]) {// 可编辑的图片
            image = [info objectForKey:UIImagePickerControllerEditedImage];
        }
        
        isPhotoAlbum = YES;
        
        uImage = image;
        [self getImgGoPreview:uImage];

        
    }
}

//取消照相机的回调
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    if (captureManager) {
        
        [captureManager reTakePicture];
        
    }
    
    TKLogInfo(@"取消照相机的回调");
}


#pragma mark -implement MBProgressHUDDelegate
- (void)hudWasHidden:(TKMBProgressHUD *)hud {
    // Remove HUD from screen when the HUD was hidded
    [mHUD removeFromSuperview];
    
    mHUD = nil;
}

#pragma mark - 加半透明的水印
- (UIImage *)imageWithTransImage:(UIImage *)useImage addtransparentImage:(UIImage *)transparentimg
{
    UIGraphicsBeginImageContext(useImage.size);
    
    [useImage drawInRect:CGRectMake(0, 0, useImage.size.width, useImage.size.height)];
    
    [transparentimg drawInRect:CGRectMake(useImage.size.width - useImage.size.width*0.18 - 10, 10, useImage.size.width*0.18, useImage.size.height*0.26)];
    
    UIImage *resultingImage = UIGraphicsGetImageFromCurrentImageContext();
    
    UIGraphicsEndImageContext();
    
    return resultingImage;
}

//#pragma mark 裁剪图片大小
//- (UIImage*)imageByCroppingForSize:(UIImage*)anImage toSize:(CGRect)frameSize vFrame:(CGRect)vSize
//{
//    TKLogInfo(@"w1= %f w= %f,h1= %f h= %f",vSize.size.width,anImage.size.width,vSize.size.height,anImage.size.height);
//
//    float sHeight = anImage.size.height < anImage.size.width ? anImage.size.height : anImage.size.width;
//
//    CGFloat scale = sHeight/vSize.size.width;
//
//    CGImageRef imageRef = CGImageCreateWithImageInRect([anImage CGImage], CGRectMake(frameSize.origin.y*scale, frameSize.origin.x*scale, frameSize.size.height*scale, frameSize.size.width*scale));
//
//    UIImage *cropped = [UIImage imageWithCGImage:imageRef];
//
//    CGImageRelease(imageRef);
//
//    return cropped;
//}

//#pragma mark- json数据转成NSDictionary
//- (id)JSONObject:(NSString*)content
//{
//    NSError* error = nil;
//
//    id object = [NSJSONSerialization JSONObjectWithData:[content dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers error:&error];
//
//    if (error != nil) {
//
//        return nil;
//    }
//
//    return object;
//}

//- (void)takeFinish:(BOOL)result
//{
//
//
//}


- (void)dealloc{
    
    [[UIApplication sharedApplication] setStatusBarHidden:NO];
    
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    
    TKLogInfo(@"%@__%s",NSStringFromClass([self class]), __FUNCTION__);
    
    captureManager = nil;
    
    tCView = nil;
    
    pCView = nil;
    
    mService = nil;
    
    uImage = nil;
    
    reqParam = nil;
    
    tImageType = nil;
    
}
@end

