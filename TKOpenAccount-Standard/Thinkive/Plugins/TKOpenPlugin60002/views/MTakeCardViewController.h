//
//  MTakeBigPictureViewController.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/8/4.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol TakeCardResultDelegate <NSObject>

- (void)takeFinish:(id)result;

@end

/**
 *  Description  拍摄身份证件照
 */

@interface MTakeCardViewController : TKBaseViewController


@property (nonatomic, assign) id<TakeCardResultDelegate> delegate;

@property (nonatomic, assign) NSInteger tType;

@property (nonatomic, retain) NSMutableDictionary *param;


- (IBAction)btnOnClicked:(id)sender;

@end
