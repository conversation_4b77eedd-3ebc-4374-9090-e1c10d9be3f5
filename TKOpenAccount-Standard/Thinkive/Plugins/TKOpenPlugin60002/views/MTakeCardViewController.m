//
//  MTakeBigPictureViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/8/4.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "MTakeCardViewController.h"
#import "TKAVCaptureManager.h"


#define kParam_uuid       @"uuid"
#define kParam_userId     @"userId"
#define kParam_r          @"r"
#define kParam_imgType    @"imgType"
#define kParam_funcNum    @"funcNum"
#define kParam_photoType  @"photoType"
#define kParam_action     @"action"
#define kParam_url        @"url"
#define kParam_clientInfo @"clientInfo"
#define kParam_jsessionId @"jsessionId"
#define kParam_key        @"key"

@interface MTakeCardViewController ()<TKMBProgressHUDDelegate>
{
    TKAVCaptureManager* captureManager;
    
    BOOL isTakePhoto, flag;
    
    UIButton *reTPBtn, *tpBtn, *upBtn;
    
    TKMBProgressHUD *mHUD;
    
    UIImage *tPhoto;
    
}

@end

@implementation MTakeCardViewController

- (void)viewDidLoad {
    
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.5];
    
    if (_param && _param[@"imgType"] && ([_param[@"imgType"] integerValue] == 3 || [_param[@"imgType"] integerValue] == 209)) {

        _tType = 2;

    }else{

        _tType = 1;
    }

    [self initView];
    
    if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_View.objectId",@"open"]]) {
        
        [TKTraffic visitPage:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_View.objectId",@"open"]]];
    }
}

-(BOOL)prefersStatusBarHidden
{
    return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
}

- (void)initView{
    
    if (self.view.TKWidth > self.view.TKHeight) {
        
        self.view.frame = CGRectMake(0, 0, self.view.TKHeight, self.view.TKWidth);
    }
    
    CGSize vSize = self.view.frame.size;
    
    int buttomHeight = 128;
    
    UIView *buttomView = [[UIView alloc] initWithFrame:CGRectMake(0, vSize.height - buttomHeight, vSize.width, buttomHeight)];
    
    [buttomView setBackgroundColor:[UIColor blackColor]];
    
    reTPBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    reTPBtn.tag = 100;
    
    [reTPBtn setTitle:@"取消" forState:UIControlStateNormal];
    
    [reTPBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    NSInteger btnSpace = (self.view.TKWidth - 300)/4;
    
    reTPBtn.frame = CGRectMake(btnSpace - 20, 34, 100, 60);
    
    [reTPBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [reTPBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [reTPBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [buttomView addSubview:reTPBtn];
    
    tpBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    tpBtn.tag = 101;
    
//    [tpBtn setTitle:@"拍照" forState:UIControlStateNormal];
//    
//    [tpBtn setTitleColor:[UIColor greenColor] forState:UIControlStateNormal];
    
    tpBtn.frame = CGRectMake((self.view.frame.size.width - 60)/2, 34, 60, 60);
    
    [tpBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
    
    [tpBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [tpBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [tpBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [buttomView addSubview:tpBtn];
    
    upBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    [upBtn setHidden:YES];
    
    upBtn.tag = 102;
    
    [upBtn setTitle:@"使用图片" forState:UIControlStateNormal];
 
    [upBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    upBtn.frame = CGRectMake(self.view.TKWidth - 100 - btnSpace + 15, 34, 100, 60);
    
    [upBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [upBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [upBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [buttomView addSubview:upBtn];
    
    UIView *cView;
    
    if (_tType == 1) {
        
        UIView *topView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.TKWidth, 64)];
        
        topView.backgroundColor = [UIColor blackColor];
        
        [self.view addSubview:topView];
        
        cView = [[UIView alloc] initWithFrame:CGRectMake(0, 64, self.view.TKWidth, self.view.TKHeight - buttomHeight - 64)];
        
        UIView* overLayView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.TKWidth, cView.TKHeight)];
        
        overLayView.backgroundColor = [UIColor clearColor];
        
        UIImageView *pIV = [[UIImageView alloc] initWithFrame:CGRectMake(0, -30, self.view.TKWidth, 30)];
        
        [pIV setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60002/page-camra.PNG", TK_OPEN_RESOURCE_NAME]]];
        
        [overLayView addSubview:pIV];
        
        
        UILabel *leftUpOne = [[UILabel alloc] initWithFrame:CGRectMake(30, 5, 60, 6)];
        
        leftUpOne.backgroundColor = [UIColor colorWithRed:10/255.0f green:96/255.0f blue:254/255.0f alpha:1.0];
        
        [overLayView addSubview:leftUpOne];
        
        UILabel *leftUpTwo = [[UILabel alloc] initWithFrame:CGRectMake(30, 5, 6, 60)];
        
        leftUpTwo.backgroundColor = [UIColor colorWithRed:10/255.0f green:96/255.0f blue:254/255.0f alpha:1.0];
        
        [overLayView addSubview:leftUpTwo];

        UILabel *leftDownOne = [[UILabel alloc] initWithFrame:CGRectMake(30, overLayView.TKHeight- 10 -6, 60, 6)];
        
        leftDownOne.backgroundColor = [UIColor colorWithRed:10/255.0f green:96/255.0f blue:254/255.0f alpha:1.0];
        
        [overLayView addSubview:leftDownOne];
        
        UILabel *leftDownTwo = [[UILabel alloc] initWithFrame:CGRectMake(30, overLayView.TKHeight- 10 - 60, 6, 60)];
        
        leftDownTwo.backgroundColor = [UIColor colorWithRed:10/255.0f green:96/255.0f blue:254/255.0f alpha:1.0];
        
        [overLayView addSubview:leftDownTwo];
        
        
        UILabel *rightUpOne = [[UILabel alloc] initWithFrame:CGRectMake(self.view.TKWidth - 30 - 60, 5, 60, 6)];
        
        rightUpOne.backgroundColor = [UIColor colorWithRed:10/255.0f green:96/255.0f blue:254/255.0f alpha:1.0];
        
        [overLayView addSubview:rightUpOne];
        
        UILabel *rightUpTwo = [[UILabel alloc] initWithFrame:CGRectMake(self.view.TKWidth -30 - 6, 5, 6, 60)];
        
        rightUpTwo.backgroundColor = [UIColor colorWithRed:10/255.0f green:96/255.0f blue:254/255.0f alpha:1.0];
        
        [overLayView addSubview:rightUpTwo];
        
        UILabel *rightDownOne = [[UILabel alloc] initWithFrame:CGRectMake(self.view.TKWidth - 30 - 60, overLayView.frame.size.height- 10 -6, 60, 6)];
        
        rightDownOne.backgroundColor = [UIColor colorWithRed:10/255.0f green:96/255.0f blue:254/255.0f alpha:1.0];
        
        [overLayView addSubview:rightDownOne];
        
        UILabel *rightDownTwo = [[UILabel alloc] initWithFrame:CGRectMake(self.view.TKWidth - 30 - 6, overLayView.frame.size.height- 10 - 60, 6, 60)];
        
        rightDownTwo.backgroundColor = [UIColor colorWithRed:10/255.0f green:96/255.0f blue:254/255.0f alpha:1.0];
        
        [overLayView addSubview:rightDownTwo];

        [cView addSubview:overLayView];
        
        [self.view addSubview:cView];
        
    }else{
        
        CGFloat statusHeight = [[UIApplication sharedApplication] statusBarFrame].size.height;
        
        cView = [[UIView alloc] initWithFrame:CGRectMake(0, statusHeight, self.view.TKWidth, self.view.TKHeight - buttomHeight - statusHeight)];
        
        UIImage *img = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/photo_face.png", TK_OPEN_RESOURCE_NAME]];
        
        UIImageView *fIv = [[UIImageView alloc] initWithFrame:CGRectMake(0, 10, self.view.TKWidth, cView.TKHeight - 10)];
        
        [fIv setImage:img];
        
        [cView addSubview:fIv];
        
        UILabel *pLable = [[UILabel alloc] initWithFrame:CGRectMake(0, cView.TKHeight - 50, self.view.TKWidth, 40)];
        
        pLable.backgroundColor = [UIColor clearColor];
        
        pLable.text = @"脸部请拍摄在人像框内";
        
        pLable.textAlignment = NSTextAlignmentCenter;
        
        pLable.font = [UIFont systemFontOfSize:17.0f];
        
        pLable.textColor = [UIColor whiteColor];
        
        [cView addSubview:pLable];
        
        [self.view addSubview:cView];
        if (_param[@"isShowFaceGizmos"]&&[_param[@"isShowFaceGizmos"] integerValue]==0) {
            [fIv setHidden:YES];
            [pLable setHidden:YES];
        }
        
    }
    
    [self.view addSubview:buttomView];
    
    captureManager = [[TKAVCaptureManager alloc] initWithPreviewView:cView withCameraPosition:_tType withCamraOrientation:AVCaptureVideoOrientationPortrait handleBusinessType:TK_VIDEO_TAKE_PHOTO];
    
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

- (IBAction)btnOnClicked:(id)sender {
    
    UIButton *btn = (UIButton*)sender;
    
    NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
    bpDic[@"eventType"]=@"2";
    
    if (btn.tag == 100) {
        
        if (isTakePhoto) {
            
            isTakePhoto = NO;
            
            [captureManager reTakePicture];
            
            [reTPBtn setTitle:@"取消" forState:UIControlStateNormal];
            
            [tpBtn setHidden:NO];
            
            [upBtn setHidden:YES];
            
            bpDic[@"objectId"]=[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Retake.objectId",@"open"]];
            bpDic[@"actionId"]=[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Retake.actionId",@"open"]];
            
        }else{
            
            bpDic[@"objectId"]=[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Cancel.objectId",@"open"]];
            bpDic[@"actionId"]=[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Cancel.actionId",@"open"]];
            
            [self dismissViewControllerAnimated:YES completion:nil];
            
             [captureManager cancelTakePicture];
            
            if (_delegate && [_delegate respondsToSelector:@selector(takeFinish:)] ) {
                
                [_delegate takeFinish:nil];
            }
        }
        
    }else if (btn.tag == 101){
        
        [captureManager takePicture:^(id hResult) {
            
            tPhoto = hResult;
        }];
        
        isTakePhoto = YES;
        
        [reTPBtn setTitle:@"重拍" forState:UIControlStateNormal];
        
        [tpBtn setHidden:YES];
        
        [upBtn setHidden:NO];
        
        bpDic[@"objectId"]=[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Take.objectId",@"open"]] ;
        bpDic[@"actionId"]=[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Take.actionId",@"open"]] ;
        
    }else if (btn.tag == 102){
   
        [self dismissViewControllerAnimated:YES completion:^{
            
            if (_delegate && [_delegate respondsToSelector:@selector(takeFinish:)] ) {
                
                [_delegate takeFinish:tPhoto];
            }
        }];
        
    }
    
    if (bpDic[@"objectId"]) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:3];
        id extParams = _param[@"extParams"];
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            [dic addEntriesFromDictionary:extParams];
            if (extParams[@"i_business_type"]) {
                dic[@"i_group_name"]=extParams[@"i_business_type"];
            }
        }
        [TKTraffic event:bpDic[@"objectId"] eventType:@"2" actionId:bpDic[@"actionId"] attributes:dic];
    }
}

#pragma mark- json数据转成NSDictionary
- (id)JSONObject:(NSString*)content
{
    NSError* error = nil;
    
    id object = [NSJSONSerialization JSONObjectWithData:[content dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers error:&error];
    
    if (error != nil) {
        
        return nil;
    }
    
    return object;
}

- (void)dealloc{
    
    [[UIApplication sharedApplication] setStatusBarHidden:NO];
    
}
@end

