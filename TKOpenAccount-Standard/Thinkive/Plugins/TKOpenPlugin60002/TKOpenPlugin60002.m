//
//  TKOpenPlugin60002.m
//  TKApp
//
//  Created by 叶璐 on 15/4/17.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKOpenPlugin60002.h"
#import <MobileCoreServices/UTCoreTypes.h>
#import "TKOpenAccountService.h"
#import "MTakeCardViewController.h"
#import <AssetsLibrary/AssetsLibrary.h>
#import <Photos/Photos.h>
#import "TKCommonUtil.h"
#import "UIViewController+TKAuthorityKit.h"

#define kParam_uuid       @"uuid"
#define kParam_userId     @"userId"
#define kParam_r          @"r"
#define kParam_imgType    @"imgType"
#define kParam_funcNum    @"funcNum"
#define kParam_photoType  @"photoType"
#define kParam_action     @"action"
#define kParam_url        @"url"
#define kParam_clientInfo @"clientInfo"
#define kParam_jsessionId @"jsessionId"
#define kParam_key        @"key"

#define kImgTypeCameraDeviceFront @"3"   // 前置摄像头

#define IS_NEW_CARD_PHOTO_TAKE 0

@interface TKOpenPlugin60002()<UINavigationControllerDelegate, UIImagePickerControllerDelegate,TakeCardResultDelegate,TKMBProgressHUDDelegate>
{
    ResultVo *_resultVo;// 返回的结果
    TKOpenAccountService *_service;
    NSMutableDictionary *_param;
    UIImage *_image;
    TKMBProgressHUD *mHUD;
    NSData *_imageData;
    BOOL isLibraryUploadFinish;//是否相册上传完成
}
@end

@implementation TKOpenPlugin60002

/**
 *  <AUTHOR> 2014-11-27 12:11:58
 *
 *  功能描述：上传用户的身份证正反面或者大头照
 *  参考插件:对应phoneGap的carmeraPlugin插件
 *
 *  @param uuid         唯一标识
 *  @param userId       用户ID
 *  @param r            10位随机数
 *  @param imgType      影像类型
 *  @param funcNum      上传照片调用的功能号
 *  @param photoType    影像类型名字（身份证，大头像）
 *  @param action       照片来源类别(phone:相册选择,pai:照像机拍摄)     Y
 *  @param url          图片上传服务地址
 *  @param clientInfo   客户信息
 *  @param jsessionId   会话ID
 *  @param key          通讯唯一标识
 *
 *  @return 无
 */
-(ResultVo *)serverInvoke:(id)param
{
    dispatch_async(dispatch_get_main_queue(), ^{
        if (param && param[@"moduleName"]) {
            
            if (self.currentViewCtrl && [self.currentViewCtrl isKindOfClass:[TKBaseWebViewController class]]) {
                
                TKBaseWebViewController *webCtl = (TKBaseWebViewController*)self.currentViewCtrl;
                
                webCtl.tkName = param[@"moduleName"];
            }
        }
    });
    
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    
    NSString *action = reqParam[kParam_action];
    
    // 检测入参是否有误
    if ([self IsErrorParam:reqParam]) {
        return _resultVo;
    }
    
    _param = reqParam;
    
    _service = [[TKOpenAccountService alloc]init];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //是否在调用插件前展示介绍页面
        if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
            NSMutableArray *authArray=[[NSMutableArray alloc] init];
            if ([param[@"isAlbum"] integerValue] != 0||[TKStringHelper isEmpty:param[@"isAlbum"]]) {
                [authArray addObject:@(TKAuthorizationType_Photo)];
            }
            
            if ([action isEqualToString:@"phone"]) {
                [TKAuthorizationHelper requestAuthorization:authArray   authCallBacks:nil btnCallBack:^{
                    [self.currentViewCtrl tkIsPhotoLibraryPermissions:^{
                        [self openAlbum];
                    }];
                }];
            }else{
                [authArray addObject:@(TKAuthorizationType_Camera)];
                
                [TKAuthorizationHelper requestAuthorization:authArray   authCallBacks:nil btnCallBack:^{
                    [self.currentViewCtrl tkIsCameraPermissions:^{
                        [self takeCard:reqParam];
                    }];
                }];
            }
        }else{
            if ([action isEqualToString:@"phone"]) {
                [self.currentViewCtrl tkIsPhotoLibraryPermissions:^{
                    [self openAlbum];
                }];
            }else{
                [self.currentViewCtrl tkIsCameraPermissions:^{
                    [self takeCard:reqParam];
                }];
                      
            }
        }
    });
    
    
    return _resultVo;
    
}


/**
 *  <AUTHOR> 2015-04-20 00:12:37
 *
 *  判断输入参数是否有误
 *
 *  @param reqParam 传入参数
 *
 *  @return
 YES: 参数有误
 NO:  参数正常
 */
-(BOOL)IsErrorParam:(NSMutableDictionary *)reqParam
{
    ResultVo *resultVo = [[ResultVo alloc]init];
    _resultVo = resultVo;
    
    NSString *uuid       = reqParam[kParam_uuid];
    NSString *userId     = reqParam[kParam_userId];
    NSNumber *tempRD     = [reqParam objectForKey:kParam_r];
    NSString *rd         = [NSString stringWithFormat:@"%@",tempRD];    //随机数
    NSString *imgType    = reqParam[kParam_imgType];
    NSString *funcNum    = reqParam[kParam_funcNum];
    NSString *photoType  = reqParam[kParam_photoType];
    NSString *action     = reqParam[kParam_action];
    NSString *url        = reqParam[kParam_url];
    //    NSString *clientInfo = reqParam[kParam_clientInfo];
    //    NSString *jsessionId = reqParam[kParam_jsessionId];
    NSString *key        = reqParam[kParam_key];
    
    if ([TKStringHelper isEmpty:uuid]) {
        resultVo.errorNo = -6000201;
        resultVo.errorInfo = @"唯一标识不能为空!";
        
        return YES;
    }
    
    if ([TKStringHelper isEmpty:userId]) {
        resultVo.errorNo = -6000202;
        resultVo.errorInfo = @"用户ID不能为空!";
        
        return YES;
    }
    
    if ([TKStringHelper isEmpty:rd]) {
        resultVo.errorNo = -6000203;
        resultVo.errorInfo = @"随机数不能为空!";
        
        return YES;
    }
    
    if ([TKStringHelper isEmpty:imgType]) {
        resultVo.errorNo = -6000204;
        resultVo.errorInfo = @"影像类型不能为空!";
        
        return YES;
    }
    
    if ([TKStringHelper isEmpty:funcNum]) {
        resultVo.errorNo = -6000205;
        resultVo.errorInfo = @"上传功能号不能为空";
        
        return YES;
    }
    
    if ([TKStringHelper isEmpty:photoType]) {
        resultVo.errorNo = -6000206;
        resultVo.errorInfo = @"影像类型名字不能为空!";
        
        return YES;
    }
    
    if ([TKStringHelper isEmpty:action]) {
        resultVo.errorNo = -6000207;
        resultVo.errorInfo = @"照片来源类别不能为空!";
        
        return YES;
    }
    
    if ([TKStringHelper isEmpty:url]) {
        resultVo.errorNo = -6000208;
        resultVo.errorInfo = @"图片上传地址不能为空!";
        
        return YES;
    }
    
    //    if ([TKStringHelper isEmpty:jsessionId]) {
    //        resultVo.errorNo = -6000210;
    //        resultVo.errorInfo = @"会话ID不能为空!";
    //
    //        return YES;
    //    }
    
    if ([TKStringHelper isEmpty:key]) {
        resultVo.errorNo = -6000211;
        resultVo.errorInfo = @"通讯唯一标识不能为空!";
        
        return YES;
    }
    
    return NO;
}

/**
 *  Description 打开相册
 */
- (void)openAlbum{
    isLibraryUploadFinish=YES;
    UIImagePickerController* cameraPicker = [[UIImagePickerController alloc] init];
    cameraPicker.delegate = self;
    cameraPicker.allowsEditing = NO; // 不需要编辑图片
    cameraPicker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    NSArray* mediaArray = [NSArray arrayWithObjects:(NSString*)kUTTypeImage, nil];
    cameraPicker.mediaTypes = mediaArray;
    cameraPicker.modalPresentationStyle=UIModalPresentationFullScreen;
    if ([TKCommonUtil isCurrentViewControllerVisible:self.currentViewCtrl]) {
        
        [self.currentViewCtrl presentViewController:cameraPicker animated:YES completion:nil];
    }else{
        
        [[TKCommonUtil getCurrentVisibleVC] presentViewController:cameraPicker animated:YES completion:nil];
    }
    
    if ([self.currentViewCtrl isKindOfClass:[TKBaseWebViewController class]])
    {
        ((TKBaseWebViewController*)self.currentViewCtrl).isNeedReInitJSModule = NO;
    }
}

/**
 *  Description 拍照
 */
- (void)takeCard:(id)param{
    
    MTakeCardViewController *tBCtl = [[MTakeCardViewController alloc] init];
    
    tBCtl.param = param;
    
    tBCtl.delegate = self;
    
    if ([TKCommonUtil isCurrentViewControllerVisible:self.currentViewCtrl]) {
        
        [self.currentViewCtrl presentViewController:tBCtl animated:YES completion:nil];
    }else{
        
        [[TKCommonUtil getCurrentVisibleVC] presentViewController:tBCtl animated:YES completion:nil];
    }
    
    if ([self.currentViewCtrl isKindOfClass:[TKBaseWebViewController class]])
    {
        ((TKBaseWebViewController*)self.currentViewCtrl).isNeedReInitJSModule = NO;
    }
}

#pragma mark - ImagePicker代理
//  成功获得相片还是视频后的回调
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info
{
    if (isLibraryUploadFinish) {
        //没有相册上传完成就执行，避免用户快速点击相册导致多次回调重复上传销毁问题
        isLibraryUploadFinish=NO;
        [picker dismissViewControllerAnimated:YES completion:^{
            // IMAGE TYPE  仅仅允许图片。。
            NSString* mediaType = [info objectForKey:UIImagePickerControllerMediaType];
            
            if ([mediaType isEqualToString:(NSString*)kUTTypeImage])
            {
                // get the image
                UIImage* image = [info objectForKey:UIImagePickerControllerOriginalImage];
                if (picker.allowsEditing && [info objectForKey:UIImagePickerControllerEditedImage]) {// 可编辑的图片
                    image = [info objectForKey:UIImagePickerControllerEditedImage];
                }
                
                if (_param[@"isUpload"] && [_param[@"isUpload"] integerValue] == 0) {//提交证件图给H5上传
                    isLibraryUploadFinish=YES;
                    [self showImage:image];
                    
                }else{
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        mHUD = [[TKMBProgressHUD alloc] initWithView:self.currentViewCtrl.view];
                        [self.currentViewCtrl.view addSubview:mHUD];
                        mHUD.dimBackground = YES;
                        mHUD.labelText = @"请稍后...";
                        mHUD.mode = TKMBProgressHUDModeIndeterminate;
                        mHUD.delegate = self;
                        [mHUD show:YES];
                    });
                    
                    dispatch_async(dispatch_get_global_queue(0, 0), ^{
                        NSData* data =[TKImageHelper compressImageData:image toByte:(_param[@"compressSize"]? [_param[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];

                        
                        _imageData = data;
                        
                        [self upLoadImage:data];
                    });
                }
            }
        }];
    }
    
}

//取消照相机的回调
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    TKLogInfo(@"取消照相机的回调");
}

/**
 *  Description 压缩图片传给h5
 */
- (void)showImage:(UIImage*)img
{
    NSData* hexData =[TKImageHelper compressImageData:img toByte:(_param[@"compressSize"]? [_param[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];
    
    if (hexData == nil) {
        TKLogInfo(@"hexData is nil");
        return;
    }
    
    NSString *tmpBase64 = [TKBase64Helper stringWithEncodeBase64Data:hexData];
    
    NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tmpBase64];
    
    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
    

    reqParam[@"funcNo"]=@"60050";
    
    reqParam[@"base64"]=base64;
    
    
    [self iosCallJsWithParam:reqParam];
    
}


#pragma mark 上传照片
-(void)upLoadImage:(NSData *)imageData
{
    NSDate *now = [NSDate date];
    NSInteger tempInterval = [now timeIntervalSince1970];
    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
    [reqParam addEntriesFromDictionary:_param];
    reqParam[@"funcNo"]        = _param[kParam_funcNum];
    reqParam[@"uuid"]          = _param[kParam_uuid];
    reqParam[@"user_id"]       = _param[kParam_userId];
    reqParam[@"r"]             = _param[kParam_r];
    NSString *key              = [_param objectForKey:kParam_key];
    reqParam[@"signMsg"]       = [TKMd5Helper md5Encrypt:key];
    reqParam[@"img_type"]      = _param[kParam_imgType];
    reqParam[@"photo_type"]    = _param[kParam_photoType];
    reqParam[@"url"]           = _param[kParam_url];
    reqParam[@"type_id"]       = _param[kParam_imgType];
    
    if (_param[@"serverAccessType"] && [_param[@"serverAccessType"] integerValue] == 1) {
        reqParam[@"img_data"]=[TKBase64Helper stringWithEncodeBase64Data:imageData];
    }else{
        reqParam[@"img_data@@F"]=imageData;
    }
    
    NSString *hostUrl = _param[@"url"];
    
    
    [_service uploadFileWithURL:hostUrl param:reqParam callBackFunc:^(ResultVo *resultVo)
     {
         isLibraryUploadFinish=YES;
         if (mHUD) {
             [mHUD setHidden:YES];
         }
         
         NSArray *results = (NSArray *)resultVo.results;
         if (resultVo.errorNo == 0)
         {
             [self UploadFileFinished:results];
         }else if(resultVo.errorNo == -999){
        
             //未登录情况特殊处理,iOS8以后用
             UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:resultVo.errorInfo?resultVo.errorInfo:@"未登录" preferredStyle: UIAlertControllerStyleAlert];

             UIAlertAction *okAction = [UIAlertAction actionWithTitle: @"确定" style: UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                 NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
                 reqParam[@"funcNo"]=@"60050";
                 reqParam[@"moduleName"]=_param[@"moduleName"];
                 reqParam[@"tkuuid"]=_param[@"tkuuid"];
                 reqParam[@"error_no"]=@"-999";
                 reqParam[@"error_info"]=resultVo.errorInfo;
             
                 [self iosCallJsWithParam:reqParam];
             }];

             [alertController addAction:okAction];
             [self.currentViewCtrl presentViewController:alertController animated:YES completion:nil];
         }
         else
         {
             TKLogInfo(@"%@",resultVo.errorInfo);
             
             [self UploadFiledFailed:resultVo.errorInfo];
             
         }
         
         if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_UTime.objectId", @"open"]]) {
             NSDate *n = [NSDate date];
             NSTimeInterval diff = [n timeIntervalSince1970] - tempInterval;
             NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:5];
             dic[@"i_ocr_time"]=[NSNumber numberWithInteger:diff];
             dic[@"i_pic_from"]=[_param[kParam_action] isEqualToString:@"phone"]? @"2":@"1";
             NSString *tStr = @"3";
             if ([_param[@"imgType"] integerValue] == 3) {
                 tStr = @"4";
             }else if([_param[@"imgType"] integerValue] == 4){
                 tStr = @"1";
             }else if([_param[@"imgType"] integerValue] == 5){
                 tStr = @"2";
             }
             id extParams = _param[@"extParams"];
             if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
                 [dic addEntriesFromDictionary:extParams];
             }
             dic[@"i_pic_type"]=tStr;
             dic[@"i_recognition_state"]=resultVo.errorNo == 0? @"1":@"0";
             [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_UTime.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_UTime.actionId",@"open"]] attributes:dic];
         }
         
         if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Upload.objectId",@"open"]]) {
             NSDate *n = [NSDate date];
             NSTimeInterval diff = [n timeIntervalSince1970] - tempInterval;
             NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:5];
             dic[@"i_upload_time"]=[NSNumber numberWithInteger:diff];
             dic[@"i_pic_from"]=[_param[kParam_action] isEqualToString:@"phone"]? @"2":@"1";
             NSString *tStr = @"3";
             if ([_param[@"imgType"] integerValue] == 3) {
                 tStr = @"4";
             }else if([_param[@"imgType"] integerValue] == 4){
                 tStr = @"1";
             }else if([_param[@"imgType"] integerValue] == 5){
                 tStr = @"2";
             }
             id extParams = _param[@"extParams"];
             if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
                 [dic addEntriesFromDictionary:extParams];
             }
             dic[@"i_pic_type"]=tStr;
             dic[@"i_upload_state"]=resultVo.errorNo == 0? @"1":@"0";
             [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Upload.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Upload.actionId",@"open"]] attributes:dic];
         }
     }];
}

- (void)UploadFileFinished:(NSArray *)response
{
    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
    
    NSData *hexData = _imageData;
    if (!hexData) {
        TKLogInfo(@"hexData is nil");
        return;
    }
    
    NSString *tmpBase64 = [TKBase64Helper stringWithEncodeBase64Data:hexData];
    
    NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tmpBase64];
    
    reqParam[@"funcNo"]=@"60050";
    
    reqParam[@"base64"]=base64;

    reqParam[@"error_no"]=@"0";
    
    if ([_param[@"imgType"] integerValue] == 4 || [_param[@"imgType"] integerValue] == 5 || [_param[@"imgType"] integerValue] == 1 || [_param[@"imgType"] integerValue] == 2 || [_param[@"imgType"] integerValue] == 207 || [_param[@"imgType"] integerValue] == 208 || [@"1" isEqualToString:@"isAppOcr"]) {//上传身份证正反面
        
        if (response.count == 0) {
            
            reqParam[@"error_no"]=@"-6000212";
            reqParam[@"error_info"]=@"数据有误，请重试";
            
        }else{
            
            NSDictionary *source =   (NSDictionary*)[response objectAtIndex:0];
            [reqParam addEntriesFromDictionary:source];
            reqParam[@"idNo"]=source[@"idno"];
            reqParam[@"custName"]=source[@"custname"];
            reqParam[@"native"]=source[@"native"];
            reqParam[@"ethnicName"]=source[@"ethnicname"];
            reqParam[@"birthday"]=source[@"birthday"]? source[@"birthday"]:source[@"borthday"];
            reqParam[@"policeOrg"]=source[@"policeorg"];
            reqParam[@"idbeginDate"]=source[@"idbegindate"];
            reqParam[@"idendDate"]=source[@"idenddate"];
            reqParam[@"usersex"]=source[@"usersex"]?source[@"usersex"]:source[@"sex"];
            reqParam[@"idno"]=source[@"idno"];
            reqParam[@"name"]=source[@"name"];
            reqParam[@"address"]=source[@"address"];
            reqParam[@"sex"]=source[@"sex"];
            reqParam[@"userSex"]=source[@"usersex"]?source[@"usersex"]:source[@"sex"];
        }
        
    }
    
    [self iosCallJsWithParam:reqParam];
}

- (void)UploadFiledFailed:(NSString *)results
{
    TKLogInfo(@"UploadFiledFailed:%@", results);
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"请重试" message:results preferredStyle:UIAlertControllerStyleAlert];
      // 2.创建并添加按钮
      UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {

      }];

      [alertController addAction:okAction];
      [self.currentViewCtrl presentViewController:alertController animated:YES completion:nil];
}



#pragma mark -implement 拍照完成后处理
- (void)takeFinish:(id)result
{
    if (result) {
        
        if (_param[@"isUpload"] && [_param[@"isUpload"] integerValue] == 0) {//提交证件图给H5上传
            
            [self showImage:result];
            
        }else{
            
            dispatch_async(dispatch_get_main_queue(), ^{
                mHUD = [[TKMBProgressHUD alloc] initWithView:self.currentViewCtrl.view];
                [self.currentViewCtrl.view addSubview:mHUD];
                mHUD.dimBackground = YES;
                mHUD.labelText = @"请稍后...";
                mHUD.mode = TKMBProgressHUDModeIndeterminate;
                mHUD.delegate = self;
                [mHUD show:YES];
            });
            
            dispatch_async(dispatch_get_global_queue(0, 0), ^{
                NSData* data =[TKImageHelper compressImageData:result toByte:(_param[@"compressSize"]? [_param[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];

                
                _imageData = data;
                
                [self upLoadImage:data];
            });
        }
    }
    
}

-(void)didReceiveMemoryWarning
{
    if (_service)
    {
        [_service clearAllRequest];
    }
}

#pragma mark -implement MBProgressHUDDelegate
- (void)hudWasHidden:(TKMBProgressHUD *)hud {
    // Remove HUD from screen when the HUD was hidded
    [mHUD removeFromSuperview];
    
    mHUD = nil;
}

@end
