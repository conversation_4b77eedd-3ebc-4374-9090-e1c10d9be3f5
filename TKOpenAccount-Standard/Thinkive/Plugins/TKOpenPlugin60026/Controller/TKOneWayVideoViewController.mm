//
//  TKOneWayVideoViewController.m
//  OneWayVideo
//
//  Created by <PERSON>ie on 2019/3/2.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKOneWayVideoViewController.h"

//#import <AdSupport/ASIdentifierManager.h>

#import "TKOneWayVideoView.h"
#import "TKOneWayVideoEndView.h"
#import "TKVideoRecordManager.h"


@interface TKOneWayVideoViewController ()



@end

@implementation TKOneWayVideoViewController

//#pragma mark - View life cycle
//- (void)viewDidLoad {
//    
////    [self.view addSubview:self.avPreviewView];
//    
//    [super viewDidLoad];
//    
////    [self.view bringSubviewToFront:self.tkOneView];
//    
//    
//}

#pragma mark - Selector
- (void)stopRecordingAction {

    [super stopRecordingAction];
    
}


/**
 <AUTHOR> 2019年04月08日19:31:25
 @重新开始单向视频流程
 */
- (void)restartOneVideo {
    
//    if ([TKCommonUtil isHeadsetPluggedIn]) {
//        [self showAlertView:@"提示" message:@"您当前在使用耳机，请移除耳机后重试" tag:7001];
//        return;
//    }
    
    [super restartOneVideo];
    
//    self.isEndOneWayVideo = NO;
}


- (NSString *)pluginCallBackfuncNo {
    
    return @"60027";
}


- (void)sendCallBack:(NSMutableDictionary *)callJsParam {
    if (self.delegate && [self.delegate respondsToSelector:@selector(tkSmartOneWayVideoDidComplete:)]) {
       [self.delegate tkSmartOneWayVideoDidComplete:callJsParam];
    }else{
        [super sendCallBack:callJsParam];
    }
}

//继续视频客服排队（针对视频接通时候单独处理退出视频再重新打开排队）
//- (void)uploadBtnClick{
//    
//    [super uploadBtnClick];
//    
//    NSMutableDictionary *callJsParam = [NSMutableDictionary dictionary];
//    callJsParam[@"funcNo"] = [self pluginCallBackfuncNo];
//    callJsParam[@"start_time"] = self.recordStartDateString;
//
//    // ----------设置你想要的格式,hh与HH的区别:分别表示12小时制,24小时制
//    //现在时间,你可以输出来看下是什么格式
//    //----------将nsdate按formatter格式转成nsstring
//    callJsParam[@"end_time"]=[TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
//    callJsParam[@"video_length"] = self.videoLength;
//    if ([self.requestParam[@"resultType"] isEqualToString:@"2"]) {
//        //给文件路径
////        callJsParam[@"filePath"] = [self getLocalOneWayVideoPath];
//        callJsParam[@"filePath"] = self.fullVideoFileURLString;
//    }else{
////        NSString *tempBase64=[TKBase64Helper stringWithEncodeBase64Data:[NSData dataWithContentsOfFile:[self getLocalOneWayVideoPath]]];
//        NSString *tempBase64=[TKBase64Helper stringWithEncodeBase64Data:[NSData dataWithContentsOfFile:self.fullVideoFileURLString]];
//
//        callJsParam[@"videoBase64"]=[NSString stringWithFormat:@"data:video/mp4;base64,%@", tempBase64];
//    }
//
//    // 增加签名
//    callJsParam[@"videoSign"] = [TKCommonUtil signOneWayVideo:[self getLocalOneWayVideoPath]];
//
//    NSString *tmpImgBase64 = [TKBase64Helper stringWithEncodeBase64Data:UIImageJPEGRepresentation(self.videoFirstImage, 0.7)];
//    callJsParam[@"headBase64"] = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tmpImgBase64];
//    callJsParam[@"error_no"] = @"0";
//    
//    if ([self skipPreview]) {
//        //isSuccessShow = 0 不显示结果页面时返回；给h5多返回参数
//        callJsParam[@"detectFailureCount"]=[NSString stringWithFormat:@"%d",self.faceDetectManager.faceDetectFailureCount];//人脸不在框次数
//        callJsParam[@"compareFailureCount"]=[NSString stringWithFormat:@"%d",self.faceDetectManager.faceCompareFailureCount];//人脸比对失败次数
//        callJsParam[@"multipleFaceCount"]=[NSString stringWithFormat:@"%d",self.faceDetectManager.faceNumberFailureCount];//多人脸次数
//    }
//    
//    [self sendCallBack:callJsParam];
//    
//    // 本地录制要先返回结果给h5，否则父类方法会把视频删掉，导致返回给h5的视频为空
//    [self exitProcess];
//}


///**
// <AUTHOR> 2019年04月13日14:19:09
// @单向视频结果页播放视频
// */
- (void)playVideo{
    
//    //扬声器播放
//    NSError *error = nil;
//    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
//    [audioSession setCategory:AVAudioSessionCategoryPlayback withOptions:audioSession.categoryOptions error:&error];
////    [audioSession setActive:YES error:&error];
//    if (error) {
//        TKLogDebug(@"AVAudioSession setCategory错误，错误信息%@", error.localizedDescription);
//    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        TKLogInfo(@"思迪录制日志:查看录制视频");
        
        if (!self.player.isPlaying) {
            
            if(self.player.playState==TKZFPlayerPlayStatePlayStopped){
                [self.player replay];
            }else if(self.player.playState == TKZFPlayerPlayStatePlayFailed && !self.player.isDownloadingFile){
                [self.player reloadPlayer];
            }else{
                [self.player play];
            }
        }else{
            [self.player pause];
            [self.tkOneEndView stopPlay:([self.videoLength floatValue]-[self.tkOneEndView.secondsString floatValue]) longestTime:[self.videoLength floatValue]];
            self.tkOneEndView.secondsString = self.tkOneEndView.secondsString;
        }
        
        [self.tkOneEndView changePlayStatus:self.player.isPlaying];
    });
    
}

/**
 <AUTHOR> 2022年11月11日13:49:11
 @单向视频结果页重新播放视频
 */
- (void)replayVideo{
    
    [self.player replay];
    [self.tkOneEndView changePlayStatus:self.player.isPlaying];
    self.tkOneEndView.secondsString = self.videoLength;
    [self.tkOneEndView stopPlay:([self.videoLength floatValue]-[self.tkOneEndView.secondsString floatValue]) longestTime:[self.videoLength floatValue]];
}

/**
 <AUTHOR> 2019年04月13日14:21:40
 @单向视频结果页提交
 */
- (void)endSubmit{
    
    [super endSubmit];
    
    [self.tkOneEndView changePlayStatus:self.player.isPlaying];
}

-(void)enterBackground:(NSNotification *)notif{
    //    [self showAlertView:@"提示" message:@"视频过程中请勿切换App或锁屏" tag:7001];
    if (self.isRecording) {
        [self failActionCallJsWith:@"-3" errorMsg:@"视频过程中请勿切换App或锁屏"];
    }
    
    if(self.player.isPlaying){
        [self.player pause];
        [self.tkOneEndView stopPlay:([self.videoLength floatValue]-[self.tkOneEndView.secondsString floatValue]) longestTime:[self.videoLength floatValue]];
        self.tkOneEndView.secondsString = self.tkOneEndView.secondsString;
        [self.tkOneEndView changePlayStatus:self.player.isPlaying];
    }
}

- (void)audioRouteChangeListenerCallback:(NSNotification *)notif{
    
    [super audioRouteChangeListenerCallback:notif];
    
    if (self.player && self.player.isPlaying) {
        //视频回放中插拔耳机行为都暂停播放
        dispatch_async(dispatch_get_main_queue(), ^{
            
            [self.player pause];
            [self.tkOneEndView stopPlay:([self.videoLength floatValue]-[self.tkOneEndView.secondsString floatValue]) longestTime:[self.videoLength floatValue]];
            self.tkOneEndView.secondsString = self.tkOneEndView.secondsString;
            [self.tkOneEndView changePlayStatus:self.player.isPlaying];
        });
    }
}

- (void)previewVideo
{
    // 这里有个坑，需要先updateTipViewWithTipArr再加载url。因为结果页播放图层提前加载，updateTipViewWithTipArr会移除掉播放图层
    // 视频录制结束才抓取url图片
    self.player.contentView = self.tkOneEndView.videoShowImgView; // 每次重新录制都是新创建view.因此要重新设置
    
    self.tkOneEndView.secondsString = self.videoLength;
    
    if (self.isLocalRecord) {
            self.player.assetURL = [NSURL fileURLWithPath:[self fullVideoFileURLString]];
    } else {
            self.player.assetURL = [NSURL URLWithString:[self fullVideoFileURLString]];
    }
}

#pragma mark - TKPlayerDelegate
/// 开始加载资源回调
- (void)tkPlayerPlay:(TKPlayer *)player didStartLoadUrl:(NSURL *)url {
    if ([self.tkOneEndView respondsToSelector:@selector(showLoadingVideo:)]) {
        
        [self.tkOneEndView showLoadingVideo:YES];
    }
}

/**
 @视频播放完成通知
 */
- (void)tkPlayerPlayDidEnd: (TKPlayer *)player {
    [self.tkOneEndView changePlayStatus:NO];
    self.tkOneEndView.secondsString = self.videoLength;
    [self.tkOneEndView stopPlay:([self.videoLength floatValue]-[self.tkOneEndView.secondsString floatValue])longestTime:[self.videoLength floatValue]];
    
}

/// 播放状态更新
- (void)tkPlayerPlay:(TKPlayer *)player didUpdateStatus:(AVPlayerStatus)stauts {
    
    //    return;
    if (stauts == AVPlayerStatusReadyToPlay) {
        
        if ([self.tkOneEndView respondsToSelector:@selector(showLoadingVideo:)]) {
            
            [self.tkOneEndView showLoadingVideo:NO];
        }
    } else if (stauts == AVPlayerStatusFailed) {
        if ([self.tkOneEndView respondsToSelector:@selector(showLoadingVideo:)]) {
            
            [self.tkOneEndView showLoadingVideo:NO];
        }
    }
}

- (void)tkPlayerPlay:(TKPlayer *)player didUpdatePlayTime:(NSTimeInterval)time {
    if(self.player.isPlaying){
        // 当前播放总时间
        float playCountdown = [self.videoLength floatValue] - time;
        self.tkOneEndView.secondsString = [NSString stringWithFormat:@"%f",(playCountdown > 0 ? playCountdown : 0)];
        [self.tkOneEndView playTime:time longestTime:player.totalTime];
    }
}

- (void)tkPlayerLoadResourceFail:(TKPlayer *)player url:(nonnull NSURL *)url {
    
    //    return;
    [self.tkOneEndView showLoadingVideo:NO];
    
    [self showAlertView:@"温馨提示" message:@"视频播放失败，请重试" tag:7001];
}


#pragma mark - lazyloading

/**
 <AUTHOR> 2019年04月03日10:09:41
 @初始化懒加载单向视频界面
 @return 单向视频界面
 */
-(UIView<TKBaseVideoRecordViewProtocol> *)tkOneView{
    if (!super.tkOneView) {
        super.tkOneView = [[TKOneWayVideoView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        super.tkOneView.delegate = self;
    }
    return super.tkOneView;
}

/**
 <AUTHOR>
 @初始化单向视频结果页面
 */
- (UIView<TKBaseVideoRecordEndViewProtocol>  *)tkOneEndView{
    if (!super.tkOneEndView) {
        super.tkOneEndView = [[TKOneWayVideoEndView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        super.tkOneEndView.delegate = self;
    }
    return super.tkOneEndView;
}


/// 视频录制工具类
- (NSObject<TKRecordManagerProtocol> *)recordManager {
    if (!super.recordManager) {
        super.recordManager = [[TKVideoRecordManager alloc] initWithConfig:self.requestParam];
        super.recordManager.delegate = self;
//        super.recordManager.contentView = self.tkOneView.avPreviewView;
        super.recordManager.contentView = self.avPreviewView;
        super.recordManager.videoFilePath = [self getLocalOneWayVideoPath];
    }
    return super.recordManager;
}


/// 播放视频工具视图
- (TKPlayerControlView *)playerControlView{
    
    return nil;
}

- (TKZFPlayerController *)playerController {
    
    return nil;
}

@end
