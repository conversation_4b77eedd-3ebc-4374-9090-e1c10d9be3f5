//
//  TKOneWayVideoEndView.m
//  OneWayVideo
//
//  Created by <PERSON>ie on 2019/4/13.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKOneWayVideoEndView.h"
#import "TKOpenTipView.h"

#define TK_ONEVIEW_TIP_LABEL_END_COLOR_BLUE  [TKUIHelper colorWithHexString:@"#0354C2"]
#define TK_ONEVIEW_TIP_LABEL_END_COLOR_BLACK  [TKUIHelper colorWithHexString:@"#333333"]
#define TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE  [TKUIHelper colorWithHexString:@"#FFFFFF"]
#define TK_OPEN_MIN(a,b,c) (a<b?(a<c?a:c):(b<c?b:c))

@interface TKOneWayVideoEndView()

@property (nonatomic, assign) BOOL isPlay;


@end

@implementation TKOneWayVideoEndView
@synthesize playVideoBtn = _playVideoBtn;
@synthesize playTipLabel = _playTipLabel;


- (void)viewInit {
    [super viewInit];
    
    [self changePlayStatus:self.isPlay];
}

/**
 <AUTHOR>
 @修改播放状态页面
 */
-(void)changePlayStatus:(BOOL)isPlay{
    self.isPlay = isPlay;
    if (!isPlay) {
        [self.playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play1.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        self.playTipLabel.text = @"点击预览";
    }else{
        [self.playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_stop1.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        self.playTipLabel.text = @"点击暂停";
    }
    
    // 设置剩余的播放时间
    self.secondsString = self.secondsString;
}

-(UIButton *)playVideoBtn{
    if (!_playVideoBtn) {
        _playVideoBtn=[[UIButton alloc] initWithFrame:CGRectMake(0, 0, 44, 44)];
        [_playVideoBtn setFrameY:(self.videoShowBgView.TKHeight-44)/2.0f];
        [_playVideoBtn setFrameX:(self.videoShowBgView.TKWidth-44)/2.0f];
        [_playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_playVideoBtn addTarget:self action:@selector(playAction:) forControlEvents:UIControlEventTouchUpInside];
        _playVideoBtn.layer.zPosition = MAXFLOAT;//总是保持在最上面避免被播放视频的view挡住
    }
    return _playVideoBtn;
}


/**
 <AUTHOR> 2019年04月26日17:31:53
 @初始化懒加载视频播放按钮底部提示文字
 @return 视频播放按钮底部提示文字
 */
-(UILabel *)playTipLabel{
    if (!_playTipLabel) {
        _playTipLabel=[[UILabel alloc] init];
        _playTipLabel.text = @"点击预览";
        _playTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        _playTipLabel.textColor = [UIColor colorWithWhite:1 alpha:0.77];
        _playTipLabel.textAlignment=NSTextAlignmentCenter;
        
        float x=(self.videoShowBgView.TKWidth-self.videoShowImgView.TKWidth)/2.0f;
        _playTipLabel.frame=CGRectMake(x, 0, self.videoShowImgView.TKWidth, 22);
        [_playTipLabel setFrameY:self.playVideoBtn.TKBottom+17];
        _playTipLabel.layer.zPosition = MAXFLOAT;//总是保持在最上面避免被播放视频的view挡住
    }
    return _playTipLabel;
}

@end
