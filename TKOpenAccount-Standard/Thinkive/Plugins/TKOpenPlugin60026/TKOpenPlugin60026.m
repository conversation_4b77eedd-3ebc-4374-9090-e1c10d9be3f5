//
//  TKOpenPlugin60026.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2019/4/27.
//  Copyright © 2019 thinkive. All rights reserved.
//

#import "TKOpenPlugin60026.h"
#import "UIViewController+TKAuthorityKit.h"
#import "TKOneWayVideoViewController.h"
#import <AVFoundation/AVFoundation.h>
#import <MediaPlayer/MediaPlayer.h>
#import "TKVideoAlertView.h"

@interface TKOpenPlugin60026 ()<TKOneWayVideoResultDelegate,TKVideoAlertViewDelegate>
@property (nonatomic, strong) NSMutableDictionary *requestParam;//h5带过来的参数

@property (nonatomic, readwrite, assign) BOOL isInvokeing; // 是否正在被调用
@property (nonatomic, strong) TKVideoAlertView *videoAlertView;//视频挂断提示框
@end

@implementation TKOpenPlugin60026
-(ResultVo *)serverInvoke:(id)param{
    
    ResultVo *resultVo = [[ResultVo alloc]init];
//    param[@"aliYunKey"]=@"default";
//    param[@"aliYunToken"]=@"default";
//    param[@"aliYunUrl"]=@"ws://**************:8101/ws/v1";
//   param[@"mainColor"]=@"#FD671A";
//    param[@"uploadTipString"]=@"请您确认：我是***，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。";
    // 防止h5传一个“”字符串进来代表json对象
    NSDictionary *requestHeaders = (NSDictionary *)[param getObjectWithKey:@"requestHeaders"];
    if ([requestHeaders isKindOfClass:NSString.class]) requestHeaders = [TKDataHelper jsonToDictionary:(NSString *)requestHeaders];
    param[@"requestHeaders"] = requestHeaders;
    
    self.requestParam=param;
    
    // 埋点-单向调用
//    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
//                         subEventName:TKPrivateSubEventNone
//                             progress:TKPrivateEventProgressStart
//                               result:TKPrivateEventResultNone
//                          orientation:TKPrivateVideoOrientationPortrait
//                      oneWayVideoType:TKPrivateOneWayVideoTypeLocalSmart
//                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
//                             eventDic:self.requestParam];
    
    NSArray *questionJson = (NSArray *)[param getObjectWithKey:@"questionJson"];
    NSArray *questionArray = nil;
    if ([[param getObjectWithKey:@"questionArray"] isKindOfClass:NSArray.class]) {
        questionArray = (NSArray *)[param getObjectWithKey:@"questionArray"];
    } else {
        questionArray = [TKDataHelper jsonToArray:[param getStringWithKey:@"questionArray"]];
    }
    
    BOOL questionJsonIsNotEmpty = [questionJson isKindOfClass:NSArray.class] && questionJson.count > 0;
    BOOL questionArrayIsNotEmpty = [questionArray isKindOfClass:NSArray.class] && questionArray.count > 0;
    
    // 非空判断
    if (!questionJsonIsNotEmpty && !questionArrayIsNotEmpty) {
        resultVo.errorNo = -6002607;
        resultVo.errorInfo = @"问题对象json数组|questionArray不能为空";
        return resultVo;
    }
    
    // 针对旧版字段做适配
    if (questionJsonIsNotEmpty && !questionArrayIsNotEmpty) {
        self.requestParam = [self convertOldParamToNewParam:param];
    }
    
//    TKLogInfo(@"静音检查，准备走流程打开视频录制页面");
    
//    if ([TKCommonUtil isHeadsetPluggedIn]) {
//        dispatch_async(dispatch_get_main_queue(), ^{
//            [self.currentViewCtrl.view addSubview:self.videoAlertView];
//            self.videoAlertView.describeLabel.text=@"您当前在使用耳机，请移除耳机后重试";
//            self.videoAlertView.titleLabel.text=@"提示";
//            [self.videoAlertView setOnlyBtnTitle:@"确定"];
//        });
//        return resultVo;
//    }
    
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //是否在调用插件前展示介绍页面
        if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
            [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Camera),@(TKAuthorizationType_Audio)] authCallBacks:nil btnCallBack:^{
                [self.currentViewCtrl tkIsMicrophonePermissions:^{
                                        
                    [self.currentViewCtrl tkIsCameraPermissions:^{
                        [self handleAuthorized:param];
                    }];
                                                
                }];
            }];
        }else{
            [self.currentViewCtrl tkIsMicrophonePermissions:^{
                                    
                [self.currentViewCtrl tkIsCameraPermissions:^{
                    [self handleAuthorized:param];
                }];
                                            
            }];

        }

    });
    
    
    
     return resultVo;
}

- (NSMutableDictionary *)convertOldParamToNewParam:(NSDictionary *)param
{
    NSMutableDictionary *tempParam = [NSMutableDictionary dictionaryWithDictionary:param];
    
    // 1. 处理原来的prepareWords
    NSString *prepareWords = [tempParam getStringWithKey:@"prepareWords"];
    prepareWords = [TKStringHelper isNotEmpty:prepareWords] ? prepareWords : @"请您保持全脸在人像框内。";
    [tempParam removeObjectForKey:@"prepareWords"];
    
    // 转为beforeVideoArray
        NSMutableArray *beforeVideoArray = [[NSMutableArray alloc] init];
    NSMutableDictionary *beforeVideoDic = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    beforeVideoDic[@"fileSource"] = @"2";
    beforeVideoDic[@"tipContent"] = prepareWords;
    [beforeVideoArray addObject:beforeVideoDic];
    tempParam[@"beforeVideoArray"] = [TKDataHelper arrayToJson:beforeVideoArray];
    
    // 2. 处理原来的questionJson
//    NSArray *questionJson = [TKDataHelper jsonToArray:[param getStringWithKey:@"questionJson"]];
    NSArray *questionJson = (NSArray *)[tempParam getObjectWithKey:@"questionJson"];
    NSMutableArray *questionArray = [[NSMutableArray alloc] init];
    for (int i = 0; i < questionJson.count; i++) {
        NSMutableDictionary *oldQuestionDic = questionJson[i];
        
        // 转为questionArray
        NSMutableDictionary *newQuestionDic = [[NSMutableDictionary alloc] init];
        //提示问题会语音播报
        newQuestionDic[@"fileSource"] = @"2";
        newQuestionDic[@"tipContent"] = [oldQuestionDic getStringWithKey:@"tip_content"];
        newQuestionDic[@"tipTitleStr"] = [oldQuestionDic getStringWithKey:@"tip_title"];
        newQuestionDic[@"promptStr"] = [NSString stringWithFormat:@"请回答%@", [oldQuestionDic getStringWithKey:@"prompt"]];   // 回答提示
        newQuestionDic[@"standardans"] = [oldQuestionDic getStringWithKey:@"standardans"];   //需要回答的问题，正确回答的正则
        newQuestionDic[@"failans"] = [oldQuestionDic getStringWithKey:@"failans"]; //需要回答的问题，错误回答的newQuestionDic
        newQuestionDic[@"waitTime"] = [oldQuestionDic getStringWithKey:@"wait_time"];  //该问题预留的回答时间
        newQuestionDic[@"noAnswerPromptTime"] = [oldQuestionDic getStringWithKey:@"noAnswerPromptTime"];  // 无回答提示时间，必须比waitTime小
//        // 错误提示
//        NSMutableDictionary *errorTip = [[NSMutableDictionary alloc] init];
//        errorTip[@"fileSource"] = @"2";
//        errorTip[@"tipContent"] = @"回答不通过，本次视频见证失败，您可重新发起录制。";
//        newQuestionDic[@"errorTip"] = errorTip;  //错误提示
//        // 没有声音提示
//        NSMutableDictionary *noVoiceTip = [[NSMutableDictionary alloc] init];
//        noVoiceTip[@"fileSource"] = @"2";
//        noVoiceTip[@"tipContent"] = @"您好，未听清您的回复，请您重新回答。";
//        newQuestionDic[@"noVoiceTip"] = noVoiceTip;  //错误提示
        // 正确回答提示
        //    NSMutableDictionary *standardansTip = [[NSMutableDictionary alloc] init];
        //    standardansTip[@"fileSource"] = @"2";
        //    standardansTip[@"tipContent"] = @"好的。谢谢您";
        //    questionDic1[@"standardansTip"] = standardansTip;
        [questionArray addObject:newQuestionDic];
    }
    [tempParam removeObjectForKey:@"questionJson"];

    // 3. 处理原来的endWords-也要录制进视频
    // 不录制进视频
    //    NSString *endWords = [tempParam getStringWithKey:@"endWords"];
    //    endWords = [TKStringHelper isNotEmpty:endWords] ? endWords : @"录制完毕，感谢您的配合。";
    //    [tempParam removeObjectForKey:@"endWords"];
            
    //    // 转为afterVideoArray
    //    NSMutableArray *afterVideoArray = [[NSMutableArray alloc] init];
    //    NSMutableDictionary *afterVideoDic1 = [[NSMutableDictionary alloc] init];
    //    //提示问题会语音播报
    //    afterVideoDic1[@"fileSource"] = @"2";
    //    afterVideoDic1[@"tipContent"] = endWords;
    //    [afterVideoArray addObject:afterVideoDic1];
    // 录制进视频
    NSString *endWords = [tempParam getStringWithKey:@"endWords"];
    endWords = [TKStringHelper isNotEmpty:endWords] ? endWords : @"录制完毕，感谢您的配合。";
    // 转为questionArray
    NSMutableDictionary *newQuestionDic = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    newQuestionDic[@"fileSource"] = @"2";
    newQuestionDic[@"tipContent"] = endWords;
    [questionArray addObject:newQuestionDic];
    [tempParam removeObjectForKey:@"endWords"];
    
    tempParam[@"questionArray"] = [TKDataHelper arrayToJson:questionArray];
    
    // 4. 增加errorTipJson
//        //  全局错误提示语音
//        NSMutableDictionary *errorTipDic = [[NSMutableDictionary alloc] init];
//        NSMutableDictionary *overNoVoiceCountTip = [[NSMutableDictionary alloc] init];
//        // 多次错误
//        overNoVoiceCountTip[@"fileSource"] = @"2";
//        overNoVoiceCountTip[@"tipContent"] = @"回答不通过，本次视频见证失败，您可重新发起录制。";
//        errorTipDic[@"overNoVoiceCountTip"] = overNoVoiceCountTip;
//
//        // 多张人脸
//        NSMutableDictionary *overPresonCountTip = [[NSMutableDictionary alloc] init];
//        overPresonCountTip[@"fileSource"] = @"2";
//        overPresonCountTip[@"tipContent"] = @"人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。";
//        errorTipDic[@"overPresonCountTip"] = overPresonCountTip;
//
//        // 多次超出屏幕
//        NSMutableDictionary *overScreenCountTip = [[NSMutableDictionary alloc] init];
//        overScreenCountTip[@"fileSource"] = @"2";
//        overScreenCountTip[@"tipContent"] = @"由于长时间未检测到面部在框，本次视频录制失败，请重新录制。";
//        errorTipDic[@"overScreenCountTip"] = overScreenCountTip;
//
//        // 多次人脸比对失败
//        NSMutableDictionary *overCompareCountTip = [[NSMutableDictionary alloc] init];
//        overCompareCountTip[@"fileSource"] = @"2";
//        overCompareCountTip[@"tipContent"] = @"人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。";
//        errorTipDic[@"overCompareCountTip"] = overCompareCountTip;
//    tempParam[@"errorTipJson"] = [TKDataHelper dictionaryToJson:errorTipDic];
    
    // 5. 设置不支持没有回答重复询问
    tempParam[@"maxFailureCountPerAsrNoVoice"] = @"1";
    
    // 6. 标记旧版本，方便兼容旧版本
    tempParam[@"oldVersion"] = @"4.0.0";
    
    return tempParam;
}

- (void)handleAuthorized:(id)param
{
    // 已在调用，拦截重复调用
    if (self.isInvokeing == YES) {
        TKLogInfo(@"拦截重复调用");
        return ;
    };
    
    // 标记正在调用
    self.isInvokeing = YES;
    
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [[AVAudioSession sharedInstance] setActive:YES error:nil];

    CGFloat volume = audioSession.outputVolume;
    //默认音量调整支持h5控制
    float defaultVolume=TKSmartOpenVolume;
    if (self.requestParam[@"defaultVolume"]) {
        defaultVolume=[self.requestParam[@"defaultVolume"] intValue]/100.00f;
    }
    if ((volume+0.05)<defaultVolume) {
        //直接调整音量api还能使用，先改音量不提示用户
        MPMusicPlayerController *mp=[MPMusicPlayerController applicationMusicPlayer];
        mp.volume=defaultVolume;//0为最小1为最大
    }
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    TKOneWayVideoViewController *oneWayCtr=[[TKOneWayVideoViewController alloc] initWithParam:self.requestParam];
    oneWayCtr.delegate=self;
    
    if ([self.requestParam[@"isPush"] intValue]==1) {
        oneWayCtr.isHideSystemNavigationBar=@"1";
        [self.currentViewCtrl.navigationController pushViewController:oneWayCtr animated:YES];
    }else{
        [self.currentViewCtrl presentViewController:oneWayCtr animated:YES completion:nil];
    }
   


}

//结果信息
-(void)tkSmartOneWayVideoDidComplete:(NSMutableDictionary *)result{
    [self iosCallJsWithParam:result];
    
    // 重置标志位
    self.isInvokeing = NO;
    TKLogInfo(@"重置标志位");
}

#pragma mark lazyloading
/**
 <AUTHOR> 2022年05月25日09:43:41
 @初始化懒加载videoAlertView
 @return videoAlertView
 */
-(TKVideoAlertView *)videoAlertView{
    if (!_videoAlertView) {
        _videoAlertView=[[TKVideoAlertView alloc] initWithFrame:self.currentViewCtrl.view.frame requestParams:self.requestParam];
        _videoAlertView.delegate=self;
    }
    return _videoAlertView;
}

#pragma mark TKVideoAlertViewDelegate


//取消按钮事件
-(void)cancelVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}

//继续按钮事件
-(void)takeVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}

//独立按钮事件
-(void)onlyVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}
@end
