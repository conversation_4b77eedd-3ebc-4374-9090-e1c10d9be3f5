//
//  TKNLSPlayAudio.h
//  AliyunNlsClientSample
//
//  Created by 刘方 on 2/25/16.
//  Copyright © 2016 阿里巴巴. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AudioToolbox/AudioToolbox.h>
#define DEBUG_MODE
#ifdef DEBUG_MODE
#define TLog( s, ... ) NSLog( s, ##__VA_ARGS__ )
#else
#define TLog( s, ... )
#endif

//#define EVERY_READ_LENGTH 200
#define NUM_BUFFERS 3
//#define QUEUE_BUFFER_SIZE (640)

enum PlayerState {
    idle = 0,
    playing = 1,
    paused = 2,
    stopped = 3,
    draining = 4,
};

/**
 *@discuss TKNLSPlayAudio 回调接口
 */
@protocol NlsPlayerDelegate <NSObject>
/**
 * @Player数据完成播放回调
 */
- (void)playerDidFinish;

/**
 * @Player数据完成超时回调
 */
- (void)playerDidTimeOut;

/**
 * @Player播放失败回调
 */
- (void)playerDidFail:(NSString *)errMsg;

/**
 * @Player播放进度
 */
- (void)playerHavePlayed:(NSInteger)currentPlayIndex willPlayIndex:(NSInteger)willPlayIndex;

@end


@interface TKNLSPlayAudio : NSObject {
    
    // 音频播放队列
    AudioQueueRef audioQueue;
    // 音频缓存
    AudioQueueBufferRef audioQueueBuffers[NUM_BUFFERS];
    
}

@property(nonatomic, weak) id<NlsPlayerDelegate> delegate;
@property (nonatomic, readwrite, assign) NSTimeInterval timeOut;    // 播放器没有收到数据空等待总时间.默认10.0s

// 播放采样率，默认16000
@property (nonatomic, readwrite, assign) int mSampleRate;

// 音频参数,默认pcm格式
@property (nonatomic, readwrite, assign) AudioStreamBasicDescription audioDescription;

// 当前正在播报的字节index
@property (nonatomic, readwrite, assign) int currentPlayIndex;

-(void)play;
-(void)pause;
-(void)resume;
-(void)stop;
-(void)drain;
-(void)cleanup;
-(void)setstate :(PlayerState)state;
-(void)write:(const char*)buffer Length:(int)len;
-(int)GetAudioData:(AudioQueueBufferRef)buffer;
- (int)getBufferFilledSize; // 获取已填充缓冲区大小

@end
