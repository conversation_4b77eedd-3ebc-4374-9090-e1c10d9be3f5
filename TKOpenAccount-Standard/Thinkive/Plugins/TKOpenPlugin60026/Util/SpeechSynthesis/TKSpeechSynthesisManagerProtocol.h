//
//  TKSpeechSynthesisManagerProtocol.h
//  TKApp_H5SHARE
//
//  Created by <PERSON> on 2020/12/9.
//  Copyright © 2020 liubao. All rights reserved.
//


@protocol TKSpeechSynthesisManagerDelegate <NSObject>

@optional

// 语音合成开始回调
- (void)speechSynthesisDidStart;


/// 多段语音合成开始回调
/// - Parameters:
///   - index: 当前正在合成的语音索引
///   - synthesisArray: 要合成的语音数组
- (void)speechSynthesisDidStartWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray;

// 语音合成结束回调
// @param text 合成语音的文本
// @param speechSynthesisFilePath 语音合成后保存的文件路径
- (void)speechSynthesisDidComplete:(NSString *)text speechSynthesisFilePath:(NSString *)speechSynthesisFilePath;

/// 语音合成失败
- (void)speechSynthesisDidFail:(NSString *)errorMsg;

// 语音合成播放结束回调
- (void)speechSynthesisDidPlayDone;

/// 多段语音合成播放结束回调
/// - Parameters:
///   - index: 当前正在播放的语音索引
///   - synthesisArray: 要合成的语音数组
- (void)speechSynthesisDidPlayDoneWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray;

/// 多段语音合成播放进度回调
/// - Parameters:
///   - startIndex: 当前播放开始索引
///   - endIndex: 当前播放结束索引
///   - duration: 播放持续时间
- (void)speechSynthesisDidPlayWithStartIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration;

// 语音合成buffer
- (void)speechSynthesisDidDataBuffer:(char *)buffer len:(int)len;

@end


@protocol TKSpeechSynthesisManagerProtocol <NSObject>

@required
/// 代理对象
@property (nonatomic, readwrite, weak) id<TKSpeechSynthesisManagerDelegate> delegate;

/// 配置字典
@property (nonatomic, readwrite, weak) NSDictionary *configParam;

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam;


// 提前合成语音。先检查沙盒和bundle中是否存在文件，若没有才合成
// @param textArray 待合成的语音文本数组
- (void)synthesisInAdvanceWithTextArray:(NSArray *)textArray tipSpeed:(NSString *)tipSpeed;

// 合成语音
// @param text 待合成文本
// @param autoPlay 是否自动播放
- (void)syntheticWithText:(NSString *)text autoPlay:(BOOL)autoPlay tipSpeed:(NSString *)tipSpeed;

// 合成语音并播放
// @param text 待合成文本
- (void)syntheticAndPlay:(NSString *)text tipSpeed:(NSString *)tipSpeed;
- (void)syntheticAndPlayContents:(NSArray *)texts tipSpeed:(NSString *)tipSpeed;

/// 播放远程文件
/// - Parameters:
///   - url: 资源地址
///   - tipSpeed: 播放速度
///   - needRequestToken: 是否需要先申请token
///   - playRangeModels: 字节列表模型
- (void)playRemoteUrl:(NSString *)url tipSpeed:(NSString *)tipSpeed needRequestToken:(BOOL)needRequestToken playRangeModels:(NSArray *)playRangeModels;

// 停止合成和播放
- (void)stop;

// 停止合成
- (void)stopSynthetic;

// 停止播放
- (void)stopPlay;

// 取消
- (void)cancel;

// 是否正在合成
- (BOOL)isSyntheticing;

// 是否正在播放
- (BOOL)isPlaying;

/// 是否能预合成语音。
- (BOOL)canSynthesisInAdvance;

@end


