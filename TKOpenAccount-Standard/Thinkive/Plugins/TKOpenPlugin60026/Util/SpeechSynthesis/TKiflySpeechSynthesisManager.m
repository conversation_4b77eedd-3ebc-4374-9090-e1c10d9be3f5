//
//  TKiflySpeechSynthesisManager.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/4/2.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKiflySpeechSynthesisManager.h"

// 讯飞语音合成
#import <AIPIFlyMSC/AIPIFlyMSC.h>
#import <AIPIFlyMSC/AIPIFlySpeechSynthesizer.h>
#import <AIPIFlyMSC/AIPIFlySpeechUtility.h>
#import <AIPIFlyMSC/AIPIFlySpeechError.h>
#import <AIPIFlyMSC/AIPIFlySpeechConstant.h>

@interface TKiflySpeechSynthesisManager()<TKSpeechSynthesisManagerDelegate,AIPIFlySpeechSynthesizerDelegate>
{
    
    dispatch_queue_t _speechSynthesisOutputQueue;
}

@property (nonatomic, strong) AIPIFlySpeechSynthesizer *iFlySpeechSynthesizer;//科大讯飞语音合成对象

@property(nonatomic, assign) BOOL isPlayingVoice; //是否开始播放语音
@property(nonatomic, assign) BOOL isSyntheticing; //是否开始合成语音
@property (nonatomic, readwrite, assign) BOOL autoPlay; // 合成后是否自动播放
@property(nonatomic, assign) int checkTTSCatonCount;//检测tts卡顿次数；默认5次，0表示不检查卡顿次数
@property(nonatomic, assign) int currentTTSCatonCount;//当前tts语音卡顿次数
@property(nonatomic, assign) float checkTTSCatonTime;//tts卡顿时长，默认3000ms，0表示不检查卡顿时间提示
@property (nonatomic, readwrite, strong) NSTimer *checkTTSCatonTimer; //检测tts单次卡顿时间，默认3000ms，单位毫秒；不传不检测
@property (nonatomic, assign) CFAbsoluteTime lastUpdateTime;//最后一次记录的卡顿时间
@property(nonatomic, assign) BOOL isSpeakPaused; //是否进入过暂停
@end

@implementation TKiflySpeechSynthesisManager
@synthesize configParam;
@synthesize delegate;

#pragma mark - Init && Dealloc
- (void)dealloc {
//    NSLog(@"TKiflySpeechSynthesisManager dealloc");
    
    if ([AIPIFlySpeechUtility getUtility] != nil) {
        [AIPIFlySpeechUtility destroy];
    }
}

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam {
    if (self = [super init]) {
        self.configParam = configParam;
        self.checkTTSCatonCount=5;
        self.checkTTSCatonTime=3000;
        if([TKStringHelper isNotEmpty:self.configParam[@"checkTTSCatonCount"]]){
            self.checkTTSCatonCount=[self.configParam getIntWithKey:@"checkTTSCatonCount"];
        }
        
        if([TKStringHelper isNotEmpty:self.configParam[@"checkTTSCatonTime"]]){
            self.checkTTSCatonTime=[self.configParam getFloatWithKey:@"checkTTSCatonTime"];
        }
    
        self.currentTTSCatonCount=0;
        NSString *cfgPath = [[NSBundle mainBundle] pathForResource:@"mt_scylla" ofType:@"cfg"];
        NSString *initString = nil;
        //开启识别底层日志
        initString = [[NSString alloc] initWithFormat:@"cfg_path=%@,log_path=%@/Documents/mt_scylla.log",cfgPath, NSHomeDirectory()];
        //开启msc日志
        [AIPIFlySpeechUtility setPrintFlag:NO];
        [AIPIFlySpeechUtility setSaveFlag:YES path:[NSString stringWithFormat:@"%@/Documents/MSCLog/", NSHomeDirectory()]];

        //所有服务启动前，需要确保执行createUtility
        [AIPIFlySpeechUtility createUtility:initString];
        
        // 创建多线程队列
        NSString *queueName = [NSString stringWithFormat:@"com.thinkive.%@outputQueue", self.class];
        const char *queueName_char = [queueName UTF8String];
        _speechSynthesisOutputQueue = dispatch_queue_create(queueName_char, NULL);
    }
    return  self;
}

#pragma mark - Public Selector
- (void)syntheticAndPlay:(NSString *)text tipSpeed:(NSString *)tipSpeed {
    
    [self syntheticWithText:text autoPlay:YES tipSpeed:tipSpeed];
}

- (void)syntheticWithText:(NSString *)text autoPlay:(BOOL)autoPlay tipSpeed:(NSString *)tipSpeed {
    TKLogDebug(@"思迪语音合成调试:语音播放开始, text = %@", text);
    self.isPlayingVoice=NO;
    [self updateSpeedLevelByTipSpeed:tipSpeed];
    
    [self.iFlySpeechSynthesizer startSpeaking:text];
}

- (void)stop {
    [self.iFlySpeechSynthesizer stopSpeaking];
}

- (void)stopSynthetic {
    [self.iFlySpeechSynthesizer stopSpeaking];
}

- (void)stopPlay {
    [self.iFlySpeechSynthesizer stopSpeaking];
}


- (void)cancel{
    [self.iFlySpeechSynthesizer stopSpeaking];
    _iFlySpeechSynthesizer = nil;
}

- (BOOL)isSyntheticing {

    return _isSyntheticing;
}

- (BOOL)isPlaying {
    
    return  _isPlayingVoice;
}

- (BOOL)canSynthesisInAdvance {
    return NO; // 腾讯语音合成不支持下载语音到本地
}


- (void)synthesisInAdvanceWithTextArray:(NSArray *)textArray {
    return;
}


#pragma mark - private Selector
- (void)updateSpeedLevelByTipSpeed:(NSString *)tipSpeed
{
    if ([TKStringHelper isNotEmpty:tipSpeed]) {
        float speed = [tipSpeed floatValue];  // 0.5 ~ 1.5
        speed = speed < 0.5 ? 0.5 : speed;
        speed = speed > 1.5 ? 1.5 : speed;
        int iflyTipSpeed =  (int)((speed - 1) * (500 / 0.5f)); //科大讯飞 [-500, 500]
        NSString *iflySpeedLevel = [NSString stringWithFormat:@"%i", iflyTipSpeed];
        [self.iFlySpeechSynthesizer setParameterEx:[NSString stringWithFormat:@"spd=%@", iflySpeedLevel]];
    }
}

- (void)checkTTSCatonOver:(NSTimer *)timer
{
    TKLogInfo(@"思迪语音合成调试：卡顿时间超过限制");
    self.isSyntheticing = NO;
    self.isPlayingVoice = NO;
    if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
        [self.delegate speechSynthesisDidFail:@"您当前网络卡顿，请检查网络连接情况再重试。"];
    }
}
#pragma mark AIPIFlySpeechSynthesizerDelegate
/*!
 *  结束回调<br>
 *  当整个合成结束之后会回调此函数
 *
 *  @param error 错误码
 */
- (void)onCompletedEx:(AIPIFlySpeechError *)errorCode{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (errorCode.errorCode==0) {
            self.isSyntheticing = NO;
            self.isPlayingVoice = NO;
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
                [self.delegate speechSynthesisDidPlayDone];
            }
        }else{
            self.isSyntheticing = NO;
            self.isPlayingVoice = NO;
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
                [self.delegate speechSynthesisDidFail:[NSString stringWithFormat:@"%@(%i)", @"语音请求失败", errorCode.errorCode]];
            }
        }

    });

}

/*!
 *  开始合成回调
 */
-(void) onSpeakBeginEx{
    _isSyntheticing = YES;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if(!self.isPlayingVoice){
            TKLogInfo(@"思迪语音合成调试:开始合成回调代理执行");
            self.isPlayingVoice=YES;
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
                [self.delegate speechSynthesisDidStart];
            }
        }

    });
}


/** 扩展事件回调

 根据事件类型返回额外的数据

 @param eventType 事件类型，具体参见IFlySpeechEventType枚举。目前只支持EVENT_TTS_BUFFER也就是实时返回合成音频。

 */
- (void)onEventEx:(int)eventType arg0Ex:(int)arg0 arg1Ex:(int)arg1 dataEx:(NSData *)eventData {
//    NSLog(@"eventData = %@", eventData);
    
    if (eventType == 21001) {
        
        // 记录数据，防止源数据被销毁形成的杂音
        char *tempBuffer = (char *)malloc(eventData.length);
        memcpy(tempBuffer, eventData.bytes, eventData.length);
        dispatch_async(_speechSynthesisOutputQueue, ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidDataBuffer:len:)]) {
                [self.delegate speechSynthesisDidDataBuffer:tempBuffer len:(int)eventData.length];
            }
            free(tempBuffer);
        });
    }
}

/** 暂停播放回调 */
- (void)onSpeakPausedEx{
    //播放中有暂停播放一次就认为tts请求片段卡顿一次
    TKLogInfo(@"思迪语音合成调试：当前出现卡顿");
    if(self.checkTTSCatonCount>0){
        self.lastUpdateTime= CFAbsoluteTimeGetCurrent()*1000;//记录毫秒时间
        self.isSpeakPaused=true;
    }
    
    if(self.checkTTSCatonTime>0){
        if(!self.checkTTSCatonTimer){
            float catonTime=self.checkTTSCatonTime/1000.00f;
            TKLogInfo(@"思迪语音合成调试：卡顿时长倒计时：%f",catonTime);
            //单次卡顿时长定时器
            self.checkTTSCatonTimer = [NSTimer timerWithTimeInterval:catonTime target:self selector:@selector(checkTTSCatonOver:) userInfo:nil repeats:NO];
            [[NSRunLoop mainRunLoop] addTimer:self.checkTTSCatonTimer forMode:NSRunLoopCommonModes];
        }
    }
}


- (void)onBufferProgressEx:(float)progress messageEx:(NSString *)msg{
    TKLogInfo(@"思迪语音合成调试：缓冲进度：%f",progress);
    
    TKLogInfo(@"思迪语音合成调试：当前出现卡顿");
    if(self.isSpeakPaused){
        self.isSpeakPaused=false;
        if(self.checkTTSCatonCount>0&&(CFAbsoluteTimeGetCurrent()*1000-self.lastUpdateTime>150)){
            self.currentTTSCatonCount++;
            TKLogInfo(@"思迪语音合成调试：卡顿次数统计：%d",self.currentTTSCatonCount);
            if(self.currentTTSCatonCount>=self.checkTTSCatonCount){
                self.isSyntheticing = NO;
                self.isPlayingVoice = NO;
                if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
                    [self.delegate speechSynthesisDidFail:@"您当前网络卡顿，请检查网络连接情况再重试。"];
                }
            }
        }
    }

    
    if(self.checkTTSCatonTime>0){
        if(self.checkTTSCatonTimer){
            TKLogInfo(@"思迪语音合成调试：卡顿时长倒计时释放");
            [self.checkTTSCatonTimer invalidate];
            self.checkTTSCatonTimer = nil;
        }
    }
}

#pragma mark QCloudTTSDelegate
//缓冲完成
- (void) onTTSPlayContinue {
    _isSyntheticing = NO;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
            [self.delegate speechSynthesisDidPlayDone];
        }
    });
}


#pragma mark lazyloading
/**
 <AUTHOR> 2020年07月31日11:02:34
 @初始化懒加载科大讯飞语音合成对象
 @return 科大讯飞语音合成对象
 */
-(AIPIFlySpeechSynthesizer *)iFlySpeechSynthesizer{
    if (!_iFlySpeechSynthesizer) {
        //获取语音合成单例
        _iFlySpeechSynthesizer = [AIPIFlySpeechSynthesizer sharedInstance];
        //设置协议委托对象
        _iFlySpeechSynthesizer.delegate = self;
        if (self.configParam[@"iflyTtsParameter"]) {
//            [_iFlySpeechSynthesizer setParameter:@"vid=65580,auf=4,aue=raw,svc=tts,type=1,uid=660Y5r,appid=57567804,url=*************:8084" forKey:[AIPIFlySpeechConstant IFlyParam]];
            [_iFlySpeechSynthesizer setParameter:self.configParam[@"iflyTtsParameter"] forKey:[AIPIFlySpeechConstant IFlyParam]];
        }
        
        if (self.configParam[@"iflytipSpeed"]) {
            NSString *tipSpeed=[NSString stringWithFormat:@"%@",self.configParam[@"iflytipSpeed"]];
            [self.iFlySpeechSynthesizer setParameterEx:[NSString stringWithFormat:@"spd=%@",tipSpeed]];
        }else{
            [self.iFlySpeechSynthesizer setParameterEx:@"spd=0"];
        }
        
        [self updateSpeedLevelByTipSpeed:[self.configParam getStringWithKey:@"tipSpeed"]];

//        if ([[self.configParam getStringWithKey:@"enableHeadphone"] isEqualToString:@"1"]) {
            [_iFlySpeechSynthesizer setParameter:@"1" forKey:@"tts_data_notify"];
//        }
    }
    return _iFlySpeechSynthesizer;
}

@end
