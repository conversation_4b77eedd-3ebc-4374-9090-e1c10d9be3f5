//
//  TKSpeechSynthesisManager.m
//  TKApp_H5SHARE
//
//  Created by <PERSON> on 2020/12/9.
//  Copyright © 2020 liubao. All rights reserved.
//

#import "TKSpeechSynthesisManager.h"



@interface TKSpeechSynthesisManager()<TKSpeechSynthesisManagerDelegate>
{
    dispatch_semaphore_t _semaphore;
    dispatch_queue_t _synthesisInAdvanceQueue;
}

@property (nonatomic, strong) id<TKSpeechSynthesisManagerProtocol> speechSynthesisManager;
@property (nonatomic, strong) AVPlayer *mp3Player;//MP3文件播放

@property (nonatomic, readwrite, assign) BOOL isSynthesisInAdvance;  // 是否正在预合成

@property (nonatomic, readwrite, strong) NSArray *synthesisArray;   // 是否正在合成多个语音
@property (nonatomic, readwrite, assign) int currentIndex;
@property (nonatomic, readwrite, copy) NSString *currentTipSpeed; // 当前语音播放速度

@end

@implementation TKSpeechSynthesisManager
@synthesize configParam;
@synthesize delegate;

#pragma mark - Init && Dealloc
// 构造方法
// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam {
    if (self = [super init]) {
        self.configParam = configParam;
    }
    
    return  self;
}

#pragma mark - Public Selector
- (void)synthesisInAdvanceWithTextArray:(NSArray *)textArray tipSpeed:(NSString *)tipSpeed {
    self.currentTipSpeed = tipSpeed;
    
    if (self.canSynthesisInAdvance == NO) {
        TKLogDebug(@"思迪语音合成调试:不支持预合成");
        return;
    }
    
    TKLogDebug(@"思迪语音合成调试:预合成开始...合成的是mp3文件");
    
    if (!_semaphore) {
        _semaphore = dispatch_semaphore_create(0);
    }
        
    if (!_synthesisInAdvanceQueue) {
        _synthesisInAdvanceQueue = dispatch_queue_create("com.thinkive.TKSpeechSynthesisManager.synthesisInAdvanceQueue", DISPATCH_QUEUE_SERIAL);
    }
    
    dispatch_async(_synthesisInAdvanceQueue, ^{
        NSString *text = nil;
        for (int i = 0; i < textArray.count; i++) {
            text = textArray[i];
            
            TKLogDebug(@"思迪语音合成调试:预合成text = %@", text);
            if ([text isKindOfClass:NSString.class] && [TKStringHelper isNotEmpty:text]) {
                
                // 检查缓存
                if ([self checkSyntheticCacheWithText:text]) {  // 缓存存在
                    
                    TKLogDebug(@"思迪语音合成调试:预合成text存在缓存，缓存路径是%@", [self checkSyntheticCacheWithText:text]);
                } else {
                    
                    TKLogDebug(@"思迪语音合成调试:预合成text不存在缓存，开始语音合成");
                    [self syntheticWithText:text autoPlay:NO tipSpeed:tipSpeed]; // 只合成不播放
                    self.isSynthesisInAdvance = YES;
                    
                    // 单步处理
                    dispatch_semaphore_wait(_semaphore, DISPATCH_TIME_FOREVER);
                }
            }
        }
        
        TKLogDebug(@"思迪语音合成调试:预合成——结束");
    });
}

- (void)syntheticAndPlay:(NSString *)text tipSpeed:(NSString *)tipSpeed {
    self.currentTipSpeed = tipSpeed;
    
    self.synthesisArray = nil;
    
    TKLogDebug(@"思迪语音合成调试:语音播放开始, text = %@", text);
    [self syntheticWithText:text autoPlay:YES tipSpeed:tipSpeed];
}

- (void)syntheticAndPlayContents:(NSArray *)texts tipSpeed:(NSString *)tipSpeed {
    self.currentTipSpeed = tipSpeed;
    
    TKLogDebug(@"思迪语音合成调试:准备播报%i段语音", (int)texts.count);
    self.currentIndex = 0;
    self.synthesisArray = texts;
    
    TKLogDebug(@"思迪语音合成调试:语音播放开始, text = %@", texts[self.currentIndex]);
    [self syntheticWithText:texts[self.currentIndex] autoPlay:YES tipSpeed:tipSpeed];
}

- (void)syntheticWithText:(NSString *)text autoPlay:(BOOL)autoPlay tipSpeed:(NSString *)tipSpeed {
    
    self.currentTipSpeed = tipSpeed;
    
    NSString *fileName = [self getMp3FileNameWithText:text];
    NSString *bundleFullPath = [self getBunldePathWithFileName:fileName];
    NSString *documentFilePath = [self getDocumentPathWithFileName:fileName];
    
    bundleFullPath = documentFilePath = nil;
    
    // 检查本地是否已有该文件
    if ([TKFileHelper isFileExists:documentFilePath]) { // bundle中有，直接读取bundle中的
        
        if (autoPlay) {
            TKLogDebug(@"思迪语音合成调试:document中存在缓存，播放document中mp3文件");
            [self handleAudioPlay:documentFilePath];
        }
    } else if ([TKFileHelper isFileExists:bundleFullPath]) {  // 沙盒中有，直接读取沙盒的
        
        if (autoPlay) {
            TKLogDebug(@"思迪语音合成调试:bundle中存在缓存，播放bundle中mp3文件");
            [self handleAudioPlay:bundleFullPath];
        }
        
        // 复制文件到沙盒中
        [TKFileHelper copyFile:bundleFullPath to:documentFilePath];
    } else { // 若没有，则语音合成
        
        if (self.isSynthesisInAdvance == YES) {
            
            TKLogDebug(@"思迪语音合成调试:预合成提前结束，开始实时合成和播放，播放的pcm格式文件");
            // 在语音合成前，需要先暂停预合成的内容
            [self.speechSynthesisManager stopSynthetic];
            self.isSynthesisInAdvance = NO;
        }
        
        // 语音合成播放
        if (autoPlay) TKLogDebug(@"思迪语音合成调试:实时语音合成播放开始");
        [self.speechSynthesisManager syntheticWithText:text autoPlay:autoPlay tipSpeed:tipSpeed];
    }
}

- (void)stop {
    if (_speechSynthesisManager) [self.speechSynthesisManager stop];    // 关闭语音合成工具类，停止合成和播放
    if (_mp3Player) [self handleAudioStopPlay]; // 关闭本地播放器
}

- (void)stopSynthetic {
    self.synthesisArray = nil;
    self.currentIndex = 0;
    
    if (_speechSynthesisManager) [self.speechSynthesisManager stopSynthetic];
}

- (void)stopPlay {
    self.synthesisArray = nil;
    self.currentIndex = 0;
    
    if (_speechSynthesisManager) [self.speechSynthesisManager stopPlay]; // 停止语音合成工具类播放
    if (_mp3Player) [self handleAudioStopPlay]; // 关闭本地播放器
}

- (BOOL)isSyntheticing {
    if (!_speechSynthesisManager) return NO;
    return self.speechSynthesisManager.isSyntheticing;
}

- (BOOL)isPlaying {
    
    if (_mp3Player && self.mp3Player.rate > 0) return YES;
    if (_speechSynthesisManager && self.speechSynthesisManager.isPlaying) return YES;

    return  NO;
}

- (void)cancel {
    [self.speechSynthesisManager cancel];
}


- (BOOL)canSynthesisInAdvance { 
    return [self.speechSynthesisManager canSynthesisInAdvance];
}


#pragma mark - private Selector
- (NSString *)checkSyntheticCacheWithText:(NSString *)text
{
    NSString *fileName = [self getMp3FileNameWithText:text];
    NSString *bundleFullPath = [self getBunldePathWithFileName:fileName];
    NSString *documentFilePath = [self getDocumentPathWithFileName:fileName];
    
    if ([TKFileHelper isFileExists:documentFilePath]) {
        return documentFilePath;
    } else if ([TKFileHelper isFileExists:bundleFullPath]) {
        return bundleFullPath;
    } else {
        return nil;
    }
}

- (void)handleAudioPlay:(NSString *)filePath
{
    // 存在缓存的情况,通知外层开始
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
            [self.delegate speechSynthesisDidStart];
        }
    });
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        NSURL *aUrl = [NSURL fileURLWithPath:filePath];
        AVPlayerItem * songItem = [[AVPlayerItem alloc]initWithURL:aUrl];
        self.mp3Player = [[AVPlayer alloc]initWithPlayerItem:songItem];
        
        [self.mp3Player play];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(moviePlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.mp3Player.currentItem];
    });
}

//音频循环播放
- (void)moviePlayDidEnd:(NSNotification*)notification {
    [self speechSynthesisDidPlayDone];
}

//停止播放音乐
- (void)handleAudioStopPlay {

    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:nil];
    [self.mp3Player pause];
}

- (NSString *)getMp3FileNameWithText:(NSString *)text {
    
    if ([TKStringHelper isEmpty:text]) return @"";
        
    NSString *text_md5 = [TKMd5Helper md5Encrypt:text];
    NSString *fileName = [NSString stringWithFormat:@"%@.mp3", text_md5];
    
    return fileName;
}

- (NSString *)getBunldePathWithFileName:(NSString *)fileName {
    
    if ([TKStringHelper isEmpty:fileName]) return @"";
        
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    NSString* fPath = [bundle pathForResource:fileName ofType:@"" inDirectory:@"Resources/TKOpenPlugin60026"];
    
    return fPath;
}

- (NSString *)getDocumentPathWithFileName:(NSString *)fileName {
    
    if ([TKStringHelper isEmpty:fileName]) return @"";
    
    return [[TKFileHelper documentFolder] stringByAppendingFormat:@"/thinkive/speechSynthesis/%@",fileName];
}

#pragma mark - TKSpeechSynthesisManagerDelegate
// 语音合成开始回调
- (void)speechSynthesisDidStart {
    
    if (self.isSynthesisInAdvance) {
        TKLogDebug(@"思迪语音合成调试:预合成开始，语音合成工具开始合成...");
        // 预合成直接结束
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.synthesisArray.count) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStartWithIndex:synthesisArray:)]) {
                [self.delegate speechSynthesisDidStartWithIndex:self.currentIndex synthesisArray:self.synthesisArray];
            }
        } else {
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
                [self.delegate speechSynthesisDidStart];
            }
        }
    });
}

// 语音合成结束回调
- (void)speechSynthesisDidComplete:(NSString *)text speechSynthesisFilePath:(NSString *)speechSynthesisFilePath {
    
    if (self.isSynthesisInAdvance) {
        TKLogDebug(@"思迪语音合成调试:预合成语音结束，text = %@，文件路径是%@, 即将移动到%@", text, speechSynthesisFilePath, [self getDocumentPathWithFileName:[self getMp3FileNameWithText:text]]);
        
        [TKFileHelper moveFile:speechSynthesisFilePath to:[self getDocumentPathWithFileName:[self getMp3FileNameWithText:text]]];
        self.isSynthesisInAdvance = NO;
        dispatch_semaphore_signal(_semaphore);
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.synthesisArray.count) {
            // 没有场景测试，暂不回调
//            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidComplete:speechSynthesisFilePath:)]) {
//                [self.delegate speechSynthesisDidComplete:text speechSynthesisFilePath:speechSynthesisFilePath];
//            }
        } else {
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidComplete:speechSynthesisFilePath:)]) {
                [self.delegate speechSynthesisDidComplete:text speechSynthesisFilePath:speechSynthesisFilePath];
            }
        }
    });
}

// 语音合成结束回调
- (void)speechSynthesisDidFail:(NSString *)errorMsg {
    
    if (self.isSynthesisInAdvance) {
        TKLogDebug(@"思迪语音合成调试:预合成语音结束，合成出错");
        self.isSynthesisInAdvance = NO;
        dispatch_semaphore_signal(_semaphore);
        return;
    }
    
//    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
            [self.delegate speechSynthesisDidFail:errorMsg];
        }
//    });
}

// 语音合成播放结束回调
- (void)speechSynthesisDidPlayDone {

    if (self.synthesisArray.count) {
        
        int index = self.currentIndex;  // 先记录
        // 回调上层
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDoneWithIndex:synthesisArray:)]) {
                [self.delegate speechSynthesisDidPlayDoneWithIndex:index synthesisArray:self.synthesisArray];
            }
        });
        
        if (self.currentIndex < self.synthesisArray.count - 1) {
            TKLogDebug(@"思迪语音合成调试:多段语音-继续播放下段语音");
            // 播放下一段语音
            self.currentIndex++;
            [self syntheticWithText:self.synthesisArray[self.currentIndex] autoPlay:YES tipSpeed:self.currentTipSpeed];
        } else {
            TKLogDebug(@"思迪语音合成调试:多段语音播放结束");
        }
        
    } else {
        TKLogDebug(@"思迪语音合成调试:播放结束");
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
                [self.delegate speechSynthesisDidPlayDone];
            }
        });
    }
}

- (void)speechSynthesisDidDataBuffer:(char *)buffer len:(int)len {
//    dispatch_async(dispatch_get_main_queue(), ^{
    // 大数据流传输，不在主线程中操作
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidDataBuffer:len:)]) {
            [self.delegate speechSynthesisDidDataBuffer:buffer len:len];
        }
//    });
}

#pragma mark - Setter && Getter
- (id<TKSpeechSynthesisManagerProtocol>)speechSynthesisManager {
    if (!_speechSynthesisManager) {
        
        NSString *className = nil;
        
        // 获取真实的工具类
        className = self.speechSynthesManagerClassNameDic[@(speechRecognizeToolType)];
        Class speechRecognizeManagerClazz = NSClassFromString(className);
        
        NSAssert(speechRecognizeManagerClazz != nil, @"语音合成实现工具类不存在");
        
        // 初始化并设置代理
        _speechSynthesisManager = [[speechRecognizeManagerClazz alloc] initWithConfig:self.configParam];
        _speechSynthesisManager.delegate = self;
    }
    
    return (id<TKSpeechSynthesisManagerProtocol>)_speechSynthesisManager;
}

- (NSDictionary *)speechSynthesManagerClassNameDic {
    return @{
        @(1) : @"TKALSpeechSynthesisManager",  // 阿里
        @(2) : @"TKTencentSpeechSynthesisManager", // 腾讯
        @(3) : @"TKiflySpeechSynthesisManager", // 科大讯飞
        @(4) : @"TKTHSSpeechSynthesisManager", // 同花顺
    };
}

@end
