//
//  TKNLSPlayAudio.mm
//  NuiDemo
//
//  Created by zhouguangdong on 2019/12/4.
//  Copyright © 2019 Alibaba idst. All rights reserved.
//

//#define DEBUG_MODE

#import "TKNLSPlayAudio.h"
#import "pthread.h"
#import "TKringBuf.h"

static UInt32 gBufferSizeBytes=2048;//It must be pow(2,x)


RINGBUFFER_NEW(tk_ring_buf, 16000 * 1000)

@interface TKNLSPlayAudio() {
    int state;
    dispatch_queue_t _writeBufferQueue;
    char *_emptyBuffer;  // 大小为gBufferSizeBytes，数据为0的空数据包的首地址
    dispatch_queue_t _bufferTimeOutQueue;
}

@property (atomic, readwrite, assign) long needToWriteLength;
@property (atomic, readwrite, assign) long needToReadLength;
@property (nonatomic, readwrite, strong) NSMutableDictionary *bufferWaitTimeDic;   // buffer空转等待时间，单位毫秒。数量和顺序与NUM_BUFFERS一致
@property (nonatomic, readwrite, assign) NSTimeInterval totalTimeOut;   // buffer空等待总超时时长

@property (nonatomic, strong) NSTimer *startPlayerTimer;    // 一开始没有数据的时候不启动播放器，不断轮询直到有数据才启动播放器
@property (nonatomic, strong) NSTimer *bufferCallBackTimeOutTimer;    // audioQueue可能由于category修改不再回调

@end

@implementation TKNLSPlayAudio

- (void)dealloc {
    
    [self releaseEmptyBuffer];
    [self releaseStartPlayerTimer];
    [self releaseBufferCallBackTimeOutTimer];
}

- (id) init {
    self = [super init];
    [self cleanup];
    
    self.mSampleRate = 16000;
    
    // 创建多线程队列
    NSString *queueName = [NSString stringWithFormat:@"com.thinkive.%@_writeBufferQueue", self.class];
    const char *queueName_char = [queueName UTF8String];
    _writeBufferQueue = dispatch_queue_create(queueName_char, DISPATCH_QUEUE_SERIAL);
    
    queueName = [NSString stringWithFormat:@"com.thinkive.%@_bufferTimeOutQueue", self.class];
    queueName_char = [queueName UTF8String];
    _bufferTimeOutQueue = dispatch_queue_create(queueName_char, DISPATCH_QUEUE_SERIAL);
    
    // 设置参数
    self.timeOut = 10.0;
//    self.openFill = YES;
    
    ///设置音频参数
    [self createAudioDescription];
    // 创建queue和buffer
    [self createAudioQueueAndBuffer];
    
    return self;
}

- (void)createAudioDescription
{
    ///设置音频参数
    _audioDescription.mSampleRate  = self.mSampleRate;//采样率
    _audioDescription.mFormatID    = kAudioFormatLinearPCM;
//    _audioDescription.mFormatFlags =  kAudioFormatFlagIsSignedInteger|kAudioFormatFlagIsNonInterleaved;
    _audioDescription.mFormatFlags =  kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
    _audioDescription.mChannelsPerFrame = 1;
    _audioDescription.mFramesPerPacket  = 1;//每一个packet一侦数据
    _audioDescription.mBitsPerChannel   = 16;//av_get_bytes_per_sample(AV_SAMPLE_FMT_S16)*8;//每个采样点16bit量化
    _audioDescription.mBytesPerPacket   = 2;
    _audioDescription.mBytesPerFrame    = 2;
    _audioDescription.mReserved = 0;
}

- (void)createAudioQueueAndBuffer
{
    //使用player的内部线程播 创建AudioQueue
    AudioQueueNewOutput(&_audioDescription, bufferCallback, (__bridge void *)(self), nil, nil, 0, &audioQueue);
    if(audioQueue)
    {
        Float32 gain=1.0;
        //设置音量
        AudioQueueSetParameter(audioQueue, kAudioQueueParam_Volume, gain);
        ////添加buffer区 创建Buffer
        for(int i=0;i < NUM_BUFFERS; i++) {
            int result = AudioQueueAllocateBuffer(audioQueue, gBufferSizeBytes, &audioQueueBuffers[i]);
//            AudioQueueEnqueueBuffer(audioQueue, audioQueueBuffers[i], 0, NULL);
            TKLogDebug(@"audioplayer: AudioQueueAllocateBuffer i = %d,result = %d",i,result);
            
            // 初始化空转时间
            self.bufferWaitTimeDic[@(i)] = @(0);
        }
    }
}

- (void)primePlayQueueBuffers
{
    for (int t = 0; t < NUM_BUFFERS; ++t)
    {
        TKLogDebug(@"audioplayer: buffer %d available size %d", t, audioQueueBuffers[t]->mAudioDataBytesCapacity);
        bufferCallback((__bridge void *)(self), audioQueue, audioQueueBuffers[t]);
    }
    AudioQueuePrime(audioQueue, 0, NULL);
}

- (void)play {
    
    [self stop];
    
    TKLogDebug(@"audioplayer: Audio Play Start >>>>> ");
    
    state = playing;
    
    int delayTime = 20;
    if (_startPlayerTimer == nil)
    {
        TKLogDebug(@"audioplayer: Audio Create Timer >>>>> ");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            _startPlayerTimer= [NSTimer timerWithTimeInterval:delayTime * 1.0f / 1000
                                                   target:self
                                                 selector:@selector(checkAndStartPlayer)
                                                 userInfo:nil
                                                  repeats:YES];
            [[NSRunLoop currentRunLoop] addTimer:_startPlayerTimer forMode:NSRunLoopCommonModes];
        });
    }
}

- (void)checkAndStartPlayer
{
    TKLogDebug(@"audioplayer: Check And Start Player >>>>> ");
    
    if (self.needToReadLength <= 0) {
    
        TKLogDebug(@"没有数据需要播报, 等待下一次检测");
        return;
    }
    
    // 开始播放
    if (audioQueue) {
        [self primePlayQueueBuffers];
        OSStatus status = AudioQueueStart(audioQueue, NULL);
        if (status != 0) {
            AudioQueueFlush(audioQueue);
            status = AudioQueueStart(audioQueue, NULL);
        }
        if (status != 0) {
            [self stop];
            
            TKLogDebug(@"audioplayer: 启动queue失败 %d", (int)status);
            if([self->_delegate respondsToSelector:@selector(playerDidFail:)]){
               dispatch_async(dispatch_get_main_queue(), ^{
                   [self->_delegate playerDidFail:[NSString stringWithFormat:@"语音播放失败(启动播放队列失败(%d))", (int)status]];
               });
            }
        }
    } else {
        [self stop];
        
        TKLogDebug(@"audioplayer: Audio Play audioQueue is null! >>>>> ");
        if([self->_delegate respondsToSelector:@selector(playerDidFail:)]){
           dispatch_async(dispatch_get_main_queue(), ^{
               [self->_delegate playerDidFail:@"语音播放失败(创建播放队列失败)"];
           });
        }
    }
    
    // 销毁定时器
    [self releaseStartPlayerTimer];
}

- (void)pause {
    state = paused;
    if (audioQueue) {
        AudioQueuePause(audioQueue);
    }
}

- (void)resume {
    state = playing;
    if (audioQueue) {
        AudioQueueStart(audioQueue, NULL);
    }
}


- (void) setstate:(PlayerState)pstate{
    state = pstate;
}

- (void)write:(const char*)buffer Length:(int)len {
    
    if (self->state == idle) {
        TKLogDebug(@"prepare to write, but state is idle , break");
        return;
    }
    
    self->_needToWriteLength += len;
    self->_needToReadLength += len;
    TKLogDebug(@"prepare to write, 此次待写入数据长度为%ld， 待写入总长度为 %ld", len, _needToWriteLength);
        
    TKLogDebug(@"start to write");
    
    int wait_time_ms = 0;
    
    while (1) {
        if(wait_time_ms > 3000) {
            TKLogDebug(@"wait for 3s, player must not consuming pcm data. overrun...");
            if([self->_delegate respondsToSelector:@selector(playerDidFail:)]){
               dispatch_async(dispatch_get_main_queue(), ^{
                   [self->_delegate playerDidFail:[NSString stringWithFormat:@"语音播放失败(写入数据超时)"]];
               });
            }
            
            break;
        }
        TKLogDebug(@"ringbuf want write data %d",  len);
        int ret = tk_ringbuffer_write(&tk_ring_buf, (unsigned char*)buffer, (unsigned int)len);
        TKLogDebug(@"ringbuf write data %d",  ret);
        if (self->state != playing) {
            
//                TKLogDebug(@"self->state = %i && _needToWriteLength = %d, self->state == draining = %i, _needToReadLength > 0 = %i",  self->state, _needToReadLength, self->state == draining, _needToReadLength > 0);
            if (self->state == draining && _needToReadLength > 0) {
                TKLogDebug(@"已收到合成结束通知，数据未播报完毕，继续写入");
            } else {
                TKLogDebug(@"不是播放状态，停止写入");
                break;
            }
        }
        _needToWriteLength -= ret;
        TKLogDebug(@"本次写入数据 %ld, 待写入总长度为 %ld, 已有缓存数据长度 %ld", (long)ret, _needToWriteLength, (long)tk_ringbuffer_get_filled(&tk_ring_buf));
        
        if (ret <= 0) {
            int delayTime = 30;
            usleep(delayTime * 1000);
            wait_time_ms += delayTime;
            TKLogDebug(@"数据写入失败，等待0.03s");
            continue;
        } else {
            
            break;
        }
    }
}

- (void)stop {
    TKLogDebug(@"audioplayer: Audio Player Stop");
    state = idle;
    _needToWriteLength = 0;
    _needToReadLength = 0;
    _totalTimeOut = 0.0;
    _currentPlayIndex = 0;
    tk_ringbuffer_reset(&tk_ring_buf);
    
    AudioQueuePropertyID propertyID = kAudioQueueProperty_IsRunning;
    UInt32 isRunning = 0;
    UInt32 propertySize = sizeof(UInt32);

    if (audioQueue) {
        // 获取音频队列的属性值
        OSStatus status = AudioQueueGetProperty(audioQueue, propertyID, &isRunning, &propertySize);
        if (status == noErr) {
            // 判断是否正在运行
            if (isRunning == 1) {
//                NSLog(@"AudioQueueStart 已经调用");
                TKLogDebug(@"audioplayer: Flush reset");
                //AudioQueueReset(audioQueue);
                AudioQueueStop(audioQueue, TRUE);
                AudioQueueFlush(audioQueue);
                TKLogDebug(@"audioplayer: Flush reset done");
                
            } else {
//                NSLog(@"AudioQueueStart 未调用");
            }
        } else {
//            NSLog(@"获取音频队列属性失败");
        }
        
    } else {
//        NSLog(@"获取音频队列失败");
    }
    
    // 销毁定时器
    [self releaseStartPlayerTimer];
    [self releaseBufferCallBackTimeOutTimer];
}

-(void)drain {
    state = draining;
}

- (void)cleanup {
    tk_ringbuffer_reset(&tk_ring_buf);
    state = idle;
    _currentPlayIndex = 0;
    if(audioQueue)
    {
        TKLogDebug(@"audioplayer: Release AudioQueueNewOutput");
        
        AudioQueueFlush(audioQueue);
        AudioQueueReset(audioQueue);
        AudioQueueStop(audioQueue, TRUE);
        for(int i=0; i < NUM_BUFFERS; i++)
        {
            AudioQueueFreeBuffer(audioQueue, audioQueueBuffers[i]);
            audioQueueBuffers[i] = nil;
        }
        AudioQueueDispose(audioQueue, true);
        audioQueue = nil;
    }
}

//回调函数(Callback)的实现
static void bufferCallback(void *inUserData,AudioQueueRef inAQ,AudioQueueBufferRef buffer) {
    
    TKNLSPlayAudio* player = (__bridge TKNLSPlayAudio *)inUserData;
    
    int i = [player getBufferIndex:buffer];
//    TKLogDebug(@"buffer %i, bufferCallback", i);
//    NSLog(@"buffer %i, bufferCallback", i);
    
    int ret = [player GetAudioData:buffer];
    
    if (ret > 0) {
        OSStatus status = AudioQueueEnqueueBuffer(inAQ, buffer, 0, NULL);
        TKLogDebug(@"audioplayer: playCallback status %d", status);
        
        if (status == 0) {
            [player createBufferCallBackTimeOutTimer];
        
        } else {
            [player stop];
            if([player->_delegate respondsToSelector:@selector(playerDidFail:)]){
               dispatch_async(dispatch_get_main_queue(), ^{
                   [player->_delegate playerDidFail:[NSString stringWithFormat:@"语音播放失败(写入缓存失败(%i))", status]];
               });
            }
        }
    } else {
        
        if (player->state == draining && player.needToReadLength <= 0) {
            TKLogDebug(@"audioplayer: no more data");
            //drain data finish, stop player.
            [player stop];
            if([player->_delegate respondsToSelector:@selector(playerDidFinish)]){
               dispatch_async(dispatch_get_main_queue(), ^{
                   [player->_delegate playerDidFinish];
               });
            }
        }
    }
}


- (int)GetAudioData:(AudioQueueBufferRef)buffer {
    int i = [self getBufferIndex:buffer];
    TKLogDebug(@"buffer %i 准备获取数据", i);
    
    if (buffer == NULL || buffer->mAudioData == NULL) {
         TKLogDebug(@"no more data to play");
        return 0;
    }
    
    int wait_time_ms = 0;
    
    while (1) {
        
        // 状态检查
//      TKLogDebug(@"self->state = %i && _needToReadLength = %d",  self->state, _needToReadLength);
        if (state != playing) {
            if (self->state == draining && _needToReadLength > 0) {
                TKLogDebug(@"已收到合成结束通知，数据未播报完毕，继续播报");
                    
            } else {
                // 重置已等待时间
                _bufferWaitTimeDic[@(i)] = @(0);
                
                TKLogDebug(@"不是播放状态，停止播放");
                break;
            }
        }
        
        int ret = tk_ringbuffer_read(&tk_ring_buf, (unsigned char*)buffer->mAudioData, buffer->mAudioDataBytesCapacity);
        
        if (0 < ret) {
            int currentPlayIndex = self.currentPlayIndex;
            int willPlayIndex = self.currentPlayIndex + ret;
//            NSLog(@"111当前进度index = %i, 即将播放的index = %i", currentPlayIndex, willPlayIndex);
            if([self->_delegate respondsToSelector:@selector(playerDidFail:)]){
               dispatch_async(dispatch_get_main_queue(), ^{
                   [self->_delegate playerHavePlayed:currentPlayIndex willPlayIndex:willPlayIndex];
               });
            }
            self.currentPlayIndex += ret;
            
            // 重置已等待时间
            _bufferWaitTimeDic[@(i)] = @(0);
            
            TKLogDebug(@"读取数据长度 %d, 剩余未播放数据长度 %d",  ret, tk_ringbuffer_get_filled(&tk_ring_buf));
            buffer->mAudioDataByteSize = ret;
            
            // 计算未读数据
            _needToReadLength -= ret;
            
            return ret;
        } else {
            
            int delayTime = 30;
            usleep(delayTime * 1000);
            TKLogDebug(@"wait for 0.03s");
            
            // 记录单次超时时长
            if(wait_time_ms > 3000) {
                wait_time_ms += delayTime;
                TKLogDebug(@"wait for 3s, player must not consuming pcm data. overrun...");
                
                if([self->_delegate respondsToSelector:@selector(playerDidFail:)]){
                   dispatch_async(dispatch_get_main_queue(), ^{
                       [self->_delegate playerDidFail:[NSString stringWithFormat:@"语音播放失败(播报音频超时)"]];
                   });
                }
                
                break;
            }
            
            // 记录超时时长
//            self.totalTimeOut += delayTime;
//            if (self.totalTimeOut >= (self.timeOut * 1000)) {
//
//                if([self->_delegate respondsToSelector:@selector(playerDidTimeOut)]){
//                   dispatch_async(dispatch_get_main_queue(), ^{
//                       [self->_delegate playerDidTimeOut];
//                   });
//                }
//
//                [self stop];
//
//                return 0;
//            }
            
            int waitTime = [_bufferWaitTimeDic[@(i)] intValue];
            waitTime += delayTime;
            _bufferWaitTimeDic[@(i)] = @(waitTime);
//            TKLogDebug(@"buffer %i已等待 %i 毫秒",  i, [_bufferWaitTimeDic[@(i)] intValue]);

//            int ret = tk_ringbuffer_read(&tk_ring_buf, (unsigned char*)buffer->mAudioData, buffer->mAudioDataBytesCapacity);

            // 300ms没有数据，buffer会回收，后续再写入数据不会再播放声音。因此累积280ms（预留空间）没有新数据进来，写入空数据包。
            if (waitTime >= 60) {
                int byte = [self fillEmptyBuffer:buffer];
                if (byte > 0) {
                    // 重置已等待时间
                    _bufferWaitTimeDic[@(i)] = @(0);
                    
                    return byte;
                }
            }
        
        continue;
        }
    }
    return 0;
}

- (int)getBufferIndex:(AudioQueueBufferRef)buffer
{
    int i = -1;
    
    for (int j = 0; j < NUM_BUFFERS; j++) {
        if (buffer == self->audioQueueBuffers[j] ) {
            i = j;
            break;
        }
    }
    
    return i;
}

- (int)fillEmptyBuffer:(AudioQueueBufferRef)buffer
{
//    UInt32 allDataSize = buffer->mAudioDataBytesCapacity;
    UInt32 allDataSize = gBufferSizeBytes;
    if (_emptyBuffer == NULL) {

        _emptyBuffer = (char *)malloc(allDataSize);
    }

    if (_emptyBuffer != NULL) {
        memset(_emptyBuffer, 0, allDataSize);


        if (buffer != NULL) {
            memcpy((unsigned char*)buffer->mAudioData, _emptyBuffer, allDataSize);
            buffer->mAudioDataByteSize = allDataSize;

//            NSLog(@"填充静音包 %d", allDataSize);

            return allDataSize;
        }
    }

//    NSLog(@"填充静音包失败");
    return 0;
}

- (void)releaseEmptyBuffer
{
    if (_emptyBuffer != NULL) {
        free(_emptyBuffer);
    }
}

- (void)releaseStartPlayerTimer
{
    if (_startPlayerTimer && [_startPlayerTimer isValid])
    {
      [_startPlayerTimer invalidate];
        _startPlayerTimer = nil;
    }
}

- (void)createBufferCallBackTimeOutTimer
{
    [self releaseBufferCallBackTimeOutTimer];
        
    TKLogDebug(@"createBufferCallBackTimeOutTimer");
    _bufferCallBackTimeOutTimer = [NSTimer timerWithTimeInterval:3.0
                                           target:self
                                         selector:@selector(bufferCallBackTimeOut)
                                         userInfo:nil
                                          repeats:NO];
    [[NSRunLoop currentRunLoop] addTimer:_bufferCallBackTimeOutTimer forMode:NSRunLoopCommonModes];
}

- (void)bufferCallBackTimeOut
{
    TKLogDebug(@"bufferCallBackTimeOut");
    
    __weak typeof (self) weakSelf = self;
    dispatch_sync(_bufferTimeOutQueue, ^{
        if (weakSelf.bufferCallBackTimeOutTimer == nil) {
            TKLogDebug(@"bufferCallBackTimeOut 已经回调");
            return;
        }
        
        [weakSelf releaseBufferCallBackTimeOutTimer];
        
        if(weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(playerDidFail:)]){
           dispatch_async(dispatch_get_main_queue(), ^{
               [weakSelf.delegate playerDidFail:[NSString stringWithFormat:@"语音播放失败(audioQueue回调超时)"]];
           });
        }
    });
}

- (void)releaseBufferCallBackTimeOutTimer
{
    if (_bufferCallBackTimeOutTimer && [_bufferCallBackTimeOutTimer isValid])
    {
        TKLogDebug(@"releaseBufferCallBackTimeOutTimer");
        [_bufferCallBackTimeOutTimer invalidate];
        _bufferCallBackTimeOutTimer = nil;
    }
}

- (int)getBufferFilledSize {
    return tk_ringbuffer_get_filled(&tk_ring_buf);
}

#pragma mark - Setter && Getter
- (NSMutableDictionary *)bufferWaitTimeDic {
    if (_bufferWaitTimeDic == nil) {
        _bufferWaitTimeDic = [NSMutableDictionary dictionary];
    }
    
    return _bufferWaitTimeDic;
}

- (void)setMSampleRate:(int)mSampleRate {
    if (_mSampleRate != mSampleRate) {
        _mSampleRate = mSampleRate;
        
        [self cleanup];
        ///设置音频参数
        [self createAudioDescription];
        // 创建queue和buffer
        [self createAudioQueueAndBuffer];
    }
}

- (void)setAudioDescription:(AudioStreamBasicDescription)audioDescription {
    if (!AreAudioStreamBasicDescriptionsEqual(&_audioDescription, &audioDescription)) {
        _audioDescription = audioDescription;
        
        [self cleanup];
        // 创建queue和buffer
        [self createAudioQueueAndBuffer];
    }
}

BOOL AreAudioStreamBasicDescriptionsEqual(const AudioStreamBasicDescription *desc1, const AudioStreamBasicDescription *desc2) {
    if (desc1->mFormatID != desc2->mFormatID) {
        return NO;
    }
    
    if (desc1->mSampleRate != desc2->mSampleRate) {
        return NO;
    }
    
    if (desc1->mFormatFlags != desc2->mFormatFlags) {
        return NO;
    }
    
    if (desc1->mBytesPerPacket != desc2->mBytesPerPacket) {
        return NO;
    }
    
    if (desc1->mFramesPerPacket != desc2->mFramesPerPacket) {
        return NO;
    }
    
    if (desc1->mBytesPerFrame != desc2->mBytesPerFrame) {
        return NO;
    }
    
    if (desc1->mChannelsPerFrame != desc2->mChannelsPerFrame) {
        return NO;
    }
    
    if (desc1->mBitsPerChannel != desc2->mBitsPerChannel) {
        return NO;
    }
    
    return YES;
}

@end
