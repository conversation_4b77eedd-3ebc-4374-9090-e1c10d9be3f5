//
//  TKiflySpeechRecognizeManager.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/4/6.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKiflySpeechRecognizeManager.h"

// 讯飞语音识别
#import <AIPIFlyMSC/AIPIFlyMSC.h>
#import <AIPIFlyMSC/AIPIFlySpeechRecognizer.h>
#import <AIPIFlyMSC/AIPIFlySpeechError.h>
#import <AIPIFlyMSC/AIPIFlySpeechConstant.h>
#import <AIPIFlyMSC/AIPIFlySpeechUtility.h>


@interface TKiflySpeechRecognizeManager()<AIPIFlySpeechRecognizerDelegate>

@property (nonatomic, strong) AIPIFlySpeechRecognizer *iFlySpeechRecognizer;//科大讯飞不带界面的识别对象

@end

@implementation TKiflySpeechRecognizeManager
{
    BOOL _isRecognizing;
}
@synthesize configParam;
@synthesize delegate;


#pragma mark - Init && Dealloc
- (void)dealloc {
//    NSLog(@"TKiflySpeechRecognizeManager dealloc");
    
    if ([AIPIFlySpeechUtility getUtility] != nil) {
        [AIPIFlySpeechUtility destroy];
    }
}


// 构造方法
// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam {
    if (self = [self init]) {
        self.configParam = configParam;
        
        if ([AIPIFlySpeechUtility getUtility] == nil) {
            NSString *cfgPath = [[NSBundle mainBundle] pathForResource:@"mt_scylla" ofType:@"cfg"];
            NSString *initString = nil;
            //开启识别底层日志
            initString = [[NSString alloc] initWithFormat:@"cfg_path=%@,log_path=%@/Documents/mt_scylla.log",cfgPath, NSHomeDirectory()];
    //        //开启msc日志
    //        [AIPIFlySpeechUtility setPrintFlag:YES];
    //        [AIPIFlySpeechUtility setSaveFlag:YES path:[NSString stringWithFormat:@"%@/Documents/MSCLog/", NSHomeDirectory()]];
            //所有服务启动前，需要确保执行createUtility
            [AIPIFlySpeechUtility createUtility:initString];
        }
    }
    
    return  self;
}

#pragma mark - Selector
// 开始识别
- (void)start{
    [self.iFlySpeechRecognizer startListening];
}

// 停止识别
- (void)stop{
    [self.iFlySpeechRecognizer stopListening];
}

// 取消识别
- (void)cancel{
    [self.iFlySpeechRecognizer cancel];
    _iFlySpeechRecognizer = nil;
}

- (BOOL)isRecognizing {
    return self.iFlySpeechRecognizer.isListening;
}

/*!
 *  写入音频流
 */
- (BOOL)writeAudio:(NSData *)audioData {
    return [self.iFlySpeechRecognizer writeAudio:audioData];
}

#pragma mark  AIPIFlySpeechRecognizerDelegate
/*!
 *  识别结果回调
 *
 *  在进行语音识别过程中的任何时刻都有可能回调此函数，你可以根据errorCode进行相应的处理，当errorCode没有错误时，表示此次会话正常结束；否则，表示此次会话有错误发生。特别的当调用`cancel`函数时，引擎不会自动结束，需要等到回调此函数，才表示此次会话结束。在没有回调此函数之前如果重新调用了`startListenging`函数则会报错误。
 *
 *  @param errorCode 错误描述
 */
- (void)onErrorEx:(AIPIFlySpeechError *)errorCode{
    dispatch_async(dispatch_get_main_queue(), ^{
        if (errorCode.errorCode!=0) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidFail:)]) {
                [self.delegate speechRecognizeDidFail:[NSString stringWithFormat:@"%@(%i)", @"语音识别失败", errorCode.errorCode]];
            }
        }
    });
}

- (void)onIFlyResultsEx:(NSString *)results {
    
}


//识别结果返回代理
- (void)onIFlyResultsEx:(NSString *)results isLast:(BOOL)isLast{
    
    dispatch_async(dispatch_get_main_queue(), ^{
  
        NSError *error;
        NSData* data=[results dataUsingEncoding:NSUTF8StringEncoding];
        NSDictionary * dataDic = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableLeaves error:&error];
        
        NSString * identifyString;
        if([[dataDic objectForKey:@"result"] length] != 0){
            identifyString = [dataDic objectForKey:@"result"];
        }

        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeOnSliceRecognize:)]) {
            [self.delegate speechRecognizeOnSliceRecognize:identifyString];
        }
    });
}


/*!
 *  开始录音回调
 *   当调用了`startListening`函数之后，如果没有发生错误则会回调此函数。
 *  如果发生错误则回调onErrorEx:函数
 */
- (void)onBeginOfSpeechEx{
    dispatch_async(dispatch_get_main_queue(), ^{

        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidStart)]) {
            [self.delegate speechRecognizeDidStart];
        }
    });
}

#pragma mark lazyloading
/**
 <AUTHOR> 2020年07月31日11:02:39
 @初始化懒加载科大讯飞不带界面的识别对象
 @return 科大讯飞不带界面的识别对象
 */
-(AIPIFlySpeechRecognizer *)iFlySpeechRecognizer{
    if (!_iFlySpeechRecognizer) {
        //创建语音识别对象
        _iFlySpeechRecognizer = [AIPIFlySpeechRecognizer sharedInstance];
        _iFlySpeechRecognizer.delegate = self;
        //设置识别参数
        if (self.configParam[@"iflyAsrParameter"]) {
//            [_iFlySpeechRecognizer setParameter:@"extend_params={\"params\":\"eos=3000,bos=3000\"},appid=pc20onli,url=*************:8084,time_out=3,svc=iat,auf=audio/L16;rate=16000,aue=raw,type=1,uid=660Y5r,mi=80" forKey:[AIPIFlySpeechConstant IFlyParam]];
            [_iFlySpeechRecognizer setParameter:self.configParam[@"iflyAsrParameter"] forKey:[AIPIFlySpeechConstant IFlyParam]];
        }
    }
    return _iFlySpeechRecognizer;
}
@end
