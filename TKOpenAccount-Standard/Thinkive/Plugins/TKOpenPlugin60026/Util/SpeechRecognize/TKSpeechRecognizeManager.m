//
//  TKSpeechRecognizeManager.m
//  TKApp_H5SHARE
//
//  Created by <PERSON> on 2020/12/9.
//  Copyright © 2020 liubao. All rights reserved.
//

#import "TKSpeechRecognizeManager.h"



@interface TKSpeechRecognizeManager()<TKSpeechRecognizeManagerDelegate>

@property (nonatomic, strong) id<TKSpeechRecognizeManagerProtocol> speechRecognizeManager;

@end

@implementation TKSpeechRecognizeManager
@synthesize configParam;
@synthesize delegate;


#pragma mark - Init && Dealloc
/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam {
    if (self = [self init]) {
        self.configParam = configParam;
    }
    
    return  self;
}

#pragma mark - Selector
- (void)start
{
    [self.speechRecognizeManager start];
}

- (void)stop {
    //结束识别 停止录音，停止识别请求
    [self.speechRecognizeManager stop];
}

- (void)cancel {
    [self.speechRecognizeManager cancel];
}


- (BOOL)isRecognizing {
    return [self.speechRecognizeManager isRecognizing];
}

#pragma mark - TKSpeechRecognizeManagerDelegate

- (void)speechRecognizeDidStart {
    TKLogDebug(@"思迪语音识别调试:识别开始");
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidStart)]) {
            [self.delegate speechRecognizeDidStart];
        }
    });
}


/// 每个语音包分片识别结果
/// @param result 一段语音每次识别结果
- (void)speechRecognizeOnSliceRecognize:(NSString *)result {
    TKLogDebug(@"思迪语音识别调试:获取分片识别结果");
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeOnSliceRecognize:)]) {
            [self.delegate speechRecognizeOnSliceRecognize:result];
        }
    });
}

/// 一次语音识别结果
/// @param reslut 一次识别结果字符串
- (void)speechRecognizeDidRecognize:(NSString *)result {
    
    TKLogDebug(@"思迪语音识别调试:获取一次语音识别结果");
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidRecognize:)]) {
            [self.delegate speechRecognizeDidRecognize:result];
        }
    });
}

/// 语音识别结束回调
- (void)speechRecognizeDidComplete {
    TKLogDebug(@"思迪语音识别调试:语音识别结束");
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidComplete)]) {
            [self.delegate speechRecognizeDidComplete];
        }
    });
}

/// 语音识别失败回调
- (void)speechRecognizeDidFail:(NSString *)errorMsg {
    TKLogDebug(@"思迪语音识别调试:语音识别出错");
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidFail:)]) {
            [self.delegate speechRecognizeDidFail:errorMsg];
        }
    });
}

#pragma mark - Setter && Getter
- (id<TKSpeechRecognizeManagerProtocol>)speechRecognizeManager {
    if (!_speechRecognizeManager) {
        
        NSString *className = nil;
        
        // 获取真实的工具类
        className = self.speechSynthesManagerClassNameDic[@(speechRecognizeToolType)];
        Class speechRecognizeManagerClazz = NSClassFromString(className);
        NSAssert(speechRecognizeManagerClazz != nil, @"语音识别实现工具类不存在");
        
        _speechRecognizeManager = [[speechRecognizeManagerClazz alloc] initWithConfig:self.configParam];
        _speechRecognizeManager.delegate = self;
    }
    
    return (id<TKSpeechRecognizeManagerProtocol>)_speechRecognizeManager;
}

- (NSDictionary *)speechSynthesManagerClassNameDic {
    return @{
        @(1) : @"TKALSpeechRecognizeManager",  // 阿里
        @(2) : @"TKTencentSpeechRecognizeManager", // 腾讯
        @(3) : @"TKiflySpeechRecognizeManager", // 科大讯飞
        @(4) : @"TKTHSSpeechRecognizeManager", // 同花顺
    };
}

@end

