//
//  TKSpeechRecognizeManagerProtocol.h
//  TKApp_H5SHARE
//
//  Created by <PERSON> on 2020/12/9.
//  Copyright © 2020 liubao. All rights reserved.
//


@protocol TKSpeechRecognizeManagerDelegate <NSObject>

@optional
/// 语音识别开始回调
- (void)speechRecognizeDidStart;


/// 每个语音包分片识别结果
/// @param result 一段语音每次识别结果
- (void)speechRecognizeOnSliceRecognize:(NSString *)result;

/// 一次语音识别结果
/// @param reslut 一次识别结果字符串
- (void)speechRecognizeDidRecognize:(NSString *)result;

/// 语音识别结束回调
- (void)speechRecognizeDidComplete;

/// 语音识别失败回调
- (void)speechRecognizeDidFail:(NSString *)errorMsg;


@end


@protocol TKSpeechRecognizeManagerProtocol <NSObject>

@required
/// 代理对象
@property (nonatomic, readwrite, weak) id<TKSpeechRecognizeManagerDelegate> delegate;

/// 配置字典
@property (nonatomic, readwrite, weak) NSDictionary *configParam;

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam;

/// 开始识别
- (void)start;

/// 停止识别
- (void)stop;

/// 取消识别
- (void)cancel;

/// 是否正在识别
- (BOOL)isRecognizing;

/*!
 *  写入音频流
 */
- (BOOL)writeAudio:(NSData *)audioData;

@end


