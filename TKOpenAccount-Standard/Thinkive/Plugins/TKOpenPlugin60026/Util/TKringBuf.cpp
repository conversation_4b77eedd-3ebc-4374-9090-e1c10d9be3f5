//
//  ring_buf.cpp
//  testNuiUI
//
//  Created by zhouguangdong on 2019/12/4.
//  Copyright © 2019 Songsong Shao. All rights reserved.
//

#include "TKringBuf.h"
#include <assert.h>
#include <string.h>
#include <mutex>

static std::mutex lock;

int tk_ringbuffer_empty(RINGBUFFER_T *rb)
{
    if (rb == nullptr)
        return 1;
    
    std::unique_lock<decltype(lock)> auto_lock(lock);
    /* It's empty when the read and write pointers are the same. */
    if (0 == rb->fill) {
        return 1;
    }else {
        return 0;
    }
}

int tk_ringbuffer_full(RINGBUFFER_T *rb)
{
    if (rb == nullptr)
        return 0;
    
    std::unique_lock<decltype(lock)> auto_lock(lock);
    /* It's full when the write ponter is 1 element before the read pointer*/
    if (rb->size == rb->fill) {
        return 1;
    }else {
        return 0;
    }
}

int tk_ringbuffer_get_write_index(RINGBUFFER_T *rb) {
    return rb->write - rb->buffer;
}

int tk_ringbuffer_get_read_index(RINGBUFFER_T *rb) {
    return rb->read - rb->buffer;
}

int tk_ringbuffer_get_filled(RINGBUFFER_T *rb) {
    if (rb == nullptr)
        return 0;
    
    int r = tk_ringbuffer_get_read_index(rb);
    int w = tk_ringbuffer_get_write_index(rb);
    if (w >= r) {
        return w - r;
    } else {
        return w + rb->size - r;
    }
}

int tk_ringbuffer_read(RINGBUFFER_T *rb, unsigned char* buf, unsigned int len)
{
    if (rb == nullptr)
        return 0;
    
    std::unique_lock<decltype(lock)> auto_lock(lock);
//    printf("ringbuffer_read write %d read %d len %d fill %d\n", tk_ringbuffer_get_write_index(rb), tk_ringbuffer_get_read_index(rb), len, rb->fill);
    assert(len>0);
    if (rb->fill < len) {
        len = rb->fill;
    }
    if (rb->fill >= len) {
        // in one direction, there is enough data for retrieving
        if (rb->write > rb->read) {
            memcpy(buf, rb->read, len);
            rb->read += len;
        }else if (rb->write < rb->read) {
            long len1 = rb->buffer + rb->size - 1 - rb->read + 1;
            if (len1 >= len) {
                memcpy(buf, rb->read, len);
                rb->read += len;
            } else {
                long len2 = len - len1;
                memcpy(buf, rb->read, len1);
                memcpy(buf + len1, rb->buffer, len2);
                rb->read = rb->buffer + len2; // Wrap around
            }
        }
        rb-> fill -= len;
        return len;
    } else    {
        return 0;
    }
}

int tk_ringbuffer_write(RINGBUFFER_T *rb, unsigned char* buf, unsigned int len)
{
    if (rb == nullptr)
        return 0;
    
    std::unique_lock<decltype(lock)> auto_lock(lock);
//    printf("ringbuffer_write write %d read %d len %d fill %d\n", tk_ringbuffer_get_write_index(rb), tk_ringbuffer_get_read_index(rb), len, rb->fill);
    assert(len > 0);
    if (rb->size - rb->fill <= len) {
        return 0;
    }
    else {
        if (rb->write >= rb->read) {
            long remain = rb->buffer + rb->size - rb->write; // 缓冲区尾部剩余空间
            if (remain >= len) {
                memcpy(rb->write, buf, len);
                rb->write += len;
            } else {
                long circul_len = len - remain; // 数据写入缓冲区尾部后剩余未写入数据
                long leisure = rb->read - rb->buffer; // 缓冲区头部可写入空间
                memcpy(rb->write, buf, remain);
                memcpy(rb->buffer, buf+remain, circul_len);
                rb->write = rb->buffer + circul_len; // Wrap around
            }
        } else {
            memcpy(rb->write, buf, len);
            rb->write += len;
        }
        rb->fill += len;
        return len;
    }
}

void tk_ringbuffer_reset(RINGBUFFER_T *rb) {
    std::unique_lock<decltype(lock)> auto_lock(lock);
    if (rb == nullptr)
        return;
    
    rb->fill = 0;
    rb->write = rb->buffer;
    rb->read = rb->buffer;
    memset(rb->buffer, 0, rb->size);
    return;
}
