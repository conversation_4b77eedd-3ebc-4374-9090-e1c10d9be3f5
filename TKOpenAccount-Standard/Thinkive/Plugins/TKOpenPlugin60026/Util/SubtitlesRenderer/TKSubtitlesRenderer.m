//
//  TKSubtitlesRenderer.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2024/12/19.
//  Copyright © 2024 thinkive. All rights reserved.
//

#import "TKSubtitlesRenderer.h"

@implementation TKSubtitlesRenderer

#pragma mark - Init && Dealloc

- (instancetype)init {
    if (self = [super init]) {
        self.scrollSpeed = 0.19f; // 默认滚动速度
        self.changeReadTextViewWords = 0;
    }
    return self;
}

- (void)dealloc {
    [self stopSync];
}

#pragma mark - Public Methods

- (void)setupSubtitlesWithText:(NSString *)text {
    if ([TKStringHelper isEmpty:text] || !self.subtitlesTextView) {
        return;
    }
    
    NSString *string = [TKCommonUtil switchLabelToSpan:text];
    
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:string textColor:@"#FFFFFF"];
    attStr = [self convertTextToHtmlString:attStr.string textColor:@"#FFFFFF"]; // 去掉原来的html字符配置，强制转成白色字体
    
    // 不重复处理
    if (([self.subtitlesTextView.text isEqualToString:string]
        || [self.subtitlesTextView.attributedText isEqualToAttributedString:attStr]
        || [self.subtitlesTextView.attributedText.string isEqualToString:attStr.string]) 
        && self.changeReadTextViewWords > 0) {
        return;
    }
    
    // 先停止滚动
    [self stopSync];
    
    // 重置滚动值
    self.subtitlesTextView.attributedText = nil;
    self.subtitlesTextView.contentOffset = CGPointZero;
    
    // 生成富文本
    self.subtitlesTextView.attributedText = attStr;
    self.currentOriginHtmlStr = attStr;
    
    // 通知代理字幕已更新
    if (self.delegate && [self.delegate respondsToSelector:@selector(subtitlesDidUpdateWithAttributedText:)]) {
        [self.delegate subtitlesDidUpdateWithAttributedText:attStr];
    }
}

- (void)startSyncWithStartIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration {
    TKLogInfo(@"[TKSubtitlesRenderer] 开始字幕同步 - startIndex=%d, endIndex=%d, duration=%.2f", startIndex, endIndex, duration);
    [self scrollSubtitlesTextView:startIndex endIndex:endIndex duration:duration];
    TKLogInfo(@"[TKSubtitlesRenderer] 字幕滚动启动完成");
}

- (void)stopSync {
    if (self.changeReadTextViewOffSetTimer) {
        self.changeReadTextViewWords = 0;
        [self.changeReadTextViewOffSetTimer invalidate];
        self.changeReadTextViewOffSetTimer = nil;
    }
}

- (void)reset {
    [self stopSync];
    self.currentOriginHtmlStr = nil;
    if (self.subtitlesTextView) {
        self.subtitlesTextView.attributedText = nil;
        self.subtitlesTextView.contentOffset = CGPointZero;
    }
}

- (UIImage *)renderCurrentSubtitlesToImage {
    if (!self.subtitlesTextView) {
        return nil;
    }
    
    @autoreleasepool {
        // 创建一个UIImage对象，指定图片的大小和比例
        CGFloat scale = UIScreen.mainScreen.scale;
        CGSize imageSize = CGSizeMake(480.0 / scale, 640.0 / scale);
        UIGraphicsBeginImageContextWithOptions(imageSize, NO, 0);
        CGContextRef context = UIGraphicsGetCurrentContext();
        [[UIColor blackColor] setFill];
        CGContextFillRect(context, CGRectMake(0, 0, imageSize.width, imageSize.height));

        CGFloat margin = 10;
        // 在上下文中绘制字幕视图
        [self.subtitlesTextView drawViewHierarchyInRect:CGRectMake(margin, margin, self.subtitlesTextView.TKWidth / scale, self.subtitlesTextView.TKHeight / scale) afterScreenUpdates:YES];
        
        // 获取绘制好的图片
        UIImage *image = UIGraphicsGetImageFromCurrentImageContext();

        // 结束上下文
        UIGraphicsEndImageContext();
        
        // 通知代理图片已渲染
        if (self.delegate && [self.delegate respondsToSelector:@selector(subtitlesDidRenderToImage:)]) {
            [self.delegate subtitlesDidRenderToImage:image];
        }
        
        return image;
    }
}

- (void)createSubtitles:(NSString *)text showTime:(NSString *)showTime questionOneWordSpeed:(NSString *)questionOneWordSpeed autoScroll:(BOOL)autoScroll {
    
    TKLogInfo(@"TKSubtitlesRenderer createSubtitles: %@", text);
    
    [self setupSubtitlesWithText:text];
    
    // 问题播放需要判断是否走
    if (autoScroll) {
        // 计算滚动时间
        NSTimeInterval durationTime = [showTime doubleValue];
        if (durationTime <= 0) durationTime = 5.0f; // 默认5秒
        
        // 计算滚动速度
        float subtitlesScrollSpeed = [questionOneWordSpeed floatValue];
        if (subtitlesScrollSpeed == 0) subtitlesScrollSpeed = 0.19f;
        self.scrollSpeed = subtitlesScrollSpeed;
        
        [self createScrollSubtitlesTextViewTimer:self.currentOriginHtmlStr startIndex:0 endIndex:(int)text.length - 1 duration:durationTime isHighlight:YES];
    }
}

#pragma mark - Private Methods

- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString {
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)self.subtitlesTextView.font.pointSize, colorString, text];
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

- (void)scrollSubtitlesTextView:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration {
    // 先暂停滚动
    if (self.changeReadTextViewOffSetTimer) {
        [self.changeReadTextViewOffSetTimer invalidate];
        self.changeReadTextViewOffSetTimer = nil;
    }

    TKLogInfo(@"[TKSubtitlesRenderer] 开始字幕定时器滚动 - startIndex=%d, endIndex=%d, duration=%.2f", startIndex, endIndex, duration);
    [self createScrollSubtitlesTextViewTimer:self.currentOriginHtmlStr startIndex:startIndex endIndex:endIndex duration:duration isHighlight:YES];
}

- (void)createScrollSubtitlesTextViewTimer:(NSAttributedString *)originHtml startIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration isHighlight:(BOOL)isHighlight {
    if (self.changeReadTextViewOffSetTimer == nil) {
        
        // 文字高亮
        __weak typeof(self) weakSelf = self;
        NSTimeInterval repeatTime = 0.1f;
        __block int repeatIndex = 0; // 当前循环次数
        self.changeReadTextViewOffSetTimer = [NSTimer timerWithTimeInterval:repeatTime repeats:YES block:^(NSTimer * _Nonnull timer) {
            
            int currentRepeatIndex = repeatIndex;   // 只传递值
            [weakSelf changeReadTextViewOffSet:originHtml startIndex:startIndex endIndex:endIndex duration:duration * 0.9 repeatTime:repeatTime repeatIndex:currentRepeatIndex isHighlight:isHighlight];    // duration * 0.9是为了高亮和滚动效果快一点
            repeatIndex++; // 次数加一
        }];
        [[NSRunLoop mainRunLoop] addTimer:self.changeReadTextViewOffSetTimer forMode:NSRunLoopCommonModes];

        // 立即执行第一次字幕高亮，消除100ms延迟
        [self changeReadTextViewOffSet:originHtml startIndex:startIndex endIndex:endIndex duration:duration * 0.9 repeatTime:repeatTime repeatIndex:0 isHighlight:isHighlight];
        repeatIndex++; // 次数加一，保持与定时器同步
    }
}

- (void)changeReadTextViewOffSet:(NSAttributedString *)originHtml startIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration repeatTime:(NSTimeInterval)repeatTime repeatIndex:(int)repeatIndex isHighlight:(BOOL)isHighlight {

    // 使用传入的startIndex和endIndex进行精确的字幕高亮控制
    TKLogInfo(@"[TKSubtitlesRenderer] 字幕更新 - startIndex=%d, endIndex=%d, repeatIndex=%d, duration=%.2f", startIndex, endIndex, repeatIndex, duration);

    if (self.changeReadTextViewWords < startIndex) {
        self.changeReadTextViewWords = startIndex;
    }

    int wordPerSecond = 0;
    int totalChars = endIndex - startIndex;

    // 修正：基于scrollSpeed配置计算每次滚动的字数
    float charsPerSecond = 0.0f;
    if (self.scrollSpeed > 0) {
        // 基于配置的scrollSpeed计算每秒应该高亮的字数
        charsPerSecond = 1.0f / self.scrollSpeed;  // scrollSpeed是每个字符的时间，倒数就是每秒字数
        // 计算每次定时器触发应该增加的字数
        wordPerSecond = ceilf(charsPerSecond * repeatTime);
    } else {
        wordPerSecond = 1;
    }

    if (wordPerSecond <= 0) wordPerSecond = 1;

    TKLogInfo(@"[TKSubtitlesRenderer] 滚动计算 - totalChars=%d, duration=%.2f, scrollSpeed=%.2f, charsPerSecond=%.2f, wordPerSecond=%d",
             totalChars, duration, self.scrollSpeed, charsPerSecond, wordPerSecond);

    // 检查是否还有内容需要滚动（修正：应该检查是否到达endIndex）
    if (self.changeReadTextViewWords < endIndex && self.changeReadTextViewWords < self.subtitlesTextView.attributedText.string.length) {

        // 高亮处理
        if (isHighlight && self.subtitlesTextView.attributedText) {
            NSMutableAttributedString *html = self.subtitlesTextView.attributedText.mutableCopy;

            // 重置所有文字颜色为默认色
            [html addAttribute:NSForegroundColorAttributeName value:[UIColor whiteColor] range:NSMakeRange(0, html.length)];

            // 修正：基于定时器进度计算当前应该高亮到的位置
            NSInteger currentHighlightEnd = MIN(self.changeReadTextViewWords + wordPerSecond, endIndex);
            currentHighlightEnd = MIN(currentHighlightEnd, html.length);

            NSInteger safeStartIndex = MAX(0, MIN(startIndex, html.length));

            if (currentHighlightEnd > safeStartIndex) {
                // 增量高亮：从startIndex到当前计算的位置
                NSRange highlightRange = NSMakeRange(safeStartIndex, currentHighlightEnd - safeStartIndex);
                [html addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FCC006"] range:highlightRange];

                TKLogInfo(@"[TKSubtitlesRenderer] 字幕增量高亮 - startIndex=%d, currentEnd=%ld, totalEnd=%d, changeWords=%ld, highlightRange=(%ld,%ld)",
                         startIndex, (long)currentHighlightEnd, endIndex, (long)self.changeReadTextViewWords, (long)highlightRange.location, (long)highlightRange.length);
            } else {
                TKLogInfo(@"[TKSubtitlesRenderer] 字幕高亮跳过 - startIndex=%d, currentEnd=%ld, 范围无效", startIndex, (long)currentHighlightEnd);
            }

            self.subtitlesTextView.attributedText = html;

            // 通知代理字幕已更新
            if (self.delegate && [self.delegate respondsToSelector:@selector(subtitlesDidUpdateWithAttributedText:)]) {
                [self.delegate subtitlesDidUpdateWithAttributedText:html];
            }
        }

        // 滚动处理
        NSInteger lenth = wordPerSecond * 50; // 4s的播放长度
        NSInteger maxLenth = self.subtitlesTextView.attributedText.string.length - self.changeReadTextViewWords;
        lenth = lenth < (maxLenth) ? lenth : maxLenth;
        NSRange range = NSMakeRange(self.changeReadTextViewWords, lenth); // 为了展示效果，多滚动一些内容
        [self.subtitlesTextView scrollRangeToVisible:range];

        // 记录数据
        self.changeReadTextViewWords = self.changeReadTextViewWords + wordPerSecond;

    } else {
        TKLogInfo(@"[TKSubtitlesRenderer] 字幕滚动完成 - changeWords=%ld, endIndex=%d, 停止定时器", (long)self.changeReadTextViewWords, endIndex);
        [self stopSync];
    }
}

@end
