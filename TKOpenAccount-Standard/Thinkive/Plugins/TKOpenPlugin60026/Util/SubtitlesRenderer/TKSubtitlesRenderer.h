//
//  TKSubtitlesRenderer.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2024/12/19.
//  Copyright © 2024 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol TKSubtitlesRendererDelegate <NSObject>

@optional
/// 字幕更新回调，供外层UI更新
/// @param attributedText 更新后的富文本
- (void)subtitlesDidUpdateWithAttributedText:(NSAttributedString *)attributedText;

/// 字幕渲染完成回调，供外层获取渲染图片
/// @param image 渲染后的字幕图片
- (void)subtitlesDidRenderToImage:(UIImage *)image;

@end

/**
 * 字幕渲染工具类
 * 专门负责字幕的渲染、高亮效果和滚动动画
 * 可被多个录制管理类复用
 */
@interface TKSubtitlesRenderer : NSObject

/// 代理对象
@property (nonatomic, weak) id<TKSubtitlesRendererDelegate> delegate;

/// 字幕显示控件
@property (nonatomic, weak) UITextView *subtitlesTextView;

/// 滚动速度，默认0.19f
@property (nonatomic, assign) float scrollSpeed;

/// 当前变色的字数
@property (nonatomic, assign) int changeReadTextViewWords;

/// 当前原始HTML富文本
@property (nonatomic, strong) NSAttributedString *currentOriginHtmlStr;

/// 字幕滚动定时器
@property (nonatomic, strong) NSTimer *changeReadTextViewOffSetTimer;

/// 初始化字幕内容
/// @param text 字幕文本内容
- (void)setupSubtitlesWithText:(NSString *)text;

/// 开始字幕同步（根据播放进度更新高亮）
/// @param startIndex 开始索引
/// @param endIndex 结束索引
/// @param duration 持续时间
- (void)startSyncWithStartIndex:(int)startIndex 
                       endIndex:(int)endIndex 
                       duration:(NSTimeInterval)duration;

/// 停止字幕同步
- (void)stopSync;

/// 重置字幕状态
- (void)reset;

/// 渲染当前字幕为图片
/// @return 渲染后的字幕图片
- (UIImage * _Nullable)renderCurrentSubtitlesToImage;

/// 创建字幕，根据展示时间滚动
/// @param text 字幕内容
/// @param showTime 展示时间
/// @param questionOneWordSpeed 字幕滚动速度
/// @param autoScroll 自动滚动
- (void)createSubtitles:(NSString *)text 
               showTime:(NSString *)showTime 
   questionOneWordSpeed:(NSString *)questionOneWordSpeed 
             autoScroll:(BOOL)autoScroll;

@end

NS_ASSUME_NONNULL_END
