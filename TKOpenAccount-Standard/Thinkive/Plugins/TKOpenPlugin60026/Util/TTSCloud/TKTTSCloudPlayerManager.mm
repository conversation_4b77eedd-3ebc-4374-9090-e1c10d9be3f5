//
//  TKTTSCloudPlayerManager.mm
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2024/12/19.
//  Copyright © 2024 thinkive. All rights reserved.
//

#import "TKTTSCloudPlayerManager.h"
#import "TKOpenAccountService.h"
#import "TKPlayRangeModel.h"
#import "TKOpenAccountService.h"

// 语音播放工具类
#if __has_include(<TKSpeechSynthesisManager.h>)
#define IsImportTKSpeechUtil 1
#import "TKNLSPlayAudio.h"
#elif __has_include("TKSpeechSynthesisManager.h")
#define IsImportTKSpeechUtil 1
#import "TKNLSPlayAudio.h"
#else
#define IsImportTKSpeechUtil 0
#endif

// WAV文件头结构
typedef struct {
    char riff[4];           // "RIFF"
    uint32_t fileSize;      // 文件大小
    char wave[4];           // "WAVE"
    char fmt[4];            // "fmt "
    uint32_t fmtSize;       // fmt块大小
    uint16_t audioFormat;   // 音频格式
    uint16_t numChannels;   // 声道数
    uint32_t sampleRate;    // 采样率
    uint32_t byteRate;      // 字节率
    uint16_t blockAlign;    // 块对齐
    uint16_t bitsPerSample; // 位深度
    char data[4];           // "data"
    uint32_t dataSize;      // 数据大小
} WAVHeader;

#define kDownloadLenth (1024 * 64)      // 65536

#if IsImportTKSpeechUtil
@interface TKTTSCloudPlayerManager()<NlsPlayerDelegate>
#else
@interface TKTTSCloudPlayerManager()
#endif

/// 网络服务对象，用于token获取和文件下载（内部创建）
@property(nonatomic, strong) TKOpenAccountService *openAccountService;

#if IsImportTKSpeechUtil
@property(nonatomic, strong) TKNLSPlayAudio *nlsAudioPlayer; // 语音播放工具类
#endif

@end

@implementation TKTTSCloudPlayerManager

@synthesize delegate;
@synthesize configParam;

#pragma mark - Init && Dealloc

- (instancetype)initWithConfig:(NSDictionary *)configParam {
    if (self = [super init]) {
        self.configParam = configParam;

        // 调试输出：验证条件编译是否生效
#if IsImportTKSpeechUtil
        TKLogInfo(@"[TKTTSCloudPlayerManager] IsImportTKSpeechUtil=1, NlsPlayerDelegate协议已启用");
#else
        TKLogInfo(@"[TKTTSCloudPlayerManager] IsImportTKSpeechUtil=0, NlsPlayerDelegate协议未启用");
#endif

        // 内部创建网络服务对象
        self.openAccountService = [[TKOpenAccountService alloc] init];

        // 创建音频播放队列
        NSString *queueName = [NSString stringWithFormat:@"com.thinkive.%@_audioPlayQueue", self.class];
        const char *queueName_char = [queueName UTF8String];
        _audioPlayQueue = dispatch_queue_create(queueName_char, DISPATCH_QUEUE_SERIAL);

        // 初始化播放状态
        self.isForceStopPlay = NO;
        self.currentPlayRangeIndex = 0;
        self.currentRemoteFileTotalSize = 0;
    }
    return self;
}

- (void)dealloc {
#if IsImportTKSpeechUtil
    [self.nlsAudioPlayer stop];
    [self.nlsAudioPlayer cleanup];
#endif
}


#pragma mark - Public Methods
- (void)stop {
#if IsImportTKSpeechUtil
    [self.nlsAudioPlayer stop];
#endif
    self.isForceStopPlay = YES;
}

- (void)stopSynthetic {
    [self stop];
}

- (void)stopPlay {
    [self stop];
}

- (void)cancel {
    [self stop];
}

- (BOOL)isSyntheticing {
    return NO; // 云端播放不涉及合成
}

- (BOOL)isPlaying {
#if IsImportTKSpeechUtil
    return self.nlsAudioPlayer != nil && !self.isForceStopPlay;
#else
    return NO;
#endif
}

- (BOOL)canSynthesisInAdvance {
    return NO; // 云端播放不支持预合成
}

- (BOOL)checkCanPlayRemoteVideo {
    if (self.isForceStopPlay) {
        TKLogInfo(@"思迪录制日志:强制停止播放，不予处理");
        return NO;
    }
    return YES;
}


#pragma mark - Private Methods


- (void)notifyPlayStart {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
            [self.delegate speechSynthesisDidStart];
        }
    });
}

- (void)notifyPlayFail:(NSString *)errorMsg {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
            [self.delegate speechSynthesisDidFail:errorMsg];
        }
    });
}

- (void)notifyPlayDone {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
            [self.delegate speechSynthesisDidPlayDone];
        }
    });
}



#pragma mark - Core Implementation Methods

- (void)getVideoUrl:(NSString *)url callBackFunc:(void(^)(BOOL isSuccess, NSString *errorMsg, NSString *newVoiceUrl))callBackFunc {
    NSMutableString *tokenUrl = [self.configParam getStringWithKey:@"voiceTokenUrl"].mutableCopy;
    tokenUrl = [TKCommonUtil url:tokenUrl appendingParamStr:[NSString stringWithFormat:@"voiceUrl=%@",url]].mutableCopy;

    NSMutableDictionary *params = [NSMutableDictionary dictionary];

    [self.openAccountService getVideoTokenWithURL:tokenUrl param:params callBackFunc:^(ResultVo *resultVo) {

        NSArray *results = (NSArray *)resultVo.results;
        if (resultVo.errorNo == 0) {
            NSDictionary *dic = results.firstObject;

            NSString *time = [dic getStringWithKey:@"time"];
            NSString *token = [dic getStringWithKey:@"token"];
            NSString *newVoiceUrl = [TKCommonUtil url:url appendingParamStr:[NSString stringWithFormat:@"t=%@&token=%@",time,token]];

            if (callBackFunc) {
                callBackFunc(YES, @"", newVoiceUrl);
            }
        } else {
            NSString *errorInfo = resultVo.errorInfo;
            if (callBackFunc) {
                callBackFunc(NO, errorInfo, @"");
            }
        }
    }];
}

- (void)downloadOneWayVideo:(NSString *)remoteUrlStr downloadRange:(NSRange)downloadRange totalSize:(unsigned long)totalSize keepDownloading:(BOOL)keepDownloading callBack:(void(^)(BOOL success, NSString *errorMsg, NSData *data))callBack {

    if ([self checkCanPlayRemoteVideo] == NO) {
        return;
    }

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:remoteUrlStr]];
    [request setHTTPMethod:@"GET"];
    [request setValue:@"bytes=0-" forHTTPHeaderField:@"Range"];

    if (downloadRange.length > 0) {
        NSString *rangeValue = [NSString stringWithFormat:@"bytes=%lu-%lu", (unsigned long)downloadRange.location, (unsigned long)(downloadRange.location + downloadRange.length - 1)];
        [request setValue:rangeValue forHTTPHeaderField:@"Range"];
    }

    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {

        if (error) {
            TKLogError(@"思迪录制日志:下载音频数据失败: %@", error.localizedDescription);
            if (callBack) {
                callBack(NO, error.localizedDescription, nil);
            }
            return;
        }

        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        if (httpResponse.statusCode >= 200 && httpResponse.statusCode < 300) {
            if (callBack) {
                callBack(YES, @"", data);
            }
        } else {
            NSString *errorMsg = [NSString stringWithFormat:@"HTTP错误: %ld", (long)httpResponse.statusCode];
            TKLogError(@"思迪录制日志:下载音频数据HTTP错误: %@", errorMsg);
            if (callBack) {
                callBack(NO, errorMsg, nil);
            }
        }
    }];

    [task resume];
}

- (void)handleWAVAudioFormat:(NSString *)remoteUrlStr data:(NSData *)data playRangeModels:(NSArray *)playRangeModels {
    __weak typeof(self) weakSelf = self;
    dispatch_async(self.audioPlayQueue, ^{

        __strong typeof(weakSelf) strongSelf = weakSelf;
        // 解析 WAV 文件头部
        WAVHeader *header = (WAVHeader *)[data bytes];

        if (header == nil) {
            TKLogInfo(@"思迪录制日志:解析音频头失败，数据为空");
            [weakSelf notifyPlayFail:@"语音请求失败(加载云端音频失败)"];
            return;
        }

#if IsImportTKSpeechUtil
        // 创建 AudioStreamBasicDescription 结构
        AudioStreamBasicDescription audioDesc;
        memset(&audioDesc, 0, sizeof(audioDesc)); // 初始化为零

        audioDesc.mFormatID = kAudioFormatLinearPCM;
        audioDesc.mFormatFlags = kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
        audioDesc.mBytesPerPacket = (header->bitsPerSample / 8) * header->numChannels;
        audioDesc.mFramesPerPacket = 1;
        audioDesc.mBytesPerFrame = audioDesc.mBytesPerPacket;
        audioDesc.mChannelsPerFrame = header->numChannels;
        audioDesc.mBitsPerChannel = header->bitsPerSample;
        audioDesc.mSampleRate = (Float64)header->sampleRate;

        // 更新播放器
        weakSelf.nlsAudioPlayer.audioDescription = audioDesc;
        [weakSelf.nlsAudioPlayer setstate:(PlayerState)playing];
        [weakSelf.nlsAudioPlayer play];

        // 开始下载音频数据
        unsigned long startIndex = sizeof(WAVHeader);
        unsigned long endIndex = startIndex + kDownloadLenth - 1;
        strongSelf.currentRemoteFileTotalSize = header->dataSize + sizeof(WAVHeader);

        [strongSelf downloadAudioDataRecursively:remoteUrlStr startIndex:startIndex endIndex:endIndex];
#else
        [weakSelf notifyPlayFail:@"语音播放功能不可用"];
#endif
    });
}

#if IsImportTKSpeechUtil
- (void)downloadAudioDataRecursively:(NSString *)remoteUrlStr startIndex:(unsigned long)startIndex endIndex:(unsigned long)endIndex {

    __weak typeof(self) weakSelf = self;

    // 检查是否已下载完成
    if (startIndex >= self.currentRemoteFileTotalSize) {
        TKLogInfo(@"思迪录制日志:音频数据下载完成");
        [self.nlsAudioPlayer drain];
        return;
    }

    // 调整结束索引
    if (endIndex >= self.currentRemoteFileTotalSize) {
        endIndex = self.currentRemoteFileTotalSize - 1;
    }

    unsigned long downloadLength = endIndex - startIndex + 1;

    [self downloadOneWayVideo:remoteUrlStr downloadRange:NSMakeRange(startIndex, downloadLength) totalSize:downloadLength keepDownloading:YES callBack:^(BOOL success, NSString *errorMsg, NSData *data) {

        if ([weakSelf checkCanPlayRemoteVideo] == NO) {
            return;
        }

        if (success && data.length > 0) {
            // 写入音频数据到播放器
            [weakSelf.nlsAudioPlayer write:(const char*)data.bytes Length:(int)data.length];

            // 同时回调音频数据给delegate（用于视频录制）
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(speechSynthesisDidDataBuffer:len:)]) {
                TKLogInfo(@"[TKTTSCloudPlayerManager] 回调音频数据 - length=%d", (int)data.length);
                [weakSelf.delegate speechSynthesisDidDataBuffer:(char*)data.bytes len:(int)data.length];
            } else {
                TKLogInfo(@"[TKTTSCloudPlayerManager] delegate不响应音频数据回调或为空");
            }

            // 修复字幕高亮延迟：第一次下载音频数据时立即触发第一段字幕回调
            if (startIndex == sizeof(WAVHeader) && weakSelf.playRangeModels.count > 0 && weakSelf.currentPlayRangeIndex == 0) {
                TKPlayRangeModel *firstPlayRangeModel = weakSelf.playRangeModels[0];

                // 计算第一段字幕的播放时长
                NSInteger nextRangeStart = weakSelf.currentRemoteFileTotalSize;
                if (weakSelf.playRangeModels.count > 1) {
                    TKPlayRangeModel *secondPlayRangeModel = weakSelf.playRangeModels[1];
                    nextRangeStart = secondPlayRangeModel.rangeStart;
                }
                NSTimeInterval duration = (nextRangeStart - firstPlayRangeModel.rangeStart) * 1.0f / 32000;

                TKLogInfo(@"[TKTTSCloudPlayerManager] 修复字幕延迟 - 立即触发第一段字幕回调 - startIndex=%ld, endIndex=%ld, duration=%.2f", (long)firstPlayRangeModel.startIndex, (long)firstPlayRangeModel.endIndex, duration);

                // 立即回调第一段字幕高亮
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(speechSynthesisDidPlayWithStartIndex:endIndex:duration:)]) {
                        [weakSelf.delegate speechSynthesisDidPlayWithStartIndex:firstPlayRangeModel.startIndex endIndex:firstPlayRangeModel.endIndex duration:duration];
                        TKLogInfo(@"[TKTTSCloudPlayerManager] 第一段字幕回调成功触发");
                    }
                });

                // 更新当前播放范围索引，避免重复回调
                weakSelf.currentPlayRangeIndex = 1;
            }

            // 继续下载下一段
            unsigned long nextStartIndex = endIndex + 1;
            unsigned long nextEndIndex = nextStartIndex + kDownloadLenth - 1;
            [weakSelf downloadAudioDataRecursively:remoteUrlStr startIndex:nextStartIndex endIndex:nextEndIndex];

        } else {
            TKLogError(@"思迪录制日志:下载音频数据失败: %@", errorMsg);
            [weakSelf notifyPlayFail:[NSString stringWithFormat:@"语音请求失败(加载云端音频失败(%@))", errorMsg]];
            [weakSelf.nlsAudioPlayer stop];
        }
    }];
}

#pragma mark - NlsPlayerDelegate

- (void)playerDidFail:(NSString *)errMsg {
    TKLogInfo(@"[TKTTSCloudPlayerManager] NlsPlayerDelegate - playerDidFail: %@", errMsg);
    [self notifyPlayFail:errMsg];
}

- (void)playerHavePlayed:(NSInteger)currentPlayIndex willPlayIndex:(NSInteger)willPlayIndex {
    TKLogInfo(@"[TKTTSCloudPlayerManager] NlsPlayerDelegate - playerHavePlayed: currentIndex=%ld, willPlayIndex=%ld", (long)currentPlayIndex, (long)willPlayIndex);

    if (self.playRangeModels.count <= 0) return;
    if (self.currentPlayRangeIndex > (self.playRangeModels.count - 1)) return;

    TKPlayRangeModel *playRangeModel = self.playRangeModels[self.currentPlayRangeIndex];
    NSInteger rangeStart = playRangeModel.rangeStart;

    if (currentPlayIndex > rangeStart) {

        // 计算播放用时
        TKPlayRangeModel *nextPlayRangeModel = nil;
        NSInteger nextRangeStart = rangeStart;
        if (self.currentPlayRangeIndex + 1 <= (self.playRangeModels.count - 1)) {
            nextPlayRangeModel = self.playRangeModels[self.currentPlayRangeIndex + 1];
            nextRangeStart = nextPlayRangeModel.rangeStart;
        } else {
            nextRangeStart = self.currentRemoteFileTotalSize;
        }
        NSTimeInterval duration = (nextRangeStart - rangeStart) * 1.0f / 32000;  // 每秒播放32000

        TKLogInfo(@"[TKTTSCloudPlayerManager] 后续字幕回调 - 第%d段 - startIndex=%ld, endIndex=%ld, duration=%.2f", self.currentPlayRangeIndex, (long)playRangeModel.startIndex, (long)playRangeModel.endIndex, duration);

        // 更新UI 高亮范围
        dispatch_async(dispatch_get_main_queue(), ^{

            // 通知播放进度（第一段已在downloadAudioDataRecursively中提前触发）
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayWithStartIndex:endIndex:duration:)]) {
                TKLogInfo(@"[TKTTSCloudPlayerManager] 调用delegate后续字幕回调成功 - 第%d段", self.currentPlayRangeIndex);
                [self.delegate speechSynthesisDidPlayWithStartIndex:playRangeModel.startIndex endIndex:playRangeModel.endIndex duration:duration];
            } else {
                TKLogInfo(@"[TKTTSCloudPlayerManager] delegate字幕回调失败 - delegate为空或不响应方法");
            }
        });

        self.currentPlayRangeIndex++;
    }
}

- (void)playerDidFinish {
    TKLogInfo(@"[TKTTSCloudPlayerManager] NlsPlayerDelegate - playerDidFinish");
    [self notifyPlayDone];
}

- (void)playerDidTimeOut {
    TKLogInfo(@"[TKTTSCloudPlayerManager] NlsPlayerDelegate - playerDidTimeOut");
    [self notifyPlayFail:@"网络不稳定，请切换网络再试"];
}

#endif

#pragma mark - TKSpeechSynthesisManagerProtocol

- (void)playRemoteUrl:(NSString *)url tipSpeed:(NSString *)tipSpeed needRequestToken:(BOOL)needRequestToken playRangeModels:(NSArray *)playRangeModels {
    
    self.isForceStopPlay = NO;
    self.playRangeModels = playRangeModels;
    self.currentPlayRangeIndex = 0;
    self.currentRemoteFileTotalSize = 0;
    
    // 通知开始播放
    [self notifyPlayStart];
    
    __weak typeof(self) weakSelf = self;
    
    // 直接播放文件
    if (needRequestToken == NO) {
        __block NSMutableString *newVoiceUrl = url.mutableCopy;
        
        // 下载音频文件头
        [self downloadOneWayVideo:newVoiceUrl downloadRange:NSMakeRange(0, sizeof(WAVHeader)) totalSize:sizeof(WAVHeader) keepDownloading:NO callBack:^(BOOL success, NSString *errorMsg, NSData *data) {
            
            if ([weakSelf checkCanPlayRemoteVideo] == NO) {
                return;
            }

            if (success) {
                // 处理wav头
                [weakSelf handleWAVAudioFormat:newVoiceUrl data:data playRangeModels:playRangeModels];
            } else {
                [weakSelf notifyPlayFail:errorMsg];
            }
        }];
        return;
    }
    
    // 先获取token,再播放文件
    [self getVideoUrl:url callBackFunc:^(BOOL isSuccess, NSString *errorMsg, NSString *newVoiceUrl) {
        if (isSuccess) {
            
            TKLogInfo(@"思迪录制日志:生成的缓存tts路径=%@", newVoiceUrl);
            
            if ([weakSelf checkCanPlayRemoteVideo] == NO) {
                return;
            }

            // 下载音频文件头
            [weakSelf downloadOneWayVideo:newVoiceUrl downloadRange:NSMakeRange(0, sizeof(WAVHeader)) totalSize:sizeof(WAVHeader) keepDownloading:NO callBack:^(BOOL success, NSString *errorMsg, NSData *data) {
                
                if ([weakSelf checkCanPlayRemoteVideo] == NO) {
                    return;
                }

                if (success) {
                    // 处理wav头
                    [weakSelf handleWAVAudioFormat:newVoiceUrl data:data playRangeModels:playRangeModels];
                } else {
                    [weakSelf notifyPlayFail:errorMsg];
                }
            }];
        } else {
            [weakSelf notifyPlayFail:errorMsg];
        }
    }];
}

// 以下方法为协议要求但本类不支持的功能，提供空实现
- (void)syntheticAndPlay:(NSString *)text tipSpeed:(NSString *)tipSpeed {
    TKLogError(@"TKTTSCloudPlayerManager不支持实时语音合成功能，请使用TKSpeechSynthesisManager");
}

- (void)syntheticAndPlayContents:(NSArray *)texts tipSpeed:(NSString *)tipSpeed {
    TKLogError(@"TKTTSCloudPlayerManager不支持实时语音合成功能，请使用TKSpeechSynthesisManager");
}

- (void)synthesisInAdvanceWithTextArray:(NSArray *)textArray tipSpeed:(NSString *)tipSpeed {
    TKLogError(@"TKTTSCloudPlayerManager不支持预合成功能，请使用TKSpeechSynthesisManager");
}

- (void)syntheticWithText:(NSString *)text autoPlay:(BOOL)autoPlay tipSpeed:(NSString *)tipSpeed {
    TKLogError(@"TKTTSCloudPlayerManager不支持实时语音合成功能，请使用TKSpeechSynthesisManager");
}

#pragma mark - setter && getter
- (NSURLSession *)session {
    if (!_session) {
        // 1. 创建NSURLSession对象
        // 创建NSURLSessionConfiguration对象
        NSURLSessionConfiguration *configuration = [NSURLSessionConfiguration defaultSessionConfiguration];

        // 设置超时时间为10秒
        configuration.timeoutIntervalForRequest = 10.0;

        // 创建NSURLSession对象
        _session = [NSURLSession sessionWithConfiguration:configuration];
    }
    
    return _session;
}

#if IsImportTKSpeechUtil
- (TKNLSPlayAudio *)nlsAudioPlayer {
    if (!_nlsAudioPlayer) {
        _nlsAudioPlayer = [[TKNLSPlayAudio alloc] init];
        _nlsAudioPlayer.delegate = self;
    }
    return _nlsAudioPlayer;
}
#endif

@end
