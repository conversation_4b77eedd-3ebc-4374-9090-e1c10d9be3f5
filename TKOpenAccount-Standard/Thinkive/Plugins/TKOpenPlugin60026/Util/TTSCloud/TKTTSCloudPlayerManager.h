//
//  TKTTSCloudPlayerManager.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2024/12/19.
//  Copyright © 2024 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKSpeechSynthesisManagerProtocol.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * TTS云端音频播放管理器
 * 专门负责播放云端缓存的TTS音频文件
 * 实现TKSpeechSynthesisManagerProtocol协议，但只处理云端播放相关功能
 */
@interface TKTTSCloudPlayerManager : NSObject<TKSpeechSynthesisManagerProtocol>

/// 是否强制停止播放
@property (nonatomic, assign) BOOL isForceStopPlay;

/// 下载session
@property (nonatomic, readwrite, strong) NSURLSession *session;

/// 当前播放的范围模型数组
@property (nonatomic, strong) NSArray *playRangeModels;

/// 当前播放范围索引
@property (nonatomic, assign) int currentPlayRangeIndex;

/// 当前远程文件总大小
@property (nonatomic, assign) unsigned long currentRemoteFileTotalSize;

/// 音频播放队列
@property (nonatomic, strong) dispatch_queue_t audioPlayQueue;

/// 检查是否可以播放远程视频
- (BOOL)checkCanPlayRemoteVideo;

/// 获取带token的视频URL
/// @param url 原始URL
/// @param callBackFunc 回调函数
- (void)getVideoUrl:(NSString *)url callBackFunc:(void(^)(BOOL isSuccess, NSString *errorMsg, NSString *newVoiceUrl))callBackFunc;

/// 下载音频数据
/// @param remoteUrlStr 远程URL
/// @param downloadRange 下载范围
/// @param totalSize 总大小
/// @param keepDownloading 是否继续下载
/// @param callBack 回调函数
- (void)downloadOneWayVideo:(NSString *)remoteUrlStr 
              downloadRange:(NSRange)downloadRange 
                  totalSize:(unsigned long)totalSize 
            keepDownloading:(BOOL)keepDownloading 
                   callBack:(void(^)(BOOL success, NSString *errorMsg, NSData *data))callBack;

/// 处理WAV音频格式
/// @param remoteUrlStr 远程URL
/// @param data WAV头数据
/// @param playRangeModels 播放范围模型数组
- (void)handleWAVAudioFormat:(NSString *)remoteUrlStr 
                        data:(NSData *)data 
             playRangeModels:(NSArray *)playRangeModels;

@end

NS_ASSUME_NONNULL_END
