//
//  TKPlayerToolView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2022/11/10.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKPlayerToolView.h"
@interface TKPlayerToolView()
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@property (nonatomic, strong) UIView *playLineView;//当前播放进度占比
@property (nonatomic, strong) UIButton *replayBtn;//重播按钮
@property (nonatomic, strong) UIButton *pauseBtn;//暂停按钮
@end
@implementation TKPlayerToolView

/**
 *<AUTHOR> 2022年11月10日14:46:42
 *@单向视频重播，暂停播放进度条视图
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self=[super initWithFrame:frame];
    if (self) {
        [self setBackgroundColor:[UIColor clearColor]];
        self.requestParams=requestParams;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.requestParams[@"mainColor"]=nil;
        }
        [self viewInit];
    }
    return self;
}
/**
 <AUTHOR> 2022年11月10日14:47:11
 */
-(void)viewInit{
    [self addSubview:self.playLineView];
    [self addSubview:self.replayBtn];
    [self addSubview:self.pauseBtn];
}

/**
@Auther Vie 2022年11月10日16:44:19
@param playTime 当前播放时间
@param longestTime 最长播放时间
*/
-(void)playTime:(float)currentTime longestTime:(float)longestTime{
    
    if (longestTime == 0) return;
    [self.playLineView setFrameWidth:self.TKWidth/longestTime*currentTime];
}

/**
@Auther Vie 2022年11月10日16:54:16
@暂停播放
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
-(void)stopPlay:(float)currentTime longestTime:(float)longestTime{
    
    if (longestTime == 0) return;
    
    [self setHidden:YES];
    [self.playLineView.layer removeAllAnimations];
    [self.playLineView setFrameWidth:self.TKWidth/longestTime*currentTime];
}

#pragma mark 按钮事件
//重新播放按钮事件
-(void)replayAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(replayVideoBtnAction)]) {
        [self.delegate replayVideoBtnAction];
    }
}

//暂停播放按钮事件
-(void)pauseAction{
    [self setHidden:YES];
    if (self.delegate&&[self.delegate respondsToSelector:@selector(pauseVideoBtnAction)]) {
        [self.delegate pauseVideoBtnAction];
    }
}

#pragma mark lazyloading

/**
 <AUTHOR> 2022年11月10日15:23:24
 @初始化懒加载当前播放进度占比
 @return 当前播放进度占比
 */
-(UIView *)playLineView{
    if (!_playLineView) {
        _playLineView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, 36, 4)];
        _playLineView.layer.cornerRadius=2.0f;
        _playLineView.backgroundColor=[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#387CFF"];
    }
    return _playLineView;
}

/**
 <AUTHOR> 2022年11月10日15:07:49
 @初始化懒加载重播按钮
 @return 重播按钮
 */
-(UIButton *)replayBtn{
    if (!_replayBtn) {
        float width=(self.TKWidth-1)/2.0f;
        float height=self.TKHeight-self.playLineView.TKBottom;
        _replayBtn=[[UIButton alloc] initWithFrame:CGRectMake(0, self.playLineView.TKBottom, width, height)];
        _replayBtn.backgroundColor=[UIColor clearColor];
        
        CGRect pathRect=CGRectMake(0, 0, width, height);
        UIBezierPath *path=[UIBezierPath bezierPathWithRoundedRect:pathRect byRoundingCorners:UIRectCornerBottomLeft cornerRadii:CGSizeMake(8, 8)];
        //创建CAShapeLayer
        CAShapeLayer *shapeLayer=[CAShapeLayer layer];
        shapeLayer.fillColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.4f].CGColor;
        shapeLayer.path=path.CGPath;
        [_replayBtn.layer addSublayer:shapeLayer];
        
        //创建富文本
        NSString *string=@" 重播";
        NSMutableAttributedString *attri = [[NSMutableAttributedString alloc] initWithString:string];
        [attri addAttribute:NSForegroundColorAttributeName value:[UIColor whiteColor] range:NSMakeRange(0, string.length)];
        [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, string.length)];
                
        //NSTextAttachment可以将要插入的图片作为特殊字符处理
        NSTextAttachment *attch = [[NSTextAttachment alloc] init];
        //定义图片内容及位置和大小
        attch.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60030/tk_replay_btn.png", TK_OPEN_RESOURCE_NAME]];
        attch.bounds = CGRectMake(0, -2, 16, 16);
        //创建带有图片的富文本
        NSAttributedString *attrString = [NSAttributedString attributedStringWithAttachment:attch];
        //将图片放在第一位
        [attri insertAttributedString:attrString atIndex:0];
        [_replayBtn setAttributedTitle:attri forState:UIControlStateNormal];
        [_replayBtn addTarget:self action:@selector(replayAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _replayBtn;
}

/**
 <AUTHOR> 2022年11月10日15:07:49
 @初始化懒加载暂停按钮
 @return 暂停按钮
 */
-(UIButton *)pauseBtn{
    if (!_pauseBtn) {
        float width=self.replayBtn.TKWidth;
        float height=self.replayBtn.TKHeight;
        _pauseBtn=[[UIButton alloc] initWithFrame:CGRectMake(self.replayBtn.TKRight+1, self.replayBtn.TKTop, width, height)];
        _pauseBtn.backgroundColor=[UIColor clearColor];
        
        CGRect pathRect=CGRectMake(0, 0, width, height);
        UIBezierPath *path=[UIBezierPath bezierPathWithRoundedRect:pathRect byRoundingCorners:UIRectCornerBottomRight cornerRadii:CGSizeMake(8, 8)];
        //创建CAShapeLayer
        CAShapeLayer *shapeLayer=[CAShapeLayer layer];
        shapeLayer.fillColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.4f].CGColor;
        shapeLayer.path=path.CGPath;
        [_pauseBtn.layer addSublayer:shapeLayer];
        
        //创建富文本
        NSString *string=@" 暂停";
        NSMutableAttributedString *attri = [[NSMutableAttributedString alloc] initWithString:string];
        [attri addAttribute:NSForegroundColorAttributeName value:[UIColor whiteColor] range:NSMakeRange(0, string.length)];
        [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, string.length)];
                
        //NSTextAttachment可以将要插入的图片作为特殊字符处理
        NSTextAttachment *attch = [[NSTextAttachment alloc] init];
        //定义图片内容及位置和大小
        attch.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60030/tk_pause_btn", TK_OPEN_RESOURCE_NAME]];
        attch.bounds = CGRectMake(0, -2, 16, 16);
        //创建带有图片的富文本
        NSAttributedString *attrString = [NSAttributedString attributedStringWithAttachment:attch];
        //将图片放在第一位
        [attri insertAttributedString:attrString atIndex:0];
        [_pauseBtn setAttributedTitle:attri forState:UIControlStateNormal];
        [_pauseBtn addTarget:self action:@selector(pauseAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _pauseBtn;
}
@end
