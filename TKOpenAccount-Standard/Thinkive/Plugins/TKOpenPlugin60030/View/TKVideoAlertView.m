//
//  TKEXIDTimeoutView.m
//  HrSec
//
//  Created by <PERSON>ie on 2019/9/24.
//  Copyright © 2019 刘任朋. All rights reserved.
//

#import "TKVideoAlertView.h"
@interface TKVideoAlertView()
@property (nonatomic, strong) UIView *hud;//蒙版
@property (nonatomic, strong) UIView *tipBgView;//提示窗口背景视图
@property (nonatomic, strong) UIButton *onlyBtn;//只有一个按钮情况
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@end
@implementation TKVideoAlertView
/**
 *<AUTHOR> 2019年09月24日15:58:25
 *@初始化身份证OCR超时提示视图
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self=[super initWithFrame:frame];
    if (self) {
//        [self setBackgroundColor:[UIColor colorWithRed:128/255.0 green:128/255.0 blue:128/255.0 alpha:0.6]];
        self.requestParams=requestParams;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.requestParams[@"mainColor"]=nil;
        }
        [self viewInit];
    }
    return self;
}
/**
 <AUTHOR> 2019年09月24日15:58:32
 @初始化身份证OCR超时提示视图
 */
-(void)viewInit{
    [self addSubview:self.hud];
    [self addSubview:self.tipBgView];
    [self.tipBgView addSubview:self.titleLabel];
    [self.tipBgView addSubview:self.describeLabel];
    [self.tipBgView addSubview:self.cancelBtn];
    [self.tipBgView addSubview:self.takeBtn];
//    self.tipBgView.center=self.center;
}

//设置单独按钮文案，两个按钮就会隐藏
-(void)setOnlyBtnTitle:(NSString *)title{
    [self.onlyBtn setTitle:title forState:UIControlStateNormal];
    [self.tipBgView addSubview:self.onlyBtn];
    [self.cancelBtn removeFromSuperview];
    [self.takeBtn removeFromSuperview];
}
#pragma mark 按钮事件

-(void)cancelVideoBtnAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(cancelVideoBtnAction)]) {
        [self.delegate cancelVideoBtnAction];
    }
}

-(void)takeVideoBtnAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(takeVideoBtnAction)]) {
        [self.delegate takeVideoBtnAction];
    }
}

-(void)onlyVideoBtnAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(onlyVideoBtnAction)]) {
        [self.delegate onlyVideoBtnAction];
    }
}

#pragma mark lazyloading
- (UIView *)hud {
    if (!_hud) {
        _hud = [[UIView alloc] initWithFrame:self.bounds];
        _hud.backgroundColor = [UIColor colorWithRed:128/255.0 green:128/255.0 blue:128/255.0 alpha:0.6];
    }
    return _hud;
}

/**
 <AUTHOR> 2019年08月23日11:17:19
 @初始化懒加载提示窗口背景视图
 @return 提示窗口背景视图
 */
-(UIView *)tipBgView{
    if (!_tipBgView) {
        float width=300;
        float height;
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            height=251;
        }else{
            height=190;
        }
        float x = (self.TKWidth - width)/2;
        float y = (self.TKHeight - height)/2;
        _tipBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _tipBgView.backgroundColor=[UIColor whiteColor];
        _tipBgView.layer.cornerRadius=8.0f;
        _tipBgView.layer.masksToBounds=YES;
    }
    return _tipBgView;
}



/**
 <AUTHOR> 2019年09月07日13:20:27
 @初始化懒加载titleLabel
 @return titleLabel
 */
-(UILabel *)titleLabel{
    if (!_titleLabel) {
        float x=0;
        float y=24;
        float width=self.tipBgView.TKWidth;
        float height=26;
        _titleLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _titleLabel.textAlignment=NSTextAlignmentCenter;
        _titleLabel.text = @"视频见证提示";
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:24];
        }else{
            _titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
        }
        
        _titleLabel.textColor = [TKUIHelper colorWithHexString:@"#000000"];
        _titleLabel.backgroundColor=[UIColor whiteColor];
    }
    return _titleLabel;
}


/**
 <AUTHOR> 2019年09月24日19:16:45
 @初始化懒加载describeLabel
 @return describeLabel
 */
-(UITextView *)describeLabel{
    if (!_describeLabel) {
        float x=20;
        float height;
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            height=99;
        }else{
            height=42;
        }
        float width=self.tipBgView.TKWidth-2*x;
        float y=self.titleLabel.TKBottom+8;
        _describeLabel=[[UITextView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _describeLabel.text = @"自行退出视频见证将导致本次视频提交失败，请您等待坐席结束见证。";
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _describeLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _describeLabel.font = [UIFont fontWithName:@"PingFang SC" size:15];
        }
        _describeLabel.textAlignment=NSTextAlignmentCenter;
        _describeLabel.textColor = [TKUIHelper colorWithHexString:@"#666666"];
        _describeLabel.textContainerInset=UIEdgeInsetsMake(-3, -5, 0, -5);
        [_describeLabel setEditable:false];
        _describeLabel.backgroundColor=[UIColor whiteColor];
    }
    return _describeLabel;
}



/**
 <AUTHOR> 2019年09月24日19:37:43
 @初始化懒加载cancelBtn
 @return cancelBtn
 */
-(UIButton *)cancelBtn{
    if (!_cancelBtn) {

        float width=120;
        float height=40;
        float x=24;
        float y=self.tipBgView.TKHeight-height-24;
        _cancelBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_cancelBtn setTitle:@"执意退出" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _cancelBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:15];
        }
        
        if (self.requestParams[@"mainColor"]) {
            [_cancelBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]] forState:UIControlStateNormal];
            [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"] alpha:0.05f]];
        }else{
            [_cancelBtn setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
            [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
        }
        
        _cancelBtn.layer.cornerRadius=height/2.0f;
        [_cancelBtn addTarget:self action:@selector(cancelVideoBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelBtn;
}

/**
 <AUTHOR> 2019年09月24日19:37:51
 @初始化懒加载takeBtn
 @return continueBtn
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {

        float width=self.cancelBtn.TKWidth;
        float height=self.cancelBtn.TKHeight;
        float x=self.tipBgView.TKWidth-width-24;
        float y=self.cancelBtn.TKTop;
        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];


        if (self.requestParams[@"mainColor"]) {
            [_takeBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
        }else{
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = CGRectMake(0, 0, width, height);
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius=height/2.0f;
            [_takeBtn.layer addSublayer:btoGradientLayer]; //设置颜色渐变
        }

        [_takeBtn setTitle:@"继续见证" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _takeBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _takeBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:15];
        }
        
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _takeBtn.layer.cornerRadius=height/2.0f;
        [_takeBtn addTarget:self action:@selector(takeVideoBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _takeBtn;
}



/**
 <AUTHOR> 2019年09月24日19:37:51
 @初始化懒加载onlyBtn
 @return onlyBtn
 */
-(UIButton *)onlyBtn{
    if (!_onlyBtn) {

        float width=120;
        float height=40;
        float x=(self.tipBgView.TKWidth-width)/2.0f;
        float y=self.tipBgView.TKHeight-height-24;
        _onlyBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];


        if (self.requestParams[@"mainColor"]) {
            [_onlyBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
        }else{
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = CGRectMake(0, 0, width, height);
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius=height/2.0f;
            [_onlyBtn.layer addSublayer:btoGradientLayer]; //设置颜色渐变
        }

        [_onlyBtn setTitle:@"确定" forState:UIControlStateNormal];
        _onlyBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:15];
        [_onlyBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _onlyBtn.layer.cornerRadius=height/2.0f;
        [_onlyBtn addTarget:self action:@selector(onlyVideoBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _onlyBtn;
}

@end
