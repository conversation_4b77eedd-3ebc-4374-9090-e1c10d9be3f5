//
//  TKOrdinaryOneVideoView.m
//  OneWayVideo
//
//  Created by <PERSON>ie on 2019/4/3.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKOrdinaryOneVideoView.h"

//@interface TKOrdinaryOneVideoView()
//
//
//
//
//
////@property (nonatomic, readwrite, strong) UIView *badgeView; // 红点
////@property (nonatomic, assign) BOOL badgeViewHidden;//记录红点显示隐藏状态
//
//
//
//@end

@implementation TKOrdinaryOneVideoView


//-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
//    self=[super initWithFrame:frame];
//    if (self) {
//        self.requestParam=param;
//
//        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
//            self.mainColorString=@"#2772FE";
//        }else{
//            self.mainColorString=param[@"mainColor"];
//        }
//        [self viewInit];
//    }
//    return self;
//}
//
///**
// <AUTHOR> 2019年04月03日09:13:34
// @初始化单向视频默认界面
// */
//-(void)viewInit{
//    [super viewInit];
//    self.requestParam[@"readString"]=self.requestParam[@"readString"]?self.requestParam[@"readString"]:@"我自愿开立证券账户。";
//    self.colorWordsNum=0;
//}

/**
 <AUTHOR> 2019年04月03日09:13:34
 @初始化单向视频默认界面
 */
-(void)viewInit{

    [super viewInit];
    
    if (self.requestParam[@"isShowHeadRect"]&&[self.requestParam[@"isShowHeadRect"] integerValue] == 0) {
        //虚拟人目前不需要无头像框场景
        [self.boxImgBackgroundView setHidden:NO];
        [self.boxImgView setHidden:NO];
        [self.topView setHidden:NO];
        [self.bottomView setHidden:NO];
        [self.leftView setHidden:NO];
        [self.rightView setHidden:NO];
        
        [self.topBgView setHidden:NO];
        [self.bottomBgView setHidden:NO];
    }
}


#pragma mark 事件方法

///**
// @Auther Vie 2019年04月08日10:46:51
// @param sender 返回按钮点击事件
// */
//-(void)backAction:(UIButton *)sender{
//    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
//        [self.delegate goBack];
//    }
//}


///**
// @Auther Vie 2020年02月27日17:45:23
// @param sender 拍照事件
// */
//-(void)takeAction:(UIButton *)sender{
//    
//    // 开始录制之后，5秒内不可点击完成录制
//    [self enableFinishTakeRecord:NO];
//    //显示录制倒计时
//    _recordTimeLabel.hidden=NO;
//    
//    if (self.delegate&&[self.delegate respondsToSelector:@selector(takeRecord)]) {
//        [self.takeBtn removeFromSuperview];
//        [self addSubview:self.endBtn];
//        [self.delegate takeRecord];
//        [self showReadTextView];
//    }
//}


#pragma mark lazyloading

///**
// <AUTHOR> 2019年04月03日14:52:07
// @初始化懒加载recordTimeLabel
// @return recordTimeLabel
// */
//-(UILabel *)recordTimeLabel{
//    if (!_recordTimeLabel) {
//        float y=6+STATUSBAR_HEIGHT;
////        float width=96;
//        float width=75;
//        float height=32;
//        float x=(self.TKWidth-width)/2;
//        _recordTimeLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
//        _recordTimeLabel.text=@"00:00";
//        _recordTimeLabel.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
//        _recordTimeLabel.textAlignment=NSTextAlignmentCenter;
//        _recordTimeLabel.font = [UIFont fontWithName:@"PingFang SC" size:23];;
//        _recordTimeLabel.hidden=YES;
//    }
//    return _recordTimeLabel;
//}


///**
// <AUTHOR> 2020年02月28日10:50:22
// @初始化懒加载开始录制按钮
// @return 开始录制按钮
// */
//-(UIButton *)takeBtn{
//    if (!_takeBtn) {
//
//        float height=44;
//
//        float width=175;
//        float x=(self.TKWidth-width)/2.0f;
//
//        float gap=40;
//        //5s等小屏幕机型
//        if (UISCREEN_WIDTH==320) {
//            gap=10;
//        }
//        float y=self.TKHeight-height-gap-IPHONEX_BUTTOM_HEIGHT;
//        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
//
//        [_takeBtn setTitle:@"◉  开始录制" forState:UIControlStateNormal];
//        _takeBtn.layer.cornerRadius=height/2.0f;
//        [_takeBtn addTarget:self action:@selector(takeAction:) forControlEvents:UIControlEventTouchUpInside];
//        
//        _takeBtn.layer.borderWidth=1.0f;
//        // 默认一开始不可用
//        [self enableTakeRecord:NO];
//
//    }
//    return _takeBtn;
//}


@end
