//
//  TKOrdinaryOneVideoView.h
//  OneWayVideo
//  
//  Created by <PERSON>ie on 2019/4/3.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKBaseVideoRecordView.h"

//单向视频视图代理，按钮等事件
@protocol TKOrdinaryOneVideoViewDelegate <NSObject>


/**
 <AUTHOR> 2019年04月08日10:38:08
 @返回代理
 */
-(void)goBack;

/**
 <AUTHOR> 2020年02月27日20:14:49
 @开始录制代理
 */
-(void)takeRecord;

/**
 <AUTHOR> 2020年02月28日10:47:29
 @结束录制代理
 */
-(void)endRecord;



@end

@interface TKOrdinaryOneVideoView : TKBaseVideoRecordView
//@property (nonatomic, assign) CGRect boxRect;//人像取景框矩阵
@property (nonatomic, weak) id<TKOrdinaryOneVideoViewDelegate> delegate;
//@property (nonatomic, strong) UIView *avPreviewView;//视频预览视图

-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param;


/**
@Auther Vie 2020年02月28日12:48:01
@param recordTime 当前录制时间
@param longestTime 最长录制时间
*/
-(void)recordTime:(int)recordTime longestTime:(int)longestTime;


///**
//@Auther Vie 2020年03月10日14:43:37
//@param showWaitTip 中间准备提示文本
//*/
//-(void)showWaitTip:(NSString *)string;
//
///**
//@Auther Vie 2020年03月10日14:46:52
//@隐藏中间准备提示文本
//*/
//-(void)hideWaitTip;



/**
@Auther Vie 2021年03月08日13:19:01
@阅读文本引导
*/
-(void)readingGuideTip;



/**
<AUTHOR> 2021年03月12日173704
@最长录制时间倒计时
*/
- (void)recordLongTime:(int)recordTime startRecord:(BOOL)startRecord;

@end


