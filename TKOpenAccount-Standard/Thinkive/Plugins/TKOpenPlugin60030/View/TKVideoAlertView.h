//
//  TKVideoAlertView.h
//  HrSec
//  身份证OCR超时提示视图
//  Created by <PERSON>ie on 2019/9/24.
//  Copyright © 2019 刘任朋. All rights reserved.
//

@protocol TKVideoAlertViewDelegate <NSObject>

//取消按钮事件
-(void)cancelVideoBtnAction;

//继续按钮事件
-(void)takeVideoBtnAction;

//独立按钮事件
-(void)onlyVideoBtnAction;

@end



@interface TKVideoAlertView : UIView
@property (nonatomic, strong) UILabel *titleLabel;//提示标题文本
@property (nonatomic, strong) UITextView *describeLabel;//提示描述文本
@property (nonatomic, strong) UIButton *cancelBtn;//取消按钮
@property (nonatomic, strong) UIButton *takeBtn;//继续按钮
@property (nonatomic, weak) id<TKVideoAlertViewDelegate>delegate;
/**
 *<AUTHOR> 2019年09月24日15:58:25
 *@初始化身份证OCR超时提示视图
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams;

//设置单独按钮文案，两个按钮就会隐藏
-(void)setOnlyBtnTitle:(NSString *)title;
@end


