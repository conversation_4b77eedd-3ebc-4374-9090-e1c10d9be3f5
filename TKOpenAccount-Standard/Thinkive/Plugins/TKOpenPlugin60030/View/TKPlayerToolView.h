//
//  TKPlayerToolView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2022/11/10.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol TKPlayerToolViewDelegate <NSObject>

//重新播放按钮事件
-(void)replayVideoBtnAction;

//暂停播放按钮事件
-(void)pauseVideoBtnAction;



@end

@interface TKPlayerToolView : UIView

@property (nonatomic, weak) id<TKPlayerToolViewDelegate>delegate;

/**
 *<AUTHOR> 2019年09月24日15:58:25
 *@初始化身份证OCR超时提示视图
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams;

/**
@Auther Vie 2022年11月10日16:44:19
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
-(void)playTime:(float)currentTime longestTime:(float)longestTime;

/**
@Auther Vie 2022年11月10日16:54:16
@暂停播放
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
-(void)stopPlay:(float)currentTime longestTime:(float)longestTime;
@end


