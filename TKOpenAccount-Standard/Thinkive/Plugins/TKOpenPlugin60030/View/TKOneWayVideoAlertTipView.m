//
//  TKServerAlertTipView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2019/10/23.
//  Copyright © 2019 thinkive. All rights reserved.
//

#import "TKOneWayVideoAlertTipView.h"

@interface TKOneWayVideoAlertTipView()
@property (nonatomic, strong) UIView *alertBgView;//弹窗底色图
@property (nonatomic, strong) UILabel *titleLabel;//标题文本

@property (nonatomic, strong) UITextView *textView;//提示文本内容
@property (nonatomic, strong) NSString *tipString;//提示文本
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@end

@implementation TKOneWayVideoAlertTipView


/**
 *<AUTHOR> 2019年09月07日10:13:59
 *@初始化视频页面弹窗提示视图
 */
-(instancetype)initWithFrame:(CGRect)frame withTipString:(NSString *)string withParam:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        [self setBackgroundColor:[TKUIHelper colorWithHexString:@"#000000" alpha:0.3f]];
        self.requestParams=param;
        self.tipString=string;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.requestParams[@"mainColor"]=nil;
        }
        [self viewInit];
    }
    return self;
}

/**
 <AUTHOR> 2019年09月07日10:14:04
 @初始化视频页面弹窗提示视图
 */
-(void)viewInit{
    [self addSubview:self.alertBgView];
    
    [self.alertBgView addSubview:self.titleLabel];
    [self.alertBgView addSubview:self.textView];
    [self.alertBgView addSubview:self.cancelBtn];
    [self.alertBgView addSubview:self.continueBtn];
}



#pragma mark 按钮事件
-(void)cancelAction{
    [self removeFromSuperview];
    if (self.delegate&&[self.delegate respondsToSelector:@selector(cancelBtnClick)]) {
        [self.delegate cancelBtnClick];
    }
}

-(void)continueAction{
    [self removeFromSuperview];
    if (self.delegate&&[self.delegate respondsToSelector:@selector(uploadBtnClick)]) {
        [self.delegate uploadBtnClick];
    }
}


#pragma mark lazyloading
/**
 <AUTHOR> 2019年09月07日13:20:06
 @初始化懒加载alertBgView
 @return alertBgView
 */
-(UIView *)alertBgView{
    if (!_alertBgView) {
        float x=0;
        float y=0;
        float width=280;
        float height=59+68+self.textView.TKHeight;
        _alertBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _alertBgView.backgroundColor=[UIColor whiteColor];
        _alertBgView.layer.cornerRadius=8.0f;
        _alertBgView.layer.masksToBounds=YES;
        _alertBgView.center=self.center;
        
    }
    return _alertBgView;
}


/**
 <AUTHOR> 2021年03月11日11:14:15
 @初始化懒加载titleLabel
 @return titleLabel
 */
-(UILabel *)titleLabel{
    if (!_titleLabel) {
        float x=0;
        float y=25;
        float height=25;
        float width=self.alertBgView.TKWidth-2*x;
        _titleLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _titleLabel.textAlignment=NSTextAlignmentCenter;
        _titleLabel.text=@"请您确认";
        _titleLabel.numberOfLines=0;
        _titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
        _titleLabel.textColor=[TKUIHelper colorWithHexString:@"#333333"];
        _titleLabel.backgroundColor = UIColor.whiteColor;
    }
    return _titleLabel;
}

/**
 <AUTHOR> 2021年03月11日11:26:05
 @初始化懒加载textView
 @return textView
 */
-(UITextView *)textView{
    if (!_textView) {
        float x=20;
        float y=59;
//        float height=200;
        float width=280-2*x;
        _textView=[[UITextView alloc] initWithFrame:CGRectMake(x, y, width, 0)];
        _textView.textAlignment=NSTextAlignmentCenter;
        _textView.text=self.tipString;
        _textView.font = [UIFont fontWithName:@"PingFang SC" size:15];;
        _textView.textColor=[TKUIHelper colorWithHexString:@"#666666"];
        _textView.backgroundColor = UIColor.whiteColor;
         _textView.editable = NO;
         _textView.textAlignment=NSTextAlignmentCenter;
        CGSize size=[_textView sizeThatFits:CGSizeMake(width, MAXFLOAT)];
        if (size.height>200) {
            [_textView setFrameHeight:200];
        }else{
            [_textView setFrameHeight:size.height];
        }
    }
    return _textView;
}




/**
 <AUTHOR> 2019年09月07日13:22:18
 @初始化懒加载cancelBtn
 @return cancelBtn
 */
-(UIButton *)cancelBtn{
    if (!_cancelBtn) {
        float y=self.textView.TKBottom+8;
        float x=24;
        float height=40;
        float width=110;
        
        
        
        _cancelBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
        if (self.requestParams[@"mainColor"]) {
            [_cancelBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]] forState:UIControlStateNormal];
            [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"] alpha:0.05f]];
        }else{
            [_cancelBtn setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
            [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
        }

        
        
        _cancelBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
        [_cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
        _cancelBtn.layer.cornerRadius=height/2;
        

        [_cancelBtn addTarget:self action:@selector(cancelAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelBtn;
}

/**
 <AUTHOR> 2019年09月07日13:22:42
 @初始化懒加载continueBtn
 @return continueBtn
 */
-(UIButton *)continueBtn{
    if (!_continueBtn) {
        
        float y=self.cancelBtn.TKTop;
        float width=self.cancelBtn.TKWidth;
        float height=self.cancelBtn.TKHeight;
        float x=self.alertBgView.TKWidth-width-24;
        _continueBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];


        
        if (self.requestParams[@"mainColor"]) {
            [_continueBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
        }else{
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = CGRectMake(0, 0, width, height);
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius=height/2.0f;
            [_continueBtn.layer addSublayer:btoGradientLayer]; //设置颜色渐变
        }

    
        [_continueBtn setTitle:@"确定" forState:UIControlStateNormal];
        _continueBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
        [_continueBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _continueBtn.layer.cornerRadius=height/2.0f;
        
        [_continueBtn addTarget:self action:@selector(continueAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _continueBtn;
}
@end
