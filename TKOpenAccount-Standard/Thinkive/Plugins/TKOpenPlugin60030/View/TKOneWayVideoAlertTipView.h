//
//  TKServerAlertTipView.h
//  TKOpenAccount-Standard
//  视频客服弹窗提示框
//  Created by <PERSON><PERSON> on 2019/10/23.
//  Copyright © 2019 thinkive. All rights reserved.
//

@protocol TKOneWayVideoAlertTipViewDelegate <NSObject>
-(void)uploadBtnClick;//继续视频客服排队（针对视频接通时候单独处理退出视频再重新打开排队）

@optional
-(void)cancelBtnClick;//离开视频客服排队
@end


@interface TKOneWayVideoAlertTipView : UIView
@property (nonatomic, strong) UIButton *cancelBtn;//离开排队按钮
@property (nonatomic, strong) UIButton *continueBtn;//继续排队按钮
@property (nonatomic, weak) id<TKOneWayVideoAlertTipViewDelegate> delegate;

/**
 *<AUTHOR> 2019年09月07日10:13:59
 *@初始化视频页面弹窗提示视图
 */
-(instancetype)initWithFrame:(CGRect)frame withTipString:(NSString *)string withParam:(NSMutableDictionary *)param;

@end


