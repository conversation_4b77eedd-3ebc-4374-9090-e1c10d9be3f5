//
//  TKOpenTipView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/3/13.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKOpenTipView.h"

@interface TKOpenTipView()



@end

@implementation TKOpenTipView

#pragma mark - Init && Dealloc
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self addSubview:self.imageView];
        [self addSubview:self.label];
    }
    
    return self;
}

#pragma mark - Selector
- (void)updateUIWithImage:(NSString *)imageName title:(NSString *)title {
    self.imageView.image = [UIImage imageNamed:imageName];
    self.label.text = title;
    
    // 计算文本高度
    CGFloat height = [title boundingRectWithSize:CGSizeMake(self.label.frame.size.width, MAXFLOAT)
                        options: NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading
                     attributes:@{ NSFontAttributeName: self.label.font}
                        context:nil].size.height;
    if (height < self.TKHeight * 2) {
        // 只有1行
    } else {
        self.imageView.TKTop = 3; // 文本会多出边距。让图片往下移
        self.label.TKHeight = height;
        self.TKHeight = height;
    }
}

#pragma mark - Setter && Getter
- (UIImageView *)imageView {
    if (!_imageView) {
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, self.TKHeight, self.TKHeight)];
        _imageView = imageView;
        
    }
    return _imageView;
}

- (UILabel *)label {
    if (!_label) {
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(CGRectGetMaxX(self.imageView.frame) + 8, CGRectGetMinY(self.imageView.frame), self.bounds.size.width - CGRectGetMaxX(self.imageView.frame) - 8, self.imageView.frame.size.height)];
        label.font = [UIFont fontWithName:@"PingFangSC-Regular" size:16];
        label.textColor = [TKUIHelper colorWithHexString:@"333333"];
        label.numberOfLines = 0;
        label.lineBreakMode = NSLineBreakByCharWrapping;
        _label = label;
        
    }
    return _label;
}

@end
