//
//  TKOrdinaryOneVideoViewController.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/2/27.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKOpenAccountService.h"
#import "TKRecordManagerProtocol.h"
#import "TKVideoAlertView.h"
#import "TKBaseVideoRecordViewController.h"

@protocol TKOrdinaryOneVideoResultDelegate <NSObject>
//结果信息
- (void)tkOrdinaryOneWayVideoDidComplete:(NSMutableDictionary *)result;
@end

@interface TKOrdinaryOneVideoViewController : TKBaseVideoRecordViewController<TKRecordManagerDelegate, TKVideoAlertViewDelegate>
{
//    TKVideoAlertView *_videoAlertView;
}

/// 代理
@property (nonatomic, weak)   id<TKOrdinaryOneVideoResultDelegate>delegate;

//@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数

@property (nonatomic, strong) TKOpenAccountService *mService;   // 开户服务

//@property (nonatomic, readwrite, assign) BOOL isLandscape;   // 是否横屏录制

//@property (nonatomic, strong) UIView *avPreviewView;//视频预览视图

/// 构造方法
/// @param param 入参
-(instancetype)initWithParam:(NSMutableDictionary *)param;

/// 回调功能号
- (NSString *)pluginCallBackfuncNo;

/// 回调h5或者原生代码
/// @param callJsParam 回调参数
- (void)sendCallBack:(NSMutableDictionary *)callJsParam;

// 解决子类编译警告
- (void)goBack;
- (void)takeRecord;
- (void)endGoBack;
- (void)endReste;
- (void)endSubmit;
- (void)playVideo;
- (void)replayVideo;
@end


