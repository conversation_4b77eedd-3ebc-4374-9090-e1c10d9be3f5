//
//  TKOrdinaryOneVideoViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/2/27.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKOrdinaryOneVideoViewController.h"
#import "TKOrdinaryOneVideoView.h"
#import "TKOrdinaryOneVideoEndView.h"
#import <MediaPlayer/MediaPlayer.h>
#import "TKFaceDetectManager.h"
#import "TKOneWayVideoAlertTipView.h"
#import "TKPlayer.h"
#import "TKVideoRecordManager.h"
#import "TKZFPlayerController.h"
#import "TKZFPortraitViewController.h"

@interface TKOrdinaryOneVideoViewController ()<TKOrdinaryOneVideoViewDelegate,TKBaseVideoRecordEndViewDelegate,TKFaceDetectManagerDelegate,TKOneWayVideoAlertTipViewDelegate, TKPlayerDelegate>


//@property (nonatomic, readwrite, strong) NSObject<TKRecordManagerProtocol> * _Nonnull recordManager; // 人脸检测管理者
//@property (nonatomic, assign) BOOL isRecording;//是否正在录制
//@property (nonatomic, assign) CGFloat videoWidth;//视频宽度
//@property (nonatomic, assign) CGFloat videoHeight;//视频高度

//// 录制工具
//@property (nonatomic, strong) TKPlayer *player; //播放器

//@property (nonatomic, assign) BOOL isPlay;//是否正在预览视频
//@property (nonatomic, assign) BOOL isAlertViewShow;//是否已有弹窗层
//@property (nonatomic, assign) CFAbsoluteTime recordStartTime;//视频开始录制时间
//@property (nonatomic, strong) NSString *recordStartDateString;//格式化的视频开始录制时间字符串
//@property (nonatomic, strong) NSString *videoLength;//视频录制时长（秒）

//@property(nonatomic, assign) BOOL isEndOneWayVideo;//是否走完了流程
//@property (nonatomic, assign) int shortestTime;//最短录制时间
//@property (nonatomic, assign) int longestTime;//最长录制时间

//@property (nonatomic, strong) TKOrdinaryOneVideoView *oneVideoView;//普通单向视频页面

//@property (nonatomic, strong) TKOrdinaryOneVideoEndView *tkOneEndView;//单向视频结果页
//@property (nonatomic, assign) BOOL isGetImg;//是否截取过照片
//@property (nonatomic, strong) UIImage *videoGetImg;//视频录制中获取的照片
//@property (nonatomic, strong) AVPlayer *voicePlayer;//按钮响应语音播放
//@property (nonatomic, readwrite, strong) TKFaceDetectManager *faceDetectManager; // 人脸检测管理者
//@property (nonatomic, strong) TKOneWayVideoAlertTipView *alertTipView;//弹窗提示

//@property (nonatomic, readwrite, assign) int faceDetectFailureCount; // 最大人脸检测失败次数
//@property (nonatomic, readwrite, assign) int faceCompareFailureCount; // 最大人脸比对失败次数
//@property (nonatomic, readwrite, assign) int totalFaceDetectFailureCount; // 最大人脸检测失败次数
//@property (nonatomic, readwrite, assign) int totalFaceCompareFailureCount; // 最大人脸比对失败次数


//@property (nonatomic, readwrite, assign) BOOL isClickTakeRecordBtn; // 是否点击了录制按钮

//@property (nonatomic, readwrite, strong) NSDateFormatter * _Nullable formatter; // 时间格式
//@property (nonatomic, readwrite, copy) NSString * _Nullable fullVideoFileURLString; // 完整的文件路径
//@property (nonatomic, strong) NSString * _Nullable recordEndDateString;//格式化的视频结束录制时间字符串

//@property (nonatomic, readwrite, strong) TKPlayerControlView *playerControlView;    //播放视频工具视图
@property (nonatomic, readwrite, assign) NSTimeInterval statisticEventStartTime;    // 总事件初始时间
@property (nonatomic, readwrite, assign) BOOL recordSuccess;    // 是否录制成功

@end

@implementation TKOrdinaryOneVideoViewController


//-(instancetype)initWithParam:(NSMutableDictionary *)param{
//    self=[super init];
//    if (self) {
//        self.requestParam=param;
//        self.mService=[[TKOpenAccountService alloc] init];
//    }
//    return self;
//}

//- (UIInterfaceOrientationMask)supportedInterfaceOrientations
//{
//    return UIInterfaceOrientationMaskPortrait;
//}
//
//- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
//{
//    return UIInterfaceOrientationPortrait;
//}
//
//- (BOOL)shouldAutorotate
//{
//    return NO;
//}

- (void)viewDidLoad {
    self.isReadVideo=YES;
    
    [super viewDidLoad];
//    self.isResetParentStatusBarStyle=YES;
//    self.statusBarStyle = UIStatusBarStyleLightContent;
//    // 删除原有的视频。这是防止异常退出没有清除视频
//    [self deleteLocalVideoFile:[self getLocalOneWayVideoPath]];
//    
//    self.view.backgroundColor = [UIColor grayColor];
//    
//    // 在框通过允许录制
////    [self.oneVideoView enableTakeRecord:YES];
////    self.requestParam[@"shortestTime"] = @"3";
//    
//    self.isEndOneWayVideo = NO;
////    self.shortestTime = [self.requestParam[@"shortestTime"] intValue];
////    self.longestTime = [self.requestParam[@"longestTime"] intValue];
//    self.totalFaceDetectFailureCount = [TKStringHelper isEmpty:self.requestParam[@"totalFaceDetectFailureCount"]] ? 3 : [self.requestParam[@"totalFaceDetectFailureCount"] intValue];
//    self.totalFaceCompareFailureCount = [TKStringHelper isEmpty:self.requestParam[@"totalFaceCompareFailureCount"]] ? 3 : [self.requestParam[@"totalFaceCompareFailureCount"] intValue];
//    [self.view addSubview:self.avPreviewView];
//    [self.view addSubview:self.tkOneView];
//    
//    // 横屏时旋转录制页面
//    if (self.isLandscape) {
//
////        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
////        self.oneVideoView.transform = transform;
////        self.oneVideoView.frame = CGRectMake(0, 0, self.oneVideoView.TKWidth, self.oneVideoView.TKHeight);
//        
//        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
//        self.view.transform = transform;
//        self.view.frame = CGRectMake(0, 0, self.view.TKWidth, self.view.TKHeight);
//    }
//    
//    // 启动音视频采集设备
//   [self bootDevice:YES];
}

//-(void)viewWillAppear:(BOOL)animated{
//    [super viewWillAppear:animated];
//
//    // 录制
//    [self activeDevice];
//}

//-(void)viewDidAppear:(BOOL)animated{
//    [super viewDidAppear:animated];
//    
//    [TKCommonUtil autoHeadsetState];
////    //监听系统音量
////    [[AVAudioSession sharedInstance] addObserver:self forKeyPath:@"outputVolume" options:NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld context:(void *)[AVAudioSession sharedInstance]];
////    //App进入后台监听
////    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(enterBackground:) name:UIApplicationWillResignActiveNotification object:nil];
//    //耳机状态获取的通知
////    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(audioRouteChangeListenerCallback:)  name:AVAudioSessionRouteChangeNotification object:nil];
////    [self.tkOneView enableTakeRecord:NO]; // 播放提示音的时候不可点击
//    
//    // 播放语音提示
//    [self handleAudioPlay:@"tk_oneVideo_remind" withType:@"mp3" sourceDirectory:@"Resources/TKOpenPlugin60030"];
//    [self recordTime:0 longestTime:self.longestTime startRecord:NO];
//}


//-(void)viewWillDisappear:(BOOL)animated{
//    [super viewWillDisappear:animated];
////    [[AVAudioSession sharedInstance] removeObserver:self forKeyPath:@"outputVolume" context:(void *)[AVAudioSession sharedInstance]];
////    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVAudioSessionRouteChangeNotification object:nil];
//
//    // 停止录制
//    [self deactiveDevice];
//}

#pragma mark 框架通知监听
//- (NSArray *)listNotificationInter
//{
//    NSMutableArray *notes = [[super listNotificationInter] mutableCopy];
//    [notes addObject:NOTE_NETWORK_CHANGE];//监听网络变换
//    return notes;
//}
//
//- (void)handleNotification:(NSNotification *)notification
//{
//    if ([notification.name isEqualToString:NOTE_NETWORK_CHANGE])
//    {
//        Network_Type networkType=[TKNetHelper getNetworkType];
//        //断网或者2g退出，将错误信息给h5
//        if (networkType==Network_No||networkType==Network_2G) {
//            dispatch_async(dispatch_get_main_queue(), ^{
//                [self exitProcess];
//            });
//
//            [self failActionCallJsWith:@"-9" errorMsg:@"网络不稳定，请切换网络再试"];
//        }
//
//    }
//    else
//    {
//        [super handleNotification:notification];
//    }
//}

//#pragma mark - KVO
//- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
//
//    if(context == (__bridge void *)[AVAudioSession sharedInstance] && [keyPath isEqualToString:@"outputVolume"]) {
//        float newVolume = [[change objectForKey:@"new"] floatValue];
//        float oldVolume = [[change objectForKey:@"old"] floatValue];
//
////        NSLog(@"volumeChangeNotification volumn = %.2f, oldValue = %.2f", newVolume, oldVolume);
//
//        //最小音量调整支持h5控制
//        float deviceMinVolume=TKSmartOpenVolume;
//        if (self.requestParam[@"deviceMinVolume"]) {
//            deviceMinVolume=[self.requestParam[@"deviceMinVolume"] intValue]/100.00f;
//        }
//        if ((newVolume+0.05)  < deviceMinVolume) {
//            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                //直接调整音量api还能使用，先改音量不提示用户
//                MPMusicPlayerController *mp=[MPMusicPlayerController applicationMusicPlayer];
//                mp.volume=deviceMinVolume;//0为最小1为最大
//            });
//        }
//    }
//}

#pragma mark 事件方法
///**
//*  <AUTHOR> 2018-06-09 15:13:50
//*  监听耳机是否插入
//*  @param notif
//*/
//-(void)audioRouteChangeListenerCallback:(NSNotification *)notif{
//
//    NSDictionary *interuptionDict = notif.userInfo;
//         NSInteger routeChangeReason   = [[interuptionDict
//             valueForKey:AVAudioSessionRouteChangeReasonKey] integerValue];
//    if (routeChangeReason==AVAudioSessionRouteChangeReasonNewDeviceAvailable) {
//
//         [TKCommonUtil autoHeadsetState];
//    }else
//
//
//    [self.voicePlayer play];
//
//    if (_player) {
//        //视频回放中插拔耳机行为都暂停播放
//        dispatch_async(dispatch_get_main_queue(), ^{
//            self.isPlay=NO;
//            [self.player pause];
//            [self.tkOneEndView changePlayStatus:self.isPlay];
//
//        });
//    }
//}

     
///**
// *  <AUTHOR> 2018-06-09 15:13:50
// *  标记App进入到后台
// *  @param notif
// */
//-(void)enterBackground:(NSNotification *)notif{
//    if (!self.isRecording) {
//        return;
//    }
//
//    dispatch_async(dispatch_get_main_queue(), ^{
//        //先停止预览视频
//        [self.player pause];
//        [self exitProcess];
//    });
//
//    [self failActionCallJsWith:@"-2" errorMsg:@"视频过程中请勿切换App或锁屏"];
//}

- (void)sendCallBack:(NSMutableDictionary *)callJsParam
{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(tkOrdinaryOneWayVideoDidComplete:)]) {
       [self.delegate tkOrdinaryOneWayVideoDidComplete:callJsParam];
    }else{
       [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
    }
}


//-(void)volumeChange{
//    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
//    [[AVAudioSession sharedInstance] setActive:YES error:nil];
//
//    CGFloat volume = audioSession.outputVolume;
//    //最小音量调整支持h5控制
//    float deviceMinVolume=TKSmartOpenVolume;
//    if (self.requestParam[@"deviceMinVolume"]) {
//        deviceMinVolume=[self.requestParam[@"deviceMinVolume"] intValue]/100.00f;
//    }
//    if ((volume+0.05)<deviceMinVolume) {
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            //直接调整音量api还能使用，先改音量不提示用户
//            MPMusicPlayerController *mp=[MPMusicPlayerController applicationMusicPlayer];
//            mp.volume=deviceMinVolume;//0为最小1为最大
//        });
//    }
//}

//- (void)startRecordingAction {
//    [self.recordManager startRecord];
//}
//
//- (void)stopRecordingAction {
//    [self.recordManager stopRecord];
//}


//-(NSString *)getLocalOneWayVideoPath {
//    return [NSHomeDirectory() stringByAppendingFormat:@"/Documents/%@.mp4",TK_ONE_WAY_TEMP_MOVE_NAME];
//}

///**
// <AUTHOR> 2019年04月08日19:31:25
// @重新开始单向视频流程
// */
//-(void)restartOneVideo{
//    self.statusBarStyle = UIStatusBarStyleLightContent;
//    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
//    [[AVAudioSession sharedInstance] setActive:YES error:nil];
//    
//    CGFloat volume = audioSession.outputVolume;
//    TKLogInfo(@"系统音量:%f", volume);
//    
//    TKLogInfo(@"TKSmartOneVideo:重新开始单向视频流程");
//    [self stopRecordingAction];
//    
//    // 停止视频预览
//    if (self.player) {
//        [self.player stop];
//    }
//
//    self.isPlay=NO;
////    self.playerLayer=nil;
////    self.player=nil;
//    self.isClickTakeRecordBtn = NO;
//    
//    // 删除原有的视频。这是防止异常退出没有清除视频
//    [self deleteLocalVideoFile:[self getLocalOneWayVideoPath]];
//    
//    // 重置UI
//    [self.tkOneEndView removeFromSuperview];
//    self.tkOneEndView=nil;
//    [self.tkOneView removeFromSuperview];
//    self.tkOneView=nil;
//    [self.view addSubview:self.tkOneView];
////    [self.tkOneEndView changePlayStatus:self.isPlay];
////    [self.tkOneView enableTakeRecord:NO]; // 播放提示音的时候不可点击
//    
//    // 重新连接视频服务器
//    [self bootDevice:NO];
//    
//    self.isEndOneWayVideo = NO;
//    
//    // 播放语音提示
//    [self handleAudioPlay:@"tk_oneVideo_remind" withType:@"mp3" sourceDirectory:@"Resources/TKOpenPlugin60030"];
//    
//    [self recordTime:0 longestTime:self.longestTime startRecord:NO];
//    
//    // 默认不能点击
//    [self.tkOneView enableTakeRecord:NO];
//}


///**
// <AUTHOR> 2019年04月15日15:59:58
// @退出界面流程
// */
//-(void)exitProcess{
//    TKLogInfo(@"TKSmartOneVideo:退出界面流程");
//    self.isPlay=!self.isPlay;
//    if (!self.isPlay) {
//        [self.player pause];
//        
//    }
//    
//    // 非本地录制，删除原有的视频;本地录制不能删除，返回地址供外层下载的逻辑，删除了文件，外层没法下载
//    if (!([self.requestParam getIntWithKey:@"resultType"] == 2))
//        [self deleteLocalVideoFile:[self getLocalOneWayVideoPath]];
//    
//    [self stopRecordingAction];
//    //关闭屏幕常亮
//    [UIApplication sharedApplication].idleTimerDisabled = NO;
//    [self dismissViewControllerAnimated:YES completion:nil];
//    
//    // 关闭人脸检测
//    [self.faceDetectManager stopFaceDetect];
//}

///**
// <AUTHOR> 2020年02月28日13:23:19
// @录制计时
// */
//-(void)recordStatus{
//    if (self.isRecording) {
//        CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
//        int seconds=currentTime-self.recordStartTime;
//        
//        // 显示录制时间,显示开始录制
//        [self recordTime:seconds longestTime:self.longestTime startRecord:YES];
//        
//        if (seconds<self.longestTime) {
//            [self performSelector:@selector(recordStatus) withObject:nil afterDelay:1.0f];
//        }else{
//            [self endRecord];
//        }
//    }
//}

//- (void)recordTime:(int)recordTime longestTime:(int)longestTime startRecord:(BOOL)startRecord;
//{
//    if (!self.isLandscape) {
//        
//        if ([self.tkOneView respondsToSelector:@selector(recordTime:longestTime:)]) {
//            
//            [self.tkOneView recordTime:recordTime longestTime:longestTime]; // 显示最长录制时间时间
//        }
//    } else {
//        if ([self.tkOneView respondsToSelector:@selector(recordLongTime:startRecord:)]) {
//            
//            [self.tkOneView recordLongTime:recordTime startRecord:startRecord];
//        }
//    }
//}

///**
// <AUTHOR> 2019年05月26日12:41:59
// @人脸在框检测失败超过3次就退出给h5处理页面
// */
//-(void)moreMaxFaceDetectFailAction:(NSString *)errorMsg{
//    dispatch_async(dispatch_get_main_queue(), ^{
//        [self exitProcess];
//    });
//
//    [self failActionCallJsWith:@"-3" errorMsg:errorMsg];
//}

///**
// <AUTHOR> 2019年05月26日12:41:59
// @人脸比对失败超过3次就退出给h5处理页面
// */
//-(void)moreMaxFaceCompareFailAction:(NSString *)errorMsg{
//    dispatch_async(dispatch_get_main_queue(), ^{
//        [self exitProcess];
//    });
//
//    [self failActionCallJsWith:@"-4" errorMsg:errorMsg];
//}

//- (void)bootDevice:(BOOL)isFirst {
//    
//    [self.recordManager bootDevcie:isFirst];
//}

//- (void)stopDevice:(BOOL)isRelease{
//    [self.recordManager stopDevice:isRelease];
//}


//- (void)activeDevice {
//    if ([self.recordManager respondsToSelector:@selector(activeDevice)]) {
//        [self.recordManager activeDevice];
//    }
//}

//- (void)deactiveDevice {
//    if ([self.recordManager respondsToSelector:@selector(deactiveDevice)]) {
//        [self.recordManager deactiveDevice];
//    }
//}

//- (void)detectFace:(UIImage *)image {
//    [self.faceDetectManager detectFace:image];
//}

//- (void)handleRecordDidStart
//{
//    // 展示录制时间信息
//    [self.tkOneView hideWaitTip];
//    
//    self.isRecording = YES;
//    
//    self.recordStartTime = CFAbsoluteTimeGetCurrent();
//    //----------将nsdate按formatter格式转成nsstring
//    self.recordStartDateString = [self.formatter stringFromDate:[NSDate date]];
//    
//    self.isGetImg=NO;
//    [self.tkOneView readingGuideTip];
//    
//    // 录制过程中持续继续人脸检测失败次数
//    [self.faceDetectManager startRecordFailCount];
//    
//    [self performSelector:@selector(recordStatus) withObject:nil afterDelay:0.0f];
//}

//- (void)handleRecordDidSuccess:(NSString *)filePath fullFilePath:(NSString *)fullFilePath videoLenth:(int)videoLenth
//{
//    self.isRecording = NO;
//    
//    self.fullVideoFileURLString = [TKStringHelper encodeURL:fullFilePath];
//    self.videoLength = [NSString stringWithFormat:@"%i", videoLenth / 1000];
//    self.recordEndDateString = [self.formatter stringFromDate:[NSDate date]];
//    
//    
//    TKLogInfo(@"思迪录制日志:视频时长%@", self.videoLength);
//    TKLogInfo(@"预览日志----视频地址%@", [self fullVideoFileURLString]);
//}
//
//- (void)handleRecordDidFail:(NSString *)errorMsg
//{
//    [self failActionCallJsWith:@"-8" errorMsg:errorMsg];
//}
//
//- (void)handleNetworkError:(NSString *_Nullable)errorMsg {
//    // @"网络不稳定，请切换网络再试"
//    [self failActionCallJsWith:@"-9" errorMsg:errorMsg];
//}

///**
// <AUTHOR> 2019年05月26日12:41:59
// @失败次数超过指定次数就退出给h5处理页面
// */
//- (void)failActionCallJsWith:(NSString *)errorNo errorMsg:(NSString *)errorMsg
//{
//    [self exitProcess];
//
//    NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
//    callJsParam[@"funcNo"] = [self pluginCallBackfuncNo];
//    callJsParam[@"error_no"] = errorNo;
//    callJsParam[@"error_info"] = errorMsg;
//    [callJsParam setObject:[TKStringHelper isNotEmpty:self.requestParam[@"tkuuid"]] ? self.requestParam[@"tkuuid"] : @"" forKey:@"tkuuid"];
//
//    [self sendCallBack:callJsParam];
//}

//- (void)handleBootDeviceDidFail:(NSString *)errorMsg
//{
//    [self failActionCallJsWith:@"-5" errorMsg:errorMsg];
//}
//
//- (void)handleDeviceRunFail:(NSString *)errorMsg
//{
//    [self failActionCallJsWith:@"-5" errorMsg:errorMsg];
//}

//- (void)deleteLocalVideoFile:(NSString *)localFilePath
//{
//    NSFileManager *fileManager = [NSFileManager defaultManager];
//    BOOL fileExists = [fileManager fileExistsAtPath:localFilePath];
//
//    if (fileExists == NO) {
//        TKLogInfo(@"预览视频文件不存在，无需删除");
//        return;
//    }
//
//    // 删除原有的视频
//    NSError *error = nil;
//    [fileManager removeItemAtPath:localFilePath error:&error];
//    if (error) {
//        TKLogInfo(@"删除预览视频文件出错%@", error.description);
////                return;
//    } else {
//        fileExists = NO;
//        TKLogInfo(@"预览视频文件删除成功");
//    }
//}


//- (void)stopAndJumpToFailurePage:(TKFaceErrorType)faceErrorType errorMsg:(NSString *)errorMsg
//{
//    [self.tkOneView enableFinishTakeRecord:NO]; // 防止在做动画的时候，用户点击完成录制导致展示的页面出错。
//    [self stopRecordingAction];
//    [self.tkOneEndView setFrameX:self.view.frame.size.width];
//    self.tkOneEndView.failureString = errorMsg;
//    self.tkOneEndView.endType = faceErrorType == TKFaceErrorTypeFaceDetect ? TKOneWayVideoEndTypeFaceDetectError : TKOneWayVideoEndTypeFaceCompareError; // 和下面的情况是枚举和错误信息不一样
//    [self.view addSubview:self.tkOneEndView];
//    // 结束人脸在框检测
//    [self.faceDetectManager stopFaceDetect];
//
//    [self animateToResultPage];
//}

//- (void)animateToResultPage
//{
//    [self.tkOneEndView setFrameX:self.view.frame.size.width];
//    [self.view addSubview:self.tkOneEndView];
//    [UIView animateWithDuration:0.3f animations:^{
//        [self.tkOneEndView setFrameX:self.view.frame.origin.x];
//    } completion:^(BOOL finished) {
//        self.statusBarStyle = TKUIStatusBarStyleDefault;
//    }];
//}


//#pragma mark 播放本地mp3处理
//
//- (void)handleAudioPlay:(NSString*)voiceName withType:(NSString*)type
//{
//    [self handleAudioPlay:voiceName withType:type sourceDirectory:@"Resources/TKOpenPlugin60006"];
//}
//
///**
// <AUTHOR> 2020年03月07日17:54:19
// @播放本地语音文件
// */
//- (void)handleAudioPlay:(NSString*)voiceName withType:(NSString*)type sourceDirectory:(NSString *)sourceDirectory {
//    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
//    NSURL *aUrl = nil;
//    NSString* fPath = [bundle pathForResource:voiceName ofType:type inDirectory:sourceDirectory];
//
//    NSFileManager *fm = [[NSFileManager alloc] init];
//
//    if ([fm fileExistsAtPath:fPath]) {
//        aUrl = [NSURL fileURLWithPath:fPath];
//    }
//    AVPlayerItem * songItem = [[AVPlayerItem alloc]initWithURL:aUrl];
//    self.voicePlayer = [[AVPlayer alloc]initWithPlayerItem:songItem];
//    [self.voicePlayer play];
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(moviePlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.voicePlayer.currentItem];
//}
//
////音频播放完毕
//- (void)moviePlayDidEnd:(NSNotification*)notification {
//    dispatch_async(dispatch_get_main_queue(), ^{
//
//        if (self.isClickTakeRecordBtn == NO) {
//
//            // 创建定时器抓取图片做人脸检测
//            [self.faceDetectManager startFaceDetect];
//        }
//    });
//}


#pragma mark - TKFaceDetectManagerDelegate
//- (void)faceDetectDidComplete:(TKFacePassStatus)facePassStatus faceCompareStatus:(TKFaceCompareStatus)faceCompareStatus facNumberPassStatus:(TKSingleFaceStatus)facNumberPassStatus errorMsg:(NSString *)errorMsg {
//
////    // 未开始录制前，默认不可录制
////    if (self.isRecording == NO) {
////        [self.tkOneView enableTakeRecord:NO];
////    }
//
//    // 检查人脸数量情况
//    if (facNumberPassStatus == TKSingleFaceStatusNotSingle) {
//        [self.tkOneView liveWarning:errorMsg];
//        return;
//    }
//
//    // 检验人脸在框情况
//    if (facePassStatus == TKFacePassStatusNotPass) {
//        [self.tkOneView liveWarning:errorMsg];
//        return;
//    } else {
//        // 在框通过允许录制
//        [self.tkOneView enableTakeRecord:YES];
//    }
//
//    // 检查人脸比对情况
//    if (faceCompareStatus == TKFaceCompareStatusNotPass) {
//        [self.tkOneView liveWarning:errorMsg forceDisplay:YES];
//        return;
//    }
//
//    // 通过情况隐藏提示
//    [self.tkOneView liveWarning:nil];
//}
 
///// 人脸检测失败回调
//- (void)faceDetectDidFail:(NSString *)errorMsg {
//    [self.tkOneView liveWarning:errorMsg];
//}

///// 失败次数超限
//- (void)faceDetectDidExceedLimit:(TKFaceErrorType)faceErrorType errorMsg:(NSString *)errorMsg {
//    
//    // 如果在收到回调前已经完成录制，不做处理
//    if (self.isEndOneWayVideo == YES) return;
//
//    // 标记已经完成录制
//    self.isEndOneWayVideo = YES;
//    
//    NSString *isRejectToH5=[NSString stringWithFormat:@"%@",self.requestParam[@"isRejectToH5"]];
//    if ([isRejectToH5 isEqualToString:@"1"]) {
//        
//        // h5处理报错逻辑
//        if (faceErrorType == TKFaceErrorTypeFaceDetect) {
//            [self moreMaxFaceDetectFailAction:errorMsg];
//        } else {
//            [self moreMaxFaceCompareFailAction:errorMsg];
//        }
//        
//    } else {
//        // 原生处理错误次数超限逻辑
//        if (faceErrorType == TKFaceErrorTypeFaceDetect) {
//            
//            self.faceDetectFailureCount++;
//            if (self.faceDetectFailureCount >= self.totalFaceDetectFailureCount) { // 超过最大次数
//                // 次数超限，返回给h5
////                [self moreMaxFaceDetectFailAction:@"由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。"];
//                [self moreMaxFaceDetectFailAction:errorMsg];
//            } else {
//
//                // 跳到失败页面
//                [self stopAndJumpToFailurePage:faceErrorType errorMsg:errorMsg];
//            }
//        } else {
//            
//            self.faceCompareFailureCount++;
//            if (self.faceCompareFailureCount >= self.totalFaceCompareFailureCount) { // 超过最大次数
//                // 次数超限，返回给h5
////                [self moreMaxFaceCompareFailAction:@"由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。"];
//                [self moreMaxFaceDetectFailAction:errorMsg];
//            } else {
//                // 跳到失败页面
//                [self stopAndJumpToFailurePage:faceErrorType errorMsg:errorMsg];
//            }
//        }
//    }
//}



#pragma mark TKOrdinaryOneVideoEndViewDelegate
///**
// <AUTHOR> 2019年04月13日14:18:27
// @单向视频结果页返回
// */
//-(void)endGoBack{
//    [self exitProcess];
//    
//    [self failActionCallJsWith:@"-1" errorMsg:@"客户主动返回"];
//}

///**
// <AUTHOR> 2019年04月13日14:19:09
// @单向视频结果页播放视频
// */
//-(void)playVideo{
//      dispatch_async(dispatch_get_main_queue(), ^{
//         TKLogInfo(@"TKSmartOneVideo:查看录制视频");
//
//         self.isPlay=!self.isPlay;
//        [self.tkOneEndView changePlayStatus:self.isPlay];
//         if (self.isPlay) {
//
//             if(self.player.playState==TKPlayerPlayStatePlayStopped){
//                 [self.player replay];
//             }else{
//                 [self.player play];
//             }
//
//
//         }else{
//             [self.player pause];
//             [self.tkOneEndView stopPlay:([self.videoLength floatValue]-[self.tkOneEndView.secondsString floatValue]) longestTime:[self.videoLength floatValue]];
//             self.tkOneEndView.secondsString = self.tkOneEndView.secondsString;
//         }
//     });
//}


///**
// <AUTHOR> 2022年11月11日13:49:11
// @单向视频结果页重新播放视频
// */
//-(void)replayVideo{
//
//    [self.player replay];
//    self.isPlay=YES;
//    [self.tkOneEndView changePlayStatus:self.isPlay];
//    self.tkOneEndView.secondsString = self.videoLength;
//    [self.tkOneEndView stopPlay:([self.videoLength floatValue]-[self.tkOneEndView.secondsString floatValue]) longestTime:[self.videoLength floatValue]];
//}

///**
// <AUTHOR> 2019年04月13日14:20:28
// @单向视频结果页重试
// */
//-(void)endReste{
//    [self restartOneVideo];
//}


///**
// <AUTHOR> 2019年04月13日14:21:40
// @单向视频结果页提交
// */
//-(void)endSubmit{
//    dispatch_async(dispatch_get_main_queue(), ^{
//        
//        //先停止预览视频
//        [self.player pause];
//        
//        //是否需要提示上传确认
//        if ([TKStringHelper isEmpty:self.requestParam[@"uploadTipString"]]) {
//            [self uploadBtnClick];
//        }else{
//            [self.view addSubview:self.alertTipView];
//
//        }
//    });
//}

//#pragma mark - TKPlayerDelegate
///// 开始加载资源回调
//- (void)tkPlayerPlay:(TKPlayer *)player didStartLoadUrl:(NSURL *)url {
//
//}
//
///**
// @视频播放完成通知
// */
//- (void)tkPlayerPlayDidEnd: (TKPlayer *)player {
//    self.isPlay=NO;
//    [self.tkOneEndView changePlayStatus:NO];
//    self.tkOneEndView.secondsString = self.videoLength;
//    [self.tkOneEndView stopPlay:([self.videoLength floatValue]-[self.tkOneEndView.secondsString floatValue])longestTime:[self.videoLength floatValue]];
//}
//
///// 播放状态更新
//- (void)tkPlayerPlay:(TKPlayer *)player didUpdateStatus:(AVPlayerStatus)stauts {
//
//}
//
//- (void)tkPlayerPlay:(TKPlayer *)player didUpdatePlayTime:(NSTimeInterval)time {
//    if(self.player.isPlaying){
//        // 当前播放总时间
//        float playCountdown = [self.videoLength floatValue] - time;
//        self.tkOneEndView.secondsString = [NSString stringWithFormat:@"%f",(playCountdown > 0 ? playCountdown : 0)];
//        [self.tkOneEndView playTime:time longestTime:player.totalTime];
//    }
//}
//
//- (void)tkPlayerLoadResourceFail:(TKPlayer *)player url:(nonnull NSURL *)url {
//    
////    [self.tkOneEndView showLoadingVideo:NO];
//    
//    __weak typeof(self) weakSelf = self;
//    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"温馨提示" message:@"视频播放失败，请重试" preferredStyle:UIAlertControllerStyleAlert];
//    // 1.创建并添加按钮
//    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"重试" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//      
//        [weakSelf.player reloadPlayer];
//    }];
//    // 2.创建并添加按钮
//    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
//      
//    }];
//    [alertController addAction:okAction];
//    [alertController addAction:cancelAction];
//    [self presentViewController:alertController animated:YES completion:nil];
//}
//

#pragma mark - TKOneWayVideoAlertTipViewDelegate
//继续视频客服排队（针对视频接通时候单独处理退出视频再重新打开排队）
-(void)uploadBtnClick{
    
    // 埋点-单向_提交
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    NSTimeInterval startTime = [[NSDate date] timeIntervalSince1970];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoSubmit
                             progress:TKPrivateEventProgressStart
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    // 埋点-单向_结果_成功
    eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = [NSString stringWithFormat:@"0"];;
    eventDic[@"event_err"] = @"";
    eventDic[@"costTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - self.statisticEventStartTime) * 1000] ; // 单位毫秒
    TKPrivateEventResult result = TKPrivateEventResultSuccess;
//    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventNone
                             progress:TKPrivateEventProgressEnd
                               result:result
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    

    
    NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
    callJsParam[@"funcNo"]=[self pluginCallBackfuncNo];
    callJsParam[@"error_no"]=@"0";
    callJsParam[@"start_time"] = self.recordStartDateString;
    callJsParam[@"video_length"]=self.videoLength;
    

    // ----------设置你想要的格式,hh与HH的区别:分别表示12小时制,24小时制
    //现在时间,你可以输出来看下是什么格式
    //----------将nsdate按formatter格式转成nsstring
    callJsParam[@"end_time"]= self.recordEndDateString;
    if ([self.requestParam[@"resultType"] isEqualToString:@"2"]) {
        //给文件路径
        callJsParam[@"filePath"] = self.fullVideoFileURLString;
    }else{
        NSString *tempBase64=[TKBase64Helper stringWithEncodeBase64Data:[NSData dataWithContentsOfFile:self.fullVideoFileURLString]];
        
        callJsParam[@"videoBase64"]=[NSString stringWithFormat:@"data:video/mp4;base64,%@", tempBase64];
    }
    
    // 增加签名
    callJsParam[@"videoSign"] = [NSString stringWithFormat:@"%@", [TKCommonUtil signOneWayVideo:self.fullVideoFileURLString]];

    [self sendCallBack:callJsParam];
    
    // 结束单向
    [self exitProcess];
}


#pragma mark TKOrdinaryOneVideoViewDelegate
///**
// <AUTHOR> 2019年04月08日10:38:08
// @返回代理
// */
//- (void)goBack{
//    [self exitProcess];
//
//    [self failActionCallJsWith:@"-1" errorMsg:@"客户主动返回"];
//}

///**
// <AUTHOR> 2020年02月27日20:14:49
// @开始录制代理
// */
//-(void)takeRecord{
//    
//    self.isClickTakeRecordBtn = YES;
//    
//    [self.tkOneView showWaitTip:@"开始"];
//    [self handleAudioPlay:@"tk_oneVideo_start" withType:@"mp3"];
//    self.recordStartTime=0;
//    //延迟，让开始语音播放完再录制
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        
//        [self startRecordingAction];
//    });
//}

///**
// <AUTHOR> 2020年02月28日10:47:29
// @结束录制代理
// */
//-(void)endRecord{
//
//    CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
//    int seconds=currentTime-self.recordStartTime;
//    if (seconds<self.shortestTime||self.recordStartTime==0) {
//
//        TKLayerView *layerView=[[TKLayerView alloc] initContentView:self.rootWindow withBtnTextColor:nil];
//        [layerView showTip:[NSString stringWithFormat:@"录制时间不得少于%d秒",self.shortestTime] position:TKLayerPosition_Center];
//
//    }else{
//
//        // 如果在收到回调前已经完成录制，不做处理。 否则可能存在完成录制时间和人脸检测不通过时间先后到达导致UI异常的问题
//        if (self.isEndOneWayVideo == YES) return;
//
//        // 标记已经完成录制
//        self.isEndOneWayVideo = YES;
//
//        [self stopRecordingAction];
//        [self.tkOneView showWaitTip:@"结束"];
//        [self handleAudioPlay:@"tk_oneVideo_end" withType:@"mp3"];
//
//        //延迟，让开始语音播放完跳转页面
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [self.tkOneView hideWaitTip];
//
//            self.tkOneEndView.secondsString=[NSString stringWithFormat:@"%d秒",seconds];
//             self.tkOneEndView.videoShowImg=self.videoGetImg;
//            [self.tkOneEndView setFrameX:self.view.frame.size.width];
//            [self.tkOneEndView updateTipViewWithTipArr:[self.faceDetectManager getFaceDetectTipArray]];
//
//            // 这里有个坑，需要先updateTipViewWithTipArr再加载url。因为结果页播放图层提前加载，updateTipViewWithTipArr会移除掉播放图层
//            // 视频录制结束才抓取url图片
//            self.player.contentView = self.tkOneEndView.videoShowImgView; // 每次重新录制都是新创建view.因此要重新设置
//
//
//            self.player.url = [NSURL fileURLWithPath:self.fullVideoFileURLString];
//            //视频时长
//            self.tkOneEndView.secondsString = self.videoLength;
//
//            [self.view addSubview:self.tkOneEndView];
//            [self animateToResultPage];
//
//            // 结束人脸在框检测
//            [self.faceDetectManager stopFaceDetect];
//        });
//
//    }
//}



#pragma mark - TKVideoRecordManagerDelegate
///// 连接视频服务器回调
//- (void)connectServerRoomDidComplete:(BOOL)success errorMsg:(NSString *)errorMsg {
//
//    if (success) {
//
//        TKLogInfo(@"思迪录制日志：设备初始化成功，开始录制");
//
//    } else {
//
//        [self handleBootDeviceDidFail:errorMsg];
//    }
//}
//
///// 断开视频服务器回调
//- (void)disconnectServerRoomDidComplete:(BOOL)success errorMsg:(NSString *)errorMsg {
//    TKLogInfo(@"思迪录制日志：视频录制异常退出 errorMsg=%@", errorMsg);
//    [self handleDeviceRunFail:errorMsg];
//}

///// 开始录制回调
//- (void)recordStartCallBack {
//
//    [self handleRecordDidStart];
//}

///// 结束录制回调
//- (void)recordStopCallBack:(NSString *)filePath fullFilePath:(NSString *)fullFilePath videoLenth:(int)videoLenth catonLength:(int)catonLength{
//
//    [self handleRecordDidSuccess:filePath fullFilePath:fullFilePath videoLenth:videoLenth];
//}

///// 结束录制出错回调
//- (void)recordStopErrocCallBack:(NSString *)errorMsg {
//    TKLogInfo(@"思迪录制日志：视频录制异常退出 errorMsg=%@", errorMsg);
//    [self handleRecordDidFail:errorMsg];
//}

///// 视频流回调
//- (void)OnVideoDataCallBack:(UIImage *)image {
//
//    [self detectFace:image];
//}


//#pragma mark TKPlayerControlViewViewDelegate
////重新播放按钮事件
//- (void)replayVideoBtnAction{
//
//    [self replayVideo];
//}
//
////暂停播放按钮事件
//- (void)pauseVideoBtnAction{
//
//    [self playVideo];
//}
//
//- (void)playAction {
//    [self playVideo];
//}


#pragma mark lazyloading
///**
// <AUTHOR> 2019年03月02日10:39:21
// @懒加载初始化视频预览视图
// @return UIView
// */
//-(UIView *)avPreviewView{
//    if (!_avPreviewView) {
//        _avPreviewView=[[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
//        [_avPreviewView setBackgroundColor:[UIColor grayColor]];
////        [_avPreviewView.layer addSublayer:self.avPreviewLayer];
//        
//    }
//    return _avPreviewView;
//}


///**
// <AUTHOR> 2019年04月13日17:30:09
// @初始化懒加载视频播放器
// @return 视频播放器
// */
//- (TKPlayer *)player{
//    if (!_player) {
//        _player =  [TKPlayer playerWithURL:nil contentView:nil];
//        _player.delegate = self;
////        _player.speakerPlayback = YES;
//        _player.isInvertLayer = NO;
//        _player.shouldAutoPlay = NO;
//        _player.timeRefreshInterval=0.1;
//        if (!([self.requestParam getIntWithKey:@"resultType"] == 2))
//            _player.downLoadFileFullPath = [self getLocalOneWayVideoPath]; // TKPlayer最后会把文件删掉，本地录制返回地址供外层下载的需求会冲突
//
//        NSString *loadRemoteVideoMaxTime = [self.requestParam getStringWithKey:@"loadRemoteVideoMaxTime"];
//        if ([TKStringHelper isNotEmpty:loadRemoteVideoMaxTime]) {
//            _player.loadRemoteVideoMaxTime = loadRemoteVideoMaxTime.intValue;
//        }
//
//        NSString *disableDownload = [self.requestParam getStringWithKey:@"disableDownload"];
//        _player.disableDownload = [disableDownload isEqualToString:@"1"];
//        _player.disableDownload = YES;
//
//    }
//    return _player;
//}


///// 服务端视频录制工具类
//- (NSObject<TKRecordManagerProtocol> *)recordManager {
//    if (!_recordManager) {
//        _recordManager = [[TKVideoRecordManager alloc] initWithConfig:self.requestParam];
//        _recordManager.delegate = self;
////        super.recordManager.contentView = self.tkOneView.avPreviewView;
//        _recordManager.contentView = self.avPreviewView;
//        _recordManager.videoFilePath = [self getLocalOneWayVideoPath];
//        _recordManager.isLandscape = self.isLandscape;
//    }
//    return _recordManager;
//}


///**
// <AUTHOR> 2019年04月03日10:09:41
// @初始化懒加载单向视频界面
// @return 单向视频界面
// */
//-(TKOrdinaryOneVideoView *)oneVideoView{
//    if (!_oneVideoView) {
//        _oneVideoView=[[TKOrdinaryOneVideoView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
//        _oneVideoView.delegate=self;
//        _oneVideoView.avPreviewView = self.avPreviewView;
//    }
//    return _oneVideoView;
//}

/**
 <AUTHOR> 2019年04月03日10:09:41
 @初始化懒加载单向视频界面
 @return 单向视频界面
 */
-(UIView<TKBaseVideoRecordViewProtocol> *)tkOneView{
    if (!super.tkOneView) {
        self.requestParam[@"isReadVideo"]=self.isReadVideo?@"1":@"0";
        super.tkOneView = [[TKOrdinaryOneVideoView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        super.tkOneView.delegate = self;
        super.tkOneView.isReadVideo=self.isReadVideo;
    }
    return super.tkOneView;
}

/**
 <AUTHOR>
 @初始化单向视频结果页面
 */
- (UIView<TKBaseVideoRecordEndViewProtocol>  *)tkOneEndView{
    if (!super.tkOneEndView) {
        super.tkOneEndView = [[TKOrdinaryOneVideoEndView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        super.tkOneEndView.delegate = self;
    }
    return super.tkOneEndView;
}


/// 视频录制工具类
- (NSObject<TKRecordManagerProtocol> *)recordManager {
    if (!super.recordManager) {
        super.recordManager = [[TKVideoRecordManager alloc] initWithConfig:self.requestParam];
        super.recordManager.delegate = self;
//        super.recordManager.contentView = self.tkOneView.avPreviewView;
        super.recordManager.contentView = self.avPreviewView;
        super.recordManager.videoFilePath = [self getLocalOneWayVideoPath];
        super.recordManager.isLandscape=self.isLandscape;
    }
    return super.recordManager;
}


///**
// <AUTHOR>
// @初始化单向视频结果页面
// */
//-(TKOrdinaryOneVideoEndView *)tkOneEndView{
//    if (!_tkOneEndView) {
//        _tkOneEndView=[[TKOrdinaryOneVideoEndView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
//        _tkOneEndView.delegate=self;
//    }
//    return _tkOneEndView;
//}

///// 人脸在框检测工具类
//- (TKFaceDetectManager *)faceDetectManager {
//    if (!_faceDetectManager) {
//        _faceDetectManager = [[TKFaceDetectManager alloc] initWithConfig:self.requestParam];
//        _faceDetectManager.delegate = self;
//        _faceDetectManager.targetRect = self.oneVideoView.boxRect;
//    }
//    return _faceDetectManager;
//}

///**
// <AUTHOR> 2019年10月23日13:10:48
// @初始化懒加载alertTipView
// @return alertTipView
// */
//-(TKOneWayVideoAlertTipView *)alertTipView{
//    if (!_alertTipView) {
//        CGRect rect = CGRectMake(self.view.TKTop, self.view.TKLeft, MIN(UISCREEN_WIDTH, UISCREEN_HEIGHT), MAX(UISCREEN_WIDTH, UISCREEN_HEIGHT));
//        if (self.isLandscape) rect= CGRectMake(self.view.TKTop, self.view.TKLeft, MAX(UISCREEN_WIDTH, UISCREEN_HEIGHT), MIN(UISCREEN_WIDTH, UISCREEN_HEIGHT));
//        _alertTipView=[[TKOneWayVideoAlertTipView alloc] initWithFrame:rect withTipString:self.requestParam[@"uploadTipString"] withParam:self.requestParam];
//        _alertTipView.delegate=self;
//    }
//    return _alertTipView;
//}

- (NSString *)pluginCallBackfuncNo {
    return @"60031";
}

//- (NSDateFormatter *)formatter {
//    if (_formatter == nil) {
//        _formatter = [[NSDateFormatter alloc] init];
//        _formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
//        // ----------设置你想要的格式,hh与HH的区别:分别表示12小时制,24小时制
//        [_formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
//    }
//    return _formatter;
//}

- (NSDictionary *)pluginCallBackfuncNoToTKPrivateOneWayVideoType
{
    // 由于没有记录插件号，这里用插件回调号曲线救国
    return @{
        @"60031" : @(TKPrivateOneWayVideoTypeNormal),
        @"60027" : @(TKPrivateOneWayVideoTypeLocalSmart),
        @"60058" : @(TKPrivateOneWayVideoTypeTChatSmart),
        @"60073" : @(TKPrivateOneWayVideoTypeTChatDigitalMan),
        @"60092" : @(TKPrivateOneWayVideoTypeNormal),
        @"60078" : @(TKPrivateOneWayVideoTypeLocalSmart),
        @"60088" : @(TKPrivateOneWayVideoTypeTChatSmart),
    };
}


/// 播放视频工具视图
//- (TKZFPortraitControlView *)playerControlView{
//    if (!_playerControlView) {
//
//        _playerControlView = [[TKZFPortraitControlView alloc] initWithFrame:self.tkOneEndView.videoShowImgView.frame withParam:self.requestParam];
//        _playerControlView.delegate = self;
//    }
//    return _playerControlView;
//}

//- (TKZFPlayerController *)playerController {
//    if (!_playerController) {
//
//        _playerController = [TKZFPlayerController playerWithPlayerManager:self.player containerView:self.tkOneEndView.videoShowImgView];
//        _playerController.controlView = self.playerControlView;
//        
//        /// 播放完成
//        @tkzf_weakify(self)
//        self.playerController.playerDidToEnd = ^(id  _Nonnull asset) {
//            @tkzf_strongify(self)
//                            
//            [self.player seekToTime:0 completionHandler:nil];
////            [self.playerControlView resetControlView];
//            [self.playerControlView.portraitControlView playBtnSelectedState:NO];   // 默认播放，这里要修改为暂停状态
//            [self.playerControlView.landScapeControlView playBtnSelectedState:NO];  // 默认播放，这里要修改为暂停状态
////            [self.playerController stop];
//        };
//        
//        self.playerController.playerPrepareToPlay = ^(id<TKZFPlayerMediaPlayback>  _Nonnull asset, NSURL * _Nonnull assetURL) {
//            @tkzf_strongify(self)
//            self.playerController.disableGestureTypes = TKZFPlayerDisableGestureTypesNone;
//        };
//        
//        self.playerController.playerPlayFailed = ^(id<TKZFPlayerMediaPlayback>  _Nonnull asset, id  _Nonnull error) {
//            @tkzf_strongify(self)
//            self.playerController.disableGestureTypes = TKZFPlayerDisableGestureTypesAll;
//        };
//        
//        // 横屏时旋转预览全屏页面
//        if (self.isLandscape) {
////            self.playerController.orientationObserver.portraitViewController.disablePortraitGestureTypes = TKZFDisablePortraitGestureTypesTap; // 禁用单击收起全屏手势
//            self.playerController.orientationObserver.portraitViewController.fullScreenSize = CGSizeMake(UISCREEN_HEIGHT, UISCREEN_WIDTH);
//            self.playerController.orientationObserver.portraitFullScreenMode = TKZFPortraitFullScreenModeScaleToFill;
//            self.playerController.orientationObserver.presentationSize = CGSizeMake(UISCREEN_HEIGHT, UISCREEN_WIDTH);
//            self.playerController.orientationObserver.portraitViewController.fullScreenAnimation = NO;
//            self.playerController.orientationWillChange = ^(TKZFPlayerController * _Nonnull player, BOOL isFullScreen) {
//                @tkzf_strongify(self)
//
//                if (isFullScreen) {
//                    CGFloat angle = (90.0f * M_PI) / 180.0f; // 旋转90度，顺时针方向
//                    self.playerController.orientationObserver.portraitViewController.view.transform = CGAffineTransformIdentity; // 先重置偏移
//                    CGAffineTransform transform = CGAffineTransformMakeRotation(angle);
//                    self.playerController.orientationObserver.portraitViewController.view.transform = transform;
//                    self.playerController.orientationObserver.portraitViewController.view.frame = CGRectMake(0, 0, MIN(self.view.TKWidth, self.view.TKHeight), MAX(self.view.TKWidth, self.view.TKHeight));
//                }
//            };
//        };
//        
//        
//    }
//    return _playerController;
//}


@end
