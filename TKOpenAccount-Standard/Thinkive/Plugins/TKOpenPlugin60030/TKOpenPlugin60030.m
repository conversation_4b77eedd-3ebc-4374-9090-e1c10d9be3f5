//
//  TKOpenPlugin60030.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/2/19.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKOpenPlugin60030.h"
#import "TKOrdinaryOneVideoViewController.h"
#import <MediaPlayer/MediaPlayer.h>


@interface TKOpenPlugin60030()<TKOrdinaryOneVideoResultDelegate>

@property (nonatomic, readwrite, assign) BOOL isInvokeing; // 是否正在被调用
@property (nonatomic, readwrite, strong) NSDictionary *param;

@end

@implementation TKOpenPlugin60030
- (ResultVo *)serverInvoke:(id)param{
    
    ResultVo *resultVo = [[ResultVo alloc]init];
//    param[@"mainColor"]=@"#FD671A";
    self.param = param;
    
    // 埋点-单向调用
//    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
//                         subEventName:TKPrivateSubEventNone
//                             progress:TKPrivateEventProgressStart
//                               result:TKPrivateEventResultNone
//                          orientation:TKPrivateVideoOrientationPortrait
//                      oneWayVideoType:TKPrivateOneWayVideoTypeNormal
//                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
//                             eventDic:self.param];

     dispatch_async(dispatch_get_main_queue(), ^{
         
         //是否在调用插件前展示介绍页面
         if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
             [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Camera),@(TKAuthorizationType_Audio)] authCallBacks:nil btnCallBack:^{
                 [self.currentViewCtrl tkIsMicrophonePermissions:^{
                                 
                     [self.currentViewCtrl tkIsCameraPermissions:^{
                         [self handleAuthorized:param];
                     }];
                                         
                 }];
             }];
         }else{
             [self.currentViewCtrl tkIsMicrophonePermissions:^{
                             
                 [self.currentViewCtrl tkIsCameraPermissions:^{
                     [self handleAuthorized:param];
                 }];
                                     
             }];

         }
         
     });
    
    
    return resultVo;
}

- (void)handleAuthorized:(id)param
{
    // 已在调用，拦截重复调用
    if (self.isInvokeing == YES) {
//        TKLogInfo(@"静音检查，拦截重复调用");
        return ;
    };
    
    // 标记正在调用
    self.isInvokeing = YES;
            
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [[AVAudioSession sharedInstance] setActive:YES error:nil];
                                            
    CGFloat volume = audioSession.outputVolume;
    //默认音量调整支持h5控制
    float defaultVolume=TKSmartOpenVolume;
    if (param[@"defaultVolume"]) {
        defaultVolume=[param[@"defaultVolume"] intValue]/100.00f;
    }
    if ((volume+0.05)<defaultVolume) {
        //直接调整音量api还能使用，先改音量不提示用户
        MPMusicPlayerController *mp=[MPMusicPlayerController applicationMusicPlayer];
        mp.volume=defaultVolume;//0为最小1为最大
    }

    //屏幕常亮
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    TKOrdinaryOneVideoViewController *viewCtr=[[TKOrdinaryOneVideoViewController alloc] initWithParam:param];
    viewCtr.delegate=self;
    [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:viewCtr animated:YES completion:nil];

                    
}

//结果信息
-(void)tkOrdinaryOneWayVideoDidComplete:(NSMutableDictionary *)result{
    [self iosCallJsWithParam:result];
    
    // 重置标志位
    self.isInvokeing = NO;
}


@end
