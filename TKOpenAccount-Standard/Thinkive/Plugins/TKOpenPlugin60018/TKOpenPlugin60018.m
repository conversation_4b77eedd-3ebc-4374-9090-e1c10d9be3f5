//
//  TKOpenPlugin60018.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 16/6/3.
//  Copyright © 2016年 thinkive. All rights reserved.
//

#import "TKOpenPlugin60018.h"

@interface TKOpenPlugin60018()<TKMBProgressHUDDelegate>{
    
    ResultVo *resultVo;
    
}

@end

@implementation TKOpenPlugin60018

- (ResultVo *)serverInvoke:(id)param{
    
    resultVo = [[ResultVo alloc]init];
    
    if (param == nil || param[@"copyText"] == nil) {
        
        resultVo.errorNo = -6001801;
        
        resultVo.errorInfo = @"数据有误";
        
    }else{
    
        dispatch_async(dispatch_get_main_queue(), ^{
            [self copyContent:param[@"copyText"]];
            
        });
    
    }
    
    return resultVo;
    
}

#pragma mark -复制内容到黏贴版
- (void)copyContent:(NSString*)content{
    
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    pasteboard.string = content;
    
    TKMBProgressHUD *mHUD = [[TKMBProgressHUD alloc] initWithWindow:self.rootWindow];
    [self.currentViewCtrl.view addSubview:mHUD];
    UIImage *image = [UIImage imageNamed:@"TKOpenResource.bundle/Resources/TKOpenGeneral/37x-Checkmark.png"];
    UIImageView *imageView = [[UIImageView alloc] initWithImage:image];
    mHUD.customView = imageView;
    mHUD.mode = TKMBProgressHUDModeCustomView;
    mHUD.labelText = @"复制完成";
    mHUD.delegate = self;
    [mHUD show:YES];
    [mHUD hide:YES afterDelay:2.0];
}


#pragma mark -implement MBProgressHUDDelegate
- (void)hudWasHidden:(TKMBProgressHUD *)hud {
    // Remove HUD from screen when the HUD was hidded
    [hud removeFromSuperview];
    
}

@end
