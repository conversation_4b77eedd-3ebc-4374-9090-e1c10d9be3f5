//
//  TKOpenPlugin60017.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2016/7/21.
//  Copyright © 2016年 thinkive. All rights reserved.
//

#import "TKOpenPlugin60017.h"

@implementation TKOpenPlugin60017

-(ResultVo *)serverInvoke:(id)param
{
    NSMutableDictionary *paramMap = (NSMutableDictionary *)param;
    
    NSString *base64Str = paramMap[@"base64"];
    
//    NSString *fileName = paramMap[@"fileName"];
    
    ResultVo *resultVo = [[ResultVo alloc]init];
    
    if([TKStringHelper isEmpty:base64Str])
    {
        resultVo.errorInfo = @"base64不能为空!";
        resultVo.errorNo = -6001701;
        return resultVo;
    }
    
    //    if([TKStringHelper isEmpty:fileName])
    //    {
    //        resultVo.errorInfo = @"文件名不能为空";
    //        resultVo.errorNo = -6001402;
    //        return resultVo;
    //    }
    
    NSArray *arr = [base64Str componentsSeparatedByString:@","];
    
    if (arr && [arr count] >= 2) {
        
        base64Str = arr[1];
        
    }else{
        
        resultVo.errorInfo = @"base64数据异常!";
        resultVo.errorNo = -6001702;
        return resultVo;
        
    }
    
    NSData *imageData =  [TKBase64Helper dataWithDecodeBase64String:base64Str];
    
    UIImage *qrImage = [UIImage imageWithData:imageData];
    
    if(!qrImage)
    {
        resultVo.errorInfo = @"无法解析base64";
        resultVo.errorNo = -6001703;
        return resultVo;
    }
    
    UIImageWriteToSavedPhotosAlbum(qrImage, self, @selector(imageSavedToPhotosAlbum:didFinishSavingWithError:contextInfo:), nil);
    
    return resultVo;
}

- (void)imageSavedToPhotosAlbum:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if(error)
        {
            UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"无法访问相册,请在 设置-隐私-照片 打开权限." preferredStyle:UIAlertControllerStyleAlert];
            // 2.创建并添加按钮
            UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                    #ifdef __IPHONE_8_0
                    [TKSystemHelper openAppURLStr:UIApplicationOpenSettingsURLString completionHandler:nil];
                    #endif
            }];
            // 2.创建并添加按钮
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
                
            }];
            [alertController addAction:okAction];
            [alertController addAction:cancelAction];
            [self.currentViewCtrl presentViewController:alertController animated:YES completion:nil];
            return;
        }

        
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"图片已成功保存到相册!" preferredStyle:UIAlertControllerStyleAlert];
        // 2.创建并添加按钮
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
       
        [alertController addAction:okAction];
        [self.currentViewCtrl presentViewController:alertController animated:YES completion:nil];
    });
}




@end
