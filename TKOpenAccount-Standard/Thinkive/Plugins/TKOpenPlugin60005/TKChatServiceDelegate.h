//
//  TKChatServiceDelegate.h
//  TKchat_AC
//
//  Created by chensj on 14-4-18.
//  Copyright (c) 2014年 chensj. All rights reserved.
//

#import <Foundation/Foundation.h>

@protocol TKChatServiceDelegate <NSObject>

//请求排队返回，当前位置
-(void)chatServiceRequestToOrderBack:(int)loc error:(NSString*)e;

//获取见证地址
-(void)chatServiceGetVideoIP:(NSString*)ip port:(int)port roomId:(NSString *)roomId extParam:(NSString*)eParam error:(NSString*)e;

//用户校检
-(void)chatServiceValidPass:(BOOL)isPass error:(NSString*)e;

//////以下为轮询用
//是否到自己见证
-(void)chatServiceIsMyTurn:(BOOL)isMe error:(NSString*)e staff_tips:(NSString *)staff_tips;

//网点排队人数
-(void)chatServiceOrgOrderCount:(int)pcount error:(NSString*)e;
//请求座席状态返回
-(void)chatServiceRequestBranchStateBack:(int)loc error:(NSString *)e;

-(void)onRequestZybh:(NSString *)zybh AndKfh:(NSString *)kfh;

-(void)handleRequestException:(NSString*)errorNo errorDescription:(NSString*)eMsg;

-(void)requestCallBack:(NSString*)rFunc withResult:(id)rResult;
@end
