//
//  TKChatService.m
//  TKchat_AC
//
//  Created by chensj on 14-4-18.
//  Copyright (c) 2014年 chensj. All rights reserved.
//

#import "TKChatService.h"
#import <Foundation/Foundation.h>
#import "TKOpenAccountService.h"

//Class object_getClass(id object);

@interface TKChatService()
{
    id iParams;
    
    TKOpenAccountService *oService;

}
@end

@implementation TKChatService
@synthesize delegate,host;

-(id)initWithDelegate:(id<TKChatServiceDelegate>)dele host:(NSString*)url {
    if (self = [super init]) {
        self.delegate = dele;
        self.host = url;
    }
    return self;
}

-(id)initWithDelegate:(id<TKChatServiceDelegate>)dele host:(NSString*)url withParams:(id)params{
    if (self = [super init]) {
        self.delegate = dele;
        self.host = url;
        iParams = params;
        
        if (!oService) {
            oService = [[TKOpenAccountService alloc] init];
        }
        
    }
    return self;
}


//客户端请求关闭视频
-(void)requestEndVideoByUserId:(NSString*)userId{
    
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501596";
    
    tkReqParam[@"user_id"]=userId;
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        int ret = 0;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *data = [dic objectForKey:@"flag"];
            
            ret = [@"true" isEqualToString:data];

            
        }else{
            
        }
        
        if (ret) {
            
            TKLogInfo(@"客户端退出视频见证成功");
        }else{
            
//            TKLogInfo(@"客户端退出视频见证失败");
        }
    }];

}


//客户端取消排队
-(void)requestEndOrder:(NSString*)userId orgId:(NSString*)orgId{
    
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501597";
    
    tkReqParam[@"user_id"]=userId;
    
    tkReqParam[@"org_id"]=@"orgId";
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        int ret = 0;
        [self cancelAllRequest];
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *data = [dic objectForKey:@"flag"];
            
            ret = [@"true" isEqualToString:data];
            
        }else{
            
        }
        
        if (ret) {
            
            TKLogInfo(@"取消排队成功");
        }else{
        
//            TKLogInfo(@"取消排队失败");
        }
    }];

}

//请求排队
-(void)requestForToOrder:(NSString*)userId nickName:(NSString*)nName orgId:(NSString*)oid level:(NSString*)lv origin:(NSString*)ori{
    
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501570";
    
    tkReqParam[@"user_id"]=userId;
    
    tkReqParam[@"nick_name"]=nName;
    
    tkReqParam[@"org_id"]=oid;
    
    tkReqParam[@"level"]=lv;
    
    tkReqParam[@"origin"]=ori;
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *data = [dic objectForKey:@"location"];
            
            int ret = [data intValue]+1;
        
            if ([delegate respondsToSelector:@selector(chatServiceRequestToOrderBack:error:)]) {
                [delegate chatServiceRequestToOrderBack:ret error:nil];
            }
            
        }else{
            
            if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                
                [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
            }
            
        }
    }];

}

//请求见证地址
-(void)requestForVideoServerByUserId:(NSString*)userId{

    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501585";
    
    tkReqParam[@"user_id"]=userId;
    
    tkReqParam[@"user_type"]=@"1";
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *data = [dic objectForKey:@"conn_str"];
            
//            NSArray *arrTemp = [data componentsSeparatedByString:@":"];
            
            //针对ipv6和ipv4不同情况处理
            NSArray *arrTemp;
            if ([data rangeOfString:@"]"].location == NSNotFound) {
                //ipv4地址
                arrTemp=[data componentsSeparatedByString:@":"];
            }else{
                //是ipv6的地址有[]情况
                NSArray *tempArray=[data componentsSeparatedByString:@"]"];
                NSString *tempIp;
                if ([@"tchat" isEqualToString:iParams[@"videoType"]] || (iParams[@"videoType"] && [iParams[@"videoType"] integerValue] == 0)) {
                    //tchat使用ipv6需要[]
                    tempIp=[NSString stringWithFormat:@"%@]",tempArray[0]];
                }else{
                    //anychat使用ipv6不需要[]
                    tempIp=[tempArray[0] stringByReplacingOccurrencesOfString:@"["withString:@""];
                }
                NSArray *portRoomArray=[tempArray[1] componentsSeparatedByString:@":"];
                NSString *tempPort=portRoomArray[1];
                NSString *tempRoom=portRoomArray[2];
                arrTemp=[[NSArray alloc] initWithObjects:tempIp,tempPort,tempRoom, nil];
            }
            
            NSString* ip, *roomId,*errMsg = nil, *eParam = nil;
            
            int port = 0;
            
            if (arrTemp.count >= 3) {
                
                ip = [arrTemp objectAtIndex:0];
                
                port = [[arrTemp objectAtIndex:1] intValue];
                
                roomId = [NSString stringWithFormat:@"tk%@",[arrTemp objectAtIndex:2]];
                
                if (iParams) {
                    
                    if (iParams[@"securitiesName"]) {
                        
                        if ([iParams[@"securitiesName"] isEqualToString:@"dongfang"]) {
                            
                            NSString *tempStr = [arrTemp objectAtIndex:2];
                            
                            if (tempStr) {
                                
                                NSArray *arr = [data componentsSeparatedByString:@"-"];
                                
                                if (arr && [arr count] >= 2) {
                                    
                                    roomId = [NSString stringWithFormat:@"2%@%@", [[arr objectAtIndex:0] substringFromIndex:[[arr objectAtIndex:0 ] length]-3] ,[arr objectAtIndex:1]];
                                    
                                }
                                
                            }
                            
                        }else if ([iParams[@"securitiesName"] isEqualToString:@"lianxun"]) {
                            
                            roomId = [NSString stringWithFormat:@"%@",[arrTemp objectAtIndex:2]];
                            
                        }else if ([iParams[@"securitiesName"] isEqualToString:@"huaxi"]) {
                        
                            if (arrTemp.count > 3) {
                                eParam = [arrTemp objectAtIndex:3];
                            }
                        }
                    }
                }
                
                TKLogInfo(@"ip=%@,port=%d,roomid=%@",ip,port,roomId);
            }
            else{
                errMsg = @"分配服务器数据格式错误。";
            }
           
            if ([delegate respondsToSelector:@selector(chatServiceGetVideoIP:port:roomId:extParam:error:)]) {
                [delegate chatServiceGetVideoIP:ip port:port roomId:roomId extParam:eParam error:errMsg];
            }
            
        }else{
            
            if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                
                [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
            }
            
        }
    }];

}

//用户校检
-(void)requestForValidUserId:(NSString*)userId password:(NSString*)pwd{

    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501583";
    
    tkReqParam[@"user_id"]=userId;
    
    tkReqParam[@"password"]=pwd;
    
    tkReqParam[@"user_type"]=@"1";
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"] ;
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *data = [dic objectForKey:@"result_state"];
            
            BOOL ret = [@"1" isEqualToString:data];
        
            if ([delegate respondsToSelector:@selector(chatServiceValidPass:error:)]) {
                [delegate chatServiceValidPass:ret error:nil];
            }
            
        }else{
            
            if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                
                [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
            }
            
        }
    }];

}

//是否轮到自己见证
-(void)requestForIsMyTurnByUserId:(NSString*)userId{
    
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501575";
    
    tkReqParam[@"user_id"]=userId;
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *data = [dic objectForKey:@"flag"];
            
           BOOL ret = [@"true" isEqualToString:data];
        
             if (delegate && [delegate respondsToSelector:@selector(chatServiceIsMyTurn:error:staff_tips:)]) {
                           
                 [delegate chatServiceIsMyTurn:ret error:nil staff_tips:dic[@"staff_tips"]];
             }
            
        }else{
            
            if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                
                [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
            }
            
        }
    }];

}

//网点排队人数
-(void)requestForOrgOrderCountByOrgId:(NSString*)oid{

    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501574";
    
    tkReqParam[@"org_id"]=oid;
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *result = [dic objectForKey:@"count"];
            
            if ([delegate respondsToSelector:@selector(chatServiceOrgOrderCount:error:)]) {
                [delegate chatServiceOrgOrderCount:[result intValue] error:nil];
            }
            
        }else{
            
            if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                
                [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
            }
            
        }
    }];
}

//查询当前客户前面的排队人数
- (void)requestForOrgOrderCountByUserId:(NSString*)userId orgId:(NSString*)orgId{

    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501586";
    
    tkReqParam[@"user_id"]=userId;
    
    tkReqParam[@"org_id"]=orgId;
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *result = [dic objectForKey:@"count"];
            
            if ([delegate respondsToSelector:@selector(chatServiceOrgOrderCount:error:)]) {
                [delegate chatServiceOrgOrderCount:[result intValue]  error:nil];
            }
            
        }else{
            
            if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                
                [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
            }
            
        }
    }];

}
/**
 *  查询当前座席状态
 *
 *  @param branchNo 营业部编号
 */
-(void)requestForBranchStateByBranchNo:(NSString*)branchNo {
    
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501581";
    
    tkReqParam[@"branch_no"]=branchNo;
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *result = [dic objectForKey:@"result"];
            
            if ([delegate respondsToSelector:@selector(chatServiceRequestBranchStateBack:error:)]) {
                [delegate chatServiceRequestBranchStateBack:[result intValue] error:nil];
            }
            
        }else{
            
            if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                
                [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
            }
            
        }
    }];
}

/**
 *  通过坐席id查询坐席执业编号
 *
 */
-(void)requestZybhByZxid:(NSString *)zxid
{
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    if (iParams) {
        [tkReqParam addEntriesFromDictionary:iParams];
    }
    
    tkReqParam[@"funcNo"]=@"501323";
    
    tkReqParam[@"uid2"]=zxid;
    
    if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
        tkReqParam[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
    }
    
    [oService handleNetworkWithURL:host param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", tkReqParam[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            NSString *zybh = [dic objectForKey:@"email"];
            
            NSString *kfh = [dic objectForKey:@"uid2"];
            
            if (delegate && [delegate respondsToSelector:@selector(onRequestZybh:AndKfh:)]) {
                
                [delegate onRequestZybh:zybh AndKfh:kfh];
            }
            
        }else{
            
            if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                
                [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
            }

        }
    }];

}

/**
 * @method handleIngerfaceRequest:
 * @description  接口通用请求处理
 * @param  rParams 请求参数
 */
- (void)handleInterfaceRequest:(NSMutableDictionary *)rParams
{
    if (rParams) {
        
        if (iParams) {
            [rParams addEntriesFromDictionary:iParams];
        }
        
        if ([TKSystemHelper getMemcacheWithKey:@"module_account"]) {
            
            rParams[@"ygt_fund_account"]=[TKSystemHelper getMemcacheWithKey:@"module_account"];
        }
        
        [oService handleNetworkWithURL:rParams[@"url"] param:rParams callBackFunc:^(ResultVo *resultVo){
            
            TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", rParams[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
            
            NSArray *results = (NSArray *)resultVo.results;
            
            if (resultVo.errorNo == 0 && [results count] > 0)
            {
                NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
                
                if (delegate && [delegate respondsToSelector:@selector(requestCallBack:withResult:)]) {
                    
                    [delegate requestCallBack:rParams[@"funcNo"] withResult:dic];
                }
                
            }else{
                
                if (delegate && [delegate respondsToSelector:@selector(handleRequestException:errorDescription:)]) {
                    
                    [delegate handleRequestException:[NSString stringWithFormat:@"%ld", (long)resultVo.errorNo] errorDescription:resultVo.errorInfo];
                }
                
            }
        }];
        
    }
}

-(void)cancelAllRequest
{
    if (oService) {
        [oService clearAllRequest];
    }

}

-(void)dealloc{
    
    if (oService) {
        [oService clearAllRequest];
        oService = nil;
    }
    delegate=nil;
    self.host = nil;
    TKLogInfo(@"%@%s",NSStringFromClass([self class]), __FUNCTION__);
    
}

@end
