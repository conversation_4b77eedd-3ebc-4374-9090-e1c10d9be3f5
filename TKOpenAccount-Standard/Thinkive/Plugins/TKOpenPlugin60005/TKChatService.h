//
//  TKChatService.h
//  TKchat_AC
//
//  Created by chensj on 14-4-18.
//  Copyright (c) 2014年 chensj. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKChatServiceDelegate.h"

@interface TKChatService : NSObject{
    __unsafe_unretained id<TKChatServiceDelegate> delegate;
    NSString *host;
}

@property(nonatomic,assign)id<TKChatServiceDelegate> delegate;
@property(nonatomic,retain)NSString *host;


-(id)initWithDelegate:(id<TKChatServiceDelegate>)dele host:(NSString*)url ;

-(id)initWithDelegate:(id<TKChatServiceDelegate>)dele host:(NSString*)url withParams:(id)params;

//请求排队
-(void)requestForToOrder:(NSString*)userId nickName:(NSString*)nName orgId:(NSString*)oid level:(NSString*)lv origin:(NSString*)ori;
//客户端请求断开视频
-(void)requestEndVideoByUserId:(NSString*)userId;
//请求见证地址
-(void)requestForVideoServerByUserId:(NSString*)userId;

//用户校检
-(void)requestForValidUserId:(NSString*)userId password:(NSString*)pwd;

//是否轮到自己见证
-(void)requestForIsMyTurnByUserId:(NSString*)userId;

//网点排队人数
-(void)requestForOrgOrderCountByOrgId:(NSString*)oid;

//取消排队
-(void)requestEndOrder:(NSString*)userId orgId:(NSString*)orgId;

//查询当前客户前面的排队人数
-(void)requestForOrgOrderCountByUserId:(NSString*)userId orgId:(NSString*)orgId;

-(void)handleInterfaceRequest:(NSMutableDictionary *)rParams;

//  查询当前座席状态
-(void)requestForBranchStateByBranchNo:(NSString*)branchNo ;
-(void)requestZybhByZxid:(NSString *)zxid;
//取消之前的请求
-(void)cancelAllRequest;
@end
