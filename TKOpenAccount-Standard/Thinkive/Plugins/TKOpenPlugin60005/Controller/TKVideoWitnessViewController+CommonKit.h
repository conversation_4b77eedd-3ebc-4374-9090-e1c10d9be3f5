//
//  TKVideoWitnessViewController+CommonKit.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON>lover on 2017/6/8.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "TKVideoWitnessViewController.h"

/**
 *
 * @class TKVideoWitnessViewController (CommonKit)
 *
 * @description 扩展视频见证控制器
 *
 */

@interface TKVideoWitnessViewController (CommonKit)

/**
 *
 * @method startLineingUp
 *
 * @brief 开始4.0视频排队接口
 *
 */
- (void)startLineingUp;


/**
 *
 * @method stopLineingUp
 *
 * @brief 结束4.0视频排队接口
 *
 */
- (void)stopLineingUp;

/**
 * @method handleIngerfaceRequest:
 * @description  接口通用请求处理
 * @param  rParams 请求参数
 */
- (void)handleInterfaceRequest:(NSMutableDictionary *)rParams;

@end
