//
//  TKVideoWitnessViewController+CommonKit.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2017/6/8.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "TKVideoWitnessViewController+CommonKit.h"
#import "TKOpenAccountService.h"

@interface TKVideoWitnessViewController (private)

- (NSMutableDictionary*)requestParams;

- (BOOL)isShowAlert;

- (void)setIsShowAlert:(BOOL)isShowAlert;

- (UIView*)waitView;

- (UIView*)witnessView;

- (NSString*)witnessResult;

- (void)setWitnessResult:(NSString*)witnessResult;

- (BOOL)isCancelLineingUp;

- (void)tkStartVideoWitness:(NSString*)sUrl withPort:(int)sPort;

- (void)setWitRoomId:(NSString*)witRoomId;

- (BOOL)aExit;

- (void)setLinePosition:(int)position;
-(void)setTipType:(int)tipType;
- (int)linePosition;
-(int)tipType;

- (void)setMService:(TKOpenAccountService *)mService;

- (TKOpenAccountService *)mService;

-(void)uploadLog:(NSString *)logString;

- (void)setIsLinePositionLog:(BOOL)isLinePositionLog;

- (BOOL)isLinePositionLog;
@end

@implementation TKVideoWitnessViewController (CommonKit)

//请求排队
- (void)startLineingUp{
    
    if (self.requestParams && !self.isCancelLineingUp) {
        

        
        self.requestParams[@"funcNo"]=@"501301";
        
        if (!self.requestParams[@"origin"]) {
            self.requestParams[@"origin"]=@"2";
        }
        if (!self.requestParams[@"level"]) {
            self.requestParams[@"level"]=@"0";
        }
        
        
        
        [self.mService handleNetworkWithURL:self.requestParams[@"url"] param:self.requestParams callBackFunc:^(ResultVo *resultVo) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
                
                if (resultVo.errorNo == 0) {
                    
                    NSArray *arr = (NSArray*)resultVo.results;
                    
                    if (arr.count > 0) {
                        
                        NSDictionary *resReslut = [arr objectAtIndex:0];
                        
                        if (resReslut) {
                            
                            if ([[resReslut objectForKey:@"staff_exist"] isEqualToString:@"true"]) {//有坐席
                                
                                if ([[resReslut objectForKey:@"queue_location"] integerValue] == 0){//排队位置 可以开始见证
                                    
                                    if ([resReslut objectForKey:@"server_roomNo"]){//开始见证
                                        
                                        linePromptLable.text = @"正在连接视频服务器...";
                                        
                                        //针对ipv6和ipv4不同情况处理
                                        NSArray *arr;
                                        if ([[resReslut objectForKey:@"server_roomNo"] rangeOfString:@"]"].location == NSNotFound) {
                                            //ipv4地址
                                            arr=[[resReslut objectForKey:@"server_roomNo"] componentsSeparatedByString:@":"];
                                        }else{
                                            //是ipv6的地址有[]情况
                                            NSArray *tempArray=[[resReslut objectForKey:@"server_roomNo"] componentsSeparatedByString:@"]"];
                                            NSString *tempIp;
                                            if ([@"tchat" isEqualToString:self.requestParams[@"videoType"]] || (self.requestParams[@"videoType"] && [self.requestParams[@"videoType"] integerValue] == 0)) {
                                                //tchat使用ipv6需要[]
                                                tempIp=[NSString stringWithFormat:@"%@]",tempArray[0]];
                                            }else{
                                                //anychat使用ipv6不需要[]
                                                tempIp=[tempArray[0] stringByReplacingOccurrencesOfString:@"["withString:@""];
                                            }
                                            
                                            NSArray *portRoomArray=[tempArray[1] componentsSeparatedByString:@":"];
                                            NSString *tempPort=portRoomArray[1];
                                            NSString *tempRoom=portRoomArray[2];
                                            arr=[[NSArray alloc] initWithObjects:tempIp,tempPort,tempRoom, nil];
                                        }
                                        
                                        if (arr.count >= 3) {
                                            
                                            NSString *acRoomId = arr[2];
                                            
                                            TKLogInfo(@"房间号:%@",acRoomId);
                                            
                                            if (acRoomId && ![acRoomId isEqualToString:@""]) {
                                                
                                                self.witRoomId = acRoomId;
                                                
                                                //h5传了视频服务器ip端口的话就用h5传的，h5没穿再用排队bus的
                                                NSString *videoServerIP=self.requestParams[@"videoServerIP"]?self.requestParams[@"videoServerIP"]:arr[0];
                                                NSString *videoServerPort=self.requestParams[@"videoServerPort"]?self.requestParams[@"videoServerPort"]:arr[1];
                                                
                                                NSString *logString=[NSString stringWithFormat:@"TKMSG1003:%@|%@:%@",acRoomId,videoServerIP,videoServerPort];
                                                [self uploadLog:logString];
                                                
                                                [self tkStartVideoWitness:videoServerIP withPort:[videoServerPort intValue]];

                                                
                                            }else{
                                                
                                                TKLogInfo(@"未获取正常的房间号");
                                                
                                                [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
                                                
                                            }
                                            
                                        }
                                        
                                    }else{//未获取到见证地址
                                        
                                        [self startLineingUp];
                                    }
                                    
                                }else{//处于排队中
                                    
                                    TKLogInfo(@"排队中...");
                                    
                                    if ([[resReslut objectForKey:@"queue_location"] integerValue] < 0) {
                                        
                                        linePromptLable.text =[NSString stringWithFormat:@"等待%@同意，请稍候...",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服"];
                                        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_LineingEnd.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_LineingEnd.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
                                        
                                    }else{
                                        
                                        int currentPosition = [[resReslut objectForKey:@"queue_location"] intValue];
                                        
                                        
                                        if (self.linePosition == 0 || self.linePosition > currentPosition) {
                                            //默认被插队，或位置不变，不修改位置提示语
                                            self.linePosition = currentPosition;
                                        }
                                        
                                        if(!self.isLinePositionLog) {
                                            //记录第一次排队位置日志
                                            self.isLinePositionLog=YES;
                                            NSString *logString=[NSString stringWithFormat:@"TKMSG1002:%d",self.linePosition];
                                            [self uploadLog:logString];
                                        }
                                        
                                        if ([resReslut objectForKey:@"tip_type"]) {
                                            if (self.tipType==0||self.tipType>[[resReslut objectForKey:@"tip_type"] intValue]) {
                                                self.tipType=[[resReslut objectForKey:@"tip_type"] intValue];
                                            }
                                        }
                                        //有tip_type修改提示语，如果被插队或位置不变话术就用原来的(东亚前海的需求)
                                        if ([resReslut objectForKey:@"tip_type"]) {
                                            switch (self.tipType) {
                                                case 1:
                                              
                                                 
                                                    linePromptLable.text =    [NSString stringWithFormat:@"您目前排在第%d位,%@正在接入,请稍等", self.linePosition,self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服"];
                                                case 2:
                                                    linePromptLable.text = [NSString stringWithFormat:@"您目前排在第%d位,请耐心等待", self.linePosition];
                                                    break;
                                                case 3:
                                                    linePromptLable.text = [NSString stringWithFormat:@"您目前排在第%d位,请耐心等待", self.linePosition];
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }else{
                                            linePromptLable.text = [NSString stringWithFormat:@"您当前排在第%d位", self.linePosition];
                                        }
                                    }
                                    
                                    [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
                                }
                                
                            }else{//没有坐席
                                
                                TKLogInfo(@"查询坐席...");
                                
//                                 linePromptLable.text = @"当前坐席繁忙，您可稍后进行视频见证或选择继续等待";
                              
                                linePromptLable.text =   [NSString stringWithFormat:@"当前无%@服务，您可稍后进行视频见证或选择继续等待",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服"];
                                
                                [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
                            }
                            
                        }else{//数据异常
                            
                            [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
                        }
                        
                    }
                    
                }else{//网络异常
                     [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_LineingErr.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_LineingErr.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
                    
                    
                    TKLogInfo(@"501301_%ld-%@", (long)resultVo.errorNo, resultVo.errorInfo);
                    
                    linePromptLable.text = @"网络环境较差，请等待或更换网络...";
                    
                    if (resultVo.errorNo == -1001) {
                        
                        [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
                        
                    }else{
                        
                        if (!self.isShowAlert) {
                            
                            self.isShowAlert = YES;
                            
                            UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"" message:resultVo.errorInfo ? resultVo.errorInfo:@"您的网络异常，请重新排队！" delegate:self cancelButtonTitle:@"确定" otherButtonTitles: nil];
                            
                            alert.tag = 106;
                            
                            [alert show];
                            
                        }
                        
                        self.witnessResult = [NSString stringWithFormat:@"%ld",(long)resultVo.errorNo];
                        
                    }
                    
                }
            });
            
        }];
        
    }else{
        
        TKLogInfo(@"数据异常");
         [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_LineingErr.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_LineingErr.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
    }
    
}

//结束排队
- (void)stopLineingUp{
    
    if (self.requestParams) {
        
        NSString *logString=[NSString stringWithFormat:@"TKMSG1021:用户主动取消排队"];
        [self uploadLog:logString];
        
        self.requestParams[@"funcNo"]=@"501302";
        
        if (!self.requestParams[@"origin"]) {
            self.requestParams[@"origin"]=@"2";
        }
        if (!self.requestParams[@"level"]) {
            self.requestParams[@"level"]=@"0";
        }
        
        self.requestParams[@"abnormal_exit"]=[NSNumber numberWithBool:self.aExit];
        
        [self.mService handleNetworkWithURL:self.requestParams[@"url"] param:self.requestParams callBackFunc:^(ResultVo *resultVo) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                if (resultVo.errorNo == 0) {
                    
                    NSArray *arr = (NSArray*)resultVo.results;
                    
                    if (arr.count > 0) {
                        
                        NSDictionary *resReslut = [arr objectAtIndex:0];
                        
                        if (resReslut && [[resReslut objectForKey:@"flag"] isEqualToString:@"true"]) {
                            
                            TKLogInfo(@"排队取消成功");
                        }
                    }
                    
                }else{//网络异常
                    
                    TKLogInfo(@"501302_%@", resultVo.errorInfo);
                    
                }
            });
            
        }];
        
    }else{
        
        TKLogInfo(@"数据异常");
    }
    
}

/**
 * @method handleIngerfaceRequest:
 * @description  接口通用请求处理
 * @param  rParams 请求参数
 */
- (void)handleInterfaceRequest:(NSMutableDictionary *)rParams
{
    if (rParams) {
        

        
        [self.mService handleNetworkWithURL:rParams[@"url"] param:rParams callBackFunc:^(ResultVo *resultVo){
            
            TKLogInfo(@"%@_errorNo:%ld, errorInfo-%@", rParams[@"funcNo"], (long)resultVo.errorNo, resultVo.errorInfo);
            
            NSArray *results = (NSArray *)resultVo.results;
            
            if (resultVo.errorNo == 0 && [results count] > 0)
            {
                NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
                
                if (rParams[@"funcNo"] && dic) {
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        if ([@"502000" isEqualToString:rParams[@"funcNo"]]) {
                            
                            NSString *str = [NSString stringWithFormat:@"执业编号：%@", dic[@"zybh"]];
                            
                            UILabel *seatPromptLable = (UILabel*)[self.witnessView viewWithTag:2002];
                            
                            seatPromptLable.text = str;
                            
                        }
                        
                    });
                }
                
            }else{}
        }];
        
    }
}


@end
