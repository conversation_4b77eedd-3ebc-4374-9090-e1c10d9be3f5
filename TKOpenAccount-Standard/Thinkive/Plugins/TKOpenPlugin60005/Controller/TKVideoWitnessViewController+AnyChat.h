//
//  TKVideoWitnessViewController+AnyChat.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON>lover on 2017/6/8.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "TKVideoWitnessViewController.h"

/**
 *
 * @class TKVideoWitnessViewController (AnyChat)
 *
 * @description anychat视频见证
 *
 */

@interface TKVideoWitnessViewController (AnyChat)


/**
*
* @method tkStartAnyChatWitness: withPort:
*
* @brief 开启视频见证
* @param sUrl 视频服务器地址
* @param sPort 视频服务器端口
*
*/
-(void)tkStartAnyChatWitness:(NSString*)sUrl withPort:(int)sPort;

/**
 *
 * @method tkStopAnyChatWitness
 *
 * @brief 结束视频见证
 *
 */
-(void)tkStopAnyChatWitness;

@end
