//
//  TKVideoWitnessViewController.m
//  TKOpenAccount-Standard
//
//  Created by Clover on 2017/6/8.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "TKVideoWitnessViewController.h"
#import "TKVideoWitnessViewController+CommonKit.h"
#import "TKAVCaptureManager.h"
#import "TKOpenAccountService.h"

@interface TKVideoWitnessViewController ()<UIAlertViewDelegate>
{}

@property(nonatomic,retain) NSMutableDictionary *requestParams;

@property(nonatomic,retain) UIView *waitView;

@property(nonatomic,retain) UIView *witnessView;

@property(nonatomic,retain) NSString *witnessResult;

@property(nonatomic,retain) NSString* witRoomId;

@property(nonatomic,assign) BOOL isCancelLineingUp;

@property(nonatomic,assign) BOOL isShowAlert;

@property(nonatomic, assign) BOOL aExit;

@property(nonatomic, assign) int  linePosition;
@property(nonatomic, assign) int  tipType;//提示语类型记录，也只记录较小值
@property (nonatomic, strong) NSString *tkNewTipString;//左下角滚动提示语字符串
@property (nonatomic, strong) TKOpenAccountService *mService;

@property(nonatomic, assign) BOOL  isLinePositionLog;//是否记录了第一次排队位置,默认no

-(void)startAnyChatWitness:(NSString*)sUrl withPort:(int)sPort;

-(void)stopAnyChatWitness;

-(void)startTChatWitness:(NSString*)sUrl withPort:(int)sPort;

-(void)stopTChatWitness;

-(void)uploadLog:(NSString *)logString;

-(NSString *)getTimeStamp;
@end

@implementation TKVideoWitnessViewController

- (id)initWithParams:(id)mParams nibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil
{
    self = [self initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    
    if (self) {
        
        if (mParams && [mParams isKindOfClass:[NSDictionary class]]) {
            
            self.requestParams = mParams;
            self.mService=[[TKOpenAccountService alloc] init];
        }
    }
    
    return self;
}

- (void)viewDidLoad {
    
    [super viewDidLoad];

    
    //h5做了原生就不做了
//    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
//        //获取ip要放异步太耗时了
//        NSString *logString=[NSString stringWithFormat:@"TKMSG1001:网络情况%@|%@|%@|%@",[TKNetHelper getNetworkTypeInfo],[TKNetHelper getIP],[TKNetHelper getPhoneOperatorInfo],[TKDeviceHelper getDevicePlatformInfo]];
//        [self uploadLog:logString];
//    });


}

-(NSString *)netWorkInfo:(Network_Type)networkType{
    NSString *netWorkInfo;
    switch (networkType) {
        case Network_No:
            netWorkInfo=@"无网络";
            break;
        case Network_2G:
            netWorkInfo=@"2G";
            break;
        case Network_3G:
            netWorkInfo=@"3G";
            break;
        case Network_4G:
            netWorkInfo=@"4G";
            break;
        case Network_WIFI:
            netWorkInfo=@"WIFI";
            break;
        case Network_WWAN:
            netWorkInfo=@"WWAN";
            break;
        case Network_5G:
            netWorkInfo=@"5G";
            break;
        case Network_UnDefined:
            netWorkInfo=@"未知";
            break;
        default:
            break;
    }
    return netWorkInfo;
}

-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    
    NSArray *views = [self.view subviews];
    
    if ([views count] == 2) {
        
        self.witnessView = [views objectAtIndex:0];
        
        self.waitView = [views objectAtIndex:1];
    }
    /**视频排队界面**/
    if (self.waitView) {
        
        if (![@"2" isEqualToString:self.requestParams[@"interfaceStyles"]]){
            
            UIView *lacalVideoView = [self.waitView viewWithTag:1002];
            
            if (lacalVideoView) {
                
                CGRect localFrame = lacalVideoView.frame;
                
                localFrame.size.width  =  [UIScreen mainScreen].bounds.size.width;
                
                localFrame.size.height = [UIScreen mainScreen].bounds.size.height;
                
                lacalVideoView.frame = localFrame;
            
                TKAVCaptureManager* captureManager = [[TKAVCaptureManager alloc] initWithPreviewView:lacalVideoView withCameraPosition:AVCaptureDevicePositionFront withCamraOrientation:AVCaptureVideoOrientationPortrait handleBusinessType:TK_VIDEO_PREVIEW];
            }
            
        }
        //使用h5传的的客服提示字样
        if (self.requestParams[@"serviceTipString"]) {
            UILabel *tipSrvLable= [self.waitView viewWithTag:2005];
            tipSrvLable.text=self.requestParams[@"serviceTipString"];
        }
        
        UIButton *cancelLineingBtn = [self.waitView viewWithTag:1005];
        
        [cancelLineingBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
        
        
        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_SLineing.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_SLineing.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
        
        
        self.isLinePositionLog=NO;
        /**启动排队**/
        [self startLineingUp];
        
    }
    /**视频见证界面**/
    if (self.witnessView) {
        
        UIButton *cancelWitnessBtn = [self.witnessView viewWithTag:3002];
        [cancelWitnessBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    _witnessResult = @"20000";
    
    _aExit = YES;
    
    _isShowAlert = NO;
    
    _isCancelLineingUp = NO;
    
    _linePosition = 0;
    _tipType=0;
}

- (void)viewDidLayoutSubviews
{
    UIView *lacalVideoView = [self.waitView viewWithTag:1002];
    
    if (lacalVideoView) {
        
        CGRect localFrame = lacalVideoView.frame;
        
        localFrame.size.width  =  [UIScreen mainScreen].bounds.size.width;
        
        localFrame.size.height = [UIScreen mainScreen].bounds.size.height;
        
        lacalVideoView.frame = localFrame;
    }
}

- (UIStatusBarStyle)preferredStatusBarStyle
{
    return UIStatusBarStyleDefault;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    [TKTraffic visitPage:[NSString stringWithFormat:@"%@.10.4503",[TKCommonUtil fetchAppStatisticsMarker]]];
}

- (void)btnOnClicked:(id)sender
{
    UIButton *btn = sender;
    
    if (btn.tag == 1005) {//取消排队
        
        NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
        bpDic[@"eventType"]=@"2";
        bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.10.4513",[TKCommonUtil fetchAppStatisticsMarker]];
        bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.10.904",[TKCommonUtil fetchAppStatisticsMarker]];
        [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50405" param:bpDic moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
        
        self.isCancelLineingUp = YES;
        
        _aExit = NO;
        
        if ([@"tchat" isEqualToString:_requestParams[@"videoType"]] || [@"0" isEqualToString:_requestParams[@"videoType"]]) {
            
            [self stopTChatWitness];
            
        }else{
            
            [self stopAnyChatWitness];
        }
        
    }else if(btn.tag == 3002){//挂断视频见证
        
        if (!self.isShowAlert) {
            
            self.isShowAlert = YES;
            
            UIAlertView *mAlertView = [[UIAlertView alloc] initWithTitle:@"" message:@"确定要挂断视频见证吗?" delegate:self cancelButtonTitle:@"确定" otherButtonTitles:@"取消", nil];
            
            mAlertView.tag = 100;
            
            [mAlertView show];
        }
        
        _aExit = NO;
        
    }
}

- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    
    if (buttonIndex == 0) {
        
        NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
        bpDic[@"eventType"]=@"2";
        bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.10.4514",[TKCommonUtil fetchAppStatisticsMarker]];
        bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.10.905",[TKCommonUtil fetchAppStatisticsMarker]];
        [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50405" param:bpDic moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
      
        if (alertView.tag == 106) {
            
            self.isCancelLineingUp = YES;
        }
        
        
        if(alertView.tag==100){
            //挂断视频埋点
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CWitness.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CWitness.actionId", @"open"]] attributes:self.requestParams[@"extParams"]];
        }
        
        if ([@"tchat" isEqualToString:_requestParams[@"videoType"]] || [@"0" isEqualToString:_requestParams[@"videoType"]]) {
            
            [self stopTChatWitness];
            
        }else{
            
            [self stopAnyChatWitness];
        }
        
    }else{
        self.isShowAlert = NO;
    }
}

/***
 启动视频见证
 */
- (void)tkStartVideoWitness:(NSString*)sUrl withPort:(int)sPort
{
    [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_StartConnServer.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_StartConnServer.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
    if ([@"tchat" isEqualToString:_requestParams[@"videoType"]] || (_requestParams[@"videoType"] && [_requestParams[@"videoType"] integerValue] == 0)) {
        
        TKLogInfo(@"---启动TChat视频---");
        
        [self startTChatWitness:sUrl withPort:sPort];
        
    }else{
        
        TKLogInfo(@"---启动AnyChat视频---");
        
        [self startAnyChatWitness:sUrl withPort:sPort];
    }
}

- (void)startAnyChatWitness:(NSString *)sUrl withPort:(int)sPort{
    
    [self startTChatWitness:sUrl withPort:sPort];
}
- (void)stopAnyChatWitness{
    
    [self stopTChatWitness];
}
- (void)startTChatWitness:(NSString *)sUrl withPort:(int)sPort{
    
    [self startAnyChatWitness:sUrl withPort:sPort];
}
- (void)stopTChatWitness{
    
    [self stopAnyChatWitness];
}

-(void)uploadLog:(NSString *)logString{
    
    if ([TKStringHelper isNotEmpty:self.requestParams[@"logUrl"]]){
        //需要上传单向视频日志
//        NSMutableDictionary *param=[[NSMutableDictionary alloc] init];
//        param[@"user_id"]=self.requestParams[@"userId"];
//        param[@"msg"]=logString;
        NSString *url=[NSString stringWithFormat:@"%@user_id=%@&msg=%@",self.requestParams[@"logUrl"],self.requestParams[@"userId"],logString];

        [self.mService uploadFileWithURL:url param:nil callBackFunc:^(ResultVo *resultVo) {
       
            TKLogInfo(@"-------");
        }];
        

    }
}

-(NSString *)getTimeStamp{

    // 设置想要的格式，hh与HH的区别:分别表示12小时制,24小时制
    //把NSDate按formatter格式转成NSString
    NSString *currentTime = [TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
    return currentTime;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)dealloc{
    
    self.requestParams = nil;
    
    self.witRoomId = nil;
    
    self.waitView = nil;
    
    self.witnessView = nil;
    
    self.witnessResult = nil;
    
    self.delegate = nil;
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"ANYCHATNOTIFY" object:nil];
    
    TKLogInfo(@"%@%s",NSStringFromClass([self class]), __FUNCTION__);
}


@end
