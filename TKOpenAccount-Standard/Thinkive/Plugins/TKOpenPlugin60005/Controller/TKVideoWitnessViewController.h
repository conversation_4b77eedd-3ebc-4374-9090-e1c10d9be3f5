//
//  TKVideoWitnessViewController.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON>lover on 2017/6/8.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

/****
 
 @protocol TKVideoWitnessDelegate
 
 @description 视频见证结果委托
 
 */
@protocol TKVideoWitnessDelegate <NSObject>

@required

- (void)tkVideoWitnessResult:(NSString*)wResult;

@end

/****
 
 @class TKVideoWitnessViewController
 
 @description anychat 视频见证控制器
 
 */
@interface TKVideoWitnessViewController : TKBaseViewController

@property(nonatomic,assign) id<TKVideoWitnessDelegate> delegate;

- (id)initWithParams:(id)mParams nibName:(NSString*)nibNameOrNil bundle:(NSBundle*)nibBundleOrNil;

@end
