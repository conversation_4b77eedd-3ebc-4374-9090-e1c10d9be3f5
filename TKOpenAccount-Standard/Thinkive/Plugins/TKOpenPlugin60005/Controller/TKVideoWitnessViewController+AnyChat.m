//
//  TKVideoWitnessViewController+AnyChat.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2017/6/8.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "TKVideoWitnessViewController+AnyChat.h"
#import "AnyChatDefine.h"
#import "AnyChatErrorCode.h"
#import "AnyChatPlatform.h"


@interface TKVideoWitnessViewController (private)<AnyChatNotifyMessageDelegate,AnyChatTransDataDelegate,AnyChatTextMsgDelegate,AnyChatStateChangeDelegate, UIAlertViewDelegate>

- (NSMutableDictionary*)requestParams;

- (BOOL)isShowAlert;

- (BOOL)isCancelLineingUp;

- (void)setIsShowAlert:(BOOL)isShowAlert;

- (UIView*)waitView;

- (UIView*)witnessView;

- (NSString*)witRoomId;

- (void)setWitnessResult:(NSString*)witnessResult;

- (NSString*)witnessResult;

- (void)setAExit:(BOOL)aExit;

- (void)stopLineingUp;

- (void)handleInterfaceRequest:(NSMutableDictionary *)rParams;

void TKSwizzle(Class c, SEL org, SEL new);

-(void)startAnyChatWitness:(NSString*)sUrl withPort:(int)sPort;

-(void)stopAnyChatWitness;

-(void)setTkNewTipString:(NSString *)tkNewTipString;

-(NSString *)tkNewTipString;

-(void)uploadLog:(NSString *)logString;

-(NSString *)getTimeStamp;
@end


@implementation TKVideoWitnessViewController (AnyChat)

int acUserId, acSeatId, nCountDown;

BOOL isStartingVideo, isTransBufferMsg;

NSTimer *countDownTime, *udTimer;

AnyChatPlatform *anychat;

UITextView *tipMsgAppendTextView;//客服话术提示追加滚动提示语语视图


+ (void)load
{
    TKSwizzle([self class], @selector(startAnyChatWitness:withPort:), @selector(tkStartAnyChatWitness:withPort:));
    
    TKSwizzle([self class], @selector(stopAnyChatWitness), @selector(tkStopAnyChatWitness));
}

/**启动anychat视频**/
-(void)tkStartAnyChatWitness:(NSString*)sUrl withPort:(int)sPort
{
    //黄坚修改初始化
    acUserId = 0;
    acSeatId = 0;
    
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(AnyChatNotifyHandler:) name:@"ANYCHATNOTIFY" object:nil];
    [AnyChatPlatform InitSDK:0];
    anychat = [[AnyChatPlatform alloc] init];
    anychat.notifyMsgDelegate = self;
    anychat.stateChangeDelegate = self;
    anychat.transDataDelegate = self;
    anychat.textMsgDelegate = self;
#ifdef BRAC_SO_CLOUD_APPGUID
    if (self.requestParams[@"appId"]) {
        [AnyChatPlatform SetSDKOptionString:BRAC_SO_CLOUD_APPGUID : self.requestParams[@"appId"]];
    }
#endif
    [AnyChatPlatform Connect:sUrl : sPort];
    nCountDown = 20;
    isStartingVideo = NO;
    isTransBufferMsg = NO;
    tipMsgAppendTextView=nil;

    //记录开始连接视频服务器事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1004:网络情况%@|发起连接|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];
    [self uploadLog:logString];
}

-(void)tkStopAnyChatWitness
{
    [self endACVideo];

}

/***************** anychat delegate ******************/
- (void)AnyChatNotifyHandler:(NSNotification*)notify
{
    NSDictionary* dict = notify.userInfo;
    [anychat OnRecvAnyChatNotify:dict];
}

// 连接服务器消息
- (void) OnAnyChatConnect:(BOOL) bSuccess
{
    //记录开始连接视频服务器返回事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1005:网络情况%@|%d|%@",[TKNetHelper getNetworkTypeInfo],bSuccess,[self getTimeStamp]];
    [self uploadLog:logString];
    if(bSuccess){//登录anychat
        
        UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
        
        linePromptLable.text = @"正在登陆视频服务器...";
        
        NSString *loginName = [NSString stringWithFormat:@"user%@", self.requestParams[@"user_id"]];
        
        [AnyChatPlatform Login:loginName : @"123456"];
        
        //记录开始登陆视频服务器
        NSString *logString=[NSString stringWithFormat:@"TKMSG1006:网络情况%@|开始登录服务器|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];
        [self uploadLog:logString];
    }else{
        
        TKLogInfo(@"-----------------connect fail------------");
        
        
        
        if (!self.isShowAlert) {
            self.isShowAlert = YES;
            UIAlertView *mAlertView = [[UIAlertView alloc] initWithTitle:@"" message:@"服务器异常，请稍后重试！" delegate:self cancelButtonTitle:@"确定" otherButtonTitles: nil];
            mAlertView.tag = 101;
            [mAlertView show];
            
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_ConnServerErr.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_ConnServerErr.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
        }
        
    }
}
// 用户登陆消息
- (void) OnAnyChatLogin:(int) dwUserId : (int) dwErrorCode
{
    //记录开始登陆视频服务器返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1007:网络情况%@|登陆服务器结果%d|%@",[TKNetHelper getNetworkTypeInfo],dwErrorCode,[self getTimeStamp]];
    [self uploadLog:logString];
    
    if(dwErrorCode == GV_ERR_SUCCESS)
    {
        TKLogInfo(@"用户id:%d",dwUserId);
        
        acUserId = dwUserId;
        
        UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
        
        linePromptLable.text = @"正在进入房间...";
        
        [AnyChatPlatform EnterRoom:[self.witRoomId intValue] :@""];
        //记录开始进入房间

        NSString *logString=[NSString stringWithFormat:@"TKMSG1008:网络情况%@|开始进入房间|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];
        [self uploadLog:logString];
    }else{
        
        TKLogInfo(@"登陆失败");
        
        if (!self.isShowAlert) {
            self.isShowAlert = YES;
            UIAlertView *mAlertView = [[UIAlertView alloc] initWithTitle:@"" message:@"服务器异常，请稍后重试！" delegate:self cancelButtonTitle:@"确定" otherButtonTitles: nil];
            mAlertView.tag = 102;
            [mAlertView show];
            
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_ConnServerErr.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_ConnServerErr.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
        }
    }
}
// 用户进入房间消息
- (void) OnAnyChatEnterRoom:(int) dwRoomId : (int) dwErrorCode
{
    //记录开始进入房间返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1009:网络情况%@|进入房间结果%d|%@",[TKNetHelper getNetworkTypeInfo],dwErrorCode,[self getTimeStamp]];
    [self uploadLog:logString];
    
    if (dwErrorCode == 0) {
        
        TKLogInfo(@"已进入房间");
        
        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_MeEnterRoom.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_MeEnterRoom.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
        
        NSMutableArray *onlineUser = [AnyChatPlatform GetOnlineUser];//获取房间中在线人数
        
        if (onlineUser.count > 0){
    
            for (NSNumber *uid in onlineUser) {
                
                if ([uid intValue] != acUserId) {
                    
                    acSeatId = [uid intValue];
                    
                    break;
                }
            }
        }
        
        if (acSeatId) {
            
            if (!isStartingVideo) {
                
                isStartingVideo = YES;
                
                TKLogInfo(@"坐席id=%d",acSeatId);
                
                NSString *uName = [AnyChatPlatform GetUserName:acSeatId];
                
                if (self.requestParams && [@"lianchu" isEqualToString:self.requestParams[@"securitiesName"]]) {
                    
                    NSMutableDictionary *rDic = [NSMutableDictionary dictionaryWithCapacity:5];
                    
                    rDic[@"url"]=self.requestParams[@"url"];
                    
                    rDic[@"funcNo"]=@"502000";
                    
                    rDic[@"video_num"]=uName;
                    
                    [self handleInterfaceRequest:rDic];
                    
                }else{
                    
                    ((UILabel*)[self.witnessView viewWithTag:2002]).text = [NSString stringWithFormat:@"%@：%@",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服",uName];
                }
                
                UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
                
                linePromptLable.text = @"视频见证开启中...";
                
                [self startACVideo];
            }
            
        }else{
            
//            [self showACCountDown];
            if (countDownTime)
            {
                [countDownTime invalidate];
                countDownTime = nil;
            }
            countDownTime = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(showACCountDown) userInfo:nil repeats:YES];
        }
        
    }else{
        
        if (!self.isShowAlert) {
            self.isShowAlert = YES;
            UIAlertView *mAlertView =[[UIAlertView alloc] initWithTitle:@"" message:@"视频连接失败,请稍后重试" delegate:self cancelButtonTitle:@"确定" otherButtonTitles:nil, nil];
            mAlertView.tag = 103;
            [mAlertView show];
            
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_ConnServerErr.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_ConnServerErr.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
        }
        
    }
}

// 房间在线用户消息
- (void) OnAnyChatOnlineUser:(int) dwUserNum : (int) dwRoomId
{
    TKLogInfo(@"房间在线人数:%d",dwUserNum);
    //记录开始房间人数日志
    NSString *logString=[NSString stringWithFormat:@"TKMSG100901:网络情况%@|当前房间人数%d|%@",[TKNetHelper getNetworkTypeInfo],dwUserNum,[self getTimeStamp]];
    [self uploadLog:logString];
}

// 用户进入房间消息
- (void) OnAnyChatUserEnterRoom:(int) dwUserId
{
    NSString *l = [NSString stringWithFormat:@"用户[%@]进入房间",[AnyChatPlatform GetUserName:dwUserId]];
    
    TKLogInfo(@"%@", l);
    
    if (countDownTime) {
        [countDownTime invalidate];
        countDownTime = nil;
    }
    
    if (udTimer) {//启动上下行获取定时器
        [udTimer setFireDate:[NSDate date]];
    }
    
    NSMutableArray *onlineUser = [AnyChatPlatform GetOnlineUser];//获取房间中在线人数
    
    if (onlineUser.count > 0){
    
        for (NSNumber *uid in onlineUser) {
            
            if ([uid intValue] != acUserId) {
                
                acSeatId = [uid intValue];
                
                break;
            }
        }
        
        if (!isStartingVideo && acSeatId) {
            
            isStartingVideo = YES;
            
            TKLogInfo(@"坐席id=%d",acSeatId);
            
            NSString *uName = [AnyChatPlatform GetUserName:acSeatId];
            
            if (self.requestParams && [@"lianchu" isEqualToString:self.requestParams[@"securitiesName"]]) {
                
                NSMutableDictionary *rDic = [NSMutableDictionary dictionaryWithCapacity:5];
      
                rDic[@"url"]=self.requestParams[@"url"];
                
                rDic[@"funcNo"]=@"502000";
                
                rDic[@"video_num"]=uName;
                
                [self handleInterfaceRequest:rDic];
                
            }else{
                
                ((UILabel*)[self.witnessView viewWithTag:2002]).text = [NSString stringWithFormat:@"%@：%@",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服",uName];
            }
            
            UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
            
            linePromptLable.text = @"视频见证开启中...";
            
            [self startACVideo];
        }
        
    }else{

        if (!self.isShowAlert) {

            self.isShowAlert = YES;

            UIAlertView *mAlertView =[[UIAlertView alloc] initWithTitle:@"" message:[NSString stringWithFormat:@"未找到%@,请重试",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服"] delegate:self cancelButtonTitle:@"确定" otherButtonTitles:nil, nil];
            mAlertView.tag = 103;
            [mAlertView show];
        }
    }
}

// 用户退出房间消息
- (void) OnAnyChatUserLeaveRoom:(int) dwUserId
{
    NSString *l = [NSString stringWithFormat:@"[%@]离开房间",[AnyChatPlatform GetUserName:dwUserId]];
    
    TKLogInfo(@"%@",l);
    
    if (!isTransBufferMsg) {
        
        if (udTimer) {//暂停上下行获取定时器
            [udTimer setFireDate:[NSDate distantFuture]];
        }
        
        if (countDownTime)
        {
            [countDownTime invalidate];
            countDownTime = nil;
        }
        countDownTime = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(showACCountDown) userInfo:nil repeats:YES];
        
    }
}

- (void)showACCountDown{
    
    NSMutableArray *onlineUser = [AnyChatPlatform GetOnlineUser];
    
    if (onlineUser.count < 2) {
        
        if (nCountDown > 0) {
            TKLogInfo(@"show Count Down = %d", nCountDown);
            UILabel *upLable = (UILabel*)[self.witnessView viewWithTag:2003];
            upLable.text = [NSString stringWithFormat:@"%d",nCountDown];
            UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
            
            linePromptLable.text = [NSString stringWithFormat:@"等待%@进入...[%d]",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服",nCountDown];
            nCountDown--;
        }else{
            
            if (!self.isShowAlert)
            {
                self.isShowAlert = YES;
                
                UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"" message:[NSString stringWithFormat:@"%@匹配失败，请重新开始视频见证！",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服"] delegate:self cancelButtonTitle:@"确定" otherButtonTitles: nil];
                alert.tag=101;
                [alert show];
                
                [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_ConnServerErr.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_ConnServerErr.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
            }
        }
    }
    else
    {
        if (countDownTime) {
            [countDownTime invalidate];
            countDownTime = nil;
        }
        
    }
    
}

// 网络断开消息
- (void) OnAnyChatLinkClose:(int) dwErrorCode
{
    TKLogInfo(@"视频连接断开");
    
    if (!self.isShowAlert && !isTransBufferMsg) {
        
        self.isShowAlert = YES;
        
        UIAlertView *mAlertView =[[UIAlertView alloc] initWithTitle:@"" message:@"视频连接异常，请重试！" delegate:self cancelButtonTitle:@"确定" otherButtonTitles:nil, nil];
        mAlertView.tag = 105;
        [mAlertView show];
    }
    
}

- (void) OnLocalVideoInit:(id)session
{
    UIView *vView = [self.witnessView viewWithTag:3001];
    if ( self.requestParams[@"showVideoKinds"]&& [self.requestParams[@"showVideoKinds"] isEqualToString:@"0"]) {
         vView.layer.contents=(__bridge id)[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/video_photo.png",TK_OPEN_RESOURCE_NAME]].CGImage;
    }else{
        AVCaptureVideoPreviewLayer *localVideoSurface = [AVCaptureVideoPreviewLayer layerWithSession: (AVCaptureSession*)session];
        
        localVideoSurface.frame = CGRectMake(0, 0, vView.frame.size.width, vView.frame.size.height);
        
        localVideoSurface.videoGravity = AVLayerVideoGravityResizeAspectFill;
        
        localVideoSurface.backgroundColor = [UIColor darkGrayColor].CGColor;
        
        [vView.layer addSublayer: localVideoSurface];
    }

    
    if ([self.requestParams[@"isShowHeadRect"] isEqualToString:@"1"]) {
        //需要人脸指示框
        UIImageView *headIV = [[UIImageView alloc] initWithFrame:CGRectMake((self.view.frame.size.width - 300)/2, 100, 300, 378)];
        headIV.tag = 50000;
        [headIV setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/head_prompt_img.png", TK_OPEN_RESOURCE_NAME]]];
        [self.view addSubview:headIV];
    }
    
}

- (void) OnLocalVideoRelease:(id)sender
{}

///////////////////// protocol ///////////////////////////

////////////////////文字信息协议
-(void)OnAnyChatTextMsgCallBack:(int)dwFromUserid :(int)dwToUserid :(BOOL)bSecret :(NSString *)lpMsgBuf{
    
    NSRange userRange = [lpMsgBuf rangeOfString:@"USR:0:"];
    
    if (userRange.length>0) {
        
        NSString *msg = [lpMsgBuf substringFromIndex:userRange.location+userRange.length];
        
        [self showSeatMsgStr:msg];
        
    }
}

//展示坐席发送的消息
-(void)showSeatMsgStr:(NSString *)serverStr{
    
    if (serverStr != nil) {
        
        if ([self.requestParams[@"isMsgAppend"] isEqualToString:@"1"]) {
            UILabel *bottomLab = [self.witnessView viewWithTag:2004];
            [bottomLab setHidden:YES];
            if (!tipMsgAppendTextView) {
                //按消息文本追加方式显示
                float height=120;
                float y=self.view.frame.size.height-height-50;
                float x=0;
                float width=self.view.frame.size.width;
                tipMsgAppendTextView=[[UITextView alloc] initWithFrame:CGRectMake(x, y, width, height)];
                tipMsgAppendTextView.layoutManager.allowsNonContiguousLayout = NO;
                tipMsgAppendTextView.backgroundColor=[UIColor clearColor];
                [tipMsgAppendTextView setEditable:NO];
                
                UIView *tipBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
                
                NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.05f].CGColor,(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f].CGColor, nil];
                CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
                
                btoGradientLayer.frame = CGRectMake(0, 0, width, height);
                btoGradientLayer.startPoint = CGPointMake(0.5, 0);
                btoGradientLayer.endPoint = CGPointMake(0.5, 1);
                [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
                [tipBgView.layer addSublayer:btoGradientLayer]; //设置颜色渐变
                [self.view addSubview:tipBgView];
                [self.view addSubview:tipMsgAppendTextView];
                
                
            }
            
            UIView  *seatVideoSurface = [self.witnessView viewWithTag:2000];
            
            seatVideoSurface.frame = CGRectMake(seatVideoSurface.frame.origin.x, tipMsgAppendTextView.frame.origin.y-seatVideoSurface.frame.size.height, seatVideoSurface.frame.size.width, seatVideoSurface.frame.size.height);
            
            
            UILabel *seatLable = [self.witnessView viewWithTag:2002];
            
            seatLable.frame = CGRectMake(seatLable.frame.origin.x, tipMsgAppendTextView.frame.origin.y-seatLable.frame.size.height, seatLable.frame.size.width, seatLable.frame.size.height);
            
            NSString *tipName;
            if ([TKStringHelper isEmpty:self.tkNewTipString]) {
                self.tkNewTipString=serverStr;
            }else{
                
                self.tkNewTipString=[NSString stringWithFormat:@"%@\n%@",self.tkNewTipString,serverStr];
            }
            
            
            //创建富文本
            NSMutableAttributedString *attri = [[NSMutableAttributedString alloc] initWithString:self.tkNewTipString];
            [attri addAttribute:NSForegroundColorAttributeName value:[UIColor whiteColor] range:NSMakeRange(0, self.tkNewTipString.length)];
            [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFangSC-Regular" size:15] range:NSMakeRange(0, self.tkNewTipString.length)];
            
            if([TKStringHelper isNotEmpty:tipName]){
                [attri addAttribute:NSUnderlineStyleAttributeName value:[NSNumber numberWithInteger:NSUnderlineStyleSingle] range:[self.tkNewTipString rangeOfString:tipName]];
            }
            tipMsgAppendTextView.attributedText =attri;
            
            [tipMsgAppendTextView scrollRangeToVisible:NSMakeRange(tipMsgAppendTextView.attributedText.length, 1)];
        }else{
            UILabel *bottomLab = [self.witnessView viewWithTag:2004];
            
            NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
            
            paragraphStyle.lineBreakMode = NSLineBreakByWordWrapping;
            
            NSDictionary *attributes = @{NSFontAttributeName:bottomLab.font, NSParagraphStyleAttributeName:paragraphStyle.copy};
            
            CGSize lSize = [serverStr boundingRectWithSize:[UIScreen mainScreen].bounds.size
                                                   options:
                            NSStringDrawingUsesLineFragmentOrigin |
                            NSStringDrawingUsesFontLeading
                                                attributes:attributes
                                                   context:nil].size;
            
            UIView  *seatVideoSurface = [self.witnessView viewWithTag:2000];
            
            seatVideoSurface.frame = CGRectMake(seatVideoSurface.frame.origin.x, seatVideoSurface.frame.origin.y - lSize.height - 10 , seatVideoSurface.frame.size.width, seatVideoSurface.frame.size.height);
            
            UILabel *seatLable = [self.witnessView viewWithTag:2002];
            
            seatLable.frame = CGRectMake(seatLable.frame.origin.x, seatLable.frame.origin.y - lSize.height - 10 , seatLable.frame.size.width, seatLable.frame.size.height);
            
            bottomLab.frame = CGRectMake(0, bottomLab.frame.origin.y-lSize.height -10, self.view.bounds.size.width, lSize.height + 10);
            
            bottomLab.text = [NSString stringWithFormat:@"%@：%@",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服",serverStr];
            
            [self performSelector:@selector(clearSeatMsgStr:) withObject:nil afterDelay:8.0];
        }
        
        
       
    }
}

//清除坐席发送的消息
-(void)clearSeatMsgStr:(NSString *)serverStr{
    UILabel *bottomLab = [self.witnessView viewWithTag:2004];
    bottomLab.text =@"您好，见证开始了，请不要随意挂断或者离开";
    UIView  *seatVideoSurface = [self.witnessView viewWithTag:2000];
    
    seatVideoSurface.frame = CGRectMake(seatVideoSurface.frame.origin.x,  self.view.frame.size.height-237, seatVideoSurface.frame.size.width, seatVideoSurface.frame.size.height);
    
    UILabel *seatLable = [self.witnessView viewWithTag:2002];
    
    seatLable.frame = CGRectMake(seatLable.frame.origin.x, self.view.frame.size.height-117 , seatLable.frame.size.width, seatLable.frame.size.height);
    bottomLab.frame = CGRectMake(0, self.view.frame.size.height-67, self.view.bounds.size.width, 17);
}

////////////////////服务器消息协议
-(void)OnAnyChatSDKFilterDataCallBack:(NSData *)lpBuf{
    TKLogInfo(@"收到服务器数据");
    
}

-(void)OnAnyChatTransFileCallBack:(int)dwUserid :(NSString *)lpFileName :(NSString *)lpTempFilePath :(int)dwFileLength :(int)wParam :(int)lParam :(int)dwTaskId{
    TKLogInfo(@"有文件传输...");
}


-(void)OnAnyChatTransBufferCallBack:(int)dwUserid :(NSData *)lpBuf{
    
    if (!isTransBufferMsg) {
        
        isTransBufferMsg = YES;
        
        TKLogInfo(@"transBuffer callback");
        
        NSString *lpMsgBuf=  [[NSString alloc] initWithData:(lpBuf) encoding:NSUTF8StringEncoding];
        
        if (lpMsgBuf) {
            
            NSString *urlDecode = [lpMsgBuf stringByRemovingPercentEncoding];
            
            if (urlDecode) {
                
                lpMsgBuf = urlDecode;
            }
        }
        
        TKLogInfo(@"返回状态:%@", lpMsgBuf);
        
        self.witnessResult = lpMsgBuf;
        
        NSRange sysRange = [lpMsgBuf rangeOfString:@"SYS:"];
        
        if (sysRange.length > 0) {  //见证返回的透明信息
            
            self.aExit = NO;
            
        }else{ //其它消息
            
        }
        
        [self endACVideo];
        
    }
    
}

-(void)OnAnyChatTransBufferExCallBack:(int)dwUserid :(NSData *)lpBuf :(int)wParam :(int)lParam :(int)dwTaskId{
    TKLogInfo(@"transBuff Ex callback");
    
    if (!isTransBufferMsg) {
        
        isTransBufferMsg = YES;
        
        NSString *lpMsgBuf=  [[NSString alloc] initWithData:(lpBuf) encoding:NSUTF8StringEncoding];
        
        if (lpMsgBuf) {
            
            NSString *urlDecode = [lpMsgBuf stringByRemovingPercentEncoding];
            
            if (urlDecode) {
                
                lpMsgBuf = urlDecode;
            }
        }
        
        TKLogInfo(@"返回状态:%@", lpMsgBuf);
        
        self.witnessResult = lpMsgBuf;
        
        NSRange sysRange = [lpMsgBuf rangeOfString:@"SYS:"];
        
        if (sysRange.length > 0) {  //见证返回的透明信息
            
            self.aExit = NO;
            
        }else{ //其它消息
        }
        
        [self endACVideo];
    }
    
}

///////////////////////状态改变
-(void)OnAnyChatMicStateChg:(int)dwUserId :(BOOL)bGetMic{
    TKLogInfo(@"[%d]的mic状态改变-[%d]",dwUserId,bGetMic);
}

-(void)OnAnyChatCameraStateChg:(int)dwUserId :(int)dwState{
    TKLogInfo(@"[%d]摄像头状态改变为[%d]",dwUserId,dwState);
    
}

-(void)OnAnyChatActiveStateChg:(int)dwUserId :(int)dwState{
    TKLogInfo(@"[%d]状态改变为[%d]",dwUserId,dwState);
}

-(void)OnAnyChatP2PConnectState:(int)dwUserId :(int)dwState{
    switch (dwState) {
        case 0:
            TKLogInfo(@"当前无连接");
            break;
        case 1:
            TKLogInfo(@"TCP-P2P连接建立");
            break;
        case 2:
            TKLogInfo(@"UDP-P2P连接建立");
            break;
        case 3:
            TKLogInfo(@"UDP&TCP连接建立");
            break;
        default:
            break;
    }
}

-(void)OnAnyChatVideoSizeChg:(int)dwUserId :(int)dwWidth :(int)dwHeight{
    TKLogInfo(@"[%d]摄像头尺寸调整为(%d,%d)",dwUserId,dwWidth,dwHeight);
    
}

//启动anychat视频
- (void)startACVideo{

    [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_UnicomVideo.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_UnicomVideo.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
    
    NSMutableArray *cams = [AnyChatPlatform EnumVideoCapture];
    
    if(cams.count != 0){
        NSString *camName = (NSString*)[cams objectAtIndex:cams.count-1];
        TKLogInfo(@"切换摄像头为:%@",camName);
        [AnyChatPlatform SelectVideoCapture:camName];
    }else{
        TKLogInfo(@"该设备没有摄像头");
    }
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_NETWORK_P2PPOLITIC : 0];
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_LOCALVIDEO_APPLYPARAM :0];
    //本地摄像影像自动旋转
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_LOCALVIDEO_AUTOROTATION :1];
    //远端摄像影像不自动旋转
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_VIDEOSHOW_AUTOROTATION :0];
    // 静音检测
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_VADCTRL : 1];
    // 回音消除
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_ECHOCTRL : 1];
#ifdef  BRAC_SO_AUDIO_ECHOLEVEL
    //音频回声消除水平参数设置 =4（7.2以后）
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_ECHOLEVEL : 4];
#endif
    // 噪音抑制
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_NSCTRL : 1];
    // 自动增益
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_AGCCTRL : 1];
    
//#ifdef BRAC_SO_CORESDK_FITTENCENTLIVE
//    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_CORESDK_FITTENCENTLIVE : 1];
//#endif
    
    //开启本地硬件
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_LOCALVIDEO_OVERLAY :1];
    int selfAudioErrorCode=[AnyChatPlatform UserSpeakControl: -1 : YES];
    NSString *logAudioString=[NSString stringWithFormat:@"TKMSG1011:打开本地麦克风情况%d|%@",selfAudioErrorCode,[self getTimeStamp]];
    [self uploadLog:logAudioString];
    [AnyChatPlatform SetVideoPos: -1 :self : 0 : 0 : 0 : 0];
    int selfVideoErrorCode=[AnyChatPlatform UserCameraControl: -1 : YES];
    NSString *logVideoString=[NSString stringWithFormat:@"TKMSG1010:打开远本地摄像头情况%d|%@",selfVideoErrorCode,[self getTimeStamp]];
    [self uploadLog:logVideoString];
    

    //请求远端硬件
    int seatAudioErrorCode=[AnyChatPlatform UserSpeakControl: acSeatId : YES];
    NSString *logSeatAudioString=[NSString stringWithFormat:@"TKMSG1011:打开远程麦克风情况%d|%@",seatAudioErrorCode,[self getTimeStamp]];
    [self uploadLog:logSeatAudioString];
    
    if ( self.requestParams[@"showVideoKinds"]&& [self.requestParams[@"showVideoKinds"] isEqualToString:@"1"]) {
        UIView *vView=[self.witnessView viewWithTag:2001];
        vView.layer.contents=(__bridge id)[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/video_photo.png",TK_OPEN_RESOURCE_NAME]].CGImage;
    }else{
            [AnyChatPlatform SetVideoPos: acSeatId : [self.witnessView viewWithTag:2001] : 0 : 0 : 0 : 0];
    }

    //请求远端硬件
    int seatVideoErrorCode=[AnyChatPlatform UserCameraControl:  acSeatId : YES];
    NSString *logSeatVideoString=[NSString stringWithFormat:@"TKMSG1010:打开远程摄像头情况%d|%@",seatVideoErrorCode,[self getTimeStamp]];
    [self uploadLog:logSeatVideoString];
    
    udTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(featchACNetworkSpeed) userInfo:nil repeats:YES];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.waitView setHidden:YES];
    });
}

- (void)featchACNetworkSpeed
{
    float upstream = [AnyChatPlatform QueryUserStateInt:-1 :BRAC_USERSTATE_VIDEOBITRATE]/1000.0;
    
    float downstream = [AnyChatPlatform QueryUserStateInt:acSeatId :BRAC_USERSTATE_VIDEOBITRATE]/1000.0;
    
    UILabel *upLable = (UILabel*)[self.witnessView viewWithTag:2003];
    upLable.numberOfLines = 0;
    upLable.lineBreakMode = NSLineBreakByWordWrapping;
    upLable.text = [NSString stringWithFormat:@"上行:%.1fKB/S\n下行:%.1fKB/S",upstream,downstream];
}

//结束视频
- (void)endACVideo{
    
    TKLogInfo(@"end video");
    
    if (countDownTime) {
        [countDownTime invalidate];
        countDownTime = nil;
    }
    if (udTimer) {
        [udTimer invalidate];
        udTimer = nil;
    }
    
    if (!isTransBufferMsg) {
        //正常通过驳回无需调用退出排队接口
        [self stopLineingUp];
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.isCancelLineingUp) {}
        
        if (self.witRoomId) {
            if (!isTransBufferMsg) {
                [AnyChatPlatform TransBuffer:-1 :[@"SYS:10002" dataUsingEncoding:NSUTF8StringEncoding]];
            }
            [AnyChatPlatform UserSpeakControl: -1 : NO];
            [AnyChatPlatform UserCameraControl: -1 : NO];
            [AnyChatPlatform UserSpeakControl: acSeatId : NO];
            [AnyChatPlatform UserCameraControl: acSeatId : NO];
            [AnyChatPlatform LeaveRoom:-1];
            [AnyChatPlatform Logout];
            anychat = nil;
            [[NSNotificationCenter defaultCenter] removeObserver:self name:@"ANYCHATNOTIFY" object:nil];
        }
        
    });
    
    [self dismissViewControllerAnimated:YES completion:^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkVideoWitnessResult:)]) {
            
            [self.delegate tkVideoWitnessResult:self.witnessResult];
        }
    }];
}


@end
