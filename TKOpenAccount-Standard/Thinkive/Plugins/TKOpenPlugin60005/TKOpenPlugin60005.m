//
//  TKOpenPlugin60005.m
//  TKApp
//
//  Created by 叶璐 on 15/4/22.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKOpenPlugin60005.h"
#import "TKOpenAccountService.h"
#import "TKCommonUtil.h"

#import "UIViewController+TKAuthorityKit.h"
#import "TKOpenController.h"
#import "TKSmartTwoVideoController.h"
#import "TKDirectVideoModel.h"
//#import "TKChatControllerAC.h"
#import "TKVideoWitnessViewController.h"

#define codeTKChatResultPASS @"10000" //见证通过
#define codeTKChatResultFAIL @"10001" //见证不通过
#define codeTKChatResultRECHECKPASS @"10003" //复核通过
#define codeTKChatResultFORCEQUIT @"10004" //被退出
#define codeTKChatResultDataFAIL @"10009" //资料异常,见证不通过


@interface TKOpenPlugin60005()<UIAlertViewDelegate,TKVideoWitnessDelegate,TKSmartTwoVideoDelegate>
{
    NSMutableDictionary *jsParam;
     double tempInterval;
    NSString *extParamString;
}


@property (nonatomic, readwrite, assign) BOOL isInvokeing; // 是否正在被调用；怕3.0,4.0出问题，所以只针对5.0新ui做防重复调用
@end

@implementation TKOpenPlugin60005

/**
 *  <AUTHOR> 2014-11-27 12:11:58
 *
 *  功能描述：进行AnyChat双向视频
 *  参考插件：对应phoneGap的VideoPlugin插件
 *
 *  @param userId       用户ID
 *  @param userName         用户姓名
 *  @param orgId      营业部编号
 *  @param jsessionId       会话ID
 *  @param netWorkStatus         设备使用的网络状态
 *  @param url      连接排队BAS的服务器地址
 *
 *  @return 无
 */
-(ResultVo *)serverInvoke:(id)param
{
    dispatch_async(dispatch_get_main_queue(), ^{
        if (param && param[@"moduleName"]) {
            
            if (self.currentViewCtrl && [self.currentViewCtrl isKindOfClass:[TKBaseWebViewController class]]) {
                
                TKBaseWebViewController *webCtl = (TKBaseWebViewController*)self.currentViewCtrl;
                
                webCtl.tkName = param[@"moduleName"];
            }
        }
    });
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    
    NSString *userId = reqParam[@"userId"];
    NSString *userName = reqParam[@"userName"];
    NSString *orgId = reqParam[@"orgId"];
//    NSString *jsessionId = reqParam[@"jsessionId"];
    NSString *url = reqParam[@"url"];
//    NSString *netWorkStatus = reqParam[@"netWorkStatus"];
//    NSString *openVersion = reqParam[@"version"];
    ResultVo *resultVo = [[ResultVo alloc]init];
    jsParam = reqParam;
    
    if ([TKStringHelper isEmpty:userId]) {
        resultVo.errorNo = -6000501;
        resultVo.errorInfo = @"用户ID不能为空!";
        
        return resultVo;
    }
    
    if ([TKStringHelper isEmpty:userName]) {
        resultVo.errorNo = -6000502;
        resultVo.errorInfo = @"用户姓名不能为空!";
        
        return resultVo;
    }
    
    if ([TKStringHelper isEmpty:orgId]) {
        resultVo.errorNo = -6000503;
        resultVo.errorInfo = @"营业部编号不能为空!";
        
        return resultVo;
    }
  
    
    if ([TKStringHelper isEmpty:url]) {
        resultVo.errorNo = -6000505;
        resultVo.errorInfo = @"排队服务器地址不能为空!";
        
        return resultVo;
    }
    
    
    
    
    
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //是否在调用插件前展示介绍页面
        if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
            [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Camera),@(TKAuthorizationType_Audio)] authCallBacks:nil btnCallBack:^{
                [self.currentViewCtrl tkIsMicrophonePermissions:^{
                    
                    [self.currentViewCtrl tkIsCameraPermissions:^{
                            
                        [self jumpToVideoController:reqParam];
                    }];
                    
                }];
            }];
        }else{
            [self.currentViewCtrl tkIsMicrophonePermissions:^{
                
                [self.currentViewCtrl tkIsCameraPermissions:^{
                        
                    [self jumpToVideoController:reqParam];
                }];
                
            }];

        }


    });
    
 
    return resultVo;
}

- (void)jumpToVideoController:(id)h5Params
{

    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSDate *now = [NSDate date];
    tempInterval = [now timeIntervalSince1970];

    if ([@"3.0" isEqualToString:h5Params[@"version"]]) {//3.0视频排队处理
        //3.0和安卓保持一致，直接走新界面
        [self jumpNewVideo:h5Params];
    }else{
        //isNewView为0走新版界面，其他都走老板版界面
        if (![h5Params[@"isNewView"] isEqualToString:@"0"]) {
            
            [self jumpOldVideo:h5Params];
        }else{

            [self jumpNewVideo:h5Params];
        }
    }

    if ([self.currentViewCtrl isKindOfClass:[TKBaseWebViewController class]])
    {
        ((TKBaseWebViewController*)self.currentViewCtrl).isNeedReInitJSModule = NO;
    }
}


- (void)jumpOldVideo:(id)h5Params
{

    NSMutableDictionary *dic = [[NSMutableDictionary alloc] initWithCapacity:10];
    
    [dic addEntriesFromDictionary:h5Params];
    
    if (h5Params[@"userId"]) {
        dic[@"user_id"]=h5Params[@"userId"];
    }
    
    if (h5Params[@"userName"]) {
        dic[@"user_name"]=h5Params[@"userName"];
    }
    
    if (h5Params[@"orgId"]) {
        dic[@"branch_id"]=h5Params[@"orgId"];
    }
    
    if (h5Params[@"branchno"]) {
        dic[@"branch_id"]=h5Params[@"branchno"];
    }
    
    dic[@"business_id"]=h5Params[@"business_id"]? h5Params[@"business_id"]:@"fxckhWitness";
    
    [dic removeObjectForKey:@"funcNo"];
    
    dic[@"queueType"]=h5Params[@"queueType"]? h5Params[@"queueType"]:@"2";
    
    dic[@"videoType"]=h5Params[@"videoType"]? h5Params[@"videoType"]:@"1";
    
    if (h5Params[@"biz_type"] == nil) {
        dic[@"biz_type"]=h5Params[@"bizType"]? h5Params[@"bizType"]:@"1";
    }
    
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:@"TKOpenResource" withExtension:@"bundle"]];
    
    NSString *xibName = @"TKVideoWitnessViewController";
    
    if ([@"1" isEqualToString:h5Params[@"interfaceStyles"]]) {//3.0的界面
        
        xibName = @"TKAnyChatViewController";
        
    }else if ([@"2" isEqualToString:h5Params[@"interfaceStyles"]]) {
        
        xibName = @"TKAnyChatViewController4";
    }
    
    TKVideoWitnessViewController *vc = [[TKVideoWitnessViewController alloc] initWithParams:dic nibName:xibName bundle:bundle];
    
    vc.delegate = self;
    
    UIViewController *hCtl = self.currentViewCtrl;
    
    if (![TKCommonUtil isCurrentViewControllerVisible:self.currentViewCtrl]) {
        
        hCtl = [TKCommonUtil getCurrentVisibleVC];
    }
    
    if ([hCtl isKindOfClass:[TKOpenController class]] && h5Params[@"moduleName"]) {
        
        ((TKOpenController*)hCtl).tkName = h5Params[@"moduleName"];
    }
    
    //        if (hCtl.navigationController) {
    //
    //            [hCtl.navigationController pushViewController:vc animated:YES];
    //        }else{
    //这里只用present，因为推出都只用了dismiss这种
    [hCtl presentViewController:vc animated:YES completion:nil];
    //        }

}


- (void)jumpNewVideo:(id)h5Params{
    // 已在调用，拦截重复调用
    if (self.isInvokeing == YES) {
        return ;
    };
    // 标记正在调用
    self.isInvokeing = YES;
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSDate *now = [NSDate date];
    tempInterval = [now timeIntervalSince1970];
    
    NSMutableDictionary *dic = [[NSMutableDictionary alloc] initWithCapacity:10];
    
    [dic addEntriesFromDictionary:h5Params];
    
    if (h5Params[@"userId"]) {
        dic[@"user_id"]=h5Params[@"userId"];
    }
    
    if (h5Params[@"userName"]) {
        dic[@"user_name"]=h5Params[@"userName"];
    }
    
    if (h5Params[@"orgId"]) {
        dic[@"branch_id"]=h5Params[@"orgId"];
    }
    
    if (h5Params[@"branchno"]) {
        dic[@"branch_id"]=h5Params[@"branchno"];
    }
    
    dic[@"business_id"]=h5Params[@"business_id"]? h5Params[@"business_id"]:@"fxckhWitness";
    
    [dic removeObjectForKey:@"funcNo"];
    
    dic[@"queueType"]=h5Params[@"queueType"]? h5Params[@"queueType"]:@"2";
    dic[@"videoType"]=h5Params[@"videoType"]? h5Params[@"videoType"]:@"1";
    
    if (h5Params[@"biz_type"] == nil) {
        dic[@"biz_type"]=h5Params[@"bizType"]? h5Params[@"bizType"]:@"1";
    }
    
    TKSmartTwoVideoController *videoCtr=[[TKSmartTwoVideoController alloc] initWithParam:dic];
    videoCtr.delegate=self;
    [self.currentViewCtrl presentViewController:videoCtr animated:YES completion:nil];

}


- (void)tkChatDelegateResult:(NSString*)statusCode statusMsg:(NSString*)sMsg{
    TKLogInfo(@"tkChatDelegateResult begin");
    
    NSString *_ret = statusCode;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        [UIApplication sharedApplication].idleTimerDisabled = NO;
        
    });
    
    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
    
    reqParam[@"funcNo"]=@"60051";
    
    if ([@"1" isEqualToString:jsParam[@"isRejectToH5"]]) {
        NSString *wResultDecode=  [statusCode stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
        reqParam[@"videoFlag"]=wResultDecode;
        
        reqParam[@"message"]=wResultDecode;
        
        NSDate *nowEnd = [NSDate date];
        tempInterval = [nowEnd timeIntervalSince1970] - tempInterval;
        reqParam[@"i_step_time"]=[NSNumber numberWithInteger:tempInterval*1000];
        [self iosCallJsWithParam:reqParam];
        
        return;
    }

    NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
    id extParams = jsParam[@"extParams"];
    if (extParams && extParams[@"phoneNum"]) {
        [bpDic addEntriesFromDictionary:extParams];
    }
    if ([_ret isEqualToString:@"1"] || [_ret isEqualToString:@"0"] || [_ret isEqualToString:@"2"] || [_ret isEqualToString:@"-1"]) {
        
        TKLogInfo(@"用户取消排队或挂断视频或接通中");
        if ([_ret isEqualToString:@"0"]){
            if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CLineing.objectId",@"open"]]) {
                if (extParams[@"i_business_type"]) {
                    bpDic[@"i_group_name"]=extParams[@"i_business_type"];
                }
                [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CLineing.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CLineing.actionId",@"open"]] attributes:bpDic];
            }
        }else if ([_ret isEqualToString:@"-1"]){
            if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId",@"open"]]) {
                bpDic[@"i_break_type"]=@"3";
                bpDic[@"i_break_msg"]=@"视频异常断开";
                bpDic[@"i_reject_flow"]=@"2";
                [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId",@"open"]] attributes:bpDic];
            }
        }else{
            if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CWitness.objectId",@"open"]]) {
                bpDic[@"i_break_type"]=@"2";
                bpDic[@"i_break_msg"]=@"用户挂断视频";
                [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CWitness.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CWitness.actionId", @"open"]] attributes:bpDic];
            }
        }
        
        return;
        
    }else if ([_ret rangeOfString:codeTKChatResultPASS].length > 0 || [_ret rangeOfString:codeTKChatResultRECHECKPASS].length > 0)
    {
        reqParam[@"videoFlag"]=@"0";
        
        if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WSuccess.objectId", @"open"]]) {
            NSDate *now = [NSDate date];
            tempInterval = [now timeIntervalSince1970] - tempInterval;
            bpDic[@"i_step_time"]=[NSNumber numberWithInteger:tempInterval*1000];
            bpDic[@"i_open_step"]=@"4";
            bpDic[@"i_step_type"]=@"3";
            bpDic[@"i_is_reject"]=@"0";
            bpDic[@"i_step_desc"]=@"视频见证通过";
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WSuccess.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WSuccess.actionId", @"open"]] attributes:bpDic];
        }
    }
    else if ([_ret rangeOfString:codeTKChatResultDataFAIL].length > 0)
    {
        reqParam[@"videoFlag"]=@"2";
        if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]]) {
            bpDic[@"i_break_type"]=@"1";
            bpDic[@"i_break_msg"]=@"资料异常或不完整,请重新填写资料";
            bpDic[@"i_reject_flow"]=@"1";
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        }
    }else{
    
        reqParam[@"videoFlag"]=_ret;
        
        if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]]) {
            bpDic[@"i_break_type"]=@"1";
            if ([_ret rangeOfString:@"SYS:10001"].length > 0){
                bpDic[@"i_break_msg"]=@"直接驳回";
            }else if ([_ret rangeOfString:@"SYS:10007"].length > 0){
                bpDic[@"i_break_msg"]=@"网络异常，请您选择较好的网络进行视频见证";
            }else if ([_ret rangeOfString:@"SYS:10008"].length > 0){
                bpDic[@"i_break_msg"]=@"视频录制异常,坐席视频录制出现异常,请稍后再试";
            }else{
                bpDic[@"i_break_msg"]=@"视频异常被驳回";
            }
            bpDic[@"i_reject_flow"]=@"2";
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        }
        
        TKLayerView  *layerView=[[TKLayerView alloc] initContentView:[[[UIApplication sharedApplication] windows] lastObject] withBtnTextColor:nil];
        [layerView showTip:@"见证失败，请重试" position:TKLayerPosition_Bottom];
    }
    
    reqParam[@"message"]=_ret;
    
    [self iosCallJsWithParam:reqParam];
}

- (void)tkChatWitnessResult:(NSString *)wResult
{
    // 重置标志位
    self.isInvokeing = NO;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        [UIApplication sharedApplication].idleTimerDisabled = NO;
    });
    
    if(wResult && [wResult rangeOfString:codeTKChatResultPASS].length >0)
    {//视频见证通过埋点
        NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
        bpDic[@"eventType"]=@"2";
        bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.10.4526",[TKCommonUtil fetchAppStatisticsMarker]];
        bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.10.917",[TKCommonUtil fetchAppStatisticsMarker]];
        [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50405" param:bpDic moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
    }

    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
    
    reqParam[@"funcNo"]=@"60051";
    
    reqParam[@"error_no"]=@"0";
    
    if ([TKStringHelper isNotEmpty:extParamString]) {
        //存在需要返回给h5的排队扩展参数
        reqParam[@"extParam"]=extParamString;
    }
    
    if ([@"1" isEqualToString:jsParam[@"isRejectToH5"]]) {
       NSString *wResultDecode=  [wResult stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
//        reqParam[@"videoFlag"]=wResultDecode;
//
//        reqParam[@"message"]=wResultDecode;

        if ([wResultDecode isEqualToString:@"-999"]||[wResultDecode isEqualToString:@"920"]||[wResultDecode isEqualToString:@"922"]||[wResultDecode isEqualToString:@"924"]) {
//            reqParam[@"error_no"]=@"-999";
//            reqParam[@"error_info"]=@"因为客户端长时间没有操作，您需要重新登录！";
            [self sendToJS:reqParam errorNo:@"-999" errorInfo:@"因为客户端长时间没有操作，您需要重新登录！" videoFlag:wResultDecode message:wResultDecode rejectReason:nil];
            [self sendStaticEventWith:@"因为客户端长时间没有操作，您需要重新登录！" videoFlag:@"-999"];
        }else if ([wResultDecode isEqualToString:@"-11"]) {
//            reqParam[@"error_no"]=@"-11";
//            reqParam[@"error_info"]=@"排队人数超限客户选择走单向";
            [self sendToJS:reqParam errorNo:@"-11" errorInfo:@"排队人数超限客户选择走单向" videoFlag:wResultDecode message:wResultDecode rejectReason:nil];
            [self sendStaticEventWith:@"排队人数超限客户选择走单向" videoFlag:@"-11"];
        }else if ([wResultDecode isEqualToString:@"20000"]) {
            if([TKDirectVideoModel shareInstance].isQueueStaffExist){
//                reqParam[@"error_no"]=@"-1";
//                reqParam[@"error_info"]=@"用户主动退出";
                [self sendToJS:reqParam errorNo:@"-1" errorInfo:@"用户主动退出" videoFlag:wResultDecode message:wResultDecode rejectReason:nil];
                [self sendStaticEventWith:@"用户主动退出" videoFlag:@"-1"];
            }else{
//                reqParam[@"error_no"]=@"-10";
//                reqParam[@"error_info"]=@"无坐席";
                [self sendToJS:reqParam errorNo:@"-10" errorInfo:@"无坐席" videoFlag:wResultDecode message:wResultDecode rejectReason:nil];
                [self sendStaticEventWith:@"无坐席" videoFlag:@"-10"];
            }
        }else if ([wResultDecode isEqualToString:@"-2"]||[wResultDecode isEqualToString:@"-3"]||[wResultDecode isEqualToString:@"-4"]||[wResultDecode isEqualToString:@"-5"]||[wResultDecode isEqualToString:@"-6"]||[wResultDecode isEqualToString:@"-7"]||[wResultDecode isEqualToString:@"-9"]) {
//            reqParam[@"error_no"]=[TKDirectVideoModel shareInstance].witnessResult;
//            reqParam[@"error_info"]=[TKDirectVideoModel shareInstance].witnessInfo;
            [self sendToJS:reqParam errorNo:[TKDirectVideoModel shareInstance].witnessResult errorInfo:[TKDirectVideoModel shareInstance].witnessInfo videoFlag:wResultDecode message:wResultDecode rejectReason:nil];
            [self sendStaticEventWith:[TKDirectVideoModel shareInstance].witnessInfo videoFlag:wResultDecode];
        }else{
            [self sendToJS:reqParam errorNo:nil errorInfo:nil videoFlag:wResultDecode message:wResultDecode rejectReason:nil];
        }

        NSDate *nowEnd = [NSDate date];
        tempInterval = [nowEnd timeIntervalSince1970] - tempInterval;
        reqParam[@"i_step_time"]=[NSNumber numberWithInteger:tempInterval*1000];

//        [self iosCallJsWithParam:reqParam];

        return;
    }
    
    NSString *status;
    
    if ([wResult rangeOfString:@":"].location != NSNotFound) {
        
        status = [wResult componentsSeparatedByString:@":"][1];
        
    }else{
        
        status = wResult;
    }
    
//    NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
//    id extParams = jsParam[@"extParams"];
//    if (extParams && extParams[@"phoneNum"]) {
//        [bpDic addEntriesFromDictionary:extParams];
//    }
    
    if([status isEqualToString:codeTKChatResultPASS] || [status isEqualToString:codeTKChatResultRECHECKPASS])
    {
//        reqParam[@"videoFlag"]=@"0";
        [self sendStaticEventWith:nil videoFlag:@"0"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"0" message:status rejectReason:nil];
    }
//    else if ([status isEqualToString:@"20000"]){//取消排队
//        if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CLineing.objectId",@"open"]]) {
////            if (extParams[@"i_business_type"]) {
////                bpDic[@"i_group_name"]=extParams[@"i_business_type"];
////            }
////            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CLineing.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CLineing.actionId",@"open"]] attributes:bpDic];
//            [self sendStaticEventWith:nil videoFlag:@"-1"];
//        }
//        return;
//
//    }
    else if ([status isEqualToString:@"20001"]){
        
//        reqParam[@"videoFlag"]=@"1";
//
//        reqParam[@"rejectReason"]=@"没有公安头像";
        
//        bpDic[@"rejectReason"]=@"没有公安头像";
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"没有公安头像" videoFlag:@"1"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"1" message:status rejectReason:@"没有公安头像"];
    }else if ([status isEqualToString:@"20002"]){
        
//        reqParam[@"videoFlag"]=@"2";
//
//        reqParam[@"rejectReason"]=@"证件正面不合规";
        
//        bpDic[@"rejectReason"]=@"证件正面不合规";
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"证件正面不合规" videoFlag:@"2"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"2" message:status rejectReason:@"证件正面不合规"];
    }else if ([status isEqualToString:@"20003"]){

//        reqParam[@"videoFlag"]=@"3";
//
//        reqParam[@"rejectReason"]=@"证件反面不合规";
//
//        bpDic[@"rejectReason"]=@"证件反面不合规";
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"证件反面不合规"  videoFlag:@"3"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"3" message:status rejectReason:@"证件反面不合规"];
    }
    else if ([status isEqualToString:@"20004"]){
        
//        reqParam[@"videoFlag"]=@"4";
//
//        reqParam[@"rejectReason"]=@"证件正反面都不合规" ;
        
//        bpDic[@"rejectReason"]=@"证件正反面都不合规" ;
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"证件正反面都不合规"  videoFlag:@"4"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"4" message:status rejectReason:@"证件正反面都不合规" ];
        
    }else if ([status isEqualToString:@"20005"]){
//        reqParam[@"videoFlag"]=@"5";
//
//        reqParam[@"rejectReason"]=@"客户不是本人";
        
//        bpDic[@"rejectReason"]=@"客户不是本人";
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"客户不是本人"  videoFlag:@"5"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"5" message:status rejectReason:@"客户不是本人"];
    }else if ([status isEqualToString:@"20006"]){
        
//        reqParam[@"videoFlag"]=@"6";
//
//        reqParam[@"rejectReason"]=@"客户环境不符合要求";
        
//        bpDic[@"rejectReason"]=@"客户环境不符合要求";
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"客户环境不符合要求" videoFlag:@"6"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"6" message:status rejectReason:@"客户环境不符合要求"];
    }else if ([status isEqualToString:@"-999"]||[status isEqualToString:@"920"]||[status isEqualToString:@"922"]||[status isEqualToString:@"924"]){
        
        
        
//        reqParam[@"error_no"]=@"-999";
//        reqParam[@"error_info"]=@"因为客户端长时间没有操作，您需要重新登录！";
//
//        reqParam[@"videoFlag"]=@"-999";
//
//        reqParam[@"rejectReason"]=@"因为客户端长时间没有操作，您需要重新登录！";
        
//        bpDic[@"rejectReason"]=@"因为客户端长时间没有操作，您需要重新登录！";
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"因为客户端长时间没有操作，您需要重新登录！" videoFlag:@"-999"];
        [self sendToJS:reqParam errorNo:@"-999" errorInfo:@"因为客户端长时间没有操作，您需要重新登录！" videoFlag:@"-999" message:status rejectReason:@"因为客户端长时间没有操作，您需要重新登录！"];
    }else if ([status isEqualToString:@"-11"]) {
//        reqParam[@"error_no"]=@"-11";
//        reqParam[@"error_info"]=@"排队人数超限客户选择走单向";
        
        [self sendStaticEventWith:@"排队人数超限客户选择走单向" videoFlag:@"-11"];
        [self sendToJS:reqParam errorNo:@"-11" errorInfo:nil videoFlag:nil message:status rejectReason:@"排队人数超限客户选择走单向"];
    }else if ([status isEqualToString:@"20000"]) {
        if([TKDirectVideoModel shareInstance].isQueueStaffExist){
//            reqParam[@"error_no"]=@"-1";
//            reqParam[@"error_info"]=@"用户主动退出";
            [self sendToJS:reqParam errorNo:@"-1" errorInfo:nil videoFlag:nil message:status rejectReason:@"用户主动退出"];
            [self sendStaticEventWith:@"用户主动退出" videoFlag:@"-1"];
        }else{
//            reqParam[@"error_no"]=@"-10";
//            reqParam[@"error_info"]=@"无坐席";
            [self sendToJS:reqParam errorNo:@"-10" errorInfo:nil videoFlag:nil message:status rejectReason:@"无坐席"];
            [self sendStaticEventWith:@"无坐席" videoFlag:@"-10"];
        }
        
    }else if ([status isEqualToString:@"-2"]||[status isEqualToString:@"-3"]||[status isEqualToString:@"-4"]||[status isEqualToString:@"-5"]||[status isEqualToString:@"-6"]||[status isEqualToString:@"-7"]||[status isEqualToString:@"-9"]) {
        reqParam[@"error_no"]=[TKDirectVideoModel shareInstance].witnessResult;
        reqParam[@"error_info"]=[TKDirectVideoModel shareInstance].witnessInfo;
        
        [self sendStaticEventWith:[TKDirectVideoModel shareInstance].witnessInfo videoFlag:status];
        [self sendToJS:reqParam errorNo:[TKDirectVideoModel shareInstance].witnessResult errorInfo:[TKDirectVideoModel shareInstance].witnessInfo videoFlag:nil message:status rejectReason:nil];
    }else if ([status isEqualToString:@"30001"]){
        
//        reqParam[@"videoFlag"]=@"7";
//
//        reqParam[@"rejectReason"]=@"请重新排队";
        
//        bpDic[@"rejectReason"]=@"请重新排队";
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"请重新排队" videoFlag:@"7"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"7" message:status rejectReason:@"请重新排队"];
    }else if ([status isEqualToString:@"20008"]){
        
//        reqParam[@"videoFlag"]=@"8";
//
//        reqParam[@"rejectReason"]=@"营业部不合规";
        
//        bpDic[@"rejectReason"]=@"营业部不合规";
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
        [self sendStaticEventWith:@"营业部不合规" videoFlag:@"8"];
        [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"8" message:status rejectReason:@"营业部不合规"];
    }else{
        //其它驳回原因
//        reqParam[@"videoFlag"]=@"2";
        
        NSRange range = [wResult rangeOfString:@":"];
        
        if (range.length > 0) {
            
            NSArray *reason = [wResult componentsSeparatedByString:@":"];
            
            if (reason && [reason count] >= 3) {
                
//                reqParam[@"rejectReason"]=[reason[2] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
                
//                bpDic[@"rejectReason"]=[reason[2] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
                [self sendStaticEventWith:[reason[2] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding] videoFlag:@"2"];
                [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"2" message:status rejectReason:[reason[2] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding]];
            }
        }else{
            
//            reqParam[@"rejectReason"]=@"见证失败";
            
//            bpDic[@"rejectReason"]=@"见证失败";
            [self sendStaticEventWith:@"见证失败" videoFlag:@"2"];
            [self sendToJS:reqParam errorNo:@"0" errorInfo:nil videoFlag:@"2" message:status rejectReason:@"见证失败"];
        }
       
//        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.objectId", @"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WFailure.actionId", @"open"]] attributes:bpDic];
    }
//    reqParam[@"message"]=status;
//    [self iosCallJsWithParam:reqParam];
}



-(void)sendToJS:(NSMutableDictionary *)reqParam errorNo:(NSString *)errorNo errorInfo:(NSString *)errorInfo videoFlag:(NSString *)videoFlag message:(NSString *)msg rejectReason:(NSString *)rejectReason{
    if ([TKStringHelper isNotEmpty:errorNo]) reqParam[@"error_no"]=errorNo;
    reqParam[@"error_info"]=errorInfo;
    reqParam[@"videoFlag"]=videoFlag;
    reqParam[@"message"]=msg;
    reqParam[@"rejectReason"]=rejectReason;
    [self iosCallJsWithParam:reqParam];
}

- (void)sendStaticEventWith:(NSString *)rejectReason videoFlag:(NSString *)videoFlag
{
    NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
    id extParams = jsParam[@"extParams"];
    if (extParams && extParams[@"phoneNum"]) {
        [bpDic addEntriesFromDictionary:extParams];
    }
    
    if (extParams[@"i_business_type"]) {
        bpDic[@"i_group_name"]=extParams[@"i_business_type"];
    }
    
    if (rejectReason) {
        bpDic[@"rejectReason"] = rejectReason;
    }
    [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CLineing.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_CLineing.actionId",@"open"]] attributes:bpDic];
    
    if ([videoFlag isEqualToString:@"0"]) {
        // 埋点-双向-结果-成功
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:jsParam];
        eventDic[@"errorNo"] = @"0";
        eventDic[@"event_err"] = @"";
        eventDic[@"enqueueSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isEnqueueSuccess];
        eventDic[@"connectSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isStartingVideo];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventNone
                                 progress:TKPrivateEventProgressEnd
                                   result:TKPrivateEventResultSuccess
                                 eventDic:eventDic];
    } else if ([videoFlag isEqualToString:@"-1"] || [videoFlag isEqualToString:@"-10"]) {
        // 埋点-双向-排队-取消
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:jsParam];
        eventDic[@"errorNo"] = videoFlag;
        eventDic[@"event_err"] = rejectReason;
        eventDic[@"enqueueSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isEnqueueSuccess];
        eventDic[@"connectSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isStartingVideo];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventNone
                                 progress:TKPrivateEventProgressEnd
                                   result:TKPrivateEventResultCancel
                                 eventDic:eventDic];
    } else if ([videoFlag isEqualToString:@"-2"] || [videoFlag isEqualToString:@"-9"]  || [videoFlag isEqualToString:@"-999"] || [videoFlag isEqualToString:@"-11"]) {
        // 埋点-双向-排队-异常
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:jsParam];
        eventDic[@"errorNo"] = videoFlag;
        eventDic[@"event_err"] = rejectReason;
        eventDic[@"enqueueSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isEnqueueSuccess];
        eventDic[@"connectSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isStartingVideo];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventNone
                                 progress:TKPrivateEventProgressEnd
                                   result:TKPrivateEventResultError
                                 eventDic:eventDic];
    }  else if ([videoFlag isEqualToString:@"-3"] || [videoFlag isEqualToString:@"-4"]  || [videoFlag isEqualToString:@"-5"]) {
        // 埋点-双向-连接-异常
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:jsParam];
        eventDic[@"errorNo"] = videoFlag;
        eventDic[@"event_err"] = rejectReason;
        eventDic[@"enqueueSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isEnqueueSuccess];
        eventDic[@"connectSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isStartingVideo];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventNone
                                 progress:TKPrivateEventProgressEnd
                                   result:TKPrivateEventResultError
                                 eventDic:eventDic];
    } else if ([videoFlag isEqualToString:@"-6"] || [videoFlag isEqualToString:@"-7"]) {
        // 埋点-双向-结果-异常
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:jsParam];
        eventDic[@"errorNo"] = videoFlag;
        eventDic[@"event_err"] = rejectReason;
        eventDic[@"enqueueSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isEnqueueSuccess];
        eventDic[@"connectSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isStartingVideo];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventNone
                                 progress:TKPrivateEventProgressEnd
                                   result:TKPrivateEventResultError
                                 eventDic:eventDic];
    } else {
        // 埋点-双向-结果-失败
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:jsParam];
        eventDic[@"errorNo"] = videoFlag;
        eventDic[@"event_err"] = rejectReason;
        eventDic[@"enqueueSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isEnqueueSuccess];
        eventDic[@"connectSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isStartingVideo];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventNone
                                 progress:TKPrivateEventProgressEnd
                                   result:TKPrivateEventResultFail
                                 eventDic:eventDic];
    }
    
}

- (void)tkVideoWitnessResult:(NSString *)wResult
{
    TKLogInfo(@"%@", wResult);
    
    [self tkChatWitnessResult:wResult];
}

- (void)tkSmartTwoResult:(NSString *)wResult extParam:(NSString *)extParam{
    extParamString=extParam;
    
    
    if ([@"3.0" isEqualToString:jsParam[@"version"]]) {//回调处理,方便华安h5不改isRejectToH5走老参数
        [self tkChatDelegateResult:wResult statusMsg:nil];
    }else{
        [self tkChatWitnessResult:wResult];
    }
}

-(void)didReceiveMemoryWarning
{}

@end
