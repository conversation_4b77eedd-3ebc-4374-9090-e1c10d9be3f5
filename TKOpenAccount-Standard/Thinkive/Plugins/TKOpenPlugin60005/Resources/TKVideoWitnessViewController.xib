<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14460.31" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_5" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14460.20"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="TKAnyChatViewController">
            <connections>
                <outlet property="view" destination="iN0-l3-epB" id="Y7y-kP-PYa"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QDD-1Z-KVc">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                    <subviews>
                        <view tag="3001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zlK-ag-lTS">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                            <color key="backgroundColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" tag="2003" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="u0Q-l3-Dt1">
                            <rect key="frame" x="10" y="20" width="334" height="60"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="60" id="ror-HM-z6z"/>
                            </constraints>
                            <string key="text">上行:0.0kb/s
下行:0.0kb/s</string>
                            <fontDescription key="fontDescription" name=".AppleSystemUIFont" family=".AppleSystemUIFont" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" tag="3002" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TsM-cL-UvQ">
                            <rect key="frame" x="344" y="20" width="60" height="60"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="60" id="eZW-nD-Kwz"/>
                                <constraint firstAttribute="height" constant="60" id="lHD-f6-HC6"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="18"/>
                            <state key="normal" title="挂 断">
                                <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                        </button>
                        <view tag="2000" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rss-sW-TOb">
                            <rect key="frame" x="204" y="710" width="200" height="128"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" tag="2001" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="PAs-fZ-d9j">
                                    <rect key="frame" x="0.0" y="0.0" width="200" height="128"/>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="200" id="8ta-4b-067"/>
                                <constraint firstItem="PAs-fZ-d9j" firstAttribute="top" secondItem="rss-sW-TOb" secondAttribute="top" id="GaJ-Z7-pZ9"/>
                                <constraint firstAttribute="trailing" secondItem="PAs-fZ-d9j" secondAttribute="trailing" id="Rrg-Mt-gEt"/>
                                <constraint firstAttribute="height" constant="128" id="cmd-M4-DpI"/>
                                <constraint firstAttribute="bottom" secondItem="PAs-fZ-d9j" secondAttribute="bottom" id="f87-z1-z2A"/>
                                <constraint firstItem="PAs-fZ-d9j" firstAttribute="leading" secondItem="rss-sW-TOb" secondAttribute="leading" id="mYZ-7V-gBh"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" alpha="0.5" tag="2002" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="坐席:ad" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nN2-IW-MFl">
                            <rect key="frame" x="204" y="817" width="200" height="21"/>
                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="200" id="Hpm-Ns-rIq"/>
                                <constraint firstAttribute="height" constant="21" id="wN6-Rn-p3q"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" tag="2004" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="正在见证，请按照客服提示操作，不要随意挂断" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ohz-rD-rqo">
                            <rect key="frame" x="0.0" y="864" width="414" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="rss-sW-TOb" secondAttribute="trailing" constant="10" id="04I-7r-kOH"/>
                        <constraint firstItem="zlK-ag-lTS" firstAttribute="centerX" secondItem="QDD-1Z-KVc" secondAttribute="centerX" id="6Re-re-ajf"/>
                        <constraint firstAttribute="trailing" secondItem="TsM-cL-UvQ" secondAttribute="trailing" constant="10" id="Cju-fv-L1q"/>
                        <constraint firstItem="Ohz-rD-rqo" firstAttribute="centerX" secondItem="QDD-1Z-KVc" secondAttribute="centerX" id="JWG-6R-5g2"/>
                        <constraint firstItem="zlK-ag-lTS" firstAttribute="top" secondItem="QDD-1Z-KVc" secondAttribute="top" id="KSU-JN-dpK"/>
                        <constraint firstItem="u0Q-l3-Dt1" firstAttribute="leading" secondItem="QDD-1Z-KVc" secondAttribute="leading" constant="10" id="YcG-nP-4L1"/>
                        <constraint firstItem="Ohz-rD-rqo" firstAttribute="top" secondItem="nN2-IW-MFl" secondAttribute="bottom" constant="26" id="avK-Ce-8rP"/>
                        <constraint firstItem="u0Q-l3-Dt1" firstAttribute="top" secondItem="QDD-1Z-KVc" secondAttribute="top" constant="20" id="btI-FS-LEH"/>
                        <constraint firstAttribute="trailing" secondItem="zlK-ag-lTS" secondAttribute="trailing" id="cKO-dp-iI8"/>
                        <constraint firstAttribute="bottom" secondItem="Ohz-rD-rqo" secondAttribute="bottom" constant="15" id="g8S-DB-Rbd"/>
                        <constraint firstAttribute="bottom" secondItem="zlK-ag-lTS" secondAttribute="bottom" id="gn0-vJ-Z4D"/>
                        <constraint firstItem="TsM-cL-UvQ" firstAttribute="leading" secondItem="u0Q-l3-Dt1" secondAttribute="trailing" id="iHE-P8-v8q"/>
                        <constraint firstItem="Ohz-rD-rqo" firstAttribute="leading" secondItem="QDD-1Z-KVc" secondAttribute="leading" id="lwv-Kh-LgD"/>
                        <constraint firstAttribute="trailing" secondItem="Ohz-rD-rqo" secondAttribute="trailing" id="rwD-Iu-4cG"/>
                        <constraint firstItem="Ohz-rD-rqo" firstAttribute="top" secondItem="rss-sW-TOb" secondAttribute="bottom" constant="26" id="sc5-IC-lT6"/>
                        <constraint firstItem="zlK-ag-lTS" firstAttribute="centerY" secondItem="QDD-1Z-KVc" secondAttribute="centerY" id="xOa-Us-FeB"/>
                        <constraint firstItem="TsM-cL-UvQ" firstAttribute="top" secondItem="QDD-1Z-KVc" secondAttribute="top" constant="20" id="ycA-ee-VKx"/>
                        <constraint firstAttribute="trailing" secondItem="nN2-IW-MFl" secondAttribute="trailing" constant="10" id="ylz-7F-y9F"/>
                        <constraint firstItem="zlK-ag-lTS" firstAttribute="leading" secondItem="QDD-1Z-KVc" secondAttribute="leading" id="yqp-5f-81B"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="i9V-lf-cDv">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                    <subviews>
                        <view tag="1002" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gxf-fX-gg0">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                            <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="calibratedWhite"/>
                        </view>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="kefu_bg_img.png" translatesAutoresizingMaskIntoConstraints="NO" id="GUe-zr-L3i">
                            <rect key="frame" x="10" y="50" width="65" height="65"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="65" id="6wR-8T-4Kt"/>
                                <constraint firstAttribute="width" constant="65" id="pDL-oJ-PAR"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" tag="2005" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="客服" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QGF-XQ-alJ">
                            <rect key="frame" x="85" y="53" width="319" height="30"/>
                            <fontDescription key="fontDescription" type="system" pointSize="25"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="正在为您连接客服，请耐心等待..." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fTo-Kb-bP5">
                            <rect key="frame" x="85" y="93" width="319" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view alpha="0.5" tag="1006" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MlT-KS-3SQ">
                            <rect key="frame" x="14" y="418" width="386" height="60"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" tag="1004" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="视频连接中..." textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8NP-21-rcJ">
                                    <rect key="frame" x="10" y="20.333333333333314" width="366" height="19.333333333333329"/>
                                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstItem="8NP-21-rcJ" firstAttribute="centerX" secondItem="MlT-KS-3SQ" secondAttribute="centerX" id="2nd-FH-kAo"/>
                                <constraint firstItem="8NP-21-rcJ" firstAttribute="leading" secondItem="MlT-KS-3SQ" secondAttribute="leading" constant="10" id="4Rh-Y5-Gri"/>
                                <constraint firstItem="8NP-21-rcJ" firstAttribute="centerY" secondItem="MlT-KS-3SQ" secondAttribute="centerY" id="9NJ-F6-bCL"/>
                                <constraint firstAttribute="trailing" secondItem="8NP-21-rcJ" secondAttribute="trailing" constant="10" id="eRo-ZX-9ta"/>
                                <constraint firstAttribute="height" constant="60" id="urD-yB-i2R"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="5"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chat_cancel_btn.png" translatesAutoresizingMaskIntoConstraints="NO" id="zru-H6-3lo">
                            <rect key="frame" x="172" y="728" width="70" height="70"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="70" id="7Xy-Hx-g0d"/>
                                <constraint firstAttribute="width" constant="70" id="p0Y-Yw-kjh"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="取消" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3Lk-GI-doI">
                            <rect key="frame" x="172" y="806" width="70" height="44"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="70" id="XZV-D1-6QA"/>
                                <constraint firstAttribute="height" constant="44" id="gae-z1-UI8"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" tag="1005" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qEp-5S-XAH">
                            <rect key="frame" x="177" y="728" width="60" height="114"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="114" id="Fck-90-9zU"/>
                                <constraint firstAttribute="width" constant="60" id="qKJ-8P-xD8"/>
                            </constraints>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="0.33333333333333331" green="0.33333333333333331" blue="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="QGF-XQ-alJ" secondAttribute="trailing" constant="10" id="0Gd-V6-4aK"/>
                        <constraint firstItem="gxf-fX-gg0" firstAttribute="top" secondItem="i9V-lf-cDv" secondAttribute="top" id="16u-zw-bn9"/>
                        <constraint firstItem="gxf-fX-gg0" firstAttribute="centerY" secondItem="i9V-lf-cDv" secondAttribute="centerY" id="3rZ-HI-sst"/>
                        <constraint firstItem="fTo-Kb-bP5" firstAttribute="top" secondItem="QGF-XQ-alJ" secondAttribute="bottom" constant="10" id="4or-ia-IoE"/>
                        <constraint firstItem="fTo-Kb-bP5" firstAttribute="leading" secondItem="GUe-zr-L3i" secondAttribute="trailing" constant="10" id="6bE-8f-hn9"/>
                        <constraint firstAttribute="bottom" secondItem="gxf-fX-gg0" secondAttribute="bottom" id="A2q-KO-oK4"/>
                        <constraint firstItem="gxf-fX-gg0" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" id="Af9-iO-6qo"/>
                        <constraint firstItem="MlT-KS-3SQ" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" constant="14" id="Cdf-SF-bfq"/>
                        <constraint firstItem="3Lk-GI-doI" firstAttribute="centerX" secondItem="i9V-lf-cDv" secondAttribute="centerX" id="DAI-Gl-e6B"/>
                        <constraint firstItem="GUe-zr-L3i" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" constant="10" id="DDZ-3o-1i1"/>
                        <constraint firstItem="MlT-KS-3SQ" firstAttribute="centerX" secondItem="i9V-lf-cDv" secondAttribute="centerX" id="Ddg-r5-cGX"/>
                        <constraint firstItem="zru-H6-3lo" firstAttribute="centerX" secondItem="i9V-lf-cDv" secondAttribute="centerX" id="G35-yr-MoO"/>
                        <constraint firstItem="QGF-XQ-alJ" firstAttribute="leading" secondItem="GUe-zr-L3i" secondAttribute="trailing" constant="10" id="KPY-s7-TMP"/>
                        <constraint firstItem="MlT-KS-3SQ" firstAttribute="centerY" secondItem="i9V-lf-cDv" secondAttribute="centerY" id="LqN-rd-zvn"/>
                        <constraint firstItem="qEp-5S-XAH" firstAttribute="centerX" secondItem="i9V-lf-cDv" secondAttribute="centerX" id="OTN-Eo-hVb"/>
                        <constraint firstItem="fTo-Kb-bP5" firstAttribute="top" secondItem="QGF-XQ-alJ" secondAttribute="bottom" constant="10" id="S6d-6v-M8O"/>
                        <constraint firstItem="gxf-fX-gg0" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" id="UbN-F9-IEM"/>
                        <constraint firstAttribute="bottom" secondItem="3Lk-GI-doI" secondAttribute="bottom" constant="46" id="WE7-Gp-55G"/>
                        <constraint firstAttribute="trailing" secondItem="MlT-KS-3SQ" secondAttribute="trailing" constant="14" id="YSo-H8-fTC"/>
                        <constraint firstItem="3Lk-GI-doI" firstAttribute="top" secondItem="zru-H6-3lo" secondAttribute="bottom" constant="8" id="Ydh-BH-CrW"/>
                        <constraint firstAttribute="trailing" secondItem="fTo-Kb-bP5" secondAttribute="trailing" constant="10" id="bVI-Z6-M7z"/>
                        <constraint firstItem="gxf-fX-gg0" firstAttribute="centerX" secondItem="i9V-lf-cDv" secondAttribute="centerX" id="eg8-wH-uha"/>
                        <constraint firstItem="QGF-XQ-alJ" firstAttribute="top" secondItem="i9V-lf-cDv" secondAttribute="top" constant="53" id="fPh-7t-EXc"/>
                        <constraint firstItem="GUe-zr-L3i" firstAttribute="top" secondItem="i9V-lf-cDv" secondAttribute="top" constant="50" id="jAz-sW-Ixa"/>
                        <constraint firstAttribute="trailing" secondItem="gxf-fX-gg0" secondAttribute="trailing" id="ker-j6-06T"/>
                        <constraint firstAttribute="bottom" secondItem="qEp-5S-XAH" secondAttribute="bottom" constant="54" id="oUB-Cm-Efw"/>
                        <constraint firstAttribute="bottom" secondItem="gxf-fX-gg0" secondAttribute="bottom" id="reY-tx-taj"/>
                        <constraint firstItem="gxf-fX-gg0" firstAttribute="top" secondItem="i9V-lf-cDv" secondAttribute="top" id="tQj-Ds-XLz"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="QDD-1Z-KVc" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="3wp-Rb-anc"/>
                <constraint firstItem="QDD-1Z-KVc" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="8Cl-bk-9hC"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="8s0-CE-P1T"/>
                <constraint firstAttribute="bottom" secondItem="i9V-lf-cDv" secondAttribute="bottom" id="91Q-fw-lWP"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="CPT-0y-EzA"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="Md0-Mv-caW"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="OCX-NB-OSf"/>
                <constraint firstItem="QDD-1Z-KVc" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="QVz-Ab-JsK"/>
                <constraint firstItem="QDD-1Z-KVc" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="TYj-xk-j22"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="top" secondItem="QDD-1Z-KVc" secondAttribute="top" id="Y0m-8H-65O"/>
                <constraint firstAttribute="trailing" secondItem="i9V-lf-cDv" secondAttribute="trailing" id="aPe-3e-Ezp"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="leading" secondItem="QDD-1Z-KVc" secondAttribute="leading" id="eW5-ld-RcS"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="gZ7-uX-xeE"/>
                <constraint firstAttribute="trailing" secondItem="i9V-lf-cDv" secondAttribute="trailing" id="iyz-7u-ayP"/>
                <constraint firstAttribute="bottom" secondItem="QDD-1Z-KVc" secondAttribute="bottom" id="mRk-FT-qQJ"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="qcv-wF-XuX"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="uvz-Hn-uh4"/>
                <constraint firstAttribute="bottom" secondItem="i9V-lf-cDv" secondAttribute="bottom" id="vCA-aK-jMv"/>
                <constraint firstAttribute="trailing" secondItem="QDD-1Z-KVc" secondAttribute="trailing" id="xqC-3y-QqP"/>
            </constraints>
            <point key="canvasLocation" x="51.5" y="104.5"/>
        </view>
    </objects>
    <resources>
        <image name="chat_cancel_btn.png" width="129" height="129"/>
        <image name="kefu_bg_img.png" width="131" height="131"/>
    </resources>
</document>
