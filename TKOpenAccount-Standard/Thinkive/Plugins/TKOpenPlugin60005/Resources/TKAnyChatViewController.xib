<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="12120" systemVersion="16G23a" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina5_5" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="12088"/>
        <capability name="Constraints with non-1.0 multipliers" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="TKAnyChatViewController">
            <connections>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8Ax-Mu-YRd">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                    <subviews>
                        <view tag="2000" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="A8z-nC-r3o">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="368"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" tag="2001" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="XFb-vR-mbE">
                                    <rect key="frame" x="0.0" y="0.0" width="414" height="368"/>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" tag="2002" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="客服号：0001" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SHI-Fp-SjZ">
                                    <rect key="frame" x="5" y="20" width="134.66666666666666" height="15"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" tag="2003" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="上行：下行：" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FmR-dM-X8E">
                                    <rect key="frame" x="139.66666666666669" y="20" width="269.33333333333337" height="15"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" alpha="0.5" tag="2004" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="您好，见证开始了，请不要随意挂断或者离开" textAlignment="center" lineBreakMode="characterWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tsu-tQ-SQF">
                                    <rect key="frame" x="0.0" y="352" width="414" height="16"/>
                                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="tsu-tQ-SQF" secondAttribute="trailing" id="2O6-H6-eJG"/>
                                <constraint firstAttribute="trailing" secondItem="FmR-dM-X8E" secondAttribute="trailing" constant="5" id="3BC-ts-ATZ"/>
                                <constraint firstItem="FmR-dM-X8E" firstAttribute="width" secondItem="SHI-Fp-SjZ" secondAttribute="width" multiplier="2:1" id="3Wt-gW-WT5"/>
                                <constraint firstAttribute="bottom" secondItem="tsu-tQ-SQF" secondAttribute="bottom" id="7u4-pY-6Op"/>
                                <constraint firstItem="tsu-tQ-SQF" firstAttribute="leading" secondItem="A8z-nC-r3o" secondAttribute="leading" id="CTV-e1-oNA"/>
                                <constraint firstItem="XFb-vR-mbE" firstAttribute="leading" secondItem="A8z-nC-r3o" secondAttribute="leading" id="FaM-Ch-rdf"/>
                                <constraint firstItem="FmR-dM-X8E" firstAttribute="leading" secondItem="SHI-Fp-SjZ" secondAttribute="trailing" id="MTg-qb-jux"/>
                                <constraint firstItem="FmR-dM-X8E" firstAttribute="leading" secondItem="SHI-Fp-SjZ" secondAttribute="trailing" id="WtQ-Of-FKu"/>
                                <constraint firstItem="SHI-Fp-SjZ" firstAttribute="top" secondItem="A8z-nC-r3o" secondAttribute="top" constant="20" id="XdE-Mz-EJK"/>
                                <constraint firstItem="SHI-Fp-SjZ" firstAttribute="leading" secondItem="A8z-nC-r3o" secondAttribute="leading" constant="5" id="aep-Qx-hj5"/>
                                <constraint firstAttribute="trailing" secondItem="XFb-vR-mbE" secondAttribute="trailing" id="bJC-aG-2Pk"/>
                                <constraint firstAttribute="bottom" secondItem="XFb-vR-mbE" secondAttribute="bottom" id="csE-ZO-mgf"/>
                                <constraint firstItem="FmR-dM-X8E" firstAttribute="leading" secondItem="SHI-Fp-SjZ" secondAttribute="trailing" id="eJh-2X-Om5"/>
                                <constraint firstItem="FmR-dM-X8E" firstAttribute="top" secondItem="A8z-nC-r3o" secondAttribute="top" constant="20" id="hum-BD-iEA"/>
                                <constraint firstItem="FmR-dM-X8E" firstAttribute="leading" secondItem="SHI-Fp-SjZ" secondAttribute="trailing" constant="5" id="iTj-vF-4WJ"/>
                                <constraint firstItem="XFb-vR-mbE" firstAttribute="top" secondItem="A8z-nC-r3o" secondAttribute="top" id="qyG-63-INN"/>
                            </constraints>
                            <variation key="default">
                                <mask key="constraints">
                                    <exclude reference="WtQ-Of-FKu"/>
                                    <exclude reference="iTj-vF-4WJ"/>
                                </mask>
                            </variation>
                        </view>
                        <view tag="3000" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ekb-L0-HOb">
                            <rect key="frame" x="0.0" y="368" width="414" height="368"/>
                            <subviews>
                                <view tag="3001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yV7-Y9-ZiI">
                                    <rect key="frame" x="40" y="0.0" width="334" height="311"/>
                                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </view>
                                <button opaque="NO" tag="3002" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ErQ-Jl-9yJ">
                                    <rect key="frame" x="20" y="311" width="374" height="50"/>
                                    <color key="backgroundColor" red="0.0" green="0.60784313725490191" blue="0.90588235294117647" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="50" id="yn7-2D-WfY"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <state key="normal" title="挂   断">
                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="5"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </button>
                            </subviews>
                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstItem="yV7-Y9-ZiI" firstAttribute="leading" secondItem="ekb-L0-HOb" secondAttribute="leading" constant="40" id="FEP-JY-AED"/>
                                <constraint firstItem="ErQ-Jl-9yJ" firstAttribute="top" secondItem="yV7-Y9-ZiI" secondAttribute="bottom" id="GWV-Zx-G0P"/>
                                <constraint firstAttribute="bottom" secondItem="ErQ-Jl-9yJ" secondAttribute="bottom" constant="7" id="WcO-4K-879"/>
                                <constraint firstItem="yV7-Y9-ZiI" firstAttribute="top" secondItem="ekb-L0-HOb" secondAttribute="top" id="fNt-3k-8CC"/>
                                <constraint firstAttribute="trailing" secondItem="yV7-Y9-ZiI" secondAttribute="trailing" constant="40" id="kxN-Yc-uwx"/>
                                <constraint firstItem="ErQ-Jl-9yJ" firstAttribute="leading" secondItem="ekb-L0-HOb" secondAttribute="leading" constant="20" id="nrm-IK-JOW"/>
                                <constraint firstAttribute="trailing" secondItem="ErQ-Jl-9yJ" secondAttribute="trailing" constant="20" id="rd5-3c-2wD"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstItem="ekb-L0-HOb" firstAttribute="height" secondItem="A8z-nC-r3o" secondAttribute="height" id="8Dr-wI-pag"/>
                        <constraint firstAttribute="bottom" secondItem="ekb-L0-HOb" secondAttribute="bottom" id="HJr-uW-st8"/>
                        <constraint firstItem="ekb-L0-HOb" firstAttribute="top" secondItem="A8z-nC-r3o" secondAttribute="bottom" id="Ncw-Fd-nvf"/>
                        <constraint firstAttribute="trailing" secondItem="ekb-L0-HOb" secondAttribute="trailing" id="Of5-PD-ebX"/>
                        <constraint firstItem="A8z-nC-r3o" firstAttribute="leading" secondItem="8Ax-Mu-YRd" secondAttribute="leading" id="aT6-wo-TdA"/>
                        <constraint firstItem="A8z-nC-r3o" firstAttribute="top" secondItem="8Ax-Mu-YRd" secondAttribute="top" id="mlH-tx-qY8"/>
                        <constraint firstAttribute="trailing" secondItem="A8z-nC-r3o" secondAttribute="trailing" id="n7z-M2-DTr"/>
                        <constraint firstItem="ekb-L0-HOb" firstAttribute="leading" secondItem="8Ax-Mu-YRd" secondAttribute="leading" id="xUk-ha-5OU"/>
                    </constraints>
                </view>
                <view tag="1000" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="B1v-7d-Zf1">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" tag="1001" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="page_bg.jpg" translatesAutoresizingMaskIntoConstraints="NO" id="jYO-be-eXT">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" tag="1003" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="排队和见证过程请不要切换程序，否则需要重新排队见证" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hVQ-V8-Mq2">
                            <rect key="frame" x="0.0" y="359.66666666666669" width="414" height="16"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="16" id="K29-eE-GEh"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                            <color key="textColor" red="0.97647058819999999" green="0.0" blue="0.0078431372550000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" tag="1004" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="正在等待见证，请稍等..." textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uUA-Nh-5ZK">
                            <rect key="frame" x="0.0" y="383.66666666666669" width="414" height="24"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="24" id="UXO-6E-IKS"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view tag="1002" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eVO-HV-UXj">
                            <rect key="frame" x="76.666666666666686" y="424.66666666666674" width="260" height="244.33333333333326"/>
                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="260" id="gsN-xf-yan"/>
                            </constraints>
                        </view>
                        <button opaque="NO" tag="1005" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2Vy-ko-gQU">
                            <rect key="frame" x="56.666666666666657" y="677" width="300" height="44"/>
                            <color key="backgroundColor" red="0.0" green="0.60784313729999995" blue="0.90588235289999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="300" id="X1p-nv-znh"/>
                                <constraint firstAttribute="height" constant="44" id="XGK-v5-uYl"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <state key="normal" title="取消排队">
                                <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="5"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstItem="hVQ-V8-Mq2" firstAttribute="centerY" secondItem="B1v-7d-Zf1" secondAttribute="centerY" id="1XW-f1-0Pc"/>
                        <constraint firstItem="eVO-HV-UXj" firstAttribute="top" secondItem="uUA-Nh-5ZK" secondAttribute="bottom" constant="17" id="1lN-lB-UVZ"/>
                        <constraint firstAttribute="trailing" secondItem="hVQ-V8-Mq2" secondAttribute="trailing" id="AXC-a9-Ocd"/>
                        <constraint firstAttribute="bottom" secondItem="jYO-be-eXT" secondAttribute="bottom" id="DTO-Lq-85J"/>
                        <constraint firstItem="eVO-HV-UXj" firstAttribute="centerX" secondItem="B1v-7d-Zf1" secondAttribute="centerX" id="Fgd-BU-oFw"/>
                        <constraint firstItem="jYO-be-eXT" firstAttribute="leading" secondItem="B1v-7d-Zf1" secondAttribute="leading" id="M0k-ml-pt1"/>
                        <constraint firstItem="2Vy-ko-gQU" firstAttribute="top" secondItem="eVO-HV-UXj" secondAttribute="bottom" constant="8" id="Mnb-WC-ilT"/>
                        <constraint firstItem="hVQ-V8-Mq2" firstAttribute="leading" secondItem="B1v-7d-Zf1" secondAttribute="leading" id="NrO-dp-qgd"/>
                        <constraint firstAttribute="trailing" secondItem="jYO-be-eXT" secondAttribute="trailing" id="PPe-W4-RZx"/>
                        <constraint firstAttribute="bottom" secondItem="2Vy-ko-gQU" secondAttribute="bottom" constant="15" id="WKI-X6-wxs"/>
                        <constraint firstItem="2Vy-ko-gQU" firstAttribute="centerX" secondItem="B1v-7d-Zf1" secondAttribute="centerX" id="Xk3-rf-tU0"/>
                        <constraint firstAttribute="trailing" secondItem="uUA-Nh-5ZK" secondAttribute="trailing" id="c4q-rg-pCS"/>
                        <constraint firstItem="hVQ-V8-Mq2" firstAttribute="centerX" secondItem="B1v-7d-Zf1" secondAttribute="centerX" id="cgR-HL-sSk"/>
                        <constraint firstItem="uUA-Nh-5ZK" firstAttribute="top" secondItem="hVQ-V8-Mq2" secondAttribute="bottom" constant="8" id="fpo-IY-c1G"/>
                        <constraint firstItem="jYO-be-eXT" firstAttribute="top" secondItem="B1v-7d-Zf1" secondAttribute="top" id="gIJ-Ni-pRn"/>
                        <constraint firstItem="uUA-Nh-5ZK" firstAttribute="leading" secondItem="B1v-7d-Zf1" secondAttribute="leading" id="nbi-Lb-sCX"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="8Ax-Mu-YRd" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="4Hn-te-136"/>
                <constraint firstAttribute="bottom" secondItem="8Ax-Mu-YRd" secondAttribute="bottom" id="6yy-M8-nai"/>
                <constraint firstItem="8Ax-Mu-YRd" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="M8a-uX-Bvm"/>
                <constraint firstAttribute="bottom" secondItem="B1v-7d-Zf1" secondAttribute="bottom" id="MT6-u1-wEC"/>
                <constraint firstItem="B1v-7d-Zf1" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="QY5-6O-avU"/>
                <constraint firstAttribute="trailing" secondItem="8Ax-Mu-YRd" secondAttribute="trailing" id="eyw-eo-jRC"/>
                <constraint firstItem="B1v-7d-Zf1" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="k7w-5S-Sko"/>
                <constraint firstAttribute="trailing" secondItem="B1v-7d-Zf1" secondAttribute="trailing" id="wOb-XY-x83"/>
            </constraints>
        </view>
    </objects>
    <resources>
        <image name="page_bg.jpg" width="304" height="360"/>
    </resources>
</document>
