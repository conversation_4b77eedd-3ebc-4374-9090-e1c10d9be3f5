<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14460.31" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina5_5" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14460.20"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="TKTChatViewController">
            <connections>
                <outlet property="view" destination="iN0-l3-epB" id="Y7y-kP-PYa"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QDD-1Z-KVc">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                    <subviews>
                        <view tag="3001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zlK-ag-lTS">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                            <color key="backgroundColor" red="0.40781933069229126" green="0.40781933069229126" blue="0.40781933069229126" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" tag="2003" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="上行:0.0kb/s 下行:0.0kb/s" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="u0Q-l3-Dt1">
                            <rect key="frame" x="10" y="20" width="303" height="50"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="50" id="ixT-sd-jYH"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" tag="3002" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TsM-cL-UvQ">
                            <rect key="frame" x="339" y="20" width="60" height="50"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="50" id="NYd-6m-L75"/>
                                <constraint firstAttribute="width" constant="60" id="jCi-ZR-Gzj"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                            <state key="normal" title="挂 断">
                                <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                        </button>
                        <view tag="2000" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rss-sW-TOb">
                            <rect key="frame" x="239" y="499" width="160" height="120"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" tag="2001" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="PAs-fZ-d9j">
                                    <rect key="frame" x="0.0" y="0.0" width="160" height="120"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="120" id="4TM-C6-1P7"/>
                                <constraint firstAttribute="width" constant="160" id="AsV-wM-WeV"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" alpha="0.5" tag="2002" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="客服：admin" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wU3-KA-8T3">
                            <rect key="frame" x="239" y="619" width="160" height="30"/>
                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="160" id="cKL-UY-pZ3"/>
                                <constraint firstAttribute="height" constant="30" id="rZW-ES-9PC"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" tag="2004" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="正在见证，请按照客服提示操作，不要随意挂断" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="szH-2x-9dZ">
                            <rect key="frame" x="38" y="669" width="338" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstItem="u0Q-l3-Dt1" firstAttribute="leading" secondItem="QDD-1Z-KVc" secondAttribute="leading" constant="10" id="8nJ-No-cj7"/>
                        <constraint firstItem="TsM-cL-UvQ" firstAttribute="leading" secondItem="u0Q-l3-Dt1" secondAttribute="trailing" constant="26" id="Afw-Nm-xsK"/>
                        <constraint firstAttribute="trailing" secondItem="szH-2x-9dZ" secondAttribute="trailing" constant="38" id="EKi-R9-F1Y"/>
                        <constraint firstItem="zlK-ag-lTS" firstAttribute="top" secondItem="QDD-1Z-KVc" secondAttribute="top" id="KSU-JN-dpK"/>
                        <constraint firstAttribute="trailing" secondItem="TsM-cL-UvQ" secondAttribute="trailing" constant="15" id="LF6-Ys-DhB"/>
                        <constraint firstItem="TsM-cL-UvQ" firstAttribute="top" secondItem="QDD-1Z-KVc" secondAttribute="top" constant="20" id="PMo-X7-S0T"/>
                        <constraint firstAttribute="trailing" secondItem="rss-sW-TOb" secondAttribute="trailing" constant="15" id="RLw-7d-uMx"/>
                        <constraint firstItem="szH-2x-9dZ" firstAttribute="centerX" secondItem="QDD-1Z-KVc" secondAttribute="centerX" id="Syf-Vc-hs3"/>
                        <constraint firstAttribute="trailing" secondItem="wU3-KA-8T3" secondAttribute="trailing" constant="15" id="Z8A-gT-bxh"/>
                        <constraint firstAttribute="trailing" secondItem="zlK-ag-lTS" secondAttribute="trailing" id="cKO-dp-iI8"/>
                        <constraint firstAttribute="bottom" secondItem="zlK-ag-lTS" secondAttribute="bottom" id="gn0-vJ-Z4D"/>
                        <constraint firstItem="u0Q-l3-Dt1" firstAttribute="top" secondItem="QDD-1Z-KVc" secondAttribute="top" constant="20" id="rrd-zS-3Kk"/>
                        <constraint firstItem="szH-2x-9dZ" firstAttribute="top" secondItem="wU3-KA-8T3" secondAttribute="bottom" constant="20" id="s5f-NS-ca9"/>
                        <constraint firstItem="szH-2x-9dZ" firstAttribute="leading" secondItem="QDD-1Z-KVc" secondAttribute="leading" constant="38" id="uhB-NS-s5e"/>
                        <constraint firstItem="wU3-KA-8T3" firstAttribute="top" secondItem="rss-sW-TOb" secondAttribute="bottom" id="usK-E0-SSo"/>
                        <constraint firstItem="zlK-ag-lTS" firstAttribute="leading" secondItem="QDD-1Z-KVc" secondAttribute="leading" id="yqp-5f-81B"/>
                        <constraint firstAttribute="bottom" secondItem="szH-2x-9dZ" secondAttribute="bottom" constant="50" id="ze5-BA-imL"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="i9V-lf-cDv">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="视频认证" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3Lk-GI-doI">
                            <rect key="frame" x="0.0" y="20" width="414" height="44"/>
                            <color key="backgroundColor" red="0.97194463014602661" green="0.97196739912033081" blue="0.97195512056350708" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="44" id="UWW-Pn-wlB"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="20"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LBC-bo-zLv">
                            <rect key="frame" x="0.0" y="64" width="414" height="1"/>
                            <color key="backgroundColor" red="0.78773695230484009" green="0.78775566816329956" blue="0.78774565458297729" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="6S1-re-rBx"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ztu-n8-2Gt">
                            <rect key="frame" x="0.0" y="65" width="414" height="258"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="tk_video_icon_iphone.png" translatesAutoresizingMaskIntoConstraints="NO" id="wA2-XH-QXc">
                                    <rect key="frame" x="162" y="31" width="90" height="90"/>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="排队中" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8NP-21-rcJ">
                                    <rect key="frame" x="0.0" y="139" width="414" height="60"/>
                                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" tag="1004" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="在您前面还有1人在排队等待视频认证" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DEG-sS-6f0">
                                    <rect key="frame" x="0.0" y="181" width="414" height="50"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="50" id="N3H-hz-YbS"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                    <color key="textColor" red="0.62508308887481689" green="0.62509810924530029" blue="0.62509006261825562" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstItem="DEG-sS-6f0" firstAttribute="trailing" secondItem="8NP-21-rcJ" secondAttribute="trailing" id="3FL-B8-B3h"/>
                                <constraint firstItem="DEG-sS-6f0" firstAttribute="leading" secondItem="8NP-21-rcJ" secondAttribute="leading" id="EHs-NQ-DEK"/>
                                <constraint firstAttribute="trailing" secondItem="DEG-sS-6f0" secondAttribute="trailing" id="b9H-Wx-nvl"/>
                                <constraint firstItem="wA2-XH-QXc" firstAttribute="centerX" secondItem="Ztu-n8-2Gt" secondAttribute="centerX" id="izs-Dd-hDj"/>
                                <constraint firstAttribute="bottom" secondItem="DEG-sS-6f0" secondAttribute="bottom" constant="27" id="ofr-pz-v2Y"/>
                                <constraint firstItem="DEG-sS-6f0" firstAttribute="leading" secondItem="Ztu-n8-2Gt" secondAttribute="leading" id="pXG-3V-bq6"/>
                                <constraint firstItem="DEG-sS-6f0" firstAttribute="centerX" secondItem="wA2-XH-QXc" secondAttribute="centerX" id="tDQ-VJ-Zv3"/>
                                <constraint firstAttribute="height" constant="258" id="tem-NF-U61"/>
                                <constraint firstItem="DEG-sS-6f0" firstAttribute="top" secondItem="wA2-XH-QXc" secondAttribute="bottom" constant="60" id="toZ-bD-2co"/>
                                <constraint firstItem="8NP-21-rcJ" firstAttribute="top" secondItem="wA2-XH-QXc" secondAttribute="bottom" constant="18" id="wfO-Fm-PDB"/>
                                <constraint firstAttribute="bottom" secondItem="8NP-21-rcJ" secondAttribute="bottom" constant="59" id="yeL-TJ-DWB"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="epY-Xc-pHp">
                            <rect key="frame" x="0.0" y="323" width="414" height="1"/>
                            <color key="backgroundColor" red="0.9246174693107605" green="0.92473882436752319" blue="0.92781233787536621" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="3B7-Fc-87m"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" tag="1005" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qEp-5S-XAH">
                            <rect key="frame" x="20" y="371" width="374" height="40"/>
                            <color key="backgroundColor" red="0.97818964719772339" green="0.39854151010513306" blue="0.38095545768737793" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="elb-2x-0sa"/>
                            </constraints>
                            <state key="normal" title="取消排队">
                                <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="0.9562498927116394" green="0.9562721848487854" blue="0.95626014471054077" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstItem="3Lk-GI-doI" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" id="2gg-Xi-Cyi"/>
                        <constraint firstAttribute="trailing" secondItem="LBC-bo-zLv" secondAttribute="trailing" id="62s-Jj-ovT"/>
                        <constraint firstItem="epY-Xc-pHp" firstAttribute="top" secondItem="Ztu-n8-2Gt" secondAttribute="bottom" id="7ys-yc-pSu"/>
                        <constraint firstItem="qEp-5S-XAH" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" constant="20" id="BY1-ON-8SA"/>
                        <constraint firstItem="Ztu-n8-2Gt" firstAttribute="top" secondItem="LBC-bo-zLv" secondAttribute="bottom" id="Cww-iL-kKN"/>
                        <constraint firstAttribute="trailing" secondItem="3Lk-GI-doI" secondAttribute="trailing" id="JeY-dq-G2i"/>
                        <constraint firstAttribute="trailing" secondItem="qEp-5S-XAH" secondAttribute="trailing" constant="20" id="Mx1-4h-yX6"/>
                        <constraint firstItem="LBC-bo-zLv" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" id="Yf4-df-Xod"/>
                        <constraint firstItem="3Lk-GI-doI" firstAttribute="top" secondItem="i9V-lf-cDv" secondAttribute="top" constant="20" id="fzx-nR-LXg"/>
                        <constraint firstAttribute="trailing" secondItem="epY-Xc-pHp" secondAttribute="trailing" id="hrB-2T-Qwk"/>
                        <constraint firstAttribute="trailing" secondItem="Ztu-n8-2Gt" secondAttribute="trailing" id="oxC-HL-coh"/>
                        <constraint firstItem="epY-Xc-pHp" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" id="pIu-6T-vus"/>
                        <constraint firstItem="Ztu-n8-2Gt" firstAttribute="centerX" secondItem="i9V-lf-cDv" secondAttribute="centerX" id="qHe-Js-kpx"/>
                        <constraint firstItem="Ztu-n8-2Gt" firstAttribute="leading" secondItem="i9V-lf-cDv" secondAttribute="leading" id="w3k-Cg-hL5"/>
                        <constraint firstItem="qEp-5S-XAH" firstAttribute="top" secondItem="epY-Xc-pHp" secondAttribute="bottom" constant="47" id="y1q-kt-gLx"/>
                        <constraint firstItem="LBC-bo-zLv" firstAttribute="top" secondItem="3Lk-GI-doI" secondAttribute="bottom" id="zcG-UM-A1Y"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="QDD-1Z-KVc" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="3wp-Rb-anc"/>
                <constraint firstItem="QDD-1Z-KVc" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="BbI-6I-Z3O"/>
                <constraint firstAttribute="trailing" secondItem="i9V-lf-cDv" secondAttribute="trailing" id="Ea6-Yi-nXF"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="HWF-f3-P3x"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="JWt-EW-C7I"/>
                <constraint firstAttribute="bottom" secondItem="i9V-lf-cDv" secondAttribute="bottom" id="SRj-EZ-6FW"/>
                <constraint firstAttribute="trailing" secondItem="i9V-lf-cDv" secondAttribute="trailing" id="TLG-TB-zGN"/>
                <constraint firstItem="QDD-1Z-KVc" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="TYj-xk-j22"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="X33-MF-pim"/>
                <constraint firstAttribute="bottom" secondItem="i9V-lf-cDv" secondAttribute="bottom" id="gKD-Vd-AYm"/>
                <constraint firstAttribute="bottom" secondItem="QDD-1Z-KVc" secondAttribute="bottom" id="mRk-FT-qQJ"/>
                <constraint firstItem="i9V-lf-cDv" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="thF-06-rXb"/>
                <constraint firstAttribute="trailing" secondItem="QDD-1Z-KVc" secondAttribute="trailing" id="xqC-3y-QqP"/>
            </constraints>
            <point key="canvasLocation" x="-79.5" y="103.5"/>
        </view>
    </objects>
    <resources>
        <image name="tk_video_icon_iphone.png" width="90" height="90"/>
    </resources>
</document>
