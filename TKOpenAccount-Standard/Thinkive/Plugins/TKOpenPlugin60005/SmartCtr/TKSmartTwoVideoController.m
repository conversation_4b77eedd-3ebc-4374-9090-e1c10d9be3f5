//
//  TKSmartTwoVideoController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2019/8/28.
//  Copyright © 2019 thinkive. All rights reserved.
//

#import "TKSmartTwoVideoController.h"
#import "TKOpenQueueView.h"
#import "TKOpenVideoChatView.h"
#import "TKOpenAccountService.h"
#import "TKVideoAlertView.h"
#import "TKDirectVideoModel.h"
#import "TKSmartTwoVideoManager.h"

@interface TKSmartTwoVideoController ()<TKOpenQueueDelegate,TKOpenVideoChatDelegate,TKVideoAlertViewDelegate,TKSmartTwoVideoManagerDelegate>
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@property (nonatomic, strong) TKOpenQueueView *queueView;//视频排队页面
@property (nonatomic, assign) int linePosition;//记录排队位置，被插队了也显示原来靠前的位置
@property (nonatomic, strong) TKOpenVideoChatView *videoChatView;//视频见证页面
@property (nonatomic, strong) TKLayerView  *layerView;//提示layer
@property (nonatomic, strong) TKOpenAccountService *mService;
@property (nonatomic, strong) TKVideoAlertView *videoAlertView;//视频挂断提示框

@property (nonatomic, strong) NSTimer *videoLengthTimer; //见证时长定时器
@property (nonatomic, assign) int longestTime;//视频见证时间时间
@property (nonatomic, strong) id<TKSmartTwoVideoManagerProtocol> smartTwoVideoManager;//智能双向管理类
//@property (nonatomic, readwrite, assign) NSTimeInterval statisticEventStartTime;    // 总事件初始时间
@property (nonatomic, assign) BOOL isShowNoSeverAlertTip;//是否展示过无坐席跳转单向提示
@end

@implementation TKSmartTwoVideoController
/**
 <AUTHOR> 2019年09月05日14:18:48
 @初始化TKSmartTwoVideoController
 @return TKSmartTwoVideoController
 */
-(instancetype)initWithParam:(NSMutableDictionary *)param{
    self=[super init];
    if (self) {
        self.requestParams=param;
        self.mService=[[TKOpenAccountService alloc] init];
    }
    return self;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self.view setBackgroundColor:[TKUIHelper colorWithHexString:@"#F3F3F6"]];
    
    self.isResetParentStatusBarStyle=YES;
    self.statusBarStyle=TKUIStatusBarStyleDefault;
    
    // 埋点-双向-开始
//    self.statisticEventStartTime = [[NSDate date] timeIntervalSince1970];
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventStart progress:TKPrivateEventProgressNone result:TKPrivateEventResultNone eventDic:eventDic];
    
    
    if([TKStringHelper isEmpty:self.requestParams[@"serviceTipString"]]){
        [TKDirectVideoModel shareInstance].serviceTipString = @"坐席";
    }else{
        [TKDirectVideoModel shareInstance].serviceTipString = self.requestParams[@"serviceTipString"];
    }
    
    [TKDirectVideoModel shareInstance].isFrontCamera=YES;//当前使用前置摄像头
    [TKDirectVideoModel shareInstance].witnessResult = @"20000";
    [TKDirectVideoModel shareInstance].witnessInfo = @"";
    
    [TKDirectVideoModel shareInstance].aExit = YES;
    
    [TKDirectVideoModel shareInstance].isShowAlert = NO;
    self.isShowNoSeverAlertTip=NO;

    [TKDirectVideoModel shareInstance].tipType=0;
    [TKDirectVideoModel shareInstance].isFrontCamera=YES;//当前使用前置摄像头
    [TKDirectVideoModel shareInstance].isDirectVideo=NO;
    [TKDirectVideoModel shareInstance].isQueueStaffExist=YES;//没排队前先假设有坐席，避免没排队就返回情况统计数据错误
    
    [self.view addSubview:self.queueView];

    // 埋点-双向-排队-开始
    eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessEnqueue progress:TKPrivateEventProgressStart result:TKPrivateEventResultNone eventDic:eventDic];
    
    [TKDirectVideoModel shareInstance].staffTips=nil;
    [self.smartTwoVideoManager startLineingUp];

    
}


-(NSString *)getTimeStamp{
    // 设置想要的格式，hh与HH的区别:分别表示12小时制,24小时制
    //把NSDate按formatter格式转成NSString
    NSString *currentTime =[TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
    return currentTime;
}

-(void)startVidoeTime{
    //启动录制时长
    _videoLengthTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(videoLengthAction) userInfo:nil repeats:YES];
    self.longestTime=0;
}

/**
 <AUTHOR> 2022年05月25日16:19:58
 @见证时长
 */
-(void)videoLengthAction{
   self.longestTime++;
   NSDate *d = [NSDate dateWithTimeIntervalSince1970:self.longestTime];
   NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];

   if (self.longestTime/3600 >= 1) {
       [formatter setDateFormat:@"HH:mm:ss"];
   } else {
       [formatter setDateFormat:@"mm:ss"];
   }

    self.videoChatView.topTipLabel.text=[NSString stringWithFormat:@"%@",[formatter stringFromDate:d]] ;
    
}

#pragma mark TKSmartTwoVideoManagerDelegate
//上传日志
-(void)uploadSmartTwoVideoLog:(NSString *)logString{
    
    if ([TKStringHelper isNotEmpty:self.requestParams[@"logUrl"]]){
        //需要上传单向视频日志
//        NSMutableDictionary *param=[[NSMutableDictionary alloc] init];
//        param[@"user_id"]=self.requestParams[@"userId"];
//        param[@"msg"]=logString;
        NSString *url=[NSString stringWithFormat:@"%@user_id=%@&msg=%@",self.requestParams[@"logUrl"],self.requestParams[@"userId"],logString];

        [self.mService uploadFileWithURL:url param:nil callBackFunc:^(ResultVo *resultVo) {
       
            TKLogInfo(@"-------");
        }];
    }
}
/**
 <AUTHOR> 2023年01月28日14:25:14
 @提示排队提示语
 @param tipString提示文本
 @param location排队位置
 @param status当前排队状态
 */
-(void)changeSmartTwoVideoTipText:(NSString *)tipString queueLocation:(int)location currentStatus:(TKOpenQueueStatus)status{
    [self changeTipText:tipString queueLocation:location currentStatus:status];
    
    if(status==TKOpenQueueStatusNOService){
        //无坐席
        if([self.requestParams getIntWithKey:@"isShowSelfHelpWitness"]==1){
            [self.queueView showCancelBtn:YES];
            //展示自助见证按钮情况下弹窗
            if (![TKDirectVideoModel shareInstance].isShowAlert&&!self.isShowNoSeverAlertTip) {
                [TKDirectVideoModel shareInstance].isShowAlert = YES;
                [TKDirectVideoModel shareInstance].witnessResult=@"-11";
                self.isShowNoSeverAlertTip=YES;
                [self.view addSubview:self.videoAlertView];
                self.videoAlertView.describeLabel.text=[NSString stringWithFormat:@"当前无%@在线，建议您切换为自助见证，无需等待。",[TKDirectVideoModel shareInstance].serviceTipString];
                self.videoAlertView.titleLabel.text=[NSString stringWithFormat:@"当前无%@在线！",[TKDirectVideoModel shareInstance].serviceTipString];
                [self.videoAlertView.cancelBtn setTitle:@"继续等待" forState:UIControlStateNormal];
                [self.videoAlertView.takeBtn setTitle:@"切换为自助见证" forState:UIControlStateNormal];
             }
        }else{
            [self.queueView showCancelBtn:NO];
        }
    }
}

/**
 <AUTHOR> 2023年01月28日14:25:14
 @弹窗提示
 @param title标题
 @param describe描述
 @param cancelBtnTitle取消按钮文本
 @param takeBtnTitle确认按钮文本
 */
-(void)alertSmartTwoVideoTip:(NSString *)title  describe:(NSString *)describe cancelBtnTitle:(NSString *)cancelBtnTitle takeBtnTitle:(NSString *)takeBtnTitle{
    [self.view addSubview:self.videoAlertView];
    self.videoAlertView.describeLabel.text=describe;
    self.videoAlertView.titleLabel.text=title;
    if ([TKStringHelper isEmpty:cancelBtnTitle]) {
        [self.videoAlertView setOnlyBtnTitle:takeBtnTitle];
        
    }else{
        [self.videoAlertView.cancelBtn setTitle:cancelBtnTitle forState:UIControlStateNormal];
        [self.videoAlertView.takeBtn setTitle:takeBtnTitle forState:UIControlStateNormal];
    }

}

/***
 启动视频见证
 */
- (void)tkStartSmartTwoVideo:(NSString*)sUrl withPort:(int)sPort{
    //处理人数超限情况下，没点继续排队被坐席接入后，视频见证页面无法挂断问题
    [self.videoAlertView removeFromSuperview];
    [TKDirectVideoModel shareInstance].isShowAlert = NO;
    [TKDirectVideoModel shareInstance].witnessResult = @"20000";
    

    
    [self.smartTwoVideoManager startSmartTwoVideo:sUrl withPort:sPort];
}

/***
 添加视频画面到界面
 */
- (void)addSmartTwoVideoChatView{
    [self.view addSubview:self.videoChatView];
    self.smartTwoVideoManager.contentView=self.videoChatView.meVideoView;
    self.smartTwoVideoManager.remoteContentView=self.videoChatView.remoteVideoView;
    [self.videoChatView setHidden:YES];
}

/***
 视频画面显示
 */
- (void)showSmartTwoVideoChatView{
    self.statusBarStyle=UIStatusBarStyleLightContent;
    
    // 埋点-双向-视频-就绪
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    eventDic[@"enqueueSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isEnqueueSuccess];
    eventDic[@"connectSuccess"] = [NSString stringWithFormat:@"%i", [TKDirectVideoModel shareInstance].isStartingVideo];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressNone result:TKPrivateEventResultSuccess orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressVideoReady eventDic:eventDic];
    
    [self.videoChatView setHidden:NO];
    //接通提示
    [self showVideoNetStatus:@"已接通" withColor:@"#333333"];
}

//显示网络状态展示
-(void)showSmartTwoVideoNetStatus:(NSString *)info withColor:(NSString *)color{
    [self showVideoNetStatus:info withColor:color];
}

//显示网络上下行网速
-(void)smartTwoVideoNetWorkUpDownTip:(NSString *)string{
    self.videoChatView.networkLabel.text=string;
}

//显示风险协议阅读文本
-(void)showSmartTwoVideoRead:(NSMutableDictionary *)param{
    [self showVideoRead:param];
}
//显示toast提示
-(void)showSmartTwoVideoToast:(NSString *)string{
    [self showToast:string];
}

//显示坐席消息
-(void)showSmartTwoVideoSeatMessage:(NSString *)message{
    [self showSeatMessage:message];
}
//显示坐席信息
-(void)showSmartTwoVideoServiceInfo:(NSString *)info{
    [self showVideoServiceInfo:info];
}

//开始视频计时
-(void)startSmartTwoVidoeTime{
    [self startVidoeTime];
}
//关闭视频
-(void)endSmartTwoVidoe{
    
    if (self.videoLengthTimer) {
        [self.videoLengthTimer invalidate];
        self.videoLengthTimer = nil;
    }
    
    if (![TKDirectVideoModel shareInstance].isTransBufferMsg) {
        //正常通过驳回无需调用退出排队接口
        [self.smartTwoVideoManager stopLineingUp];
    }
    
    
    [self dismissViewControllerAnimated:YES completion:^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkSmartTwoResult:extParam:)]) {
            
            [self.delegate tkSmartTwoResult:[TKDirectVideoModel shareInstance].witnessResult extParam:[TKDirectVideoModel shareInstance].extParam];
        }
    }];
}

/**
*<AUTHOR> 根据坐席指令展示带标题的对准框
*/
-(void)showSmartTwoVideoTitleBox:(NSMutableDictionary *)param{
    [self showVideoTitleBox:param];
}

/**
*<AUTHOR> 根据坐席指令展示可滚动文本
*/
-(void)showSmartTwoVideoRollText:(NSMutableDictionary *)param{
    [self showVideoRollText:param];
}

/**
*<AUTHOR> 根据坐席指令展示进度条
*/
-(void)showSmartTwoVideoProcessNode:(NSMutableDictionary *)param{
    [self showVideoProcessNode:param];
}

#pragma mark 页面修改事件
/**
 <AUTHOR> 2019年09月06日14:08:17
 @提示排队提示语
 @param tipString提示文本
 @param location排队位置
 @param status当前排队状态
 */
-(void)changeTipText:(NSString *)tipString queueLocation:(int)location currentStatus:(TKOpenQueueStatus)status{
    [self.queueView showTipText:tipString queueLocation:location currentStatus:status tipType:[TKDirectVideoModel shareInstance].tipType];
}

//显示坐席消息
-(void)showSeatMessage:(NSString *)message{
    // 埋点-双向-收到文本消息
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessReceiveMessage progress:TKPrivateEventProgressNone result:TKPrivateEventResultNone eventDic:eventDic];
    
    [self.videoChatView appendServerMessage:message];
}

//显示风险协议阅读文本
-(void)showVideoRead:(NSMutableDictionary *)param{
    [self.videoChatView showReadAgree:param];
}

//显示坐席信息
-(void)showVideoServiceInfo:(NSString *)info{
    [self.videoChatView showServiceInfo:info];
}

//显示网络状态展示
-(void)showVideoNetStatus:(NSString *)info withColor:(NSString *)color{
    [self.layerView showTip:info position:TKLayerPosition_Bottom textColor:@"#FFFFFF" bgColor:color];
}

//显示toast提示
-(void)showToast:(NSString *)string{
    [self.layerView showTip:string position:TKLayerPosition_Center textColor:@"#FFFFFF" bgColor:@"#FF0000"];
}

//根据坐席指令展示带标题的对准框
-(void)showVideoTitleBox:(NSMutableDictionary *)param{
//    [self.videoChatView showTitleBox:param];
}

//根据坐席指令展示可滚动文本
-(void)showVideoRollText:(NSMutableDictionary *)param{
//   [self.videoChatView showRollText:param];
}

/**
*<AUTHOR> 根据坐席指令展示进度条
*/
-(void)showVideoProcessNode:(NSMutableDictionary *)param{
//    [self.videoChatView showProcessNode:param];
}


-(void)cancelAction{
    
    [TKDirectVideoModel shareInstance].isCancelLineingUp = YES;
    [self.videoAlertView removeFromSuperview];
    //退出视频
    [self.smartTwoVideoManager stopSmartTwoVideo];
}

//继续按钮事件
-(void)takeVideoAction{
    [TKDirectVideoModel shareInstance].witnessResult = @"20000";
    [self.videoAlertView removeFromSuperview];
    [TKDirectVideoModel shareInstance].isShowAlert = NO;
    //继续视频
   
    [TKDirectVideoModel shareInstance].isCancelLineingUp =NO;
    [TKDirectVideoModel shareInstance].aExit = YES;
}


//人数超限时候显示取消排队，自助见证安按钮
-(void)showCancelBtn{
    [self.queueView showCancelBtn:YES];
}

#pragma mark TKVideoAlertViewDelegate


//取消按钮事件
-(void)cancelVideoBtnAction{
    
    if ([[TKDirectVideoModel shareInstance].witnessResult isEqualToString:@"-11"]) {
        [self takeVideoAction];
    }else{
        [self cancelAction];
    }
}



//继续按钮事件
-(void)takeVideoBtnAction{
    if ([[TKDirectVideoModel shareInstance].witnessResult isEqualToString:@"-11"]) {
        [self cancelAction];
    }else{
        [self takeVideoAction];
    }
}

//独立按钮事件
-(void)onlyVideoBtnAction{
    [self cancelAction];
}


#pragma mark TKOpenQueueDelegate
//取消排队
-(void)cancelQueue{
    
    
    [TKDirectVideoModel shareInstance].aExit = NO;
    if (![TKDirectVideoModel shareInstance].isShowAlert) {
        
        [TKDirectVideoModel shareInstance].isShowAlert = YES;
        

        [self.view addSubview:self.videoAlertView];
        self.videoAlertView.describeLabel.text=@"您正在退出排队，确认要退出排队吗？";
        self.videoAlertView.titleLabel.text=@"确定退出排队吗？";
        [self.videoAlertView.cancelBtn setTitle:@"确认退出" forState:UIControlStateNormal];
        [self.videoAlertView.takeBtn setTitle:@"继续等待" forState:UIControlStateNormal];
    }
}

//自助见证
-(void)goSelfHelpWitness{
    [TKDirectVideoModel shareInstance].witnessResult=@"-11";
    [self cancelAction];
}

#pragma mark TKOpenVideoChatDelegate
-(void)hangUp{
    if (![TKDirectVideoModel shareInstance].isShowAlert) {
        
        [TKDirectVideoModel shareInstance].isShowAlert = YES;
        
        [self.view addSubview:self.videoAlertView];
        self.videoAlertView.describeLabel.text=[NSString stringWithFormat:@"自行退出视频见证将导致本次视频提交失败，请您等待%@结束见证。",[TKDirectVideoModel shareInstance].serviceTipString];
        self.videoAlertView.titleLabel.text=@"视频录制提示";
        [self.videoAlertView.cancelBtn setTitle:@"执意退出" forState:UIControlStateNormal];
        [self.videoAlertView.takeBtn setTitle:@"继续见证" forState:UIControlStateNormal];
    }
    
    [TKDirectVideoModel shareInstance].aExit = NO;
}

//切换摄像头
-(void)switchCamera{
    [self.smartTwoVideoManager switchCameraSmartTwoVideo:[TKDirectVideoModel shareInstance].isFrontCamera];
    [TKDirectVideoModel shareInstance].isFrontCamera=![TKDirectVideoModel shareInstance].isFrontCamera;
}

#pragma mark lazyloading
/**
 <AUTHOR> 2019年09月04日20:23:33
 @初始化懒加载queueView
 @return queueView
 */
-(TKOpenQueueView *)queueView{
    if (!_queueView) {
        _queueView=[[TKOpenQueueView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, self.view.frame.size.height) requestParams:self.requestParams];
        _queueView.delegate=self;
    }
    return _queueView;
}

/**
 <AUTHOR> 2019年09月07日14:16:48
 @初始化懒加载queueView
 @return queueView
 */
-(TKOpenVideoChatView *)videoChatView{
    if (!_videoChatView) {
        _videoChatView=[[TKOpenVideoChatView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, self.view.frame.size.height) requestParams:self.requestParams];
        _videoChatView.delegate=self;
    }
    return _videoChatView;
}

/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载提示layer
 @return 提示layer
 */
-(TKLayerView *)layerView{
    if (!_layerView) {
        _layerView=[[TKLayerView alloc] initContentView:self.view withBtnTextColor:nil];
        [_layerView setShowTipDuration:2];
    }
    return _layerView;
}

/**
 <AUTHOR> 2022年05月25日09:43:41
 @初始化懒加载videoAlertView
 @return videoAlertView
 */
-(TKVideoAlertView *)videoAlertView{
    if (!_videoAlertView) {
        _videoAlertView=[[TKVideoAlertView alloc] initWithFrame:self.view.frame requestParams:self.requestParams];
        _videoAlertView.delegate=self;
    }
    return _videoAlertView;
}

/**
<AUTHOR> 2023年01月28日14:20:413
@初始化懒加智能双向管理类
@return 智能双向管理类
*/
- (id)smartTwoVideoManager {
    if (!_smartTwoVideoManager) {
        _smartTwoVideoManager = [[TKSmartTwoVideoManager alloc] initWithConfig:self.requestParams];
        _smartTwoVideoManager.delegate = self;
    }
    return _smartTwoVideoManager;
}
@end
