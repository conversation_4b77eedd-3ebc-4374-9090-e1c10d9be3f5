//
//  TKReadAgreeView.m
//  TKOpenDemo
//
//  Created by <PERSON>ie on 2020/5/25.
//  Copyright © 2020 Thinkive. All rights reserved.
//

#import "TKVideoReadAgreeView.h"

@interface TKVideoReadAgreeView()
@property (nonatomic, strong) NSMutableDictionary *param;
@property (nonatomic, strong) UITextView *textView;
@property (nonatomic, strong) NSTimer *timer;
@end

@implementation TKVideoReadAgreeView

-(instancetype)initWithFrame:(CGRect)frame param:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        self.param=param;
        self.backgroundColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        [self viewInit];
    }
    return self;
}

#pragma mark 按钮事件
-(void)removeSelf{
    if (self.timer) {
        [self.timer invalidate];
        self.timer = nil;
    }
    if (self.delegate &&[self.delegate  respondsToSelector:@selector(removeReadAgreeView)]) {
        [self.delegate removeReadAgreeView];
    }
}

-(void)showTextView{
    //隐藏隐式动画
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    [self.textView flashScrollIndicators];
    [CATransaction commit];
    
}


/**
<AUTHOR> 2020年03月16日09:45:05
@初始页面
*/
-(void)viewInit{
    
    //标题提示区域
    float titleX=0;
    float titleY=14;
    float titleWidth=self.TKWidth;
    float titleHeight=25;
    UILabel *titleLabel=[[UILabel alloc] initWithFrame:CGRectMake(titleX, titleY, titleWidth, titleHeight)];
    titleLabel.text=self.param[@"title"];
    titleLabel.textAlignment=NSTextAlignmentCenter;
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:18];
    titleLabel.textColor=[TKUIHelper colorWithHexString:self.param[@"mainColor"]?self.param[@"mainColor"]:@"#2F85FF"];
    [self addSubview:titleLabel];
    
    
    float cancelBtnWidth=32;
    float cancelBtnTopGap=15;
    
    //阅读文本
    float readTextX=0;
    float readTextY=titleY+titleHeight+12;
    float readTextWidth=self.TKWidth;
    float readTextHeight=self.TKHeight-readTextY-cancelBtnTopGap-cancelBtnWidth-20;
    UITextView *readTextView=[[UITextView alloc] initWithFrame:CGRectMake(readTextX, readTextY, readTextWidth, readTextHeight)];
    readTextView.text=self.param[@"content"];
    readTextView.editable = NO;
    readTextView.textColor=[TKUIHelper colorWithHexString:@"#000000"];
    readTextView.backgroundColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
    readTextView.textAlignment=NSTextAlignmentLeft;
    //四周边距
    readTextView.textContainerInset =UIEdgeInsetsMake(0.0f, 20.0f, 0.0f, 20.0f);
    readTextView.font = [UIFont fontWithName:@"PingFangSC-Medium" size:18];
    [self addSubview:readTextView];
    self.textView=readTextView;
    if (self.timer) {
        [self.timer invalidate];
        self.timer = nil;
    }
    self.timer=[NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(showTextView) userInfo:nil repeats:YES];
    
    //取消按钮
    float cancelBtnX=(self.TKWidth-cancelBtnWidth)/2;
    float cancelBtnY=cancelBtnTopGap+readTextY+readTextHeight;
    UIButton *cancelBtn=[[UIButton alloc] initWithFrame:CGRectMake(cancelBtnX, cancelBtnY, cancelBtnWidth, cancelBtnWidth)];
    
    UIImage *cancelImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/tk_read_cancel.png", TK_OPEN_RESOURCE_NAME]];
    if (self.param[@"mainColor"]) {
        cancelImg = [cancelImg imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [cancelBtn setTintColor:[TKUIHelper colorWithHexString:self.param[@"mainColor"]]];
    }
    [cancelBtn setImage:cancelImg forState:UIControlStateNormal];
    [cancelBtn addTarget:self action:@selector(removeSelf) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:cancelBtn];
    
    //边框图
    UIImageView *rectImgView=[[UIImageView alloc] initWithFrame:CGRectMake(0, 0, self.TKWidth, self.TKHeight)];
    
    UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/tk_read_rect.png", TK_OPEN_RESOURCE_NAME]];
    if (self.param[@"mainColor"]) {
        img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [rectImgView setTintColor:[TKUIHelper colorWithHexString:self.param[@"mainColor"]]];
    }
    [rectImgView setImage:img];
    [self addSubview:rectImgView];
}

@end
