//
//  TKOpenVideoChatView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2019/9/7.
//  Copyright © 2019 thinkive. All rights reserved.
//

#import "TKOpenVideoChatView.h"
#import "TKVideoMsgTableViewCell.h"
#import "TKVideoReadAgreeView.h"

@interface TKOpenVideoChatView()<UITableViewDelegate,UITableViewDataSource,TKVideoReadAgreeViewDelegate>

@property (nonatomic, strong) UIView *topBgView;//顶部底层图
@property (nonatomic, strong) UIButton *cancelBtn;//挂断视频按钮
//@property (nonatomic, strong) UILabel *netStatusLabel;//tchat网络状态好坏文本
@property (nonatomic, strong) UIButton *switchCameraBtn;//切换摄像头按钮

@property (nonatomic, strong) UIImageView *headImgView;//头像指示框


@property (nonatomic,strong) UITableView *tableView;//展示后台推送数据的tableview
@property (nonatomic,strong) NSMutableArray *messageArray;

@property (nonatomic, strong) TKVideoReadAgreeView *tkReadAgreeView;//协议阅读文本

@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@end

@implementation TKOpenVideoChatView

/**
 *<AUTHOR> 2019年09月07日14:20:15
 *@初始化新版视频见证页面
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self=[super initWithFrame:frame];
    if (self) {
        self.requestParams=requestParams;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.requestParams[@"mainColor"]=nil;
        }
        [self viewInit];
    }
    return self;
}
/**
 <AUTHOR> 2019年09月07日14:20:48
 @初始化新版视频见证页面
 */
-(void)viewInit{
    self.messageArray=[[NSMutableArray alloc] init];
    [self setBackgroundColor:[UIColor whiteColor]];
    [self addSubview:self.meVideoView];
    
    NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f].CGColor,(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.0f].CGColor, nil];
    CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
    
    
    //宽固定等比算高
    float remoteVideoViewWidth=(UISCREEN_WIDTH-(10.0f+16.0f+10.0f))*0.35;
    float remoteVideoViewHeight=remoteVideoViewWidth/4.0f*3.0f;
    btoGradientLayer.frame = CGRectMake(0, 0, self.TKWidth, STATUSBAR_HEIGHT+NAVBAR_HEIGHT+remoteVideoViewHeight);
    btoGradientLayer.startPoint = CGPointMake(0.5, 0);
    btoGradientLayer.endPoint = CGPointMake(0.5, 1);
    [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
    [self.layer addSublayer:btoGradientLayer]; //设置颜色渐变
    
    [self addSubview:self.topBgView];
    [self.topBgView addSubview:self.topTipLabel];
    
//    [self.topBgView addSubview:self.netStatusLabel];
    [self.topBgView addSubview:self.cancelBtn];
    [self.topBgView addSubview:self.switchCameraBtn];
    [self.topBgView addSubview:self.networkLabel];
    
    if ([self.requestParams[@"isShowHeadRect"] isEqualToString:@"1"]) {
        //需要人脸指示框
        [self addSubview:self.headImgView];
    }
    
    if (self.requestParams[@"isShowHangUp"] && [self.requestParams[@"isShowHangUp"] integerValue] == 0) {
        //需要隐藏挂断按钮
        [self.cancelBtn setHidden:YES];
    }
    
    [self addSubview:self.remoteVideoView];
    [self addSubview:self.tableView];
    

}


/**
*<AUTHOR> 展示坐席提示语
*/
-(void)appendServerMessage:(NSString *)msg{
    [self.messageArray addObject:msg];
    [self.tableView reloadData];
    NSInteger s = [self.tableView numberOfSections];  //有多少组
    if (s<1){
         return;  //无数据时不执行 要不会crash
    }
    NSInteger r = [self.tableView numberOfRowsInSection:s-1]; //最后一组有多少行
    if (r<1){
        return;
    }
    //取最后一行数据
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:r-1 inSection:s-1];
    //滚动到最后一行
    [self.tableView scrollToRowAtIndexPath:indexPath atScrollPosition:UITableViewScrollPositionBottom animated:YES];
}

/**
*<AUTHOR> 风险协议等客户阅读协议展示
*/
-(void)showReadAgree:(NSMutableDictionary *)param{
    if ([param[@"action"] isEqualToString:@"1"]) {
        //显示风险阅读协议
        if (!_tkReadAgreeView) {
            float x=15;
            float width=self.TKWidth-(x*2);
            float height=self.TKHeight*0.5f;
            float y=(self.TKHeight-height)*2/5;
            
            //计算文本对应高度
            UILabel *txtLable=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, 10, 10)];
            txtLable.numberOfLines=0;
            txtLable.textAlignment=NSTextAlignmentLeft;
            txtLable.font = [UIFont fontWithName:@"PingFangSC-Medium" size:18];
            txtLable.text=param[@"content"];
            CGSize labelSize=[txtLable sizeThatFits:CGSizeMake((width-40), MAXFLOAT)];
            float boxHeight=labelSize.height+14+25+12+15+32+20;
            height=height>(boxHeight)?boxHeight:height;
            
            _tkReadAgreeView=[[TKVideoReadAgreeView alloc] initWithFrame:CGRectMake(x, y, width, height) param:param];
            _tkReadAgreeView.delegate=self;
        }
        self.tkReadAgreeView=_tkReadAgreeView;
        [self addSubview:self.tkReadAgreeView];
    }else if ([param[@"action"] isEqualToString:@"2"]){
        //隐藏风险阅读协议
        if(self.tkReadAgreeView){
            [self.tkReadAgreeView removeFromSuperview];
            self.tkReadAgreeView=nil;
        }
    }
}

/**
*<AUTHOR> 坐席信息展示
*/
-(void)showServiceInfo:(NSString *)info{
    //0：坐席视频画面下方，1：手机屏幕底部；默认0
    if([self.requestParams[@"serviceInfoShowPosition"] intValue]==1){
        UILabel *label=[[UILabel alloc] init];
        label.numberOfLines=0;
        label.textAlignment=NSTextAlignmentLeft;
        label.font=[UIFont fontWithName:@"PingFang SC" size:15.0f];
        label.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        label.backgroundColor=[UIColor clearColor];
        label.text=info;
        float x=10;
        float labelWidth=self.TKWidth-2*x;
        CGSize size=[label sizeThatFits:CGSizeMake(labelWidth, MAXFLOAT)];
        float y=self.TKHeight-size.height-IPHONEX_BUTTOM_HEIGHT;
        label.frame=CGRectMake(x, y, labelWidth, size.height);
        
        
        //底部渐变
        float height=size.height+IPHONEX_BUTTOM_HEIGHT;
        float width=self.TKWidth;
        float bgViewX=0;
        float bgViewY=self.TKHeight-height;
        UIView *bottomBgView=[[UIView alloc] initWithFrame:CGRectMake(bgViewX, bgViewY, width, height)];
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.00f].CGColor,(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
        btoGradientLayer.frame = CGRectMake(0, 0, width, height);
        btoGradientLayer.startPoint = CGPointMake(0.5, 0);
        btoGradientLayer.endPoint = CGPointMake(0.5, 1);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        [bottomBgView.layer addSublayer:btoGradientLayer]; //设置颜色渐变
        
        [self addSubview:bottomBgView];
        [self addSubview:label];
    }else{
        UILabel *label=[[UILabel alloc] init];
        label.numberOfLines=0;
        label.textAlignment=NSTextAlignmentLeft;
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            label.font=[UIFont fontWithName:@"PingFang SC" size:18.0f];
        }else{
            label.font=[UIFont fontWithName:@"PingFang SC" size:13.0f];
        }
        
        label.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        label.backgroundColor=[UIColor clearColor];
        label.text=info;
        float labelWidth=self.remoteVideoView.TKWidth-12;
        CGSize size=[label sizeThatFits:CGSizeMake(labelWidth, MAXFLOAT)];
        label.frame=CGRectMake(self.remoteVideoView.TKLeft+6, self.remoteVideoView.TKBottom+6, labelWidth, size.height);
        
        CGRect pathRect=CGRectMake(self.remoteVideoView.TKLeft, self.remoteVideoView.TKBottom, label.TKWidth+12, label.TKHeight+12);
        UIBezierPath *path=[UIBezierPath bezierPathWithRoundedRect:pathRect byRoundingCorners:UIRectCornerBottomLeft|UIRectCornerBottomRight cornerRadii:CGSizeMake(3, 3)];
        //创建CAShapeLayer
        CAShapeLayer *shapeLayer=[CAShapeLayer layer];
        shapeLayer.fillColor=[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#51B4FE" alpha:0.5].CGColor;;
        shapeLayer.path=path.CGPath;
        [self.layer addSublayer:shapeLayer];
        [self addSubview:label];
    }
}

///**
//*<AUTHOR> tchat网络状态展示
//*/
//-(void)showNetStatus:(NSString *)info withColor:(UIColor *)color{
//    self.netStatusLabel.text=info;
//    self.netStatusLabel.textColor=color;
//}

#pragma mark TKVideoReadAgreeViewDelegate
-(void)removeReadAgreeView{
    [self.tkReadAgreeView removeFromSuperview];
    self.tkReadAgreeView=nil;

}


#pragma mark 按钮点击事件
-(void)backAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(hangUp)]) {
        [self.delegate hangUp];
    }
}

-(void)switchCameraAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(switchCamera)]) {
        [self.delegate switchCamera];
    }
}

#pragma mark UITableViewDelegate
//返回分区
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}
//返回分区单元格数量
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.messageArray.count;
}
//设置单元格
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
   //这里单元格不重用，以便横竖屏切换界面适应
    TKVideoMsgTableViewCell *cell=[[TKVideoMsgTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:[NSString stringWithFormat:@"cell%ld",(long)indexPath.row] msg:[self.messageArray objectAtIndex:indexPath.row] headStringColor:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2" msgTextSize:self.requestParams[@"msgTextSize"]];
    cell.selectionStyle=UITableViewCellSelectionStyleNone;
    return cell;

}
//设置单元格高度
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return [TKVideoMsgTableViewCell heightForRow:[self.messageArray objectAtIndex:indexPath.row] msgTextSize:self.requestParams[@"msgTextSize"]];
}


#pragma mark lazyloading
/**
 *  <AUTHOR> 2019年09月07日15:05:54
 *  @初始化懒加载meVideoView
 *  @return  meVideoView
 */
-(UIView *)meVideoView{
    if (!_meVideoView) {
        _meVideoView=[[UIView alloc] initWithFrame:self.frame];
        _meVideoView.backgroundColor=[UIColor whiteColor];
        _meVideoView.tag=3001;
    }
    return _meVideoView;
}

/**
 *  <AUTHOR> 2019年09月08日14:05:04
 *  @初始化懒加载topBgView
 *  @return  topBgView
 */
-(UIView *)topBgView{
    if (!_topBgView) {
        float height=STATUSBAR_HEIGHT+NAVBAR_HEIGHT;
        float width=self.TKWidth;
        float x=0;
        float y=0;
        _topBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
//        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f].CGColor,(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.05f].CGColor, nil];
//        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
//
//        btoGradientLayer.frame = CGRectMake(0, 0, width, height);
//        btoGradientLayer.startPoint = CGPointMake(0.5, 0);
//        btoGradientLayer.endPoint = CGPointMake(0.5, 1);
//        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
//        [_topBgView.layer addSublayer:btoGradientLayer]; //设置颜色渐变
    }
    return _topBgView;
}

/**
 *  <AUTHOR> 2019-09-08 14:09:46
 *  @初始化懒加载topTipLabel
 *  @return  topTipLabel
 */
-(UILabel *)topTipLabel{
    if (!_topTipLabel) {
        float x=0;
        float y=STATUSBAR_HEIGHT;
        float width=self.topBgView.TKWidth;
        float height=NAVBAR_HEIGHT;
        _topTipLabel = [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _topTipLabel.text= @"00:00";
        _topTipLabel.font= [UIFont fontWithName:@"PingFang SC" size:23];
        _topTipLabel.textColor = [UIColor whiteColor];
        _topTipLabel.textAlignment=NSTextAlignmentCenter;
        _topTipLabel.tag=3003;

    }
    return _topTipLabel;
}


/**
 *  <AUTHOR> 2019年09月08日14:16:35
 *  @初始化懒加载networkLabel
 *  @return  networkLabel
 */
-(UILabel *)networkLabel{
    if (!_networkLabel) {
        float y=STATUSBAR_HEIGHT;
        float width=80;
        float height=NAVBAR_HEIGHT;
        float x=self.switchCameraBtn.TKLeft-width;
        _networkLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _networkLabel.text = @"上行：00KB/s\n下行：00KB/s";
        _networkLabel.font = [UIFont fontWithName:@"PingFang SC" size:10];
        _networkLabel.textColor = [UIColor whiteColor];
        _networkLabel.numberOfLines=0;
        _networkLabel.textAlignment=NSTextAlignmentCenter;

        _networkLabel.tag=2003;
    }
    return _networkLabel;
}



///**
// *  <AUTHOR> 2022年05月09日13:06:29
// *  @初始化懒加载netStatusLabel
// *  @return  netStatusLabel
// */
//-(UILabel *)netStatusLabel{
//    if (!_netStatusLabel) {
//        float width=48;
//        float height=12;
//        float x=self.topBgView.TKWidth-width-10.0f;
//        float y=STATUSBAR_HEIGHT+(NAVBAR_HEIGHT-height)/2.0f;
//        _netStatusLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
//        _netStatusLabel.font = [UIFont fontWithName:@"PingFang SC" size:12];
//        _netStatusLabel.backgroundColor=[UIColor clearColor];
//        _netStatusLabel.numberOfLines=0;
//        _netStatusLabel.textAlignment=NSTextAlignmentCenter;
//    }
//    return _netStatusLabel;
//}


/**
 *  <AUTHOR> 2019年09月08日14:32:34
 *  @初始化懒加载cancelBtn
 *  @return  cancelBtn
 */
-(UIButton *)cancelBtn{
    if (!_cancelBtn) {
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            float backBtnWidth=71;
            float backBtnheight=32;
            float backBtnX=20.0f;
            float backBtnY=6+STATUSBAR_HEIGHT;
            _cancelBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
            _cancelBtn.clipsToBounds = YES;
            [_cancelBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
            [_cancelBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -4, 0, 4)]; // 图片往右偏了，需要往左偏回来

            [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
            _cancelBtn.layer.cornerRadius = 8.0f;
            [_cancelBtn setTitle:@"返回" forState:UIControlStateNormal];
        }else{
            float width=32;
            float height=32;
            float x=20.0f;
            float y=6+STATUSBAR_HEIGHT;
            _cancelBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
            _cancelBtn.clipsToBounds = YES;
            [_cancelBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
            [_cancelBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来

            [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
            _cancelBtn.layer.cornerRadius = width/2.0f;
        }

        
        [_cancelBtn addTarget:self action:@selector(backAction)
          forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelBtn;
}


/**
 *  <AUTHOR> 2019年09月08日14:32:34
 *  @初始化懒加载switchCameraBtn
 *  @return  switchCameraBtn
 */
-(UIButton *)switchCameraBtn{
    if (!_switchCameraBtn) {
        float width=32;
        float height=32;
        float x=self.TKWidth-width-15;
        float y=6+STATUSBAR_HEIGHT;
        _switchCameraBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _switchCameraBtn.clipsToBounds = YES;
        [_switchCameraBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_switchCamera_btn.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_switchCameraBtn setImageEdgeInsets:UIEdgeInsetsMake(6, 6, 8, 6)];

        [_switchCameraBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
        _switchCameraBtn.layer.cornerRadius = width/2.0f;
        
        [_switchCameraBtn addTarget:self action:@selector(switchCameraAction)
          forControlEvents:UIControlEventTouchUpInside];
    }
    return _switchCameraBtn;
}

/**
 *  <AUTHOR> 2019年09月07日15:06:43
 *  @初始化懒加载headImgView
 *  @return  headImgView
 */
-(UIImageView *)headImgView{
    if (!_headImgView) {
        float width=self.TKWidth;
        float height=width;
        float x=0;
        float y=(self.TKHeight-width-self.topBgView.TKBottom)/2.0f+self.topBgView.TKBottom;
        _headImgView=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
        
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/video_head_box.png", TK_OPEN_RESOURCE_NAME]];
        if (self.requestParams[@"mainColor"]) {
            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_headImgView setTintColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
        }
        [_headImgView setImage:img];
        
    }
    return _headImgView;
}



/**
 *  <AUTHOR> 2019年09月07日15:24:04
 *  @初始化懒加载remoteVideoView
 *  @return remoteVideoView
 */
-(UIImageView *)remoteVideoView{
    if (!_remoteVideoView) {
        
        //宽固定等比算高
        float width=(UISCREEN_WIDTH-(10.0f+16.0f+10.0f))*0.35;
        float height=width/4.0f*3.0f;

        float y=self.topBgView.TKBottom;
        
        float x=self.TKWidth-10-width;
        if ([self.requestParams[@"isServiceVideoLeft"] intValue]==1) {
         //坐席画面靠左
            x=10;
        }
        _remoteVideoView=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _remoteVideoView.backgroundColor=[UIColor whiteColor];
        _remoteVideoView.tag=2000;
    }
    return _remoteVideoView;
}

/**
*  <AUTHOR> 2020年03月16日11:55:14
*  @初始化懒加载tableView
*  @return tableView
*/
-(UITableView *)tableView{
    if (!_tableView) {
        float height=[TKStringHelper isEmpty:self.requestParams[@"msgBoxHeight"]] ? 210 : [self.requestParams[@"msgBoxHeight"] intValue];

        float x=10;
        float width=(UISCREEN_WIDTH-(10.0f+16.0f+10.0f))*0.65;
        float y=self.topBgView.TKBottom;
        if ([self.requestParams[@"isServiceVideoLeft"] intValue]==1) {
         //聊天画面靠右
            x=self.TKWidth-10-width;
        }

        _tableView=[[UITableView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        objc_setAssociatedObject(_tableView,"kTableCustomSelfBgColor",@"1",OBJC_ASSOCIATION_COPY_NONATOMIC);
        [_tableView setBackgroundColor:[UIColor clearColor]];
        _tableView.delegate=self;
        _tableView.dataSource=self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        
//        _tableView.tableHeaderView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, height, width)];
    }
    return _tableView;
}



@end
