//
//  TKOpenQueueView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2019/9/4.
//  Copyright © 2019 thinkive. All rights reserved.
//

#import "TKOpenQueueView.h"
#import "TKDirectVideoModel.h"
@interface  TKOpenQueueView()
@property (nonatomic, strong) TKNavBar *navBar;//导航头
@property (nonatomic, strong) UIView *headAreaView;//头部区域背景图;
@property (nonatomic, strong) UIView *circularView;//中间圆形展示背景图
@property (nonatomic, strong) CALayer *circularLayer;//圆圈旋转效果图层
@property (nonatomic, strong) UIImageView *serviceIconImgView;//客服头像视图
@property (nonatomic, strong) UILabel *locationLabel;//位置文本大字
@property (nonatomic, strong) UILabel *bigTipLabel;//大文字提示文本
@property (nonatomic, strong) UILabel *minTipLabel;//小文字提示文本
@property (nonatomic, strong) UIButton *cancelQueueBtn;//取消排队按钮
@property (nonatomic, strong) UIButton *selfHelpWitnessBtn;//自助见证按钮
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@end

@implementation TKOpenQueueView

/**
 *<AUTHOR> 2019年09月04日20:09:36
 *@初始化新版视频排队页面
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self=[super initWithFrame:frame];
    if (self) {
        self.requestParams=requestParams;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.mainColorString=@"#2F85FF";
            self.requestParams[@"mainColor"]=nil;
        }else{
            self.mainColorString=self.requestParams[@"mainColor"];
        }
        [self viewInit];
    }
    return self;
}
/**
 <AUTHOR> 2019年04月27日13:49:06
 @初始化新版视频排队页面
 */
-(void)viewInit{
    [self setBackgroundColor:[TKUIHelper colorWithHexString:@"#FFFFFF"]];
    [self addSubview:self.navBar];
    [self addSubview:self.headAreaView];
    [self addSubview:self.circularView];
    [self.layer addSublayer:self.circularLayer];
    [self addSubview:self.serviceIconImgView];
    [self addSubview:self.locationLabel];
    [self addSubview:self.bigTipLabel];
    [self addSubview:self.minTipLabel];

    //按钮要再人数超限，或者无坐席时候再显示
    [self addSubview:self.cancelQueueBtn];
    [self.cancelQueueBtn setHidden:YES];
    [self addSubview:self.selfHelpWitnessBtn];
    [self.selfHelpWitnessBtn setHidden:YES];
}
#pragma mark 按钮点击事件
//取消排队按钮
-(void)backAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(cancelQueue)]) {
        [self.delegate cancelQueue];
    }
}

//切换自助见证按钮
-(void)selfHelpWitnessAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goSelfHelpWitness)]) {
        [self.delegate goSelfHelpWitness];
    }
}


#pragma mark 页面改变事件
-(void)showTipText:(NSString *)tipString queueLocation:(int)location currentStatus:(TKOpenQueueStatus)status tipType:(int)tipType{
    self.bigTipLabel.text=tipString;
    if (status==TKOpenQueueStatusLocation) {
        [self.serviceIconImgView removeFromSuperview];
        [self addSubview:self.locationLabel];
        self.locationLabel.text = [NSString stringWithFormat:@"%d",location];
        
        self.minTipLabel.text=[NSString stringWithFormat:@"您当前排在第%d位,请耐心等待",location];
        //有tip_type修改提示语，如果被插队或位置不变话术就用原来的(东亚前海的需求)
        switch (tipType) {
            case 1:
                self.minTipLabel.text=    [NSString stringWithFormat:@"您当前排在第%d位,%@正在接入,请稍等", location,[TKDirectVideoModel shareInstance].serviceTipString];
            case 2:
                self.minTipLabel.text= [NSString stringWithFormat:@"您当前排在第%d位,请耐心等待", location];
                break;
            case 3:
                self.minTipLabel.text= [NSString stringWithFormat:@"您当前排在第%d位,请耐心等待", location];
                break;
            default:
                break;
        }
        //如果h5入参，或者排队bus修改提示语调整
        if([TKStringHelper isNotEmpty:[TKDirectVideoModel shareInstance].queueSubMsg]){
            self.minTipLabel.text=[TKDirectVideoModel shareInstance].queueSubMsg;
        }
        
    }else{
        [self.locationLabel removeFromSuperview];
        [self addSubview:self.serviceIconImgView];
        //默认这些场景不需要小字提示语，如果h5排队bus传就有
        if([TKStringHelper isNotEmpty:[TKDirectVideoModel shareInstance].queueSubMsg]){
            self.minTipLabel.text=[TKDirectVideoModel shareInstance].queueSubMsg;
        }else{
            self.minTipLabel.text=@"";
        }
        
    }
}


-(void)showCancelBtn:(BOOL)needSelfHelpWitness{
    if(self.cancelQueueBtn.hidden){
        [self.cancelQueueBtn setHidden:NO];
        if(needSelfHelpWitness){
            [self.selfHelpWitnessBtn setHidden:NO];
        }else{
            //只显示取消排队按钮时候，弄宽取消排队按钮
            [self.cancelQueueBtn setTKWidth:(self.TKWidth-self.cancelQueueBtn.TKLeft*2.0f)];
        }
    }
}


#pragma mark lazyloading
/**
 <AUTHOR> 2019年08月28日20:23:21
 @初始化懒加载navbar
 @return navbar
 */
-(TKNavBar *)navBar{
    if (!_navBar) {
        _navBar=[[TKNavBar alloc] initWithFrame:CGRectMake(0, STATUSBAR_HEIGHT, self.frame.size.width, NAVBAR_HEIGHT)];
        _navBar.barTintColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        [_navBar setTitleTextAttributes:@{NSFontAttributeName:[UIFont fontWithName:@"PingFang SC" size:18.0f],NSForegroundColorAttributeName:[TKUIHelper colorWithHexString:@"#000000"]}];
        //创建导航栏内容
        UINavigationItem *navigationItem = [[UINavigationItem alloc]init];
        navigationItem.title=@"视频见证";
        [_navBar pushNavigationItem:navigationItem animated:NO];
        
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            float backBtnWidth=71;
            float backBtnheight=32;
            float backBtnX=20.0f;
            float backBtnY=6;
            UIButton  *backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
            backBtn.clipsToBounds = YES;


            UIImage *idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]];


            idAreaImg = [idAreaImg imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [backBtn setTintColor:[TKUIHelper colorWithHexString:@"#000000"]];


            [backBtn setImage:idAreaImg forState:UIControlStateNormal];
            [backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -4, 0, 4)]; // 图片往右偏了，需要往左偏回来

            [backBtn setBackgroundColor:[UIColor clearColor]];
            backBtn.layer.cornerRadius = 8.0f;
            [backBtn setTitle:@"返回" forState:UIControlStateNormal];
            [backBtn setTitleColor:[TKUIHelper colorWithHexString:@"#000000"] forState:UIControlStateNormal];
            [backBtn addTarget:self action:@selector(backAction) forControlEvents:UIControlEventTouchUpInside];
            [_navBar addSubview:backBtn];
        }else{
            //设置返回按钮
            [navigationItem setTKLeftItemWithTarget:self action:@selector(backAction) image:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_video_ic_back", TK_OPEN_RESOURCE_NAME]];
        }

        

    }
    return _navBar;
}


/**
 <AUTHOR> 2019年09月04日20:31:17
 @初始化懒加载headAreaView
 @return headAreaView
 */
-(UIView *)headAreaView{
    if (!_headAreaView) {
        float x=0;
        float y=self.navBar.TKBottom;
        float width=self.TKWidth;
        float height=310;
        _headAreaView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_headAreaView setBackgroundColor:[TKUIHelper colorWithHexString:@"#FFFFFF"]];
    }
    return _headAreaView;
}

/**
 <AUTHOR> 2019年08月29日10:18:17
 @初始化懒加载circularView
 @return circularView
 */
-(UIView *)circularView{
    if (!_circularView) {
        float y=self.navBar.TKBottom+50;
        float width=120;
        float height=120;
        float x=(self.TKWidth-width)/2;
        _circularView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _circularView.backgroundColor=[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#1061FF" alpha:0.05f];
        _circularView.layer.cornerRadius=width/2;
        
    }
    return _circularView;
}



/**
 <AUTHOR> 2019年08月29日10:18:17
 @初始化懒加载circularLayer
 @return circularLayer
 */
-(CALayer *)circularLayer{
    if (!_circularLayer) {
        
        float width=self.circularView.TKWidth;
        float height=self.circularView.TKHeight;
        float x=self.circularView.TKLeft;
        float y=self.circularView.TKTop;
        
        _circularLayer= [CALayer layer];
        _circularLayer.frame = CGRectMake(x, y, width, height);
        _circularLayer.masksToBounds=NO;
        //创建圆环
        UIBezierPath *bezierPath = [UIBezierPath bezierPathWithArcCenter:CGPointMake(width/2, width/2) radius:width/2-3 startAngle:0 endAngle:M_PI * 2 clockwise:YES];
        
        //圆环遮罩
        CAShapeLayer *shapeLayer=[CAShapeLayer layer];
        
        shapeLayer.fillColor = [UIColor clearColor].CGColor;
        shapeLayer.strokeColor =[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#1061FF"].CGColor;
//        [TKUIHelper colorWithHexString:@"#0354C2"].CGColor;
        shapeLayer.lineWidth = 5;
        shapeLayer.strokeStart = 0;
        shapeLayer.strokeEnd = 1;
        shapeLayer.lineCap = kCALineCapRound;
        shapeLayer.lineDashPhase = 0.8;
        shapeLayer.path = bezierPath.CGPath;
        //颜色渐变
        NSMutableArray *topColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#1061FF"].CGColor,(id)[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#1061FF" alpha:0.5f].CGColor, nil];
        CAGradientLayer *topGradientLayer = [CAGradientLayer layer];
        topGradientLayer.shadowPath = bezierPath.CGPath;
        topGradientLayer.frame = CGRectMake(0, 0, width, width/2);
        topGradientLayer.startPoint = CGPointMake(1, 0);
        topGradientLayer.endPoint = CGPointMake(0, 0);
        [topGradientLayer setColors:[NSArray arrayWithArray:topColors]];
        
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#1061FF" alpha:0.5f].CGColor,(id)[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#1061FF" alpha:0.0f].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
        btoGradientLayer.shadowPath = bezierPath.CGPath;
        btoGradientLayer.frame = CGRectMake(0, width/2, width, width/2);
        btoGradientLayer.startPoint = CGPointMake(0, 1);
        btoGradientLayer.endPoint = CGPointMake(1, 1);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        
        [_circularLayer addSublayer:topGradientLayer]; //设置颜色渐变
        [_circularLayer addSublayer:btoGradientLayer]; //设置颜色渐变
        [_circularLayer setMask:shapeLayer]; //设置圆环遮罩
        
        
        //动画
        CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
        rotationAnimation.fromValue = [NSNumber numberWithFloat:0];
        rotationAnimation.toValue = [NSNumber numberWithFloat:2.0*M_PI];
        rotationAnimation.repeatCount = MAXFLOAT;
        rotationAnimation.duration = 2;
        rotationAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
        rotationAnimation.removedOnCompletion=NO;
        [_circularLayer addAnimation:rotationAnimation forKey:@"queueCircularAnnimation"];
    }
    return _circularLayer;
}



/**
 <AUTHOR> 2019年08月29日10:18:17
 @初始化懒加载serviceIconImgView
 @return serviceIconImgView
 */
-(UIImageView *)serviceIconImgView{
    if (!_serviceIconImgView) {
        _serviceIconImgView=[[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 55, 62)];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/serviceIcon.png", TK_OPEN_RESOURCE_NAME]];
        if (self.requestParams[@"mainColor"]) {
            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_serviceIconImgView setTintColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
        }
        [_serviceIconImgView setImage:img];
        _serviceIconImgView.center=self.circularView.center;
    }
    return _serviceIconImgView;
}

/**
 <AUTHOR> 2019年09月06日16:11:53
 @初始化懒加载locationLabel
 @return locationLabel
 */
-(UILabel *)locationLabel{
    if (!_locationLabel) {
        float width=64;
        float height=72;
        _locationLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, width, height)];
        _locationLabel.textAlignment=NSTextAlignmentCenter;
        _locationLabel.font = [UIFont fontWithName:@"DIN Alternate" size:62];
        _locationLabel.textColor =[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#1061FF"];
        _locationLabel.center=self.circularView.center;

    }
    return _locationLabel;
}

/**
 <AUTHOR> 2019年08月29日10:18:17
 @初始化懒加载bigTipLabel
 @return bigTipLabel
 */
-(UILabel *)bigTipLabel{
    if (!_bigTipLabel) {
        float x=0;
        float width=self.TKWidth;
        float height=27;
        float y=self.circularView.TKBottom+36;
        _bigTipLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _bigTipLabel.textAlignment=NSTextAlignmentCenter;
        _bigTipLabel.textColor=[TKUIHelper colorWithHexString:@"#333333"];
        _bigTipLabel.font=[UIFont fontWithName:@"PingFang SC" size:22];
        _bigTipLabel.text=@"正在连接排队中…";
    }
    return _bigTipLabel;
}

/**
 <AUTHOR> 2019年09月06日16:11:58
 @初始化懒加载minTipLabel
 @return minTipLabel
 */
-(UILabel *)minTipLabel{
    if (!_minTipLabel) {
        float y=11+self.bigTipLabel.TKBottom;
        float x=0;
        float width=self.TKWidth;
        float height=20;
        _minTipLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _minTipLabel.textAlignment=NSTextAlignmentCenter;
        _minTipLabel.textColor=[TKUIHelper colorWithHexString:@"#999999"];
        _minTipLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
    }
    return _minTipLabel;
}




/**
 <AUTHOR> 2019年09月05日10:56:35
 @初始化懒加载cancelQueueBtn
 @return cancelQueueBtn
 */
-(UIButton *)cancelQueueBtn{
    if (!_cancelQueueBtn) {
        float x=15;
        float height=44;
        float width=(self.TKWidth-3*x)/2;
        float y=self.TKHeight-height-40-IPHONEX_BUTTOM_HEIGHT;
        _cancelQueueBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
        

        [_cancelQueueBtn setTitle:@"取消排队" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _cancelQueueBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _cancelQueueBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:16];
        }
        
        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [_cancelQueueBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]] forState:UIControlStateNormal];
            [_cancelQueueBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"] alpha:0.05f]];
        }else{
            [_cancelQueueBtn setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
            [_cancelQueueBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
        }
        
        _cancelQueueBtn.layer.cornerRadius=height/2.0f;
        [_cancelQueueBtn addTarget:self action:@selector(backAction)
          forControlEvents:UIControlEventTouchUpInside];
        
    }
    return _cancelQueueBtn;
}


/**
 <AUTHOR> 2019年04月13日14:02:47
 @初始化懒加载确认提交按钮
 @return 确认提交按钮
 */
-(UIButton *)selfHelpWitnessBtn{
    if (!_selfHelpWitnessBtn) {
        float x=self.cancelQueueBtn.TKLeft*2+self.cancelQueueBtn.TKWidth;
        float width=self.cancelQueueBtn.TKWidth;
        float height=self.cancelQueueBtn.TKHeight;
        float y=self.cancelQueueBtn.TKTop;
        _selfHelpWitnessBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
        if (self.requestParams[@"mainColor"]) {
            [_selfHelpWitnessBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
        }else{
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = CGRectMake(0, 0, width, height);
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius=height/2.0f;
            [_selfHelpWitnessBtn.layer addSublayer:btoGradientLayer]; //设置颜色渐变
        }

        [_selfHelpWitnessBtn setTitle:@"切换为自助见证" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _selfHelpWitnessBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _selfHelpWitnessBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        
        [_selfHelpWitnessBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        
        [_selfHelpWitnessBtn addTarget:self action:@selector(selfHelpWitnessAction) forControlEvents:UIControlEventTouchUpInside];
        
        _selfHelpWitnessBtn.layer.cornerRadius=height/2.0f;
    }
    return _selfHelpWitnessBtn;
}
@end
