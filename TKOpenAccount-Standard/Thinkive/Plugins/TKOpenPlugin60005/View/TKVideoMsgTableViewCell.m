//
//  TKVideoMessageTableViewCell.m
//  TKOpenDemo
//
//  Created by Vie on 2020/3/16.
//  Copyright © 2020 Thinkive. All rights reserved.
//

#import "TKVideoMsgTableViewCell.h"
#import "TKDirectVideoModel.h"
@interface TKVideoMsgTableViewCell ()
@property (nonatomic, strong) UILabel *messageLabel;//文本消息
@end

@implementation TKVideoMsgTableViewCell


-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier msg:(NSString *)msg headStringColor:(NSString *)colorString msgTextSize:(NSString *)msgTextSize{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self)
    {
        self.backgroundColor=[UIColor clearColor];
        [self viewInit:msg headStringColor:colorString msgTextSize:msgTextSize];
    }
    return self;
}


/**
<AUTHOR> 2020年03月16日09:45:05
@初始页面
*/
-(void)viewInit:(NSString *)msg headStringColor:(NSString *)colorString msgTextSize:(NSString *)msgTextSize{
    
//    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc]initWithString:msg];
//    if ([TKOpenViewStyleHelper shareInstance].isElder) {
//        [attributedStr addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:18] range:NSMakeRange(0, msg.length)];
//    }else{
//        [attributedStr addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, msg.length)];
//    }
//
//    [attributedStr addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:colorString] range:NSMakeRange(0, 2)];
//    [attributedStr addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FFFFFF"] range:NSMakeRange(2, msg.length - 2)];
    
    // 生成富文本
    NSMutableAttributedString *attributedStr = [self convertTextToHtmlString:msg textColor:colorString msgTextSize:msgTextSize];
    
    
    self.messageLabel=[[UILabel alloc] init];
    float xGap=6.0f;
    float yGap=6.0f;
//    self.messageLabel.font =  [UIFont fontWithName:@"PingFang SC" size:16];
    self.messageLabel.attributedText = attributedStr;
    self.messageLabel.numberOfLines = 0;
    self.messageLabel.lineBreakMode = NSLineBreakByCharWrapping;
    float width=(UISCREEN_WIDTH-(10.0f+16.0f+10.0f))*0.65;
    CGSize size=[self.messageLabel sizeThatFits:CGSizeMake(width-xGap*2, MAXFLOAT)];
    self.messageLabel.frame=CGRectMake(xGap, yGap, size.width, size.height);
//    self.messageLabel.textColor=[UIColor whiteColor];

    
    UIView *bgView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, size.width+xGap*2, size.height+yGap*2)];
    bgView.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.2f];
    bgView.layer.cornerRadius=8.0f;
    [self.contentView addSubview:bgView];
    [bgView addSubview:self.messageLabel];
}

/**
 *  <AUTHOR> 2020年03月16日09:18:07
 *  @brief  行高
 *  @param msg
 *  @return
 */
+(CGFloat)heightForRow:(NSString *)msg msgTextSize:(NSString *)msgTextSize{
//    UILabel *messageLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    float xGap=6.0f;
    float yGap=6.0f;
//    if ([TKOpenViewStyleHelper shareInstance].isElder) {
//        messageLabel.font =  [UIFont fontWithName:@"PingFang SC" size:18];
//    }else{
//        messageLabel.font =  [UIFont fontWithName:@"PingFang SC" size:16];
//    }
//    messageLabel.text = msg;
//    messageLabel.numberOfLines = 0;
//    messageLabel.lineBreakMode = NSLineBreakByCharWrapping;
    

    float width=(UISCREEN_WIDTH-(10.0f+16.0f+10.0f))*0.65;
    

//    CGSize size=[messageLabel sizeThatFits:CGSizeMake(width-xGap*2, MAXFLOAT)];
    
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:msg textColor:@"#000000" msgTextSize:msgTextSize];
    // 计算文本的总高度
    CGSize size= [self getHTMLHeightByStr:attStr width:width-xGap*2];
    
    return (size.height+yGap*2+7);
}

/**
计算html字符串高度

@param str html 未处理的字符串
@param font 字体设置
@param lineSpacing 行高设置
@param width 容器宽度设置
@return 富文本高度
*/
+(CGSize)getHTMLHeightByStr:(NSMutableAttributedString *)str width:(CGFloat)width
{
   CGSize contextSize = [str boundingRectWithSize:(CGSize){width, CGFLOAT_MAX} options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size;
   return contextSize;

}

+(NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString msgTextSize:(NSString *)msgTextSize
{
    CGFloat pointSize = 16;
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        pointSize = 18;
    }
    if([TKStringHelper isNotEmpty:msgTextSize]){
        pointSize=[msgTextSize intValue];
    }
    NSString *tempDivString = nil;
    
    // 是否是带有"坐席  "的纯文本
    NSString *prefixStr = [NSString stringWithFormat:@"%@  ",[TKDirectVideoModel shareInstance].serviceTipString];
    if ([text hasPrefix:prefixStr]) {
        
        NSString *insertHtmlStr = [NSString stringWithFormat:@"<span style=\"color:%@\">%@</span>", colorString, prefixStr];
        NSString *subText = [text stringByReplacingOccurrencesOfString:prefixStr withString:@""];
        tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@%@</span>", (int)pointSize, @"#FFFFFF", insertHtmlStr, subText];
    } else {
        tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)pointSize, colorString, text];
        tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)pointSize, @"#FFFFFF", text];
    }
    
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}


- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString msgTextSize:(NSString *)msgTextSize
{
    CGFloat pointSize = 16;
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        pointSize = 18;
    }
    if([TKStringHelper isNotEmpty:msgTextSize]){
        pointSize=[msgTextSize intValue];
    }
    NSString *tempDivString = nil;
    text=[TKCommonUtil switchLabelToSpan:text];
    // 是否是带有"坐席  "的纯文本
    NSString *prefixStr = [NSString stringWithFormat:@"%@  ",[TKDirectVideoModel shareInstance].serviceTipString];
    if ([text hasPrefix:prefixStr]) {
        
        NSString *insertHtmlStr = [NSString stringWithFormat:@"<span style=\"color:%@\">%@</span>", colorString, prefixStr];
        NSString *subText = [text stringByReplacingOccurrencesOfString:prefixStr withString:@""];
        tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@%@</span>", (int)pointSize, @"#FFFFFF", insertHtmlStr, subText];
    } else {
        tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)pointSize, colorString, text];
        tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)pointSize, @"#FFFFFF", text];
    }
    
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

@end
