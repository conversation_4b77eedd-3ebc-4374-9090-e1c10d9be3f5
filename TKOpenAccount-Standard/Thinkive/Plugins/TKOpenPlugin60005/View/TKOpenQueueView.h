//
//  TKOpenQueueView.h
//  TKOpenAccount-Standard
//  开户新版排队页面
//  Created by <PERSON>ie on 2019/9/4.
//  Copyright © 2019 thinkive. All rights reserved.
//



typedef enum {
    TKOpenQueueStatusStart=0,//刚开始排队还未获取到排队信息
    TKOpenQueueStatusNOService=1,//暂无在线客服
    TKOpenQueueStatusLocation=2,//排队成功获取到排队位置
    TKOpenQueueStatusGetService=3,//已经分配到坐席，等待连接视屏服务器
    TKOpenQueueStatusConcentVideo=4//连接视频服务器
}TKOpenQueueStatus;

@protocol TKOpenQueueDelegate <NSObject>

//取消排队
-(void)cancelQueue;

//自助见证
-(void)goSelfHelpWitness;
@end

@interface TKOpenQueueView : UIView

@property (nonatomic, strong) id<TKOpenQueueDelegate> delegate;

/**
 *<AUTHOR> 2019年09月04日20:09:36
 *@初始化新版视频排队页面
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams;

/**
 <AUTHOR> 2019年09月06日14:08:17
 @提示排队提示语
 @param tipString提示文本
 @param location排队位置
 @param status当前排队状态
 @param tipType提示语类型
 */
-(void)showTipText:(NSString *)tipString queueLocation:(int)location currentStatus:(TKOpenQueueStatus)status tipType:(int)tipType;

/**
 <AUTHOR> 2019年09月06日14:08:17
 @显示取消按钮，是否额外需要显示自助见证按钮
 @param needSelfHelpWitness 是否还要显示自助见证按钮
 */
-(void)showCancelBtn:(BOOL)needSelfHelpWitness;
@end


