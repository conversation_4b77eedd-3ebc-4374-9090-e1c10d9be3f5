//
//  TKOpenPlugin60037.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/9/7.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "TKOpenPlugin60037.h"
#import "TKTakeBankPhotoViewController.h"
#import "UIViewController+TKAuthorityKit.h"

@interface TKOpenPlugin60037()<TKTakeBankPhotoResultDelegate>
{
     ResultVo *_resultVo;// 返回的结果
}

@end

@implementation TKOpenPlugin60037


- (ResultVo *)serverInvoke:(id)param{

    
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    
    dispatch_async(dispatch_get_main_queue(), ^{
       
        
        //是否在调用插件前展示介绍页面
        if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
            NSMutableArray *authArray=[[NSMutableArray alloc] init];
            [authArray addObject:@(TKAuthorizationType_Camera)];
            if ([param[@"isAlbum"] integerValue] != 0||[TKStringHelper isEmpty:param[@"isAlbum"]]) {
                [authArray addObject:@(TKAuthorizationType_Photo)];
            }
            [TKAuthorizationHelper requestAuthorization:authArray authCallBacks:nil btnCallBack:^{
                [self.currentViewCtrl tkIsCameraPermissions:^{
                    [self handleAuthorized:reqParam];
                }];
            }];
        }else{
            [self.currentViewCtrl tkIsCameraPermissions:^{
                [self handleAuthorized:reqParam];
            }];
        }
        

    
    });
    
    
    
    return _resultVo;
}



- (void)handleAuthorized:(id)param
{
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    

    TKTakeBankPhotoViewController *tCtl = [[TKTakeBankPhotoViewController alloc] init];
    tCtl.delegate=self;
    tCtl.param = param;
 
    [self.currentViewCtrl presentViewController:tCtl animated:YES completion:nil];
        
   
}
#pragma mark TKTakeBankPhotoResultDelegate
//结果信息
- (void)tkTakeBankPhotoDidComplete:(NSMutableDictionary *)result{
    [self iosCallJsWithParam:result];
}
@end
