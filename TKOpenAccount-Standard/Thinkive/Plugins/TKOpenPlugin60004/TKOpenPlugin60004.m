//
//  TKOpenPlugin60004.m
//  TKApp
//
//  Created by 叶璐 on 15/4/22.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKOpenPlugin60004.h"


@implementation TKOpenPlugin60004

/**
 *  <AUTHOR> 2014-11-27 12:11:58
 *
 *  功能描述：安装证书
 *  参考插件：对应phoneGap的initZsPlugin插件				
 *
 *  @param userId       用户ID
 *  @param type         指定证书的厂商（tw ：天威，zd ：中登）
 *  @param content      证书字串
 *
 *  @return
 *      publicKey     中登返回的公钥信息
 */
-(ResultVo *)serverInvoke:(id)param
{
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    NSString *content = reqParam[@"content"];
    NSString *userId = reqParam[@"userId"];
    if ([param[@"type"] isEqualToString:@"tw_new"]){
        userId = [NSString stringWithFormat:@"%@_tw_new",userId];
    }
    ResultVo *resultVo = [[ResultVo alloc]init];
    
    if ([TKStringHelper isEmpty:userId]) {
        resultVo.errorNo = -6000402;
        resultVo.errorInfo = @"证书厂商不能为空!";
        
        return resultVo;
    }
    
    if ([TKStringHelper isEmpty:content]) {
        resultVo.errorNo = -6000403;
        resultVo.errorInfo = @"证书字串不能为空!";
        
        return resultVo;
    }
    
    NSLog(@"-----------------content:%@", content);
    
    //未完成：初始化证书
    int ret =[TKCertLibHelper importCert:content userId:userId];

    if (ret == TK_ERR_CREATEP10_OK) {
        NSDictionary *dic = [NSDictionary dictionaryWithObjectsAndKeys:@"",@"publicKey",nil];
        resultVo.results = dic;
        resultVo.errorNo = 0;
    }
    else
    {
        resultVo.errorInfo = @"创建公私钥失败!";
        resultVo.results = nil;
        resultVo.errorNo = -6000404;
    }
    
    return resultVo;
}

@end
