//
//  TKOpenPlugin60007.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2019/7/18.
//  Copyright © 2019 thinkive. All rights reserved.
//

#import "TKOpenPlugin60007.h"
#import "TKLiveFaceViewController.h"
#import <MediaPlayer/MediaPlayer.h>


@interface TKOpenPlugin60007()<TKLiveFaceDelegate>{
    ResultVo *_resultVo;// 返回的结果
    NSMutableDictionary *h5Params;
    
}

@property (nonatomic, readwrite, assign) BOOL isInvokeing; // 是否正在被调用

@end

@implementation TKOpenPlugin60007
- (ResultVo *)serverInvoke:(id)param{
    
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
//    param[@"mainColor"]=@"#FD671A";
    
    h5Params=reqParam;
    // 检测入参是否有误
    if ([self IsErrorParam:reqParam]) {
        return _resultVo;
    }
    
    // 埋点-活体_调用
//    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
//    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
//    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect
//                         subEventName:TKPrivateSubEventNone
//                             progress:TKPrivateEventProgressStart
//                               result:TKPrivateEventResultNone
//                          orientation:TKPrivateVideoOrientationNone
//                      oneWayVideoType:TKPrivateOneWayVideoTypeNone
//                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
//                             eventDic:self.requestParam];

     dispatch_async(dispatch_get_main_queue(), ^{
         
         //是否在调用插件前展示介绍页面
         if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
             
             [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Camera)] authCallBacks:nil btnCallBack:^{
                 [self.currentViewCtrl tkIsCameraPermissions:^{
  
                     [self openLiveness:reqParam];

                 }];
             }];
         }else{
             [self.currentViewCtrl tkIsCameraPermissions:^{

                 [self openLiveness:reqParam];
  
             }];
         }
         

     });
    
    
    return nil;
}

-(void)openLiveness:(NSMutableDictionary *)reqParam{
    
    // 已在调用，拦截重复调用
    if (self.isInvokeing == YES) {
//        TKLogInfo(@"静音检查，拦截重复调用");
        return ;
    };
    
    // 标记正在调用
    self.isInvokeing = YES;
    
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [audioSession setCategory:AVAudioSessionCategoryPlayAndRecord  withOptions:AVAudioSessionCategoryOptionDefaultToSpeaker error:nil];
    [[AVAudioSession sharedInstance] setActive:YES error:nil];
                           
    CGFloat volume = audioSession.outputVolume;
    //默认音量调整支持h5控制
    float defaultVolume=TKSmartOpenVolume;
    if (h5Params[@"defaultVolume"]) {
        defaultVolume=[h5Params[@"defaultVolume"] intValue]/100.00f;
    }
    if ((volume+0.05)<defaultVolume) {
        //直接调整音量api还能使用，先改音量不提示用户
        MPMusicPlayerController *mp=[MPMusicPlayerController applicationMusicPlayer];
        mp.volume=defaultVolume;//0为最小1为最大
    }
    TKLiveFaceViewController *viewCtr=[[TKLiveFaceViewController alloc] initWithParam:reqParam];
    viewCtr.delegate=self;
    [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:viewCtr animated:YES completion:nil];
}

- (BOOL)IsErrorParam:(NSMutableDictionary *)reqParam
{
    ResultVo *resultVo = [[ResultVo alloc] init];
    
    _resultVo = resultVo;
    
   
    return NO;
}


//活体识别结果
- (void)tkLiveDetectDidComplete:(NSMutableDictionary *)result{
    [self iosCallJsWithParam:result];
    
    // 重置标志位
    self.isInvokeing = NO;
}

@end
