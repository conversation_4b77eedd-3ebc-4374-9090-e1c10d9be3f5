//
//  TKLiveFaceView.h
//  OneWayVideo
//  活体检测页面
//  Created by <PERSON>ie on 2019/4/3.
//  Copyright © 2019 Vie. All rights reserved.
//


typedef enum : NSInteger {
    TKLiveActionUnknown = -1, // 未知
    TKLiveActionBlink = 0, // 眨眼
    TKLiveActionMouth, // 张嘴
    TKLiveActionNod,   // 点头
    TKLiveActionShake, // 摇头
} TKLiveAction;

//单向视频视图代理，按钮等事件
@protocol TKLiveFaceViewDelegate <NSObject>


/**
 <AUTHOR> 2019年04月08日10:38:08
 @返回代理
 */
-(void)goBack;



@end

@interface TKLiveFaceView : UIView
@property (nonatomic, assign) CGRect boxRect;//人像取景框矩阵
@property (nonatomic, weak) id<TKLiveFaceViewDelegate> delegate;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数

-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param;


/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting;

/**
 <AUTHOR> 2019年04月15日15:22:00
 @活体继续识别
 */
-(void)liveContinue;

/// 更新活体动作gif
- (void)updateLiveActionGif:(TKLiveAction)liveAction;

//更新文字提示
-(void)updateTipLabel:(NSString *)string;
@end


