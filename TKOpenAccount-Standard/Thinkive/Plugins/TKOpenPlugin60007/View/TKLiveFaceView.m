//
//  TKLiveFaceView.m
//  OneWayVideo
//
//  Created by <PERSON>ie on 2019/4/3.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKLiveFaceView.h"

#define TK_LIVEFACE_PIECE_COLOR  [UIColor colorWithRed:0/255.0 green:13/255.0 blue:41/255.0 alpha:0.85/1.0]
#define TK_LIVEFACE_TIP_LABEL_COLOR [TKUIHelper colorWithHexString:@"#3CCDFF"]
#define TK_WAIT_COUNT_DOWN 0.9f

@interface TKLiveFaceView()

@property (nonatomic, strong) UIImageView *boxImgView;//人像取景框
@property (nonatomic, strong) UIImageView *boxImgBackgroundView;//人像取景背景框
@property (nonatomic, strong) UIView *topView,*bottomView,*leftView,*rightView;//顶部遮罩层,底部遮罩层,左部遮罩层，右部遮罩层
@property (nonatomic, strong) UIButton *backBtn;//返回按钮
@property (nonatomic, strong) TKGIFImageView *liveTipGifView;//活体界面提示gif图
@property (nonatomic, strong) UILabel *warningLabel; // 提示文本
//@property (nonatomic, strong) UILabel *bottomShowLabel;//提示文字展示
@property (nonatomic, strong) UITextView *bottomShowLabel;//提示文字展示
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@end

@implementation TKLiveFaceView

-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        self.requestParam=param;
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2772FE";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
        [self viewInit];
    }
    return self;
}

/**
 <AUTHOR> 2019年04月03日09:13:34
 @初始化单向视频默认界面
 */
-(void)viewInit{
    [self addSubview:self.boxImgBackgroundView];
    [self addSubview:self.boxImgView];
    [self addSubview:self.topView];
    [self addSubview:self.bottomView];
    [self addSubview:self.leftView];
    [self addSubview:self.rightView];
    [self addSubview:self.backBtn];
    [self addSubview:self.warningLabel];
    [self addSubview:self.bottomShowLabel];
    [self addSubview:self.liveTipGifView];
    
    //判断是否测试授权，增加测试版字样
    if (![self isSTLivenessDetectorAuth]) {
        UILabel *testLabel=[[UILabel alloc] init];
        testLabel.font=[UIFont systemFontOfSize:18.0f];
        testLabel.text=@"测试版";
        testLabel.textColor=[UIColor redColor];
        CGSize lableSize = [testLabel sizeThatFits:CGSizeMake(self.TKWidth, MAXFLOAT)];
        testLabel.frame=CGRectMake(12, self.TKHeight-lableSize.height - IPHONEX_BUTTOM_HEIGHT, lableSize.width, lableSize.height);
        [self addSubview:testLabel];
    }
}

#pragma mark 事件方法

/**
 @Auther Vie 2019年04月08日10:46:51

 @param sender 返回按钮点击事件
 */
-(void)backAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
        [self.delegate goBack];
    }
}



/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting{
        
    CGFloat horizontalMargin = 19;
    CGFloat verticalMargin = 6;
    CGSize size = [warningSting boundingRectWithSize:CGSizeMake(self.TKWidth - (self.bottomShowLabel.TKLeft + horizontalMargin) * 2, CGFLOAT_MAX)
                                             options:
                      NSStringDrawingUsesLineFragmentOrigin |
                      NSStringDrawingUsesFontLeading
                                                   attributes:@{ NSFontAttributeName: self.warningLabel.font}
                                             context:nil].size;
    CGFloat width = size.width > 0 ? (size.width + horizontalMargin * 2) : 0;
    CGFloat height = size.height > 0 ? (size.height + verticalMargin * 2) : 0;
    self.warningLabel.frame = CGRectMake(0, 0, width, height);
    self.warningLabel.center = CGPointMake(self.boxRect.origin.x + self.boxRect.size.width * 0.5, self.boxRect.origin.y + self.boxRect.size.height * 0.5);
    self.warningLabel.text = warningSting;
}


/**
 <AUTHOR> 2019年04月15日15:22:00
 @活体继续识别
 */
-(void)liveContinue{

}

/// 更新活体动作gif
- (void)updateLiveActionGif:(TKLiveAction)liveAction {

    [_liveTipGifView setImageByName:[self getLiveTipGifFullNameWithLiveAction:liveAction]];
}

//更新文字提示
-(void)updateTipLabel:(NSString *)string{
    self.bottomShowLabel.text = string;
    float height=42;
    float x=20;
    float width=self.TKWidth-2*x;//左右保持留白15
    
    CGSize lableSize = [_bottomShowLabel sizeThatFits:CGSizeMake(width, MAXFLOAT)];
    if(lableSize.height>height){
        height=lableSize.height+10;
    }

    float gap =40;
    //5s等小屏幕机型
    if (UISCREEN_WIDTH==320) {
        gap=25;
    }
    float y=self.boxRect.origin.y-height-gap;
    self.bottomShowLabel.frame=CGRectMake(x, y, width, height);
}
#pragma mark lazyloading

/**
 <AUTHOR> 2019年04月03日10:47:55
 @初始化懒加载人像取景框矩阵
 @return 人像取景框矩阵
 */
-(CGRect)boxRect{
    if (CGRectIsEmpty(_boxRect)) {
        //活体对准框要居中
        float boxRectX =38;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            boxRectX=32;
        }
        float boxRectWidth = self.TKWidth-boxRectX*2.0f;
        float boxRectHeight = boxRectWidth / 300.0f * 340.0f; // 图片宽高是300 * 340
        float boxRectY = (self.TKHeight-boxRectHeight)/2.0f;
        _boxRect=CGRectMake(boxRectX, boxRectY, boxRectWidth, boxRectHeight);
        
    }
    return _boxRect;
}


/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加载顶部遮罩层
 @return 顶部遮罩层
 */
-(UIView *)topView{
    if (!_topView) {
        _topView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, self.TKWidth, self.boxRect.origin.y)];
        _topView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _topView;
}


/**
 <AUTHOR> 2019年04月03日11:20:41
 @初始化懒加载底部遮罩层
 @return 底部遮罩层
 */
-(UIView *)bottomView{
    if (!_bottomView) {
        _bottomView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.size.height+self.boxRect.origin.y, self.frame.size.width, self.frame.size.height-self.boxRect.origin.y-self.boxRect.size.height)];
        _bottomView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _bottomView;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加左部遮罩层
 @return 左部遮罩层
 */
-(UIView *)leftView{
    if (!_leftView) {
        _leftView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.origin.y, self.boxRect.origin.x, self.boxRect.size.height)];
        _leftView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _leftView;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加右部遮罩层
 @return 右部遮罩层
 */
-(UIView *)rightView{
    if (!_rightView) {
        _rightView=[[UIView alloc] initWithFrame:CGRectMake(self.boxRect.origin.x+self.boxRect.size.width, self.boxRect.origin.y, self.boxRect.origin.x, self.boxRect.size.height)];
        _rightView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _rightView;
}



/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载底部文字展示
 @return 底部文字展示
 */
-(UITextView *)bottomShowLabel{
    if (!_bottomShowLabel) {

        _bottomShowLabel=[[UITextView alloc] init];
        
        _bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
        _bottomShowLabel.textAlignment=NSTextAlignmentCenter;
        _bottomShowLabel.textColor =[TKUIHelper colorWithHexString:@"#FFFFFF"];
//        _bottomShowLabel.numberOfLines=0;
        _bottomShowLabel.textContainerInset = UIEdgeInsetsMake(8, 0, 0, 0);


        _bottomShowLabel.layer.borderWidth=1.5f;

        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            _bottomShowLabel.layer.borderColor=[TKUIHelper colorWithHexString:self.mainColorString alpha:0.2f].CGColor;
        }else{
            _bottomShowLabel.layer.borderColor=[TKUIHelper colorWithHexString:@"#498FD5" alpha:0.2f].CGColor;
        }
        [_bottomShowLabel setBackgroundColor:[TKUIHelper colorWithHexString:@"#040D16" alpha:0.5f]];
        _bottomShowLabel.layer.cornerRadius=8.0f;

        _bottomShowLabel.clipsToBounds=YES;//子视图是否局限于视图的边界。
        if([TKStringHelper isEmpty:self.requestParam[@"prepareTip"]]){
            [self updateTipLabel:@"请您正对手机屏幕，开始刷脸。"];
        }else{
            [self updateTipLabel:self.requestParam[@"prepareTip"]];
        }
        

        
    }
    return _bottomShowLabel;
}




- (UILabel *)warningLabel{
    if (!_warningLabel) {
        _warningLabel = [UILabel new];
        _warningLabel.frame = CGRectMake(0, CGRectGetMaxY(self.boxRect) - 40 -150, 0, 40);
        _warningLabel.backgroundColor = [TKUIHelper colorWithHexString:@"#FD4D43" alpha:1.0f];
        _warningLabel.textColor = UIColor.whiteColor;
        _warningLabel.layer.cornerRadius = 20.0f;
        _warningLabel.layer.masksToBounds = YES;

        _warningLabel.textAlignment = NSTextAlignmentCenter;
        _warningLabel.font = [UIFont fontWithName:@"PingFangSC-Semibold" size:22];
        _warningLabel.numberOfLines = 0;
    }
    return _warningLabel;
}


/**
 <AUTHOR> 2019年04月01日14:48:01
 @初始化懒加载UIImageView人像取景框
 @return UIImageView人像取景框
 */
-(UIImageView *)boxImgView{
    if (!_boxImgView) {
       
        _boxImgView=[[UIImageView alloc] initWithFrame:self.boxRect];
        _boxImgView=[[UIImageView alloc] initWithFrame:self.boxRect];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_default_box.png", TK_OPEN_RESOURCE_NAME]];

//        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
//            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
//            [_boxImgView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
//        }
        [_boxImgView setImage:img];
    }
    return _boxImgView;
}

/**
 @初始化懒加载UIImageView人像取景背景框(外层的黑色边框)
 @return UIImageView人像取景背景框
 */
-(UIImageView *)boxImgBackgroundView{
    if (!_boxImgBackgroundView) {
       
        _boxImgBackgroundView = [[UIImageView alloc] initWithFrame:self.boxRect];
        [_boxImgBackgroundView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/<EMAIL>", TK_OPEN_RESOURCE_NAME]]];
    }
    return _boxImgBackgroundView;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            float backBtnWidth=71;
            float backBtnheight=32;
            float backBtnX=20.0f;
            float backBtnY=6+STATUSBAR_HEIGHT;
            _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
            _backBtn.clipsToBounds = YES;
            [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
            [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -4, 0, 4)]; // 图片往右偏了，需要往左偏回来

            [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
            _backBtn.layer.cornerRadius = 8.0f;
            [_backBtn setTitle:@"返回" forState:UIControlStateNormal];
        }else{
            float backBtnWidth=32;
            float backBtnheight=32;
            float backBtnX=20.0f;
            float backBtnY=6+STATUSBAR_HEIGHT;
            _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
            _backBtn.clipsToBounds = YES;
            [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
            [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来

            [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
            _backBtn.layer.cornerRadius = backBtnWidth/2.0f;
        }


        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}


/**
 <AUTHOR> 2019年04月09日16:00:05
 @初始化懒加载活体界面提示gif图
 @return 活体界面提示gif图
 */
-(TKGIFImageView *)liveTipGifView{
    if (!_liveTipGifView) {
        float liveTipGifViewWidth=90;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            liveTipGifViewWidth=76;
        }
        float liveTipGifViewX=(self.frame.size.width-liveTipGifViewWidth)/2;

        float gap=40;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=20;
        }
        float liveTipGifViewY=self.TKHeight-liveTipGifViewWidth-gap-IPHONEX_BUTTOM_HEIGHT;
        _liveTipGifView=[[TKGIFImageView alloc] initWithFrame:CGRectMake(liveTipGifViewX, liveTipGifViewY, liveTipGifViewWidth, liveTipGifViewWidth)];
        [_liveTipGifView setImageByName:[self getLiveTipGifFullNameWithLiveAction:TKLiveActionUnknown]];
    }
    return _liveTipGifView;
}

- (NSString *)getLiveTipGifFullNameWithLiveAction:(TKLiveAction)liveAction {
    
    NSString *gifName = [self liveTipGifName][@(liveAction)];
    gifName = [TKStringHelper isNotEmpty:gifName] ? gifName : @"tk_live_tip"; // 容错处理
    NSString *liveTipGifFullName = [NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/%@.gif", TK_OPEN_RESOURCE_NAME, gifName];
    return liveTipGifFullName;
}

- (NSDictionary *)liveTipGifName {
    // 0:眨眼 1:张嘴 2:点头 3:摇头
    return @{
        @(0) : @"tk_live_blink",
        @(1) : @"tk_live_mouth",
        @(2) : @"tk_live_nod",
        @(3) : @"tk_live_shake",
    };
}

/**
 *  <AUTHOR> 2021年08月03日09:37:35
 *  是否商汤活体正式授权（小于X月判断为测授权）
 */
-(BOOL)isSTLivenessDetectorAuth{
    TKLogInfo(@"SenseID_Liveness_Silent.lic:授权检查");
    //授权文件解密方法
    NSString *authLicPath = [[NSBundle mainBundle] pathForResource:@"SenseID_Liveness_Silent" ofType:@"lic"];
    NSFileManager *fm = [[NSFileManager alloc] init];
    if (![fm fileExistsAtPath:authLicPath])
    {
        TKLogInfo(@"SenseID_Liveness_Silent.lic:授权文件不存在");
        return false;
    }
    else
    {
        NSString *licString = [[NSString alloc] initWithContentsOfFile:authLicPath encoding:NSUTF8StringEncoding error:nil];
        NSArray *authArray = [licString componentsSeparatedByString:@"\n"];
        if (authArray.count < 4)
        {
            TKLogInfo(@"TKOpenAuthorization.lic:授权文件没有看到时间认为是测试授权");
            return false;
        }
        else
        {
            NSArray *timeInfoArray = [authArray[3] componentsSeparatedByString:@":"];
            if (timeInfoArray.count < 2)
            {
                TKLogInfo(@"TKOpenAuthorization.lic:授权文件没有看到时间认为是测试授权");
                return false;
            }else{
                //授权文件时间字符串去空格
                NSString *timeString=[TKStringHelper stringWithTrim:timeInfoArray[1]];
                NSArray *timeArray=[timeString componentsSeparatedByString:@"~"];
                if (timeInfoArray.count < 2)
                {
                    TKLogInfo(@"TKOpenAuthorization.lic:授权文件没有开始和结束认为是测试授权");
                    return false;
                }else{
                    NSString *beginTime=timeArray[0];
                    NSString *endTime=timeArray[1];
                    NSDateFormatter *format=[[NSDateFormatter alloc] init];
                    format.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];

                    [format setDateFormat:@"yyyyMMdd"];
                    //授权开始时间
                    NSDate *beginFromdate=[format dateFromString:beginTime];
                    //授权结束时间
                    NSDate *endFromdate=[format dateFromString:endTime];
                    
                    NSTimeZone *beginZone = [NSTimeZone systemTimeZone];
                    NSInteger beginFrominterval = [beginZone secondsFromGMTForDate: beginFromdate];
                    beginFromdate= [beginFromdate  dateByAddingTimeInterval: beginFrominterval];
                    
                    
                    NSTimeZone *endZone = [NSTimeZone systemTimeZone];
                    NSInteger interval = [endZone secondsFromGMTForDate: endFromdate];
                    endFromdate= [endFromdate  dateByAddingTimeInterval: interval];
                    
                    NSCalendar *gregorian = [[NSCalendar alloc] initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
                    NSUInteger unitFlags = NSCalendarUnitMonth | NSCalendarUnitDay;
                    NSDateComponents *components = [gregorian components:unitFlags fromDate:beginFromdate toDate:endFromdate options:0];
                    //月份时间差
                    NSInteger months = [components month];
                    if (months>6) {
                        //授权大于半年都是正式授权
                        return true;
                    }else{
                        TKLogInfo(@"TKOpenAuthorization.lic:授权文件授权时间小于半年测试授权");
                        return false;
                    }
                }
            }
        }
    }
}
@end
