//
//  TKLiveFaceViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/2/18.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKLiveFaceViewController.h"
#import "TKLiveFaceView.h"
#import "TKOpenAccountService.h"
#import <MediaPlayer/MediaPlayer.h>


////走商汤活体宏定义
#define MODEL_ST_SILENT_LIVENESS

#ifdef MODEL_ST_SILENT_LIVENESS
//@import STDetectorCore;
@import STLivenessDetector;
////商汤活体类
//#import <STSilentLiveness/STSilentLiveness.h>

#else
//腾讯活体
#import <YtSDKKitFramework/YtSDKKitFramework.h>
    #import <YTCommon/AuthManager.h>
    #import <YTCommon/UIImage+YTCvMat.h>
    #import <YtSDKKitFramework/YtSDKLogger.h>
    #import <YtSDKKitFramework/YtSDKKitConfig.h>
    #import <YtSDKKitFramework/YtSDKCommonDefines.h>
#endif



#ifdef MODEL_ST_SILENT_LIVENESS
@interface TKLiveFaceViewController ()<AVCaptureVideoDataOutputSampleBufferDelegate,AVCaptureAudioDataOutputSampleBufferDelegate,TKLiveFaceViewDelegate,STLivenessDetectorDelegate> //,STSilentLivenessDetectorDelegate

#else
@interface TKLiveFaceViewController ()<AVCaptureVideoDataOutputSampleBufferDelegate,AVCaptureAudioDataOutputSampleBufferDelegate,TKLiveFaceViewDelegate>
#endif

@property (nonatomic, strong) AVCaptureSession *avSession;//,用于捕捉视频和音频,协调视频和音频的输入和输出流
@property (nonatomic, strong) AVCaptureDeviceInput *avDeviceInput;//设备输入流

@property (strong, nonatomic) dispatch_queue_t outPutQueue;//输出流线程队列
@property (strong, nonatomic) AVCaptureVideoDataOutput *videoOutput;//视频输出流

@property (strong, nonatomic) AVAssetWriter *assetWriter;//写流
@property (strong, nonatomic) AVAssetWriterInput *videoWriterInput;//视频输入流




@property (nonatomic, strong) AVCaptureVideoPreviewLayer *avPreviewLayer;//视频预览图层
@property (nonatomic, strong) UIView *avPreviewView;//视频预览视图
@property (nonatomic, assign) BOOL isRecording;//是否正在录制
@property (nonatomic, assign) CGFloat videoWidth;//视频宽度
@property (nonatomic, assign) CGFloat videoHeight;//视频高度
@property (nonatomic, assign) BOOL isAlertViewShow;//是否已有弹窗层
@property (nonatomic, readwrite, strong) NSArray *randomActionTypeArray; // 随机动作数组


#ifdef MODEL_ST_SILENT_LIVENESS
//@property (nonatomic, strong) STSilentLivenessDetector *stDetector;//商汤静默活体探测器类
@property (nonatomic, strong) STLivenessDetector *stDetector;//商汤动作活体探测器类
//@property (nonatomic, strong) NSArray *motionArray;//商汤动作数组
@property (nonatomic, strong) NSDictionary *motionDic;//动作字典
#else

#endif




@property (nonatomic, assign) CFAbsoluteTime lastUpdateTime;

@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
//@property (nonatomic, assign) NSInteger moreMaxFailNum;//失败次数超过3次就退出给h5处理页面

@property (nonatomic, strong) TKLiveFaceView *tkLiveFaceView;//活体界面
@property (nonatomic, strong) AVPlayer *mp3Player;//MP3文件播放
@property (nonatomic, strong) AVPlayer *startMp3Player;//开始MP3文件播放，单独建投

@property (nonatomic, strong) NSString *recordStartDateString;//格式化的视频开始录制时间字符串
@property (nonatomic, strong) NSString *videoLength;//视频录制时长（秒）
@property (nonatomic, readwrite, assign) int faceCount; // 检测到的人脸数量

@property (nonatomic, readwrite, assign) BOOL isStartDectet; // 开始检测
@property (nonatomic, assign) BOOL isPlayingMp3;//是否正在播放map3文件
@property (nonatomic, strong) NSString *currentActionMp3;//当前动作播放的mp3名称
@property (nonatomic, strong) NSString *currentPlayingMp3;//当前正在播放的mp3名称
@property (nonatomic, strong) TKOpenAccountService *mService;
@end

@implementation TKLiveFaceViewController
int _faceCount;

-(instancetype)initWithParam:(NSMutableDictionary *)param{
    self=[super init];
    if (self) {
        self.requestParam=param;
        self.mService=[[TKOpenAccountService alloc] init];
    }
    return self;
}



- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}


- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor grayColor];
    
    [self.view addSubview:self.avPreviewView];
    [self.view addSubview:self.tkLiveFaceView];
//    self.moreMaxFailNum=0;
    
    
    // 埋点-活体_开始
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventStart progress:TKPrivateEventProgressNone result:TKPrivateEventResultNone eventDic:eventDic];
}

-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    if (!self.avSession.isRunning) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            
            [self.avSession startRunning];
        });
        
        // 等待摄像头启动
        sleep(1);
    }
}

-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [TKCommonUtil autoHeadsetState];
    //App进入后台监听
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(enterBackground:) name:UIApplicationWillResignActiveNotification object:nil];
    
    [self handleStartAudioPlay:@"tk_live_startWords" sourceDirectory:@"Resources/TKOpenPlugin60006"];

}


-(void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    if (!self.avSession.isRunning) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            
            [self.avSession stopRunning];
        });
    }
}


#pragma mark 事件方法

/**
 *  <AUTHOR> 2018-06-09 15:13:50
 *  标记App进入到后台
 *  @param notif
 */
-(void)enterBackground:(NSNotification *)notif{
   

    dispatch_async(dispatch_get_main_queue(), ^{
        [self exitProcess];
    });
    
    NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
    callJsParam[@"funcNo"]=@"60055";
    callJsParam[@"error_no"]=@"-4";
    callJsParam[@"sub_error_no"]=@"-400";
    callJsParam[@"error_info"]=@"视频过程中请勿切换App或锁屏" ;
    callJsParam[@"tkuuid"]=self.requestParam[@"tkuuid"];
    
    if (self.delegate&&[self.delegate respondsToSelector:@selector(tkLiveDetectDidComplete:)]) {
        [self.delegate tkLiveDetectDidComplete:callJsParam];
    }else{
       [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
    }
}

/**
 <AUTHOR> 2019年04月08日19:31:25
 @重新开始单向视频流程
 */
-(void)reStartOneVideo{
    
    
#ifdef MODEL_ST_SILENT_LIVENESS
    [self.stDetector cancel];
#else
    [[YtSDKKitFramework sharedInstance] reset];
    [[YtSDKKitFramework sharedInstance] deInit];
#endif
    
    
    TKLogInfo(@"TKSmartOneVideo:重新开始单向视频流程");
    
   
    [self.view addSubview:self.tkLiveFaceView];
   
    if (!self.avSession.isRunning) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            
            [self.avSession startRunning];
        });
        
        // 等待摄像头启动
        sleep(1);
    }
    
#ifdef MODEL_ST_SILENT_LIVENESS
//    [self.stDetector startDetection];
//    [self.stDetector reStartDetection];
    //随机支持的动作
    [self.stDetector start];
#else
    [self tencentSilent];
    [[YtSDKKitFramework sharedInstance] fireEvent:YTTriggerBeginLiveness withContent:nil];
#endif
    
    // 埋点-活体-对齐开始
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventLiveDetectAlignment progress:TKPrivateEventProgressStart result:TKPrivateEventResultNone eventDic:eventDic];
}

#ifdef MODEL_ST_SILENT_LIVENESS
- (NSDictionary *)motionDic {
    if (_motionDic == nil) {
        _motionDic = @{
            @(0) : @(kSTLivenessMotionTypeBlink),
            @(1) : @(kSTLivenessMotionTypeMouth),
            @(2) : @(kSTLivenessMotionTypeNod),
            @(3) : @(kSTLivenessMotionTypeYaw)
            
        };  // 0-眨眼，1-张嘴，2-点头，3-摇头
    }
    return _motionDic;
}

- (NSArray *)randomActionTypeArray {
    if (_randomActionTypeArray == nil) {

        // 0-眨眼，1-张嘴，2-点头，3-摇头
        NSArray *actionGroupArray;
        if ([[NSString stringWithFormat:@"%@",self.requestParam[@"actionGroup"]] rangeOfString:@","].location != NSNotFound) {
                 
            actionGroupArray = [self.requestParam[@"actionGroup"] componentsSeparatedByString:@","];
        }else{
            actionGroupArray = @[[NSString stringWithFormat:@"%@",self.requestParam[@"actionGroup"]]];
        }
        
        NSMutableSet *randomSet = [[NSMutableSet alloc] init];
        if (self.motionDic && self.motionDic.allKeys.count > 0) {
            for (int i = 0; i < actionGroupArray.count; i++) {
                int actionGroup = [actionGroupArray[i] intValue];
                NSNumber *motion = _motionDic[@(actionGroup)];
                if (motion) [randomSet addObject:motion]; // 没有传actionGroup，得到的actionGroupArray为"（null）"。经intValue转换后为0.
            }
        }
        _randomActionTypeArray = [randomSet allObjects] ? [randomSet allObjects] : @[];
    }
    
    return _randomActionTypeArray;;
}
#endif


/**
 <AUTHOR> 2019年04月08日19:27:12
 @开始录制单向视频
 */
-(void)startRecordingAction{
    
    
    // ----------设置你想要的格式,hh与HH的区别:分别表示12小时制,24小时制
    //现在时间,你可以输出来看下是什么格式
    //----------将nsdate按formatter格式转成nsstring
    self.recordStartDateString =[TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];

    
    // 删除旧录像文件，因为这个录制有同名文件就不会进行
    NSFileManager *fm = [[NSFileManager alloc] init];
    
    if ([fm fileExistsAtPath:[self videoFileURLString]])
    {
        
        NSError *error;
        if ([fm removeItemAtPath:[self videoFileURLString] error:&error]) {
            TKLogInfo(@"TKSmartOneVideo:录制视频前先删除旧视频文件");
        }
    }
    self.isRecording=YES;
}

-(void)stopRecordingAction{
    if (!self.avSession.isRunning) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            
            [self.avSession stopRunning];
        });
    }
    self.isRecording=NO;
    @try{
        if(self.assetWriter.status == AVAssetWriterStatusWriting)
        {
            [self.videoWriterInput markAsFinished];
//            [self.audioWriterInput markAsFinished];
            dispatch_semaphore_t wait = dispatch_semaphore_create(0l);
            [self.assetWriter finishWritingWithCompletionHandler:^{
                 
                TKLogInfo(@"TKSmartOneVideo:停止录制视频文件");
                self.assetWriter = nil;
                
                dispatch_semaphore_signal(wait);
            }];
            dispatch_semaphore_wait(wait, DISPATCH_TIME_FOREVER);
        }
        else
        {
            TKLogInfo(@"TKSmartOneVideo:当前写入状态:%ld", (long)self.assetWriter.status);
        }
        
    }
    @catch(NSException *exception) {
        TKLogInfo(@"TKSmartOneVideo:exception:%@", exception);
    }
    @finally {
        
    }
    
}

-(NSString *)videoFileURLString {
    return [NSHomeDirectory() stringByAppendingFormat:@"/Documents/%@.mp4",TK_ONE_WAY_TEMP_MOVE_NAME];
}


/**
 <AUTHOR> 2019年04月15日15:59:58
 @退出界面流程
 */
-(void)exitProcess{
    TKLogInfo(@"TKSmartOneVideo:退出界面流程");
    
#ifdef MODEL_ST_SILENT_LIVENESS
    [self.stDetector cancel];
#else
    
    [[YtSDKKitFramework sharedInstance] deInit];
#endif
    [self.mp3Player pause];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:self.mp3Player.currentItem];
    [self.startMp3Player pause];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:self.startMp3Player.currentItem];
    self.isPlayingMp3=NO;
    [self dismissViewControllerAnimated:YES completion:nil];
    
}

/**
 <AUTHOR> 2019年05月24日13:15:37
 @弹出提示窗
 */
-(void)liveFaceEnd:(NSData *)imgData bestImageSign:(NSString *)bestImageSign actionImgArr:(NSArray *)actionImgArr actionSignatureArr:(NSArray *)actionSignatureArr {
    // 埋点-活体-结果-成功
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = @"0";
    eventDic[@"event_err"] = @"";
    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventNone progress:TKPrivateEventProgressEnd result:TKPrivateEventResultSuccess eventDic:eventDic];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self exitProcess];
        
        TKLogInfo(@"活体识别成功，%s",__func__);
        NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:imgData];
        NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
        
        NSMutableDictionary *callJsParam = [NSMutableDictionary dictionary];
        callJsParam[@"funcNo"]=@"60055";
        callJsParam[@"error_no"]=@"0";
        callJsParam[@"sub_error_no"]=@"0";
        callJsParam[@"error_info"]=@"活体检测成功";
        if ([self.requestParam[@"isNeedRecord"] intValue]==1) {
            //视频信息
            callJsParam[@"start_time"]=self.recordStartDateString;
            callJsParam[@"video_length"]=self.videoLength;
            NSString *tmpVideoBase64=[TKBase64Helper stringWithEncodeBase64Data:[NSData dataWithContentsOfFile:[self videoFileURLString]]];
            callJsParam[@"videoBase64"]=[NSString stringWithFormat:@"data:video/mp4;base64,%@", tmpVideoBase64];
            
            // ----------设置你想要的格式,hh与HH的区别:分别表示12小时制,24小时制
            //现在时间,你可以输出来看下是什么格式
            //----------将nsdate按formatter格式转成nsstring
            callJsParam[@"end_time"]= [TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
        }
        
        // 上传动作图片数组做质检留痕
        NSMutableString *actionBase64 = [NSMutableString string];
        NSString *tmpActionImageBase64 = nil;
        for (int i = 0; i < actionImgArr.count; i++) {
            NSData *imgData = actionImgArr[i];
            tmpActionImageBase64 = [TKBase64Helper stringWithEncodeBase64Data:imgData];
            if (i == 0) {
                [actionBase64 appendString:[NSString stringWithFormat:@"%@", tmpActionImageBase64]];
            } else {
                [actionBase64 appendString:[NSString stringWithFormat:@"|%@", tmpActionImageBase64]];
            }
        }
        callJsParam[@"actionBase64"]=actionBase64;
        if (actionSignatureArr.count > 0) {
            callJsParam[@"actionBase64Signs"]=actionSignatureArr;
        }
        
        callJsParam[@"base64"]=base64;
        if ([TKStringHelper isNotEmpty:bestImageSign]){
            callJsParam[@"base64Sign"]=bestImageSign;
        }
        
        callJsParam[@"tkuuid"]=self.requestParam[@"tkuuid"];
        
        if (self.delegate&&[self.delegate respondsToSelector:@selector(tkLiveDetectDidComplete:)]) {
            [self.delegate tkLiveDetectDidComplete:callJsParam];
        }else{
           [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
        }
    });
    
    
    
}

/**
 <AUTHOR> 2019年05月24日13:15:37
 @弹出提示窗
 */
-(void)showAlertView:(NSString *)title message:(NSString *)message tag:(NSInteger)tag{
    dispatch_async(dispatch_get_main_queue(), ^{
        TKLogInfo(@"TKSmartOneVideo:isShow:%d",self.isTKShow);
        if (self.isTKShow) {
            if (!self.isAlertViewShow) {

                if (!self.avSession.isRunning) {
                    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
                        
                        [self.avSession stopRunning];
                    });
                }
#ifdef MODEL_ST_SILENT_LIVENESS
                [self.stDetector cancel];
#else
                
                [[YtSDKKitFramework sharedInstance] fireEvent:YTTriggerCancelLiveness withContent:nil];
#endif

                
                self.isAlertViewShow=YES;
                
                UIAlertController *alertController = [UIAlertController alertControllerWithTitle:title message:message preferredStyle:UIAlertControllerStyleAlert];
                  // 2.创建并添加按钮
                  UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"重试" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                      self.isAlertViewShow=NO;
                      [self reStartOneVideo];
                  }];
                  // 2.创建并添加按钮
                  UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
                      self.isAlertViewShow=NO;
                      [self exitProcess];
                      
                      // 目前直接退出页面，返回错误值给h5，这里不会调用
                      
                      NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
                      callJsParam[@"funcNo"]=@"60055";
                      callJsParam[@"error_no"]=@"-3";
                      callJsParam[@"sub_error_no"]=@"-305";
                      callJsParam[@"error_info"]=@"活体检测失败点击取消";
                      callJsParam[@"tkuuid"]=self.requestParam[@"tkuuid"];
                      if (self.delegate&&[self.delegate respondsToSelector:@selector(tkLiveDetectDidComplete:)]) {
                          [self.delegate tkLiveDetectDidComplete:callJsParam];
                      }else{
                         [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
                      }
                  }];
                  [alertController addAction:okAction];
                  [alertController addAction:cancelAction];
                  [self presentViewController:alertController animated:YES completion:nil];
            }
            
        }
    });
    
    
}

/**
 <AUTHOR> 2019年05月26日12:41:59
 @活体检测、人脸识别、语音识别超过3次就退出给h5处理页面
 */
-(void)moreMaxFailAction:(NSString *)errorMsg subErrorNo:(NSString *)subErrorNo {
    // 埋点-活体-结果-失败
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = @"-3";
    eventDic[@"sub_error_no"] = subErrorNo;
    eventDic[@"event_err"] = errorMsg;
    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventNone progress:TKPrivateEventProgressEnd result:TKPrivateEventResultFail eventDic:eventDic];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self exitProcess];
    });
    
    NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
    callJsParam[@"funcNo"]=@"60055";
    callJsParam[@"error_no"]=@"-3";
    callJsParam[@"sub_error_no"] = subErrorNo;
    callJsParam[@"error_info"]=errorMsg;
    callJsParam[@"tkuuid"]=self.requestParam[@"tkuuid"];
    
    if (self.delegate&&[self.delegate respondsToSelector:@selector(tkLiveDetectDidComplete:)]) {
        [self.delegate tkLiveDetectDidComplete:callJsParam];
    }else{
       [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
    }
}

/**
 <AUTHOR> 2021年02月18日18:11:37
 @活体检测授权错误
 */
-(void)moreAuthFailAction{
    // 埋点-活体-结果-异常
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = @"-2";
    eventDic[@"event_err"] = @"活体授权异常";
    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventNone progress:TKPrivateEventProgressEnd result:TKPrivateEventResultError eventDic:eventDic];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self exitProcess];
    });
    
    NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
    callJsParam[@"funcNo"]=@"60055";
    callJsParam[@"error_no"]=@"-2";
    callJsParam[@"sub_error_no"] = @"-200";
    callJsParam[@"error_info"]=@"活体授权异常";
    callJsParam[@"tkuuid"]=self.requestParam[@"tkuuid"];
    if (self.delegate&&[self.delegate respondsToSelector:@selector(tkLiveDetectDidComplete:)]) {
        [self.delegate tkLiveDetectDidComplete:callJsParam];
    }else{
       [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
    }
}

#pragma mark TKLiveFaceViewDelegate
/**
 <AUTHOR> 2019年04月13日14:18:27
 @单向视频结果页返回
 */
-(void)goBack{
    //埋点
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = @"-1";
    eventDic[@"event_err"] = @"用户主动返回";
    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventNone progress:TKPrivateEventProgressNone result:TKPrivateEventResultCancel eventDic:eventDic];
    
    [self exitProcess];
    
    NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
    callJsParam[@"funcNo"]=@"60055";
    callJsParam[@"error_no"]=@"-1";
    callJsParam[@"sub_error_no"] = @"-100";
    callJsParam[@"error_info"]=@"用户主动返回";
    callJsParam[@"tkuuid"]=self.requestParam[@"tkuuid"];
    if (self.delegate&&[self.delegate respondsToSelector:@selector(tkLiveDetectDidComplete:)]) {
        [self.delegate tkLiveDetectDidComplete:callJsParam];
    }else{
       [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
    }
}




#ifdef MODEL_ST_SILENT_LIVENESS


#pragma mark STLivenessDetectorDelegate
/// 活体检测失败
/// @param error 失败原因
/// @param resultImage 检测过程中的最佳图片
/// @param resultCroppedImage 检测过程中最佳图片人脸裁剪图
/// @param motionImages 检测过程动作图
/// @param motionCroppedImages 检测过程动作图人脸裁剪图
- (void)livenessDidFailWithError: (NSError *)error
                  andResultImage: (nullable STLivenessResultImage *)resultImage
            andResultCropedImage: (nullable STLivenessResultImage *)resultCroppedImage
                 andMotionImages: (nullable NSArray<STLivenessResultImage *> *)motionImages
          andMotionCroppedImages: (nullable NSArray<STLivenessResultImage *> *)motionCroppedImages{
    
    // 埋点-活体-动作-结束
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventLiveDetectAction progress:TKPrivateEventProgressEnd result:TKPrivateEventResultNone eventDic:eventDic];
    
    if ([self.requestParam[@"isNeedRecord"] intValue]==1) {
        //停止录制视频
        [self stopRecordingAction];
    }
    
    // 2-未通过活体检测 30-人脸丢失 408-检测超时
    if (error.code == 2) {
        [self moreMaxFailAction:@"活体识别不通过，请重试。" subErrorNo:@"-303"];    // 2- Hack 攻击
    } else if (error.code == 30) {
        [self moreMaxFailAction:@"活体识别不通过，请重试。" subErrorNo:@"-300"];    // 30- 人脸丢失
    } else if (error.code == 407) {
        [self moreMaxFailAction:@"由于长时间活体识别未通过，本次人脸认证失败，请重新检测。" subErrorNo:@"-301"];    // 407- 准备阶段超时
    } else if (error.code == 407) {
        [self moreMaxFailAction:@"由于长时间活体识别未通过，本次人脸认证失败，请重新检测。" subErrorNo:@"-302"];    // 408- 动作阶段超时
    }  else if (error.code == 409) {
        [self moreMaxFailAction:@"活体识别不通过，请重试。" subErrorNo:@"-304"];    // 409- 未通过活体检测
    } else {
        [self moreMaxFailAction:@"活体识别不通过，请重试。" subErrorNo:@"-304"];
    }
}

/// 活体检测成功
/// @param resultImage 检测过程中最佳结果图
/// @param resultCroppedImage 最佳结果人脸裁剪图
/// @param motionImages 检测过程中最佳动作图
/// @param motionCroppedImages 每个最佳动作图人脸裁剪图
- (void)livenessDidSuccessWithResultImage: (nonnull STLivenessResultImage *)resultImage
                    andResultCroppedImage: (nonnull STLivenessResultImage *)resultCroppedImage
                          andMotionImages: (nullable NSArray<STLivenessResultImage *> *)motionImages
                   andMotionCroppedImages: (nullable NSArray<STLivenessResultImage *> *)motionCroppedImages{
    // 埋点-活体-动作-结束
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
    [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventLiveDetectAction progress:TKPrivateEventProgressEnd result:TKPrivateEventResultNone eventDic:eventDic];
    
    NSData *bestImageData = nil;
    // 商汤3.0.4增加验证功能，要把图片rawData直接上传
    if (resultImage.rawData) {
        bestImageData = resultImage.rawData;
    } else {
        bestImageData = UIImageJPEGRepresentation(resultImage.image, 1);
    }
    
    // 商汤3.0.4增加验证功能，要把图片rawData直接上传
    // 商汤3.0.4增加验证功能，图片对应的签名文件
    NSMutableArray *actionImgArr = [[NSMutableArray alloc] init];
    NSMutableArray *actionSignatureArr = [[NSMutableArray alloc] init];
    for (STLivenessResultImage *stImage in motionImages) {
        // 动作图片
        NSData *actionImgData = nil;
        if (stImage.rawData) {
            actionImgData = stImage.rawData;
        } else {
            actionImgData = UIImageJPEGRepresentation(stImage.image, 1);
        }
        [actionImgArr addObject:actionImgData];
        
        // 动作图片签名
        if ([TKStringHelper isNotEmpty:stImage.signature]) [actionSignatureArr addObject:stImage.signature];
    }



    dispatch_async(dispatch_get_main_queue(), ^{
         if ([self.requestParam[@"isNeedRecord"] intValue]==1) {
             //停止录制视频
             [self stopRecordingAction];
         }
         TKLogInfo(@"TKSmartOneVideo:活体检测成功");
         [self.stDetector cancel];

        // actionImgArr从3.0.4版本后，从image改成data传入。方便做验签
        [self liveFaceEnd:bestImageData bestImageSign:resultImage.signature actionImgArr:actionImgArr actionSignatureArr:actionSignatureArr];
    });
}


/// 检测过程中的状态返回
- (void)livenessUpdateStatus:(STLivenessDetectorFaceStatus)status {
    
    CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent() ;
    if ((currentTime - self.lastUpdateTime) >=1) {
        NSString *tipString=[self descriptionOfStatus:status];
        [self.tkLiveFaceView liveWarning:tipString];
        self.lastUpdateTime = currentTime;
        if ([TKStringHelper isNotEmpty:tipString]) {
            if (!self.isPlayingMp3) {
                // 播放语音提示
                [self handleAudioPlay:@"common_notice_error"];
            }else{
                //有音频文件且是动作提示时候，换成播放错误提示
                if ([self.currentActionMp3 isEqualToString:self.currentPlayingMp3]) {
                    [self handleAudioPlay:@"common_notice_error"];
                }
            }

        }
        else{
            //没有错误提示时候播放动作
            if (!self.isPlayingMp3) {
                //没有音频文件正在播放情况下直接提示动作
                [self handleAudioPlay:self.currentActionMp3];
            }else{
                //有音频文件且不是动作提示时候，换成播放动作提示
                if (![self.currentActionMp3 isEqualToString:self.currentPlayingMp3]) {
                    [self handleAudioPlay:self.currentActionMp3];
                }
            }
        }

    }
}


/**
*  每个检测模块开始的回调方法
*
*  @param detectionType       当前开始检测的模块类型
*  @param detectionIndex      当前开始检测的模块在动作序列中的位置, 从0开始.
*/
- (void)livenessDidStartDetectMotionType:(STLivenessMotionType)motionType andIndex:(NSInteger)index {
    
    if (index == 0) {
        // 埋点-活体-对齐结束
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
        eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
        [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventLiveDetectAlignment progress:TKPrivateEventProgressEnd result:TKPrivateEventResultNone eventDic:eventDic];
        
        // 埋点-活体-动作-开始
        eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
        eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
        [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventLiveDetectAction progress:TKPrivateEventProgressStart result:TKPrivateEventResultNone eventDic:eventDic];
    }
    
    NSString *msg = @"";
    NSString *musicFileName = @"";
    TKLiveAction liveAction = TKLiveActionUnknown;
    switch (motionType) {
        case kSTLivenessMotionTypeBlink:
            msg = @"请眨眼";
            musicFileName = @"common_notice_blink";
            liveAction = TKLiveActionBlink;
            break;
        case kSTLivenessMotionTypeMouth:
            msg = @"请张嘴,随后合拢";
            musicFileName = @"common_notice_mouth";
            liveAction = TKLiveActionMouth;
            break;
        case kSTLivenessMotionTypeNod:
            msg = @"请上下点头";
            musicFileName = @"common_notice_nod";
            liveAction = TKLiveActionNod;
            break;

        case kSTLivenessMotionTypeYaw:
            msg = @"请缓慢摇头";
            musicFileName = @"common_notice_yaw";
            liveAction = TKLiveActionShake;
            break;
    }


    [self.tkLiveFaceView updateTipLabel:msg];
    [self.tkLiveFaceView updateLiveActionGif:liveAction];
    self.currentActionMp3=musicFileName;
    // 播放语音提示
    [self handleAudioPlay:musicFileName];
    
}


- (NSString *)descriptionOfStatus: (STLivenessDetectorFaceStatus)status {
//    NSLog(@"STDetectorFaceStatus:%lu",(unsigned long)status);
    switch (status) {
        case kSTLivenessDetectorFaceStatusNoFaceFound:
//            return @"请您保持全脸在人像框内";
            return @"未检测到您的面部";
        case kSTLivenessDetectorFaceStatusFaceOcclusionBrow:
            return @"请不要遮挡眉毛";
        case kSTLivenessDetectorFaceStatusFaceOcclusionEye:
            return @"请不要遮挡眼睛";
        case kSTLivenessDetectorFaceStatusFaceOcclusionNose:
            return @"请不要遮挡鼻子";
        case kSTLivenessDetectorFaceStatusFaceOcclusionMouth:
            return @"请不要遮挡嘴巴";

        case kSTLivenessDetectorFaceStatusFaceTooFar:
//            return @"请靠近一点";
            return @"请靠近屏幕";
        case kSTLivenessDetectorFaceStatusFaceTooClose:
//            return @"请离远一点";
            return @"请远离屏幕";
        case kSTLivenessDetectorFaceStatusFaceOutBound:
//            return @"请保持全脸正对人像框";
            return @"超出人脸框";
            
        case kSTLivenessDetectorFaceStatusFaceOcclusionCheek:
            return @"请不要遮挡脸颊";
        case kSTLivenessDetectorFaceStatusTargetLost:
            return @"人脸丢失";
        case kSTLivenessDetectorFaceStatusAngleFail:
            return @"请正视屏幕，请勿倾斜";
        case kSTLivenessDetectorFaceStatusTooDark:
            return @"环境光线太暗，请调整";
        case kSTLivenessDetectorFaceStatusOverExposure:
            return @"环境光线太亮，请调整";
        case kSTLivenessDetectorFaceStatusBlur:
            return @"人脸检测模糊，请调整";
        case kSTLivenessDetectorFaceStatusEyeClosed:
            return @"请勿闭眼";
        case kSTLivenessDetectorFaceStatusTooManyTargetFound:
            return @"检测到多张人脸";
            
        case kSTLivenessDetectorFaceStatusNormal:
            return @"";
        case kSTLivenessDetectorFaceStatusMotionBlink: /// 眨眼
        case kSTLivenessDetectorFaceStatusMotionMouth: /// 张嘴
        case kSTLivenessFaceStatusEyes: /// 准备状态  眼晴睁开
        case kSTLivenessReadyFaceStatusMouth: /// 准备状态 嘴闭合
            return @"";
        case kSTLivenessReadyFaceStatusHeadPose: /// 准备状态 头部正对并摆正
            return @"请正视屏幕，请勿倾斜";

        default: // 未知状态
            return @"请保持全脸正对人像框";
    }
}

- (void)livenessUpdateFaceRect:(CGRect)rect {

//    UIView *rectView = [self.view viewWithTag:513];
//    if (rectView == nil) {
//        rectView = [UIView new];
//        rectView.layer.borderColor = UIColor.redColor.CGColor;
//        rectView.layer.borderWidth = 2;
//        [self.view addSubview:rectView];
//        rectView.tag = 513;
//    }
//
//    rectView.frame = rect;
}

- (void)livenessUpdateFaceCount: (int)faceCount {
    _faceCount = faceCount;
}


#else
#pragma mark TencentASRActionLiveDelegate
//腾讯活体检测中的回调

//腾讯人脸检测对准；腾讯是人脸检测结果有变动才返回新的
//在json配置项中增加了same_tips_filter开关，设置为false，每帧会有返回结果

-(void)tencentFaceStateless:(NSString *)result{
    dispatch_async(dispatch_get_main_queue(), ^{
        CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent() ;
        if ((currentTime - self.lastUpdateTime) >=1) {
            TKLogInfo(@"TKSmartOneVideo:活体过程提示:%@",result);
            
            if ([result isEqualToString:YtSDKTipValueHoldPosition]||[result isEqualToString:YtSDKTipValueWait]) {
                
                
                    [self.tkLiveFaceView liveContinue];
                
            } else if ([result isEqualToString:YtSDKTipValueAdviseNoFace]) {
            
                [self.tkLiveFaceView liveWarning:@"请将脸放进屏幕"];
                
            } else if ([result isEqualToString:YtSDKTipValueAdviseCloser]) {
                
                [self.tkLiveFaceView liveWarning:@"请靠近面部"];
            
                
            } else if ([result isEqualToString:YtSDKTipValueAdviseFarer]) {
                
                [self.tkLiveFaceView liveWarning:@"请远离面部"];
            
            } else if([result isEqualToString:YtSDKTipValueAdviseOpenEye]){
                
                [self.tkLiveFaceView liveWarning:@"请睁开眼睛"];
            
            }else if([result isEqualToString:YtSDKTipValueShelterLeftFace]||[result isEqualToString:YtSDKTipValueShelterRightFace]){
                
                [self.tkLiveFaceView liveWarning:@"请勿遮挡面部"];
                
            }else if([result isEqualToString:YtSDKTipValueShelterChin]){
               
                [self.tkLiveFaceView liveWarning:@"请勿遮挡下巴"];
                
            }else if([result isEqualToString:YtSDKTipValueShelterMouth]){
                
                [self.tkLiveFaceView liveWarning:@"请勿遮挡嘴巴"];
                
            }else if([result isEqualToString:YtSDKTipValueShelterNose]){
                
                [self.tkLiveFaceView liveWarning:@"请勿遮挡鼻子"];
                
            }else if([result isEqualToString:YtSDKTipValueShelterLeftEye]||[result isEqualToString:YtSDKTipValueShelterRightEye]){
                
                [self.tkLiveFaceView liveWarning:@"请勿遮挡眼睛"];
                
            }else{
                
                [self.tkLiveFaceView liveWarning:@"请对准屏幕中央"];
            }
            
            self.lastUpdateTime = currentTime;
        }
        
        
    });
}

//腾讯SDK的活体
-(void)tencentSilent{
    
    //    //开启腾讯活体日志
    //    [[NSUserDefaults standardUserDefaults] setInteger:YT_SDK_DEBUG forKey:YtSDKLogLevelUserDefaultsDomain];
    
    //检查授权文件
    NSString* licensePath = [[NSBundle mainBundle] pathForResource:@"YTFaceSDK.licence" ofType:@""];
    
    int authRet = [AuthManager setLicencePath:licensePath];
    
    if (authRet != 0) {
        [self showAlertView:@"提示" message:@"活体检测授权失败" tag:7001];
        return;
    }
    
    //设置视频展示区域，和人脸对准框区域
    [YtSDKKitFramework sharedInstance].previewRect=self.avPreviewLayer.frame;
    [YtSDKKitFramework sharedInstance].detectRect=self.tkLiveFaceView.boxRect;
    
    NSString *bundlePath =[[NSBundle mainBundle] pathForResource:@"YtSDKKitConfig" ofType:@"bundle"];
    NSString *sdkSettingPath = [[NSBundle bundleWithPath:bundlePath] pathForResource:@"YtSDKSettings.json" ofType:@""];
    NSString * sdkConfigJsonRaw = [NSString stringWithContentsOfFile:sdkSettingPath encoding:NSUTF8StringEncoding error:nil];
    
    NSString *uiConfigPath = [[NSBundle bundleWithPath:bundlePath]  pathForResource:@"YtSDKUIConfig.json" ofType:@""];
    NSString * uiConfigJsonRaw = [NSString stringWithContentsOfFile:uiConfigPath encoding:NSUTF8StringEncoding error:nil];
    [[YtSDKKitConfig sharedInstance] loadSDKConfigWith:sdkConfigJsonRaw withUIConfig:uiConfigJsonRaw];
    
    YtSDKKitMode SDKKitMode = YT_SDK_SILENT_MODE; // 静默活体
    SDKKitMode = YT_SDK_ACTION_MODE; // 动作活体
    NSDictionary *sdkConfig = [[YtSDKKitConfig sharedInstance] getSDKConfigBy:SDKKitMode];
    NSArray *stateNameArray = [[YtSDKKitConfig sharedInstance] getStateNameArrayBy:SDKKitMode];
    NSString *modelPath=[[NSBundle mainBundle] bundlePath];
    [[YtSDKKitFramework sharedInstance] setModelRootPath:modelPath];
    
    WeakSelf();
    [[YtSDKKitFramework sharedInstance] initWithSDKSetting:sdkConfig withPipelineWorkMode:SDKKitMode withPipelineStateNameArray:stateNameArray withCamera:nil withEventHandleBlock:^(YtFrameworkEventType eventType, NSDictionary *eventDict) {
        dispatch_async(dispatch_get_main_queue(), ^{
            TKLogInfo(@"TKSmartOneVideo:腾讯eventDict:%@",eventDict);
            
            if (eventType == YT_SDK_UI_FEVENT_TYPE)
            {
                
                if ([eventDict valueForKey:YtSDKEventTipsType]) {
                    //人脸检测相关提示事件
                    [self tencentFaceStateless:eventDict[YtSDKEventTipsType]];
                }
                
                if ([eventDict valueForKey:YtSDKEventPipelineSucceedFinished]) {
                    //活体成功
                    TKLogInfo(@"TKSmartOneVideo:活体检测成功");
                    if ([self.requestParam[@"isNeedRecord"] intValue]==1) {
                        //停止录制视频
                        [self stopRecordingAction];
                    }
                   
                    UIImage *img=eventDict[YtSDKEventUserInfoType];
                    
                    [self liveFaceEnd:img];
                    return ;
                }
                
                if([eventDict valueForKey:YtSDKEventPipelineFailedErrorCode]) {
                    NSString *errTipString;
                    NSInteger errorCode=[eventDict[YtSDKEventPipelineFailedErrorCode] integerValue];
                    if (errorCode==YT_SDK_AUTH_FAILED_CODE) {
                        errTipString=@"授权失败";
                    }else if (errorCode==YT_SDK_RECOGNIZE_FAILED_CODE) {
                        errTipString=@"识别失败";
                    }else if (errorCode==YT_SDK_PARAMETER_ERROR_CODE) {
                        errTipString=@"参数异常";
                    }else if (errorCode==YT_SDK_AUTH_ERROR_CODE) {
                        errTipString=@"鉴权异常";
                    }else if (errorCode== YT_SDK_NETWORK_ERROR_CODE) {
                        errTipString=@"网络异常";
                    }else if (errorCode== YT_SDK_VERIFY_MODEL_INIT_FAIL) {
                        errTipString=@"初始化异常";
                    }else if (errorCode== YT_SDK_VERIFY_SERVER_FAIL) {
                        errTipString=@"服务解析异常";
                    }else if (errorCode== YT_SDK_VERIFY_SCORE_TOO_LOW) {
                        errTipString=@"识别分数过低";
                    }else if (errorCode== YT_SDK_VERIFY_TIMEOUT) {
                        errTipString=@"识别超时";
                    }else{
                        errTipString=@"未知异常";
                    }
                    
                    if ([self.requestParam[@"isNeedRecord"] intValue]==1) {
                        //停止录制视频
                        [self stopRecordingAction];
                    }
                    TKLogInfo(@"活体检测失败，失败原因:%@",errTipString);
                    
//                    self.moreMaxFailNum++;
//                    if (self.moreMaxFailNum>=3) {
                        [self moreAuthFailAction];
                        return ;
//                    }
//
//                    [self showAlertView:@"提示" message:@"人脸识别失败，请重试" tag:7001];
//                    return;
                }
                if ([eventDict valueForKey:YtSDKEventPipelineFailedFinished]) {
                    //活体失败
                    NSString *errTipString=eventDict[YtSDKEventPipelineFailedFinished];
                    TKLogInfo(@"活体检测失败，失败原因:%@",errTipString);
                    
//                    self.moreMaxFailNum++;
//                    if (self.moreMaxFailNum>=3) {
                        [self moreMaxFailAction];
                        return ;
//                    }
//
//                    [self showAlertView:@"提示" message:@"人脸识别失败，请重试" tag:7001];
//                    return;
                }
            }
            
        });
        
    } withNetworkRequestBlock:^(NSString *url, NSString *request, NSDictionary *headers, OnYtNetworkResponseBlock response) {
        StrongSelf();
        NSMutableDictionary *newHeaders = [[NSMutableDictionary alloc] init];
        if (headers) {
            newHeaders = [NSMutableDictionary dictionaryWithDictionary:headers];
        }
        newHeaders[@"Authorization"] = @"2+XDyxJgBlQrh1nchBq4I8d4kLthPTEwMTUxODM0Jms9QUtJRHNhN1VoanJPelBncnJncm40Qjl5YjNkMkdJOG5MZ3JxJmU9MTYwNDI5Nzk5OSZ0PTE1OTY1MjE5OTkmcj0xMTAzMzMyMDUmdT0zODA1NDk0OTQ=";
        [strongSelf handleNetworkRequest:url withHeaders:newHeaders withRequest:request withResponse:response];

    }];
}

- (void)handleNetworkRequest:(NSString *)url withHeaders:(NSDictionary *)headers withRequest:(NSString *)request withResponse:(OnYtNetworkResponseBlock)responseBlock
{
    NSURL *URL = [NSURL URLWithString:url];
    NSData *objectData = [request dataUsingEncoding:NSUTF8StringEncoding];
    NSURLSession *session = [NSURLSession sharedSession];
    NSMutableURLRequest *requestObject = [NSMutableURLRequest requestWithURL:URL];
    requestObject.HTTPMethod = @"POST";
    requestObject.HTTPBody = objectData;
    requestObject.timeoutInterval = (double)[[YtSDKKitFramework sharedInstance] networkTimeoutMs]/1000.f;
    if (headers)
    {
       for (id key in headers)
       {
           requestObject[@"key"]=headers[key] ;
       }
    }
    NSURLSessionDataTask *dataTask = [session dataTaskWithRequest:requestObject
                                                completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        
        if (error != nil)
        {
            responseBlock(nil, error);
        }
        else
        {
            NSError* errorLocal;
            NSDictionary* json = [NSJSONSerialization JSONObjectWithData:data
                                                                 options:kNilOptions
                                                                   error:&errorLocal];
            if (errorLocal != nil) {
                YTLOG_INFO(@"response :%@", [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding]);
            } else {
                YTLOG_INFO(@"response:%@", json);
            }
            responseBlock(json, errorLocal);
        }
        
    }];
    [dataTask resume];
}
#endif




#pragma mark AVCaptureVideoDataOutputSampleBufferDelegate
-(void)captureOutput:(AVCaptureOutput *)output didOutputSampleBuffer:(CMSampleBufferRef)sampleBuffer fromConnection:(AVCaptureConnection *)connection
{
    @autoreleasepool
    {
        
        CMTime lastSampleTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer);
#ifdef MODEL_ST_SILENT_LIVENESS

        if (output == self.videoOutput && self.isStartDectet == YES){
            
            [self.stDetector setTargetRect: CGRectMake(self.tkLiveFaceView.boxRect.origin.x, self.tkLiveFaceView.boxRect.origin.y, self.tkLiveFaceView.boxRect.size.height, self.tkLiveFaceView.boxRect.size.height)];
            [self.stDetector input:sampleBuffer];
        }
        
#else
        if (output==self.videoOutput) {
        
            [[YtSDKKitFramework sharedInstance] updateWithFrameData:sampleBuffer withDataType:YTVideoType];
        }
    
#endif
        
        if (self.isRecording)
                   {
                       if(self.assetWriter.status == AVAssetWriterStatusUnknown)
                       {
                           [self.assetWriter startWriting];
                           [self.assetWriter startSessionAtSourceTime:lastSampleTime];
                       }
                       
                       if(self.assetWriter.status > AVAssetWriterStatusWriting)
                       {
                           if (self.assetWriter.status == AVAssetWriterStatusCompleted)
                           {
                               TKLogInfo(@"TKSmartOneVideo:写视频数据完成");
                           }else if(self.assetWriter.status == AVAssetWriterStatusFailed )
                           {
                               TKLogInfo(@"TKSmartOneVideo:写视频数据失败:%@", self.assetWriter.error);
                           }
                           
                           [self.assetWriter endSessionAtSourceTime:lastSampleTime];
                           return;
                       }
                       
                       
                       // 写入视频
                       if (output == self.videoOutput)
                       {
                           if ([self.videoWriterInput isReadyForMoreMediaData])
                           {
                               if(![self.videoWriterInput appendSampleBuffer:sampleBuffer])
                               {
                                   TKLogInfo(@"TKSmartOneVideo:无法写入视频输入");
                               }
                           }
                       }
                       
                   }
    }
}



/**
 <AUTHOR> 2019年03月02日16:46:58
 @视频镜像处理
 @return CGAffineTransform
 */
-(CGAffineTransform)transformFromVideoBufferOrientationToOrientation:(AVCaptureVideoOrientation)orientation withAutoMirroring:(BOOL)mirror
{
    CGAffineTransform transform = CGAffineTransformIdentity;

        //树立摄像头顺时针旋转180度，这样后台看视频是正的
    transform = CGAffineTransformMakeRotation(M_PI/180.0*180.0);
    
    if ([[self getCamera:AVCaptureDevicePositionFront] position] == AVCaptureDevicePositionFront )
    {
        if ( mirror ) {
            transform = CGAffineTransformScale(transform, -1, 1);
        }
        else {
            if ( orientation ==AVCaptureVideoOrientationPortrait ) {
                transform = CGAffineTransformRotate( transform, M_PI );
            }
        }
    }
    transform = CGAffineTransformScale(transform, 1, 1);
    return transform;
}

/**
 <AUTHOR> 2019年03月02日11:04:39
 @获取指定摄像头
 @return AVCaptureDevice
 */
- (AVCaptureDevice *)getCamera:(AVCaptureDevicePosition) position
{
    //获取前置摄像头设备
    
    //返回和视频录制相关的所有默认设备
    NSArray *devices = [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];
    //遍历这些设备返回跟position相关的设备
    for (AVCaptureDevice *device in devices) {
        if ([device position] == position) {
            return device;
        }
    }
    return nil;
}

#pragma mark 播放本地mp3处理
//转接等待中播放音乐
- (void)handleAudioPlay:(NSString*)voiceName
{
    [self handleAudioPlay:voiceName sourceDirectory:@"Resources/TKOpenPlugin60007"];
}

- (void)handleAudioPlay:(NSString*)voiceName sourceDirectory:(NSString *)sourceDirectory
{
    if ([TKStringHelper isEmpty:voiceName]) {
        // 直接播放结束，跳过播放流程
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(moviePlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.mp3Player.currentItem];
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
        
        NSURL *aUrl = nil;
        NSString* fPath = [bundle pathForResource:voiceName ofType:@"mp3" inDirectory:sourceDirectory];
            
        NSFileManager *fm = [[NSFileManager alloc] init];
            
        if ([fm fileExistsAtPath:fPath]) {
            aUrl = [NSURL fileURLWithPath:fPath];
        }
        
        AVPlayerItem * songItem = [[AVPlayerItem alloc]initWithURL:aUrl];
        self.mp3Player = [[AVPlayer alloc]initWithPlayerItem:songItem];
        [self.mp3Player play];
        self.isPlayingMp3=YES;
        self.currentPlayingMp3=voiceName;
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(moviePlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.mp3Player.currentItem];
    });
}

//音频播放完毕
- (void)moviePlayDidEnd:(NSNotification*)notification{
    self.isStartDectet = YES;
    self.isPlayingMp3=NO;
}

//停止播放音乐
- (void)handleAudioStopPlay{
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:self.mp3Player.currentItem];
    [self.mp3Player pause];
    self.isPlayingMp3=NO;
}

//开始话术播放
- (void)handleStartAudioPlay:(NSString*)voiceName sourceDirectory:(NSString *)sourceDirectory
{
    if ([TKStringHelper isEmpty:voiceName]) {
        // 直接播放结束，跳过播放流程
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(movieStartPlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.startMp3Player.currentItem];
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
        
        NSURL *aUrl = nil;
        NSString* fPath = [bundle pathForResource:voiceName ofType:@"mp3" inDirectory:sourceDirectory];
        
        if([TKStringHelper isNotEmpty:self.requestParam[@"prepareTipAudio"]]){
            NSString *document = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, true)[0];
            NSString *filePath = [document stringByAppendingString:@"/livePrepareTipAudio.mp3"];
            
            NSFileManager *fileManager = [NSFileManager defaultManager];
            BOOL fileExists = [fileManager fileExistsAtPath:filePath];
            
            if (fileExists != NO) {
                // 删除原有的视频
                NSError *error = nil;
                [fileManager removeItemAtPath:filePath error:&error];
                if (error) {
                    TKLogInfo(@"删除活体音频文件出错%@", error.description);
                } else {
                    fileExists = NO;
                    TKLogInfo(@"活体音频文件删除成功");
                }
            }

            NSData *audioData=[TKBase64Helper dataWithDecodeBase64String:self.requestParam[@"prepareTipAudio"]];
            if(audioData){
                [audioData writeToFile:filePath atomically:YES];
                fPath = filePath;
            }

            

        }
        NSFileManager *fm = [[NSFileManager alloc] init];
        if ([fm fileExistsAtPath:fPath]) {
            aUrl = [NSURL fileURLWithPath:fPath];
        }
        
        AVPlayerItem * songItem = [[AVPlayerItem alloc]initWithURL:aUrl];
        [songItem addObserver:self forKeyPath:@"status" options:NSKeyValueObservingOptionNew context:nil];// 监听status属性
        self.startMp3Player = [[AVPlayer alloc]initWithPlayerItem:songItem];
        [self.startMp3Player play];
        self.isPlayingMp3=YES;
        self.currentPlayingMp3=voiceName;
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(movieStartPlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.startMp3Player.currentItem];

    });
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
    AVPlayerItem *playerItem = (AVPlayerItem *)object;
    if ([keyPath isEqualToString:@"status"]) {
        if ([playerItem status] == AVPlayerStatusUnknown) {
            TKLogInfo(@"AVPlayerStatusReadyToPlay");
        } else if ([playerItem status] == AVPlayerStatusFailed) {
            TKLogInfo(@"AVPlayerStatusFailed");
            NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
            callJsParam[@"funcNo"]=@"60055";
            callJsParam[@"error_no"]=@"-4";
            callJsParam[@"error_info"]=@"音频播放失败";
            callJsParam[@"tkuuid"]=self.requestParam[@"tkuuid"];
            if (self.delegate&&[self.delegate respondsToSelector:@selector(tkLiveDetectDidComplete:)]) {
                [self.delegate tkLiveDetectDidComplete:callJsParam];
            }else{
               [self.mService iosCallJsWithDic:callJsParam callBackFunc:nil];
            }
            
        }
    }
}

//开始话术音频播放完毕
- (void)movieStartPlayDidEnd:(NSNotification*)notification{

    dispatch_async(dispatch_get_main_queue(), ^{
                
        if ([self.requestParam[@"isNeedRecord"] intValue]==1) {
            //录制视频
            [self startRecordingAction];
        }
        
    #ifdef MODEL_ST_SILENT_LIVENESS

        // 开始检测
        [self.stDetector start];
        self.isStartDectet = YES;
        self.isPlayingMp3=NO;
    #else
        [self tencentSilent];
        [[YtSDKKitFramework sharedInstance] fireEvent:YTTriggerBeginLiveness withContent:nil];
    #endif
        
        //埋点
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
        eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
        [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventLiveDetectAlignment progress:TKPrivateEventProgressStart result:TKPrivateEventResultNone eventDic:eventDic];
    });

}
#pragma mark lazyloading
/**
 <AUTHOR> 2019年03月02日10:39:21
 @懒加载初始化视频预览视图
 @return UIView
 */
-(UIView *)avPreviewView{
    if (!_avPreviewView) {
        _avPreviewView=[[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        [_avPreviewView setBackgroundColor:[UIColor grayColor]];
        [_avPreviewView.layer addSublayer:self.avPreviewLayer];
        
    }
    return _avPreviewView;
}

/**
 <AUTHOR> 2019年03月02日10:48:12
 @懒加载初始化AVCaptureSession
 @return AVCaptureSession
 */
-(AVCaptureSession *)avSession{
    if (!_avSession) {
        _avSession=[[AVCaptureSession alloc] init];
        [_avSession beginConfiguration];
        if ([_avSession canSetSessionPreset:AVCaptureSessionPreset640x480]) {//设置分辨率
            _avSession.sessionPreset=AVCaptureSessionPreset640x480;
            
            //商汤活体摄像头是横屏，腾讯活体是竖屏

            self.videoWidth=[self.requestParam[@"videoWidth"] intValue]?[self.requestParam[@"videoWidth"] intValue]:240;
            self.videoHeight=[self.requestParam[@"videoHeight"] intValue]?[self.requestParam[@"videoHeight"] intValue]:320;
        }

        if ([_avSession canAddInput:self.avDeviceInput]) {
            [_avSession addInput:self.avDeviceInput];
        }
        
        
        if ([_avSession canAddOutput:self.videoOutput]) {
            [_avSession addOutput:self.videoOutput];
            
            
            AVCaptureConnection *connection = [self.videoOutput connectionWithMediaType:AVMediaTypeVideo];
            [connection setEnabled:YES];

            [connection setVideoOrientation:AVCaptureVideoOrientationPortrait];
            
        }



        [_avSession commitConfiguration];
    }
    return _avSession;
}


/**
 <AUTHOR> 2019年03月02日10:57:35
 @初始化懒加载AVCaptureDeviceInput摄像头
 @return AVCaptureDeviceInput
 */
-(AVCaptureDeviceInput *)avDeviceInput{
    if (!_avDeviceInput) {
        NSError *error=nil;
        _avDeviceInput=[AVCaptureDeviceInput deviceInputWithDevice:[self getCamera:AVCaptureDevicePositionFront] error:&error];
        if (error) {
            TKLogInfo(@"TKSmartOneVideo:取前置摄像头时出现问题");
            return nil;
        }
    }
    return _avDeviceInput;
}



/**
 <AUTHOR> 2019年03月02日16:20:36
 @初始化懒加载AVCaptureVideoDataOutput输出流l线程队列
 @return AVCaptureVideoDataOutput
 */
-(dispatch_queue_t)outPutQueue{
    if (!_outPutQueue) {
        _outPutQueue = dispatch_queue_create("outPutQueue", DISPATCH_QUEUE_SERIAL);
    }
    return _outPutQueue;
}

/**
 <AUTHOR> 2019年03月02日16:25:15
 @初始化懒加载AVCaptureVideoDataOutput视频输出流
 @return AVCaptureVideoDataOutput
 */
-(AVCaptureVideoDataOutput *)videoOutput{
    if (!_videoOutput) {
        _videoOutput = [[AVCaptureVideoDataOutput alloc] init];
        NSDictionary *rgbOutputSettings = @{(id) kCVPixelBufferPixelFormatTypeKey: [NSNumber numberWithInt:kCVPixelFormatType_32BGRA]};
        
        [_videoOutput setVideoSettings:rgbOutputSettings];
        [_videoOutput setAlwaysDiscardsLateVideoFrames:YES];
        [_videoOutput setSampleBufferDelegate:self queue:self.outPutQueue];

        
    }
    return _videoOutput;
}


/**
 <AUTHOR> 2019年03月02日16:36:51
 @初始化懒加载AVAssetWriter写流
 @return AVAssetWriter
 */
-(AVAssetWriter *)assetWriter{
    if (!_assetWriter) {
        NSError *error = nil;
        _assetWriter = [[AVAssetWriter alloc] initWithURL:[NSURL fileURLWithPath:[self videoFileURLString]] fileType:AVFileTypeMPEG4 error:&error];
        NSParameterAssert(_assetWriter);
        if(error)
        {
            TKLogInfo(@"TKSmartOneVideo:assetWriter:%@", [error localizedDescription]);
        }else{
            // add input
            if ([_assetWriter canAddInput:self.videoWriterInput])
            {
                [_assetWriter addInput:self.videoWriterInput];
              
            }

        }
    }
    return _assetWriter;
}

/**
 <AUTHOR> 2019-03-02 16:44:01
 @初始化懒加载AVAssetWriterInput视频写入流
 @return AVAssetWriterInput
 */
-(AVAssetWriterInput *)videoWriterInput{
    if (!_videoWriterInput) {
        // 添加视频输入
        NSDictionary *videoCompressionProps = [NSDictionary dictionaryWithObjectsAndKeys:[NSNumber numberWithDouble:512.0*1024.0],AVVideoAverageBitRateKey,  nil ];
        
        NSDictionary *videoSettings = [NSDictionary dictionaryWithObjectsAndKeys:AVVideoCodecH264, AVVideoCodecKey,[NSNumber numberWithInt:self.videoWidth], AVVideoWidthKey,[NSNumber numberWithInt:self.videoHeight],AVVideoHeightKey,videoCompressionProps, AVVideoCompressionPropertiesKey, nil];
        
        _videoWriterInput = [AVAssetWriterInput assetWriterInputWithMediaType:AVMediaTypeVideo outputSettings:videoSettings];
        
        _videoWriterInput.expectsMediaDataInRealTime = YES;// 是否裁剪视频输入
        
        NSParameterAssert(_videoWriterInput);

    }
    return _videoWriterInput;
}


/**
 <AUTHOR> 2019年03月02日11:04:22
 @初始化懒加载AVCaptureVideoPreviewLayer
 @return AVCaptureVideoPreviewLayer
 */
-(AVCaptureVideoPreviewLayer *)avPreviewLayer{
    if (!_avPreviewLayer) {
        _avPreviewLayer= [[AVCaptureVideoPreviewLayer alloc] initWithSession:self.avSession];
        _avPreviewLayer.frame=[UIScreen mainScreen].bounds;
        _avPreviewLayer.videoGravity= AVLayerVideoGravityResizeAspectFill;
        _avPreviewLayer.connection.videoOrientation = AVCaptureVideoOrientationPortrait;
        
    }
    return _avPreviewLayer;
}


#ifdef MODEL_ST_SILENT_LIVENESS
/**
 <AUTHOR> 2019年03月04日15:04:48
 @初始化懒加载STLivenessDetector
 @return STLivenessDetector
 */
- (STLivenessDetector *)stDetector{
    if (!_stDetector) {
        
        NSString *licenseFilePath = [[NSBundle mainBundle] pathForResource:@"SenseID_Liveness_Silent" ofType:@"lic"];
        
        NSString *modelBundleFilePath = [[NSBundle mainBundle] pathForResource:@"STLivenessModel" ofType:@"bundle"];
        
        STLivenessDetectorConfig *config = [[STLivenessDetectorConfig alloc] init];
        config.licenseFilePath = licenseFilePath;
        
        config.modelConfig = @{
            @"align": [modelBundleFilePath stringByAppendingPathComponent: @"KM_Align_occlusion_106_1.19.6.model"],
            @"hunter": [modelBundleFilePath stringByAppendingPathComponent: @"KM_Hunter_SmallFace_Gray_ppl_9.6.1_half_compression_v1_weak.model"],
            @"august": [modelBundleFilePath stringByAppendingPathComponent: @"KM_August_Face_Gray_PPL_2.6.3_half_compression_v2_origin.model"],
            @"headpose": [modelBundleFilePath stringByAppendingPathComponent: @"KM_Headpose_106_ppl_2.3.0_half_compression_v2_origin.model"],
            @"pageant": [modelBundleFilePath stringByAppendingPathComponent: @"KM_Pageant_88id_Fcfp32_ppl_1.0.7_half_compression_v2_origin.model"],
            @"eyestate": [modelBundleFilePath stringByAppendingPathComponent: @"KM_eyestate_ppl_3.6.1_half_compression_v2_origin.model"],
            @"liveness": [modelBundleFilePath stringByAppendingPathComponent: @"KM_RGB_Liveness_General_Face_FP32_12.1.44_half_compression_v2_origin.model"],
        };
        
        //通过配置判断是否要开启商汤质检
        NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
                               
        NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
                               
        NSMutableDictionary *cDic;
                               
        if (plistPath) {
                                   
            cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
        }
        
        if ([TKStringHelper isNotEmpty:cDic[@"isOpenSTQuality"]]&&[cDic[@"isOpenSTQuality"] intValue]==1) {
            //isOpenSTQuality为1才需要开启质检
            TKLogInfo(@"开启质检授权");

        }else{
            // 不带质量的授权，需要打开下面的配置；带质量的授权，屏蔽下面的配置
            STCommonFaceQualityConfig *qualityConfig = [STCommonFaceQualityConfig new];
            qualityConfig.angleEnabled = NO;
            qualityConfig.eyeOcclusionEnabled = NO;
            qualityConfig.mouthOcclusionEnabled = NO;
            qualityConfig.browOcclusionEnabled = NO;
            qualityConfig.mouthOpenEnabled = NO;
            qualityConfig.overDarkThreshold = 0.2f;
            qualityConfig.overGlareThreshold = 0.8f;
            qualityConfig.blurThreshold = 0.44f;
            qualityConfig.multiTargetsEnabled = NO;
            config.qualityConfig = qualityConfig;
        }
        

        
        NSError *error = nil;
        _stDetector = [[STLivenessDetector alloc] initWithConfig:config andDelegate:self onError:&error];
        
        // 初始化失败
        if (!_stDetector) {
            
            TKLogInfo(@"create Detector failed");
//            [self showAlertView:@"提示" message:@"活体检测授权失败" tag:7001];
            [self moreAuthFailAction];
            return nil;
        }
        
        //设置静默活体检测的超时时间
        int readyTimeOut = [self.requestParam getIntWithKey:@"readyTimeOut"];
        readyTimeOut = readyTimeOut <= 0 ? 15 : readyTimeOut;
        int motionTimeout = [self.requestParam getIntWithKey:@"motionTimeout"];
        motionTimeout = motionTimeout <= 0 ? 10 : motionTimeout;
        
        [_stDetector setReadyTimeoutDuration:readyTimeOut * 1000]; // 单位毫秒
        [_stDetector setMotionTimeoutDuration:motionTimeout * 1000]; // 单位毫秒
//
//        [_stDetector setLivenessFaceTooFar:0.4 tooClose:0.9];
//
//        [_stDetector setHacknessThresholdScore:0.9];
        
        [_stDetector setDetectMotionTypes:self.randomActionTypeArray];
        
        // 埋点-活体-初始化-成功
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
        eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeLocal];    // 活体类型
        [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect subEventName:TKPrivateSubEventLiveDetectPrepareVideo progress:TKPrivateEventProgressNone result:TKPrivateEventResultSuccess eventDic:eventDic];
    }
    return _stDetector;
}


#else


#endif


/**
 <AUTHOR> 2019年04月03日10:09:41
 @初始化懒加载单向视频界面
 @return 单向视频界面
 */
-(TKLiveFaceView *)tkLiveFaceView{
    if (!_tkLiveFaceView) {
        _tkLiveFaceView=[[TKLiveFaceView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        _tkLiveFaceView.delegate=self;
    }
    return _tkLiveFaceView;
}


@end
