//
//  TKOpenPlugin60013.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/9/7.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "TKOpenPlugin60013.h"
#import "MTakePhotoViewController.h"
#import "MTakeBigPictureViewController.h"
#import <AVFoundation/AVFoundation.h>
#import "TKOpenController.h"
#import "TKOpenAccountService.h"
#import <Photos/Photos.h>
#import <MobileCoreServices/UTCoreTypes.h>
#import "TKCommonUtil.h"
#import "UIViewController+TKAuthorityKit.h"
#import "TKVideoAlertView.h"
#import "TKCardPreview.h"

@interface TKOpenPlugin60013()<TKMBProgressHUDDelegate,UIImagePickerControllerDelegate,UINavigationControllerDelegate,TKTakePhotoResultDelegate,TKVideoAlertViewDelegate,TKCardPreviewDelegate>
{
     ResultVo *_resultVo;// 返回的结果
    
     TKMBProgressHUD *mHUD;
    
     NSMutableDictionary *h5Params;
}
@property (nonatomic, strong) TKOpenAccountService *mService;
@property (nonatomic, strong) TKVideoAlertView *videoAlertView;//视频挂断提示框
@property (nonatomic, strong) TKCardPreview *preview;//证件预览界面
@property (nonatomic, strong) UIImage  *albumImg;//证件预览界面
@end

@implementation TKOpenPlugin60013


- (ResultVo *)serverInvoke:(id)param{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (param && param[@"moduleName"]) {
            
            if (self.currentViewCtrl && [self.currentViewCtrl isKindOfClass:[TKBaseWebViewController class]]) {
                
                TKBaseWebViewController *webCtl = (TKBaseWebViewController*)self.currentViewCtrl;
                
                webCtl.tkName = param[@"moduleName"];
            }
        }
    });
    
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
 
    h5Params = reqParam;
    
    // 检测入参是否有误
    if ([self IsErrorParam:reqParam]) {
        return _resultVo;
    }
    
    self.mService=[[TKOpenAccountService alloc] init];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //是否在调用插件前展示原生权限介绍页面
        if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
            NSMutableArray *authArray=[[NSMutableArray alloc] init];
            if ([param[@"isAlbum"] integerValue] != 0||[TKStringHelper isEmpty:param[@"isAlbum"]]) {
                [authArray addObject:@(TKAuthorizationType_Photo)];
            }
            
            if ([@"phone" isEqualToString:param[@"action"]]) {
                [TKAuthorizationHelper requestAuthorization:authArray   authCallBacks:nil btnCallBack:^{
                    [self.currentViewCtrl tkIsPhotoLibraryPermissions:^{
                        [self openAlbum];
                    }];
                }];
            }else{
                [authArray addObject:@(TKAuthorizationType_Camera)];
                
                [TKAuthorizationHelper requestAuthorization:authArray   authCallBacks:nil btnCallBack:^{
                    [self.currentViewCtrl tkIsCameraPermissions:^{
                        [self handleAuthorized:h5Params];
                    }];
                }];
            }
  
        }else{
            if ([@"phone" isEqualToString:param[@"action"]]) {
                [self.currentViewCtrl tkIsPhotoLibraryPermissions:^{
                    [self openAlbum];
                }];
                
            }else{
                [self.currentViewCtrl tkIsCameraPermissions:^{
                    [self handleAuthorized:h5Params];
                }];
            }
        }
        
        
    });
    
    
    
    return _resultVo;
}


- (BOOL)IsErrorParam:(NSMutableDictionary *)reqParam
{
    ResultVo *resultVo = [[ResultVo alloc] init];
    
    _resultVo = resultVo;

    if ([TKStringHelper isEmpty:@"url"]) {
        
        resultVo.errorNo = -6001301;
        resultVo.errorInfo = @"访问地址不能为空";
        return YES;
        
    }else if ([TKStringHelper isEmpty:@"imgType"]) {
        
        resultVo.errorNo = -6001302;
        resultVo.errorInfo = @"证件类型不能为空";
        return YES;
        
    }else if ([TKStringHelper isEmpty:@"requestParam"]) {
        
        resultVo.errorNo = -6001303;
        resultVo.errorInfo = @"接口参数不能为空";
        return YES;
    }
    
    return NO;
}

- (void)handleAuthorized:(id)param
{
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    
    NSArray* tImageType;
    
    if ([[NSString stringWithFormat:@"%@",param[@"imgType"]] rangeOfString:@","].location != NSNotFound) {
        
        tImageType = [param[@"imgType"] componentsSeparatedByString:@","];
        
    }else{
        
        tImageType = @[[NSString stringWithFormat:@"%@",param[@"imgType"]]];
    }
    
    if (tImageType && tImageType.count == 1) {
        
        if ([[tImageType objectAtIndex:0] isEqualToString:@"3"]) {//仅拍摄大头照
            
            MTakeBigPictureViewController *tBCtl = [[MTakeBigPictureViewController alloc] init];
            
            tBCtl.param = param;
                
            [self.currentViewCtrl presentViewController:tBCtl animated:YES completion:nil];

            
            return;
        }
        
    }
    
    MTakePhotoViewController *tCtl = [[MTakePhotoViewController alloc] init];
    
    tCtl.param = param;
    
    tCtl.delegate = self;
 
    [self.currentViewCtrl presentViewController:tCtl animated:YES completion:nil];
        
   
}

/**
 *  Description 打开相册
 */
- (void)openAlbum{
    if ([h5Params getIntWithKey:@"isNeedOcrAffirmView"]!=0||!h5Params[@"isNeedOcrAffirmView"]) {
        //需要预览页情况
        [self.currentViewCtrl.view addSubview:self.preview];
        [self.preview setHidden:YES];
        self.albumImg=nil;
    }
    

    UIImagePickerController* cameraPicker = [[UIImagePickerController alloc] init];
    cameraPicker.delegate = self;
    cameraPicker.allowsEditing = NO; // 不需要编辑图片
    cameraPicker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    NSArray* mediaArray = [NSArray arrayWithObjects:(NSString*)kUTTypeImage, nil];
    cameraPicker.mediaTypes = mediaArray;
    cameraPicker.modalPresentationStyle=UIModalPresentationFullScreen;
    [self.currentViewCtrl presentViewController:cameraPicker animated:YES completion:nil];

}

#pragma mark - ImagePicker代理
//取消照相机的回调
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [self.currentViewCtrl dismissViewControllerAnimated:YES completion:nil];
    
    if ([h5Params getIntWithKey:@"isNeedOcrAffirmView"]!=0||!h5Params[@"isNeedOcrAffirmView"]) {
        //需要预览页情况
        [_preview removeFromSuperview];
        _preview=nil;
        
    }
    TKLogInfo(@"取消照相机的回调");
}

//  成功获得相片还是视频后的回调
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info
{
    
    NSString* mediaType = [info objectForKey:UIImagePickerControllerMediaType];
    if ([mediaType isEqualToString:(NSString*)kUTTypeImage])
    {
        // get the image
        self.albumImg= [info objectForKey:UIImagePickerControllerOriginalImage];
        if (picker.allowsEditing && [info objectForKey:UIImagePickerControllerEditedImage]) {// 可编辑的图片
            self.albumImg = [info objectForKey:UIImagePickerControllerEditedImage];
        }
        
        
        if([h5Params getIntWithKey:@"isNeedOcrAffirmView"]!=0||!h5Params[@"isNeedOcrAffirmView"]){
            //需要预览页情况
            [self.preview setHidden:NO];
            if ([h5Params[@"isNeedSample"] intValue]==1) {
                
                NSArray* tImageType;
                
                if ([[NSString stringWithFormat:@"%@",h5Params[@"imgType"]] rangeOfString:@","].location != NSNotFound) {
                    
                    tImageType = [h5Params[@"imgType"] componentsSeparatedByString:@","];
                }else{
                    tImageType = @[[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]];
                }
                if ([[tImageType objectAtIndex:0] intValue]==4){
                    [self.preview previewCardNeedSampleView:self.albumImg isPhotoAlbum:YES isFrontCard:YES];
                }else{
                    [self.preview previewCardNeedSampleView:self.albumImg isPhotoAlbum:YES isFrontCard:NO];
                }
                    
            }else{
                [self.preview previewCardView:self.albumImg isPhotoAlbum:YES];
            }
            
            [picker dismissViewControllerAnimated:YES completion:nil];
        }else{
            NSData* data =[TKImageHelper compressImageData:self.albumImg toByte:(h5Params[@"compressSize"]? [h5Params[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];
            [picker dismissViewControllerAnimated:YES completion:^{
                [self affirmAction:data];
            }];
        }
    }
}

/**
 <AUTHOR> 2019年06月28日19:38:06
 @照片确认上传事件
 */
-(void)affirmAction:(NSData *)data{
    if (h5Params[@"isUpload"] && ![h5Params[@"isUpload"] integerValue]) {//提交证件图给H5上传
        
        [self showImage:data];
        
    }else{
        dispatch_async(dispatch_get_main_queue(), ^{
            
            mHUD = [[TKMBProgressHUD alloc] initWithView:self.currentViewCtrl.view];
            [self.currentViewCtrl.view addSubview:mHUD];
            mHUD.dimBackground = YES;
            if ([@"3" isEqualToString:[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]]) {
                mHUD.labelText = @"上传大头照中...";
            }else{
                mHUD.labelText = @"证件识别中...";
            }
            mHUD.mode = TKMBProgressHUDModeIndeterminate;
            mHUD.delegate = self;
            [mHUD show:YES];
        });
        
        dispatch_async(dispatch_get_global_queue(0, 0), ^{
            
           
            
            if (data) {
                [self upLoadImage:data];
            }
        });
    }
}

- (void)showImage:(NSData *)imageData
{
    NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:imageData];
    NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
    

    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
    reqParam[@"funcNo"]=@"60050";
    reqParam[@"error_no"]=@"0";
    reqParam[@"tkuuid"]=h5Params[@"tkuuid"];
    reqParam[@"extParams"]=h5Params[@"extParams"];
    
    if ([@"3" isEqualToString:[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]]) {
        reqParam[@"base64"]=base64;
    }else if ([@"4" isEqualToString:[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]]){
        reqParam[@"frontBase64"]=base64;
    }else{
        reqParam[@"backBase64"]=base64;
    }
    
    [self iosCallJsWithParam:reqParam];
}
#pragma mark -上传证件图片
- (void)upLoadImage:(NSData *)imageData
{
    NSString *hostUrl;
    
    if ([h5Params[@"url"] rangeOfString:@"?"].length > 0) {
        
        hostUrl = h5Params[@"url"];
        
    }else {
        
        hostUrl = [NSString stringWithFormat:@"%@?", h5Params[@"url"]];
        
    }
    
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
//    [tkReqParam addEntriesFromDictionary:h5Params];
    
    if (h5Params[@"requestParam"]) {
        
        NSArray *rpArr = [h5Params[@"requestParam"] componentsSeparatedByString:@"&"];
        
        for (NSString *str in rpArr) {
            
            NSArray *tArr = [str componentsSeparatedByString:@"="];
            
            if ([tArr count] > 0) {
                
                tkReqParam[[tArr objectAtIndex:0]]=[tArr objectAtIndex:1];
            }
        }
        
    }
    
    if (h5Params[@"serverAccessType"] && [h5Params[@"serverAccessType"] integerValue] == 1) {
        tkReqParam[h5Params[@"fileUploadKey"]?h5Params[@"fileUploadKey"]:@"file_data"]=[TKBase64Helper stringWithEncodeBase64Data:imageData];
    }else{
        tkReqParam[h5Params[@"fileUploadKey"]?[NSString stringWithFormat:@"%@@@F",h5Params[@"fileUploadKey"]]:@"file_data@@F"]=imageData;
    }
    
    if ([@"3" isEqualToString:[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]]) {
        tkReqParam[@"image_type"]=@"otherimg";
    }else{
        tkReqParam[@"image_type"]=[@"4" isEqualToString:[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]]? @"idfrontimg" : @"idbackimg";
    }
    
    [self.mService uploadFileWithURL:hostUrl param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSMutableDictionary *dic = (NSMutableDictionary*)[results objectAtIndex:0];
            
            dic[@"imageData"]=imageData;
            
            [self uploadFinished:dic];
            
        }else if(resultVo.errorNo == -999){
            [mHUD hide:YES];
            //未登录情况特殊处理,iOS8以后用
            UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:resultVo.errorInfo?resultVo.errorInfo:@"未登录" preferredStyle: UIAlertControllerStyleAlert];

            UIAlertAction *okAction = [UIAlertAction actionWithTitle: @"确定" style: UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
                reqParam[@"funcNo"]=@"60050";
                reqParam[@"tkuuid"]=h5Params[@"tkuuid"];
                reqParam[@"error_no"]=@"-999";
                reqParam[@"error_info"]=resultVo.errorInfo;
            
                [self iosCallJsWithParam:reqParam];
            }];

            [alertController addAction: okAction];
            [self.currentViewCtrl presentViewController:alertController animated:YES completion:nil];
        }else{
            TKLogInfo(@"%@",resultVo.errorInfo);
            
            [self uploadFailed:resultVo.errorInfo];
            
        }
    }];
}



#pragma mark -上传成功
- (void)uploadFinished:(NSDictionary*)rDic
{
    if (rDic && [rDic[@"error_no"] integerValue] == 0) {//大头照上传成功
        
        [mHUD hide:YES];
        
        NSString *tmpBase64 = [TKBase64Helper stringWithEncodeBase64Data:rDic[@"imageData"]];
        NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tmpBase64];
        NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
        reqParam[@"funcNo"]=@"60050";
        reqParam[@"error_no"]=@"0";
        reqParam[@"tkuuid"]=h5Params[@"tkuuid"];
        reqParam[@"extParams"]=h5Params[@"extParams"];
        if ([@"3" isEqualToString:[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]]) {
            reqParam[@"base64"]=base64;
            reqParam[@"filepath"]=rDic[@"filepath"];
            reqParam[@"secret"]=rDic[@"secret"];

        }else if ([@"4" isEqualToString:[NSString stringWithFormat:@"%@",h5Params[@"imgType"]]]){
            reqParam[@"idNo"]=rDic[@"idno"];
            reqParam[@"usersex"]=rDic[@"usersex"]?rDic[@"usersex"]:rDic[@"sex"];
            reqParam[@"custName"]=rDic[@"custname"];
            reqParam[@"native"]=rDic[@"native"];
            reqParam[@"ethnicName"]=rDic[@"ethnicname"];
            reqParam[@"birthday"]=rDic[@"birthday"];
            reqParam[@"frontBase64"]=base64;
            reqParam[@"frontFilePath"]=rDic[@"filepath"];
            reqParam[@"frontSecret"]=rDic[@"secret"];
            
        }else{
            reqParam[@"policeOrg"]=rDic[@"policeorg"];
            reqParam[@"idbeginDate"]=rDic[@"idbegindate"];
            reqParam[@"idendDate"]=rDic[@"idenddate"];
            reqParam[@"backBase64"]=base64;
            reqParam[@"backFilePath"]=rDic[@"filepath"];
            reqParam[@"backSecret"]=rDic[@"secret"];
        }
        
        [self iosCallJsWithParam:reqParam];
        
    }else{
        
        mHUD.mode = TKMBProgressHUDModeText;
        mHUD.labelText = [NSString stringWithFormat:@"%@", rDic[@"error_info"]];
        mHUD.margin = 10.f;
        mHUD.removeFromSuperViewOnHide = YES;
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            [mHUD hide:YES];
        });
        
    }
    
}

#pragma mark -上传失败
- (void)uploadFailed:(NSString *)error
{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [mHUD hide:YES];
        
        [self.currentViewCtrl.view addSubview:self.videoAlertView];
        self.videoAlertView.describeLabel.text=error? error:@"网络异常";
        self.videoAlertView.titleLabel.text=@"证件识别不全，请检查！";
        [self.videoAlertView setOnlyBtnTitle:@"确认"];
    });
    
}

#pragma mark -implement MBProgressHUDDelegate
- (void)hudWasHidden:(TKMBProgressHUD *)hud {
    // Remove HUD from screen when the HUD was hidded
    [mHUD removeFromSuperview];
    
    mHUD = nil;
}

- (void)tkIDCardDidComplete:(id)tResult
{
    TKLogInfo(@"拍照回调h5:%@",tResult[@"moduleName"]);

    if (tResult) {
        if (self.isH5) {
            [self iosCallJsWithParam:tResult];
        } else {
            [self iosCallPluginCallBack:tResult];
        }
    }
}

#pragma mark TKVideoAlertViewDelegate


//取消按钮事件
-(void)cancelVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}

//继续按钮事件
-(void)takeVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}

//独立按钮事件
-(void)onlyVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
}

#pragma mark TKCardPreviewDelegate

//重拍重选事件
- (void)previewRetryAction:(id)sender{
    [_preview removeFromSuperview];
    _preview=nil;
    [self openAlbum];
}

//提交事件
- (void)previewSubmitAction:(id)sender{
    if([h5Params getIntWithKey:@"isNeedOcrAffirmView"]!=0||!h5Params[@"isNeedOcrAffirmView"]){
        [_preview removeFromSuperview];
        _preview=nil;
    }
    NSData* data =[TKImageHelper compressImageData:self.albumImg toByte:(h5Params[@"compressSize"]? [h5Params[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];
    [self affirmAction:data];
}

#pragma mark lazyloading
/**
 <AUTHOR> 2022年05月25日09:43:41
 @初始化懒加载videoAlertView
 @return videoAlertView
 */
-(TKVideoAlertView *)videoAlertView{
    if (!_videoAlertView) {
        _videoAlertView=[[TKVideoAlertView alloc] initWithFrame:self.currentViewCtrl.view.frame requestParams:nil];
        _videoAlertView.delegate=self;
    }
    return _videoAlertView;
}


/**
 <AUTHOR> 2023年01月13日15:45:05
 @初始化懒加载preview
 @return preview
 */
-(TKCardPreview *)preview{
    if (!_preview) {
        _preview=[[TKCardPreview alloc] initWithFrame:CGRectMake(0, 0, self.currentViewCtrl.view.TKHeight, self.currentViewCtrl.view.TKWidth) requestParams:h5Params];
        _preview.delegate=self;
        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
        _preview.transform = transform;
        [_preview setTKTop:0];
        [_preview setTKLeft:0];
    }
    return _preview;
}
@end
