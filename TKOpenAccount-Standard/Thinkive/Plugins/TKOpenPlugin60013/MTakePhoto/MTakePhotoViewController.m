//
//  MTakePhotoViewController.m
//  iosOpenP
//
//  Created by <PERSON><PERSON> on 15/7/23.
//
//

#import "MTakePhotoViewController.h"
#import <MobileCoreServices/UTCoreTypes.h>
#import "TKOpenAccountService.h"
#import "MTakeBigPictureViewController.h"
#import <AssetsLibrary/AssetsLibrary.h>
#import <Photos/Photos.h>
#import "UIViewController+TKAuthorityKit.h"
#import "TKAVCaptureManager.h"
#import "TKCommonUtil.h"
#import "TKCardPreview.h"

#define kParam_uuid       @"uuid"
#define kParam_userId     @"userId"
#define kParam_r          @"r"
#define kParam_imgType    @"imgType"
#define kParam_funcNum    @"funcNum"
#define kParam_photoType  @"photoType"
#define kParam_action     @"action"
#define kParam_url        @"url"
#define kParam_clientInfo @"clientInfo"
#define kParam_jsessionId @"jsessionId"
#define kParam_key        @"key"

#define TKCARD_AspectRatio 0.631915 //身份证高/宽的比例

@interface MTakePhotoViewController ()<UINavigationControllerDelegate,UIImagePickerControllerDelegate,TKMBProgressHUDDelegate,TakeBigPhotoResultDelegate,TKCardPreviewDelegate>
{
    TKAVCaptureManager *captureManager;
    
    BOOL isTakePhoto ,isUploadPositiveCard, flag, isRotate, isReject, isPhotoAlbum;
    
    UIView *tCView, *pCView;
    
    TKOpenAccountService *mService;
    
    UIImage *uImage;
    
    TKMBProgressHUD *mHUD;
    
    NSArray *tImageType;
    
    NSMutableDictionary *reqParam;
    
    NSInteger hStep;
    
}
@property(nonatomic,assign) CGRect  idRect,windoRect;//照片裁剪指定卡片区域,手机屏幕区域
@property(nonatomic,strong) UIView *topView,*leftView,*rightView,*bottomView;//顶部遮罩层,左侧遮罩层,右侧遮罩层,底部遮罩层
@property(nonatomic,strong) UIImageView *idAreaView;//指定扫描区域视图
@property (nonatomic, assign) BOOL isTakeClick;//是否点击过拍照
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@property (nonatomic, strong) TKCardPreview *preview;//证件预览界面
@end

@implementation MTakePhotoViewController

- (id)initWithParams:(id)mParams nibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil
{
    self = [self initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    
    if (self) {}
    
    return self;
}


- (void)viewDidLoad {
    
    [super viewDidLoad];
    
    [[UIApplication sharedApplication] setStatusBarHidden:YES];
    self.windoRect=CGRectMake(0, 0, self.view.TKWidth, self.view.TKHeight);
    isUploadPositiveCard = YES;
    
    mService = [[TKOpenAccountService alloc] init];
    
    reqParam = [NSMutableDictionary dictionary];
    
    if (_param) {
        
        if ([TKStringHelper isEmpty:_param[@"mainColor"]]) {
            self.mainColorString=TKCARD_MAIN_COLOR;
        }else{
            self.mainColorString=_param[@"mainColor"];
        }
        
        NSRange range = [[NSString stringWithFormat:@"%@",_param[@"imgType"]] rangeOfString:@","];
        
        if (range.length  > 0) {
            
            tImageType = [_param[@"imgType"] componentsSeparatedByString:@","];
            
        }else{
            
            tImageType = @[[NSString stringWithFormat:@"%@",_param[@"imgType"]]];
            
            if ([[tImageType objectAtIndex:hStep] isEqualToString:@"5"]) {//仅上传身份证反面
                
                isUploadPositiveCard = NO;
                
            }
        }
        
    }
    
    [self initTakeCardViewNew];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleEnterForegroundNotification:) name:UIApplicationWillEnterForegroundNotification object:nil];
    
    if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_View.objectId",@"open"]]) {
        
        [TKTraffic visitPage:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_View.objectId",@"open"]]];
    }
}

- (BOOL)prefersStatusBarHidden
{
    return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    if ([self respondsToSelector:@selector(setNeedsStatusBarAppearanceUpdate)]) {
        
        [self prefersStatusBarHidden];
        
        [self performSelector:@selector(setNeedsStatusBarAppearanceUpdate)];
        
    }
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    if (isUploadPositiveCard) {
        [TKTraffic visitPage:[NSString stringWithFormat:@"%@.3.1002",[TKCommonUtil fetchAppStatisticsMarker]]];
    }else{
        [TKTraffic visitPage:[NSString stringWithFormat:@"%@.3.1004",[TKCommonUtil fetchAppStatisticsMarker]]];
    }
}

- (void)handleEnterForegroundNotification:(NSNotification*)notifi{
    
    //    if (captureManager && tCView) {
    ////        CGSize sSize = [UIScreen mainScreen].bounds.size;
    ////        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
    ////        tCView.transform = transform;
    ////        tCView.frame =CGRectMake(0, 0, sSize.height, sSize.width);
    //
    //        [captureManager reTakePicture];
    //    }
    
}

#pragma 初始化拍照界面
- (void)initTakeCardViewNew{
    self.isTakeClick=NO;
    int h, w;
    
    w = self.view.TKWidth;
    
    h  = self.view.TKHeight;
    
    CGRect tFrame = CGRectMake(0, 0, w, h);
    
    tCView = [[UIView alloc] initWithFrame:tFrame];
    
    float areaX=30;//卡片指定区域X坐标
    //这里因为是竖屏布局，所以宽高比反着来
    float areaWidth=self.view.TKWidth-areaX*2;//卡片指定区域宽度
    float areaHeight=areaWidth/TKCARD_AspectRatio;//卡片指定区域高度,银行卡高/宽=0.63060748
    float areaY=(self.view.TKHeight-areaHeight)/2;//卡片指定区域Y坐标
    float viewAlpha=0.5;//遮罩透明度
    self.idRect=CGRectMake(areaX, areaY, areaWidth, areaHeight);
    
    self.idAreaView=[[UIImageView alloc] initWithFrame:self.idRect];
    UIImage *idAreaImg;
    if (isUploadPositiveCard) {
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_card_frame_new_portrait_new_front.png", TK_OPEN_RESOURCE_NAME]];
    }else{
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_card_frame_new_portrait_new_reverse.png", TK_OPEN_RESOURCE_NAME]];
    }
    if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
        idAreaImg = [idAreaImg imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [self.idAreaView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
    }
    [self.idAreaView setImage:idAreaImg];
    
    self.topView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.TKWidth, areaY)];
    self.topView.backgroundColor=[UIColor blackColor];
    self.topView.alpha=viewAlpha;
    
    self.leftView=[[UIView alloc] initWithFrame:CGRectMake(0, areaY, areaX, areaHeight)];
    self.leftView.backgroundColor=[UIColor blackColor];
    self.leftView.alpha=viewAlpha;
    
    self.rightView=[[UIView alloc] initWithFrame:CGRectMake(areaWidth+areaX, areaY, areaX, areaHeight)];
    self.rightView.backgroundColor=[UIColor blackColor];
    self.rightView.alpha=viewAlpha;
    
    self.bottomView=[[UIView alloc] initWithFrame:CGRectMake(0, areaHeight+areaY, self.view.TKWidth, self.view.TKHeight-areaY-areaHeight)];
    self.bottomView.backgroundColor=[UIColor blackColor];
    self.bottomView.alpha=viewAlpha;
    
    [tCView addSubview:self.idAreaView];
    [tCView addSubview:self.topView];
    [tCView addSubview:self.leftView];
    [tCView addSubview:self.rightView];
    [tCView addSubview:self.bottomView];
    
    CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
    
//    UIImageView *cardLogoIV = [[UIImageView alloc] init];
    
    //提示lable
    UILabel *uLable =[[UILabel alloc] init];
    
    uLable.textColor = [UIColor whiteColor];
    NSString *tipStr;
    NSRange tipRange;
    
    if (isUploadPositiveCard) {
        tipStr= @"将线框对准 身份证人像面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"身份证人像面"];
    }else{
        tipStr = @"将线框对准 身份证国徽面 再点击拍照";
        tipRange=[tipStr rangeOfString:@"身份证国徽面"];
    }
    
    //顶部提示用富文本
    NSMutableAttributedString *tipAttribut=[[NSMutableAttributedString alloc] initWithString:tipStr];
    
    // 2.添加属性
    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        [tipAttribut addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:20.0f] range:NSMakeRange(0, tipStr.length)];
    }else{
        [tipAttribut addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:18.0f] range:NSMakeRange(0, tipStr.length)];
    }
    
    uLable.attributedText=tipAttribut;
    
    uLable.textAlignment = NSTextAlignmentCenter;
    
    CGSize uLableSize = [uLable sizeThatFits:CGSizeMake(areaHeight, areaX)];
    uLable.frame=CGRectMake(0, 0, uLableSize.width, uLableSize.height);
    
    uLable.transform = transform;
    
    [uLable setFrameX:(tCView.TKWidth-6-uLableSize.height)];
    [uLable setFrameY:((tCView.TKHeight-uLableSize.width)/2)];
    [tCView addSubview:uLable];
    
//    UILabel *dLable =[[UILabel alloc] init];
//
//    dLable.font = [UIFont systemFontOfSize:15.0f];
//
//    dLable.textColor = [UIColor whiteColor];
//
//    if (self.param[@"bottomTips"]) {
//        dLable.text = self.param[@"bottomTips"];
//    } else {
//        dLable.text = @"请保持身份证四边与边框对齐，文字清晰";
//    }
//
//    dLable.textAlignment = NSTextAlignmentCenter;
//
//    CGSize dLableSize = [dLable sizeThatFits:CGSizeMake(areaHeight, areaX)];
//    dLable.frame=CGRectMake(-(dLableSize.width-areaX)/2, (self.view.frame.size.height-areaX)/2, dLableSize.width, dLableSize.height);
//
//    dLable.transform = transform;
//
//    [tCView addSubview:dLable];
    
    //拍照按钮
    UIButton *tpBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    tpBtn.tag = 101;
    
    [tpBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
    
    [tpBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_selected_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateHighlighted];
    
    float tpBtnWidth=62;
    tpBtn.frame = CGRectMake((w - tpBtnWidth)/2, h-(self.idRect.origin.y-tpBtnWidth)/2-tpBtnWidth, tpBtnWidth, tpBtnWidth);
    
    [tpBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [tpBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [tpBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [tCView addSubview:tpBtn];
    
    
    //相册按钮
    UIButton *aBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    aBtn.tag = 100;
    
    
    [aBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/album_btn_icon.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
    [aBtn setImageEdgeInsets:UIEdgeInsetsMake(11, 12, 11, 12)];
    
    //    [aBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/album_selected_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateHighlighted];
    
    float aBtnWidth=48;
    aBtn.frame = CGRectMake(20+areaX+6, (areaY-aBtnWidth)/2, aBtnWidth, aBtnWidth);
    aBtn.layer.cornerRadius=aBtn.TKWidth/2;
    [aBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f]];
    
    [aBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [aBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [aBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [tCView addSubview:aBtn];
    
    //相册按钮的文字提示
    UILabel *aLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, 28, 20)];;
    aLabel.text = @"相册";
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        [aLabel setTKHeight:28];
        [aLabel setTKWidth:40];
        aLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
    }else{
        aLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
    }
    
    aLabel.textColor=[UIColor whiteColor];
    aLabel.transform=transform;
    [aLabel setFrameX:self.idRect.origin.x];
    [aLabel setFrameY:((self.idRect.origin.y-aLabel.TKHeight)/2)];
    [tCView addSubview:aLabel];
    
    if (_param[@"isAlbum"] && [_param[@"isAlbum"] integerValue] == 0) {
        [aLabel setHidden:YES];
        [aBtn setHidden:YES];
    }else{
        
        [aLabel setHidden:NO];
        [aBtn setHidden:NO];
    }
    
    
    //返回按钮
    UIButton *bBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    bBtn.tag = 102;
    
    [bBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/back_btn_icon.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
    
    
    //    [bBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/back_selected_btn_new_p.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateHighlighted];
    [bBtn setImageEdgeInsets:UIEdgeInsetsMake(14, 12, 19, 12)];
    
    float bBtnWidth=48;
    bBtn.frame = CGRectMake(w-areaX-bBtnWidth, (areaY-bBtnWidth)/2, bBtnWidth, bBtnWidth);
    bBtn.layer.cornerRadius=bBtn.frame.size.width/2;
    [bBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f]];
    
    [bBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [bBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [bBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [tCView addSubview:bBtn];
    
    //返回按钮的文字提示
    UILabel *bLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, 28, 20)];
    bLabel.text = @"返回";
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        [bLabel setTKHeight:28];
        [bLabel setTKWidth:40];
        bLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
    }else{
        bLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
    }
    
    bLabel.textColor=[UIColor whiteColor];
    bLabel.transform=transform;
    [bLabel setFrameX:(tCView.TKWidth-self.idRect.origin.x-bBtnWidth-6-bLabel.TKWidth)];
    [bLabel setFrameY:((self.idRect.origin.y-bLabel.TKHeight)/2)];
    [tCView addSubview:bLabel];
    
    
    captureManager = [[TKAVCaptureManager alloc] initWithPreviewView:tCView withCameraPosition:AVCaptureDevicePositionBack withCamraOrientation:AVCaptureVideoOrientationPortrait handleBusinessType:TK_VIDEO_TAKE_PHOTO];
    
    
    [captureManager reTakePicture];
    
    
    [UIView transitionWithView:self.view duration:0.3 options:UIViewAnimationOptionTransitionFlipFromTop animations:^{
        
        [self.view addSubview:tCView];
        
    } completion:^(BOOL finished) {
        if(_preview){
            [_preview removeFromSuperview];
            _preview=nil;
        }
        
        if (pCView) {
            
            [pCView removeFromSuperview];
            
            pCView = nil;
        }


    }];
    
    if (isUploadPositiveCard) {
        [TKTraffic visitPage:[NSString stringWithFormat:@"%@.3.1003",[TKCommonUtil fetchAppStatisticsMarker]]];
    }else{
        [TKTraffic visitPage:[NSString stringWithFormat:@"%@.3.1005",[TKCommonUtil fetchAppStatisticsMarker]]];
    }
}

#pragma mark -初始化证件预览界面
- (void)initPreviewCardViewNew:(UIImage*)cImage{
    
    int h, w;
    
    self.view.backgroundColor = [TKUIHelper colorWithHexString:@"#232323"];
    
    w =UISCREEN_WIDTH;
    h =UISCREEN_HEIGHT;
    
    CGRect fFrame = CGRectMake(0, 0, w, h);
    
    flag = NO;
    
    pCView = [[UIView alloc] initWithFrame:fFrame];
    
    
    if (cImage) {
        if (!isPhotoAlbum) {//裁剪拍照获得的证件照
            float imgHeigt;
            float imgWidth;
            if (cImage.size.width>cImage.size.height) {
                imgWidth=cImage.size.width;
                imgHeigt=cImage.size.height;
            }else{
                imgWidth=cImage.size.height;
                imgHeigt=cImage.size.width;
            }
            CGRect changRect= [self getCuttingArea:CGSizeMake(imgWidth, imgHeigt)];
            //扩大裁剪区域容错
            changRect=CGRectMake(changRect.origin.x-60, changRect.origin.y-60, changRect.size.width+120, changRect.size.height+120);
            cImage=[UIImage imageWithCGImage:CGImageCreateWithImageInRect(cImage.CGImage,changRect)];
        }
        uImage = cImage;

        [pCView addSubview:self.preview];
        [self.preview previewCardView:uImage isPhotoAlbum:isPhotoAlbum];
    }

    pCView.frame = CGRectOffset(fFrame, 0, -fFrame.size.height);
    [UIView animateWithDuration:0.3f delay:0.0f options:UIViewAnimationOptionCurveEaseInOut animations:^{
        
        [self.view addSubview:pCView];
        
        pCView.center = CGPointMake(pCView.center.x , pCView.center.y + fFrame.size.height);
        
        
    } completion:^(BOOL finished) {
        
        if (tCView) {
            
            [tCView removeFromSuperview];
            
            tCView = nil;
            
        }
        
    }];
}


#pragma mark -初始化证件预览界面带有示例模块
- (void)initPreviewCardNeedSampleViewNew:(UIImage*)cImage{
        
        int h, w;
        
        self.view.backgroundColor = [TKUIHelper colorWithHexString:@"#232323"];
        
        w =UISCREEN_WIDTH;

    
        h =UISCREEN_HEIGHT;
        
        CGRect fFrame = CGRectMake(0, 0, w, h);
        
        flag = NO;
        
        pCView = [[UIView alloc] initWithFrame:fFrame];
        
        
        if (cImage) {
            if (!isPhotoAlbum) {//裁剪拍照获得的证件照
                float imgHeigt;
                float imgWidth;
                if (cImage.size.width>cImage.size.height) {
                    imgWidth=cImage.size.width;
                    imgHeigt=cImage.size.height;
                }else{
                    imgWidth=cImage.size.height;
                    imgHeigt=cImage.size.width;
                }
                CGRect changRect= [self getCuttingArea:CGSizeMake(imgWidth, imgHeigt)];
                //扩大裁剪区域容错
                changRect=CGRectMake(changRect.origin.x-60, changRect.origin.y-60, changRect.size.width+120, changRect.size.height+120);
                cImage=[UIImage imageWithCGImage:CGImageCreateWithImageInRect(cImage.CGImage,changRect)];
            }
            uImage = cImage;
            
            [pCView addSubview:self.preview];
            [self.preview previewCardNeedSampleView:uImage isPhotoAlbum:isPhotoAlbum isFrontCard:isUploadPositiveCard];
            
        }
        
        
        pCView.frame = CGRectOffset(fFrame, 0, -fFrame.size.height);
        [UIView animateWithDuration:0.3f delay:0.0f options:UIViewAnimationOptionCurveEaseInOut animations:^{
            
            [self.view addSubview:pCView];
            
            pCView.center = CGPointMake(pCView.center.x , pCView.center.y + fFrame.size.height);
            
            
        } completion:^(BOOL finished) {
            
            if (tCView) {
                
                [tCView removeFromSuperview];
                
                tCView = nil;
                
            }
            
        }];
        
}

/**
 *  <AUTHOR>
 *  获得照片裁剪中心区域矩阵
 *  照片是横的，然后布局坐标是竖屏的。这里要转换一下以横屏来计算坐标
 */
-(CGRect)getCuttingArea:(CGSize)imgSize{
    CGRect rect;
    
    //等比计算一个中间银行卡大小矩形
    
    float areaHeigth=self.idRect.size.width*imgSize.height/self.windoRect.size.width;
    float areaY=(imgSize.height-areaHeigth)/2;
    
    float areaWidth=self.idRect.size.height*imgSize.width/self.windoRect.size.height;
    float areaX=(imgSize.width-areaWidth)/2;
    
    rect=CGRectMake(areaX, areaY, areaWidth, areaHeigth);
    
    return rect;
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

- (IBAction)btnOnClicked:(id)sender {
    
    UIButton *btn = (UIButton*)sender;
    
    if (btn.tag == 100) {//打开相册
        
        NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
        bpDic[@"eventType"]=@"2";
        if (isUploadPositiveCard) {
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1013",[TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.204",[TKCommonUtil fetchAppStatisticsMarker]];
        }else{
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1019",[TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.210",[TKCommonUtil fetchAppStatisticsMarker]];
        }
        [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50405" param:bpDic moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
        
        
        [self tkIsPhotoLibraryPermissions:^{
            [self openAlbum];
        }];
        
    }else if(btn.tag == 101){//拍证件照
        //防止连续点击拍照
        if (self.isTakeClick) {
            return;
        }
        self.isTakeClick=YES;
        
        NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
        bpDic[@"eventType"]=@"2";
        if (isUploadPositiveCard) {
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1014", [TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.205",[TKCommonUtil fetchAppStatisticsMarker]];
        }else{
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1020",[TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.211",[TKCommonUtil fetchAppStatisticsMarker]];
        }
        [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50405" param:bpDic moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
        
        if (captureManager) {
            
            [captureManager takePicture:^(id hResult) {
                
                isPhotoAlbum = NO;
                
                uImage = hResult;
                if (uImage) {
                    [self getImgGoPreview:uImage];
                }

                
            }];
        }
        
         [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Take.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Take.actionId",@"open"]] attributes:self.param[@"extParams"]];
        
    }else if(btn.tag == 102){//返回
        
        NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
        bpDic[@"eventType"]=@"2";
        if (isUploadPositiveCard) {
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1012",[TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.203", [TKCommonUtil fetchAppStatisticsMarker]];
        }else{
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1018",[TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.209",[TKCommonUtil fetchAppStatisticsMarker]];
        }
        [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50405" param:bpDic moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
        //处理通达信里面会被横屏问题
        NSNumber *orientationTarget = [NSNumber numberWithInt:UIInterfaceOrientationPortrait];
        [[UIDevice currentDevice] setValue:orientationTarget forKey:@"orientation"];
        
        [self dismissViewControllerAnimated:YES completion:^{
            NSMutableDictionary *jsParam=[[NSMutableDictionary alloc] init];
            jsParam[@"funcNo"]=@"60050";
            jsParam[@"error_no"]=@"-1";
    
            if (self.delegate&&[self.delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                [self.delegate tkIDCardDidComplete:jsParam];
            }else{
                [mService iosCallJsWithDic:jsParam callBackFunc:^(ResultVo *resultVo) {}];
            }
        }];
        
    }else if(btn.tag == 103){//重拍
        
        NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
        bpDic[@"eventType"]=@"2";
        if (isUploadPositiveCard) {
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1016",[TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.207",[TKCommonUtil fetchAppStatisticsMarker]];
        }else{
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1022",[TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.213",[TKCommonUtil fetchAppStatisticsMarker]];
        }
        [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50405" param:bpDic moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
        
        [self initTakeCardViewNew];
        
    }else if(btn.tag == 104){
        
        NSMutableDictionary *bpDic = [[NSMutableDictionary alloc] initWithCapacity:3];
        bpDic[@"eventType"]=@"2";
        if (isUploadPositiveCard) {
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1017", [TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.208",[TKCommonUtil fetchAppStatisticsMarker]];
        }else{
            bpDic[@"objectId"]=[NSString stringWithFormat:@"%@.3.1023",[TKCommonUtil fetchAppStatisticsMarker]];
            bpDic[@"actionId"]=[NSString stringWithFormat:@"%@.3.214",[TKCommonUtil fetchAppStatisticsMarker]];
        }
        [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50405" param:bpDic moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
        
        [self affirmAction];
    }
}


//跳转预览页或者直接结果给h5
-(void)getImgGoPreview:(UIImage *)img{
    if ([self.param[@"isNeedOcrAffirmView"] isEqualToString:@"0"]) {
        [self dismissViewControllerAnimated:YES completion:^{
            [self affirmAction];
        }];
    }else{
        if ([self.param[@"isNeedSample"] isEqualToString:@"1"]) {
            [self initPreviewCardNeedSampleViewNew:img];
        }else{
            [self initPreviewCardViewNew:img];
        }
    }
}

/**
 <AUTHOR> 2019年06月28日19:38:06
 @照片确认上传事件
 */
-(void)affirmAction{
    //        isPhotoAlbum  = NO;
    if ([_param[@"isNeedWatermark"] isEqualToString:@"1"]) {
        NSString *logoName=_param[@"watermarkImgName"]?_param[@"watermarkImgName"]:@"sd_logo_new.png";
        uImage = [self imageWithTransImage:uImage addtransparentImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/%@", TK_OPEN_RESOURCE_NAME,logoName]]];
    }
    
    
    NSData* data =[TKImageHelper compressImageData:uImage toByte:(_param[@"compressSize"]? [_param[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];

    
    if (_param[@"isUpload"] && ![_param[@"isUpload"] integerValue]) {//提交证件图给H5上传
        
        [self showImage:data];
        
    }else{
        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
        
        mHUD = [[TKMBProgressHUD alloc] initWithView:self.view];
        mHUD.backgroundColor=[UIColor clearColor];
        
        
        [self.view addSubview:mHUD];
        
        mHUD.dimBackground = YES;
        
        mHUD.labelText = @"上传证件中...";
        
        mHUD.mode = TKMBProgressHUDModeIndeterminate;
        
        mHUD.delegate = self;
        
        [mHUD show:YES];
        mHUD.transform = transform;
        
        dispatch_async(dispatch_get_global_queue(0, 0), ^{
            
            
            
            [self upLoadCardImage:data];
        });
    }
}

- (void)showImage:(NSData *)imageData
{
    NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:imageData];
    NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
    hStep ++;
    if (isUploadPositiveCard) {
        
        TKLogInfo(@"身份证正面上传成功");
        
        
        reqParam[@"funcNo"]=@"60050";
        reqParam[@"error_no"]=@"0";
        reqParam[@"frontBase64"]=base64;

        

        if (tImageType.count>hStep) {
            //继续识别另一面
            isUploadPositiveCard=!isUploadPositiveCard;
            [self initTakeCardViewNew];
            
            if (_param[@"isNeedFullResults"] && ![_param[@"isNeedFullResults"] integerValue]) {
                if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                    
                    [_delegate tkIDCardDidComplete:reqParam];
                }else{
                    [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
                }
            }
        }else{
            //处理通达信里面会被横屏问题
            NSNumber *orientationTarget = [NSNumber numberWithInt:UIInterfaceOrientationPortrait];
            [[UIDevice currentDevice] setValue:orientationTarget forKey:@"orientation"];
            //识别结束
            [self dismissViewControllerAnimated:YES completion:^{
                if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                    
                    [_delegate tkIDCardDidComplete:reqParam];
                }else{
                    [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
                }
            }];
            
        }
        
    }else{
        
        TKLogInfo(@"身份证反面上传成功");
        
        reqParam[@"funcNo"]=@"60050" ;
        reqParam[@"error_no"]=@"0" ;
        reqParam[@"backBase64"]=base64;

        
        if (tImageType.count>hStep) {
            //继续识别另一面
            isUploadPositiveCard=!isUploadPositiveCard;
            [self initTakeCardViewNew];
            
            if (_param[@"isNeedFullResults"] && ![_param[@"isNeedFullResults"] integerValue]) {
                reqParam[@"frontBase64"]=nil;
                if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                    
                    [_delegate tkIDCardDidComplete:reqParam];
                }else{
                    [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
                }
            }
        }else{
            //处理通达信里面会被横屏问题
            NSNumber *orientationTarget = [NSNumber numberWithInt:UIInterfaceOrientationPortrait];
            [[UIDevice currentDevice] setValue:orientationTarget forKey:@"orientation"];
            //识别结束
            [self dismissViewControllerAnimated:YES completion:^{
                if (_param[@"isNeedFullResults"] && ![_param[@"isNeedFullResults"] integerValue]) {
                    reqParam[@"frontBase64"]=nil;
                }
                
                if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                    
                    [_delegate tkIDCardDidComplete:reqParam];
                }else{
                    [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
                }
            }];
            
        }
    }
}

#pragma mark －打开相册
- (void)openAlbum{
    
    [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Album.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Album.actionId",@"open"]] attributes:self.param[@"extParams"]];
    
    UIImagePickerController* cameraPicker = [[UIImagePickerController alloc] init];
    
    cameraPicker.delegate = self;
    
    cameraPicker.allowsEditing = NO;
    
    cameraPicker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    
    NSArray* mediaArray = [NSArray arrayWithObjects:(NSString*)kUTTypeImage, nil];
    
    cameraPicker.mediaTypes = mediaArray;
    cameraPicker.modalPresentationStyle=UIModalPresentationFullScreen;
    [self presentViewController:cameraPicker animated:YES completion:^{
        
        if (captureManager) {
            
            [captureManager cancelTakePicture];
        }
        
    }];
    
}

#pragma mark - implement UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info
{
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    NSString* mediaType = [info objectForKey:UIImagePickerControllerMediaType];
    
    if ([mediaType isEqualToString:(NSString*)kUTTypeImage])
    {
        // get the image
        UIImage* image = [info objectForKey:UIImagePickerControllerOriginalImage];
        
        if (picker.allowsEditing && [info objectForKey:UIImagePickerControllerEditedImage]) {// 可编辑的图片
            image = [info objectForKey:UIImagePickerControllerEditedImage];
        }
        
        isPhotoAlbum = YES;
        
        uImage = image;
        [self getImgGoPreview:uImage];

        
    }
}

//取消照相机的回调
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    if (captureManager) {
        
        [captureManager reTakePicture];
        
    }
    
    TKLogInfo(@"取消照相机的回调");
}


#pragma mark - implement ASIProgressDelegate
- (void)setProgress:(float)newProgress
{
    TKLogInfo(@"progress:%f",newProgress);
    
    if (mHUD) {
        
        mHUD.progress = newProgress;
        
        if (mHUD.progress == 1.0 && !flag) {
            
            flag = YES;
            
            UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/37x-Checkmark.png", TK_OPEN_RESOURCE_NAME]];
            UIImageView *imageView = [[UIImageView alloc] initWithImage:image];
            mHUD.customView = imageView;
            mHUD.mode = TKMBProgressHUDModeCustomView;
            mHUD.labelText = @"证件上传成功";
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                
                if (flag) {
                    
                    mHUD.mode = TKMBProgressHUDModeIndeterminate;
                    mHUD.labelText = @"证件识别中...";
                }
            });
        }
    }
}

#pragma mark -上传证件图片
- (void)upLoadCardImage:(NSData *)imageData
{
    
    NSDate *now = [NSDate date];
    NSInteger tempInterval = [now timeIntervalSince1970];
    
    NSString *hostUrl;
    
    if ([_param[@"url"] rangeOfString:@"?"].length > 0) {
        
        hostUrl = _param[@"url"];
        
    }else {
        
        hostUrl = [NSString stringWithFormat:@"%@?", _param[@"url"]];
        
    }
    
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    
    [tkReqParam addEntriesFromDictionary:_param];
    
    if (_param[@"requestParam"]) {
        
        NSArray *rpArr = [_param[@"requestParam"] componentsSeparatedByString:@"&"];
        
        for (NSString *str in rpArr) {
            
            NSArray *tArr = [str componentsSeparatedByString:@"="];
            
            if ([tArr count] > 0) {
                
                if ([[tArr objectAtIndex:0] isEqualToString:@"is_ocr"]) {
                    tkReqParam[[tArr objectAtIndex:0]]=@"1";
                }else{
                    tkReqParam[[tArr objectAtIndex:0]]=[tArr objectAtIndex:1];
                }
                
            }
        }
        
        [tkReqParam removeObjectForKey:@"requestParam"];
    }
    
    if (_param[@"serverAccessType"] && [_param[@"serverAccessType"] integerValue] == 1) {
        tkReqParam[_param[@"fileUploadKey"]?_param[@"fileUploadKey"]:@"file_data"]=[TKBase64Helper stringWithEncodeBase64Data:imageData];
    }else{
        tkReqParam[_param[@"fileUploadKey"]?[NSString stringWithFormat:@"%@@@F",_param[@"fileUploadKey"]]:@"file_data@@F"]=imageData;
    }
    
    tkReqParam[@"image_type"]=isUploadPositiveCard ? @"idfrontimg" : @"idbackimg";
    
    [mService uploadFileWithURL:hostUrl param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:imageData];
            NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
            if (isUploadPositiveCard) {
                reqParam[@"frontBase64"]=base64;
            }else{
                reqParam[@"backBase64"]=base64;
            }
            
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            [self uploadFinished:dic];
            
        }else if(resultVo.errorNo == -999){
            [mHUD hide:YES];
            //未登录情况特殊处理,iOS8以后用
            UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:resultVo.errorInfo?resultVo.errorInfo:@"未登录" preferredStyle: UIAlertControllerStyleAlert];

            UIAlertAction *okAction = [UIAlertAction actionWithTitle: @"确定" style: UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                
                reqParam[@"funcNo"]=@"60050";
                reqParam[@"moduleName"]=_param[@"moduleName"];
                reqParam[@"tkuuid"]=_param[@"tkuuid"];
                reqParam[@"error_no"]=@"-999";
                reqParam[@"error_info"]=resultVo.errorInfo;
                
                if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                                       
                    [_delegate tkIDCardDidComplete:reqParam];
                }
                //处理通达信里面会被横屏问题
                NSNumber *orientationTarget = [NSNumber numberWithInt:UIInterfaceOrientationPortrait];
                [[UIDevice currentDevice] setValue:orientationTarget forKey:@"orientation"];
                [self dismissViewControllerAnimated:YES completion:^{}];
            }];

            [alertController addAction: okAction];
            [self presentViewController:alertController animated:YES completion:nil];
        }else
        {
            TKLogInfo(@"%@",resultVo.errorInfo);
            
            [self uploadFailed:resultVo.errorInfo];
            
        }
        
        if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_UTime.objectId", @"open"]]) {
            NSDate *n = [NSDate date];
            NSTimeInterval diff = [n timeIntervalSince1970] - tempInterval;
            NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:5];
            dic[@"i_ocr_time"]=[NSNumber numberWithInteger:diff];
            dic[@"i_pic_from"]=[_param[kParam_action] isEqualToString:@"phone"]? @"2":@"1";
            NSString *tStr = @"3";
            if ([_param[@"imgType"] integerValue] == 3) {
                tStr = @"4";
            }else if([_param[@"imgType"] integerValue] == 4){
                tStr = @"1";
            }else if([_param[@"imgType"] integerValue] == 5){
                tStr = @"2";
            }
            id extParams = _param[@"extParams"];
            if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
                [dic addEntriesFromDictionary:extParams];
            }
            dic[@"i_pic_type"]=tStr;
            dic[@"i_recognition_state"]=resultVo.errorNo == 0? @"1":@"0";
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_UTime.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_UTime.actionId",@"open"]] attributes:dic];
        }
        
        if ([TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Upload.objectId",@"open"]]) {
            NSDate *n = [NSDate date];
            NSTimeInterval diff = [n timeIntervalSince1970] - tempInterval;
            NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:5];
            dic[@"i_upload_time"]=[NSNumber numberWithInteger:diff];
            dic[@"i_pic_from"]=[_param[kParam_action] isEqualToString:@"phone"]? @"2":@"1";
            NSString *tStr = @"3";
            if ([_param[@"imgType"] integerValue] == 3) {
                tStr = @"4";
            }else if([_param[@"imgType"] integerValue] == 4){
                tStr = @"1";
            }else if([_param[@"imgType"] integerValue] == 5){
                tStr = @"2";
            }
            id extParams = _param[@"extParams"];
            if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
                [dic addEntriesFromDictionary:extParams];
            }
            dic[@"i_pic_type"]=tStr;
            dic[@"i_upload_state"]=resultVo.errorNo == 0? @"1":@"0";
            [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Upload.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_UCImg3.0_Upload.actionId",@"open"]] attributes:dic];
        }
    }];
    
}

#pragma mark -上传成功
- (void)uploadFinished:(NSDictionary*)source
{
    if (source && [source[@"error_no"] integerValue] == 0) {
        
        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/37x-Checkmark.png", TK_OPEN_RESOURCE_NAME]];
        UIImageView *imageView = [[UIImageView alloc] initWithImage:image];
        mHUD.customView = imageView;
        mHUD.mode = TKMBProgressHUDModeCustomView;
        mHUD.labelText = @"识别成功";
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            [mHUD hide:YES];
            hStep ++;
            if (isUploadPositiveCard) {
                TKLogInfo(@"身份证正面上传成功");
                isUploadPositiveCard = NO;
                reqParam[@"funcNo"]=@"60050";
                reqParam[@"error_no"]=@"0";
                reqParam[@"idNo"]=source[@"idno"];
                reqParam[@"usersex"]=source[@"usersex"]?source[@"usersex"]:source[@"sex"];
                reqParam[@"custName"]=source[@"custname"];
                reqParam[@"native"]=source[@"native"];
                reqParam[@"ethnicName"]=source[@"ethnicname"];
                reqParam[@"birthday"]=source[@"birthday"];
                reqParam[@"frontFilePath"]=source[@"filepath"];
                reqParam[@"frontSecret"]=source[@"secret"];
                
                if (tImageType && [tImageType count] > hStep) {
                    
                    if ([[tImageType objectAtIndex:hStep] isEqualToString:@"5"]) {//拍摄身份证反面
                        
                        isUploadPositiveCard = NO;
                        
                        [self initTakeCardViewNew];
                        
                        if (_param[@"isNeedFullResults"] && ![_param[@"isNeedFullResults"] integerValue]) {
                            if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                                
                                [_delegate tkIDCardDidComplete:reqParam];
                            }else{
                                [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
                            }
                        }
                        
                    }else if ([[tImageType objectAtIndex:hStep] isEqualToString:@"3"]) {//拍摄大头照
                        
                        MTakeBigPictureViewController *tBCtl = [[MTakeBigPictureViewController alloc] init];
                        
                        tBCtl.param = _param;
                        
                        tBCtl.delegate = self;
                        
                        [self presentViewController:tBCtl animated:NO completion:^{}];
                        
                    }
                    
                }else{
                    

                    //处理通达信里面会被横屏问题
                    NSNumber *orientationTarget = [NSNumber numberWithInt:UIInterfaceOrientationPortrait];
                    [[UIDevice currentDevice] setValue:orientationTarget forKey:@"orientation"];
                    [self dismissViewControllerAnimated:YES completion:^{
                        if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                            
                            [_delegate tkIDCardDidComplete:reqParam];
                        }else{
                            [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
                        }
                    }];
                    
                }
                
            }else{
                
                TKLogInfo(@"身份证反面上传成功");
                reqParam[@"funcNo"]=@"60050";
                reqParam[@"error_no"]=@"0";
                reqParam[@"policeOrg"]=source[@"policeorg"];
                reqParam[@"idbeginDate"]=source[@"idbegindate"];
                reqParam[@"idendDate"]=source[@"idenddate"];
                reqParam[@"backFilePath"]=source[@"filepath"];
                reqParam[@"backSecret"]=source[@"secret"];
                
                if (tImageType && [tImageType count] > hStep) {
                    
                    if ([[tImageType objectAtIndex:hStep] isEqualToString:@"4"]) {//拍摄身份正面
                        
                        isUploadPositiveCard = YES;
                        
                        [self initTakeCardViewNew];
                        if (_param[@"isNeedFullResults"] && ![_param[@"isNeedFullResults"] integerValue]) {
                            //正反面分次回调情况下，回调反面时候清理正面信息避免重复数据
                            reqParam[@"frontBase64"]=nil;
                            reqParam[@"idNo"]=nil;
                            reqParam[@"usersex"]=nil;
                            reqParam[@"custName"]=nil;
                            reqParam[@"native"]=nil;
                            reqParam[@"ethnicName"]=nil;
                            reqParam[@"birthday"]=nil;
                            reqParam[@"frontFilePath"]=nil;
                            reqParam[@"frontSecret"]=nil;
                            if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                                
                                [_delegate tkIDCardDidComplete:reqParam];
                            }else{
                                [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
                            }
                        }
                        

                    }else if ([[tImageType objectAtIndex:hStep] isEqualToString:@"3"]) {//拍摄大头照
                        
                        MTakeBigPictureViewController *tBCtl = [[MTakeBigPictureViewController alloc] init];
                        
                        tBCtl.param = _param;
                        
                        tBCtl.delegate = self;
                        
                        [self presentViewController:tBCtl animated:NO completion:^{}];
                        
                    }
                    
                }else{
                    

                    
 
                    //处理通达信里面会被横屏问题
                    NSNumber *orientationTarget = [NSNumber numberWithInt:UIInterfaceOrientationPortrait];
                    [[UIDevice currentDevice] setValue:orientationTarget forKey:@"orientation"];
                    [self dismissViewControllerAnimated:YES completion:^{
                        if (_param[@"isNeedFullResults"] && ![_param[@"isNeedFullResults"] integerValue]) {
                            //正反面分次回调情况下，回调反面时候清理正面信息避免重复数据
                            reqParam[@"frontBase64"]=nil;
                            reqParam[@"idNo"]=nil;
                            reqParam[@"usersex"]=nil;
                            reqParam[@"custName"]=nil;
                            reqParam[@"native"]=nil;
                            reqParam[@"ethnicName"]=nil;
                            reqParam[@"birthday"]=nil;
                            reqParam[@"frontFilePath"]=nil;
                            reqParam[@"frontSecret"]=nil;
                        }
                        
                        if (_delegate && [_delegate respondsToSelector:@selector(tkIDCardDidComplete:)]) {
                            
                            [_delegate tkIDCardDidComplete:reqParam];
                        }else{
                            [mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {}];
                        }
                    }];
                    
                }
                
            }
        });
        
    }else{
        
        flag = NO;
        mHUD.mode = TKMBProgressHUDModeText;
        mHUD.labelText = [NSString stringWithFormat:@"%@", source[@"error_info"]];
        mHUD.margin = 10.f;
        mHUD.removeFromSuperViewOnHide = YES;
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            [mHUD hide:YES];
            
            if (pCView) {
                
                [pCView removeFromSuperview];
                
                pCView = nil;
            }
            
            [self initTakeCardViewNew];
        });
        
    }
    
}
#pragma mark -上传失败
- (void)uploadFailed:(NSString *)error
{
    //处理接入层\n被转义的问题，替换会换行符号
    error = [TKStringHelper stringWithReplace:error oldStr:@"\\n" newStr:@"\n"];
    flag = NO;
    mHUD.mode = TKMBProgressHUDModeText;
    mHUD.detailsLabel.text=error ? error:[NSString stringWithFormat:@"%@,请重试", @"网络异常"];
    mHUD.detailsLabel.textAlignment=NSTextAlignmentLeft;
    mHUD.labelText = nil;
    mHUD.margin = 10.f;
    mHUD.removeFromSuperViewOnHide = YES;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        
        [mHUD hide:YES];
        
        [self initTakeCardViewNew];
    });
    
}

#pragma mark -implement MBProgressHUDDelegate
- (void)hudWasHidden:(TKMBProgressHUD *)hud {
    // Remove HUD from screen when the HUD was hidded
    [mHUD removeFromSuperview];
    
    mHUD = nil;
}

#pragma mark TKCardPreviewDelegate
//重拍重选事件
- (void)previewRetryAction:(id)sender{
    
    UIButton *btn = (UIButton*)sender;
    btn.tag=103;
   [self btnOnClicked:sender];
}

//提交事件
- (void)previewSubmitAction:(id)sender{
    UIButton *btn = (UIButton*)sender;
    btn.tag=104;
   [self btnOnClicked:sender];
}

#pragma mark - 加半透明的水印
- (UIImage *)imageWithTransImage:(UIImage *)useImage addtransparentImage:(UIImage *)transparentimg
{
    UIGraphicsBeginImageContext(useImage.size);
    
    [useImage drawInRect:CGRectMake(0, 0, useImage.size.width, useImage.size.height)];
    
    [transparentimg drawInRect:CGRectMake(useImage.size.width - useImage.size.width*0.18 - 10, 10, useImage.size.width*0.18, useImage.size.height*0.26)];
    
    UIImage *resultingImage = UIGraphicsGetImageFromCurrentImageContext();
    
    UIGraphicsEndImageContext();
    
    return resultingImage;
}

#pragma mark 裁剪图片大小
- (UIImage*)imageByCroppingForSize:(UIImage*)anImage toSize:(CGRect)frameSize vFrame:(CGRect)vSize
{
    TKLogInfo(@"w1= %f w= %f,h1= %f h= %f",vSize.size.width,anImage.size.width,vSize.size.height,anImage.size.height);
    
    float sHeight = anImage.size.height < anImage.size.width ? anImage.size.height : anImage.size.width;
    
    CGFloat scale = sHeight/vSize.size.width;
    
    CGImageRef imageRef = CGImageCreateWithImageInRect([anImage CGImage], CGRectMake(frameSize.origin.y*scale, frameSize.origin.x*scale, frameSize.size.height*scale, frameSize.size.width*scale));
    
    UIImage *cropped = [UIImage imageWithCGImage:imageRef];
    
    CGImageRelease(imageRef);
    
    return cropped;
}

#pragma mark- json数据转成NSDictionary
- (id)JSONObject:(NSString*)content
{
    NSError* error = nil;
    
    id object = [NSJSONSerialization JSONObjectWithData:[content dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers error:&error];
    
    if (error != nil) {
        
        return nil;
    }
    
    return object;
}

- (void)takeFinish:(BOOL)result
{

    
}


- (void)dealloc{
    
    [[UIApplication sharedApplication] setStatusBarHidden:NO];
    
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    
    TKLogInfo(@"%@__%s",NSStringFromClass([self class]), __FUNCTION__);
    
    captureManager = nil;
    
    tCView = nil;
    
    pCView = nil;
    
    mService = nil;
    
    uImage = nil;
    
    reqParam = nil;
    
    tImageType = nil;
    
}


#pragma mark lazyloading
/**
 <AUTHOR> 2023年01月13日15:45:05
 @初始化懒加载preview
 @return preview
 */
-(TKCardPreview *)preview{
    if (!_preview) {
        _preview=[[TKCardPreview alloc] initWithFrame:CGRectMake(0, 0, self.view.TKHeight, self.view.TKWidth) requestParams:_param];
        _preview.delegate=self;
        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
        _preview.transform = transform;
        [_preview setTKTop:0];
        [_preview setTKLeft:0];
    }
    return _preview;
}
@end

