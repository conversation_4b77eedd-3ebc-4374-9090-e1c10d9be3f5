//
//  MTakeBigPictureViewController.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/8/4.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol TakeBigPhotoResultDelegate <NSObject>

- (void)takeFinish:(BOOL)result;

@end

/**
 *  Description  拍摄人脸大头照
 */

@interface MTakeBigPictureViewController : TKBaseViewController


@property (nonatomic, assign) id<TakeBigPhotoResultDelegate> delegate;

@property (nonatomic, assign) NSInteger tType;

@property (nonatomic, retain) NSMutableDictionary *param;


- (IBAction)btnOnClicked:(id)sender;

@end
