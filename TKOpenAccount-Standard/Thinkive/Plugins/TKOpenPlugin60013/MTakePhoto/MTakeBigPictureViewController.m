//
//  MTakeBigPictureViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/8/4.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "MTakeBigPictureViewController.h"
#import "TKOpenAccountService.h"
#import "TKAVCaptureManager.h"

#define kParam_uuid       @"uuid"
#define kParam_userId     @"userId"
#define kParam_r          @"r"
#define kParam_imgType    @"imgType"
#define kParam_funcNum    @"funcNum"
#define kParam_photoType  @"photoType"
#define kParam_action     @"action"
#define kParam_url        @"url"
#define kParam_clientInfo @"clientInfo"
#define kParam_jsessionId @"jsessionId"
#define kParam_key        @"key"

@interface MTakeBigPictureViewController ()<TKMBProgressHUDDelegate>
{
    TKAVCaptureManager* captureManager;
    
    BOOL isTakePhoto, flag;
    
    UIButton *reTPBtn, *tpBtn, *upBtn;
    
    UIImage *tPhoto;
    
    TKMBProgressHUD *mHUD;
    
}
@property (nonatomic, strong) TKOpenAccountService *mService;
@end

@implementation MTakeBigPictureViewController

- (void)viewDidLoad {
    
    [super viewDidLoad];
    self.mService=[[TKOpenAccountService alloc] init];
    self.view.backgroundColor = [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.5];
    
    [[UIApplication sharedApplication] setStatusBarHidden:YES];
    
    self.view.frame = CGRectMake(0, 0, self.view.TKWidth, self.view.TKHeight);
    
    _tType = 2;
    
    [self initView];
    
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
}

- (void)initView{
    
    CGSize vSize = self.view.frame.size;
    
    int buttomHeight = 100;
    
    UIView *buttomView = [[UIView alloc] initWithFrame:CGRectMake(0, vSize.height - buttomHeight, vSize.width, buttomHeight)];
    
    [buttomView setBackgroundColor:[UIColor blackColor]];
    
    reTPBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    reTPBtn.tag = 100;
    
    [reTPBtn setTitle:@"取消" forState:UIControlStateNormal];
    
    [reTPBtn setTitleColor:[UIColor greenColor] forState:UIControlStateNormal];
    
    NSInteger btnSpace = (self.view.TKWidth - 300)/4;
    
    reTPBtn.frame = CGRectMake(btnSpace - 10, 20, 100, 60);
    
    [reTPBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [reTPBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [reTPBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [buttomView addSubview:reTPBtn];
    
    tpBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    tpBtn.tag = 101;
    
    [tpBtn setTitle:@"拍照" forState:UIControlStateNormal];
    
    [tpBtn setTitleColor:[UIColor greenColor] forState:UIControlStateNormal];
    
    tpBtn.frame = CGRectMake((self.view.TKWidth - 100)/2, 20, 100, 60);
    
    [tpBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [tpBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [tpBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [buttomView addSubview:tpBtn];
    
    upBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    upBtn.tag = 102;
    
    [upBtn setTitle:@"使用图片" forState:UIControlStateNormal];
    
    [upBtn setHidden:YES];
    
    [upBtn setTitleColor:[UIColor greenColor] forState:UIControlStateNormal];
    
    upBtn.frame = CGRectMake(self.view.TKWidth - 100 - btnSpace, 20, 100, 60);
    
    [upBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    
    [upBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
    
    [upBtn addTarget:self action:@selector(btnOnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [buttomView addSubview:upBtn];
    
    UIView *cView;
    
    if (_tType == 1) {
        
        UIImageView *pIV = [[UIImageView alloc] initWithFrame:CGRectMake(0, 20, self.view.TKWidth, 30)];
        
        [pIV setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/page-camra.PNG", TK_OPEN_RESOURCE_NAME]]];
        
        [self.view addSubview:pIV];
        
        cView = [[UIView alloc] initWithFrame:CGRectMake(0, 50, self.view.TKWidth, self.view.TKHeight - 150)];
        
        UIImageView *fIv = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, self.view.TKWidth, self.view.TKHeight - 150)];
        
        [fIv setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/photo_land.png", TK_OPEN_RESOURCE_NAME]]];
        
        [cView addSubview:fIv];
        
        [self.view addSubview:cView];
        
    }else{
        
        cView = [[UIView alloc] initWithFrame:CGRectMake(0, 20, self.view.TKWidth, self.view.TKHeight - 120)];
        
        UIImageView *fIv = [[UIImageView alloc] initWithFrame:CGRectMake(0, 5, self.view.TKWidth, self.view.TKHeight - 105)];
        
        [fIv setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/photo_face.png", TK_OPEN_RESOURCE_NAME]]];
        
        [cView addSubview:fIv];
        
        UILabel *pLable = [[UILabel alloc] initWithFrame:CGRectMake(0, cView.TKHeight - 50, self.view.TKWidth, 40)];
        
        pLable.backgroundColor = [UIColor clearColor];
        
        pLable.text = @"脸部请拍摄在人像框内";
        
        pLable.textAlignment = NSTextAlignmentCenter;
        
        pLable.font = [UIFont systemFontOfSize:17.0f];
        
        pLable.textColor = [UIColor whiteColor];
        
        [cView addSubview:pLable];
        
        [self.view addSubview:cView];
        
        if (_param[@"isShowFaceGizmos"]&&[_param[@"isShowFaceGizmos"] integerValue]==0) {
            [fIv setHidden:YES];
            [pLable setHidden:YES];
        }
    }
    
    [self.view addSubview:buttomView];
    
    captureManager = [[TKAVCaptureManager alloc] initWithPreviewView:cView withCameraPosition:2 withCamraOrientation:AVCaptureVideoOrientationPortrait handleBusinessType:TK_VIDEO_TAKE_PHOTO];;
    
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

- (IBAction)btnOnClicked:(id)sender {
    
    UIButton *btn = (UIButton*)sender;
    
    if (btn.tag == 100) {
        
        if (isTakePhoto) {
            
            isTakePhoto = NO;
            
            [captureManager reTakePicture];
            
            [reTPBtn setTitle:@"取消" forState:UIControlStateNormal];
            
            [tpBtn setHidden:NO];
            
            [upBtn setHidden:YES];
            
        }else{
            
            [self dismissViewControllerAnimated:YES completion:nil];
            
            if (_delegate && [_delegate respondsToSelector:@selector(takeFinish:)] ) {
                
                [_delegate takeFinish:NO];
            }
        }
        
    }else if (btn.tag == 101){
        
        [captureManager takePicture:^(id hResult) {
            
            tPhoto = hResult;
            
        }];
        
        isTakePhoto = YES;
        
        [reTPBtn setTitle:@"重拍" forState:UIControlStateNormal];
        
        [tpBtn setHidden:YES];
        
        [upBtn setHidden:NO];
        
    }else if (btn.tag == 102){
        NSData* data =[TKImageHelper compressImageData:tPhoto toByte:(_param[@"compressSize"]? [_param[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];

        if (_param[@"isUpload"] && ![_param[@"isUpload"] integerValue]) {//提交大头照给H5上传
            
            [self showImage:data];
            
        }else{
            [self upLoadImage:data];
        }
    }
    
}
- (void)showImage:(NSData *)imageData
{
    [self dismissViewControllerAnimated:YES completion:nil];
    NSString *tempBase64 = [TKBase64Helper stringWithEncodeBase64Data:imageData];
    NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tempBase64];
    
    
    
    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
    reqParam[@"funcNo"]=@"60050";
    reqParam[@"error_no"]=@"0";
    reqParam[@"base64"]=base64;
    reqParam[@"tkuuid"]=_param[@"tkuuid"];
    reqParam[@"extParams"]=_param[@"extParams"];

    [self.mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {
        TKLogInfo(@"----------60050:%ld info:%@", (long)resultVo.errorNo, resultVo.errorInfo);
    }];
    
    
}

#pragma mark - implement ASIProgressDelegate
- (void)setProgress:(float)newProgress
{
    TKLogInfo(@"progress:%f",newProgress);
    
    if (mHUD) {
        
        mHUD.progress = newProgress;
        
        if (mHUD.progress == 1.0 && !flag) {
            
            flag = YES;
            
            UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/37x-Checkmark.png", TK_OPEN_RESOURCE_NAME]];
            UIImageView *imageView = [[UIImageView alloc] initWithImage:image];
            mHUD.customView = imageView;
            mHUD.mode = TKMBProgressHUDModeCustomView;
            mHUD.labelText = @"大头照上传成功";
            
        }
    }
}

#pragma mark -上传证件图片
- (void)upLoadImage:(NSData *)imageData
{
    mHUD = [[TKMBProgressHUD alloc] initWithView:self.view];
    
    [self.view addSubview:mHUD];
    
    mHUD.dimBackground = YES;
    
    mHUD.labelText = @"上传大头照中...";
    
    mHUD.mode = TKMBProgressHUDModeIndeterminate;
    
    mHUD.delegate = self;
    
    [mHUD show:YES];
    
    NSString *hostUrl;
    
    if ([_param[@"url"] rangeOfString:@"?"].length > 0) {
        
        hostUrl = _param[@"url"];
        
    }else {
        
        hostUrl = [NSString stringWithFormat:@"%@?", _param[@"url"]];
        
    }
    
    NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
    [tkReqParam addEntriesFromDictionary:_param];
    tkReqParam[@"image_type"]=@"otherimg";
    
    if (_param[@"requestParam"]) {
        
        NSArray *rpArr = [_param[@"requestParam"] componentsSeparatedByString:@"&"];
        
        for (NSString *str in rpArr) {
            
            NSArray *tArr = [str componentsSeparatedByString:@"="];
            
            if ([tArr count] > 0) {
                
                tkReqParam[[tArr objectAtIndex:0]]=[tArr objectAtIndex:1];
            }
        }
    }
    
    if (_param[@"serverAccessType"] && [_param[@"serverAccessType"] integerValue] == 1) {
        
        tkReqParam[_param[@"fileUploadKey"]?_param[@"fileUploadKey"]:@"file_data"]=[TKBase64Helper stringWithEncodeBase64Data:imageData];
    }else{
        tkReqParam[_param[@"fileUploadKey"]?[NSString stringWithFormat:@"%@@@F",_param[@"fileUploadKey"]]:@"file_data@@F"]=imageData;
    }
    
    [self.mService uploadFileWithURL:hostUrl param:tkReqParam callBackFunc:^(ResultVo *resultVo){
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0 && [results count] > 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            
            [self uploadFinished:dic];
        }else
        {
            TKLogInfo(@"%@",resultVo.errorInfo);
            
            [self uploadFailed:resultVo.errorInfo];
            
        }
    }];
}

#pragma mark -上传成功
- (void)uploadFinished:(NSDictionary*)rDic
{
    
    if (rDic && [rDic[@"error_no"] integerValue] == 0) {//大头照上传成功
        
        [mHUD hide:YES];
        
        [self dismissViewControllerAnimated:YES completion:nil];
        
        NSRange range = [[NSString stringWithFormat:@"%@",_param[@"imgType"]] rangeOfString:@","];
        
        if (range.length  > 0) {
            
            if (_delegate && [_delegate respondsToSelector:@selector(takeFinish:)] ) {
                
                [_delegate takeFinish:YES];
            }
            
        }else{
            NSData* data =[TKImageHelper compressImageData:tPhoto toByte:(_param[@"compressSize"]? [_param[@"compressSize"] integerValue]:IMAGE_COMPRESS_SIZE)*1000];
            NSString *tmpBase64 = [TKBase64Helper stringWithEncodeBase64Data:data];
            
            NSString *base64 = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tmpBase64];
            
            NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
            reqParam[@"funcNo"]=@"60050";
            reqParam[@"base64"]=base64;
            reqParam[@"error_no"]=@"0";
            reqParam[@"filepath"]=rDic[@"filepath"];
            reqParam[@"secret"]=rDic[@"secret"];
            reqParam[@"tkuuid"]=_param[@"tkuuid"];
            reqParam[@"extParams"]=_param[@"extParams"];
            
            [self.mService iosCallJsWithDic:reqParam callBackFunc:^(ResultVo *resultVo) {
                TKLogInfo(@"----------60050:%ld info:%@", (long)resultVo.errorNo, resultVo.errorInfo);
            }];
            
        }
        
    }else{
        
        flag = NO;
        mHUD.mode = TKMBProgressHUDModeText;
        mHUD.labelText = [NSString stringWithFormat:@"%@,请重试", rDic[@"error_info"]];
        mHUD.margin = 10.f;
        mHUD.removeFromSuperViewOnHide = YES;
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            [mHUD hide:YES];
        });
        
    }
    
}

#pragma mark -上传失败
- (void)uploadFailed:(NSString *)error
{
    TKLogInfo(@"error : %@",error);
    
    flag = NO;
    mHUD.mode = TKMBProgressHUDModeText;
    mHUD.labelText = [NSString stringWithFormat:@"%@,请重试", error? error:@"网络异常"];
    mHUD.margin = 10.f;
    mHUD.removeFromSuperViewOnHide = YES;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        
        [mHUD hide:YES];
    });
    
}

#pragma mark -implement MBProgressHUDDelegate
- (void)hudWasHidden:(TKMBProgressHUD *)hud {
    // Remove HUD from screen when the HUD was hidded
    [mHUD removeFromSuperview];
    
    mHUD = nil;
}

#pragma mark- json数据转成NSDictionary
- (id)JSONObject:(NSString*)content
{
    NSError* error = nil;
    
    id object = [NSJSONSerialization JSONObjectWithData:[content dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers error:&error];
    
    if (error != nil) {
        
        return nil;
    }
    
    return object;
}



- (void)dealloc{
    
    [[UIApplication sharedApplication] setStatusBarHidden:NO];
    
}
@end

