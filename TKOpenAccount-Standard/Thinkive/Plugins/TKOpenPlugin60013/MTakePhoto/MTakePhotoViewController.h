//
//  MTakePhotoViewController.h
//  iosOpenP
//
//  Created by <PERSON>lover on 15/7/23.
//
//

#import <UIKit/UIKit.h>

@protocol TKTakePhotoResultDelegate <NSObject>

- (void)tkIDCardDidComplete:(id)tResult;

@end
/**
 *  Description 拍摄身份证正反面（上传服务器进行OCR识别）
 */
@interface MTakePhotoViewController : TKBaseViewController

@property (nonatomic, assign) id<TKTakePhotoResultDelegate> delegate;

@property (nonatomic, assign) NSInteger tType;

@property (nonatomic, retain) NSMutableDictionary *param;

- (IBAction)btnOnClicked:(id)sender;

- (id)initWithParams:(id)mParams nibName:(NSString*)nibNameOrNil bundle:(NSBundle*)nibBundleOrNil;

@end
