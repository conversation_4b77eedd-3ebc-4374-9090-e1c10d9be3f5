//
//  TKSmartVirtualManVideoEndView.m
//  OneWayVideo
//
//  Created by <PERSON>ie on 2019/4/13.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKSmartVirtualManVideoEndView.h"


@interface TKSmartVirtualManVideoEndView ()

//@property (nonatomic, readwrite, strong) UIView *netBadBgView;
//@property (nonatomic, readwrite, strong) UILabel *netLabel;
//@property (nonatomic, readwrite, strong) UIImageView *netErrorImg;

@end

@implementation TKSmartVirtualManVideoEndView
@synthesize titleLabel = _titleLabel;
@synthesize videoShowBgView = _videoShowBgView;

/**
 <AUTHOR>
 @初始化单向视频正常走完流程结果页面
 */
-(void)viewInit{
    [self setBackgroundColor:[TKUIHelper colorWithHexString:@"#D8DCE0"]];

    
    [super viewInit];
 
}


/// 根据tip更新UI
/// @param tipArr tip数组。格式是：@[@{@"tipImage" : @"", @"tipContent" : @""}];
- (void)updateTipViewWithTipArr:(NSArray *)tipArr {
    UIView *waitBgView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, 0)];
    waitBgView.backgroundColor=[TKUIHelper colorWithHexString:@"#ffffff"];
    [self addSubview:waitBgView];
    [self addSubview:self.bigTipLabel];

    
    if (![tipArr isKindOfClass:NSArray.class] || tipArr.count == 0) {
        [self addSubview:self.smallTipLabel];
        return;
    }else{
        [self addSubview:self.smallTipView];
    }

    
    // 先移除
    if (self.smallTipView.subviews.count) {
        [self.smallTipView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    }
    
    NSString *tipImage = nil;
    NSString *tipContent = nil;
    CGFloat x = 10;
    CGFloat y = 0;
    CGFloat width = self.smallTipView.TKWidth-2*x;
    CGFloat height = 16;
    for (int i = 0; i < tipArr.count; i++) {
        NSDictionary *dic = tipArr[i];
        if ([dic isKindOfClass:NSDictionary.class]) {
            tipImage = dic[@"tipImage"];
            tipContent = dic[@"tipContent"];
            
            // 创建UI
            TKOpenTipView *tipView = [[TKOpenTipView alloc] initWithFrame:CGRectMake(x, y, width, height)];
            [tipView updateUIWithImage:tipImage title:tipContent];
            [self.smallTipView addSubview:tipView];
            
            y = y + tipView.frame.size.height + 15;
        }
    }
    
    // 重新调整smallTipView高度
    self.smallTipView.TKHeight = y - 15;
    self.smallTipView.TKTop = CGRectGetMinY(self.resetBtn.frame) - self.smallTipView.TKHeight - 40;
    
    // 重新布局
    self.bigTipLabel.TKTop = self.smallTipView.frame.origin.y - 20 -  self.bigTipLabel.TKHeight;
    
    // 重新创建图片控件
    UIImage *image = self.videoShowImgView.image;
    [self.videoShowBgView removeFromSuperview];
    self.videoShowBgView = nil;
    [self.videoShowImgView removeFromSuperview];
    self.videoShowImgView = nil;
    [self.tkPlayerToolView removeFromSuperview];
    self.tkPlayerToolView = nil;
    [self.playTipLabel removeFromSuperview];
    self.playTipLabel = nil;
    [self.playVideoBtn removeFromSuperview];
    self.playVideoBtn = nil;
    [self addSubview:self.videoShowBgView];
    [self.videoShowBgView addSubview:self.playerControlView];
    
    BOOL isHidden = self.playVideoBtn.hidden;
    [self.videoShowBgView addSubview:self.playVideoBtn];
    [self.videoShowBgView addSubview:self.playTipLabel];
    self.videoShowImg = image;
    [self showLoadingVideo:isHidden];
    
    
    
    if (self.bigTipLabel.TKTop+10>self.videoShowBgView.TKBottom) {
        [self.bigTipLabel setTKTop:self.videoShowBgView.TKBottom+10];
        if (![tipArr isKindOfClass:NSArray.class] || tipArr.count == 0) {
            [self.smallTipLabel setTKTop:self.bigTipLabel.TKBottom+10];
        }else{
            [self.smallTipView setTKTop:self.bigTipLabel.TKBottom+10];
        }
    }
    
    float waitBgViewWidth;
    if (![tipArr isKindOfClass:NSArray.class] || tipArr.count == 0) {
        waitBgViewWidth=self.bigTipLabel.TKWidth>self.smallTipLabel.TKWidth?self.bigTipLabel.TKWidth:self.smallTipLabel.TKWidth;
    }else{
        waitBgViewWidth=self.bigTipLabel.TKWidth>self.smallTipView.TKWidth?self.bigTipLabel.TKWidth:self.smallTipView.TKWidth;
    }
    if (waitBgViewWidth>self.videoShowBgView.TKWidth) {
        waitBgViewWidth=self.videoShowBgView.TKWidth;
    }
    float waitBgViewX=(self.TKWidth-waitBgViewWidth)/2.0f;
    float waitBgViewY=self.videoShowBgView.TKBottom-20;
    float waitBgViewHeight=self.resetBtn.TKTop-waitBgViewY-20;;
    if (waitBgViewHeight>(self.smallTipView.TKBottom-self.videoShowBgView.TKBottom+40)) {
        waitBgViewHeight=(self.smallTipView.TKBottom-self.videoShowBgView.TKBottom+40);
    }
    
    [waitBgView setTKTop:waitBgViewY];
    [waitBgView setTKLeft:waitBgViewX];
    [waitBgView setTKWidth:waitBgViewWidth];
    [waitBgView setTKHeight:waitBgViewHeight];
    waitBgView.layer.cornerRadius = 10.0; // 设置圆角半径
//    waitBgView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner; // 只设置底部的圆角
//    waitBgView.clipsToBounds = YES; // 确保超出部分被裁剪

}
//
////服务器单向卡顿提示
//-(void)netBadTipView{
//    if ([self.requestParam[@"isMoreMaxCaton"] intValue]!=1) {
//        return;//没有提示卡顿需要
//    }
//
//    float x = self.videoShowImgView.TKLeft;
//    float widht = self.videoShowImgView.TKWidth;
//    float height = self.netLabel.TKHeight + 24;
//    float y = self.videoShowImgView.TKHeight - height;
//    self.netBadBgView.frame=CGRectMake(x, y, widht, height);
//
//    [self.netBadBgView removeFromSuperview];
//
//    [self.videoShowImgView.superview addSubview:self.netBadBgView];
//    [self.netBadBgView addSubview:self.netErrorImg];
//    [self.netBadBgView addSubview:self.netLabel];
//
////    if (self.netBadBgView.TKTop<=self.playTipLabel.TKBottom) {
////        [self.playTipLabel setTKBottom:self.netBadBgView.TKTop-10];
////        [self.playVideoBtn setTKBottom:self.playTipLabel.TKTop-17];
////    }
//}
//
//
//#pragma mark  set or get
//
///**
// <AUTHOR>
// @视频展示示例图赋值
// */
//-(void)setVideoShowImg:(UIImage *)videoShowImg{
//    _videoShowImg = videoShowImg;
//    [self.videoShowImgView setImage:videoShowImg];
//    [self netBadTipView];
//}
//
//
//
///// 根据tip更新UI
///// @param tipArr tip数组。格式是：@[@{@"tipImage" : @"", @"tipContent" : @""}];
//- (void)updateTipViewWithTipArr:(NSArray *)tipArr {
//    [super updateTipViewWithTipArr:tipArr];
//
//    [self netBadTipView];
//}
//
//
//- (void)playTime:(float)currentTime longestTime:(float)longestTime {
//    [super playTime:currentTime longestTime:longestTime];
//
//    if (_netBadBgView) {
//
//        [self.netBadBgView removeFromSuperview];
//        _netBadBgView = nil;
//    }
//}
//
//
//#pragma mark - Setter && Getter
//- (UIView *)netBadBgView {
//    if (!_netBadBgView) {
//        _netBadBgView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.videoShowImgView.TKWidth, 0)];
//        _netBadBgView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.4f];
//    }
//
//    return _netBadBgView;
//}
//
//- (UILabel *)netLabel {
//    if (!_netLabel) {
//        _netLabel = [[UILabel alloc] init];
//        _netLabel.numberOfLines = 0;
//        _netLabel.backgroundColor = [UIColor clearColor];
//        _netLabel.textColor = [TKUIHelper colorWithHexString:@"#FFFFFF"];
//        _netLabel.text = @"检测到您网络状况不佳，请务必确认影像和声音正确后提交";
//        _netLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
//        float labelX = self.netErrorImg.TKRight + 8;
//        float labelWidth = self.videoShowImgView.TKWidth - labelX - 16;
//        CGSize labelSize = [_netLabel sizeThatFits:CGSizeMake(labelWidth, MAXFLOAT)];
//        _netLabel.frame = CGRectMake(labelX, 12, labelWidth, labelSize.height);
//    }
//
//    return _netLabel;
//}
//
//
//- (UIImageView *)netErrorImg {
//    if (!_netErrorImg) {
//        _netErrorImg = [[UIImageView alloc] initWithFrame:CGRectMake(16, 14, 20, 20)];
//        _netErrorImg.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_carton_warning.png", TK_OPEN_RESOURCE_NAME]];//设置图片
//    }
//
//    return _netErrorImg;
//}


/**
 <AUTHOR> 2019年04月17日20:28:31
 @初始化懒加载视频展示人像视图背景
 @return 视频展示人像视图背景
 */
-(UIView *)videoShowBgView{
    if (!_videoShowBgView) {
        float videoShowBgViewX=20;
        float videoShowBgViewWidth=self.TKWidth-videoShowBgViewX*2;

        float videoShowBgViewY=self.backBtn.TKBottom+12;
        float videoShowBgViewHeight=self.bigTipLabel.TKTop-videoShowBgViewY-10;
        
        _videoShowBgView=[[UIView alloc] initWithFrame:CGRectMake(videoShowBgViewX, videoShowBgViewY, videoShowBgViewWidth, videoShowBgViewHeight)];
        _videoShowBgView.layer.cornerRadius=8.0f;
        _videoShowBgView.backgroundColor=[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.08];
        
        float imgWidth;
        float imgHeight;
        float aspectRatio=videoShowBgViewHeight / videoShowBgViewWidth;//高除以宽的比例
        float ratioRequirements = 4.0f/3.0f;//高除以宽的要求比例
//        if (self.isLandscape) ratioRequirements = 3.0f / 4.0f;
        if (aspectRatio>ratioRequirements) {
            imgWidth=videoShowBgViewWidth;
            imgHeight=imgWidth* ratioRequirements;
        }else{
            imgHeight=videoShowBgViewHeight;
            imgWidth=imgHeight / ratioRequirements;
        }
        float imgX=0;
//        (videoShowBgViewWidth-imgWidth) / 2;
        float imgY=0;
//        (videoShowBgViewHeight-imgHeight) / 2;
        UIImageView *imgView=[[UIImageView alloc] initWithFrame:CGRectMake(imgX, imgY, imgWidth, imgHeight)];
        imgView.layer.cornerRadius = 10.0f;
        imgView.layer.masksToBounds = YES;
        
//        UIView *shadowView=[[UIView alloc] initWithFrame:imgView.frame];
//        shadowView.backgroundColor =TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE;
//        shadowView.layer.cornerRadius=10.0f;
//
//        //添加四个边阴影
//        shadowView.layer.shadowColor = [UIColor grayColor].CGColor;//阴影颜色
//        shadowView.layer.shadowOffset = CGSizeMake(0, 0);//偏移距离
//        shadowView.layer.shadowOpacity = 0.5;//不透明度
//        shadowView.layer.shadowRadius = 5.0;//半径
//        
//        [_videoShowBgView addSubview:shadowView];
        [_videoShowBgView addSubview:imgView];
        [imgView setImage:self.videoShowImg];
        imgView.userInteractionEnabled = true;
        self.videoShowImgView=imgView;
        [_videoShowBgView setTKHeight:imgHeight];
        [_videoShowBgView setTKWidth:imgWidth];
    }
    return _videoShowBgView;
}

/**
 <AUTHOR> 2025年02月19日09:53:47
 @初始化懒加载视频暂停重播工具视图
 @return 视频暂停重播工具视图
 */
- (UILabel *)titleLabel{
    if (!_titleLabel) {
        
        float width = self.TKWidth;
        float height = 24;
        float x = 0;
        float y = 10 + STATUSBAR_HEIGHT;
        _titleLabel= [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _titleLabel.font =  [UIFont fontWithName:@"PingFang SC" size:16];;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.textColor = [TKUIHelper colorWithHexString:@"#000000" alpha:1];
        _titleLabel.text = @"视频见证";
    }
    
    return _titleLabel;
}


/**
 <AUTHOR> 2019年04月13日14:01:51
 @初始化懒加载重新录制按钮
 @return 重新录制按钮
 */
-(UIButton *)resetBtn{
    if (!_resetBtn) {
        float resetBtnX = 15;
        float resetBtnWidth = (self.TKWidth-3*resetBtnX)/2;
        float resetBtnHeight = 44;
        float resetBtnY = self.TKHeight-resetBtnHeight-20-IPHONEX_BUTTOM_HEIGHT;
        _resetBtn=[[UIButton alloc] initWithFrame:CGRectMake(resetBtnX, resetBtnY, resetBtnWidth, resetBtnHeight)];
        
        [_resetBtn setTitle:@"重新录制" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _resetBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _resetBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        



        [_resetBtn setTitleColor:[TKUIHelper colorWithHexString:@"#222222"] forState:UIControlStateNormal];
        [_resetBtn setBackgroundColor:[UIColor clearColor]];
        _resetBtn.layer.borderWidth=1.0f;
        _resetBtn.layer.borderColor=[TKUIHelper colorWithHexString:@"#CCCCCC"].CGColor;
        _resetBtn.layer.cornerRadius=resetBtnHeight/2.0f;

 
        [_resetBtn addTarget:self action:@selector(resetAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _resetBtn;
}

/**
 <AUTHOR> 2019年04月13日14:02:47
 @初始化懒加载确认提交按钮
 @return 确认提交按钮
 */
-(UIButton *)submitBtn{
    if (!_submitBtn) {
        float submitBtnX=self.resetBtn.TKLeft*2+self.resetBtn.TKWidth;
        float submitBtnWidth=self.resetBtn.TKWidth;
        float submitBtnHeight=self.resetBtn.TKHeight;
        float submitBtnY=self.resetBtn.TKTop;
        _submitBtn=[[UIButton alloc] initWithFrame:CGRectMake(submitBtnX, submitBtnY, submitBtnWidth, submitBtnHeight)];

        [_submitBtn setTitle:@"下一步" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        
        [_submitBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        
        [_submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
        
        _submitBtn.layer.cornerRadius=submitBtnHeight/2.0f;
    }
    return _submitBtn;
}

@end
