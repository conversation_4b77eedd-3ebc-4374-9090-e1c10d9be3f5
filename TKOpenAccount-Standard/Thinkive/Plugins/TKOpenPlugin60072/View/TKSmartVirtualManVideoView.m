//
//  TKSmartVirtualManVideoView.m
//  OneWayVideo
//
//  Created by Vie on 2019/4/3.
//  Copyright © 2019 Vie. All rights reserved.
//

#import "TKSmartVirtualManVideoView.h"



@interface TKSmartVirtualManVideoView()

@property (nonatomic, readwrite, strong) UIView *loadingView;   // 加载页面
@property (nonatomic, strong) UIImageView *headImgView;//模拟头像图标
@property (nonatomic, strong) UILabel *nameLabel;//公司名字提示文本
@property (nonatomic, strong) UILabel *titleLabel;//业务标题提示文本
@property (nonatomic, strong) UILabel *networkLabel;//网络情况提示文本
@property (nonatomic, strong) UIImageView *boxTipImgView;   //对准框中间提示图标
@property (nonatomic, strong) UILabel *boxTipLabel;//对准框中间提示文本
@property (nonatomic, strong) NSString *answerTipString;//回答问题提示文本

@property (nonatomic, strong) NSString *boxTipText;//回答问题提示文本
@end

@implementation TKSmartVirtualManVideoView
//@synthesize avPreviewView = _avPreviewView;
@synthesize boxRect = _boxRect;
//@synthesize requestParam;
//@synthesize takeBtn = _takeBtn;
//@synthesize virtualAvPreviewView = _virtualAvPreviewView;
//@synthesize delegate = _delegate;
@synthesize answerPromptImgBg = _answerPromptImgBg;
@synthesize answerPromptLabel = _answerPromptLabel;
@synthesize backBtn = _backBtn;
@synthesize bottomShowLabel = _bottomShowLabel;
@synthesize bottomShowTipView = _bottomShowTipView;
@synthesize boxImgView = _boxImgView;//人像取景框
@synthesize boxImgBackgroundView = _boxImgBackgroundView;//人像取景背景框
/**
 <AUTHOR> 2019年04月03日09:13:34
 @初始化单向视频默认界面
 */
-(void)viewInit{

//    [self addSubview:self.avPreviewView];
//    [self addSubview:self.boxImgBackgroundView];
//    [self addSubview:self.boxImgView];
//    [self addSubview:self.topView];
//    [self addSubview:self.bottomView];
//    [self addSubview:self.leftView];
//    [self addSubview:self.rightView];
//    [self addSubview:self.backBtn];
//
//    [self addSubview:self.badgeView];
//    [self addSubview:self.recordTimeLabel];
//    [self.recordTimeLabel setHidden:YES];//进来先不展示录制倒计时
//    [self.badgeView setHidden:YES];//进来先不展示录制倒计时
//    self.badgeViewHidden=YES;
//    [self addSubview:self.warningLabel];
    
    [super viewInit];
    [self addSubview:self.headImgView];
    [self addSubview:self.nameLabel];
    [self addSubview:self.networkLabel];
    [self addSubview:self.titleLabel];
    [self.boxImgBackgroundView addSubview:self.boxTipImgView];
    [self.boxImgBackgroundView addSubview:self.boxTipLabel];
//    [self addSubview:self.virtualAvPreviewView];
    
    [self enableTakeRecord:NO];
    [self showTakeRecordBtn:NO];
//    [_takeBtn setTitle:@"开始录制" forState:UIControlStateNormal];
    
    
//    if (self.requestParam[@"isShowHeadRect"]&&[self.requestParam[@"isShowHeadRect"] integerValue] == 0) {
        //虚拟人目前不需要无头像框场景
        [self.boxImgBackgroundView setHidden:NO];
        [self.boxImgView setHidden:NO];
        [self.topView setHidden:YES];
        [self.bottomView setHidden:YES];
        [self.leftView setHidden:YES];
        [self.rightView setHidden:YES];
        
        [self.topBgView setHidden:YES];
        [self.bottomBgView setHidden:YES];
//    }
}


#pragma mark 事件方法

/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting{
    if ([TKStringHelper isEmpty:warningSting]) {
        return;
    }
    
    //展示
    self.boxTipLabel.text = warningSting;
    self.boxTipLabel.textColor=[TKUIHelper colorWithHexString:@"#FF4848"];
    CGSize size=[self.boxTipLabel sizeThatFits:CGSizeMake(self.boxRect.size.width, MAXFLOAT)];
    [self.boxTipLabel setTKHeight:size.height];
    [self.boxTipLabel setHidden:NO];
    
    
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if ([TKStringHelper isEmpty:self.boxTipText]) {
            [self.boxTipLabel setHidden:YES];
        }else{
            self.boxTipLabel.text = self.boxTipText;
            self.boxTipLabel.textColor=[TKUIHelper colorWithHexString:@"#ffffff"];
        }
    });
}




/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
- (void)takeAction:(UIButton *)sender{
    self.titleLabel.text = @"视频见证 · 录制中";
    [self addSubview:self.bottomShowTipLineView];
    [self.bottomShowTipLineView addSubview:self.bottomShowTipRecordLineView];
    
    [self.takeBtn removeFromSuperview];
    self.takeBtn = nil;
    [self.boxTipImgView setHidden:YES];
    [self.boxTipLabel setHidden:YES];
    self.boxTipText=@"";
    if (self.delegate&&[self.delegate respondsToSelector:@selector(takeRecord)]) {
        
        [self.delegate takeRecord];
    }
}



/// 展示加载层
- (void)tkShowLoading {
    if (!_loadingView) {

        [self addSubview:self.loadingView];
    }
    
    [self bringSubviewToFront:self.loadingView];
    [self bringSubviewToFront:self.backBtn];
    self.loadingView.hidden = NO;
//    [self loadingTextAnimation];
    [self bringSubviewToFront:self.headImgView];
    [self bringSubviewToFront:self.nameLabel];
    [self bringSubviewToFront:self.titleLabel];

}

/// 隐藏加载层
- (void)tkHideLoading {
    self.loadingView.hidden = YES;
    
    
    float width = 34;
    float height = 34;
    float y=(NAVBAR_HEIGHT-height)/2.0f+STATUSBAR_HEIGHT;
    [self.headImgView setTKWidth:width];
    [self.headImgView setTKHeight:height];
    [self.headImgView setTKTop:y];
    self.headImgView.layer.cornerRadius=width/2.0f;
    [self.headImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60072/tk_virtual_man_connecting_successful.png", TK_OPEN_RESOURCE_NAME]]];
}

//- (void)updateNetworkInfo:(int)sendBps recvBps:(int)recvBps
//{
//    self.networkLabel.text = [NSString stringWithFormat:@"上行:%dKB/s\n下行:%dKB/s",sendBps, recvBps];
//}

- (void)updateNetworkInfo:(TKChatVideoRecordNetworkStatus)status statusString:(NSString *)statusString {
    
    if (status == TKChatVideoRecordNetworkStatusUnknown) {
//        self.networkLabel.text = [NSString stringWithFormat:@"网络状态:%@", statusString];
        self.networkLabel.hidden = YES;
    } else {
        self.networkLabel.hidden = NO;
        
        //顶部提示用富文本
        NSMutableAttributedString *tipAttribut = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@", statusString]];
        // 2.添加属性
        [tipAttribut addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFangSC-Regular" size:12] range:NSMakeRange(0, tipAttribut.length)];
        if (status == TKChatVideoRecordNetworkStatusVeryGood || status == TKChatVideoRecordNetworkStatusGood) {
            
            [tipAttribut addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#1CAA3D"] range:NSMakeRange(0, tipAttribut.length)];
        } else if (status == TKChatVideoRecordNetworkStatusBad) {
            
            [tipAttribut addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FFBE00"] range:NSMakeRange(0, tipAttribut.length)];
        } else {
            
            [tipAttribut addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FF4951"] range:NSMakeRange(0, tipAttribut.length)];
        }
        
        self.networkLabel.attributedText = tipAttribut;
    }
}



///**
//@Auther Vie 2021年07月28日16:40:17
//@param currentNum 当前进度，
//@param allNum 总进度数目；问题n，结束语1；n+1
//*/
//-(void)currentNum:(int)currentNum allNum:(int)allNum{
//    
////    if (allNum == 0) return;
////
////    self.bottomShowTipRecordLineView.hidden = NO;
////
////    float width = currentNum * 1.0f / allNum * self.bottomShowTipLineView.TKWidth;
////    [self.bottomShowTipRecordLineView setFrameWidth:width > self.bottomShowTipLineView.TKWidth ? self.bottomShowTipLineView.TKWidth : width];
//}

///// 是否展示录制按钮
///// @param isShow 是否展示
- (void)showTakeRecordBtn:(BOOL)isShow {
    if (self.takeBtn.superview == nil) [self addSubview:self.takeBtn];
    [self bringSubviewToFront:self.takeBtn];
    self.takeBtn.hidden = !isShow;
}

- (void)updateTakeBtnWithCountDown:(int)countDown
{
    NSString *btnTitle;
    btnTitle=@"开始录制";
    [_takeBtn setTitle:btnTitle forState:UIControlStateNormal];
}

/// 设置是否可以点击开始录制
/// @param isEnable 是否可以点击
- (void)enableTakeRecord:(BOOL)isEnable {
    
    self.takeBtn.enabled = isEnable;
    if (isEnable) {
        _takeBtn.layer.borderWidth=0.0f;
        _takeBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _takeBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#2772FE":self.mainColorString];
        //        [self setButtonBackgroundColor:self.takeBtn alpha:1.0f];
    } else {
        _takeBtn.layer.borderWidth=1.0f;
        _takeBtn.layer.borderColor=[TKUIHelper colorWithHexString:@"#ffffff" alpha:1.0f].CGColor;
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:1.0f] forState:UIControlStateNormal];
//        [self setButtonBackgroundColor:self.takeBtn alpha:0.3f];
    }
}


/**
 <AUTHOR> 2025年01月24日09:18:27
 @修改提示语问题话术
 @string 文本
 @colorString 文本颜色
 @cornerRadius 背景框圆角
 @flag 是否是播放语音话术（y坐标不一样要调整）
 @flag 是否是html文本
 @return 顶部遮罩层
 */
-(void)updateTipLabel:(NSString *)string textColor:(NSString *)colorString cornerRadius:(CGFloat)cornerRadius isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed autoScroll:(BOOL)autoScroll {
    //回答问题需要展示回答语音图标
    if(isOneLineShow){
        [self.boxTipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60072/tk_virtual_man_answer.png", TK_OPEN_RESOURCE_NAME]]];
        self.boxTipText=self.boxTipLabel.text = @"请回答";
        self.boxTipLabel.textColor=[TKUIHelper colorWithHexString:@"#ffffff"];
        [self.boxTipImgView setHidden:NO];
        [self.boxTipLabel setHidden:NO];
        
        
    }else{
        colorString=@"#222222";
        //    NSLog(@"---------oneLineWidth ponitSize = %.2f, string= %@", self.bottomShowLabel.font.pointSize, string);
        string = [TKCommonUtil switchLabelToSpan:string];
        // 生成富文本
        NSMutableAttributedString *attStr = [self convertTextToHtmlString:string textColor:colorString];
        
        // 不重复处理
        if (([self.bottomShowLabel.text isEqualToString:string]
            || [self.bottomShowLabel.attributedText isEqualToAttributedString:attStr]
            || [self.bottomShowLabel.attributedText.string isEqualToString:attStr.string]) && self.changeReadTextViewWords > 0) {
            //        NSLog(@"---------oneLineWidth 传入的string = %@重复,不重复处理", string);
            return;
        }
        
        // 先暂停滚动
        [self stopTextViewScroll];
        
    //    if (!_serviceBg) {
    //        [self.bottomShowTipView addSubview:self.serviceBg];
    //        [self.serviceBg addSubview:self.serviceGifView];
    //        // 此时文案未更新高度，先不展示
    //        self.serviceBg.hidden = YES;
    //        self.serviceGifView.hidden = YES;
    //    }
        
        self.bottomShowLabel.attributedText = nil;
        self.bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 0, 0, 0); // 旧版UI会设置UIEdgeInsetsMake(5, 0, 5, 0).这里要改回来
        
        // 根据富文本调整frame
        [self updateTextViewFrame:string textColor:colorString isOneLineShow:isOneLineShow];
        
        //    self.bottomShowLabel.text = string; // 在11系统，需要设置好frame之后再赋值
        self.bottomShowLabel.attributedText = attStr;
        self.currentOriginHtmlStr = attStr;
        [self addSubview:self.bottomShowTipView];
        [self.bottomShowTipView addSubview:self.bottomShowLabel];
        // 此时文案已更新高度，展示
        self.serviceBg.hidden = NO;
        self.serviceGifView.hidden = NO;
        
        if (autoScroll) {
            //计算是否要滚动换行
            //多久渐变一个字
            float scrollSpeed = 0.19f;
            if ([TKStringHelper isNotEmpty:questionOneWordSpeed]) scrollSpeed = questionOneWordSpeed.floatValue;
            if (scrollSpeed == 0) scrollSpeed = 0.19f;
            NSTimeInterval durationTime = attStr.string.length * 1.0 / scrollSpeed;
            
            //问题播放需要判断是否走
            [self createscrollBottomShowLabelTimer:self.currentOriginHtmlStr startIndex:0 endIndex:0 duration:durationTime isHighlight:YES];
        }
    }

    

}

/**
 计算html字符串高度
 
 @param str html 未处理的字符串
 @param font 字体设置
 @param lineSpacing 行高设置
 @param width 容器宽度设置
 @return 富文本高度
 */
- (CGSize)getHTMLHeightByStr:(NSMutableAttributedString *)str width:(CGFloat)width
{
    CGSize contextSize = [str boundingRectWithSize:(CGSize){width, CGFLOAT_MAX} options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size;
    return contextSize;
    
}

/**
 @Auther Vie 2021年07月29日13:56:44
 改变阅读文本空间显示
 */
- (void)changeReadTextViewOffSet:(NSAttributedString *)originHtml startIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration repeatTime:(NSTimeInterval)repeatTime repeatIndex:(int)repeatIndex isHighlight:(BOOL)isHighlight {
        
    // 防止页面不显示的时候，定时器还在反复调用该功能
    if (!self.window) {
        
//        NSLog(@"思迪文案滚动动画：页面不显示,停止滚动");
        [self stopTextViewScroll];
        return;
    }
    
    if (self.changeReadTextViewWords < startIndex) {
//        NSLog(@"思迪文案滚动动画：changeReadTextViewWords少于startIndex，设置changeReadTextViewWords=startIndex(%i)", startIndex);
        self.changeReadTextViewWords = startIndex;
    }
    
    //    NSTimeInterval repeactTime = 1.0f;
    //    CGFloat scrollSpeed = 0.19f;
    //    if (scrollSpeed <= 0.05) repeactTime = 0.1;    // 滚动速度变大，刷新频率也更新
    //    int wordPerSecond = ceilf(repeactTime / scrollSpeed);
    
    int wordPerSecond = 0;
    
    // 若知道要滚动的内容长度和时间
    int totalWordsNeedToScroll = endIndex - startIndex;
    if (totalWordsNeedToScroll > 0) {
        wordPerSecond = ceilf(totalWordsNeedToScroll / duration * repeatTime);
        
        // 根据循环次数计算更精准的滚动字数
        if (repeatIndex > 0) {
            wordPerSecond = startIndex + ceilf(totalWordsNeedToScroll / duration * repeatTime * repeatIndex) - self.changeReadTextViewWords;
        }
        
        // 计算边界
        wordPerSecond = (self.changeReadTextViewWords + wordPerSecond) <= endIndex ? wordPerSecond : (endIndex - self.changeReadTextViewWords);
        if (wordPerSecond <= 0) {
//            NSLog(@"思迪文案滚动动画:超过endIndex，先不处理 wordPerSecond = %i", wordPerSecond);
//            return;
            wordPerSecond = 0;
        };
    } else {
        wordPerSecond = ceilf(self.bottomShowLabel.attributedText.string.length / duration * repeatTime);
    }
    
    if (self.changeReadTextViewWords < self.bottomShowLabel.attributedText.string.length) {
        // -1是防止到最后一个字再滚动的话会有点显示问题。会一整行滚动一次，又展示
        //       NSLog(@"----------555 思迪文案滚动动画：scrollSpeed = %.2f, wordPerSecond = %i, self.changeReadTextViewWords = %i, self.subtitlesTextView.attributedText.string.length = %i", scrollSpeed, wordPerSecond, self.changeReadTextViewWords, self.subtitlesTextView.attributedText.string.length);
//        NSLog(@"思迪文案滚动动画：self.changeReadTextViewWords = %i, wordPerSecond = %i, self.subtitlesTextView.attributedText.string.length = %i, startIndex = %i, endIndex = %i", self.changeReadTextViewWords, wordPerSecond, self.subtitlesTextView.attributedText.string.length, startIndex, endIndex);
        
        // 支持高亮效果
        if (isHighlight) {
                        
            // 高亮文字
            NSMutableAttributedString *html = originHtml.mutableCopy;
            NSInteger lenth = self.changeReadTextViewWords + wordPerSecond - startIndex;
            if (endIndex > 0) {
                lenth = lenth > (endIndex - startIndex) ?  (endIndex - startIndex) : lenth;
            }
//            [html addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FCC006"] range:NSMakeRange(startIndex, lenth)]; // 只高亮句子片段
            lenth = (startIndex + lenth) < self.bottomShowLabel.attributedText.string.length ? lenth : (self.bottomShowLabel.attributedText.string.length - startIndex);
            [html addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#396DE3"] range:NSMakeRange(0, startIndex + lenth)];   // 从头到播放内容都高亮
            self.bottomShowLabel.attributedText = html;
        }
        
        // 滚动
//        NSRange rang = NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
        NSInteger lenth = wordPerSecond * 50; // 5s的播放长度
        NSInteger maxLenth = self.bottomShowLabel.attributedText.string.length - self.changeReadTextViewWords;
        lenth = lenth < (maxLenth) ? lenth : maxLenth;
        NSRange range = NSMakeRange(self.changeReadTextViewWords, lenth); // 为了展示效果，多滚动一些内容
        [self.bottomShowLabel scrollRangeToVisible:range];
//        NSLog(@"思迪文案滚动动画:滚动区域 range = %@", NSStringFromRange(range));
        
        // 记录数据
        self.changeReadTextViewWords = self.changeReadTextViewWords + wordPerSecond;
        
    } else {
        NSLog(@"思迪文案滚动动画:已滚动到底部，停止滚动");
        [self stopTextViewScroll];
    }
}

- (void)createscrollBottomShowLabelTimer:(NSAttributedString *)originHtml startIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration isHighlight:(BOOL)isHighlight
{
    if (self.changeReadTextViewOffSetTimer == nil) {
//        NSLog(@"思迪文案滚动动画：创建定时器");
        
        // 文字高亮
        __weak typeof(self) weakSelf = self;
        NSTimeInterval repeatTime = 0.1f;
        __block int repeatIndex = 0; // 当前循环次数
        self.changeReadTextViewOffSetTimer = [NSTimer timerWithTimeInterval:repeatTime repeats:YES block:^(NSTimer * _Nonnull timer) {
            
            int currentRepeatIndex = repeatIndex;   // 只传递值
            [weakSelf changeReadTextViewOffSet:originHtml startIndex:startIndex endIndex:endIndex duration:duration * 0.9 repeatTime:repeatTime repeatIndex:currentRepeatIndex isHighlight:isHighlight];    // duration * 0.9是为了高亮和滚动效果快一点
            repeatIndex++; // 次数加一
        }];
        [[NSRunLoop mainRunLoop] addTimer:self.changeReadTextViewOffSetTimer forMode:NSRunLoopCommonModes];
    }
}


- (void)stopTextViewScroll
{
    if (self.changeReadTextViewOffSetTimer) {
//        NSLog(@"思迪文案滚动动画：销毁定时器");
        
        self.changeReadTextViewWords = 0;
        [self.changeReadTextViewOffSetTimer invalidate];
        self.changeReadTextViewOffSetTimer = nil;
    }
}

- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow
{
    CGFloat margin = 12;
    CGFloat bottomShowLabelX = margin;
//    if (UISCREEN_HEIGHT < 812) {
//        bottomShowLabelX = 15;
//    }
    CGFloat serviceBgRight =  0;
    bottomShowLabelX = serviceBgRight + margin;
    CGFloat bottomShowLabelY = 5;
    CGFloat bottomShowLabelWidth = self.bottomShowTipView.TKWidth - serviceBgRight - margin * 2;
    
    self.bottomShowLabel.textContainer.lineBreakMode =  NSLineBreakByWordWrapping;
    
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:string textColor:colorString];
    
    // 计算一行的宽、高度(中文高度包括行高,需要清除换行再计算)
    NSString *tempStr = [string stringByReplacingOccurrencesOfString:@"<br/>" withString:@""];
    NSMutableAttributedString *tempattStr = [self convertTextToHtmlString:tempStr textColor:colorString];
    CGFloat oneLineHeight = [self getHTMLHeightByStr:tempattStr width:CGFLOAT_MAX].height;
    
    // 如果不是播放问题，展示多行问题文本
    if (isOneLineShow == NO) {
        // 计算富文本高度，1行则1行显示，最多显示3行。仅针对问题
        // 计算文本的总高度
        //       CGFloat htmlHeight = [self getHTMLHeightByStr:attStr width:bottomShowLabelWidth].height;
        
        //        CGFloat extHeight = self.bottomShowLabel.textContainerInset.top  + self.bottomShowLabel.textContainerInset.bottom + bottomShowLabelY * 2 + self.bottomShowTipView.layer.borderWidth * 2;
        
        // 少于最大行数（如3行）展示全部
        //        CGFloat maxTotalHeight = oneLineHeight * 3;
        //        if (htmlHeight < maxTotalHeight) {
        //
        //            // 展示超过播报图案的高度，展示两行
        //            self.bottomShowTipView.TKHeight = oneLineHeight * 2 + extHeight;
        //
        //        } else {
        //            // 展示最大高度（>2）
        //            self.bottomShowTipView.TKHeight = maxTotalHeight + extHeight;
        //        }
        // 采用固定高度，不再动态计算
        self.bottomShowTipView.TKHeight = 110;
        
        //文本左右保持留白15
        self.bottomShowLabel.frame = CGRectMake(bottomShowLabelX, bottomShowLabelY, bottomShowLabelWidth, self.bottomShowTipView.TKHeight - 2 * bottomShowLabelY);
        self.bottomShowLabel.textContainer.maximumNumberOfLines = INT_MAX;
        
    } else {    // 如果是播放问题，需要展示多行（>1  问题文本 + 倒计时文本 + 识别结果文本）
        
        // 展示1行高度
        CGFloat oneLineWidth = [self getHTMLHeightByStr:attStr width:CGFLOAT_MAX].width + 16;
        self.bottomShowLabel.frame = CGRectMake(bottomShowLabelX, bottomShowLabelY, oneLineWidth, oneLineHeight * 1);
        self.bottomShowLabel.textContainer.maximumNumberOfLines = 1;
        if (self.bottomShowLabel.TKRight > self.bottomShowTipView.TKRight) {
            
            self.bottomShowLabel.TKWidth = bottomShowLabelWidth;
        }
        
        if (self.countDownType == TKCountDownTypeAnswer) {
            CGFloat margin1 = 7;
            self.countDownLabel.TKTop = self.bottomShowLabel.TKBottom + margin1;    // 换行展示
            
        } else if (self.countDownType == TKCountDownTypeUserAction) {
            
            // countDownLabel在bottomShowLabel(第1行)后拼接展示
            CGFloat margin2 = 0;
            self.countDownLabel.TKLeft = self.bottomShowLabel.TKRight + margin2;
            if ((self.countDownLabel.TKRight + margin) > self.bottomShowTipView.TKWidth) {   // countDownLabel到bottomShowTipView右侧的距离是15
                
                self.countDownLabel.TKRight = self.bottomShowTipView.TKWidth - margin;
                self.bottomShowLabel.TKWidth = self.countDownLabel.TKLeft - margin2 - self.bottomShowLabel.TKLeft;
            }
            self.countDownLabel.center = CGPointMake(self.countDownLabel.center.x, self.bottomShowLabel.center.y);
        }
        
        
        // 要和-(void)answerPromptType:(BOOL)flag identifyString:(NSString *)string中的高度对应
        // 展示最大高度
        self.bottomShowTipView.TKHeight = MAX(self.countDownLabel.TKBottom, self.bottomShowLabel.TKBottom) + 6 + self.answerPromptLabel.font.lineHeight + self.bottomShowLabel.textContainerInset.bottom + bottomShowLabelY + self.bottomShowTipView.layer.borderWidth;
    }
}



- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString
{
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)self.bottomShowLabel.font.pointSize, colorString, text];
    NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
    NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                              NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
    };
    NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}


    /**
     <AUTHOR> 2021年07月08日16:15:38
     @语音识别过程中识别到的回答小字提示
     @param回答正确，无用回答
     */
-(void)answerPromptType:(BOOL)flag identifyString:(NSString *)string{
    
    [_answerPromptLabel removeFromSuperview];
    _answerPromptLabel=nil;
    [_answerPromptImgBg removeFromSuperview];
    _answerPromptImgBg=nil;
        
    // 更新识别倒计时文案
    self.hasAsrResult = YES;
    [self updateCountDownLabelText:self.answerCount countDownType:TKCountDownTypeAnswer];
        
    self.answerPromptLabel.text=string;
        
    // 要和- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow中的计算对应
//    float y = self.countDownLabel.TKBottom + 6;
//    float y = 40;
//
//    
//    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, self.answerPromptLabel.font.lineHeight)];
//    float width=lableSize.width;
//    float x=self.bottomShowLabel.TKLeft;
//    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
    
    float y = self.bottomShowLabel.TKBottom;

    
    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowTipView.TKWidth, self.answerPromptLabel.font.lineHeight)];
    float width=lableSize.width;
    float x=(self.bottomShowLabel.TKWidth-width)/2.0f;
    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
    [self.bottomShowTipView addSubview:self.answerPromptLabel];
        
    //语音回答旁边小图标
    self.answerPromptImgBg=[[UIView alloc] initWithFrame:CGRectMake(x+width+6, y + 4, 22, 22)];
    self.answerPromptImgBg.layer.cornerRadius=self.answerPromptImgBg.TKWidth/2.0f;
    UIImageView *imgView=[[UIImageView alloc] initWithFrame:CGRectMake(0.0f, 0.0f, 22, 22)];
    [self.answerPromptImgBg addSubview:imgView];
    if (flag) {
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:self.mainColorString];
            self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:self.mainColorString];
        }else{
            self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#396DE3"];
            self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:@"#396DE3"];
        }
            
            
        imgView.image=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_one_answer_ok.png", TK_OPEN_RESOURCE_NAME]];
    }else{
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#EA4940"];
        self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:@"#EA4940"];
        imgView.image=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_one_answer_unknown.png", TK_OPEN_RESOURCE_NAME]];
    }
    [self.bottomShowTipView addSubview:self.answerPromptImgBg];
    
    [self.boxTipImgView setHidden:YES];
    [self.boxTipLabel setHidden:YES];
    self.boxTipText=@"";
}

- (void)updateCountDownLabelText:(int)answerCount countDownType:(TKCountDownType)countDownType
{
    self.countDownType = countDownType;
    
//    [self.bottomShowTipView addSubview:self.countDownLabel];
    
    NSString *tempStr = nil;
    if (countDownType == TKCountDownTypeAnswer) {
        if (self.hasAsrResult == YES) {
            tempStr =[NSString stringWithFormat:@"%@(%ds)",self.answerTipString,(int)answerCount] ;
        } else {
            tempStr =[NSString stringWithFormat:@"%@(%ds)",self.answerTipString,(int)answerCount] ;
        }
    } else {
        tempStr = [NSString stringWithFormat:@"(%d)",(int)answerCount];
    }
//    self.countDownLabel.text = tempStr;
    
//    [self.countDownLabel sizeToFit];
//    self.countDownLabel.TKHeight = 22;
    
//    string = [TKCommonUtil switchLabelToSpan:string];
//    self.answerTipString=string;
    // 生成富文本
    NSMutableAttributedString *attStr =[self convertTextToHtmlString:tempStr textColor:@"#2772FE"];
//    [self convertTextToHtmlString:self.answerTipString textColor:@"#CCCCCC"];
    self.answerPromptLabel.attributedText=attStr;
    
    float y = self.bottomShowLabel.TKBottom;

    
    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowTipView.TKWidth, self.answerPromptLabel.font.lineHeight)];
    float width=lableSize.width;
    float x=(self.bottomShowLabel.TKWidth-width)/2.0f;
    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
}

- (void)showNoAnswerPrompt
{
    self.answerPromptLabel.text = @"未检测到您的回答，请再回答一次";
    self.answerPromptLabel.backgroundColor = [TKUIHelper colorWithHexString:@"#FFFFFF"];
    
    
    self.answerPromptLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, self.answerPromptLabel.font.lineHeight)];
    float width = lableSize.width + 12;
    float x = self.bottomShowLabel.TKLeft;
    float y = self.countDownLabel.TKBottom + 6;
    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
    
    if (![self.mainColorString isEqualToString:@"#2772FE"]) {
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:self.mainColorString];
    }else{
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#2F85FF"];
    }
    
//    [self.bottomShowTipView addSubview:self.answerPromptLabel];
}

/**
@Auther Vie 2025年02月11日10:31:04
@问题回答提示语
*/
- (void)changeAnswerLabel:(NSString *)string{
    string = [TKCommonUtil switchLabelToSpan:string];
    self.answerTipString=string;
    // 生成富文本
    NSMutableAttributedString *attStr =[self convertTextToHtmlString:string textColor:@"#CCCCCC"];
//    [self convertTextToHtmlString:string textColor:[self.mainColorString isEqualToString:@"#2772FE"]?@"#51B4FE":self.mainColorString];
    self.answerPromptLabel.attributedText=attStr;
        
    // 要和- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow中的计算对应
//    float y = self.countDownLabel.TKBottom + 6;
//    float y=5;
    float y = self.bottomShowLabel.TKBottom;

    
    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowTipView.TKWidth, self.answerPromptLabel.font.lineHeight)];
    float width=lableSize.width;
    float x=(self.bottomShowLabel.TKWidth-width)/2.0f;
    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
    
    [self.bottomShowTipView setTKHeight:self.bottomShowTipView.TKHeight+lableSize.height];
    [self.bottomShowTipView addSubview:self.answerPromptLabel];
}

/**
 <AUTHOR> 2019年04月16日20:09:51
 @语音合成播放完成，修改界面
 */
- (void)playEndView:(int)waitTime prompt:(NSString *)string noAnswerPromptTime:(int)noAnswerPromptTime {
    
    if (waitTime<=0) {
        waitTime=5;
    }
    
    self.answerCount=waitTime;
    self.hasAsrResult = NO;
//    [self.serviceGifView setImageByName:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceListen.gif", TK_OPEN_RESOURCE_NAME]];
    [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeAnswer];
//    [self.bottomShowTipView addSubview:self.countDownLabel];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
    });
}

/**
 <AUTHOR> 2019年04月26日18:39:08
 @问题回答倒计时
 */
- (void)answerCountDown:(int)waitTime noAnswerPromptTime:(int)noAnswerPromptTime {
    
    __weak typeof(self) weakSelf = self;
    
    if (!self.isFinished) {
        self.answerCount = self.answerCount-1;
        
        noAnswerPromptTime = noAnswerPromptTime <= 0 ? 3 : noAnswerPromptTime;
        if ((waitTime - self.answerCount >= noAnswerPromptTime) && self.hasAsrResult == NO) {
            [self showNoAnswerPrompt];
        }
        
        [self updateCountDownLabelText:self.answerCount countDownType:TKCountDownTypeAnswer];
        
        if (self.answerCount>=3) {
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
            });
            
        }else if(self.answerCount<3&&self.answerCount>0){
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
            });
        }else{
            
            if (self.delegate&&[self.delegate respondsToSelector:@selector(answerCountDownEnd)]) {
                [self.delegate answerCountDownEnd];
            }
            
            //            [self.countDownLabel removeFromSuperview];
        }
    }else{
        
        //        [self.countDownLabel removeFromSuperview];
    }
    
}


#pragma mark lazyloading


/// 加载蒙层
- (UIView *)loadingView {
    if (!_loadingView) {
        _loadingView=[[UIView alloc] initWithFrame:self.frame];
        _loadingView.backgroundColor=[TKUIHelper colorWithHexString:@"#000000"];
        
        float gifWidth=24;
        TKGIFImageView *loadingGifView = [[TKGIFImageView alloc] initWithFrame:CGRectMake(0, 0, gifWidth, gifWidth)];
        [loadingGifView setImageByName:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60800/%@.gif", TK_OPEN_RESOURCE_NAME, @"tk_digtal_loading"]];
        [_loadingView addSubview:loadingGifView];
        
        float labelWidth=70;
        float labelHeight=26;
        UILabel *loadingLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, labelWidth, labelHeight)];
        loadingLabel.text=@"视频链接中";
        loadingLabel.font=[UIFont fontWithName:@"PingFang SC" size:14.0f];
        loadingLabel.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        [_loadingView addSubview:loadingLabel];
        
        float y=(_loadingView.TKHeight-IPHONEX_BUTTOM_HEIGHT-labelHeight)/2.0f;
        float x=(_loadingView.TKWidth-labelWidth-gifWidth)/2.0f;
        [loadingGifView setTKLeft:x];
        [loadingGifView setTKTop:y+(labelHeight-gifWidth)/2.0f];
        
        [loadingLabel setTKLeft:loadingGifView.TKRight];
        [loadingLabel setTKTop:y];
    }
    return _loadingView;
}


/**
 <AUTHOR> 2025年01月20日16:17:49
 @初始化懒加载模拟头像图标
 @return 模拟头像图标
 */
-(UIImageView *)headImgView{
    if (!_headImgView) {
        float width = 28;
        float x = 12;
        float height = 28;
        float y=(NAVBAR_HEIGHT-height)/2.0f+STATUSBAR_HEIGHT;
        _headImgView=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _headImgView.backgroundColor=[TKUIHelper colorWithHexString:@"#ffffff"];
        _headImgView.layer.cornerRadius=width/2.0f;
        [_headImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60072/tk_virtual_man_connecting.png", TK_OPEN_RESOURCE_NAME]]];
        
    }
    return _headImgView;
}


/**
 <AUTHOR> 2025年01月20日16:17:52
 @初始化懒加载公司名字提示文本
 @return 公司名字提示文本
 */
-(UILabel *)nameLabel{
    if (!_nameLabel) {
        float width = 100;
        float x = self.headImgView.TKRight+8;
        float height = NAVBAR_HEIGHT;
        _nameLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, STATUSBAR_HEIGHT, width, height)];
        _nameLabel.text = @"思迪证券";
        _nameLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _nameLabel.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        _nameLabel.numberOfLines = 0;
        _nameLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _nameLabel;
}

/**
 <AUTHOR> 2025年01月20日10:06:04
 @初始化懒加载业务标题提示文本
 @return 业务标题提示文本
 */
-(UILabel *)titleLabel{
    if (!_titleLabel) {
        float width = 120;
        float x = (self.TKWidth-width)/2.0f;
        float height = NAVBAR_HEIGHT;
        _titleLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, STATUSBAR_HEIGHT, width, height)];
        _titleLabel.text = @"视频见证";
        _titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _titleLabel.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        _titleLabel.numberOfLines = 0;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

/**
 <AUTHOR> 2025年01月20日09:56:59
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnWidth=20;
        float backBtnheight=20;
        float backBtnX=self.TKWidth-backBtnWidth-10;
        float backBtnY=(NAVBAR_HEIGHT-backBtnheight)/2.0f+STATUSBAR_HEIGHT;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        _backBtn.clipsToBounds = YES;
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_x_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}


/**
 *  @初始化懒加载networkLabel
 *  @return  networkLabel
 */
-(UILabel *)networkLabel{
    if (!_networkLabel) {
        
        float width = 100;
        float x = self.backBtn.TKLeft - 16 - width;
        float height = 12;
        float y=(NAVBAR_HEIGHT-height)/2.0f+STATUSBAR_HEIGHT;
        _networkLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
//        _networkLabel.center = CGPointMake(_networkLabel.center.x, self.backBtn.center.y);
        //        _networkLabel.text = @"上行：00KB/s\n下行：00KB/s";
        //        _networkLabel.text = @"网络状态:未知";
        _networkLabel.hidden = YES;
        _networkLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
        _networkLabel.numberOfLines = 0;
        _networkLabel.textAlignment = NSTextAlignmentRight;
    }
    return _networkLabel;
}





- (void)setVirtualAvPreviewView:(UIView *)virtualAvPreviewView {
    [self addSubview:virtualAvPreviewView];
    [self insertSubview:virtualAvPreviewView aboveSubview:self.avPreviewView];
}

/**
 <AUTHOR> 2019年04月03日10:47:55
 @初始化懒加载人像取景框矩阵
 @return 人像取景框矩阵
 */
-(CGRect)boxRect{
    if (CGRectIsEmpty(_boxRect)) {
        //因为是多问题，对准框需要固定不变位置，按文本4行高度117弄y坐标
        float boxRectX =12;
        float gap=46;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            boxRectX=32;
            gap=22;
        }
        float boxRectWidth = self.TKWidth-boxRectX*2.0f;
        float boxRectHeight = boxRectWidth / 300.0f * 340.0f; // 图片宽高是300 * 340
        float boxRectY = self.backBtn.TKBottom + gap + 117;
        _boxRect=CGRectMake(boxRectX, boxRectY, boxRectWidth, boxRectHeight);
    }
    return _boxRect;
}



/**
 <AUTHOR> 2025年02月05日14:01:37
 @初始化懒加载UIImageView人像取景框
 @return UIImageView人像取景框
 */
-(UIImageView *)boxImgView{
    if (!_boxImgView) {
        
        _boxImgView=[[UIImageView alloc] initWithFrame:self.boxRect];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60072/tk_virtual_man_box.png", TK_OPEN_RESOURCE_NAME]];
        
        //        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
        //            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        //            [_boxImgView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        //        }
        [_boxImgView setImage:img];
    }
    return _boxImgView;
}

/**
 @初始化懒加载UIImageView人像取景背景框(外层的黑色边框)
 @return UIImageView人像取景背景框
 */
-(UIImageView *)boxImgBackgroundView{
    if (!_boxImgBackgroundView) {
        
        _boxImgBackgroundView = [[UIImageView alloc] initWithFrame:self.boxRect];
        [_boxImgBackgroundView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60072/tk_virtual_man_box_bg.png", TK_OPEN_RESOURCE_NAME]]];
    }
    return _boxImgBackgroundView;
}



/**
 <AUTHOR> 2025年02月06日10:16:15
 @初始化懒加载UIImageView对准框中间提示图标
 @return UIImageView对准框中间提示图标
 */
-(UIImageView *)boxTipImgView{
    if (!_boxTipImgView) {
        float width=120;
        float height=120;
        float y=60;
        float x=(self.boxRect.size.width-width)/2.0f;
        _boxTipImgView = [[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_boxTipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60072/tk_virtual_man_wait.png", TK_OPEN_RESOURCE_NAME]]];
        _boxTipImgView.contentMode=UIViewContentModeScaleAspectFit;
    }
    return _boxTipImgView;
}


/**
 * <AUTHOR> 2025年02月06日13:25:54
 *  @初始化懒加载对准框中间提示文本
 *  @return  对准框中间提示文本
 */
-(UILabel *)boxTipLabel{
    if (!_boxTipLabel) {
        
        float width = self.boxRect.size.width;
        float x = 0;
        float height = 30;
        float y=self.boxTipImgView.TKBottom+10;
        _boxTipLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        self.boxTipText=_boxTipLabel.text = @"请保持人脸在视频框内";
        _boxTipLabel.textColor=[TKUIHelper colorWithHexString:@"#ffffff"];
        _boxTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
        _boxTipLabel.numberOfLines = 0;
        _boxTipLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _boxTipLabel;
}


/**
 <AUTHOR> 2025年01月20日16:39:16
 @初始化懒加载takeBtn
 @return takeBtn
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {
        float height=44;
        float width;
        float gap=40;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=10;
        }
        
        if(self.isReadVideo){
            width=175;
        }else{
            width = 255;
        }
        
        
        float y=self.TKHeight-height-gap-IPHONEX_BUTTOM_HEIGHT;
        float x=(self.TKWidth-width)/2;
        
        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [self updateTakeBtnWithCountDown:0];
        
        _takeBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
        [_takeBtn addTarget:self action:@selector(takeAction:) forControlEvents:UIControlEventTouchUpInside];
        _takeBtn.layer.cornerRadius=height/2.0f;
        
        // 默认一开始不可用
        [self enableTakeRecord:NO];
    }
    return _takeBtn;
}


/**
 <AUTHOR> 2025年01月22日09:24:54
 @初始化懒加载recordTimeLabel
 @return recordTimeLabel
 */
-(UILabel *)recordTimeLabel{
    if (!_recordTimeLabel) {
        float y=0;
        float width=0;
        float height=0;
        float x=0;
        _recordTimeLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _recordTimeLabel.hidden=YES;
    }
    return _recordTimeLabel;
}


/**
 <AUTHOR> 2025年01月24日09:16:07
 @初始化懒加载底部文字展示
 @return 底部文字展示
 */
-(UITextView *)bottomShowLabel{
    if (!_bottomShowLabel) {
        
        _bottomShowLabel=[[UITextView alloc] init];
        //        _bottomShowLabel.numberOfLines=0;
        _bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
        _bottomShowLabel.textColor=[UIColor whiteColor];
        _bottomShowLabel.textAlignment=NSTextAlignmentCenter;
        _bottomShowLabel.clipsToBounds=YES;//子视图是否局限于视图的边界。
        
        //        _bottomShowLabel.text=@"请您保持全脸在人像框内。";
        _bottomShowLabel.backgroundColor=[UIColor clearColor];
        _bottomShowLabel.frame=CGRectMake(0, 0, self.bottomShowTipView.TKWidth, self.bottomShowTipView.TKHeight);
        _bottomShowLabel.backgroundColor=[UIColor clearColor];
        _bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 0, 0, 0);
        [_bottomShowLabel setEditable:false];
        _bottomShowLabel.showsVerticalScrollIndicator = NO;
        _bottomShowLabel.showsHorizontalScrollIndicator = NO;
    }
    return _bottomShowLabel;
}



/**
 <AUTHOR> 2025年02月05日09:53:18
 @初始化懒加载底部文档等提示展示区域
 @return 底部文档等提示展示区域
 */
-(UIView *)bottomShowTipView{
    if (!_bottomShowTipView) {
        //        float x=15;
        //        float width=self.frame.size.width-x*2;
        //        float height=160;
        //        float y=self.frame.size.height-height-20;
        //        if (ISIPHONEX) {
        //            y=y-34;
        //        }
        
        _bottomShowTipView=[[UIView alloc] init];
//        _bottomShowTipView.layer.borderWidth = 1;
        //子视图是否局限于视图的边界。
        _bottomShowTipView.clipsToBounds=YES;
        
//        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
//            _bottomShowTipView.layer.borderColor=[TKUIHelper colorWithHexString:self.mainColorString alpha:0.2f].CGColor;
//        }else{
//            _bottomShowTipView.layer.borderColor=[TKUIHelper colorWithHexString:@"#498FD5" alpha:0.2f].CGColor;
//        }
        [_bottomShowTipView setBackgroundColor:[TKUIHelper colorWithHexString:@"#FFFFFF"]];
        
        float gap =12;
//        //5s等小屏幕机型
//        if (UISCREEN_WIDTH==320) {
//            gap=22;
//        }
        
        float bottomShowTipViewX=12;
//        if (UISCREEN_WIDTH==320) {
//            bottomShowTipViewX=15;
//        }
        float bottomShowTipViewY=self.backBtn.TKBottom+gap;
        float bottomShowTipViewWidth=self.TKWidth-2*bottomShowTipViewX;//左右留白15;
        self.bottomShowTipView.layer.cornerRadius=13.0f;
        self.bottomShowTipView.frame=CGRectMake(bottomShowTipViewX, bottomShowTipViewY, bottomShowTipViewWidth, 42);
    }
    return _bottomShowTipView;
}

/**
 <AUTHOR> 2020年08月26日10:52:52
 @初始化懒加载语音回答结果提示
 @return 语音回答结果提示
 */
- (UILabel *)answerPromptLabel{
    if (!_answerPromptLabel) {
        _answerPromptLabel = [[UILabel alloc] init];
        _answerPromptLabel.textAlignment=NSTextAlignmentCenter;
        _answerPromptLabel.font=[UIFont fontWithName:@"PingFang SC" size:20];
    }
    return _answerPromptLabel;
}
@end
