//
//  TKSmartVirtualManViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/9/17.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKSmartVirtualManViewController.h"

#import "TKSmartVirtualManVideoView.h"
#import "TKSmartVirtualManVideoEndView.h"
#import "TKChatVideoRecordManager.h"


@interface TKSmartVirtualManViewController ()<TKChatVideoRecordManagerDelegate>

//@property (nonatomic, readwrite, strong) TKLayerView *layerView;
@property (nonatomic, readwrite, strong) TKChatVideoRecordManager *recordManager; // 服务端录制管理者
@property (nonatomic, strong) UIView *virtualAvPreviewView;//虚拟坐席预览视图

@end

@implementation TKSmartVirtualManViewController
@synthesize recordManager = _recordManager;


#pragma mark - 重写父类的方法
- (void)bootDevice:(BOOL)isFirst
{
    [self handleBootDeviceDidStart];
    
    // 连接视频服务器
    [self requestTChatServerRoom:isFirst];
}

- (void)startTTS:(TKSmartQuestionModel *)model {
    //防止偶现到原生结果页面，语音还在播放问题
    if (self.oneWayProcess == TKOneWayProcessIdle) {
        TKLogInfo(@"TKSmartOneVideo:已经到结果页了，不需要再处理语音播放了");
        // 停止语音合成和识别
        [self stopTTSAndAsr];
        return;
    }
    
    [self.recordManager virtualSyntheticAndPlay:model.tipContent fileName:model.fileName fileSource:model.fileSource];
}

- (void)stopTTS {
    [self.recordManager stopVirtualSyntheticAndPlay];
}

- (void)activeDevice {
    
}

- (void)deactiveDevice {
//    [self stopRecordingAction];
}

- (void)requestTChatServerRoom:(BOOL)isFirstInitTChat
{
    __weak typeof(self) weakSelf = self;

    [self.recordManager requestServerRoomWithUrlStr:self.requestParam[@"url"] enableVh:YES callBack:^(BOOL success, NSInteger errorNo, NSString * _Nonnull errorMsg) {

        if (success) {

            if (isFirstInitTChat) [weakSelf.recordManager initTChatWitness];

            [weakSelf.recordManager connectServer];
        } else {
            [self handleBootDeviceDidFail:[NSString stringWithFormat:@"%@(%ld)", errorMsg, (long)errorNo]];
        }
    }];
}


- (NSString *)pluginCallBackfuncNo {
    
    return @"60073";
}

- (BOOL)skipPreview {
    return NO;
}

- (BOOL)isLocalRecord {
    return NO;
}

- (BOOL)isUseTChat {
    return YES;
}

- (void)sendCallBack:(NSMutableDictionary *)callJsParam {
    if (self.delegate && [self.delegate respondsToSelector:@selector(tkSmartVirtualManVideoDidComplete:)]) {
       [self.delegate tkSmartVirtualManVideoDidComplete:callJsParam];
    }else{
       [super sendCallBack:callJsParam];
    }
}

#pragma mark TKChatVideoRecordManagerDelegate
/// 视频服务器连接状态回调
- (void)connectServerStautsDidChange:(TKChatVideoRecordNetworkStatus)status statusString:(NSString *)statusString {
    
    [self.tkOneView updateNetworkInfo:status statusString:statusString];
//    // 录制未完成时，网络质量差，结束录制
//    if (status >= 4 && [TKStringHelper isEmpty:self.videoLength]) {
//        [self failActionCallJsWith:@"-9" errorMsg:@"网络不稳定，请切换网络再试"];
//    }
}


#pragma mark - lazyloading
/**
 <AUTHOR> 2019年04月03日10:09:41
 @初始化懒加载单向视频界面
 @return 单向视频界面
 */
- (UIView<TKBaseVideoRecordViewProtocol> *)tkOneView{
    if (!super.tkOneView) {
        super.tkOneView = [[TKSmartVirtualManVideoView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        super.tkOneView.delegate = self;
        super.tkOneView.virtualAvPreviewView = self.virtualAvPreviewView;
    }
    return super.tkOneView;
}

/**
 <AUTHOR>
 @初始化单向视频结果页面
 */
- (UIView<TKBaseVideoRecordEndViewProtocol> *)tkOneEndView{
    if (!super.tkOneEndView) {
        super.tkOneEndView = [[TKSmartVirtualManVideoEndView alloc] initWithFrame:self.view.frame withParam:self.requestParam];
        super.tkOneEndView.delegate = self;
    }
    return super.tkOneEndView;
}

/// 服务端视频录制工具类
- (NSObject<TKRecordManagerProtocol> *)recordManager {
    if (!super.recordManager) {
        super.recordManager = [[TKChatVideoRecordManager alloc] initWithConfig:self.requestParam];
        super.recordManager.delegate = self;
        super.recordManager.contentView = self.avPreviewView;
        super.recordManager.remoteContentView = self.virtualAvPreviewView;
    }
    return super.recordManager;
}



//- (TKLayerView *)layerView{
//    if (!_layerView) {
//        _layerView = [[TKLayerView alloc] initContentView:self.view withBtnTextColor:@"#ffffff"];
//    }
//    return _layerView;
//}


- (UIView *)virtualAvPreviewView{
    if (!_virtualAvPreviewView) {
        _virtualAvPreviewView = [[UIView alloc] initWithFrame:CGRectZero];
        [_virtualAvPreviewView setBackgroundColor:[UIColor grayColor]];
        _virtualAvPreviewView.layer.cornerRadius = 10;
        _virtualAvPreviewView.clipsToBounds = YES;
        // 设置虚拟人图层的最大宽度和高度
        _virtualAvPreviewView.TKWidth =120;
//        self.view.TKWidth;
        _virtualAvPreviewView.TKHeight =120;
//        self.view.TKHeight - CGRectGetMaxY(self.tkOneView.boxRect) - IPHONEX_BUTTOM_HEIGHT - 6 + 30; // 距离底部6px. +30是允许虚拟人和对准框有部分重叠，避免虚拟人过小。
        _virtualAvPreviewView.layer.cornerRadius = 10;
        _virtualAvPreviewView.TKTop = UISCREEN_HEIGHT; // 默认先展示在屏幕外，否则在tchat初始化渲染前会有一个灰色块
    }
    return _virtualAvPreviewView;
}

@end
