//
//  TKOpenPlugin60072.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/8/30.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKOpenPlugin60072.h"
#import "UIViewController+TKAuthorityKit.h"
#import "TKSmartVirtualManViewController.h"
#import <AVFoundation/AVFoundation.h>
#import <MediaPlayer/MediaPlayer.h>

@interface TKOpenPlugin60072 ()<TKSmartVirtualManViewControllerResultDelegate>
@property (nonatomic, strong) NSMutableDictionary *requestParam;//h5带过来的参数

@property (nonatomic, readwrite, assign) BOOL isInvokeing; // 是否正在被调用

@end

@implementation TKOpenPlugin60072
-(ResultVo *)serverInvoke:(id)param{
    
    ResultVo *resultVo = [[ResultVo alloc]init];
//    param[@"aliYunKey"]=@"default";
//    param[@"aliYunToken"]=@"default";
//    param[@"aliYunUrl"]=@"ws://**************:8101/ws/v1";
//   param[@"mainColor"]=@"#EC2E31";
//    param[@"uploadTipString"]=@"请您确认：我是***，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。";
    self.requestParam=param;
    
    // 埋点-单向调用
//    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
//                         subEventName:TKPrivateSubEventNone
//                             progress:TKPrivateEventProgressStart
//                               result:TKPrivateEventResultNone
//                          orientation:TKPrivateVideoOrientationPortrait
//                      oneWayVideoType:TKPrivateOneWayVideoTypeTChatDigitalMan
//                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
//                             eventDic:self.requestParam];
    
//    if ([TKStringHelper isEmpty:self.requestParam[@"url"]]) {
//        resultVo.errorNo = -6002601;
//        resultVo.errorInfo = @"服务端接口地址不能为空";
//        return resultVo;
//    }
//    if ([TKStringHelper isEmpty:self.requestParam[@"senseTimeKey"]]) {
//        resultVo.errorNo = -6002602;
//        resultVo.errorInfo = @"商汤授权Key不能为空";
//        return resultVo;
//    }
//    if ([TKStringHelper isEmpty:self.requestParam[@"senseTimeSecret"]]) {
//        resultVo.errorNo = -6002603;
//        resultVo.errorInfo = @"商汤授权Secret不能为空";
//        return resultVo;
//    }
    
    
    
//    if ([TKStringHelper isEmpty:self.requestParam[@"aliYunToken"]]) {
//        resultVo.errorNo = -6002604;
//        resultVo.errorInfo = @"阿里Token不能为空";
//        return resultVo;
//    }
//    if ([TKStringHelper isEmpty:self.requestParam[@"aliYunKey"]]) {
//        resultVo.errorNo = -6002605;
//        resultVo.errorInfo = @"阿里授权Key不能为空";
//        return resultVo;
//    }
//    if ([TKStringHelper isEmpty:self.requestParam[@"requestParam"]]) {
//        resultVo.errorNo = -6002606;
//        resultVo.errorInfo = @"服务端接口参数不能为空";
//        return resultVo;
//    }
    
    
//    if ([TKStringHelper isEmpty:self.requestParam[@"tencentYunAppId"]]) {
//        resultVo.errorNo = -6002601;
//        resultVo.errorInfo = @"腾讯云语音AppId不能为空";
//        return resultVo;
//    }
//    if ([TKStringHelper isEmpty:self.requestParam[@"tencentYunSecretId"]]) {
//        resultVo.errorNo = -6002602;
//        resultVo.errorInfo = @"腾讯云语音秘钥Id不能为空";
//        return resultVo;
//    }
//    if ([TKStringHelper isEmpty:self.requestParam[@"tencentYunSecretKey"]]) {
//        resultVo.errorNo = -6002603;
//        resultVo.errorInfo = @"腾讯云语音秘钥Key不能为空";
//        return resultVo;
//    }
    
//    self.requestParam[@"tencentYunAppId"]=@"1253132491";
//    self.requestParam[@"tencentYunSecretId"]=@"AKID3ua4nKadPurhHaiS1NSjkTv5ybwz3G8j";
//    self.requestParam[@"tencentYunSecretKey"]=@"SfbMq89uNYT6SWKr65nEOHsDLCm68Fn4";
    
//    TKLogInfo(@"静音检查，准备走流程打开视频录制页面");
    
//    if ([TKCommonUtil isHeadsetPluggedIn]) {
//        dispatch_async(dispatch_get_main_queue(), ^{
//            UIAlertView *alert=[[UIAlertView alloc] initWithTitle:@"提示" message:@"您当前在使用耳机，请移除耳机后重试" delegate:nil cancelButtonTitle:@"确定" otherButtonTitles:nil];
//            [alert show];
//
//        });
//        return resultVo;
//    }
//
//    self.requestParam[@"questionWords"]=@"你是自愿开户么";
//    self.requestParam[@"mainColor"]=@"#FF1493";
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //是否在调用插件前展示介绍页面
        if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
            [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Camera),@(TKAuthorizationType_Audio)] authCallBacks:nil btnCallBack:^{
                [self.currentViewCtrl tkIsMicrophonePermissions:^{
                                        
                    [self.currentViewCtrl tkIsCameraPermissions:^{
                        [self handleAuthorized:param];
                    }];
                                                
                }];
            }];
        }else{
            [self.currentViewCtrl tkIsMicrophonePermissions:^{
                                    
                [self.currentViewCtrl tkIsCameraPermissions:^{
                    [self handleAuthorized:param];
                }];
                                            
            }];

        }

    });
    
    
    
     return resultVo;
}

- (void)handleAuthorized:(id)param
{
    // 已在调用，拦截重复调用
    if (self.isInvokeing == YES) {
//        TKLogInfo(@"静音检查，拦截重复调用");
        return ;
    };
    
    // 标记正在调用
    self.isInvokeing = YES;
    
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [[AVAudioSession sharedInstance] setActive:YES error:nil];
                                
    CGFloat volume = audioSession.outputVolume;
    //默认音量调整支持h5控制
    float defaultVolume=TKSmartOpenVolume;
    if (self.requestParam[@"defaultVolume"]) {
        defaultVolume=[self.requestParam[@"defaultVolume"] intValue]/100.00f;
    }
    if ((volume+0.05)<defaultVolume) {
        //直接调整音量api还能使用，先改音量不提示用户
        MPMusicPlayerController *mp=[MPMusicPlayerController applicationMusicPlayer];
        mp.volume=defaultVolume;//0为最小1为最大
    }
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    TKSmartVirtualManViewController *oneWayCtr=[[TKSmartVirtualManViewController alloc] initWithParam:self.requestParam];
    oneWayCtr.delegate=self;
    [self.currentViewCtrl presentViewController:oneWayCtr animated:YES completion:nil];
                                    


}

//结果信息
-(void)tkSmartVirtualManVideoDidComplete:(NSMutableDictionary *)result{
    if (self.isInvokeing) {
        //已经给h5回调了，不再回调;处理tchat还可能回一个708的错误情况
        [self iosCallJsWithParam:result];
    
    }
    // 重置标志位
    self.isInvokeing = NO;
}
@end
