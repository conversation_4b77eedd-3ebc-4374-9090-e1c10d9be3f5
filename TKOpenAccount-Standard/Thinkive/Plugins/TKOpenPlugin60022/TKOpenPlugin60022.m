//
//  TKOpenPlugin60022.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2017/1/1.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "TKOpenPlugin60022.h"
#import "TKSignatureController.h"

@interface TKOpenPlugin60022()<TKSignatureControllerDelegate>
{
    NSDictionary *h5Params;
}
@end

@implementation TKOpenPlugin60022

- (ResultVo *)serverInvoke:(id)param{
    
    h5Params = param;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        TKSignatureController *signCtrl = [[TKSignatureController alloc] initWithJSParm:h5Params];
        signCtrl.delegate = self;
        [self.currentViewCtrl presentViewController:signCtrl animated:YES completion:nil];
        
    });

    return [[ResultVo alloc]init];
    
}

#pragma mark- implement tkSignatureDidComplete: 手写签名结果处理
- (void)tkSignatureDidComplete:(UIImage *)signImage
{
    NSMutableDictionary * reqParam = [NSMutableDictionary dictionaryWithCapacity:3];
    
    reqParam[@"funcNo"]=@"60061";

    NSString *signature = [TKBase64Helper stringWithEncodeBase64Data:UIImageJPEGRepresentation(signImage, 1.0f)];;
    
    reqParam[@"errorNo"]=@"0";
    
    reqParam[@"signatureBase64"]=[NSString stringWithFormat:@"data:image/jpg;base64,%@", signature];
    [self iosCallJsWithParam:reqParam];
}

@end
