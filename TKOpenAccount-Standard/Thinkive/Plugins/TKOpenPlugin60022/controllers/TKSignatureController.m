//
//  TKSignatureController.m
//  TKOpenAccount-Standard
//
//  Created by kill on 16/9/6.
//  Copyright © 2016年 thinkive. All rights reserved.
//

#import "TKSignatureController.h"
#import "TKTouchView.h"
#import "TKOpenAccountService.h"

#define TK_SingTopHeight 44

@interface TKSignatureController ()
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) TKTouchView *signView;//签名区域视图
@property(nonatomic, strong) UIButton *backBtn;//返回按钮
@property(nonatomic, strong) UIButton *cleanBtn;//清除按钮
@property(nonatomic, strong) UILabel *showTipLabel;//头部展示信息文本
@property(nonatomic, strong) UIButton *submitBtn;//提交按钮
@property(nonatomic, strong) id h5Params;//h5的参数
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@end

@implementation TKSignatureController

- (id)initWithJSParm:(id)params
{
    self = [super init];
    if (self) {
        self.h5Params = params;
        if ([TKStringHelper isEmpty:params[@"mainColor"]]) {
            self.mainColorString=@"#2772FE";
        }else{
            self.mainColorString=params[@"mainColor"];
        }
    }
    return self;
}



- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

-(void)viewDidLoad
{
    [super viewDidLoad];
    self.view.backgroundColor =[TKUIHelper colorWithHexString:@"#FFFFFF"];
    
    [self.view addSubview:self.backBtn];
    [self.view addSubview:self.cleanBtn];
    //h5有传展示信息就展示
    if (self.h5Params[@"showInfo"]) {
        [self.view addSubview:self.showTipLabel];
    }
    [self.view addSubview:self.submitBtn];
    [self.view addSubview:self.signView];
    
    CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
    self.view.transform = transform;
}

- (BOOL)prefersStatusBarHidden
{
    return YES;
}

#pragma mark btnAction
/**
 *  <AUTHOR> 2018年09月04日18:00:23
 *  返回事件
 */
-(void)backAction:(UIButton *)sender{
    [self dismissViewControllerAnimated:YES completion:nil];
}

/**
 *  <AUTHOR> 2018-09-04 18:26:37
 *  清除签名事件
 */
-(void)cleanAction:(UIButton *)sender{
     [self.signView clean];
}

/**
 *  <AUTHOR> 2018-09-04 18:26:37
 *  提交签名事件
 */
-(void)submitAction:(UIButton *)sender{
    if(_signView.strokeCount == 0) return;
    
    NSData* imgData =  UIImagePNGRepresentation([_signView catchImage]);
    
    UIImage *signImage = [UIImage imageWithData:imgData];
    
    if(self.delegate && [self.delegate respondsToSelector:@selector(tkSignatureDidComplete:)])
    {
        [self.delegate tkSignatureDidComplete:signImage];
    }
    [self dismissViewControllerAnimated:YES completion:nil];

    
}

#pragma mark lazyloading

/**
 *  <AUTHOR> 2018年09月04日17:58:24
 *  返回按钮懒加载
 *  @return UIButton
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        float height=20;
        float width=height;
        float Y=(TK_SingTopHeight-height)/2;
        float X=21;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(X, Y, width, height)];
        [_backBtn setBackgroundImage:[UIImage imageNamed:@"TKOpenResource.bundle/Resources/TKOpenPlugin60022/backSig.png"] forState:UIControlStateNormal];
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
        
    }
    return _backBtn;
}
/**
 *  <AUTHOR> 2018-09-04 18:26:21
 *  清除按钮懒加载
 *  @return UIButton
 */
-(UIButton *)cleanBtn{
    if (!_cleanBtn) {
        float height=20;
        float width=height;
        float Y=(TK_SingTopHeight-height)/2;
        float X=116;
        _cleanBtn=[[UIButton alloc] initWithFrame:CGRectMake(X, Y, width, height)];
        [_cleanBtn setBackgroundImage:[UIImage imageNamed:@"TKOpenResource.bundle/Resources/TKOpenPlugin60022/cleanSig.png"] forState:UIControlStateNormal];
        [_cleanBtn addTarget:self action:@selector(cleanAction:) forControlEvents:UIControlEventTouchUpInside];
        
    }
    return _cleanBtn;
}

/**
 *  <AUTHOR> 2018年09月04日19:03:49
 *  信息展示文本懒加载
 *  @return UILabel
 */
-(UILabel *)showTipLabel{
    if (!_showTipLabel) {
        _showTipLabel=[[UILabel alloc] init];
        NSString *divString=[NSString stringWithFormat:@"<span style=\"font-size:14px;color:#C21A16;font-family:PingFang SC\">%@</span>",self.h5Params[@"showInfo"]];
        NSData *data = [divString dataUsingEncoding:NSUnicodeStringEncoding];
        NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType};
        NSMutableAttributedString   *attStr = [[NSMutableAttributedString alloc]initWithData:data options:options documentAttributes:nil error:nil];
        _showTipLabel.attributedText=attStr;
        _showTipLabel.numberOfLines=0;
        //设置文本内容后计算出文本占用空间大小
        CGSize showTipSize = [_showTipLabel sizeThatFits:CGSizeMake(self.view.TKHeight, TK_SingTopHeight)];
        float X=(self.view.TKHeight-showTipSize.width)/2;
        float Y=(TK_SingTopHeight-showTipSize.height)/2;
        _showTipLabel.frame=CGRectMake(X, Y, showTipSize.width, showTipSize.height);
        _showTipLabel.textAlignment=NSTextAlignmentCenter;
    }
    return _showTipLabel;
}

/**
 *  <AUTHOR> 2018年09月04日19:25:34
 *  提交按钮懒加载
 *  @return UIButton
 */
-(UIButton *)submitBtn{
    if (!_submitBtn) {
        float height=20;
        float width=48;
        float Y=(TK_SingTopHeight-height)/2;
        float X=self.view.TKHeight-width-20;
        _submitBtn=[[UIButton alloc] initWithFrame:CGRectMake(X, Y, width, height)];
        _submitBtn.titleLabel.font=[UIFont systemFontOfSize:14.0f];
        [_submitBtn setTitle:@"确认" forState:UIControlStateNormal];
        [_submitBtn setTitleColor:[TKUIHelper colorWithHexString:self.mainColorString] forState:UIControlStateNormal];
        [_submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
        
    }
    return _submitBtn;
}

/**
 *  <AUTHOR> 2018年09月04日19:25:34
 *  签名区域懒加载
 *  @return UIButton
 */
-(TKTouchView *)signView{
    if (!_signView) {
        float X=0;
        float Y=TK_SingTopHeight;
        float width=self.view.TKHeight;
        float height=self.view.TKWidth-TK_SingTopHeight;
        _signView=[[TKTouchView alloc] initWithFrame:CGRectMake(X, Y, width, height)];
        _signView.backgroundColor=self.view.backgroundColor;
    }
    return _signView;
}
@end
