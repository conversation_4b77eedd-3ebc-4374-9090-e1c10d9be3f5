//
//  TKTouchView.h
//  手势签名
//
//  Created by yulei on 16/8/12.
//

#import <UIKit/UIKit.h>

@interface TKTouchView : UIView
@property UIColor * lineColor;
@property CGFloat lineWidth;
@property UILabel *titleLabel;
@property BOOL imageIsHd; //是否获取高清图片
/**
 *  清除上一笔
 */
-(void)undo;
/**
 *  清除全部
 */
-(void)clean;
/**
 *  得到所有轨迹的点
 */
-(NSArray<NSArray *> *)catchPoints;
/**
 *  得到当前轨迹的图片
 */
-(UIImage*) catchImage;
-(UIImage*) catchImage:(CGSize) size;

/**
 *  当前笔画数
 *
 */
- (NSUInteger)strokeCount;
@end
