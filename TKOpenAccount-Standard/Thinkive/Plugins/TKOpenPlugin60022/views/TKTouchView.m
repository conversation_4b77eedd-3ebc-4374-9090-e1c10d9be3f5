//
//
//  Created by yule<PERSON> on 16/8/12.
//

#import "TKTouchView.h"
@implementation TKTouchView
{
    UIBezierPath *currBeizerPath;
    NSMutableArray<UIBezierPath*> *pathArray;
}

-(void)layoutSubviews
{
    [super layoutSubviews];
    //     _titleLabel.center = CGPointMake(CGRectGetWidth(self.frame) / 2.0, CGRectGetHeight(self.frame) / 2.0);
    
    _titleLabel.frame = CGRectMake(0, CGRectGetHeight(self.frame) - 50, CGRectGetWidth(self.frame), 44);
}

-(instancetype)initWithCoder:(NSCoder *)aDecoder
{
    self = [super initWithCoder:aDecoder];
    if (self) {
        [self viewInit];
    }
    return self;
    
}

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self viewInit];
    }
    return self;
}
-(void) viewInit
{
    self.backgroundColor=[UIColor grayColor];
    _lineColor = [UIColor blackColor];
    _lineWidth = 4.0;
    [self setMultipleTouchEnabled:NO];
    pathArray = [NSMutableArray array];
    _titleLabel = [[UILabel alloc]init];
    _titleLabel.font = [UIFont systemFontOfSize:16.0f];
    _titleLabel.textColor = [UIColor colorWithRed:169/255.0 green:169/255.0 blue:169/255.0 alpha:1.0];
    _titleLabel.text = @"请在此区间手写签名...";
    _titleLabel.textAlignment = NSTextAlignmentCenter;
    [_titleLabel sizeToFit];
    [self addSubview:_titleLabel];
}

- (BOOL)isMultipleTouchEnabled
{
    return NO;
}

- (NSUInteger)strokeCount
{
    return pathArray.count;
}
/**
 *  清除笔画
 *
 *  @param sender
 */
-(void)clean
{
    _titleLabel.hidden = NO;
    [pathArray removeAllObjects];
    [self setNeedsDisplay];
}

-(UIImage*) catchImage:(CGSize) size
{
    UIImage *img =[self catchImage];
    UIImage *newImage = [self scaleImage:img ToSize:size];
    return newImage;
}

-(UIImage*) catchImage
{
    CGFloat scale = 1.0;
    if(_imageIsHd)
    {
        scale = [UIScreen mainScreen].scale;
    }
    UIGraphicsBeginImageContextWithOptions(self.bounds.size, NO,scale);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetStrokeColorSpace(context, CGColorSpaceCreateDeviceRGB());
    CGContextSetLineCap(context, kCGLineCapRound);
    [self.lineColor setStroke];
    CGContextSetLineWidth(context, self.lineWidth);
    for (UIBezierPath *path in pathArray) {
        CGContextAddPath(context, path.CGPath);
    }
    CGContextStrokePath(context);
    UIImage *catchImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return catchImage;
}

-(NSArray<NSArray *> *)catchPoints
{
    NSMutableArray *pathPointArray = [NSMutableArray array];
    for (UIBezierPath *path in pathArray) {
        NSMutableArray *pointArray = [NSMutableArray array];
        CGPathApply(path.CGPath, (void *)pointArray, pathOpenFunction);
        [pathPointArray addObject:pointArray];
    }
    return pathPointArray;
    
}
void pathOpenFunction(void *info, const CGPathElement *element)
{
    NSMutableArray *pointArray = (__bridge NSMutableArray *)(info);
    CGPoint point = element->points[0];
    [pointArray addObject:[NSValue valueWithCGPoint:point]];
}

-(void)touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event{
    currBeizerPath = [[UIBezierPath alloc]init];
    currBeizerPath.lineCapStyle = kCGLineCapRound;
    [currBeizerPath setLineWidth:self.lineWidth];
    UITouch *touch = [touches anyObject];
    CGPoint touchPoint = [touch locationInView:self];
    [currBeizerPath moveToPoint:touchPoint];
    _titleLabel.hidden = YES;
    UIButton *cBtn = [self.superview viewWithTag:1000];
    [cBtn setEnabled:YES];
//    [cBtn setTitle:@"清空" forState:UIControlStateNormal];
}

//移动过程中记录各个点
-(void)touchesMoved:(NSSet *)touches withEvent:(UIEvent *)event{
    UITouch *touch = [touches anyObject];
    CGPoint touchPoint = [touch locationInView:self];
    [currBeizerPath addLineToPoint:touchPoint];
    [self setNeedsDisplay];
}

- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event
{
    [pathArray addObject:currBeizerPath];
    [self setNeedsDisplay];
    currBeizerPath = nil;
}

-(void)undo
{
    if(pathArray.count > 0)
    {
        [pathArray removeLastObject];
    }
    if(pathArray.count == 0)
    {
        _titleLabel.hidden = NO;
    }
    [self setNeedsDisplay];
}

- (void)touchesCancelled:(NSSet *)touches withEvent:(UIEvent *)event
{
    [self touchesEnded:touches withEvent:event];
}

//绘制
-(void)drawRect:(CGRect)rect{
    [super drawRect:rect];
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetLineCap(context, kCGLineCapRound);
    CGContextSetLineJoin(context, kCGLineJoinRound);
    UIColor *strokeColor = self.lineColor;
    [strokeColor setStroke];
    for (UIBezierPath *path in pathArray) {
        [path stroke];
    }
    [currBeizerPath stroke];
}

//等比例缩放
-(UIImage*)scaleImage:(UIImage *)image ToSize:(CGSize)size
{
    CGFloat width = CGImageGetWidth(image.CGImage);
    CGFloat height = CGImageGetHeight(image.CGImage);
    
    float verticalRadio = size.height*1.0/height;
    float horizontalRadio = size.width*1.0/width;
    
    float radio = 1;
    if(verticalRadio>1 && horizontalRadio>1)
    {
        radio = verticalRadio > horizontalRadio ? horizontalRadio : verticalRadio;
    }
    else
    {
        radio = verticalRadio < horizontalRadio ? verticalRadio : horizontalRadio;
    }
    width = width*radio;
    height = height*radio;
    int xPos = (size.width - width)/2;
    int yPos = (size.height-height)/2;
    // 创建一个bitmap的context
    // 并把它设置成为当前正在使用的context
    UIGraphicsBeginImageContext(size);
    // 绘制改变大小的图片
    [image drawInRect:CGRectMake(xPos, yPos, width, height)];
    // 从当前context中创建一个改变大小后的图片
    UIImage* scaledImage = UIGraphicsGetImageFromCurrentImageContext();
    // 使当前的context出堆栈
    UIGraphicsEndImageContext();
    
    // 返回新的改变大小后的图片
    return scaledImage;
}
@end
