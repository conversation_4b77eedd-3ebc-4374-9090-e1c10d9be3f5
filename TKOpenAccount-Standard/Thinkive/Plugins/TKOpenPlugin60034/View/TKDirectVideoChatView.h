//
//  TKDirectVideoChatView.h
//  TKOpenAccount-Standard
//  视频见证页面
//  Created by <PERSON>ie on 2019/9/7.
//  Copyright © 2019 thinkive. All rights reserved.
//




@protocol TKDirectVideoChatDelegate <NSObject>

//挂断视频
-(void)hangUp;

//切换摄像头
-(void)switchCamera;
@end

@interface TKDirectVideoChatView : UIView

@property (nonatomic, strong) id<TKDirectVideoChatDelegate> delegate;
@property (nonatomic, strong) UIView *meVideoView;//我的视频画面
@property (nonatomic, strong) UIImageView *remoteVideoView;//远程客服视频画面
@property (nonatomic, strong) UILabel *networkLabel;//网络情况提示文本
@property (nonatomic, strong) UILabel *topTipLabel;//顶部文本提示

/**
 *<AUTHOR> 2019年09月07日14:20:15
 *@初始化新版视频见证页面
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams;

/**
*<AUTHOR> 移除等待层
*@初始化新版视频见证页面
*/
-(void)removeWaitTip;

/**
*<AUTHOR> 修改等待提示语
*/
-(void)changeWaitTipText:(NSString *)string;

/**
*<AUTHOR> 展示坐席提示语
*/
-(void)appendServerMessage:(NSString *)msg;

/**
*<AUTHOR> 风险协议等客户阅读协议展示
*/
-(void)showReadAgree:(NSMutableDictionary *)param;

/**
*<AUTHOR> 坐席信息展示
*/
-(void)showServiceInfo:(NSString *)info;
@end


