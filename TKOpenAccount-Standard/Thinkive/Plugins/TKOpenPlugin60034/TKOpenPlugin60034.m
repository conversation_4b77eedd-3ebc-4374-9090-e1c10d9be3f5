//
//  TKOpenPlugin60034.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/3/14.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKOpenPlugin60034.h"
#import "TKDirectVideoViewController.h"

@interface TKOpenPlugin60034()<TKDirectVideoWitnessDelegate>
{
     ResultVo *_resultVo;// 返回的结果
    
     NSMutableDictionary *h5Params;
}
@end

@implementation TKOpenPlugin60034

- (ResultVo *)serverInvoke:(id)param{
    
 
    
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    h5Params = reqParam;
    
    // 检测入参是否有误
    if ([self IsErrorParam:reqParam]) {
        return _resultVo;
    }
    
     dispatch_async(dispatch_get_main_queue(), ^{
         
         //是否在调用插件前展示介绍页面
         if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
             [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Camera),@(TKAuthorizationType_Audio)] authCallBacks:nil btnCallBack:^{
                 [self.currentViewCtrl tkIsMicrophonePermissions:^{
                     
                     [self.currentViewCtrl tkIsCameraPermissions:^{
                             
                         [self jumpToVideoController:reqParam];
                     }];
                     
                 }];
             }];
         }else{
             [self.currentViewCtrl tkIsMicrophonePermissions:^{
                 
                 [self.currentViewCtrl tkIsCameraPermissions:^{
                         
                     [self jumpToVideoController:reqParam];
                 }];
                 
             }];
         }
         


       });
    
    
    
    return _resultVo;
}

- (BOOL)IsErrorParam:(NSMutableDictionary *)reqParam
{
    ResultVo *resultVo = [[ResultVo alloc] init];
    
    _resultVo = resultVo;

    if ([TKStringHelper isEmpty:@"videoIp"]) {
        
        resultVo.errorNo = -6003401;
        resultVo.errorInfo = @"视频服务ip不能为空";
        return YES;
        
    }else if([TKStringHelper isEmpty:@"videoPort"]) {
        resultVo.errorNo = -6003402;
        resultVo.errorInfo = @"视频服务端口不能为空";
        return YES;
    }else if([TKStringHelper isEmpty:@"loginName"]) {
        resultVo.errorNo = -6003403;
        resultVo.errorInfo = @"登录名不能为空";
        return YES;
    }else if([TKStringHelper isEmpty:@"roomId"]&&[TKStringHelper isEmpty:@"roomName"]) {
        resultVo.errorNo = -6003404;
        resultVo.errorInfo = @"房间号或房间名称不能为空";
        return YES;
    }
    return NO;
}

- (void)jumpToVideoController:(id)h5Params
{
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    TKDirectVideoViewController *videoCtr=[[TKDirectVideoViewController alloc] initWithParam:h5Params];
    videoCtr.delegate=self;
    //集成方self.currentViewCtrl拿不到对应控制器情况下，currentViewCtrl参数让集成方把控制器传过来
    if([h5Params[@"currentViewCtrl"] isKindOfClass:[UIViewController class]]){
        [h5Params[@"currentViewCtrl"]  presentViewController:videoCtr animated:YES completion:nil];
    }else{
        [self.currentViewCtrl presentViewController:videoCtr animated:YES completion:nil];
    }
    
}

#pragma mark TKDirectVideoWitnessDelegate
- (void)tkDirectVideoWitnessResult:(NSString *)wResult {
    dispatch_async(dispatch_get_main_queue(), ^{
           
        [UIApplication sharedApplication].idleTimerDisabled = NO;
    });
       

    NSMutableDictionary *reqParam = [NSMutableDictionary dictionary];
       
    reqParam[@"funcNo"]=@"60051";
       
    reqParam[@"error_no"]=@"0";
       
     
    NSString *wResultDecode=  [wResult stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
//    其他视频结果：
//    app:10001，代表用户主动退出视频见证
//    app:10002，代表用户连接视频失败(连接、登录、进入房间)
//    app:10003，代表坐席连接视频失败(等待20秒超时)
//    app:10004，代表坐席主动退出视频见证
//    app:10009，代表其他异常
          
    reqParam[@"message"]=wResultDecode;
   
    [self iosCallJsWithParam:reqParam];
           
    return;
}

@end
