//
//  TKDirectVideoViewController.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/3/14.
//  Copyright © 2020 thinkive. All rights reserved.
//


/****
 
 @protocol TKDirectVideoWitnessDelegate
 
 @description 视频见证结果委托
 
 */
@protocol TKDirectVideoWitnessDelegate <NSObject>

@required

- (void)tkDirectVideoWitnessResult:(NSString*)wResult;

@end


@interface TKDirectVideoViewController : TKBaseViewController

@property(nonatomic,assign) id<TKDirectVideoWitnessDelegate> delegate;


/**
 <AUTHOR> 2020年03月14日13:58:38
 @初始化TKDirectVideoViewController
 @return TKDirectVideoViewController
 */
-(instancetype)initWithParam:(NSMutableDictionary *)param;
@end


