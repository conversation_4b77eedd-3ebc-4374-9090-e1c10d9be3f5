//
//  TKDirectVideoViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/3/14.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKDirectVideoViewController.h"
#import "TKDirectVideoChatView.h"
#import "TKDirectVideoModel.h"
#import "TKVideoAlertView.h"
#import "TKSmartTwoVideoManager.h"

@interface TKDirectVideoViewController ()<TKDirectVideoChatDelegate,UIAlertViewDelegate,TKVideoAlertViewDelegate,TKSmartTwoVideoManagerDelegate>
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@property (nonatomic, strong) TKDirectVideoChatView *videoChatView;//视频见证页面
@property (nonatomic, strong) TKDirectVideoModel *videoModel;

@property (nonatomic, strong) NSTimer *videoLengthTimer; //见证时长定时器
@property (nonatomic, assign) int longestTime;//视频见证时间
@property (nonatomic, strong) TKLayerView  *layerView;//提示layer
@property (nonatomic, strong) TKVideoAlertView *videoAlertView;//视频挂断提示框
@property (nonatomic, strong) id<TKSmartTwoVideoManagerProtocol> smartTwoVideoManager;//智能双向管理类

@end

@implementation TKDirectVideoViewController

/**
<AUTHOR> 2020年03月14日13:58:38
@初始化TKDirectVideoViewController
@return TKDirectVideoViewController
*/
-(instancetype)initWithParam:(NSMutableDictionary *)param{
    self=[super init];
    if (self) {
        self.requestParams=param;
    }
    return self;
}


- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    //埋点
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    [TKDirectVideoModel shareInstance].startInitVideoTime=CFAbsoluteTimeGetCurrent();
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessWithoutQueue progress:TKPrivateEventProgressNone result:TKPrivateEventResultSuccess orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressNone eventDic:eventDic];
    
    [TKDirectVideoModel shareInstance].isFrontCamera=YES;//当前使用前置摄像头
    [TKDirectVideoModel shareInstance].witnessResult = @"app:10001";
    [TKDirectVideoModel shareInstance].witnessInfo = @"";
    [TKDirectVideoModel shareInstance].isEnqueueSuccess = NO;
    [TKDirectVideoModel shareInstance].isStartingVideo = NO;
    [TKDirectVideoModel shareInstance].isTransBufferMsg = NO;
    [TKDirectVideoModel shareInstance].isShowAlert=NO;
    [TKDirectVideoModel shareInstance].userVideoId = 0;
    [TKDirectVideoModel shareInstance].seatVideoId = 0;
    [TKDirectVideoModel shareInstance].tkCountDown=20;
    [TKDirectVideoModel shareInstance].isDirectVideo=YES;
    
    [self.view addSubview:self.videoChatView];
    self.smartTwoVideoManager.contentView=self.videoChatView.meVideoView;
    self.smartTwoVideoManager.remoteContentView=self.videoChatView.remoteVideoView;
    
    
    [self.smartTwoVideoManager startSmartTwoVideo:self.requestParams[@"videoIp"] withPort:[self.requestParams[@"videoPort"] intValue]];
}



-(void)startVidoeTime{
    //启动录制时长
    _videoLengthTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(videoLengthAction) userInfo:nil repeats:YES];
    self.longestTime=0;
}

/**
 <AUTHOR> 2022年05月25日16:19:58
 @见证时长
 */
-(void)videoLengthAction{
   self.longestTime++;
   NSDate *d = [NSDate dateWithTimeIntervalSince1970:self.longestTime];
   NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];

   if (self.longestTime/3600 >= 1) {
       [formatter setDateFormat:@"HH:mm:ss"];
   } else {
       [formatter setDateFormat:@"mm:ss"];
   }

    self.videoChatView.topTipLabel.text=[NSString stringWithFormat:@"%@",[formatter stringFromDate:d]] ;
    
}

#pragma mark TKSmartTwoVideoManagerDelegate


/**
 <AUTHOR> 2023年01月28日14:25:14
 @弹窗提示
 @param title标题
 @param describe描述
 @param cancelBtnTitle取消按钮文本
 @param takeBtnTitle确认按钮文本
 */
-(void)alertSmartTwoVideoTip:(NSString *)title  describe:(NSString *)describe cancelBtnTitle:(NSString *)cancelBtnTitle takeBtnTitle:(NSString *)takeBtnTitle{
    [self.view addSubview:self.videoAlertView];
    self.videoAlertView.describeLabel.text=describe;
    self.videoAlertView.titleLabel.text=title;
    if ([TKStringHelper isEmpty:cancelBtnTitle]) {
        [self.videoAlertView setOnlyBtnTitle:takeBtnTitle];
        
    }else{
        [self.videoAlertView.cancelBtn setTitle:cancelBtnTitle forState:UIControlStateNormal];
        [self.videoAlertView.takeBtn setTitle:takeBtnTitle forState:UIControlStateNormal];
    }

}


/***
 视频画面显示
 */
- (void)showSmartTwoVideoChatView{
    //埋点
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    CFAbsoluteTime currentTime=CFAbsoluteTimeGetCurrent();
    eventDic[@"costTime"]=[NSString stringWithFormat:@"%d",(int)(currentTime-[TKDirectVideoModel shareInstance].startInitVideoTime)];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressNone result:TKPrivateEventResultSuccess orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressVideoReady eventDic:eventDic];
    
    [self removeVideoViewWaitTip];
    //接通提示
    [self showVideoNetStatus:@"已接通" withColor:@"#333333"];
}

//显示网络状态展示
-(void)showSmartTwoVideoNetStatus:(NSString *)info withColor:(NSString *)color{
    [self showVideoNetStatus:info withColor:color];
}

//显示网络上下行网速
-(void)smartTwoVideoNetWorkUpDownTip:(NSString *)string{
    self.videoChatView.networkLabel.text=string;
}

//显示风险协议阅读文本
-(void)showSmartTwoVideoRead:(NSMutableDictionary *)param{
    [self showVideoRead:param];
}
//显示toast提示
-(void)showSmartTwoVideoToast:(NSString *)string{
    [self showToast:string];
}

//显示坐席消息
-(void)showSmartTwoVideoSeatMessage:(NSString *)message{
    [self showSeatMessage:message];
}
//显示坐席信息
-(void)showSmartTwoVideoServiceInfo:(NSString *)info{
    [self showVideoServiceInfo:info];
}

//开始视频计时
-(void)startSmartTwoVidoeTime{
    [self startVidoeTime];
}
//关闭视频
-(void)endSmartTwoVidoe{
    //埋点
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventNone progress:TKPrivateEventProgressEnd result:TKPrivateEventResultNone orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressNone eventDic:eventDic];
    
    if (self.videoLengthTimer) {
        [self.videoLengthTimer invalidate];
        self.videoLengthTimer = nil;
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(tkDirectVideoWitnessResult:)]) {
        
        [self.delegate tkDirectVideoWitnessResult:[TKDirectVideoModel shareInstance].witnessResult];
    }
    
    [self dismissViewControllerAnimated:YES completion:nil];
}


#pragma mark 页面修改事件

//移除视频等待层提示画面
-(void)removeVideoViewWaitTip{
    [self.videoChatView removeWaitTip];
}

//修改提示语
-(void)changeTipText:(NSString *)string{
    [self.videoChatView changeWaitTipText:string];
}

//显示坐席消息
-(void)showSeatMessage:(NSString *)message{
    //埋点
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessReceiveMessage progress:TKPrivateEventProgressNone result:TKPrivateEventResultNone orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressNone eventDic:eventDic];
    
    [self.videoChatView appendServerMessage:message];
}

//显示坐席信息
-(void)showVideoServiceInfo:(NSString *)info{
    [self.videoChatView showServiceInfo:info];
}

//显示网络状态展示
-(void)showVideoNetStatus:(NSString *)info withColor:(NSString *)color{
    [self.layerView showTip:info position:TKLayerPosition_Bottom textColor:@"#FFFFFF" bgColor:color];
}

//显示风险协议阅读文本
-(void)showVideoRead:(NSMutableDictionary *)param{
    [self.videoChatView showReadAgree:param];
}

//显示toast提示
-(void)showToast:(NSString *)string{
    [self.layerView showTip:string position:TKLayerPosition_Center textColor:@"#FFFFFF" bgColor:@"#FF0000"];
}


-(void)cancelAction{
    
    [self.videoAlertView removeFromSuperview];
    //退出视频
    [self.smartTwoVideoManager stopSmartTwoVideo];
}

//继续按钮事件
-(void)takeVideoAction{
    
    [self.videoAlertView removeFromSuperview];
    [TKDirectVideoModel shareInstance].isShowAlert = NO;
}


#pragma mark TKVideoAlertViewDelegate


//取消按钮事件
-(void)cancelVideoBtnAction{
    

        [self cancelAction];
    
}



//继续按钮事件
-(void)takeVideoBtnAction{

 [self takeVideoAction];

}

//独立按钮事件
-(void)onlyVideoBtnAction{
    [self cancelAction];
}



#pragma mark TKDirectVideoChatDelegate
-(void)hangUp{

    if (![TKDirectVideoModel shareInstance].isShowAlert) {
        [TKDirectVideoModel shareInstance].isShowAlert = YES;
        [self.view addSubview:self.videoAlertView];
        self.videoAlertView.describeLabel.text=[NSString stringWithFormat:@"自行退出视频见证将导致本次视频提交失败，请您等待%@结束见证。",[TKDirectVideoModel shareInstance].serviceTipString];
        self.videoAlertView.titleLabel.text=@"视频录制提示";
        [self.videoAlertView.cancelBtn setTitle:@"执意退出" forState:UIControlStateNormal];
        [self.videoAlertView.takeBtn setTitle:@"继续见证" forState:UIControlStateNormal];
    }
}

//切换摄像头
-(void)switchCamera{
    [self.smartTwoVideoManager switchCameraSmartTwoVideo:[TKDirectVideoModel shareInstance].isFrontCamera];
    [TKDirectVideoModel shareInstance].isFrontCamera=![TKDirectVideoModel shareInstance].isFrontCamera;
}


#pragma mark lazyloading
/**
 <AUTHOR> 2019年09月07日14:16:48
 @初始化懒加载queueView
 @return queueView
 */
-(TKDirectVideoChatView *)videoChatView{
    if (!_videoChatView) {
        _videoChatView=[[TKDirectVideoChatView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, self.view.frame.size.height) requestParams:self.requestParams];
        _videoChatView.delegate=self;
    }
    return _videoChatView;
}

/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载提示layer
 @return 提示layer
 */
-(TKLayerView *)layerView{
    if (!_layerView) {
        _layerView=[[TKLayerView alloc] initContentView:self.view withBtnTextColor:nil];
        [_layerView setShowTipDuration:2];
    }
    return _layerView;
}

/**
 <AUTHOR> 2022年05月25日09:43:41
 @初始化懒加载videoAlertView
 @return videoAlertView
 */
-(TKVideoAlertView *)videoAlertView{
    if (!_videoAlertView) {
        _videoAlertView=[[TKVideoAlertView alloc] initWithFrame:self.view.frame requestParams:self.requestParams];
        _videoAlertView.delegate=self;
    }
    return _videoAlertView;
}

/**
<AUTHOR> 2023年01月28日14:20:413
@初始化懒加智能双向管理类
@return 智能双向管理类
*/
- (id)smartTwoVideoManager {
    if (!_smartTwoVideoManager) {
        _smartTwoVideoManager = [[TKSmartTwoVideoManager alloc] initWithConfig:self.requestParams];
        _smartTwoVideoManager.delegate = self;
    }
    return _smartTwoVideoManager;
}
@end
