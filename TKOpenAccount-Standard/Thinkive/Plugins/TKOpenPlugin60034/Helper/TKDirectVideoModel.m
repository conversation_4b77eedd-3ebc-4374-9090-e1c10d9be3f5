//
//  TKDirectVideoModel.m
//  TKOpenAccount-Standard
//
//  Created by Vie on 2020/3/14.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKDirectVideoModel.h"

@implementation TKDirectVideoModel
+(TKDirectVideoModel *)shareInstance
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc]init];
    });
    return instance;
}
@end
