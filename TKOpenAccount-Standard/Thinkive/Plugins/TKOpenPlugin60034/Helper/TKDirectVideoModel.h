//
//  TKDirectVideoModel.h
//  TKOpenAccount-Standard
//
//  Created by Vie on 2020/3/14.
//  Copyright © 2020 thinkive. All rights reserved.
//


@interface TKDirectVideoModel : NSObject
//单例
+(TKDirectVideoModel *)shareInstance;

@property (nonatomic, assign) BOOL isShowAlert;//是否弹出过提示窗
@property (nonatomic, assign) BOOL isEnqueueSuccess;//是否已排上队
@property (nonatomic, assign) BOOL isStartingVideo;//是否连接了视频
@property (nonatomic, assign) BOOL isTransBufferMsg;//是否收到了坐席消息
@property (nonatomic, assign) int userVideoId;//客户视频分配id
@property (nonatomic, assign) int seatVideoId;//坐席视频分配id
@property (nonatomic, strong) NSString *witnessResult;//记录视频结束错误字符
@property (nonatomic, assign) int tkCountDown;//进入房间后等待坐席最长时间
@property (nonatomic, strong) NSString *userVideoIdString;//客户视频分配id
@property (nonatomic, strong) NSString *seatVideoIdString;//坐席视频分配id
@property (nonatomic, assign) BOOL isCancelLineingUp;//是否取消排队了
@property (nonatomic, strong) NSString *staffTips;//排队获取到的坐席提示信息
@property(nonatomic, assign) int  tipType;//提示语类型记录，也只记录较小值
@property (nonatomic, strong) NSString *extParam;//排队bus拿到需要返回给h5的扩展参数，json字符串
@property (nonatomic, strong) NSString* witRoomId;//记录视频房间号
@property (nonatomic, assign) BOOL  isFrontCamera;//是否是前置相机
@property (nonatomic, assign) BOOL aExit;//是否异常结束视频
@property (nonatomic, assign) BOOL isDirectVideo;//是否是60034直接进视频模式，要区分错误码
@property (nonatomic, assign) CFAbsoluteTime startInitVideoTime;//记录开始初始化视频时间
@property (nonatomic, strong) NSString *witnessInfo;//记录视频结束错误info
@property (nonatomic, assign) BOOL isQueueStaffExist;//排队过程中是否有坐席
@property (nonatomic, strong) NSMutableDictionary *videoServerInfo;//视频服务器房间特殊处理情况（目前有即构）
@property (nonatomic, strong) NSString *serviceTipString;//坐席字样；如果h5有传入调整走h5传入值
@property (nonatomic, strong) NSString *queueBigMsg;//双向视频排队过程中大字字提示语
@property (nonatomic, strong) NSString *queueSubMsg;//双向视频排队过程中小字提示语
@end

