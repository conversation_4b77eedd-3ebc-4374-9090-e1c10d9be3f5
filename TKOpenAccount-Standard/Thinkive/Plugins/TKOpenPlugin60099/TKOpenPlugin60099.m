//
//  TKOpenPlugin60099.m
//  TKOpenAccount-Standard
//
//  Created by Clover on 2016/8/17.
//  Copyright © 2016年 thinkive. All rights reserved.
//

#import "TKOpenPlugin60099.h"

@implementation TKOpenPlugin60099

- (ResultVo *)serverInvoke:(id)param{
    
    ResultVo *rVo = [[ResultVo alloc] init];
    
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([[param getStringWithKey:@"actionType"] isEqualToString:@"externalRadio"] ) {
            
            [[NSNotificationCenter defaultCenter] postNotificationName:param[@"externalRadioName"]?param[@"externalRadioName"]:@"tkOpenExternalRadio" object:param];
            
        }else{
            [[NSNotificationCenter defaultCenter] postNotificationName:TK_INTERRUPTSDK_NOTIFICATION object:param];
        }
    });
    return rVo;
}

@end
