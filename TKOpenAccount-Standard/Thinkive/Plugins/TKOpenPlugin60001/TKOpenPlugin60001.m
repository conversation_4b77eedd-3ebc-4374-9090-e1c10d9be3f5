//
//  TKOpenPlugin60001.h
//  TKApp
//
//  Created by 叶璐 on 15/4/17.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKOpenPlugin60001.h"


@implementation TKOpenPlugin60001

/**
 *  <AUTHOR> 2014-11-27 12:11:58
 *
 *  功能描述：返回签名后的数据内容（数据签名）
 *  参考插件：对应phoneGap的signPlugin插件
 *
 *  @param userId       用户ID
 *  @param type         证书类型(tw:天威，zd:中登)
 *  @param mediaId      媒体协议ID
 *  @param content      媒体协议文本
 *  @param funcNo       功能号：60001
 *
 *  @return 
 *      mediaId         字段有返回值表示文件存在，否则不存在
 *      cipherText      签名后的协议文本
 */
-(ResultVo *)serverInvoke:(id)param
{
    NSMutableDictionary *reqParam = (NSMutableDictionary *)param;
    NSString *userId  = reqParam[@"userId"];
    if ([param[@"type"] isEqualToString:@"tw_new"]){
        userId = [NSString stringWithFormat:@"%@_tw_new",userId];
    }
    NSString *mediaId = reqParam[@"mediaId"];
    NSString *content = reqParam[@"content"];
    
    ResultVo *resultVo = [[ResultVo alloc]init];
    
    if ([TKStringHelper isEmpty:userId]) {
        resultVo.errorNo = -6000101;
        resultVo.errorInfo = @"用户ID不能为空!";
        
        return resultVo;
    }
    
    if ([TKStringHelper isEmpty:mediaId]) {
        resultVo.errorNo = -6000103;
        resultVo.errorInfo = @"媒体协议ID不能为空!";
        
        return resultVo;
    }
    
    if ([TKStringHelper isEmpty:content]) {
        resultVo.errorNo = -6000104;
        resultVo.errorInfo = @"媒体协议文本不能为空!";
        
        return resultVo;
    }

    NSMutableDictionary *dataRow = [NSMutableDictionary dictionary];
    //处理签名.....
    NSString *signedText;
    if ([param[@"type"] isEqualToString:@"tw_new"]){
        signedText =[TKCertLibHelper sign:content signType:TK_SIGN_TYPE_ATTACH userId:userId];
    }else{
        signedText =[TKCertLibHelper sign:content signType:TK_SIGN_TYPE_DETACH userId:userId];
    }
    
    NSLog(@"%lu" , (unsigned long)signedText.length);
    
    if (signedText) { // 签名成功
        [dataRow setValue:signedText forKey:@"cipherText"];
        [dataRow setValue:mediaId forKey:@"mediaId"];
        resultVo.errorNo = 0;
    }
    else
    {
        resultVo.errorNo = -6000105;
        resultVo.errorInfo = @"文件不存在!";
        [dataRow setValue:signedText forKey:@"cipherText"];
        [dataRow setValue:@"" forKey:@"mediaId"];
    }

    resultVo.results = dataRow;
    return resultVo;
}

@end
