//
//  TKOpenPlugin60010.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/7/13.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "TKOpenPlugin60010.h"
#import <CoreLocation/CoreLocation.h>
#import "TKOpenAccountService.h"
#import "TKOpenController.h"

@interface TKOpenPlugin60010()<CLLocationManagerDelegate>
{
    CLLocationManager *lManager;
    
    TKOpenAccountService *mService;
    
    ResultVo * res;
    
    BOOL flag;
}

@end

@implementation TKOpenPlugin60010

- (ResultVo*)serverInvoke:(id)param
{
    dispatch_async(dispatch_get_main_queue(), ^{
        if (param && param[@"moduleName"]) {
            
            if (self.currentViewCtrl && [self.currentViewCtrl isKindOfClass:[TKBaseWebViewController class]]) {
                
                TKBaseWebViewController *webCtl = (TKBaseWebViewController*)self.currentViewCtrl;
                
                webCtl.tkName = param[@"moduleName"];
            }
        }
    });
    
    
    TKLogInfo(@"获取当前位置的经纬度。");

    res = [[ResultVo alloc] init];
    
    //定位后异步返回地址和经纬度信息
    mService = [[TKOpenAccountService alloc] init];
    
//    [self fetchLocation];
    
    //是否在调用插件前展示介绍页面
    if ([(NSString *)[TKSystemHelper getMemcacheWithKey:@"isNeedTKAuthorIntroduce"] isEqualToString:@"1"]) {
        [TKAuthorizationHelper requestAuthorization:@[@(TKAuthorizationType_Location)] authCallBacks:nil btnCallBack:^{
                    //定位后同步返回
            dispatch_async(dispatch_get_main_queue(), ^{
                
                [self fetchLocation];
            });
        }];
    }else{
        //定位后同步返回
        dispatch_async(dispatch_get_main_queue(), ^{
            
            [self fetchLocation];
        });
    }
    
    
    return res;
}

#pragma mark -启动定位
- (void)fetchLocation
{
    lManager = [[CLLocationManager alloc] init];
    
    if (![CLLocationManager locationServicesEnabled]) {
        
        [self tkOpenAlertControllerAuthority];
        
        return;
        
    }
    
    if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined) {
        
        if([[[UIDevice currentDevice] systemVersion] floatValue] >= 8.0){
            
            [lManager requestWhenInUseAuthorization];
            
        }
        
    }else if([CLLocationManager authorizationStatus] == kCLAuthorizationStatusDenied || [CLLocationManager authorizationStatus] == kCLAuthorizationStatusRestricted){


        
        [self tkOpenAlertControllerAuthority];
        
        return;
    }
   
    lManager.delegate = self;
    
    lManager.desiredAccuracy = kCLLocationAccuracyBest;
    
    [lManager startUpdatingLocation];
}

-(void)tkOpenAlertControllerAuthority{
    NSString *msg=@"未开启应用定位权限，请进入'设置'－'隐私'－'定位'中开启";
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    
    NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
    
    NSMutableDictionary *cDic;
    
    if (plistPath) {
        
        cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
    }
    if ([TKStringHelper isNotEmpty:cDic[@"LocationTip"]]) {
        msg=cDic[@"LocationTip"];
    }
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"温馨提示" message:msg preferredStyle:UIAlertControllerStyleAlert];
    
    // 2.创建并添加按钮
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"暂不授权" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {

    }];
    [alertController addAction:cancelAction];
    
    // 2.创建并添加按钮
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"去授权" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            #ifdef __IPHONE_8_0
            [TKSystemHelper openAppURLStr:UIApplicationOpenSettingsURLString completionHandler:nil];
            #endif
    }];
    [alertController addAction:okAction];
    [self.currentViewCtrl presentViewController:alertController animated:YES completion:nil];
}


#pragma mark -implement CLLocationManagerDelegate
- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
{
    CLLocation *currLocation = [locations lastObject];
    
    TKLogInfo(@"纬度=%f 经度=%f", currLocation.coordinate.latitude, currLocation.coordinate.longitude);
    
    CLGeocoder *geocoder = [[CLGeocoder alloc] init];
    
    //根据经纬度反向地理编译出地址信息
    [geocoder reverseGeocodeLocation:currLocation completionHandler:^(NSArray *array, NSError *error)
     
     {
         if (error != nil)
             
         {
             TKLogInfo(@"An error occurred = %@", error);
             
             NSMutableDictionary *dic = [NSMutableDictionary dictionary];
             
             [dic setObject:@"60056" forKey:@"funcNo"];
             
             [dic setObject:@"-6001002" forKey:@"errorNo"];
             
             [dic setObject:@"获取经纬度失败" forKey:@"errorInfo"];
             
             [self iosCallJsWithParam:dic];
             
             
         }else{
             
             if (array.count > 0)
             {
                 CLPlacemark *placemark = [array objectAtIndex:0];
                 
                 //获取城市
                 NSString *city = placemark.locality;
                 
                 if (!city) {
                     
                     //四大直辖市的城市信息无法通过locality获得，只能通过获取省份的方法来获得（如果city为空，则可知为直辖市）
                     city = placemark.administrativeArea;
                     
                 }
                 //获取省份
                 NSString *province = placemark.administrativeArea;
                 if (!province) {
                     
                     //四大直辖市的城市信息无法通过locality获得，只能通过获取省份的方法来获得（如果city为空，则可知为直辖市）
                     province = placemark.locality;
                     
                 }
                 
                 NSMutableDictionary *dic = [NSMutableDictionary dictionary];
                 dic[@"province"]=province;
                 dic[@"city"]=city;
                 dic[@"district"]=placemark.subLocality;
                 dic[@"funcNo"]=@"60056";
                 dic[@"errorNo"]=@"0";
                 dic[@"lat"]=[NSNumber numberWithDouble:currLocation.coordinate.latitude];
                 dic[@"lon"]=[NSNumber numberWithDouble:currLocation.coordinate.longitude];
                 
                 NSString *add = [NSString stringWithFormat:@"%@%@%@%@", city, placemark.subLocality,placemark.thoroughfare,placemark.subThoroughfare ? placemark.subThoroughfare:@""];
                 dic[@"address"]=add;
                 
                 NSString *street=[NSString stringWithFormat:@"%@%@",placemark.thoroughfare,placemark.subThoroughfare ? placemark.subThoroughfare:@""];
                 dic[@"street"]=street;
                 
                 //异步调用js将经纬度值传给js处理
                 [self iosCallJsWithParam:dic];
                 
                 
             }else{
                 
                 NSMutableDictionary *dic = [NSMutableDictionary dictionary];
                 
                 [dic setObject:@"60056" forKey:@"funcNo"];
                 
                 [dic setObject:@"-6001001" forKey:@"errorNo"];
                 
                 [dic setObject:@"获取经纬度失败" forKey:@"errorInfo"];
                 
                 [self iosCallJsWithParam:dic];
                 
                 
             }
         }
         
     }];
    
    [manager stopUpdatingLocation];
    
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error
{
    TKLogInfo(@"获取经纬度失败:%@", [error localizedDescription]) ;
    
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    
    [dic setObject:@"60056" forKey:@"funcNo"];
    
    if ([error code] == kCLErrorDenied)
    {
        //访问被拒绝
        TKLogInfo(@"拒绝访问");
        [dic setObject:@"-6001004" forKey:@"errorNo"];
    }else {
        //无法获取位置信息
        TKLogInfo(@"无法获取位置信息");
        [dic setObject:@"-6001001" forKey:@"errorNo"];
    }
    
    [dic setObject:@"获取经纬度失败" forKey:@"errorInfo"];
    
    [self iosCallJsWithParam:dic];
    

}

@end
