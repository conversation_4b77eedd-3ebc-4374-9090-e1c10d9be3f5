//
//  TKOpenController.m
//  TKApp
//
//  Created by 叶璐 on 15/4/27.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKOpenController.h"
#import "MNavViewController.h"
#import <CoreLocation/CoreLocation.h>
#import "TKCommonUtil.h"
#import "TKFxcAccountInfoType.h"
#import "TKOpenAccountService.h"

//#import "TKAppletPluginManager.h"
//#import <FinApplet/FinApplet.h>

#define MODULE_NAME @"open"
#define HANDLE_MODULE @"exit_open"
#define FXC_ACCOUNT_INFO @"fxcAccountInfo"
#define SUPER_AUTHOR @"7X9FVxYHFo1/0hGuh7mHE+1/RVcWBxaNf9IRroe5hxMeT9CVhloh0e0kmq2PagBU"   //超级授权

@interface TKOpenController()<TKWebViewDelegate>
{
    NSString *refreashUrl;
    
    BOOL isLoading ,isLoadFailed, isExited;

}
@property(nonatomic, strong) NSMutableDictionary *extParam;//存储60099插件传过来的参数
@property(nonatomic, strong) NSString *networkErrorUrlString;//网络异常提示地址
@property(nonatomic, strong) NSString *notFindUrlString;//找不到页面404提示地址
@property (nonatomic, assign) BOOL currentNavSupportSwipingBack;//是否主动关闭navigationController得测回返回；yes：主动关闭；no：不做任何处理

- (void)tkAddNavigationPageView;

- (void)tkAddLoadingView;

@end

@implementation TKOpenController


- (instancetype)init
{
    self = [super init];
    if (self) {
        self.tkName = MODULE_NAME;
        self.isUseTKKeyboard=NO;
        self.isUseSSO = YES;
        self.isUseWebViewCachePool = NO;
        self.loadTimeOut=10;
        self.isUseWebViewAutoResize = NO;
        self.exParamInsertBeforeFlagChars=@"#";
        self.isCheckScreenShotRecord=YES;
        if (![[TKAppEngine shareInstance] isRuning]) {
            [[TKAppEngine shareInstance] start];
        }
//        [[TKPluginInvokeCenter shareInstance] reloadPluginConfig:@"TKOpenResource.bundle/OpenPlugin.xml"];
        [TKSystemHelper reloadConfig:[NSString stringWithFormat:@"%@.bundle/Configuration_Open_BuriedPoint.xml",TK_OPEN_RESOURCE_NAME]];
        [TKSystemHelper reloadConfig:[NSString stringWithFormat:@"%@.bundle/OpenInfo.xml",TK_OPEN_RESOURCE_NAME]];

    }
    return self;
}

- (id)initWithParams:(id)mParams
{
    self = [self init];
    
    if (mParams && [mParams isKindOfClass:[NSDictionary class]]) {
        
        [TKSystemHelper setMemcache:mParams WithKey:@"channelParams"];
        
        [TKSystemHelper setMemcache:mParams WithKey:@"channelParam"];
        
        if (mParams[@"h5Url"] && ![@"" isEqualToString:mParams[@"h5Url"]]) {

            if (mParams[@"moduleName"]) {

                self.tkName = mParams[@"moduleName"];
            }
            [self setUIWithUrl:mParams[@"h5Url"]];
            refreashUrl = mParams[@"h5Url"];
            
        }else{
            refreashUrl=self.notFindUrlString;
        }
    }
    
    return self;
}

/**
*  <AUTHOR> 2020年04月29日09:31:26
*  根据url参数设置导航等相关ui
*  @param url
*/
-(void)setUIWithUrl:(NSString *)url{
    NSArray *temp = [TKStringHelper string:url splitWith:@"?"];
    
    if (temp.count > 1)
    {
        NSString *paramStr = temp[1];
        paramStr=[TKStringHelper string:paramStr splitWith:@"#"][0];
        NSDictionary *urlParam=[TKDataHelper stringToDictionay:paramStr firstSplit:@"&" secondSplit:@"="];
        
        //状态栏颜色
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_statusColor"]]) {
            self.statusBarBgColor = [TKUIHelper colorWithHexString:urlParam[@"tk_statusColor"]];
        }
        
        //状态栏风格
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_statusStyle"]]) {
            if ([urlParam[@"tk_statusStyle"] isEqualToString:@"0"]) {
                self.statusBarStyle = TKUIStatusBarStyleDefault;
            }else if ([urlParam[@"tk_statusStyle"] isEqualToString:@"1"]){
                self.statusBarStyle = UIStatusBarStyleLightContent;
            }
        }
        
        //导航栏标题
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_title"]]) {
            self.title=urlParam[@"tk_title"];
        }
        
        //导航栏标题颜色
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_titleColor"]]) {
            self.titleColor = [TKUIHelper colorWithHexString:urlParam[@"tk_titleColor"]];
        }
        
        //左右侧相关按钮颜色
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_btnColor"]]) {
            self.leftBtnColor = [TKUIHelper colorWithHexString:urlParam[@"tk_btnColor"]];
            self.rightBtnColor = [TKUIHelper colorWithHexString:urlParam[@"tk_btnColor"]];
        }
        
        //标题文字是否根据webview的Title而变化（0：固定（默认），1：改变）
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_isChangeTitle"]]) {
            if ([urlParam[@"tk_isChangeTitle"] isEqualToString:@"1"]) {
                self.isChangeTitle = YES;
            }else{
                self.isChangeTitle = NO;
            }
        }
        
        //是否显示返回按钮(0:否，1：是（默认）)
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_isShowBackBtn"]]) {
            if ([urlParam[@"tk_isShowBackBtn"] isEqualToString:@"0"]) {
                self.isShowBackBtn = NO;
            }else{
                self.isShowBackBtn = YES;
            }
        }
        
        //是否显示关闭按钮(0:否（默认），1：是)
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_isShowCloseBtn"]]) {
            if ([urlParam[@"tk_isShowCloseBtn"] isEqualToString:@"1"]) {
                self.isShowCloseBtn = YES;
            }else{
                self.isShowCloseBtn = NO;
            }
        }
        
        //是否使用webview缓冲池(0:否（默认），1：是)
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_isUseCache"]]) {
            if ([urlParam[@"tk_isUseCache"] isEqualToString:@"1"]) {
                self.isUseWebViewCachePool = YES;
            }else{
                self.isUseWebViewCachePool = NO;
            }
        }
          
        // 是否使用沉浸式全屏H5模式(0:否（默认），1：是)
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_isUseFullScreenH5"]]) {
            if ([urlParam[@"tk_isUseFullScreenH5"] isEqualToString:@"1"]) {
                self.isImmersion = YES;
            }else{
                self.isImmersion = NO;
            }
        }
        
        // 是否是tkh5
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_isTKH5"]]) {
            if ([urlParam[@"tk_isTKH5"] isEqualToString:@"0"]) {
                self.isTKH5 = NO;
            }else{
                self.isTKH5 = YES;
            }
        }
        
        //是否支持滑动返回(0:否（默认），1：是)
        if ([TKStringHelper isNotEmpty:urlParam[@"tk_isSupportSwipingBack"]]) {
            if ([urlParam[@"tk_isSupportSwipingBack"] isEqualToString:@"1"]) {
                self.isSupportSwipingBack = YES;
            }else{
                self.isSupportSwipingBack = NO;
            }
        }
        
    }
}

/**
 *  <AUTHOR> 2018年08月11日09:35:07
 *  初始化控制器（如果平台已登录，就传对应的登陆信息；未登录传空。一般网厅才会需要登录信息）
 *  @param mParams 传入的参数（一般就h5Url）
 *  @param param 登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 */
-(instancetype)initWithParams:(id)mParams loginInfoParam:(NSDictionary *)loginInfoParam{
    self=[self initWithParams:mParams];
    if (self) {
        if (loginInfoParam) {
            [self handlingAccountInfoWithParam:loginInfoParam loginAccountType:nil];
        }else{
            //清空登陆信息
            [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
        }
    }
    return self;
}


/**
 *  <AUTHOR> 2018年08月11日11:00:54
 *  登陆信息通知H5登陆
 *  @param param 登陆信息，登陆与退出登陆要传（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  @param externalParam interruptHandleExternalCall这个代理方法传过来的参数，需要回传给h5
 *  return nil
 */
-(void)loginInfoWithParam:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [self handlingAccountInfoWithParam:loginInfoParam loginAccountType:nil];
    }else{
        //清空登陆信息
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
    }
    if (self.extParam) {
        NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
        [tkReqParam addEntriesFromDictionary:self.extParam];
        tkReqParam[@"funcNo"]=@"60098";
        tkReqParam[@"accountType"]=FXC_ACCOUNT_INFO;
        tkReqParam[@"actionType"]=@"1";
        self.extParam=tkReqParam;
    }else{
        self.extParam=(NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":FXC_ACCOUNT_INFO,@"actionType":@"1"};
    }
    //通知h5登陆
    [self iosCallJsWithParam:self.extParam];
}

/**
 *  <AUTHOR> 2023年05月25日16:33:21
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param loginAccountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoWithParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginAccountType:(NSString *)accountType loginInfoWithParam:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [self handlingAccountInfoWithParam:loginInfoParam loginAccountType:[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType]];
    }else{
        if ([TKStringHelper isEmpty:accountType]) {
            //清空所有账号登陆类型
            for (id key in [TKFxcAccountInfoType shareInstance].accountTypeDic) {
                //清空登陆信息
                [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":key,@"value":@""} moduleName:nil];
            }
            
        }else{
            //清空对应账号类型登陆信息
            [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType],@"value":@""} moduleName:nil];
        }
    }
    
    NSString *accountTypeKey;
    if ([TKStringHelper isEmpty:accountType]) {
        accountTypeKey=FXC_ACCOUNT_INFO;
    }else{
        accountTypeKey=[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType];
    }
    
    if (self.extParam) {
        NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
        [tkReqParam addEntriesFromDictionary:self.extParam];
        tkReqParam[@"funcNo"]=@"60098";
        tkReqParam[@"accountType"]=accountTypeKey;
        tkReqParam[@"actionType"]=@"1";
        self.extParam=tkReqParam;
    }else{
        self.extParam=(NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":accountTypeKey,@"actionType":@"1"};
    }
    //通知h5登陆
    [self iosCallJsWithParam:self.extParam];
}

/**
 *  <AUTHOR> 2018年08月11日12:01:57
 *  退出登陆并清理账号相关信息（当该控制器实例存在时调用）
 *  @param param 前面登陆时候传的参数KEY一致，值为NULL（例：@{@"token":@"",@"phoneNo":@""}）
 *  return nil
 */
-(void)loginOut{
    //清空登陆信息
    [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
    //通知h5退出登陆
    if (self.extParam) {
        NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
        [tkReqParam addEntriesFromDictionary:self.extParam];
        tkReqParam[@"funcNo"]=@"60098";
        tkReqParam[@"accountType"]=FXC_ACCOUNT_INFO;
        tkReqParam[@"actionType"]=@"2";
        self.extParam=tkReqParam;
    }else{
        self.extParam=(NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":FXC_ACCOUNT_INFO,@"actionType":@"2"};
    }
    [self iosCallJsWithParam:self.extParam];
}

/**
 *  <AUTHOR> 2019年01月03日18:22:04
 *  存储登录信息，不通知js的60098方法
 *  @param param 登陆信息，登陆与退出登陆要传（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginInfoWithParamNOCallJs:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [self handlingAccountInfoWithParam:loginInfoParam loginAccountType:nil];
    }else{
        //清空登陆信息
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
    }
}

/**
 *  <AUTHOR> 2023年04月17日15:28:43
 *   delegate事件结束后通知h5结果
 *  @param param h5需要获取的事件结果信息
 *  return nil
 */
-(void)openDelegateActionCallback:(NSDictionary*)param{
    NSMutableDictionary *dic=[[NSMutableDictionary alloc] init];
    dic[@"funcNo"]=@"60098";
    [dic addEntriesFromDictionary:param];
    //通知h5得60098功能号，告知原生60099代理事件结果
    [self iosCallJsWithParam:dic];
}

/**
 *  <AUTHOR> 2019年11月27日18:30:00
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param param 登陆信息，登陆与退出登陆要传,传nil的话就是清空登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
+(void)loginInfoWithParam:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [TKOpenController handlingAccountInfoWithParam:loginInfoParam loginAccountType:nil];
    }else{
        //清空登陆信息
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
    }
   
    //群发通知js
    [[TKOpenAccountService new] iosCallJsWithParam:((NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":FXC_ACCOUNT_INFO,@"actionType":@"1"})];
}

/**
 *  <AUTHOR> 2020年01月07日10:08:44
 *  初始化控制器（如果平台已登录，就传对应的登陆信息；未登录传空。一般网厅才会需要登录信息）
 *  @param mParams 传入的参数（一般就h5Url）
 *  @param loginAccountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoParam 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 */
-(instancetype)initWithParams:(id)mParams loginAccountType:(NSString *)accountType  loginInfoParam:(NSDictionary *)loginInfoParam{
    self=[self initWithParams:mParams];
    if (self) {
        if (loginInfoParam) {
            [self handlingAccountInfoWithParam:loginInfoParam loginAccountType:[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType]];
        }else{
            if ([TKStringHelper isEmpty:accountType]) {
                //清空所有账号登陆类型
                for (id key in [TKFxcAccountInfoType shareInstance].accountTypeDic) {
                    //清空登陆信息
                    [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":key,@"value":@""} moduleName:nil];
                }
                //通知h5退出登陆
                [self iosCallJsWithParam:(NSMutableDictionary *)@{@"funcNo":@"60098"}];
            }else{
                //清空对应账号类型登陆信息
                [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType],@"value":@""} moduleName:nil];
                //通知h5退出登陆
                [self iosCallJsWithParam:(NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType]}];
            }
        }
    }
    return self;
}

/**
 *  <AUTHOR> 2019年11月27日18:30:00
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param loginAccountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoWithParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
+(void)loginAccountType:(NSString *)accountType loginInfoWithParam:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [TKOpenController handlingAccountInfoWithParam:loginInfoParam loginAccountType:[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType]];
    }else{
        if ([TKStringHelper isEmpty:accountType]) {
            //清空所有账号登陆类型
            for (id key in [TKFxcAccountInfoType shareInstance].accountTypeDic) {
                //清空登陆信息
                [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":key,@"value":@""} moduleName:nil];
            }
            
        }else{
            //清空对应账号类型登陆信息
            [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType],@"value":@""} moduleName:nil];
        }
    }
    
    if ([TKStringHelper isEmpty:accountType]) {
        //群发通知js
        [[TKOpenAccountService new] iosCallJsWithParam:((NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":FXC_ACCOUNT_INFO})];
    }else{
        //群发通知js
        [[TKOpenAccountService new] iosCallJsWithParam:((NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":accountType})];
    }
    
}

/**
 *  <AUTHOR> 2019年08月22日15:58:29
 *  将登陆信息Map转换成Json字符串后存储下来类方法
 */
+(void)handlingAccountInfoWithParam:(NSDictionary*)loginInfoParam loginAccountType:(NSString *)accountType{
   if ([TKStringHelper isEmpty:accountType]) {
       accountType=FXC_ACCOUNT_INFO;
       //存字典对象
       [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":loginInfoParam} moduleName:nil];
   }else{
       //存字典对象
       [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":accountType,@"value":loginInfoParam} moduleName:nil];
   }
   //存储账号类型方便到时候清理
   if (![TKFxcAccountInfoType shareInstance].accountTypeDic) {
       [TKFxcAccountInfoType shareInstance].accountTypeDic=[NSMutableDictionary dictionary];
   }
   [TKFxcAccountInfoType shareInstance].accountTypeDic[accountType]=accountType;
   
}

/**
 *  <AUTHOR> 2018年10月08日16:35:36
 *  将登陆信息Map转换成Json字符串后存储下来
 */
-(void)handlingAccountInfoWithParam:(NSDictionary*)loginInfoParam loginAccountType:(NSString *)accountType{
    if ([TKStringHelper isEmpty:accountType]) {
        accountType=FXC_ACCOUNT_INFO;
        //存字典对象
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":loginInfoParam} moduleName:nil];
    }else{
        //存字典对象
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":accountType,@"value":loginInfoParam} moduleName:nil];
    }
    //存储账号类型方便到时候清理
    if (![TKFxcAccountInfoType shareInstance].accountTypeDic) {
        [TKFxcAccountInfoType shareInstance].accountTypeDic=[NSMutableDictionary dictionary];
    }
    [TKFxcAccountInfoType shareInstance].accountTypeDic[accountType]=accountType;
    
}

/**
 *  <AUTHOR> 2019年01月03日18:22:04
 *  打印三方SDK信息日志
 */
+(void)print3libSDKInfoLog{
    if (![[TKAppEngine shareInstance] isRuning]) {
        [[TKAppEngine shareInstance] start];
    }
    [[TKAppEngine shareInstance].pluginCenter callPlugin:@"60049" param:nil moduleName:nil isH5:NO callBackFunc:nil];
}

- (void)loadView
{
    [super loadView];
    TKLogInfo(@"#####");
    if (self.isImmersion) {
        //没有原生导航栏，H5 全屏，沉浸式
        self.isUseFullScreenH5 = YES;
    }
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.webView.delegate = self;
    
//    NSUserDefaults *uDefaults = [NSUserDefaults standardUserDefaults];
//    
//    if (![uDefaults boolForKey:@"isFirstLanuchApp"]) {
//        
//        [self initNavigationPageView];
//    }
    
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    
    NSString *documentsDirectory = [paths objectAtIndex:0];
   
    
    NSURL *url = [NSURL fileURLWithPath:documentsDirectory];
    
    [self addSkipBackupAttributeToItemAtURL:url];
    
    //url分空判断和鉴权校验
    if (refreashUrl&&[self authorChecking:[TKSystemHelper getConfigByKey:@"OpenAuthorInfo.key"] url:refreashUrl]) {
        //当前主题
        if ([TKStringHelper isNotEmpty:self.currentTheme]) {
            [TKThemeManager shareInstance].theme=self.currentTheme;
            refreashUrl=[TKCommonUtil url:refreashUrl appendingParamStr:[NSString stringWithFormat:@"tk_theme=%@",[TKThemeManager shareInstance].theme]];
        }
        self.webViewUrl = refreashUrl;

    }else{
        //授权失败不加载了
        return;
    }
    
    //浏览器底色和loading层底色一致
    if (self.loadingBgColorString) {
        [self.webView.realWebView setBackgroundColor:[TKUIHelper colorWithHexString:self.loadingBgColorString]];

    }
    //gifview新的loading方式
    if (!self.isNoShowLoading) {
        //设置loading层
        self.isShowLoading=YES;
        self.loadingWebViewUI=[self customLoadingView];
    }
   
}


/**
<AUTHOR> 2020年04月05日10:13:03
@授权检查
*/
-(BOOL)authorChecking:(NSString *)authorString url:(NSString *)urlString{
    if (![authorString isEqualToString:SUPER_AUTHOR]) {
        if ([TKStringHelper isEmpty:authorString]) {
            //没有授权
            TKLogInfo(@"鉴权-----授权字符串为空");
            return NO;
        }else{
            NSString *authorInfo=[TKAesHelper stringWithAesDecryptString:authorString withKey:[TKPasswordGenerator generatorPassword]];
            if ([TKStringHelper isEmpty:authorInfo]) {
                TKLogInfo(@"鉴权-----授权信息解密为空");
                return NO;
            }else{
                NSArray *arr = [authorInfo componentsSeparatedByString:@"#"];
                NSString *appKey;
                NSString *bundleId;
                if (arr.count>=2) {
                    appKey=arr[0];
                    bundleId=arr[1];//包名,多个用|分开
                    NSString *currentBundleId=[TKSystemHelper getAppIdentifier];
                    if ([bundleId rangeOfString:currentBundleId].location !=NSNotFound) {
                        
                        NSDictionary *params = [self paramsWithWebviewUrl:urlString];
                        NSString *tkid=params[@"tkid"];
                        if ([TKStringHelper isEmpty:tkid]) {
                            TKLogInfo(@"鉴权-----url没有tkid授权");
                            return NO;
                        }else{
                            NSURL *url=[NSURL URLWithString:urlString];
                            NSString *ipPort=[NSString stringWithFormat:@"%@:%@",url.host,url.port];
                            //生产md5截取16位，与url中tkid对比
                            NSString *md516String=[[TKMd5Helper md5Encrypt:[NSString stringWithFormat:@"%@%@",appKey,ipPort]] substringToIndex:16];
                            if ([tkid isEqualToString:md516String]) {
                                return YES;
                            }else{
                                TKLogInfo(@"鉴权-----tkid不匹配");
                                return NO;
                            }
                        }
                        
                    }else{
                        TKLogInfo(@"鉴权-----授权bundleId不正确");
                        return NO;
                    }
                }else{
                    TKLogInfo(@"鉴权-----授权信息格式不正确");
                    return NO;
                }
            }
        }
    }else{
        return YES;
    }
    
}

#pragma mark-解析url参数

- (NSDictionary *)paramsWithWebviewUrl:(NSString *)url{
    NSDictionary *params;
    NSArray *temp = [TKStringHelper string:url splitWith:@"?"];
    
    if (temp.count > 1)
    {
        NSString *paramStr = temp[1];
        paramStr=[TKStringHelper string:paramStr splitWith:@"#"][0];
        params=[TKDataHelper stringToDictionay:paramStr firstSplit:@"&" secondSplit:@"="];
    }
    return params;
}


/**
 *  <AUTHOR> 2018年08月13日18:02:15
 *  自定义loading层
 *  @return UIView
 */
-(UIView *)customLoadingView{
    UIView *backgroundView=[[UIView alloc] initWithFrame:self.view.frame];
    if (self.loadingBgColorString) {
        [backgroundView setBackgroundColor:[TKUIHelper colorWithHexString:self.loadingBgColorString]];
    }else{
        [backgroundView setBackgroundColor:[UIColor whiteColor]];
    }
    
    //gif动图
    TKGIFImageView *gifView=[[TKGIFImageView alloc] init];
    if (self.gifImgName) {
        if ([self.gifImgName isKindOfClass:[UIImage class]]) {
            [gifView setImage:(UIImage *)self.gifImgName];
        }else if([self.gifImgName isKindOfClass:[NSString class]]){
            [gifView setImageByName:(NSString *)self.gifImgName];
        }
    }else{
        [gifView setImageByName:@"TKOpenResource.bundle/Resources/tk_openLoading.gif"];
    }
    
    [gifView setFrame:CGRectMake(0, 0, gifView.image.size.width/2, gifView.image.size.height/2)];
    gifView.center=backgroundView.center;
    [backgroundView addSubview:gifView];
    
    if (self.isShowLoadingCloseBtn) {
        //返回按钮
        float btnWidth=35;
        UIButton *backBtn=[[UIButton alloc] initWithFrame:CGRectMake(gifView.frame.origin.x+gifView.frame.size.width, gifView.frame.origin.y-btnWidth, btnWidth, btnWidth)];
        [backBtn setImage:[TKImageHelper imageByName:@"TKOpenResource.bundle/Resources/tk_openClose.png"] forState:UIControlStateNormal];
        [backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
//        gifView.userInteractionEnabled=YES;
//        backgroundView.userInteractionEnabled=YES;
        [backgroundView addSubview:backBtn];
    }
   
    return backgroundView;
}
/**
 *  <AUTHOR> 2018年08月13日18:21:42
 *  loading层退出开户事件
 */
-(void)backAction:(id)sender{
    if ([self respondsToSelector:@selector(dismissViewControllerAnimated:completion:)]) {
        [self dismissViewControllerAnimated:YES completion:nil];
    } else if ([self.navigationController respondsToSelector:@selector(popViewControllerAnimated:)]) {
        [self.navigationController popViewControllerAnimated:YES];
    }
}


- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    if (![[TKAppEngine shareInstance] isRuning]) {
        [[TKAppEngine shareInstance] start];
    }
    isExited = NO;
    if (self.isImmersion) {
        //没有原生导航栏，H5 全屏，沉浸式
        [self.navigationController setNavigationBarHidden:YES animated:NO];
    }

    //侧滑返回处理
    if(self.isCloseNavSwipingBack){
        self.currentNavSupportSwipingBack=self.navigationController.interactivePopGestureRecognizer.enabled;
        self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    }
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    TKLogInfo(@"页面显示，当前代理对象为%@", _oDelegate);
//    // 让传入的参数
//    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
//    param[@"funcNo"]=@"60030";//功能号
//    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
//    param[@"shortestTime"]=@"3";//录制视频的最短时间，单位秒
//    param[@"longestTime"]=@"35";//录制视频的最长时间，单位秒
//    param[@"readString"]=@"我是威震天本人，我自愿在思迪证券开户，并了解相关风险说明。认证阅读并理解协议内容，想炒股赚钱所以开户问题不大，自愿自觉开户。";//宣读话术，单位秒，不传默认：我自愿开立证券账户
//    param[@"colorWordTime"]=@"0.2";//多久渐变一个文字，默认0.3秒渐变一个字
//    param[@"uploadTipString"]=@"请确认个人信息，视频无误后再提交";//多久渐变一个文字，默认0.3秒渐变一个字
//    NSString *pluginNo = [param getStringWithKey:@"funcNo"];    // 取出插件号
//    if ([TKStringHelper isEmpty:pluginNo]) {
//        // 插件号不能为空
//        // 回调报错信息给h5
//    }
//    NSString *moduleName = [param getStringWithKey:@"moduleName"];
//    //插件调用
//    [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNo param:param moduleName:[TKStringHelper isNotEmpty:moduleName] ? moduleName : @"open" callBackFunc:^(NSMutableDictionary *result) {
//        // 回调给原生的结果，可以直接回调给h5
//        NSLog(@"result = %@", result);
//    }];
}

-(void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    TKLogInfo(@"页面隐藏，当前代理对象为%@", _oDelegate);
}


- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    
    //侧滑返回处理
    if(self.isCloseNavSwipingBack){
        self.navigationController.interactivePopGestureRecognizer.enabled=self.currentNavSupportSwipingBack;
    }
}

//isCheckScreenShotRecord截屏录屏回调
-(void)handleCheckScreenShotRecord:(TKCheckScreenType)checkScreenType{
    NSString * isInterceptScreenshot =(NSString *)[[TKCacheManager shareInstance] getCacheDataWithKey:@"isInterceptScreenshot" cacheType:TKCacheType_Memo];

    if ([isInterceptScreenshot intValue]==1) {
        if(checkScreenType==TKCheckScreenType_Shot){
            NSString *screenCaptureTip =(NSString *)[[TKCacheManager shareInstance] getCacheDataWithKey:@"screenCaptureTip" cacheType:TKCacheType_Memo];
            if([TKStringHelper isEmpty:screenCaptureTip]){
                screenCaptureTip=@"发现正在截屏，请注意个人信息安全！";
            }
            TKLayerView *layerView=[[TKLayerView alloc] initContentView:self.rootWindow withBtnTextColor:nil];
            [layerView showTip:screenCaptureTip position:TKLayerPosition_Center];
        }else{
            NSString * screenRecordingTip =(NSString *)[[TKCacheManager shareInstance] getCacheDataWithKey:@"screenRecordingTip" cacheType:TKCacheType_Memo];
            if([TKStringHelper isEmpty:screenRecordingTip]){
                screenRecordingTip=@"发现正在录屏，请注意个人信息安全！";
            }

            TKLayerView *layerView=[[TKLayerView alloc] initContentView:self.rootWindow withBtnTextColor:nil];
            [layerView showTip:screenRecordingTip position:TKLayerPosition_Center];
        }
    }
}

//- (UIStatusBarStyle)preferredStatusBarStyle
//{
//    return self.statusBarStyle;
//}

- (void)btnOnClicked:(id)sender{
    
    UIButton *btn = (UIButton*)sender;

    if (btn.tag == 100) {
        
        if (!isExited) {
            [self exitKHSDK];
        }

    }else if(btn.tag == 101){
    
        [[TKAppStartManager shareInstance] hide:YES];
        
        NSUserDefaults *uDefaults = [NSUserDefaults standardUserDefaults];

        [uDefaults setBool:YES forKey:@"isFirstLanuchApp"];
        
        [uDefaults synchronize];
        
    }else{
    
        [self.webView reload];
        
    }
   
}

- (NSArray *)listNotificationInter
{
    NSMutableArray *notes = [[super listNotificationInter] mutableCopy];
    [notes addObject:NOTE_OPEN_MODULE];
//    [notes addObject:NOTE_CLOSE_MODULE];
    [notes addObject:TK_INTERRUPTSDK_NOTIFICATION];
    [notes addObject:TK_THIRD_WEBVIEW_NOTIFICATION];
    [notes addObject:UIApplicationWillTerminateNotification];
    return notes;
}

- (void)handleNotification:(NSNotification *)notification
{
    if ([notification.name isEqualToString:NOTE_OPEN_MODULE])
    {
        NSDictionary *mDic = notification.object;
        
        
        if (mDic && mDic[@"moduleName"])
        {
            
            if ([mDic[@"moduleName"] isEqualToString:HANDLE_MODULE]) {
                
                //先判断tkuuid是有传,传了的话不一致不做处理,也不走后续moduleName判断
                if (mDic[@"tkuuid"]) {
                    if ([mDic[@"tkuuid"] isEqualToString:self.jsCallBack.uuid]) {
                        TKLogInfo(@"控制器tkuuid：%@",self.jsCallBack.uuid);
                        if (!isExited) {
                            [self exitKHSDK];
                        }
                    }
                    return;
                }

                if (!isExited) {
                    [self exitKHSDK];
                }
                
            }else if ([mDic[@"moduleName"] isEqualToString:@"refreash_h5"]) {
                
                isLoadFailed = NO;
                self.webViewUrl = refreashUrl;

            }else{

                if (mDic[@"moduleH5Url"]) {//h5切换模块
                    
                    if (mDic[@"moduleName"]) {
                        
                        self.tkName = mDic[@"moduleName"];
                    }
                    
                    self.webViewUrl = mDic[@"moduleH5Url"];
                }
            }
            
        }
    }else if ([notification.name isEqualToString:UIApplicationWillTerminateNotification]){
    
        NSURLRequest *request = [NSURLRequest requestWithURL:[NSURL URLWithString:@"about:blank"]];
        [self.webView loadRequest:request];
        
    }else if ([notification.name isEqualToString:TK_THIRD_WEBVIEW_NOTIFICATION]){
    
        [self.webView reload];
        
    }else if ([notification.name isEqualToString:TK_INTERRUPTSDK_NOTIFICATION]){
        TKLogInfo(@"SDK收到广播通知：TK_INTERRUPTSDK_NOTIFICATION,代理为:%@", _oDelegate);
        NSMutableDictionary *mDic = notification.object;
        self.extParam=mDic;
        
        if (_oDelegate && [_oDelegate respondsToSelector:@selector(interruptHandleExternalCall:withActionType:withActionParams:)]) {
            [_oDelegate interruptHandleExternalCall:self withActionType:[mDic[@"actionType"] intValue] withActionParams:mDic[@"params"]];
            TKLogInfo(@"回调外层interruptHandleExternalCall:withActionType:withActionParams:方法,代理为:%@", _oDelegate);
            return;
        }else{
            TKLogInfo(@"代理不存在或没有实现方法，无法回调外层interruptHandleExternalCall:withActionType:withActionParams:方法，代理为%@, ", _oDelegate);
        }
        
        if (_oDelegate && [_oDelegate respondsToSelector:@selector(interruptHandleExternalCall:withParams:)]) {
            [_oDelegate interruptHandleExternalCall:self withParams:mDic];
            TKLogInfo(@"回调外层interruptHandleExternalCall:withParams:方法,代理为:%@", _oDelegate);
            return;
        }else{
            TKLogInfo(@"代理不存在或没有实现方法，无法回调外层interruptHandleExternalCall:withParams:方法,代理为:%@", _oDelegate);
        }
        

        
    }
    else
    {
        [super handleNotification:notification];
    }
}

-(BOOL)isElderTheme:(NSString *)theme{
    //_elder主题包含要适老化
    NSRange tipRange=[theme rangeOfString:@"_elder"];

    if (tipRange.length>0) {
        [TKOpenViewStyleHelper shareInstance].isElder=true;
    }else{
        [TKOpenViewStyleHelper shareInstance].isElder=false;
    }
    return [TKOpenViewStyleHelper shareInstance].isElder;
}

- (void)h5LoadFinish
{
    TKLogInfo(@"H5加载完成");
    //隐藏loading层
    self.isShowLoading=NO;
    [[TKAppStartManager shareInstance] hide:YES];
}

- (BOOL)webView:(TKWebView*)webView shouldStartLoadWithRequest:(NSURLRequest*)request navigationType:(TKWebViewNavigationType)navigationType
{
    // 拦截自定义的404和网络错误页面
    NSURL *url = request.URL;
    if ([url.absoluteString hasSuffix:@"tk_open_fxc_kh_error_back"]) { // 返回
        
        if (!isExited) {
            [self exitKHSDK];
        }
        
        return NO;
    }
    
    if ([url.absoluteString hasSuffix:@"tk_open_fxc_kh_error_reload"]) { // 重新
        
        isLoadFailed = NO;
        self.webViewUrl = refreashUrl;
        return NO;
    }
    
    
    [super webView:webView shouldStartLoadWithRequest:request navigationType:navigationType];
    
    if (self.oDelegate&&[self.oDelegate respondsToSelector:@selector(tkOpenWebView:shouldStartLoadWithRequest:navigationType:)]) {
        if([self.oDelegate tkOpenWebView:webView.realWebView shouldStartLoadWithRequest:request navigationType:navigationType]){
            return NO;
        }
    }

    NSString *urlStr = request.URL.absoluteString;
    
    TKLogInfo(@"【%@|%@】开始加载:%@", self.jsCallBack.webViewName, self.tkName, urlStr);
    
    return YES;
}

- (void)webViewDidFinishLoad:(TKWebView*)webView{
    
    TKLogInfo(@"加载完成");
    
    [super webViewDidFinishLoad:webView];
    [[TKAppStartManager shareInstance] hide:YES];
    
}

- (void)webView:(TKWebView*)webView didFailLoadWithError:(NSError*)error
{
    TKLogInfo(@"加载失败");
//    [super webView:webView didFailLoadWithError:error];//由于对失败页面有要求，所以这里不调用宝哥的super方法。使用本地html
    if([self isIgnoreWebViewRequestError:error]){
        return;
    }
    
    
    if (!isLoadFailed) {
        
        isLoadFailed = YES;
        
        self.webViewUrl=self.networkErrorUrlString;

    }
    
}

-(void)goClose:(BOOL)isShowAnimation{
    [self exitKHSDK];
}

#pragma mark -sdk退出处理
- (void)exitKHSDK{
    
    isExited = YES;
    
    [TKSystemHelper removeMemcacheWithKey:@"channelParams"];
    
    [[TKAppEngine shareInstance].pluginCenter callPlugin:@"50211" param:nil moduleName:nil isH5:NO callBackFunc:^(NSMutableDictionary *result) {}];
    
    UIViewController *rootController = [UIApplication sharedApplication].keyWindow.rootViewController;
    
//    if ([self isKindOfClass:[rootController class]]) {//关闭应用
    if (self == rootController) {    //关闭应用
        
       exit(1);
        
    }else{//退出sdk
        BOOL animated=YES;
        if (self.isNotDismissAnimated) {
            animated=NO;
        }
        if (self.navigationController) {
            if ([self.navigationController.viewControllers.firstObject isEqual:self])
            {
                [self.navigationController dismissViewControllerAnimated:animated completion:nil];
            }else
            {
//                [self.navigationController popViewControllerAnimated:animated];
                NSMutableArray *childViewControlers = [self.navigationController.viewControllers mutableCopy];
                [childViewControlers removeObject:self];
                [self.navigationController setViewControllers:childViewControlers animated:animated];
                
                [self.navigationController setNavigationBarHidden:NO animated:NO];
            }
        }
        else
        {
            [self dismissViewControllerAnimated:animated completion:nil];
        }
        

        
        if (_oDelegate && [_oDelegate respondsToSelector:@selector(openAccountExit)]) {
            
            [_oDelegate openAccountExit];
            TKLogInfo(@"回调外层openAccountExit方法,代理为:%@", _oDelegate);
        }else{
            TKLogInfo(@"代理不存在或没有实现方法，无法回调外层openAccountExit方法,代理为:%@", _oDelegate);
        }
        
    }
}

- (void)tkAddLoadingView{}

- (void)tkAddNavigationPageView{}


//防止文件备份到iCloud&iTunes
- (BOOL)addSkipBackupAttributeToItemAtURL:(NSURL *)URL
{
    assert([[NSFileManager defaultManager] fileExistsAtPath: [URL path]]);
    
    NSError *error = nil;
    
    BOOL success = [URL setResourceValue: [NSNumber numberWithBool: YES]
                                  forKey: NSURLIsExcludedFromBackupKey error: &error];
    if(!success){
        TKLogInfo(@"Error excluding %@ from backup %@", [URL lastPathComponent], error);
    }
    
    return success;
}

- (void)dealloc{
    
    TKLogInfo(@"%@%s",NSStringFromClass([self class]), __FUNCTION__);
    
    self.webView.delegate = nil;
    
}

#pragma lazy loading
/**
 *  <AUTHOR> 2018年11月29日09:13:01
 *  返回当前皮肤模式(skinMode)下网络错误页面提示地址
 *  return NSString
 */
-(NSString *)networkErrorUrlString{
    [[TKAppStartManager shareInstance] hide:YES];
    NSString *path = [[NSBundle mainBundle] pathForResource:TK_OPEN_RESOURCE_NAME ofType:@"bundle"];
    if ([self.skinMode isEqualToString:@"1"]) {
        _networkErrorUrlString=[NSURL fileURLWithPath:[NSString stringWithFormat:@"%@/tk_open/h5/exception/views/error/networkExceptionBlack.html", path]].absoluteString;
    }else{
        _networkErrorUrlString=[NSURL fileURLWithPath:[NSString stringWithFormat:@"%@/tk_open/h5/exception/views/error/networkException.html", path]].absoluteString;
    }
    return _networkErrorUrlString;
}

/**
 *  <AUTHOR> 2018年11月29日091402
 *  返回当前皮肤模式(skinMode)下404页面提示地址
 *  return NSString
 */
-(NSString *)notFindUrlString{
    [[TKAppStartManager shareInstance] hide:YES];
    NSString *path = [[NSBundle mainBundle] pathForResource:TK_OPEN_RESOURCE_NAME ofType:@"bundle"];
    if ([self.skinMode isEqualToString:@"1"]) {
        _notFindUrlString=[NSURL fileURLWithPath:[NSString stringWithFormat:@"%@/tk_open/h5/exception/views/error/404ExceptionBlack.html", path]].absoluteString;
    }else{
        _notFindUrlString=[NSURL fileURLWithPath:[NSString stringWithFormat:@"%@/tk_open/h5/exception/views/error/404Exception.html", path]].absoluteString;
    }
    
    return _notFindUrlString;
}

-(void)setIsNeedTKAuthorIntroduce:(BOOL)isNeedTKAuthorIntroduce{
    _isNeedTKAuthorIntroduce=isNeedTKAuthorIntroduce;
    if (_isNeedTKAuthorIntroduce) {
        [TKSystemHelper setMemcache:@"1" WithKey:@"isNeedTKAuthorIntroduce"];
    }else{
        [TKSystemHelper setMemcache:@"0" WithKey:@"isNeedTKAuthorIntroduce"];
    }
}

@end
