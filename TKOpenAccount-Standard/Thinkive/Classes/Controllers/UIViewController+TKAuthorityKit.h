//
//  UIViewController+TKAuthorityKit.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2017/6/9.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^TKPermissionsBlock)();

/**
 *
 * @class UIViewController (TKAuthorityKit)
 *
 * @description 用户权限检测类别
 *
 *
 */
@interface UIViewController (TKAuthorityKit)

/**
 *
 * @method tkIsMicrophonePermissions:
 *
 * @brief 麦克风权限检测
 *
 * @param pBlock 权限已允许回调
 *
 */
- (void)tkIsMicrophonePermissions:(TKPermissionsBlock)pBlock;

/**
 *
 * @method tkIsCameraPermissions:
 *
 * @brief 相机权限检测
 *
 * @param pBlock 权限已允许回调
 *
 */
- (void)tkIsCameraPermissions:(TKPermissionsBlock)pBlock;

/**
 *
 * @method tkIsPhotoLibraryPermissions:
 *
 * @brief 相册权限检测
 *
 * @param pBlock 权限已允许回调
 *
 */
- (void)tkIsPhotoLibraryPermissions:(TKPermissionsBlock)pBlock;

/**
 *
 * @method tkIsPhotoLibraryPermissions:
 *
 * @brief 网络权限检测
 *
 * @param pBlock 权限已允许回调
 *
 */
- (void)tkIsNetWorkPermissions:(TKPermissionsBlock)pBlock;

@end
