//
//  TKBaseVideoRecordViewController.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/3/23.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKVideoAlertView.h"
#import "TKFaceDetectManager.h"
#import "TKOneWayVideoAlertTipView.h"
#import "TKBaseVideoRecordViewProtocol.h"
#import "TKBaseVideoRecordEndViewProtocol.h"
#import "TKSmartQuestionModel.h"
#import "TKOpenAccountService.h"
#import "TKRecordManagerProtocol.h"
#import "TKPlayer.h"
#import "TKZFPlayerController.h"

typedef enum : NSUInteger {
    TKOneWayEventTypeUnkown,
    TKOneWayEventTypeRecordStart,
    TKOneWayEventTypeRecordEnd,
    TKOneWayEventTypeRecordFail,
    TKOneWayEventTypeFaceFail,  // 在框检测/比对失败
    TKOneWayEventTypeSubmit,
} TKOneWayEventType;

typedef enum : NSInteger {
    TKOneWayEventResultNone = -1,    // 无结果
    TKOneWayEventResultFail,
    TKOneWayEventResultSuccess,
} TKOneWayEventResult;


/// 单向视频基类
@interface TKBaseVideoRecordViewController : TKBaseViewController<TKBaseVideoRecordViewDelegate, TKFaceDetectManagerDelegate, TKBaseVideoRecordEndViewDelegate, TKRecordManagerDelegate,TKVideoAlertViewDelegate>
{
    UIView * _avPreviewView;
    TKVideoAlertView *_videoAlertView;
}

// UI
@property (nonatomic, strong) UIView * _Nonnull avPreviewView;//视频预览视图
@property (nonatomic, strong) UIView<TKBaseVideoRecordViewProtocol> * _Nullable tkOneView;//单向视频界面
@property (nonatomic, strong) UIView<TKBaseVideoRecordEndViewProtocol> * _Nullable tkOneEndView;//单向视频结果页
@property (nonatomic, readwrite, strong) NSObject<TKRecordManagerProtocol> * _Nonnull recordManager; // 录制管理者
@property (nonatomic, strong) TKVideoAlertView * _Nullable videoAlertView;//视频挂断提示框
@property (nonatomic, readwrite, strong) TKPlayerControlView *_Nonnull playerControlView;    //播放视频工具视图
@property (nonatomic, strong) TKZFPlayerController *playerController; // 播放器组件控制器

@property (nonatomic, strong) NSMutableDictionary * _Nonnull requestParam;//请求参数
@property (nonatomic, strong) TKOpenAccountService * _Nonnull openAccountService;
@property (nonatomic, readwrite, strong) TKFaceDetectManager * _Nonnull faceDetectManager; // 人脸检测管理者

// 录制完毕上传的数据字段
@property (nonatomic, readwrite, copy) NSString * _Nullable relateVideoFilePath; // 相对视频文件路径
@property (nonatomic, readwrite, copy) NSString * _Nullable fullVideoFileURLString; // 完整的文件路径
@property (nonatomic, strong) NSString * _Nullable videoLength;//视频录制时长（秒）
@property (nonatomic, assign) CFAbsoluteTime recordStartTime;//视频开始录制时间
@property (nonatomic, strong) NSString * _Nullable recordStartDateString;//格式化的视频开始录制时间字符串
@property (nonatomic, strong) NSString * _Nullable recordEndDateString;//格式化的视频结束录制时间字符串
@property (nonatomic, readwrite, strong) NSDateFormatter * _Nullable formatter; // 时间格式
@property (nonatomic, assign) TKOneWayEventType * _Nullable oneWayEventType;//格式化的视频结束录制时间字符串
@property (nonatomic, assign) BOOL isRecording;//是否正在录制
@property (nonatomic, readwrite, strong) TKSmartQuestionModel * _Nullable currentModel; // 当前正在播放的模型数据

@property(nonatomic, assign)  BOOL transcriberStarted;//是否已开始语音识别
@property (nonatomic, assign) BOOL isAlertViewShow;//是否已有弹窗层

@property (nonatomic, assign) TKOneWayProcess oneWayProcess;  // 当前单向视频录制节点
@property (nonatomic, assign) TKPlayQuessionProcess playQuessionProcess;  // 当前语音播报流程节点

// 流程数组
@property (nonatomic, strong) NSMutableArray * _Nullable beforeVideoArray;
@property (nonatomic, strong) NSMutableArray * _Nonnull questionArray;    //记录json对象的问题数组
@property (nonatomic, strong) NSMutableArray * _Nullable afterVideoArray;


@property (nonatomic, readwrite, assign) BOOL isLandscape;   // 是否横屏录制

@property (nonatomic, readwrite, assign) BOOL isReadVideo;   // 是否朗读视频模式
@property (nonatomic, readwrite, assign) BOOL isClickTakeRecordBtn; // 是否点击了录制按钮（朗读单向用，区分播放本地音频文件结束后要不要开启人脸比对）
@property (nonatomic, strong) TKPlayer *player; //播放器
@property (nonatomic, assign) int shortestTime;//最短录制时间
@property (nonatomic, assign) int longestTime;//最长录制时间
@property (nonatomic, assign) BOOL continueOnAnswerTimeout; // 回答超时是否继续录制
@property(nonatomic, assign) BOOL forcedEndOneWayVideo; // 是否已强制结束单向

/// 构造方法
/// @param param 入参
- (nonnull instancetype)initWithParam:(nonnull NSMutableDictionary *)param;

- (void)recordTime:(int)recordTime longestTime:(int)longestTime startRecord:(BOOL)startRecord;
- (void)deleteLocalVideoFile:(NSString *)localFilePath;//删除本地视频文件
- (void)handleAudioPlay:(NSString*)voiceName withType:(NSString*)type;//播放本地mp3
/**
 <AUTHOR> 2020年03月07日17:54:19
 @播放本地语音文件
 */
- (void)handleAudioPlay:(NSString*)voiceName withType:(NSString*)type sourceDirectory:(NSString *)sourceDirectory;

// 以下方法交给子类-必须重写
- (NSString *_Nonnull)pluginCallBackfuncNo; // 回调插件号


// 以下方法交给子类重写-可选
- (void)bootDevice:(BOOL)isFirst;
- (void)stopDevice:(BOOL)isRelease;
- (void)startRecordingAction;
- (void)stopRecordingAction;
- (void)startPlaySound:(NSString *_Nonnull)flag;    // 播放声音
- (void)startTTS:(TKSmartQuestionModel *_Nonnull)model;
- (void)stopTTS;
- (void)startAsr;
- (void)stopAsr;
- (void)stopTTSAndAsr;
- (UIImage *_Nullable)videoFirstImage;  // 视频第一帧图片
- (BOOL)shouldPlayBeepSound;    // 是否播放’滴‘一声
- (void)uploadBtnClick; // 上传按钮点击事件
- (void)activeDevice;
- (void)deactiveDevice;
- (BOOL)skipPreview;    // 跳过预览页
- (BOOL)isLocalRecord;    // 是否是本地录制


// 以下主要流程方法供子类调用和重写，若非必要不建议调用和重写;若要调用，请在合适的时机调用super方法
- (void)restartOneVideo;    // 重新开始单向视频
- (void)exitProcess;
- (void)detectFace:(UIImage *_Nullable)image pixelBuffer:(CVPixelBufferRef _Nullable )pixelBuffer;   /// 人脸质检
- (void)nextOneWayProcess;  // 下一个单向流程
- (void)nextPlayQuestionProcess;    // 下一个播放问题流程
- (void)videoFailureDidExceedLimit:(TKOneWayVideoEndType)oneWayVideoEndType;    // 错误统计超限执行的流程
- (void)failActionCallJsWith:(NSString *_Nonnull)errorNo errorMsg:(NSString *_Nonnull)errorMsg; /// 失败回调
- (void)sendCallBack:(NSMutableDictionary *_Nonnull)callJsParam;    // 发送回调事件
- (NSString *_Nullable)getLocalOneWayVideoPath; /// 获取本地单向视频文件地址
- (void)showAlertView:(NSString *_Nullable)title message:(NSString *_Nullable)message tag:(NSInteger)tag;   /// 错误弹窗


// 以下主要业务处理方法供子类调用和重写，若非必要不建议调用和重写;若要调用，请在合适的时机调用super方法
// 初始化设备
- (void)handleBootDeviceDidStart;
- (void)handleBootDeviceDidSuccess;
- (void)handleBootDeviceDidFail:(NSString *_Nullable)errorMsg;
- (void)handleDeviceRunFail:(NSString *_Nullable)errorMsg;
// tts
- (void)handleSpeechSynthesisDidStart;  // 单段语音
- (void)handleSpeechSynthesisDidStartWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray; // 多段语音
- (void)handleSpeechSynthesisDidFail:(NSString *_Nullable)errorMsg;
- (void)handleSpeechSynthesisDidPlayDone;
// asr
- (void)handleSpeechRecognizeResult:(NSString *_Nullable)identifyString;
- (void)hanldeSpeechRecognizeDidStart;
- (void)handleSpeechRecognizeDidFail:(NSString *_Nullable)errorMsg;
// 单向录制
- (void)handleRecordDidStart;
- (void)handleRecordDidSuccess:(NSString *_Nonnull)filePath fullFilePath:(NSString *_Nonnull)fullFilePath videoLenth:(int)videoLenth;
- (void)handleRecordDidFail:(NSString *_Nullable)errorMsg;
- (void)handleNetworkError:(NSString *_Nullable)errorMsg;
// 人脸质检
- (void)handleFaceDetectResult:(TKFacePassStatus)facePassStatus faceCompareStatus:(TKFaceCompareStatus)faceCompareStatus facNumberPassStatus:(TKSingleFaceStatus)facNumberPassStatus errorMsg:(NSString *_Nullable)errorMsg;
- (NSDictionary *)pluginCallBackfuncNoToTKPrivateOneWayVideoType;   // 获取单向视频类型


// 临时使用，后面可删掉
- (void)audioRouteChangeListenerCallback:(NSNotification *)notif;
- (void)previewVideo;

@end
