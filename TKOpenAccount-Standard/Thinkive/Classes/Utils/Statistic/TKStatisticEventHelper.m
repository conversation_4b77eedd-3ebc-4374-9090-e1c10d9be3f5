//
//  TKStatisticEventHelper.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/2/20.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKStatisticEventHelper.h"
#import "TKOpenAccountService.h"

@interface TKStatisticEventHelper()

@property (nonatomic, readwrite, strong) TKOpenAccountService *openAccountService; // 请求类

// 活体
@property (nonatomic, readwrite, assign) NSTimeInterval liveEventStartTime;    // 活体开始时间

// 双向
@property (nonatomic, readwrite, assign) NSTimeInterval witnessEventEnqueueStartTime;    // 双向排队开始时间
@property (nonatomic, readwrite, assign) NSTimeInterval witnessEventConnectStartTime;    // 双向连接开始时间
@property (nonatomic, readwrite, assign) NSTimeInterval witnessEventWitnessStartTime;    // 双向见证开始时间
@property (nonatomic, readwrite, strong) NSString *eventFlowNo; //事件流水号，串联同一个流程触发的事件
@end

@implementation TKStatisticEventHelper

+ (void)load {
    [super load];
    
    [TKStatisticEventHelper shareInstance];
}



#pragma mark - Public Selector
// 单例
+ (TKStatisticEventHelper *)shareInstance {
    
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc]init];
    });
    return instance;
}

/// 发送H5埋点事件
/// - Parameters:
///   - eventName: 事件类型。字符串
///   - eventDic: 事件参数
+ (void)sendH5Event:(NSString *)eventName
           eventDic:(NSDictionary *)eventDic {
    
    TKStatisticEventHelper *shareInstance = [TKStatisticEventHelper shareInstance];
    if ([shareInstance.delegate respondsToSelector:@selector(h5StatisticEventHelperDidCallBack:params:)]) {
        [shareInstance.delegate h5StatisticEventHelperDidCallBack:eventName params:eventDic];
    }
}

#pragma mark - 通用统计事件
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
         eventDic:(NSDictionary *)eventDic {
    
    [self sendEvent:eventName
       subEventName:subEventName
           progress:progress
             result:result
        cardSurface:TKPrivateCardSurfaceNone
  cardRecognizeType:TKPrivateCardRecognizeTypeNone
        orientation:TKPrivateVideoOrientationNone
    oneWayVideoType:TKPrivateOneWayVideoTypeNone
prepareVideoProgress:TKPrivatePrepareVideoProgressNone
           eventDic:eventDic];
}

+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
           result:(TKPrivateEventResult)result
         eventDic:(NSDictionary *)eventDic {
    
    [self sendEvent:eventName
       subEventName:subEventName
           progress:TKPrivateEventProgressNone
             result:result
           eventDic:eventDic];
}

+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
         eventDic:(NSDictionary *)eventDic {
    [self sendEvent:eventName
       subEventName:subEventName
           progress:progress
             result:TKPrivateEventResultNone
           eventDic:eventDic];
}

+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         eventDic:(NSDictionary *)eventDic {
    [self sendEvent:eventName
       subEventName:subEventName
           progress:TKPrivateEventProgressNone
             result:TKPrivateEventResultNone
           eventDic:eventDic];
}

+ (void)sendEvent:(TKPrivateEvent)eventName
         eventDic:(NSDictionary *)eventDic {
    
    [self sendEvent:eventName
       subEventName:TKPrivateSubEventNone
           progress:TKPrivateEventProgressNone
             result:TKPrivateEventResultNone
           eventDic:eventDic];
}

#pragma mark 证件统计事件
/// 发送证件识别事件(SDK内部使用)
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
      cardSurface:(TKPrivateCardSurface)cardSurface
cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
         eventDic:(NSDictionary *)eventDic {
    
    [self sendEvent:eventName
       subEventName:subEventName
           progress:progress
             result:result
        cardSurface:cardSurface
  cardRecognizeType:cardRecognizeType
        orientation:TKPrivateVideoOrientationLandscape
    oneWayVideoType:TKPrivateOneWayVideoTypeNone
prepareVideoProgress:TKPrivatePrepareVideoProgressNone
           eventDic:eventDic];
}

/// 发送证件识别事件(SDK内部使用)
/// - Parameters:
///   - eventName: 事件名
///   - subEventName: 子事件名。默认TKPrivateSubEventNone。为TKPrivateSubEventNone时，不需要关注该参数
///   - progress: 事件进度。默认TKPrivateEventProgressNone。为TKPrivateEventProgressNone时，不需要关注该参数
///   - result: 事件结果。默认TKPrivateEventResultNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - cardSurface: 卡片、证件正反面。默认TKPrivateCardSurfaceNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - cardRecognizeType: 识别方式。默认TKPrivateCardRecognizeTypeNone。为TKPrivateCardRecognizeTypeNone时，不需要关注该参数
///   - eventDic: 事件参数
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
      cardSurface:(TKPrivateCardSurface)cardSurface
cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
      orientation:(TKPrivateVideoOrientation)orientation
         eventDic:(NSDictionary *)eventDic{
    
    [self sendEvent:eventName
       subEventName:subEventName
           progress:progress
             result:result
        cardSurface:cardSurface
  cardRecognizeType:cardRecognizeType
        orientation:orientation
    oneWayVideoType:TKPrivateOneWayVideoTypeNone
prepareVideoProgress:TKPrivatePrepareVideoProgressNone
           eventDic:eventDic];
}

#pragma mark - 视频统计事件
/// 发送视频见证事件（双向+单向）(SDK内部使用)
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
      orientation:(TKPrivateVideoOrientation)orientation
  oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
         eventDic:(NSDictionary *)eventDic {
    
    [self sendEvent:eventName
       subEventName:subEventName
           progress:progress
             result:result
        cardSurface:TKPrivateCardSurfaceNone
  cardRecognizeType:TKPrivateCardRecognizeTypeNone
        orientation:orientation
    oneWayVideoType:oneWayVideoType
prepareVideoProgress:prepareVideoProgress
           eventDic:eventDic];
}


#pragma mark - Private Selector
/// 发送证件识别事件(SDK内部使用)
/// - Parameters:
///   - eventName: 事件名
///   - subEventName: 子事件名。默认TKPrivateSubEventNone。为TKPrivateSubEventNone时，不需要关注该参数
///   - progress: 事件进度。默认TKPrivateEventProgressNone。为TKPrivateEventProgressNone时，不需要关注该参数
///   - result: 事件结果。默认TKPrivateEventResultNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - cardSurface: 卡片、证件正反面。默认TKPrivateCardSurfaceNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - oneWayVideoType: 识别方式。默认TKPrivateCardRecognizeTypeNone。为TKPrivateCardRecognizeTypeNone时，不需要关注该参数
///   - orientation: 视频方向。默认TKPrivateVideoOrientationNone。为TKPrivateVideoOrientationNone时，不需要关注该参数
///   - oneWayVideoType: 单向视频类型。默认TKPrivateOneWayVideoTypeNone。为TKPrivateOneWayVideoTypeNone时，不需要关注该参数
///   - prepareVideoProgress: 视频准备流程。默认TKPrivatePrepareVideoProgressNone。为TKPrivatePrepareVideoProgressNone时，不需要关注该参数
///   - eventDic: 事件参数
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
      cardSurface:(TKPrivateCardSurface)cardSurface
cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
      orientation:(TKPrivateVideoOrientation)orientation
  oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
         eventDic:(NSDictionary *)eventDic {

    TKStatisticEventHelper *shareInstance = [TKStatisticEventHelper shareInstance];
    TKStatisticEvent event = [shareInstance combineStatisticEvent:eventName
                                                      subEventName:subEventName
                                                          progress:progress
                                                            result:result
                                                       cardSurface:cardSurface
                                                 cardRecognizeType:cardRecognizeType
                                                       orientation:orientation
                                                   oneWayVideoType:oneWayVideoType
                                              prepareVideoProgress:prepareVideoProgress
                                                          eventDic:eventDic];
    
    NSMutableDictionary *params = [shareInstance combineStatisticParam:eventName
                                                   subEventName:subEventName
                                                       progress:progress
                                                         result:result
                                                    cardSurface:cardSurface
                                              cardRecognizeType:cardRecognizeType
                                                    orientation:orientation
                                                oneWayVideoType:oneWayVideoType
                                           prepareVideoProgress:prepareVideoProgress
                                                       eventDic:eventDic];
    params[@"eventTime"] = [self getTimeStamp] ; // 获取当前时间戳
    
    if (event == TK_LIVE_DETECT_RESULT ||
        event == TK_SERVER_LIVE_DETECT_RESULT) {
        params[@"costTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - shareInstance.liveEventStartTime) * 1000] ; // 单位毫秒
    }
    
    if (event == TK_VIDEO_WITNESS_QUEUE_HOLD_DOWN ||
        event == TK_VIDEO_WITNESS_QUEUE_ERROR ||
        event == TK_VIDEO_WITNESS_QUEUE_SUCCESS) {
        
        params[@"queueCostTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - shareInstance.witnessEventEnqueueStartTime) * 1000] ; // 单位毫秒
    }
    
    if (event == TK_VIDEO_WITNESS_CONNECT_ERROR ||
        event == TK_VIDEO_WITNESS_CONNECT_HOLD_DOWN    ||
        event == TK_VIDEO_WITNESS_CONNECT_SUCCESS) {
        
        params[@"connectCostTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - shareInstance.witnessEventConnectStartTime) * 1000] ; // 单位毫秒
    }
    
    if (event == TK_VIDEO_WITNESS_ROOM_RESULT ||
        event == TK_VIDEO_WITNESS_ROOM_HOLD_DOWN ||
        event == TK_VIDEO_WITNESS_ROOM_ERROR ||
        event == TK_VIDEO_WITNESS_ROOM_REJECT ||
        event == TK_VIDEO_WITNESS_ROOM_SUCCESS||
        event == TK_VIDEO_WITNESS_ROOM_START) {
        
        params[@"witnessCostTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - shareInstance.witnessEventWitnessStartTime) * 1000] ; // 单位毫秒
    }
    
    TKLogDebug(@"思迪统计日志：输出的event = %@, \r params = %@", TKStatisticEventToString(event), params);
    [shareInstance callBackAppWith:event params:params];
}

#pragma mark 组合事件
// 核心方法
- (TKStatisticEvent)combineStatisticEvent:(TKPrivateEvent)eventName
                             subEventName:(TKPrivateSubEvent)subEventName
                                 progress:(TKPrivateEventProgress)progress
                                   result:(TKPrivateEventResult)result
                              cardSurface:(TKPrivateCardSurface)cardSurface
                        cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
                              orientation:(TKPrivateVideoOrientation)orientation
                          oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
                     prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
                                 eventDic:(NSDictionary *)eventDic
{
    TKLogDebug(@"思迪统计日志：传入的\r eventName = %@, \r subEventName = %@, \r progress = %@, \r result = %@, \r cardSurface = %@, \r cardRecognizeType = %@, \r orientation = %@, \r oneWayVideoType = %@, \r prepareVideoProgress = %@, \r eventDic = %@",
          TKPrivateEventToString(eventName),
          TKPrivateSubEventToString(subEventName),
          TKPrivateEventProgressToString(progress),
          TKPrivateEventResultToString(result),
          TKPrivateCardSurfaceToString(cardSurface),
          TKPrivateCardRecognizeTypeToString(cardRecognizeType),
          TKPrivateVideoOrientationToString(orientation),
          TKPrivateOneWayVideoTypeToString(oneWayVideoType),
          TKPrivatePrepareVideoProgressToString(prepareVideoProgress),
          eventDic);
    
    TKStatisticEvent event = TKStatisticEventUnknown;

    switch (eventName) {
        case TKPrivateEventIDCard:  // 身份证事件
            return event = [self combineIDCardStatisticEvent:eventName subEventName:subEventName progress:progress result:result cardSurface:cardSurface cardRecognizeType:cardRecognizeType orientation:orientation oneWayVideoType:oneWayVideoType prepareVideoProgress:prepareVideoProgress eventDic:eventDic];
            break;
        case TKPrivateEventBandCard:    // 银行卡事件
            return event = [self combineBandCardStatisticEvent:eventName subEventName:subEventName progress:progress result:result cardSurface:cardSurface cardRecognizeType:cardRecognizeType orientation:orientation oneWayVideoType:oneWayVideoType prepareVideoProgress:prepareVideoProgress eventDic:eventDic];
            break;
        case TKPrivateEventVideoWitness:    // 双向事件
            return event = [self combineVideoWitnessStatisticEvent:eventName subEventName:subEventName progress:progress result:result cardSurface:cardSurface cardRecognizeType:cardRecognizeType orientation:orientation oneWayVideoType:oneWayVideoType prepareVideoProgress:prepareVideoProgress eventDic:eventDic];
            break;
            
        case TKPrivateEventLiveDetect:    // 活体事件
            return event = [self combineLiveDetectStatisticEvent:eventName subEventName:subEventName progress:progress result:result cardSurface:cardSurface cardRecognizeType:cardRecognizeType orientation:orientation oneWayVideoType:oneWayVideoType prepareVideoProgress:prepareVideoProgress eventDic:eventDic];
            break;
        case TKPrivateEventOneWayVideo:    // 单向事件
            return event = [self combineOneWayVideoStatisticEvent:eventName subEventName:subEventName progress:progress result:result cardSurface:cardSurface cardRecognizeType:cardRecognizeType orientation:orientation oneWayVideoType:oneWayVideoType prepareVideoProgress:prepareVideoProgress eventDic:eventDic];
            break;
        default:
            break;
    }
    
    return event;
}


// 核心方法-身份证
- (TKStatisticEvent)combineIDCardStatisticEvent:(TKPrivateEvent)eventName
                             subEventName:(TKPrivateSubEvent)subEventName
                                 progress:(TKPrivateEventProgress)progress
                                   result:(TKPrivateEventResult)result
                              cardSurface:(TKPrivateCardSurface)cardSurface
                        cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
                              orientation:(TKPrivateVideoOrientation)orientation
                          oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
                     prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
                                 eventDic:(NSDictionary *)eventDic
{
    TKStatisticEvent event = TKStatisticEventUnknown;
    TKPrivateCardRecognizeType newType = (TKPrivateCardRecognizeType)[eventDic[@"newType"] intValue];
    
    if (cardSurface == TKPrivateCardSurfaceFront) {
        
        if (cardRecognizeType == TKPrivateCardRecognizeTypeTakePhoto) {   // 拍照
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_IDCARD_FRONT_TAKE_START;     // 身份证正面拍照开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
                return event = TK_IDCARD_FRONT_TAKE_CANCEL;    // 身份证正面拍照取消
            }
            
            if (subEventName == TKPrivateSubEventIDCardGetImageDone) {
                return event = TK_IDCARD_FRONT_TAKE_PHOTO;     // 身份证正面拍照完成
            }
            
            if (subEventName == TKPrivateSubEventIDCardSelectOtherType && newType == TKPrivateCardRecognizeTypeOCRScan) {
                return event = TK_IDCARD_FRONT_TAKE_GO_SCAN;   // 身份证正面进入扫描
            }
            
            if (subEventName == TKPrivateSubEventIDCardSelectOtherType && newType == TKPrivateCardRecognizeTypeAlbum) {
                return event = TK_IDCARD_FRONT_TAKE_ALBUM;     // 身份证正面相册选择
            }
            
            if (subEventName == TKPrivateSubEventIDCardPreview) {
                return event = TK_IDCARD_FRONT_TAKE_PREVIEW_START; // 身份证正面拍照预览开始
            }
            
            if (subEventName == TKPrivateSubEventIDCardRetry) {
                return event = TK_IDCARD_FRONT_TAKE_RELOAD;    // 身份证正面拍照重新拍摄
            }
            
            if (subEventName == TKPrivateSubEventIDCardUpload) {
                return event = TK_IDCARD_FRONT_TAKE_COMMIT;    // 身份证正面拍照提交
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
                return event = TK_IDCARD_FRONT_TAKE_RESULT;    // 身份证正面拍照结果
            }
        } else if (cardRecognizeType == TKPrivateCardRecognizeTypeOCRScan) {    // 扫描
            
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_IDCARD_FRONT_SCAN_START;     // 身份证_正面_扫描_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
                return event = TK_IDCARD_FRONT_SCAN_CANCEL;    // 身份证_正面_扫描_返回
            }
            
            if (subEventName == TKPrivateSubEventIDCardSelectOtherType && newType == TKPrivateCardRecognizeTypeTakePhoto) {
                return event = TK_IDCARD_FRONT_SCAN_GO_TAKE;   // 身份证_正面_扫描_切拍照
            }
            
            if (subEventName == TKPrivateSubEventIDCardSelectOtherType && newType == TKPrivateCardRecognizeTypeAlbum) {
                return event = TK_IDCARD_FRONT_SCAN_ALBUM;     // 身份证_正面_扫描_相册
            }
            
            if (subEventName == TKPrivateSubEventIDCardPreview) {
                return event = TK_IDCARD_FRONT_SCAN_PREVIEW_START; // 身份证_正面_扫描_预览
            }
            
            if (subEventName == TKPrivateSubEventIDCardRetry) {
                return event = TK_IDCARD_FRONT_SCAN_RELOAD;    // 身份证_正面_扫描_重拍
            }
            
            if (subEventName == TKPrivateSubEventIDCardUpload) {
                return event = TK_IDCARD_FRONT_SCAN_COMMIT;    // 身份证_正面_扫描_提交
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
                return event = TK_IDCARD_FRONT_SCAN_RESULT;    // 身份证_正面_扫描_结果
            }
        } else if (cardRecognizeType == TKPrivateCardRecognizeTypeAlbum) {    // 相册
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_IDCARD_FRONT_ALBUM_START;    // 身份证正面相册开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
                return event = TK_IDCARD_FRONT_ALBUM_CANCEL;   // 身份证正面相册取消
            }
            
            if (subEventName == TKPrivateSubEventIDCardPreview) {
                
                return event = TK_IDCARD_FRONT_ALBUM_PREVIEW_START; // 身份证正面相册预览开始
            }
            
            if (subEventName == TKPrivateSubEventIDCardRetry) {
                return event = TK_IDCARD_FRONT_ALBUM_RELOAD;   // 身份证正面相册重新选择
            }
            
            if (subEventName == TKPrivateSubEventIDCardUpload) {
                return event = TK_IDCARD_FRONT_ALBUM_COMMIT;   // 身份证正面相册提交
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
                return event = TK_IDCARD_FRONT_ALBUM_RESULT;   // 身份证正面相册结果
            }
        }
    } else if (cardSurface == TKPrivateCardSurfaceBack) {
        
        if (cardRecognizeType == TKPrivateCardRecognizeTypeTakePhoto) {   // 拍照
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_IDCARD_BACK_TAKE_START;      // 身份证反面拍照开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
                return event = TK_IDCARD_BACK_TAKE_CANCEL;     // 身份证反面拍照取消
            }
            
            if (subEventName == TKPrivateSubEventIDCardGetImageDone) {
                return event = TK_IDCARD_BACK_TAKE_PHOTO_START; // 身份证反面拍照完成
            }
            
            if (subEventName == TKPrivateSubEventIDCardSelectOtherType && newType == TKPrivateCardRecognizeTypeOCRScan) {
                return event = TK_IDCARD_BACK_TAKE_GO_SCAN;    // 身份证反面进入扫描
            }
            
            if (subEventName == TKPrivateSubEventIDCardSelectOtherType && newType == TKPrivateCardRecognizeTypeAlbum) {
                return event = TK_IDCARD_BACK_TAKE_ALBUM;      // 身份证反面相册选择
            }
            
            if (subEventName == TKPrivateSubEventIDCardPreview) {
                return event = TK_IDCARD_BABK_TAKE_PREVIEW_START; // 身份证反面拍照预览开始
            }
            
            if (subEventName == TKPrivateSubEventIDCardRetry) {
                return event = TK_IDCARD_BACK_TAKE_RELOAD;     // 身份证反面拍照重新拍摄
            }
            
            if (subEventName == TKPrivateSubEventIDCardUpload) {
                return event = TK_IDCARD_BACK_TAKE_COMMIT;     // 身份证反面拍照提交
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
                return event = TK_IDCARD_BACK_TAKE_RESULT;     // 身份证反面拍照结果
            }
        } else if (cardRecognizeType == TKPrivateCardRecognizeTypeOCRScan) {    // 扫描
            
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_IDCARD_BACK_SCAN_START;      // 身份证_反面_扫描_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
                return event = TK_IDCARD_BACK_SCAN_CANCEL;     // 身份证_反面_扫描_返回
            }
            
            if (subEventName == TKPrivateSubEventIDCardSelectOtherType && newType == TKPrivateCardRecognizeTypeTakePhoto) {
                return event = TK_IDCARD_BACK_SCAN_GO_TAKE;    // 身份证_反面_扫描_切拍照
            }
            
            if (subEventName == TKPrivateSubEventIDCardSelectOtherType && newType == TKPrivateCardRecognizeTypeAlbum) {
                return event = TK_IDCARD_BACK_SCAN_ALBUM;      // 身份证_反面_扫描_相册
            }
            
            if (subEventName == TKPrivateSubEventIDCardPreview) {
                return event = TK_IDCARD_BACK_SCAN_PREVIEW_START; // 身份证_反面_扫描_预览
            }
            
            if (subEventName == TKPrivateSubEventIDCardRetry) {
                return event = TK_IDCARD_BACK_SCAN_RELOAD;     // 身份证_反面_扫描_重拍
            }
            
            if (subEventName == TKPrivateSubEventIDCardUpload) {
                return event = TK_IDCARD_BACK_SCAN_COMMIT;     // 身份证_反面_扫描_提交
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
                return event = TK_IDCARD_BACK_SCAN_RESULT;     // 身份证_反面_扫描_结果
            }
        } else if (cardRecognizeType == TKPrivateCardRecognizeTypeAlbum) {    // 相册
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_IDCARD_BACK_ALBUM_START;     // 身份证反面相册开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
                return event = TK_IDCARD_BACK_ALBUM_CANCEL;    // 身份证反面相册取消
            }
            
            if (subEventName == TKPrivateSubEventIDCardPreview) {
                
                return event = TK_IDCARD_BACK_ALBUM_PREVIEW_START; // 身份证反面相册预览开始
            }
            
            if (subEventName == TKPrivateSubEventIDCardRetry) {
                return event = TK_IDCARD_BACK_ALBUM_RELOAD;    // 身份证反面相册重新选择
            }
            
            if (subEventName == TKPrivateSubEventIDCardUpload) {
                return event = TK_IDCARD_BACK_ALBUM_COMMIT;    // 身份证反面相册提交
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
                return event = TK_IDCARD_BACK_ALBUM_RESULT;    // 身份证反面相册结果
            }
        }
    }
    return event;
}

// 核心方法-银行卡
- (TKStatisticEvent)combineBandCardStatisticEvent:(TKPrivateEvent)eventName
                             subEventName:(TKPrivateSubEvent)subEventName
                                 progress:(TKPrivateEventProgress)progress
                                   result:(TKPrivateEventResult)result
                              cardSurface:(TKPrivateCardSurface)cardSurface
                        cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
                              orientation:(TKPrivateVideoOrientation)orientation
                          oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
                     prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
                                 eventDic:(NSDictionary *)eventDic
{
    TKStatisticEvent event = TKStatisticEventUnknown;
    TKPrivateCardRecognizeType newType = (TKPrivateCardRecognizeType)[eventDic[@"newType"] intValue];
    
    if (cardRecognizeType == TKPrivateCardRecognizeTypeOCRScan) {
        if (subEventName == TKPrivateSubEventStart) {
            return event = TK_BANKCARD_SCAN_START; // 银行卡_扫描_开始
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
            return event = TK_BANKCARD_SCAN_CANCEL; // 银行卡_扫描_返回
        }
        
        if (subEventName == TKPrivateSubEventBandCardSelectOtherType && newType == TKPrivateCardRecognizeTypeTakePhoto) {
            return event = TK_BANKCARD_SCAN_GO_TAKE; // 银行卡_扫描_切拍照
        }
        
        if (subEventName == TKPrivateSubEventBandCardSelectOtherType && newType == TKPrivateCardRecognizeTypeAlbum) {
            return event = TK_BANKCARD_SCAN_ALBUM;               // 银行卡_扫描_切相册
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
            return event = TK_BANKCARD_SCAN_RESULT; // 银行卡_扫描_结果
        }
    }
    
    if (cardRecognizeType == TKPrivateCardRecognizeTypeAlbum) {
        if (subEventName == TKPrivateSubEventStart) {
            return event =     TK_BANKCARD_ALBUM_START;               // 银行卡_相册_开始
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
            return event = TK_BANKCARD_ALBUM_CANCEL;              // 银行卡_相册_返回
        }
        
        if (subEventName == TKPrivateSubEventBandCardPreview) {
            return event = TK_BANKCARD_ALBUM_PREVIEW_START;       // 银行卡_相册_预览
        }
        
        if (subEventName == TKPrivateSubEventBandCardRetry) {
            return event = TK_BANKCARD_ALBUM_RELOAD;              // 银行卡_相册_重拍
        }
        
        if (subEventName == TKPrivateSubEventBandCardSubmit) {
            return event = TK_BANKCARD_ALBUM_COMMIT;              // 银行卡_相册_提交
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
            return event = TK_BANKCARD_ALBUM_RESULT;              // 银行卡_相册_结果
        }
    }
    
    if (cardRecognizeType == TKPrivateCardRecognizeTypeTakePhoto) {
        if (subEventName == TKPrivateSubEventStart) {
            return event = TK_BANKCARD_TAKE_START; // 银行卡_拍照_开始
        }
        
        if (subEventName == TKPrivateSubEventBandCardGetImageDone) {
            return event = TK_BANKCARD_TAKE_TAKE_PHOTO; // 银行卡_拍照_确认
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
            return event = TK_BANKCARD_TAKE_CANCEL; // 银行卡_拍照_返回
        }
        
        if (subEventName == TKPrivateSubEventBandCardSelectOtherType && newType == TKPrivateCardRecognizeTypeOCRScan) {
            return event = TK_BANKCARD_TAKE_GO_SCAN; // 银行卡_拍照_切扫描
        }
        
        if (subEventName == TKPrivateSubEventBandCardSelectOtherType && newType == TKPrivateCardRecognizeTypeAlbum) {
            return event = TK_BANKCARD_TAKE_ALBUM;                // 银行卡_拍照_切相册
        }
    
        if (subEventName == TKPrivateSubEventBandCardPreview) {
            return event = TK_BANKCARD_TAKE_PREVIEW_START; // 银行卡_拍照_预览
        }
        
        if (subEventName == TKPrivateSubEventBandCardRetry) {
            return event = TK_BANKCARD_TAKE_RELOAD; // 银行卡_拍照_重拍
        }
        
        if (subEventName == TKPrivateSubEventBandCardSubmit) {
            return event = TK_BANKCARD_TAKE_COMMIT; // 银行卡_拍照_提交
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
            return event = TK_BANKCARD_TAKE_RESULT; // 银行卡_拍照_结果
        }
    }
    
    return event;
}

// 核心方法-双向
- (TKStatisticEvent)combineVideoWitnessStatisticEvent:(TKPrivateEvent)eventName
                             subEventName:(TKPrivateSubEvent)subEventName
                                 progress:(TKPrivateEventProgress)progress
                                   result:(TKPrivateEventResult)result
                              cardSurface:(TKPrivateCardSurface)cardSurface
                        cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
                              orientation:(TKPrivateVideoOrientation)orientation
                          oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
                     prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
                                 eventDic:(NSDictionary *)eventDic
{
    TKStatisticEvent event = TKStatisticEventUnknown;
    BOOL enqueueSuccess = [eventDic[@"enqueueSuccess"] boolValue];
    BOOL connectSuccess = [eventDic[@"connectSuccess"] boolValue];
    
    if (subEventName == TKPrivateSubEventVideoWitnessEnqueue && progress == TKPrivateEventProgressStart) {
        self.witnessEventEnqueueStartTime = [[NSDate date] timeIntervalSince1970];
        
        return event = TK_VIDEO_WITNESS_QUEUE_START; // 视频见证_排队_开始
    }

    if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && enqueueSuccess == NO) {
        
        return event = TK_VIDEO_WITNESS_QUEUE_HOLD_DOWN;   // 视频见证_排队_用户挂断
    }
    
    if (subEventName == TKPrivateSubEventNone &&
        (result == TKPrivateEventResultFail || result == TKPrivateEventResultError) && enqueueSuccess == NO) {
        return event = TK_VIDEO_WITNESS_QUEUE_ERROR; // 视频见证_排队_异常
    }
    
    if (subEventName == TKPrivateSubEventVideoWitnessEnqueue && result == TKPrivateEventResultSuccess) {
        self.witnessEventConnectStartTime = [[NSDate date] timeIntervalSince1970];
        
        return event = TK_VIDEO_WITNESS_QUEUE_SUCCESS; // 视频见证_排队_成功
    }
    
    if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && enqueueSuccess == YES && connectSuccess == NO) {
        return event = TK_VIDEO_WITNESS_CONNECT_HOLD_DOWN; // 视频见证_连接_取消
    }
    
    if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultError && enqueueSuccess == YES && connectSuccess == NO) {
        return event = TK_VIDEO_WITNESS_CONNECT_ERROR; // 视频见证_连接_异常
    }
    
    if (subEventName == TKPrivateSubEventVideoWitnessPrepareVideo && result == TKPrivateEventResultSuccess && prepareVideoProgress == TKPrivatePrepareVideoProgressEnterRoom) {
        
        return event = TK_VIDEO_WITNESS_CONNECT_SUCCESS; // 视频见证_连接_成功
    }
    
    if (subEventName == TKPrivateSubEventVideoWitnessPrepareVideo && prepareVideoProgress == TKPrivatePrepareVideoProgressVideoReady) {
        
        self.witnessEventWitnessStartTime = [[NSDate date] timeIntervalSince1970];
        
        return event = TK_VIDEO_WITNESS_ROOM_START;   // 视频见证_见证_开始
    }
    
    if (subEventName == TKPrivateSubEventVideoWitnessReceiveCMD) {
        return event = TK_VIDEO_WITNESS_ROOM_RESULT;   // 视频⻅证_见证_结果
    }
    
    if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd) {
        if (result == TKPrivateEventResultCancel && connectSuccess == YES) {
            return event = TK_VIDEO_WITNESS_ROOM_HOLD_DOWN;   // 视频见证_见证_用户挂断
        } else if (result == TKPrivateEventResultError && connectSuccess == YES) {
            return event = TK_VIDEO_WITNESS_ROOM_ERROR;   // 视频见证_见证_异常断开
        } else if (result == TKPrivateEventResultFail && connectSuccess == YES) {
            return event = TK_VIDEO_WITNESS_ROOM_REJECT;   // 视频见证_见证_失败
        } else {
            return event = TK_VIDEO_WITNESS_ROOM_SUCCESS; // 视频见证_见证_成功
        }
    }
    
    return event;
}

// 核心方法-活体
- (TKStatisticEvent)combineLiveDetectStatisticEvent:(TKPrivateEvent)eventName
                             subEventName:(TKPrivateSubEvent)subEventName
                                 progress:(TKPrivateEventProgress)progress
                                   result:(TKPrivateEventResult)result
                              cardSurface:(TKPrivateCardSurface)cardSurface
                        cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
                              orientation:(TKPrivateVideoOrientation)orientation
                          oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
                     prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
                                 eventDic:(NSDictionary *)eventDic
{
    TKStatisticEvent event = TKStatisticEventUnknown;
    TKPrivateLiveDetectType liveDetectType = (TKPrivateLiveDetectType)[eventDic[@"liveDetectType"] intValue];
    
    if (liveDetectType == TKPrivateLiveDetectTypeLocal) {
        // 本地活体
        if (subEventName == TKPrivateSubEventStart) {
            self.liveEventStartTime = [[NSDate date] timeIntervalSince1970];
            
            return event = TK_LIVE_DETECT_START; // 活体识别_开始
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
            return event = TK_LIVE_DETECT_CANCEL; // 活体识别_返回
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
            return event = TK_LIVE_DETECT_RESULT; // 活体识别_结果
        }
    }
    
    // 服务端活体
    if (liveDetectType == TKPrivateLiveDetectTypeServer) {
        if (subEventName == TKPrivateSubEventStart) {
            self.liveEventStartTime = [[NSDate date] timeIntervalSince1970];
            
            return event = TK_SERVER_LIVE_DETECT_START; // 服务器活体识别_开始
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel) {
            return event = TK_SERVER_LIVE_DETECT_CANCEL; // 服务器活体识别_返回
        }
        
        if (subEventName == TKPrivateSubEventLiveDetectRequestRoom && result == TKPrivateEventResultSuccess) {
            return event = TK_SERVER_LIVE_DETECT_APPLY_SUCCESS; // 服务器活体识别_申请成功
        }
        
        if (subEventName == TKPrivateSubEventLiveDetectPrepareVideo && result == TKPrivateEventResultSuccess) {
            return event = TK_SERVER_LIVE_DETECT_CONNECT_SUCCESS; // 服务器活体识别_连接成功
        }
        
        if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd  && result != TKPrivateEventResultCancel) {
            return event = TK_SERVER_LIVE_DETECT_RESULT; // 服务器活体识别_结果
        }
    }
    
    return event;
}

// 核心方法-单向
- (TKStatisticEvent)combineOneWayVideoStatisticEvent:(TKPrivateEvent)eventName
                             subEventName:(TKPrivateSubEvent)subEventName
                                 progress:(TKPrivateEventProgress)progress
                                   result:(TKPrivateEventResult)result
                              cardSurface:(TKPrivateCardSurface)cardSurface
                        cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
                              orientation:(TKPrivateVideoOrientation)orientation
                          oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
                     prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
                                 eventDic:(NSDictionary *)eventDic
{
    TKStatisticEvent event = TKStatisticEventUnknown;
    BOOL recordSuccess = [eventDic[@"recordSuccess"] boolValue];
    BOOL isEndRecord = [eventDic[@"isEndRecord"] boolValue];
    
    // 竖屏
    if (orientation == TKPrivateVideoOrientationPortrait) {
        // 朗读单向
        if (oneWayVideoType == TKPrivateOneWayVideoTypeNormal) {
            
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_START; // 朗读单向视频_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == NO) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_CANCEL; // 朗读单向视频_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressStart) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_RECORD_START; // 朗读单向视频_开始录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressEnd) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_RECORD_END; // 朗读单向视频_结束录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_START; // 朗读单向视频_预览_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_CANCEL; // 朗读单向视频_预览返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_RERECORD; // 朗读单向视频_预览_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSubmit) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_COMMIT; // 朗读单向视频_预览_提交
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_FAIL_START; // 朗读单向视频_错误_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_FAIL_CANCEL; // 朗读单向视频_错误_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_FAIL_RERECORD; // 朗读单向视频_错误_重录
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result != TKPrivateEventResultCancel) {
                return event = TK_NORMAL_ONE_WAY_VIDEO_RESULT; // 朗读单向视频_结果
            }
        }
        
        // 智能单向
        if (oneWayVideoType == TKPrivateOneWayVideoTypeLocalSmart) {
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_SMART_ONE_WAY_VIDEO_START; // 智能单向视频_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == NO) {
                return event = TK_SMART_ONE_WAY_VIDEO_CANCEL; // 智能单向视频_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressStart) {
                return event = TK_SMART_ONE_WAY_VIDEO_RECORD_START; // 智能单向视频_开始录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressEnd) {
                return event = TK_SMART_ONE_WAY_VIDEO_RECORD_END; // 智能单向视频_结束录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_SMART_ONE_WAY_VIDEO_PREVIEW_START; // 智能单向视频_预览_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_SMART_ONE_WAY_VIDEO_PREVIEW_CANCEL; // 智能单向视频_预览返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_SMART_ONE_WAY_VIDEO_PREVIEW_RERECORD; // 智能单向视频_预览_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_SMART_ONE_WAY_VIDEO_FAIL_START; // 智能单向视频_错误_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_SMART_ONE_WAY_VIDEO_FAIL_CANCEL; // 智能单向视频_错误_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_SMART_ONE_WAY_VIDEO_FAIL_RERECORD; // 智能单向视频_错误_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSubmit) {
                return event = TK_SMART_ONE_WAY_VIDEO_PREVIEW_COMMIT; // 智能单向视频_预览_提交
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result != TKPrivateEventResultCancel) {
                return event = TK_SMART_ONE_WAY_VIDEO_RESULT; // 智能单向视频_结果
            }
        }
        
        // Tchat智能单向
        if (oneWayVideoType == TKPrivateOneWayVideoTypeTChatSmart) {
            
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_START; // Tchat智能单向视频_开始
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRequestRoom && result == TKPrivateEventResultSuccess) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_APPLY_SUCCESS; // Tchat智能单向视频_申请成功
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPrepareVideo && result == TKPrivateEventResultSuccess) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_CONNECT_SUCCESS; // Tchat智能单向视频_连接成功
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == NO) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_CANCEL; // Tchat智能单向视频_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressStart) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_RECORD_START; // Tchat智能单向视频_开始录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressEnd) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_RECORD_END; // Tchat智能单向视频_结束录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_START; // Tchat智能单向视频_预览_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_CANCEL; // TChat智能单向视频_预览返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_RERECORD; // Tchat智能单向视频_预览_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_FAIL_START; // Tchat智能单向视频_错误_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_FAIL_CANCEL; // Tchat智能单向视频_错误_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_FAIL_RERECORD; // Tchat智能单向视频_错误_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSubmit) {
                if (progress == TKPrivateEventProgressStart) {
                    return event = TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT; // Tchat智能单向视频_预览_提交
                } else if (progress == TKPrivateEventProgressEnd) {
                    return event = TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT; // Tchat智能单向视频_预览_提交结果
                }
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result != TKPrivateEventResultCancel) {
                return event = TK_TCHAT_ONE_WAY_VIDEO_RESULT; // Tchat智能单向视频_结果
            }
        }
        
        // 虚拟人单向
        if (oneWayVideoType == TKPrivateOneWayVideoTypeTChatDigitalMan) {
            
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_VH_ONE_WAY_VIDEO_START;  // 虚拟人智能单向视频_开始
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRequestRoom && result == TKPrivateEventResultSuccess) {
                return event = TK_VH_ONE_WAY_VIDEO_APPLY_SUCCESS; // 虚拟人智能单向视频_申请成功
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPrepareVideo && result == TKPrivateEventResultSuccess) {
                return event = TK_VH_ONE_WAY_VIDEO_CONNECT_SUCCESS; // 虚拟人智能单向视频_连接成功
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == NO) {
                return event = TK_VH_ONE_WAY_VIDEO_CANCEL; // 虚拟人智能单向视频_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressStart) {
                return event = TK_VH_ONE_WAY_VIDEO_RECORD_START; // 虚拟人智能单向视频_开始录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressEnd) {
                return event = TK_VH_ONE_WAY_VIDEO_RECORD_END; // 虚拟人智能单向视频_结束录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_VH_ONE_WAY_VIDEO_PREVIEW_START; // 虚拟人智能单向视频_预览_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_VH_ONE_WAY_VIDEO_PREVIEW_CANCEL; // 虚拟人智能单向视频_预览返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_VH_ONE_WAY_VIDEO_PREVIEW_RERECORD; // 虚拟人智能单向视频_预览_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_VH_ONE_WAY_VIDEO_FAIL_START; // 虚拟人智能单向视频_错误_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_VH_ONE_WAY_VIDEO_FAIL_CANCEL; // 虚拟人智能单向视频_错误_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_VH_ONE_WAY_VIDEO_FAIL_RERECORD; // 虚拟人智能单向视频_错误_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSubmit) {
                if (progress == TKPrivateEventProgressStart) {
                    return event = TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT; // 虚拟人智能单向视频_预览_提交
                } else if (progress == TKPrivateEventProgressEnd) {
                    return event = TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT; // 虚拟人智能单向视频_预览_提交结果
                }
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result != TKPrivateEventResultCancel) {
                return event = TK_VH_ONE_WAY_VIDEO_RESULT; // 虚拟人智能单向视频_结果
            }
        }
    }
    
    // 横屏
    if (orientation == TKPrivateVideoOrientationLandscape) {
        // 朗读单向
        if (oneWayVideoType == TKPrivateOneWayVideoTypeNormal) {
            
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_START; // 横屏朗读单向视频_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == NO) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_CANCEL; // 横屏朗读单向视频_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressStart) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_RECORD_START; // 横屏朗读单向视频_开始录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressEnd) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_RECORD_END; // 横屏朗读单向视频_结束录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_START; // 横屏朗读单向视频_预览_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_CANCEL; // 横屏朗读单向视频_预览返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == YES) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_RERECORD; // 横屏朗读单向视频_预览_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSubmit) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_COMMIT; // 横屏朗读单向视频_预览_提交
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_START; // 横屏朗读单向视频_错误_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_CANCEL; // 横屏朗读单向视频_错误_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES && recordSuccess == NO) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_RERECORD; // 横屏朗读单向视频_错误_重录
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result != TKPrivateEventResultCancel) {
                return event = TK_LANDS_NORMAL_ONE_WAY_VIDEO_RESULT; // 横屏朗读单向视频_结果
            }
        }
        
        // 智能单向
        if (oneWayVideoType == TKPrivateOneWayVideoTypeLocalSmart) {
            
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_START; // 横屏智能单向视频_开始
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSwitchCamera) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_SWAP; // 横屏智能单向视频_切换摄像头
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == NO) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_CANCEL; // 横屏智能单向视频_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressStart) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_START; // 横屏智能单向视频_开始录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressEnd) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_END; // 横屏智能单向视频_结束录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_START; // 横屏智能单向视频_预览_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_CANCEL; // 横屏智能单向视频_预览返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_RERECORD; // 横屏智能单向视频_预览_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_START; // 横屏智能单向视频_错误_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_CANCEL; // 横屏智能单向视频_错误_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_RERECORD; // 横屏智能单向视频_错误_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSubmit) {
                
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_COMMIT; // 横屏智能单向视频_预览_提交
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result != TKPrivateEventResultCancel) {
                return event = TK_LANDS_SMART_ONE_WAY_VIDEO_RESULT; // 横屏智能单向视频_结果
            }
        }
        
        // Tchat智能单向
        if (oneWayVideoType == TKPrivateOneWayVideoTypeTChatSmart) {
            
            if (subEventName == TKPrivateSubEventStart) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_START; // 横屏Tchat智能单向视频_开始
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRequestRoom && result == TKPrivateEventResultSuccess) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_APPLY_SUCCESS; // 横屏Tchat智能单向视频_申请成功
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPrepareVideo && result == TKPrivateEventResultSuccess) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_CONNECT_SUCCESS; // 横屏Tchat智能单向视频_连接成功
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == NO) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_CANCEL; // 横屏Tchat智能单向视频_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressStart) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_START; // 横屏Tchat智能单向视频_开始录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRecord && progress == TKPrivateEventProgressEnd) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_END; // 横屏Tchat智能单向视频_结束录制
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSwitchCamera) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_SWAP; // 横屏Tchat智能单向视频_切换摄像头
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_START; // 横屏Tchat智能单向视频_预览_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_CANCEL; // 横屏Tchat智能单向视频_预览返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES  && recordSuccess == YES) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_RERECORD; // 横屏Tchat智能单向视频_预览_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoPreview && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_START; // 横屏Tchat智能单向视频_错误_开始
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result == TKPrivateEventResultCancel && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_CANCEL; // 横屏Tchat智能单向视频_错误_返回
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoRerecord && isEndRecord == YES  && recordSuccess == NO) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_RERECORD; // 横屏Tchat智能单向视频_错误_重录
            }
            
            if (subEventName == TKPrivateSubEventOneWayVideoSubmit) {
                if (progress == TKPrivateEventProgressStart) {
                    return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT; // 横屏Tchat智能单向视频_预览_提交
                } else if (progress == TKPrivateEventProgressEnd) {
                    return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT; // 横屏Tchat智能单向视频_预览_提交结果
                }
                
            }
            
            if (subEventName == TKPrivateSubEventNone && progress == TKPrivateEventProgressEnd && result != TKPrivateEventResultCancel) {
                return event = TK_LANDS_TCHAT_ONE_WAY_VIDEO_RESULT; // 横屏Tchat智能单向视频_结果
            }
        }
        
    }

    return event;
}

// 核心方法
- (NSMutableDictionary *)combineStatisticParam:(TKPrivateEvent)eventName
                             subEventName:(TKPrivateSubEvent)subEventName
                                 progress:(TKPrivateEventProgress)progress
                                   result:(TKPrivateEventResult)result
                              cardSurface:(TKPrivateCardSurface)cardSurface
                        cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
                              orientation:(TKPrivateVideoOrientation)orientation
                          oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
                     prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
                                 eventDic:(NSDictionary *)eventDic
{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];

    //解析extParams
    [params addEntriesFromDictionary:eventDic[@"extParams"]];
    
    if (subEventName == TKPrivateSubEventVideoWitnessReceiveCMD) {
        params[@"message"] = eventDic[@"message"];
    }
    
    if (eventDic[@"costTime"]) {
        params[@"costTime"] = eventDic[@"costTime"];
    }
    if (eventDic[@"errorNo"]) {
        params[@"errorNo"] = eventDic[@"errorNo"];
    }
    if (eventDic[@"event_err"]) {
        params[@"event_err"] = eventDic[@"event_err"];
    }
    if (!params[@"requestHeaders"] && eventDic[@"requestHeaders"]) {
        params[@"requestHeaders"] = eventDic[@"requestHeaders"];
    }
    if (!params[@"requestParams"] && eventDic[@"requestParams"]) {
        params[@"requestParams"] = eventDic[@"requestParams"];
    }
    
    if (!params[@"requestHeaders"] && eventDic[@"requestHeaders"]) {
            params[@"requestHeaders"] = eventDic[@"requestHeaders"];
    }
    if (!params[@"requestParams"] && eventDic[@"requestParams"]) {
        params[@"requestParams"] = eventDic[@"requestParams"];
    }
    return params;
}

#pragma mark 回调
- (void)callBackAppWith:(TKStatisticEvent)event params:(NSDictionary *_Nullable)params
{
    if (event == TKStatisticEventUnknown) return;
    //为了处理三方实现埋点和SDK发送请求埋点并存情况；这里可以都实现
    if ([self.delegate respondsToSelector:@selector(statisticEventHelperDidCallBack:params:)]) {
        [self.delegate statisticEventHelperDidCallBack:event params:params];
    }

    NSString *url = [params getStringWithKey:@"eventUrl"];
    if ([TKStringHelper isNotEmpty:url]) {
        // SDK发送上传事件
        [self sdkHandleStatisticEventHelperDidCallBack:event params:params];
    }
}

- (void)sdkHandleStatisticEventHelperDidCallBack:(TKStatisticEvent)event params:(NSDictionary *_Nullable)params {
    NSString *eventName = nil;
    NSMutableDictionary *newParams = [NSMutableDictionary dictionaryWithDictionary:params];
    switch (event) {
            
            //        // 单向
            //        case TK_NORMAL_ONE_WAY_VIDEO_START: // 朗读单向视频_开始
            //        case TK_SMART_ONE_WAY_VIDEO_START: // 智能单向视频_开始
            //        case TK_TCHAT_ONE_WAY_VIDEO_START: // Tchat智能单向视频_开始
            //        case TK_VH_ONE_WAY_VIDEO_START: // 朗读单向视频_开始
            //        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_START: // 横屏朗读单向视频_开始
            //        case TK_LANDS_SMART_ONE_WAY_VIDEO_START: // 横屏智能单向视频_开始
            //        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_START: // 横屏Tchat智能单向视频_开始
            //            eventName = @"kh_dxrecord_start";
            //            break;
        case TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_START: // 朗读单向视频_预览_开始
        case TK_SMART_ONE_WAY_VIDEO_PREVIEW_START: // 智能单向视频_预览_开始
        case TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_START: // Tchat智能单向视频_预览_开始
        case TK_VH_ONE_WAY_VIDEO_PREVIEW_START: // 虚拟人智能单向视频_预览_开始
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_START: // 横屏朗读单向视频_预览_开始
        case TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_START: // 横屏智能单向视频_预览_开始
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_START: // 横屏Tchat智能单向视频_预览_开始
            // 开户使用参数
            eventName = @"kh_dxrecord_result";
            newParams[@"eventId"] = eventName;
            newParams[@"opfailType"] = @"4000";
            newParams[@"result"] = @"1";
            newParams[@"witnessType"] = @"1";
            break;
        case TK_NORMAL_ONE_WAY_VIDEO_CANCEL: // 普通单向视频_取消
        case TK_SMART_ONE_WAY_VIDEO_CANCEL: // 智能单向视频_取消
        case TK_TCHAT_ONE_WAY_VIDEO_CANCEL: // Tchat智能单向视频_取消
        case TK_VH_ONE_WAY_VIDEO_CANCEL: // 虚拟人智能单向视频_取消
        case TK_LANDS_SMART_ONE_WAY_VIDEO_CANCEL: // 横屏智能单向视频_取消
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_CANCEL: // 横屏Tchat智能单向视频_取消
        {
            // 开户使用参数
            eventName = @"kh_dxrecord_result";
            newParams[@"eventId"] = eventName;
            newParams[@"opfailType"] = @"4001";
            newParams[@"result"] = @"0";
            newParams[@"witnessType"] = @"1";
            
//            // 业务中台使用参数
//            NSString *errorNo = [params getStringWithKey:@"errorNo"];
//            newParams[@"result"] = @"0";
//            newParams[@"event_err"]=newParams[@"eventMsg"] = params[@"event_err"];
//            if (event == TK_NORMAL_ONE_WAY_VIDEO_CANCEL) {   // 普通单向视频_取消
//
//                eventName = @"06003";
//                newParams[@"startEventNo"] = @"06001";
//            } else if (event == TK_SMART_ONE_WAY_VIDEO_CANCEL) {    // 智能单向视频_取消
//
//                eventName = @"06103";
//                newParams[@"startEventNo"] = @"06101";
//            } else if (event == TK_TCHAT_ONE_WAY_VIDEO_CANCEL) {    // Tchat智能单向视频_取消
//
//                eventName = @"06203";
//                newParams[@"startEventNo"] = @"06201";
//            }
//            newParams[@"eventResult"] = newParams[@"result"];
//            newParams[@"witnessType"] = @"1";
//            newParams[@"opfailType"] = errorNo;
//            newParams[@"eventTime"] = params[@"eventTime"];
//            break;
        }
        case TK_NORMAL_ONE_WAY_VIDEO_FAIL_START: // 普通单向视频_错误_开始
        case TK_SMART_ONE_WAY_VIDEO_FAIL_START: // 智能单向视频_错误_开始
        case TK_TCHAT_ONE_WAY_VIDEO_FAIL_START: // Tchat智能单向视频_错误_开始
        case TK_VH_ONE_WAY_VIDEO_FAIL_START: // 虚拟人智能单向视频_错误_开始
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_START: // 横屏智能单向视频_错误_开始
        case TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_START: // 横屏智能单向视频_错误_开始
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_START: // 横屏Tchat智能单向视频_错误_开始
            
        case TK_NORMAL_ONE_WAY_VIDEO_RESULT: // 普通单向视频_结果
        case TK_SMART_ONE_WAY_VIDEO_RESULT: // 智能单向视频_结果
        case TK_TCHAT_ONE_WAY_VIDEO_RESULT: // Tchat智能单向视频_结果
        case TK_VH_ONE_WAY_VIDEO_RESULT: // 虚拟人智能单向视频_结果
        case TK_LANDS_SMART_ONE_WAY_VIDEO_RESULT: // 横屏智能单向视频_结果
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_RESULT: // 横屏Tchat智能单向视频_结果
        {
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            if (![errorNo isEqualToString:@"0"] && ![errorNo isEqualToString:@"-1"]){
                // 开户使用参数
                NSInteger inputNumber = [errorNo integerValue];
                NSInteger resultNumber = 4000 - inputNumber;
                NSString *resultString = [NSString stringWithFormat:@"%ld", (long)resultNumber];
                eventName = @"kh_dxrecord_result";
                newParams[@"eventId"] = eventName;
                newParams[@"opfailType"] = resultString;
                newParams[@"result"] = @"0";
                newParams[@"witnessType"] = @"1";
                newParams[@"eventId"] = eventName;
            }
            
            if (event == TK_TCHAT_ONE_WAY_VIDEO_RESULT  // 普通单向视频_结果
                || event == TK_SMART_ONE_WAY_VIDEO_RESULT    // 智能单向视频_结果
                || event == TK_TCHAT_ONE_WAY_VIDEO_RESULT   // Tchat智能单向视频_结果
                || event == TK_VH_ONE_WAY_VIDEO_RESULT      // 虚拟人智能单向视频_结果
                || event == TK_LANDS_SMART_ONE_WAY_VIDEO_RESULT  // 横屏智能单向视频_结果
                || event == TK_LANDS_TCHAT_ONE_WAY_VIDEO_RESULT // 横屏Tchat智能单向视频_结果
                ) {
                NSString *errorNo = [params getStringWithKey:@"errorNo"];
                
                // 业务中台使用参数
                if ([errorNo isEqualToString:@"0"]) {
                    newParams[@"result"] = @"1";
                    newParams[@"event_err"]=newParams[@"eventMsg"] =@"原生单向视频完成";
                } else  {
                    newParams[@"result"] = @"0";
                    newParams[@"event_err"]=newParams[@"eventMsg"] = params[@"event_err"];
                }
                if (event == TK_TCHAT_ONE_WAY_VIDEO_RESULT) {   // 普通单向视频_结果
                    
                    eventName = @"06009";
                    newParams[@"startEventNo"] = @"06001";
                    newParams[@"eventNo"] = eventName;
                } else if (event == TK_SMART_ONE_WAY_VIDEO_RESULT) {    // 智能单向视频_结果
                    
                    eventName = @"06109";
                    newParams[@"startEventNo"] = @"06101";
                    newParams[@"eventNo"] = eventName;
                } else if (event == TK_TCHAT_ONE_WAY_VIDEO_RESULT) {    // Tchat智能单向视频_结果
                    
                    eventName = @"06211";
                    newParams[@"startEventNo"] = @"06201";
                    newParams[@"eventNo"] = eventName;
                }
                newParams[@"eventResult"] = newParams[@"result"];
                newParams[@"witnessType"] = @"1";
                newParams[@"opfailType"] = errorNo;
                newParams[@"eventTime"] = params[@"eventTime"];
            }
            break;
        }
        case TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT: // Tchat智能单向视频_预览_提交结果
        case TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT: // 虚拟人智能单向视频_预览_提交结果
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT: // 横屏Tchat智能单向视频_预览_提交结果
        {
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            // 开户使用参数
            if (![errorNo isEqualToString:@"0"]) {
                newParams[@"opfailType"] = @"5001"; // 上传失败
            } else {
                newParams[@"opfailType"] = @"5000"; // 上传成功
            }
            eventName = @"kh_dxupload_result";
            newParams[@"eventId"] = eventName;
            newParams[@"witnessType"] = @"1";
            break;
        }
            
        //活体
        case TK_LIVE_DETECT_RESULT: // 活体识别_结果
        {
            NSString *errorNo = [params getStringWithKey:@"errorNo"];

            // 业务中台使用参数
            if ([errorNo isEqualToString:@"0"]) {
                newParams[@"result"] = @"1";
                newParams[@"event_err"]=newParams[@"eventMsg"] =@"活体识别成功";
            } else  {
                newParams[@"result"] = @"0";
                newParams[@"event_err"]=newParams[@"eventMsg"] = params[@"event_err"];
            }
            eventName = @"05004";
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"05001";
            newParams[@"eventResult"] = newParams[@"result"];
            
            newParams[@"opfailType"] = errorNo;
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        
        // 双向
        case TK_VIDEO_WITNESS_QUEUE_START: // 视频见证_排队_开始
        {
            self.eventFlowNo=@"";//排队流水号以每次排队流程统计；开始排队前先清空上一次的
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            // 业务中台使用参数
            eventName = @"01001";
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"01001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"1";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户加入排队";
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"queueCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        case TK_VIDEO_WITNESS_QUEUE_HOLD_DOWN: // 视频见证_排队_取消
        {
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            // 业务中台使用参数
            if ([errorNo isEqualToString:@"-1"]) {
                eventName = @"01002";
                newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户取消排队";
            } else if ([errorNo isEqualToString:@"-10"]) {
                eventName = @"01003";
                newParams[@"event_err"]=newParams[@"eventMsg"] = @"无坐席在线";
            }
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"01001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"0";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"queueCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        case TK_VIDEO_WITNESS_QUEUE_ERROR: // 视频见证_排队_异常
        {
            // 业务中台使用参数
            eventName = @"01004";
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"01001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"0";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"eventMsg"] = params[@"event_err"];
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"queueCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
//        case TK_VIDEO_WITNESS_QUEUE_SUCCESS: // 视频见证_排队_成功（用户开始连接视频）
//        {
//            NSString *errorNo = [params getStringWithKey:@"errorNo"];
//            // 业务中台使用参数
////            eventName = @"02001";
////            newParams[@"startEventNo"] = @"02001";
//            newParams[@"eventNo"] = eventName;
//            newParams[@"witnessType"] = @"0";
//            newParams[@"result"] = @"1";
//            newParams[@"eventResult"] = newParams[@"result"];
//            newParams[@"eventMsg"] = params[@"event_err"];
//            newParams[@"opfailType"] = errorNo;
//            newParams[@"durationtime"] = params[@"queueCostTime"];
//            newParams[@"eventTime"] = params[@"eventTime"];
//            break;
//        }
        case TK_VIDEO_WITNESS_CONNECT_SUCCESS: // 视频见证_连接_成功
        {
            // 业务中台使用参数
            eventName = @"02003";
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"02001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"1";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户连接结果成功";
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"connectCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        case TK_VIDEO_WITNESS_CONNECT_HOLD_DOWN: // 视频见证_连接_取消
        {
            // 业务中台使用参数
            eventName = @"02003";
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"02001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"0";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户取消视频";
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"connectCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        case TK_VIDEO_WITNESS_CONNECT_ERROR: // 视频见证_连接_异常
        {
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            // 业务中台使用参数
            eventName = @"02003";
            newParams[@"eventNo"] = eventName;
            if ([errorNo isEqualToString:@"-3"]) {
                newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户连接异常";
            } else if ([errorNo isEqualToString:@"-4"]) {
                newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户连接异常";
            } else if ([errorNo isEqualToString:@"-5"]) {
                newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户连接超时";
            }
            newParams[@"startEventNo"] = @"02001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"0";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"connectCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        case TK_VIDEO_WITNESS_ROOM_HOLD_DOWN:  // 视频见证_见证_用户挂断
        {
            // 业务中台使用参数
            eventName = @"03003";
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"03001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"0";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户挂断视频（点击挂断）";
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"witnessCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        case TK_VIDEO_WITNESS_ROOM_ERROR:     // 视频见证_见证_异常断开
        {
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            // 业务中台使用参数
            eventName = @"03005";
            newParams[@"eventNo"] = eventName;
            if ([errorNo isEqualToString:@"-6"]) {
                newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户连接服务器失败";
            } else if ([errorNo isEqualToString:@"-7"]) {
                newParams[@"event_err"]=newParams[@"eventMsg"] = @"坐席连接异常";
            }
            newParams[@"startEventNo"] = @"03001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"0";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"eventMsg"] = params[@"event_err"];
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"witnessCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }

        case TK_VIDEO_WITNESS_ROOM_START: // 视频见证_见证_开始
        {
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            // 业务中台使用参数
            eventName = @"03001";
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"03001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"1";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"event_err"]=newParams[@"eventMsg"] = @"用户开始视频";
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"queueCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        case TK_VIDEO_WITNESS_ROOM_RESULT:     // 视频见证_见证_结果
        {
            NSString *errorNo = [params getStringWithKey:@"errorNo"];
            // 业务中台使用参数
            eventName = @"04001";
            newParams[@"eventNo"] = eventName;
            newParams[@"startEventNo"] = @"04001";
            newParams[@"witnessType"] = @"0";
            newParams[@"result"] = @"1";
            newParams[@"eventResult"] = newParams[@"result"];
            newParams[@"event_err"]=newParams[@"eventMsg"] = [TKStringHelper decodeURL:params[@"message"]];
            newParams[@"opfailType"] = errorNo;
            newParams[@"durationTime"] = params[@"witnessCostTime"];
            newParams[@"eventTime"] = params[@"eventTime"];
            break;
        }
        default:
            break;
    }
    
    if ([TKStringHelper isEmpty:eventName]) return;
    [self sendRequestToUploadEventWith:eventName params:newParams];
}

- (void)sendRequestToUploadEventWith:(NSString *)eventName params:(NSDictionary *)params
{
//    if ([params isKindOfClass:NSDictionary.class] && params.allKeys.count > 0) {
    NSString *url = [params getStringWithKey:@"eventUrl"];
    if ([TKStringHelper isEmpty:url]) {
        TKLogDebug(@"思迪统计日志：url为空，取消sdk 上报统计事件");
        return;
    }
    
//        NSString *userId = [params getStringWithKey:@"userId"];
//        NSString *durationTime = [params getStringWithKey:@"durationTime"];
//        NSString *result = [params getStringWithKey:@"result"];
//        NSString *opfailType = [params getStringWithKey:@"opfailType"];
//        NSString *opfailMsg = [params getStringWithKey:@"opfailMsg"];
//        NSString *url = [params getStringWithKey:@"eventUrl"];
//        NSString *userId = [params getStringWithKey:@"userId"];
        
        NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:params];
        [param removeObjectForKey:@"eventUrl"];
//        param[@"eventNo"] = eventName;
        if (param[@"costTime"]) {
            param[@"durationTime"] = param[@"costTime"];
        }
        [param removeObjectForKey:@"costTime"];
        [param removeObjectForKey:@"event_err"];
        [param removeObjectForKey:@"errorNo"];
        [param removeObjectForKey:@"queueCostTime"];
        [param removeObjectForKey:@"connectCostTime"];
        [param removeObjectForKey:@"witnessCostTime"];
        param[@"takerType"] = @"02";
        param[@"opfailMsg"] = param[@"eventMsg"];
    //        param[@"eventId"] = param[@"eventNo"];
        param[@"result"] = param[@"eventResult"];
        if(param[@"requestHeaders"]&&[param[@"requestHeaders"] isKindOfClass:[NSString class]]){
            param[@"requestHeaders"]=[TKDataHelper jsonToDictionary:param[@"requestHeaders"]];//因为安卓extParams里面只能key-value都是字符串形式，让h5传json字符串，原生做处理
        }

        if([TKStringHelper isEmpty:param[@"eventFlowNo"]]){
            if([TKStringHelper isNotEmpty:self.eventFlowNo]){
                param[@"eventFlowNo"] = self.eventFlowNo;
            }
        }

        // 测试数据
//        url = @"https://opt-dev.thinkive.com:15149/kh-stats-server/data/event";
//        param[@"userId"] = @"10000064";
//        param[@"userName"] = @"10000064";
//        param[@"startEventNo"] = @"video_queue";
//        param[@"eventId"] = @"other";
//        param[@"result"] = @"0";
//        param[@"opfailType"] = @"0";
//        param[@"opfailMsg"] = @"测试数据";
//        param[@"durationTime"] = @"1000";
//        param[@"witnessType"] = @"0";
    
//        param[@"requestHeaders"] = @{@"tk-token-authorization" : @"Bearer b1892199aa9941548bc988dfa6470c38|1800000|***********"};
        
        __weak typeof(self) weakSelf = self;
        
        TKLogDebug(@"发送埋点事件eventName = %@，\r params = %@", eventName, param);
        [self.openAccountService handleStatisticEventWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {

            TKLogDebug(@"上传统计事件结果 = %i", resultVo.errorNo == 0);
            if(resultVo.errorNo == 0){
                NSArray *arr = (NSArray*)resultVo.results;
                
                if (arr.count > 0) {
                    
                    NSDictionary *resReslut = [arr objectAtIndex:0];
                    if([TKStringHelper isEmpty:weakSelf.eventFlowNo]){
                        weakSelf.eventFlowNo = resReslut[@"eventFlowNo"];
                    }
                    
                }
            }
        }];
//    }
}

+ (NSString *)getTimeStamp{

    // 设置想要的格式，hh与HH的区别:分别表示12小时制,24小时制
    //把NSDate按formatter格式转成NSString
//    NSString *currentTime = [TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
    NSString *currentTime=[NSString stringWithFormat:@"%.0f", [[NSDate date] timeIntervalSince1970] * 1000];
    return currentTime;
}

#pragma mark 宏转字符串
// 枚举 TKPrivateEvent 转字符串
NSString *TKStatisticEventToString(TKStatisticEvent event) {
    switch (event) {
        case TK_IDCARD_FRONT_TAKE_START:
            return @"TK_IDCARD_FRONT_TAKE_START";
        case TK_IDCARD_FRONT_TAKE_CANCEL:
            return @"TK_IDCARD_FRONT_TAKE_CANCEL";
        case TK_IDCARD_FRONT_TAKE_PHOTO:
            return @"TK_IDCARD_FRONT_TAKE_PHOTO";
        case TK_IDCARD_FRONT_TAKE_GO_SCAN:
            return @"TK_IDCARD_FRONT_TAKE_GO_SCAN";
        case TK_IDCARD_FRONT_TAKE_ALBUM:
            return @"TK_IDCARD_FRONT_TAKE_ALBUM";
        case TK_IDCARD_FRONT_TAKE_PREVIEW_START:
            return @"TK_IDCARD_FRONT_TAKE_PREVIEW_START";
        case TK_IDCARD_FRONT_TAKE_RELOAD:
            return @"TK_IDCARD_FRONT_TAKE_RELOAD";
        case TK_IDCARD_FRONT_TAKE_COMMIT:
            return @"TK_IDCARD_FRONT_TAKE_COMMIT";
        case TK_IDCARD_FRONT_TAKE_RESULT:
            return @"TK_IDCARD_FRONT_TAKE_RESULT";
            
        case TK_IDCARD_BACK_TAKE_START:
            return @"TK_IDCARD_BACK_TAKE_START";
        case TK_IDCARD_BACK_TAKE_CANCEL:
            return @"TK_IDCARD_BACK_TAKE_CANCEL";
        case TK_IDCARD_BACK_TAKE_PHOTO_START:
            return @"TK_IDCARD_BACK_TAKE_PHOTO_START";
        case TK_IDCARD_BACK_TAKE_GO_SCAN:
            return @"TK_IDCARD_BACK_TAKE_GO_SCAN";
        case TK_IDCARD_BACK_TAKE_ALBUM:
            return @"TK_IDCARD_BACK_TAKE_ALBUM";
        case TK_IDCARD_BABK_TAKE_PREVIEW_START:
            return @"TK_IDCARD_BABK_TAKE_PREVIEW_START";
        case TK_IDCARD_BACK_TAKE_RELOAD:
            return @"TK_IDCARD_BACK_TAKE_RELOAD";
        case TK_IDCARD_BACK_TAKE_COMMIT:
            return @"TK_IDCARD_BACK_TAKE_COMMIT";
        case TK_IDCARD_BACK_TAKE_RESULT:
            return @"TK_IDCARD_BACK_TAKE_RESULT";
            
        case TK_IDCARD_FRONT_ALBUM_START:
            return @"TK_IDCARD_FRONT_ALBUM_START";
        case TK_IDCARD_FRONT_ALBUM_CANCEL:
            return @"TK_IDCARD_FRONT_ALBUM_CANCEL";
        case TK_IDCARD_FRONT_ALBUM_PREVIEW_START:
            return @"TK_IDCARD_FRONT_ALBUM_PREVIEW_START";
        case TK_IDCARD_FRONT_ALBUM_RELOAD:
            return @"TK_IDCARD_FRONT_ALBUM_RELOAD";
        case TK_IDCARD_FRONT_ALBUM_COMMIT:
            return @"TK_IDCARD_FRONT_ALBUM_COMMIT";
        case TK_IDCARD_FRONT_ALBUM_RESULT:
            return @"TK_IDCARD_FRONT_ALBUM_RESULT";
            
        case TK_IDCARD_BACK_ALBUM_START:
            return @"TK_IDCARD_BACK_ALBUM_START";
        case TK_IDCARD_BACK_ALBUM_CANCEL:
            return @"TK_IDCARD_BACK_ALBUM_CANCEL";
        case TK_IDCARD_BACK_ALBUM_PREVIEW_START:
            return @"TK_IDCARD_BACK_ALBUM_PREVIEW_START";
        case TK_IDCARD_BACK_ALBUM_RELOAD:
            return @"TK_IDCARD_BACK_ALBUM_RELOAD";
        case TK_IDCARD_BACK_ALBUM_COMMIT:
            return @"TK_IDCARD_BACK_ALBUM_COMMIT";
        case TK_IDCARD_BACK_ALBUM_RESULT:
            return @"TK_IDCARD_BACK_ALBUM_RESULT";
            
        case TK_IDCARD_FRONT_SCAN_START:
            return @"TK_IDCARD_FRONT_SCAN_START";
        case TK_IDCARD_FRONT_SCAN_CANCEL:
            return @"TK_IDCARD_FRONT_SCAN_CANCEL";
        case TK_IDCARD_FRONT_SCAN_GO_TAKE:
            return @"TK_IDCARD_FRONT_SCAN_GO_TAKE";
        case TK_IDCARD_FRONT_SCAN_ALBUM:
            return @"TK_IDCARD_FRONT_SCAN_ALBUM";
        case TK_IDCARD_FRONT_SCAN_PREVIEW_START:
            return @"TK_IDCARD_FRONT_SCAN_PREVIEW_START";
        case TK_IDCARD_FRONT_SCAN_RELOAD:
            return @"TK_IDCARD_FRONT_SCAN_RELOAD";
        case TK_IDCARD_FRONT_SCAN_COMMIT:
            return @"TK_IDCARD_FRONT_SCAN_COMMIT";
        case TK_IDCARD_FRONT_SCAN_RESULT:
            return @"TK_IDCARD_FRONT_SCAN_RESULT";
            
        case TK_IDCARD_BACK_SCAN_START:
            return @"TK_IDCARD_BACK_SCAN_START";
        case TK_IDCARD_BACK_SCAN_CANCEL:
            return @"TK_IDCARD_BACK_SCAN_CANCEL";
        case TK_IDCARD_BACK_SCAN_GO_TAKE:
            return @"TK_IDCARD_BACK_SCAN_GO_TAKE";
        case TK_IDCARD_BACK_SCAN_ALBUM:
            return @"TK_IDCARD_BACK_SCAN_ALBUM";
        case TK_IDCARD_BACK_SCAN_PREVIEW_START:
            return @"TK_IDCARD_BACK_SCAN_PREVIEW_START";
        case TK_IDCARD_BACK_SCAN_RELOAD:
            return @"TK_IDCARD_BACK_SCAN_RELOAD";
        case TK_IDCARD_BACK_SCAN_COMMIT:
            return @"TK_IDCARD_BACK_SCAN_COMMIT";
        case TK_IDCARD_BACK_SCAN_RESULT:
            return @"TK_IDCARD_BACK_SCAN_RESULT";
            
        case TK_BANKCARD_SCAN_START:
            return @"TK_BANKCARD_SCAN_START";
        case TK_BANKCARD_SCAN_CANCEL:
            return @"TK_BANKCARD_SCAN_CANCEL";
        case TK_BANKCARD_SCAN_GO_TAKE:
            return @"TK_BANKCARD_SCAN_GO_TAKE";
        case TK_BANKCARD_SCAN_ALBUM:
            return @"TK_BANKCARD_SCAN_ALBUM";
        case TK_BANKCARD_SCAN_RESULT:
            return @"TK_BANKCARD_SCAN_RESULT";
            
        case TK_BANKCARD_ALBUM_START:
            return @"TK_BANKCARD_ALBUM_START";
        case TK_BANKCARD_ALBUM_CANCEL:
            return @"TK_BANKCARD_ALBUM_CANCEL";
        case TK_BANKCARD_ALBUM_PREVIEW_START:
            return @"TK_BANKCARD_ALBUM_PREVIEW_START";
        case TK_BANKCARD_ALBUM_RELOAD:
            return @"TK_BANKCARD_ALBUM_RELOAD";
        case TK_BANKCARD_ALBUM_COMMIT:
            return @"TK_BANKCARD_ALBUM_COMMIT";
        case TK_BANKCARD_ALBUM_RESULT:
            return @"TK_BANKCARD_ALBUM_RESULT";
            
        case TK_BANKCARD_TAKE_START:
            return @"TK_BANKCARD_TAKE_START";
        case TK_BANKCARD_TAKE_CANCEL:
            return @"TK_BANKCARD_TAKE_CANCEL";
        case TK_BANKCARD_TAKE_TAKE_PHOTO:
            return @"TK_BANKCARD_TAKE_TAKE_PHOTO";
        case TK_BANKCARD_TAKE_GO_SCAN:
            return @"TK_BANKCARD_TAKE_GO_SCAN";
        case TK_BANKCARD_TAKE_ALBUM:
            return @"TK_BANKCARD_TAKE_ALBUM";
        case TK_BANKCARD_TAKE_PREVIEW_START:
            return @"TK_BANKCARD_TAKE_PREVIEW_START";
        case TK_BANKCARD_TAKE_RELOAD:
            return @"TK_BANKCARD_TAKE_RELOAD";
        case TK_BANKCARD_TAKE_COMMIT:
            return @"TK_BANKCARD_TAKE_COMMIT";
        case TK_BANKCARD_TAKE_RESULT:
            return @"TK_BANKCARD_TAKE_RESULT";
            
        case TK_VIDEO_WITNESS_QUEUE_START:
            return @"TK_VIDEO_WITNESS_QUEUE_START";
        case TK_VIDEO_WITNESS_QUEUE_HOLD_DOWN:
            return @"TK_VIDEO_WITNESS_QUEUE_HOLD_DOWN";
        case TK_VIDEO_WITNESS_QUEUE_ERROR:
            return @"TK_VIDEO_WITNESS_QUEUE_ERROR";
        case TK_VIDEO_WITNESS_QUEUE_SUCCESS:
            return @"TK_VIDEO_WITNESS_QUEUE_SUCCESS";
        case TK_VIDEO_WITNESS_CONNECT_HOLD_DOWN:
            return @"TK_VIDEO_WITNESS_CONNECT_HOLD_DOWN";
        case TK_VIDEO_WITNESS_CONNECT_ERROR:
            return @"TK_VIDEO_WITNESS_CONNECT_ERROR";
        case TK_VIDEO_WITNESS_CONNECT_SUCCESS:
            return @"TK_VIDEO_WITNESS_CONNECT_SUCCESS";
        case TK_VIDEO_WITNESS_ROOM_START:
            return @"TK_VIDEO_WITNESS_ROOM_START";
        case TK_VIDEO_WITNESS_ROOM_HOLD_DOWN:
            return @"TK_VIDEO_WITNESS_ROOM_HOLD_DOWN";
        case TK_VIDEO_WITNESS_ROOM_ERROR:
            return @"TK_VIDEO_WITNESS_ROOM_ERROR";
        case TK_VIDEO_WITNESS_ROOM_RESULT:
            return @"TK_VIDEO_WITNESS_ROOM_RESULT";
        case TK_VIDEO_WITNESS_ROOM_SUCCESS:
            return @"TK_VIDEO_WITNESS_ROOM_SUCCESS";
        case TK_VIDEO_WITNESS_ROOM_REJECT:
            return @"TK_VIDEO_WITNESS_ROOM_REJECT";
            
        case TK_LIVE_DETECT_START:
            return @"TK_LIVE_DETECT_START";
        case TK_LIVE_DETECT_CANCEL:
            return @"TK_LIVE_DETECT_CANCEL";
        case TK_LIVE_DETECT_RESULT:
            return @"TK_LIVE_DETECT_RESULT";
            
        case TK_SERVER_LIVE_DETECT_START:
            return @"TK_SERVER_LIVE_DETECT_START";
        case TK_SERVER_LIVE_DETECT_APPLY_SUCCESS:
            return @"TK_SERVER_LIVE_DETECT_APPLY_SUCCESS";
        case TK_SERVER_LIVE_DETECT_CONNECT_SUCCESS:
            return @"TK_SERVER_LIVE_DETECT_CONNECT_SUCCESS";
        case TK_SERVER_LIVE_DETECT_CANCEL:
            return @"TK_SERVER_LIVE_DETECT_CANCEL";
        case TK_SERVER_LIVE_DETECT_RESULT:
            return @"TK_SERVER_LIVE_DETECT_RESULT";
            
        case TK_NORMAL_ONE_WAY_VIDEO_START:
            return @"TK_NORMAL_ONE_WAY_VIDEO_START";
        case TK_NORMAL_ONE_WAY_VIDEO_CANCEL:
            return @"TK_NORMAL_ONE_WAY_VIDEO_CANCEL";
        case TK_NORMAL_ONE_WAY_VIDEO_RECORD_START:
            return @"TK_NORMAL_ONE_WAY_VIDEO_RECORD_START";
        case TK_NORMAL_ONE_WAY_VIDEO_RECORD_END:
            return @"TK_NORMAL_ONE_WAY_VIDEO_RECORD_END";
        case TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_START:
            return @"TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_START";
        case TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_CANCEL:
            return @"TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_CANCEL";
        case TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_RERECORD:
            return @"TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_RERECORD";
        case TK_NORMAL_ONE_WAY_VIDEO_FAIL_START:
            return @"TK_NORMAL_ONE_WAY_VIDEO_FAIL_START";
        case TK_NORMAL_ONE_WAY_VIDEO_FAIL_CANCEL:
            return @"TK_NORMAL_ONE_WAY_VIDEO_FAIL_CANCEL";
        case TK_NORMAL_ONE_WAY_VIDEO_FAIL_RERECORD:
            return @"TK_NORMAL_ONE_WAY_VIDEO_FAIL_RERECORD";
        case TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_COMMIT:
            return @"TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_COMMIT";
        case TK_NORMAL_ONE_WAY_VIDEO_RESULT:
            return @"TK_NORMAL_ONE_WAY_VIDEO_RESULT";
            
        case TK_SMART_ONE_WAY_VIDEO_START:
            return @"TK_SMART_ONE_WAY_VIDEO_START";
        case TK_SMART_ONE_WAY_VIDEO_CANCEL:
            return @"TK_SMART_ONE_WAY_VIDEO_CANCEL";
        case TK_SMART_ONE_WAY_VIDEO_RECORD_START:
            return @"TK_SMART_ONE_WAY_VIDEO_RECORD_START";
        case TK_SMART_ONE_WAY_VIDEO_RECORD_END:
            return @"TK_SMART_ONE_WAY_VIDEO_RECORD_END";
        case TK_SMART_ONE_WAY_VIDEO_PREVIEW_START:
            return @"TK_SMART_ONE_WAY_VIDEO_PREVIEW_START";
        case TK_SMART_ONE_WAY_VIDEO_PREVIEW_CANCEL:
            return @"TK_SMART_ONE_WAY_VIDEO_PREVIEW_CANCEL";
        case TK_SMART_ONE_WAY_VIDEO_PREVIEW_RERECORD:
            return @"TK_SMART_ONE_WAY_VIDEO_PREVIEW_RERECORD";
        case TK_SMART_ONE_WAY_VIDEO_PREVIEW_COMMIT:
            return @"TK_SMART_ONE_WAY_VIDEO_PREVIEW_COMMIT";
        case TK_SMART_ONE_WAY_VIDEO_FAIL_START:
            return @"TK_SMART_ONE_WAY_VIDEO_FAIL_START";
        case TK_SMART_ONE_WAY_VIDEO_FAIL_CANCEL:
            return @"TK_SMART_ONE_WAY_VIDEO_FAIL_CANCEL";
        case TK_SMART_ONE_WAY_VIDEO_FAIL_RERECORD:
            return @"TK_SMART_ONE_WAY_VIDEO_FAIL_RERECORD";
        case TK_SMART_ONE_WAY_VIDEO_RESULT:
            return @"TK_SMART_ONE_WAY_VIDEO_RESULT";
            
        case TK_TCHAT_ONE_WAY_VIDEO_START:
            return @"TK_TCHAT_ONE_WAY_VIDEO_START";
        case TK_TCHAT_ONE_WAY_VIDEO_CANCEL:
            return @"TK_TCHAT_ONE_WAY_VIDEO_CANCEL";
        case TK_TCHAT_ONE_WAY_VIDEO_APPLY_SUCCESS:
            return @"TK_TCHAT_ONE_WAY_VIDEO_APPLY_SUCCESS";
        case TK_TCHAT_ONE_WAY_VIDEO_CONNECT_SUCCESS:
            return @"TK_TCHAT_ONE_WAY_VIDEO_CONNECT_SUCCESS";
        case TK_TCHAT_ONE_WAY_VIDEO_RECORD_START:
            return @"TK_TCHAT_ONE_WAY_VIDEO_RECORD_START";
        case TK_TCHAT_ONE_WAY_VIDEO_RECORD_END:
            return @"TK_TCHAT_ONE_WAY_VIDEO_RECORD_END";
        case TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_START:
            return @"TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_START";
        case TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_CANCEL:
            return @"TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_CANCEL";
        case TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_RERECORD:
            return @"TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_RERECORD";
        case TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT:
            return @"TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT";
        case TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT:
            return @"TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT";
        case TK_TCHAT_ONE_WAY_VIDEO_FAIL_START:
            return @"TK_TCHAT_ONE_WAY_VIDEO_FAIL_START";
        case TK_TCHAT_ONE_WAY_VIDEO_FAIL_CANCEL:
            return @"TK_TCHAT_ONE_WAY_VIDEO_FAIL_CANCEL";
        case TK_TCHAT_ONE_WAY_VIDEO_FAIL_RERECORD:
            return @"TK_TCHAT_ONE_WAY_VIDEO_FAIL_RERECORD";
        case TK_TCHAT_ONE_WAY_VIDEO_RESULT:
            return @"TK_TCHAT_ONE_WAY_VIDEO_RESULT";
            
        case TK_VH_ONE_WAY_VIDEO_START:
            return @"TK_VH_ONE_WAY_VIDEO_START";
        case TK_VH_ONE_WAY_VIDEO_CANCEL:
            return @"TK_VH_ONE_WAY_VIDEO_CANCEL";
        case TK_VH_ONE_WAY_VIDEO_APPLY_SUCCESS:
            return @"TK_VH_ONE_WAY_VIDEO_APPLY_SUCCESS";
        case TK_VH_ONE_WAY_VIDEO_CONNECT_SUCCESS:
            return @"TK_VH_ONE_WAY_VIDEO_CONNECT_SUCCESS";
        case TK_VH_ONE_WAY_VIDEO_RECORD_START:
            return @"TK_VH_ONE_WAY_VIDEO_RECORD_START";
        case TK_VH_ONE_WAY_VIDEO_RECORD_END:
            return @"TK_VH_ONE_WAY_VIDEO_RECORD_END";
        case TK_VH_ONE_WAY_VIDEO_PREVIEW_START:
            return @"TK_VH_ONE_WAY_VIDEO_PREVIEW_START";
        case TK_VH_ONE_WAY_VIDEO_PREVIEW_CANCEL:
            return @"TK_VH_ONE_WAY_VIDEO_PREVIEW_CANCEL";
        case TK_VH_ONE_WAY_VIDEO_PREVIEW_RERECORD:
            return @"TK_VH_ONE_WAY_VIDEO_PREVIEW_RERECORD";
        case TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT:
            return @"TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT";
        case TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT:
            return @"TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT";
        case TK_VH_ONE_WAY_VIDEO_FAIL_START:
            return @"TK_VH_ONE_WAY_VIDEO_FAIL_START";
        case TK_VH_ONE_WAY_VIDEO_FAIL_CANCEL:
            return @"TK_VH_ONE_WAY_VIDEO_FAIL_CANCEL";
        case TK_VH_ONE_WAY_VIDEO_FAIL_RERECORD:
            return @"TK_VH_ONE_WAY_VIDEO_FAIL_RERECORD";
        case TK_VH_ONE_WAY_VIDEO_RESULT:
            return @"TK_VH_ONE_WAY_VIDEO_RESULT";
            
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_START:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_START";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_CANCEL:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_CANCEL";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_RECORD_START:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_RECORD_START";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_RECORD_END:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_RECORD_END";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_START:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_START";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_CANCEL:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_CANCEL";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_RERECORD:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_RERECORD";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_START:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_START";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_CANCEL:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_CANCEL";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_RERECORD:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_RERECORD";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_COMMIT:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_COMMIT";
        case TK_LANDS_NORMAL_ONE_WAY_VIDEO_RESULT:
            return @"TK_LANDS_NORMAL_ONE_WAY_VIDEO_RESULT";
            
        case TK_LANDS_SMART_ONE_WAY_VIDEO_START:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_START";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_CANCEL:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_CANCEL";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_START:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_START";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_END:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_END";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_SWAP:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_SWAP";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_START:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_START";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_CANCEL:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_CANCEL";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_RERECORD:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_RERECORD";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_COMMIT:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_COMMIT";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_START:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_START";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_CANCEL:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_CANCEL";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_RERECORD:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_RERECORD";
        case TK_LANDS_SMART_ONE_WAY_VIDEO_RESULT:
            return @"TK_LANDS_SMART_ONE_WAY_VIDEO_RESULT";
            
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_START:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_START";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_CANCEL:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_CANCEL";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_APPLY_SUCCESS:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_APPLY_SUCCESS";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_CONNECT_SUCCESS:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_CONNECT_SUCCESS";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_START:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_START";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_END:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_END";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_SWAP:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_SWAP";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_START:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_START";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_CANCEL:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_CANCEL";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_RERECORD:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_RERECORD";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_START:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_START";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_CANCEL:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_CANCEL";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_RERECORD:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_RERECORD";
        case TK_LANDS_TCHAT_ONE_WAY_VIDEO_RESULT:
            return @"TK_LANDS_TCHAT_ONE_WAY_VIDEO_RESULT";
            
        default:
            return @"TKStatisticEventUnknown";
    }
}

// 枚举 TKPrivateEvent 转字符串
NSString *TKPrivateEventToString(TKPrivateEvent event) {
    switch (event) {
        case TKPrivateEventIDCard:
            return @"TKPrivateEventIDCard";
        case TKPrivateEventBandCard:
            return @"TKPrivateEventBandCard";
//        case TKPrivateEventCertificate:
//            return @"TKPrivateEventCertificate";
//        case TKPrivateEventDoublePhoto:
//            return @"TKPrivateEventDoublePhoto";
        case TKPrivateEventLiveDetect:
            return @"TKPrivateEventLiveDetect";
        case TKPrivateEventVideoWitness:
            return @"TKPrivateEventVideoWitness";
        case TKPrivateEventOneWayVideo:
            return @"TKPrivateEventOneWayVideo";
        default:
            return @"";
    }
}

// 枚举 TKPrivateSubEvent 转字符串
NSString *TKPrivateSubEventToString(TKPrivateSubEvent event) {
    switch (event) {
        case TKPrivateSubEventStart:
            return @"TKPrivateSubEventStart";
        case TKPrivateSubEventIDCardPrepareVideo:
            return @"TKPrivateSubEventIDCardPrepareVideo";
        case TKPrivateSubEventIDCardSelectOtherType:
            return @"TKPrivateSubEventIDCardSelectOtherType";
        case TKPrivateSubEventIDCardGetImageDone:
            return @"TKPrivateSubEventIDCardGetImageDone";
        case TKPrivateSubEventIDCardPreview:
            return @"TKPrivateSubEventIDCardPreview";
        case TKPrivateSubEventIDCardRetry:
            return @"TKPrivateSubEventIDCardRetry";
        case TKPrivateSubEventIDCardUpload:
            return @"TKPrivateSubEventIDCardUpload";
        case TKPrivateSubEventBandCardPrepareVideo:
            return @"TKPrivateSubEventBandCardPrepareVideo";
        case TKPrivateSubEventBandCardSelectOtherType:
            return @"TKPrivateSubEventBandCardSelectOtherType";
        case TKPrivateSubEventBandCardGetImageDone:
            return @"TKPrivateSubEventBandCardGetImageDone";
        case TKPrivateSubEventBandCardPreview:
            return @"TKPrivateSubEventBandCardPreview";
        case TKPrivateSubEventBandCardRetry:
            return @"TKPrivateSubEventBandCardRetry";
        case TKPrivateSubEventBandCardSubmit:
            return @"TKPrivateSubEventBandCardSubmit";
//        case TKPrivateSubEventCertificatePrepareVideo:
//            return @"TKPrivateSubEventCertificatePrepareVideo";
//        case TKPrivateSubEventCertificateSelectOtherType:
//            return @"TKPrivateSubEventCertificateSelectOtherType";
//        case TKPrivateSubEventCertificateGetImageDone:
//            return @"TKPrivateSubEventCertificateGetImageDone";
//        case TKPrivateSubEventDoublePhotoPrepareVideo:
//            return @"TKPrivateSubEventDoublePhotoPrepareVideo";
//        case TKPrivateSubEventDoublePhotoGetImageDone:
//            return @"TKPrivateSubEventDoublePhotoGetImageDone";
        case TKPrivateSubEventLiveDetectRequestRoom:
            return @"TKPrivateSubEventLiveDetectRequestRoom";
        case TKPrivateSubEventLiveDetectPrepareVideo:
            return @"TKPrivateSubEventLiveDetectPrepareVideo";
        case TKPrivateSubEventLiveDetectAlignment:
            return @"TKPrivateSubEventLiveDetectAlignment";
        case TKPrivateSubEventLiveDetectAction:
            return @"TKPrivateSubEventLiveDetectAction";
        case TKPrivateSubEventLiveDetectFaceDetect:
            return @"TKPrivateSubEventLiveDetectFaceDetect";
        case TKPrivateSubEventVideoWitnessWithoutQueue:
            return @"TKPrivateSubEventVideoWitnessWithoutQueue";
        case TKPrivateSubEventVideoWitnessEnqueue:
            return @"TKPrivateSubEventVideoWitnessEnqueue";
        case TKPrivateSubEventVideoWitnessDequeue:
            return @"TKPrivateSubEventVideoWitnessDequeue";
        case TKPrivateSubEventVideoWitnessPrepareVideo:
            return @"TKPrivateSubEventVideoWitnessPrepareVideo";
        case TKPrivateSubEventVideoWitnessSendMessage:
            return @"TKPrivateSubEventVideoWitnessSendMessage";
        case TKPrivateSubEventVideoWitnessReceiveMessage:
            return @"TKPrivateSubEventVideoWitnessReceiveMessage";
        case TKPrivateSubEventVideoWitnessSendCMD:
            return @"TKPrivateSubEventVideoWitnessSendCMD";
        case TKPrivateSubEventVideoWitnessReceiveCMD:
            return @"TKPrivateSubEventVideoWitnessReceiveCMD";
        case TKPrivateSubEventOneWayVideoRequestRoom:
            return @"TKPrivateSubEventOneWayVideoRequestRoom";
        case TKPrivateSubEventOneWayVideoPrepareVideo:
            return @"TKPrivateSubEventOneWayVideoPrepareVideo";
        case TKPrivateSubEventOneWayVideoRecord:
            return @"TKPrivateSubEventOneWayVideoRecord";
        case TKPrivateSubEventOneWayVideoSwitchCamera:
            return @"TKPrivateSubEventOneWayVideoSwitchCamera";
        case TKPrivateSubEventOneWayVideoReadText:
            return @"TKPrivateSubEventOneWayVideoReadText";
        case TKPrivateSubEventOneWayVideoLocalPlaySound:
            return @"TKPrivateSubEventOneWayVideoLocalPlaySound";
        case TKPrivateSubEventOneWayVideoTTS:
            return @"TKPrivateSubEventOneWayVideoTTS";
        case TKPrivateSubEventOneWayVideoASR:
            return @"TKPrivateSubEventOneWayVideoASR";
        case TKPrivateSubEventOneWayVideoFaceDetect:
            return @"TKPrivateSubEventOneWayVideoFaceDetect";
        case TKPrivateSubEventOneWayVideoFaceCompare:
            return @"TKPrivateSubEventOneWayVideoFaceCompare";
        case TKPrivateSubEventOneWayVideoPreview:
            return @"TKPrivateSubEventOneWayVideoPreview";
        case TKPrivateSubEventOneWayVideoRerecord:
            return @"TKPrivateSubEventOneWayVideoRerecord";
        case TKPrivateSubEventOneWayVideoSubmit:
            return @"TKPrivateSubEventOneWayVideoSubmit";
        default:
            return @"";
    }
}


NSString *TKPrivateEventProgressToString(TKPrivateEventProgress progress) {
    switch (progress) {
        case TKPrivateEventProgressStart:
            return @"TKPrivateEventProgressStart";
        case TKPrivateEventProgressRunning:
            return @"TKPrivateEventProgressRunning";
        case TKPrivateEventProgressEnd:
            return @"TKPrivateEventProgressEnd";
        default:
            return @"";
    }
}

NSString *TKPrivateEventResultToString(TKPrivateEventResult result) {
    switch (result) {
        case TKPrivateEventResultSuccess:
            return @"TKPrivateEventResultSuccess";
        case TKPrivateEventResultFail:
            return @"TKPrivateEventResultFail";
        case TKPrivateEventResultCancel:
            return @"TKPrivateEventResultCancel";
        case TKPrivateEventResultError:
            return @"TKPrivateEventResultError";
        default:
            return @"";
    }
}

NSString *TKPrivateCardSurfaceToString(TKPrivateCardSurface surface) {
    switch (surface) {
        case TKPrivateCardSurfaceFront:
            return @"TKPrivateCardSurfaceFront";
        case TKPrivateCardSurfaceBack:
            return @"TKPrivateCardSurfaceBack";
        default:
            return @"";
    }
}


NSString * TKPrivateCardRecognizeTypeToString(TKPrivateCardRecognizeType type) {
    switch (type) {
        case TKPrivateCardRecognizeTypeTakePhoto:
            return @"TKPrivateCardRecognizeTypeTakePhoto";
        case TKPrivateCardRecognizeTypeAlbum:
            return @"TKPrivateCardRecognizeTypeAlbum";
        case TKPrivateCardRecognizeTypeOCRScan:
            return @"TKPrivateCardRecognizeTypeOCRScan";
        case TKPrivateCardRecognizeTypeNFCScan:
            return @"TKPrivateCardRecognizeTypeNFCScan";
        default:
            return @"";
    }
}


NSString * TKPrivateVideoOrientationToString(TKPrivateVideoOrientation type) {
    switch (type) {
        case TKPrivateVideoOrientationPortrait:
            return @"TKPrivateVideoOrientationPortrait";
        case TKPrivateVideoOrientationLandscape:
            return @"TKPrivateVideoOrientationLandscape";
        default:
            return @"";
    }
}

NSString * TKPrivateOneWayVideoTypeToString(TKPrivateOneWayVideoType type) {
    switch (type) {
        case TKPrivateOneWayVideoTypeNormal:
            return @"TKPrivateOneWayVideoTypeNormal";
        case TKPrivateOneWayVideoTypeLocalSmart:
            return @"TKPrivateOneWayVideoTypeLocalSmart";
        case TKPrivateOneWayVideoTypeTChatSmart:
            return @"TKPrivateOneWayVideoTypeTChatSmart";
        case TKPrivateOneWayVideoTypeTChatDigitalMan:
            return @"TKPrivateOneWayVideoTypeTChatDigitalMan";
        default:
            return @"";
    }
}

NSString *TKPrivatePrepareVideoProgressToString(TKPrivatePrepareVideoProgress value) {
    switch (value) {
        case TKPrivatePrepareVideoProgressConnectServer:
            return @"TKPrivatePrepareVideoProgressConnectServer";
        case TKPrivatePrepareVideoProgressLogin:
            return @"TKPrivatePrepareVideoProgressLogin";
        case TKPrivatePrepareVideoProgressEnterRoom:
            return @"TKPrivatePrepareVideoProgressEnterRoom";
        case TKPrivatePrepareVideoProgressSelectDevice:
            return @"TKPrivatePrepareVideoProgressSelectDevice";
        case TKPrivatePrepareVideoProgressVideoReady:
            return @"TKPrivatePrepareVideoProgressVideoReady";
        case TKPrivatePrepareVideoProgressAudioReady:
            return @"TKPrivatePrepareVideoProgressAudioReady";
        case TKPrivatePrepareVideoProgressReady:
            return @"TKPrivatePrepareVideoProgressReady";
        case TKPrivatePrepareVideoProgressRequestToten:
            return @"TKPrivatePrepareVideoProgressRequestToten";
        case TKPrivatePrepareVideoProgressPull:
            return @"TKPrivatePrepareVideoProgressPull";
        case TKPrivatePrepareVideoProgressPush:
            return @"TKPrivatePrepareVideoProgressPush";
        default:
            return @"";
    }
}

#pragma mark - Setter && Getter
- (TKOpenAccountService *)openAccountService {
    if (_openAccountService == nil) {
        _openAccountService = [TKOpenAccountService new];
    }
    return _openAccountService;
}

@end
