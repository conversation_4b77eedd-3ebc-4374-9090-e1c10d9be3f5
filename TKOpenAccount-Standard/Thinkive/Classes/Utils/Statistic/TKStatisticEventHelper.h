//
//  TKStatisticEventHelper.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/2/20.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKStatisticEventHelperPrivate.h"

typedef enum : int {
    TKStatisticEventUnknown = 0,        // 未知事件
    
    TK_IDCARD_FRONT_TAKE_START,         // 身份证正面拍照开始
    TK_IDCARD_FRONT_TAKE_CANCEL,        // 身份证正面拍照取消
    TK_IDCARD_FRONT_TAKE_PHOTO,         // 身份证正面拍照完成
    TK_IDCARD_FRONT_TAKE_GO_SCAN,       // 身份证正面进入扫描
    TK_IDCARD_FRONT_TAKE_ALBUM,         // 身份证正面相册选择
    TK_IDCARD_FRONT_TAKE_PREVIEW_START, // 身份证正面拍照预览开始
    TK_IDCARD_FRONT_TAKE_RELOAD,        // 身份证正面拍照重新拍摄
    TK_IDCARD_FRONT_TAKE_COMMIT,        // 身份证正面拍照提交
    TK_IDCARD_FRONT_TAKE_RESULT,        // 身份证正面拍照结果
    
    TK_IDCARD_BACK_TAKE_START,          // 身份证反面拍照开始
    TK_IDCARD_BACK_TAKE_CANCEL,         // 身份证反面拍照取消
    TK_IDCARD_BACK_TAKE_PHOTO_START,    // 身份证反面拍照完成
    TK_IDCARD_BACK_TAKE_GO_SCAN,        // 身份证反面进入扫描
    TK_IDCARD_BACK_TAKE_ALBUM,          // 身份证反面相册选择
    TK_IDCARD_BABK_TAKE_PREVIEW_START,  // 身份证反面拍照预览开始
    TK_IDCARD_BACK_TAKE_RELOAD,         // 身份证反面拍照重新拍摄
    TK_IDCARD_BACK_TAKE_COMMIT,         // 身份证反面拍照提交
    TK_IDCARD_BACK_TAKE_RESULT,         // 身份证反面拍照结果
    
    TK_IDCARD_FRONT_ALBUM_START,        // 身份证正面相册开始
    TK_IDCARD_FRONT_ALBUM_CANCEL,       // 身份证正面相册取消
    TK_IDCARD_FRONT_ALBUM_PREVIEW_START,// 身份证正面相册预览开始
    TK_IDCARD_FRONT_ALBUM_RELOAD,       // 身份证正面相册重新选择
    TK_IDCARD_FRONT_ALBUM_COMMIT,       // 身份证正面相册提交
    TK_IDCARD_FRONT_ALBUM_RESULT,       // 身份证正面相册结果
    
    TK_IDCARD_BACK_ALBUM_START,         // 身份证反面相册开始
    TK_IDCARD_BACK_ALBUM_CANCEL,        // 身份证反面相册取消
    TK_IDCARD_BACK_ALBUM_PREVIEW_START, // 身份证反面相册预览开始
    TK_IDCARD_BACK_ALBUM_RELOAD,        // 身份证反面相册重新选择
    TK_IDCARD_BACK_ALBUM_COMMIT,        // 身份证反面相册提交
    TK_IDCARD_BACK_ALBUM_RESULT,        // 身份证反面相册结果
    
    TK_IDCARD_FRONT_SCAN_START,            // 身份证_正面_扫描_开始
    TK_IDCARD_FRONT_SCAN_CANCEL,           // 身份证_正面_扫描_返回
    TK_IDCARD_FRONT_SCAN_GO_TAKE,          // 身份证_正面_扫描_切拍照
    TK_IDCARD_FRONT_SCAN_ALBUM,            // 身份证_正面_扫描_相册
    TK_IDCARD_FRONT_SCAN_PREVIEW_START,    // 身份证_正面_扫描_预览
    TK_IDCARD_FRONT_SCAN_RELOAD,           // 身份证_正面_扫描_重拍
    TK_IDCARD_FRONT_SCAN_COMMIT,           // 身份证_正面_扫描_提交
    TK_IDCARD_FRONT_SCAN_RESULT,           // 身份证_正面_扫描_结果
    
    TK_IDCARD_BACK_SCAN_START,             // 身份证_反面_扫描_开始
    TK_IDCARD_BACK_SCAN_CANCEL,            // 身份证_反面_扫描_返回
    TK_IDCARD_BACK_SCAN_GO_TAKE,           // 身份证_反面_扫描_切拍照
    TK_IDCARD_BACK_SCAN_ALBUM,             // 身份证_反面_扫描_相册
    TK_IDCARD_BACK_SCAN_PREVIEW_START,     // 身份证_反面_扫描_预览
    TK_IDCARD_BACK_SCAN_RELOAD,            // 身份证_反面_扫描_重拍
    TK_IDCARD_BACK_SCAN_COMMIT,            // 身份证_反面_扫描_提交
    TK_IDCARD_BACK_SCAN_RESULT,            // 身份证_反面_扫描_结果
    
    // 银行卡
    TK_BANKCARD_SCAN_START,                // 银行卡_扫描_开始
    TK_BANKCARD_SCAN_CANCEL,               // 银行卡_扫描_返回
    TK_BANKCARD_SCAN_GO_TAKE,              // 银行卡_扫描_切拍照
    TK_BANKCARD_SCAN_ALBUM,                // 银行卡_扫描_切相册
    TK_BANKCARD_SCAN_RESULT,               // 银行卡_扫描_结果
    
    TK_BANKCARD_ALBUM_START,               // 银行卡_相册_开始
    TK_BANKCARD_ALBUM_CANCEL,              // 银行卡_相册_返回
    TK_BANKCARD_ALBUM_PREVIEW_START,       // 银行卡_相册_预览
    TK_BANKCARD_ALBUM_RELOAD,              // 银行卡_相册_重拍
    TK_BANKCARD_ALBUM_COMMIT,              // 银行卡_相册_提交
    TK_BANKCARD_ALBUM_RESULT,              // 银行卡_相册_结果
    
    TK_BANKCARD_TAKE_START,                // 银行卡_拍照_开始
    TK_BANKCARD_TAKE_CANCEL,               // 银行卡_拍照_返回
    TK_BANKCARD_TAKE_TAKE_PHOTO,           // 银行卡_拍照_确认
    TK_BANKCARD_TAKE_GO_SCAN,              // 银行卡_拍照_切扫描
    TK_BANKCARD_TAKE_ALBUM,                // 银行卡_拍照_切相册
    TK_BANKCARD_TAKE_PREVIEW_START,        // 银行卡_拍照_预览
    TK_BANKCARD_TAKE_RELOAD,               // 银行卡_拍照_重拍
    TK_BANKCARD_TAKE_COMMIT,               // 银行卡_拍照_提交
    TK_BANKCARD_TAKE_RESULT,               // 银行卡_拍照_结果
    
    // 双向
    TK_VIDEO_WITNESS_QUEUE_START,         // 视频见证_排队_开始
    TK_VIDEO_WITNESS_QUEUE_HOLD_DOWN,     // 视频见证_排队_用户挂断
    TK_VIDEO_WITNESS_QUEUE_ERROR,         // 视频见证_排队_异常
    TK_VIDEO_WITNESS_QUEUE_SUCCESS,       // 视频见证_排队_成功
    TK_VIDEO_WITNESS_CONNECT_HOLD_DOWN,      // 视频见证_连接_取消
    TK_VIDEO_WITNESS_CONNECT_ERROR,       // 视频见证_连接_异常
    TK_VIDEO_WITNESS_CONNECT_SUCCESS,     // 视频见证_连接_成功
    TK_VIDEO_WITNESS_ROOM_START,          // 视频见证_见证_开始
    TK_VIDEO_WITNESS_ROOM_HOLD_DOWN,      // 视频见证_见证_用户挂断
    TK_VIDEO_WITNESS_ROOM_ERROR,          // 视频见证_见证_异常断开
    TK_VIDEO_WITNESS_ROOM_RESULT,         // 视频见证_见证_结果
    TK_VIDEO_WITNESS_ROOM_SUCCESS,        // 视频见证_见证_成功
    TK_VIDEO_WITNESS_ROOM_REJECT,         // 视频见证_见证_失败
    
    // 活体
    TK_LIVE_DETECT_START,                 // 活体识别_开始
    TK_LIVE_DETECT_CANCEL,                // 活体识别_返回
    TK_LIVE_DETECT_RESULT,                // 活体识别_结果
    
    TK_SERVER_LIVE_DETECT_START,            // 服务器活体识别_开始
    TK_SERVER_LIVE_DETECT_APPLY_SUCCESS,    // 服务器活体识别_申请成功
    TK_SERVER_LIVE_DETECT_CONNECT_SUCCESS,  // 服务器活体识别_连接成功
    TK_SERVER_LIVE_DETECT_CANCEL,           // 服务器活体识别_返回
    TK_SERVER_LIVE_DETECT_RESULT,           // 服务器活体识别_结果
    
    // 单向
    TK_NORMAL_ONE_WAY_VIDEO_START,          // 朗读单向视频_开始
    TK_NORMAL_ONE_WAY_VIDEO_CANCEL,         // 朗读单向视频_返回
    TK_NORMAL_ONE_WAY_VIDEO_RECORD_START,   // 朗读单向视频_开始录制
    TK_NORMAL_ONE_WAY_VIDEO_RECORD_END,     // 朗读单向视频_结束录制
    TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_START,  // 朗读单向视频_预览_开始
    TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_CANCEL,  // 朗读单向视频_预览_取消
    TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_RERECORD, // 朗读单向视频_预览_重录
    TK_NORMAL_ONE_WAY_VIDEO_FAIL_START,      // 朗读单向视频_错误_开始
    TK_NORMAL_ONE_WAY_VIDEO_FAIL_CANCEL,      // 朗读单向视频_错误_取消
    TK_NORMAL_ONE_WAY_VIDEO_FAIL_RERECORD,   // 朗读单向视频_错误_重录
    TK_NORMAL_ONE_WAY_VIDEO_PREVIEW_COMMIT, // 朗读单向视频_预览_提交
    TK_NORMAL_ONE_WAY_VIDEO_RESULT,         // 朗读单向视频_结果
    
    TK_SMART_ONE_WAY_VIDEO_START,           // 智能单向视频_开始
    TK_SMART_ONE_WAY_VIDEO_CANCEL,          // 智能单向视频_返回
    TK_SMART_ONE_WAY_VIDEO_RECORD_START,    // 智能单向视频_开始录制
    TK_SMART_ONE_WAY_VIDEO_RECORD_END,      // 智能单向视频_结束录制
    TK_SMART_ONE_WAY_VIDEO_PREVIEW_START,   // 智能单向视频_预览_开始
    TK_SMART_ONE_WAY_VIDEO_PREVIEW_CANCEL,   // 智能单向视频_预览_取消
    TK_SMART_ONE_WAY_VIDEO_PREVIEW_RERECORD, // 智能单向视频_预览_重录
    TK_SMART_ONE_WAY_VIDEO_PREVIEW_COMMIT,  // 智能单向视频_预览_提交
    TK_SMART_ONE_WAY_VIDEO_FAIL_START,      // 智能单向视频_错误_开始
    TK_SMART_ONE_WAY_VIDEO_FAIL_CANCEL,      // 智能单向视频_错误_取消
    TK_SMART_ONE_WAY_VIDEO_FAIL_RERECORD,   // 智能单向视频_错误_重录
    TK_SMART_ONE_WAY_VIDEO_RESULT,          // 智能单向视频_结果
    
    TK_TCHAT_ONE_WAY_VIDEO_START,           // Tchat智能单向视频_开始
    TK_TCHAT_ONE_WAY_VIDEO_CANCEL,          // Tchat智能单向视频_返回
    TK_TCHAT_ONE_WAY_VIDEO_APPLY_SUCCESS,   // Tchat智能单向视频_申请成功
    TK_TCHAT_ONE_WAY_VIDEO_CONNECT_SUCCESS, // Tchat智能单向视频_连接成功
    TK_TCHAT_ONE_WAY_VIDEO_RECORD_START,    // Tchat智能单向视频_开始录制
    TK_TCHAT_ONE_WAY_VIDEO_RECORD_END,      // Tchat智能单向视频_结束录制
    TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_START,   // Tchat智能单向视频_预览_开始
    TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_CANCEL,   // Tchat智能单向视频_预览_取消
    TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_RERECORD, // Tchat智能单向视频_预览_重录
    TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT,  // Tchat智能单向视频_预览_提交
    TK_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT,  // Tchat智能单向视频_预览_提交结果
    TK_TCHAT_ONE_WAY_VIDEO_FAIL_START,      // Tchat智能单向视频_错误_开始
    TK_TCHAT_ONE_WAY_VIDEO_FAIL_CANCEL,      // Tchat智能单向视频_错误_取消
    TK_TCHAT_ONE_WAY_VIDEO_FAIL_RERECORD,   // Tchat智能单向视频_错误_重录
    TK_TCHAT_ONE_WAY_VIDEO_RESULT,          // Tchat智能单向视频_结果
    
    TK_VH_ONE_WAY_VIDEO_START,               // 虚拟人智能单向视频_开始
    TK_VH_ONE_WAY_VIDEO_CANCEL,              // 虚拟人智能单向视频_返回
    TK_VH_ONE_WAY_VIDEO_APPLY_SUCCESS,       // 虚拟人智能单向视频_申请成功
    TK_VH_ONE_WAY_VIDEO_CONNECT_SUCCESS,     // 虚拟人智能单向视频_连接成功
    TK_VH_ONE_WAY_VIDEO_RECORD_START,        // 虚拟人智能单向视频_开始录制
    TK_VH_ONE_WAY_VIDEO_RECORD_END,          // 虚拟人智能单向视频_结束录制
    TK_VH_ONE_WAY_VIDEO_PREVIEW_START,       // 虚拟人智能单向视频_预览_开始
    TK_VH_ONE_WAY_VIDEO_PREVIEW_CANCEL,       // 虚拟人智能单向视频_预览_取消
    TK_VH_ONE_WAY_VIDEO_PREVIEW_RERECORD,    // 虚拟人智能单向视频_预览_重录
    TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT,      // 虚拟人智能单向视频_预览_提交
    TK_VH_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT,  // 虚拟人智能单向视频_预览_提交结果
    TK_VH_ONE_WAY_VIDEO_FAIL_START,          // 虚拟人智能单向视频_错误_开始
    TK_VH_ONE_WAY_VIDEO_FAIL_CANCEL,         // 虚拟人智能单向视频_错误_取消
    TK_VH_ONE_WAY_VIDEO_FAIL_RERECORD,       // 虚拟人智能单向视频_错误_重录
    TK_VH_ONE_WAY_VIDEO_RESULT,              // 虚拟人智能单向视频_结果
    
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_START,            // 横屏朗读单向视频_开始
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_CANCEL,           // 横屏朗读单向视频_返回
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_RECORD_START,     // 横屏朗读单向视频_开始录制
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_RECORD_END,       // 横屏朗读单向视频_结束录制
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_START,    // 横屏朗读单向视频_预览_开始
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_CANCEL,    // 横屏朗读单向视频_预览_取消
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_RERECORD, // 横屏朗读单向视频_预览_重录
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_START,       // 横屏朗读单向视频_错误_开始
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_RERECORD,    // 横屏朗读单向视频_错误_重录
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_FAIL_CANCEL,      // 横屏朗读单向视频_错误_取消
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_PREVIEW_COMMIT,   // 横屏朗读单向视频_预览_提交
    TK_LANDS_NORMAL_ONE_WAY_VIDEO_RESULT,           // 横屏朗读单向视频_结果
    
    TK_LANDS_SMART_ONE_WAY_VIDEO_START,            // 横屏智能单向视频_开始
    TK_LANDS_SMART_ONE_WAY_VIDEO_CANCEL,           // 横屏智能单向视频_返回
    TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_START,     // 横屏智能单向视频_开始录制
    TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_END,       // 横屏智能单向视频_结束录制
    TK_LANDS_SMART_ONE_WAY_VIDEO_RECORD_SWAP,      // 横屏智能单向视频_切换摄像头
    TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_START,    // 横屏智能单向视频_预览_开始
    TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_CANCEL,    // 横屏智能单向视频_预览_取消
    TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_RERECORD, // 横屏智能单向视频_预览_重录
    TK_LANDS_SMART_ONE_WAY_VIDEO_PREVIEW_COMMIT,   // 横屏智能单向视频_预览_提交
    TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_START,       // 横屏智能单向视频_错误_开始
    TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_CANCEL,      // 横屏智能单向视频_错误_取消
    TK_LANDS_SMART_ONE_WAY_VIDEO_FAIL_RERECORD,    // 横屏智能单向视频_错误_重录
    TK_LANDS_SMART_ONE_WAY_VIDEO_RESULT,           // 横屏智能单向视频_结果
    
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_START,            // 横屏Tchat智能单向视频_开始
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_CANCEL,           // 横屏Tchat智能单向视频_返回
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_APPLY_SUCCESS,    // 横屏Tchat智能单向视频_申请成功
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_CONNECT_SUCCESS,  // 横屏Tchat智能单向视频_连接成功
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_START,     // 横屏Tchat智能单向视频_开始录制
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_END,       // 横屏Tchat智能单向视频_结束录制
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_RECORD_SWAP,      // 横屏Tchat智能单向视频_切换摄像头
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_START,    // 横屏Tchat智能单向视频_预览_开始
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_CANCEL,    // 横屏Tchat智能单向视频_预览_取消
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_RERECORD, // 横屏Tchat智能单向视频_预览_重录
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT,   // 横屏Tchat智能单向视频_预览_提交
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_PREVIEW_COMMIT_RESULT,   // 横屏Tchat智能单向视频_预览_提交结果
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_START,       // 横屏Tchat智能单向视频_错误_开始
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_CANCEL,      // 横屏Tchat智能单向视频_错误_取消
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_FAIL_RERECORD,    // 横屏Tchat智能单向视频_错误_重录
    TK_LANDS_TCHAT_ONE_WAY_VIDEO_RESULT,           // 横屏Tchat智能单向视频_结果

} TKStatisticEvent;



NS_ASSUME_NONNULL_BEGIN

@protocol TKStatisticEventHelperDelegate <NSObject>

/// 原生回调代理事件
/// - Parameters:
///   - event: 事件类型。枚举
///   - params: 事件参数
- (void)statisticEventHelperDidCallBack:(TKStatisticEvent)event params:(NSDictionary *_Nullable)params;

/// H5回调代理事件
/// - Parameters:
///   - event: 事件类型。字符串
///   - params: 事件参数
- (void)h5StatisticEventHelperDidCallBack:(NSString *)event params:(NSDictionary *_Nullable)params;

@end

/// 统计工具类
@interface TKStatisticEventHelper : NSObject

// 单例
+ (TKStatisticEventHelper *)shareInstance;

/// 代理
@property (nonatomic, readwrite, weak) id<TKStatisticEventHelperDelegate> delegate;


/// 发送H5埋点事件
/// - Parameters:
///   - eventName: 事件类型。字符串
///   - eventDic: 事件参数
+ (void)sendH5Event:(NSString *)eventName
           eventDic:(NSDictionary *)eventDic;


/// 发送通用事件(SDK内部使用)
/// - Parameters:
///   - eventName: 事件名
///   - subEventName: 子事件名。默认TKPrivateSubEventNone。为TKPrivateSubEventNone时，不需要关注该参数
///   - progress: 事件进度。默认TKPrivateEventProgressNone。为TKPrivateEventProgressNone时，不需要关注该参数
///   - result: 事件结果。默认TKPrivateEventResultNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - eventDic: 事件参数
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
         eventDic:(NSDictionary *)eventDic;
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
           result:(TKPrivateEventResult)result
         eventDic:(NSDictionary *)eventDic;
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
         eventDic:(NSDictionary *)eventDic;
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         eventDic:(NSDictionary *)eventDic;
+ (void)sendEvent:(TKPrivateEvent)eventName
         eventDic:(NSDictionary *)eventDic;


/// 发送证件识别事件(SDK内部使用，默认横屏)
/// - Parameters:
///   - eventName: 事件名
///   - subEventName: 子事件名。默认TKPrivateSubEventNone。为TKPrivateSubEventNone时，不需要关注该参数
///   - progress: 事件进度。默认TKPrivateEventProgressNone。为TKPrivateEventProgressNone时，不需要关注该参数
///   - result: 事件结果。默认TKPrivateEventResultNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - cardSurface: 卡片、证件正反面。默认TKPrivateCardSurfaceNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - cardRecognizeType: 识别方式。默认TKPrivateCardRecognizeTypeNone。为TKPrivateCardRecognizeTypeNone时，不需要关注该参数
///   - eventDic: 事件参数
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
      cardSurface:(TKPrivateCardSurface)cardSurface
cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
         eventDic:(NSDictionary *)eventDic;

/// 发送证件识别事件(SDK内部使用)
/// - Parameters:
///   - eventName: 事件名
///   - subEventName: 子事件名。默认TKPrivateSubEventNone。为TKPrivateSubEventNone时，不需要关注该参数
///   - progress: 事件进度。默认TKPrivateEventProgressNone。为TKPrivateEventProgressNone时，不需要关注该参数
///   - result: 事件结果。默认TKPrivateEventResultNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - cardSurface: 卡片、证件正反面。默认TKPrivateCardSurfaceNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - cardRecognizeType: 识别方式。默认TKPrivateCardRecognizeTypeNone。为TKPrivateCardRecognizeTypeNone时，不需要关注该参数
///   - eventDic: 事件参数
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
      cardSurface:(TKPrivateCardSurface)cardSurface
cardRecognizeType:(TKPrivateCardRecognizeType)cardRecognizeType
      orientation:(TKPrivateVideoOrientation)orientation
         eventDic:(NSDictionary *)eventDic;


/// 发送视频见证事件（双向+单向）(SDK内部使用)
/// - Parameters:
///   - eventName: 事件名
///   - subEventName: 子事件名。默认TKPrivateSubEventNone。为TKPrivateSubEventNone时，不需要关注该参数
///   - progress: 事件进度。默认TKPrivateEventProgressNone。为TKPrivateEventProgressNone时，不需要关注该参数
///   - result: 事件结果。默认TKPrivateEventResultNone。为TKPrivateEventResultNone时，不需要关注该参数
///   - orientation: 视频方向。默认TKPrivateVideoOrientationNone。为TKPrivateVideoOrientationNone时，不需要关注该参数
///   - oneWayVideoType: 单向视频类型。默认TKPrivateOneWayVideoTypeNone。为TKPrivateOneWayVideoTypeNone时，不需要关注该参数
///   - prepareVideoProgress: 视频准备流程。默认TKPrivatePrepareVideoProgressNone。为TKPrivatePrepareVideoProgressNone时，不需要关注该参数
///   - eventDic: 事件参数
+ (void)sendEvent:(TKPrivateEvent)eventName
     subEventName:(TKPrivateSubEvent)subEventName
         progress:(TKPrivateEventProgress)progress
           result:(TKPrivateEventResult)result
      orientation:(TKPrivateVideoOrientation)orientation
  oneWayVideoType:(TKPrivateOneWayVideoType)oneWayVideoType
prepareVideoProgress:(TKPrivatePrepareVideoProgress)prepareVideoProgress
         eventDic:(NSDictionary *)eventDic;


@end

NS_ASSUME_NONNULL_END
