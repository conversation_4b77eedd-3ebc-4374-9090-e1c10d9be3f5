//
//  TKStatisticEventHelperPrivate.h
//  TKOpenAccount-Standard
//
//  Created by Felix on 2023/2/21.
//  Copyright © 2023 thinkive. All rights reserved.
//

#ifndef TKStatisticEventHelperPrivate_h
#define TKStatisticEventHelperPrivate_h


// 思迪内部事件
typedef enum : int {
    TKPrivateEventUnkown = 0,
    TKPrivateEventIDCard,                           // 身份证
    TKPrivateEventBandCard,                         // 银行卡
//    TKPrivateEventCertificate,                      // 证件照，如港澳台通行证、居住证等
//    TKPrivateEventDoublePhoto,                      // 双人照
    
    TKPrivateEventLiveDetect,                       // 本地活体检测
    
    TKPrivateEventVideoWitness,                     // 视频⻅证（双向）
    
    TKPrivateEventOneWayVideo,                      // 单向视频
} TKPrivateEvent;


// 思迪内部子事件
typedef enum : int {
    
    // 通用
    TKPrivateSubEventNone = 0,                      // 无子事件或不重要。此时关注TKPrivateEvent本身
    TKPrivateSubEventStart,                         // 流程开始
    
    // 身份证
    TKPrivateSubEventIDCardPrepareVideo,            // 准备视频
    TKPrivateSubEventIDCardSelectOtherType,         // 拍照、相册、扫描间切换
    TKPrivateSubEventIDCardGetImageDone,            // 获取图片完成
    TKPrivateSubEventIDCardPreview,                 // 视频预览
    TKPrivateSubEventIDCardRetry,                   // 重拍
    TKPrivateSubEventIDCardUpload,                  // 身份证上传
    
    // 银行卡
    TKPrivateSubEventBandCardPrepareVideo,          // 准备视频
    TKPrivateSubEventBandCardSelectOtherType,       // 拍照、相册、扫描间切换
    TKPrivateSubEventBandCardGetImageDone,          // 获取银行卡完成
    TKPrivateSubEventBandCardPreview,               // 视频预览
    TKPrivateSubEventBandCardRetry,                 // 重拍
    TKPrivateSubEventBandCardSubmit,                // 提交
    
    // 证件照
//    TKPrivateSubEventCertificatePrepareVideo,       // 准备视频
//    TKPrivateSubEventCertificateSelectOtherType,    // 拍照、相册、扫描间切换
//    TKPrivateSubEventCertificateGetImageDone,       // 获取照片完成
//
//    // 双人照
//    TKPrivateSubEventDoublePhotoPrepareVideo,       // 准备视频
//    TKPrivateSubEventDoublePhotoGetImageDone,       // 获取照片完成
    
    // 活体检测
    TKPrivateSubEventLiveDetectRequestRoom,         // 请求房间（服务端活体流程）
    TKPrivateSubEventLiveDetectPrepareVideo,        // 准备视频连接
    TKPrivateSubEventLiveDetectAlignment,           // 对准
    TKPrivateSubEventLiveDetectAction,              // 动作
    TKPrivateSubEventLiveDetectFaceDetect,          // 持续在框检测
    
    
    // 视频⻅证（双向）
    TKPrivateSubEventVideoWitnessWithoutQueue,      // 无排队直连视频
    TKPrivateSubEventVideoWitnessEnqueue,           // 排队
    TKPrivateSubEventVideoWitnessDequeue,           // 退出排队
    TKPrivateSubEventVideoWitnessPrepareVideo,      // 准备视频连接
    TKPrivateSubEventVideoWitnessSendMessage,       // 发送文本消息
    TKPrivateSubEventVideoWitnessReceiveMessage,    // 接收文本消息
    TKPrivateSubEventVideoWitnessSendCMD,           // 发送消息指令（目前仅退出双向的指令）
    TKPrivateSubEventVideoWitnessReceiveCMD,        // 获取消息指令（目前仅退出双向的指令）
    
    
    // 单向视频
    TKPrivateSubEventOneWayVideoRequestRoom,        // 请求房间（服务端录制流程）
    TKPrivateSubEventOneWayVideoPrepareVideo,       // 准备视频连接
    TKPrivateSubEventOneWayVideoRecord,             // 语音识别（本地智能语音）
    TKPrivateSubEventOneWayVideoSwitchCamera,       // 切换摄像头
    TKPrivateSubEventOneWayVideoReadText,           // 朗读文本（普通（朗读）单向场景）
    TKPrivateSubEventOneWayVideoLocalPlaySound,     // 播放本地语音（本地智能语音，已废弃）
    TKPrivateSubEventOneWayVideoTTS,                // 语音播报（本地智能语音）
    TKPrivateSubEventOneWayVideoASR,                // 语音识别（本地智能语音）
    // 单向视频-质检
    TKPrivateSubEventOneWayVideoFaceDetect,         // 在框检测
    TKPrivateSubEventOneWayVideoFaceCompare,        // 人脸比对
    
    TKPrivateSubEventOneWayVideoPreview,            // 视频预览
    TKPrivateSubEventOneWayVideoRerecord,           // 重新录制
    TKPrivateSubEventOneWayVideoSubmit,             // 提交视频
} TKPrivateSubEvent;



// 事件进度
typedef enum : int {
    TKPrivateEventProgressNone,                     // 进度信息不重要
    TKPrivateEventProgressStart,                    // 开始
    TKPrivateEventProgressRunning,                  // 正在执行
    TKPrivateEventProgressEnd,                      // 结束
} TKPrivateEventProgress;



// 事件结果
typedef enum : int {
    TKPrivateEventResultNone = 0,                   // 无结果或不需要结果
    TKPrivateEventResultSuccess,                    // 成功
    TKPrivateEventResultFail,                       // 失败
    TKPrivateEventResultCancel,                     // 取消
    TKPrivateEventResultError,                      // 程序异常
} TKPrivateEventResult;



// 证件（卡片）正反面
typedef enum : int {
    TKPrivateCardSurfaceNone = 0,                    // 不需要或无
    TKPrivateCardSurfaceFront,                       // 正面
    TKPrivateCardSurfaceBack,                        // 反面
} TKPrivateCardSurface;



// 身份证、银行卡识别方式
typedef enum : int {
    TKPrivateCardRecognizeTypeNone = 0,              // 不需要或无
    TKPrivateCardRecognizeTypeTakePhoto,             // 拍照
    TKPrivateCardRecognizeTypeAlbum,                 // 相册
    TKPrivateCardRecognizeTypeOCRScan,               // OCR扫描
    TKPrivateCardRecognizeTypeNFCScan,               // NFC扫描。未有场景，占用字段
} TKPrivateCardRecognizeType;


// 视频方向
typedef enum : int {
    TKPrivateVideoOrientationNone = 0,              // 不需要或无
    TKPrivateVideoOrientationPortrait,              // 竖屏
    TKPrivateVideoOrientationLandscape,             // 横屏
} TKPrivateVideoOrientation;


// 活体类型
typedef enum : int {
    TKPrivateLiveDetectTypeNone = 0,               // 不需要或无
    TKPrivateLiveDetectTypeLocal,                  // 本地活体
    TKPrivateLiveDetectTypeServer,             // 服务端活体
} TKPrivateLiveDetectType;


// 单向视频类型
typedef enum : int {
    TKPrivateOneWayVideoTypeNone = 0,               // 不需要或无
    TKPrivateOneWayVideoTypeNormal,                 // 普通（朗读）单向
    TKPrivateOneWayVideoTypeLocalSmart,             // 本地智能单向
    TKPrivateOneWayVideoTypeTChatSmart,             // TChat智能单向
    TKPrivateOneWayVideoTypeTChatDigitalMan,        // TChat数字人智能单向
} TKPrivateOneWayVideoType;



typedef enum : int {
    TKPrivatePrepareVideoProgressNone = 0,           // 不需要或无
    
    // 服务端视频（服务端活体 | 服务端录制 | 双向视频）
    TKPrivatePrepareVideoProgressConnectServer,      // 连接服务器
    TKPrivatePrepareVideoProgressLogin,              // 登录
    TKPrivatePrepareVideoProgressEnterRoom,          // 进入房间
    
    // 通用
    TKPrivatePrepareVideoProgressSelectDevice,       // 开启输入输出设备（不管本地还是服务端都需要控制本地音视频设备）
    
    // 服务端视频（服务端活体 | 服务端录制 | 双向视频）
    TKPrivatePrepareVideoProgressVideoReady,         // 视频就绪渲染视频
    TKPrivatePrepareVideoProgressAudioReady,         // 视频就绪渲染视频
    
    // 通用
    TKPrivatePrepareVideoProgressReady,              // 视频就绪
    
    // 服务端视频（服务端活体 | 服务端录制 | 双向视频）
//    TKPrivatePrepareVideoProgressSendMessage,        // 发送文本消息
//    TKPrivatePrepareVideoProgressReceiveMessage,     // 接收文本消息
//    TKPrivatePrepareVideoProgressSendCMD,            // 发送消息指令（目前仅退出双向的指令）
//    TKPrivatePrepareVideoProgressReceiveCMD,         // 获取消息指令（目前仅退出双向的指令）
    // TChatRtc独有流程
    TKPrivatePrepareVideoProgressRequestToten,       // 请求token（TChatRtc服务端活体）
    TKPrivatePrepareVideoProgressPull,               // 拉流（单向TChatRtc流程）
    TKPrivatePrepareVideoProgressPush,               // 推流（单向TChatRtc流程）
    
} TKPrivatePrepareVideoProgress;

#endif /* TKStatisticEventHelperPrivate_h */
