//
//  TKCommonUtil.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2016/12/9.
//  Copyright © 2016年 thinkive. All rights reserved.
//

#import "TKCommonUtil.h"
#import <ifaddrs.h>
#import <arpa/inet.h>
#import <net/if.h>

#define IOS_CELLULAR    @"pdp_ip0"
#define IOS_WIFI        @"en0"
#define IOS_VPN         @"utun0"
#define IP_ADDR_IPv4    @"ipv4"
#define IP_ADDR_IPv6    @"ipv6"

@interface TKCommonUtil(){
    float soundDuration;
    NSTimer *playbackTimer;
    SystemSoundID    soundFileObject;
}
@property (nonatomic, copy) TKCommonUtilIsMuteBlock  isMuteBackBlock;
@end

@implementation TKCommonUtil

/**
 *<AUTHOR> 2019年11月20日10:39:32
 *@TKCommonUtil单例
 */
+(TKCommonUtil *)shareInstance{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc]init];
    });
    return instance;
}

+ (BOOL)isCurrentViewControllerVisible:(UIViewController *)viewController
{
    return (viewController.isViewLoaded && viewController.view.window);
}

//获取当前屏幕显示的viewcontroller
+ (UIViewController *)getCurrentVisibleVC
{
    UIViewController *result = nil;
    
    UIWindow * window = [[UIApplication sharedApplication] keyWindow];
    
    if (window.windowLevel != UIWindowLevelNormal)
    {
        NSArray *windows = [[UIApplication sharedApplication] windows];
        
        for(UIWindow * tmpWin in windows)
        {
            if (tmpWin.windowLevel == UIWindowLevelNormal)
            {
                window = tmpWin;
                break;
            }
        }
    }
    
    NSArray *arrViews = [window subviews];
    
    id nextResponder = [[arrViews objectAtIndex:0] nextResponder];
    
    if ([nextResponder isKindOfClass:[UIViewController class]]){
        
        if ([nextResponder isKindOfClass:[UINavigationController class]]) {
            
            result = [(UINavigationController*)nextResponder visibleViewController];
            
        }else{
            
            result = nextResponder;
        }
        
    }else{
        
        result = [self fetchPresentedCtl:window.rootViewController];
        
    }
    
    return result;
}

+ (UIViewController*)fetchPresentedCtl:(UIViewController*)hCtl
{
    UIViewController *result = hCtl.presentedViewController;
    
    if (result) {
        
        if ([result isKindOfClass:[UINavigationController class]]) {
            
            result = [(UINavigationController*)result visibleViewController];
            
        }else{
            
            result = [self fetchPresentedCtl:result];
            
        }
        
    }else{
        
        result = hCtl;
    }
    
    return result;
}



#pragma mark 裁剪图片大小
+ (UIImage*)imageByScalingNotCroppingForSize:(UIImage*)anImage toSize:(CGSize)frameSize
{
    UIImage* sourceImage = anImage;
    UIImage* newImage = nil;
    CGSize imageSize = sourceImage.size;
    CGFloat width = imageSize.width;
    CGFloat height = imageSize.height;
    CGFloat targetWidth = frameSize.width;
    CGFloat targetHeight = frameSize.height;
    CGFloat scaleFactor = 0.0;
    CGSize scaledSize = frameSize;
    
    if (CGSizeEqualToSize(imageSize, frameSize) == NO) {
        CGFloat widthFactor = targetWidth / width;
        CGFloat heightFactor = targetHeight / height;
        
        // opposite comparison to imageByScalingAndCroppingForSize in order to contain the image within the given bounds
        if (widthFactor > heightFactor) {
            scaleFactor = heightFactor; // scale to fit height
        } else {
            scaleFactor = widthFactor; // scale to fit width
        }
        scaledSize = CGSizeMake(MIN(width * scaleFactor, targetWidth), MIN(height * scaleFactor, targetHeight));
    }
    
    // If the pixels are floats, it causes a white line in iOS8 and probably other versions too
    scaledSize.width = (int)scaledSize.width;
    scaledSize.height = (int)scaledSize.height;
    
    UIGraphicsBeginImageContext(scaledSize); // this will resize
    
    [sourceImage drawInRect:CGRectMake(0, 0, scaledSize.width, scaledSize.height)];
    
    newImage = UIGraphicsGetImageFromCurrentImageContext();
    if (newImage == nil) {
        TKLogInfo(@"could not scale image");
    }
    
    // pop the context to get back to the default
    UIGraphicsEndImageContext();
    return newImage;
}

#pragma -mark 判断是不是带了耳机包括蓝牙耳机
+ (BOOL)isHeadsetPluggedIn
{
    
    AVAudioSessionRouteDescription* route = [[AVAudioSession sharedInstance] currentRoute];
    
    for (AVAudioSessionPortDescription* desc in [route outputs]) {
        
        if ([[desc portType] isEqualToString:AVAudioSessionPortHeadphones]||[[desc portType] isEqualToString:AVAudioSessionPortBluetoothA2DP])
            
            return YES;
    }
    
    return NO;
}

#pragma mark - 对字符串做网络字符串编码
+ (NSString *)encodeToPercentEscapeString:(NSString *)input
{
    NSString *outputStr = (NSString *)
    CFBridgingRelease(CFURLCreateStringByAddingPercentEscapes(
                      kCFAllocatorDefault,
                      (CFStringRef)input,
                      NULL,
                      CFSTR(":/?#[]@!$ &'()*+,;=\"<>%{}|\\^~`"),
                      CFStringConvertNSStringEncodingToEncoding(NSUTF8StringEncoding)));
    
    return outputStr;
    
}

+ (NSString *)wraperDicToJsonStr:(NSDictionary*)rParams{
    
    NSString *jsonString;
    
    NSError *error;
    
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:rParams options:NSJSONWritingPrettyPrinted error:&error];
    
    if (jsonData) {
        
        jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    }
    
    return jsonString;
    
}

+ (NSDictionary *)getIPAddresses
{
    NSMutableDictionary *addresses = [NSMutableDictionary dictionaryWithCapacity:8];
    
    struct ifaddrs *interfaces;
    
    if(!getifaddrs(&interfaces)) {
        // Loop through linked list of interfaces
        struct ifaddrs *interface;
        for(interface = interfaces; interface; interface=interface->ifa_next) {
            if(!(interface->ifa_flags & IFF_UP) /* || (interface->ifa_flags & IFF_LOOPBACK) */ ) {
                continue; // deeply nested code harder to read
            }
            const struct sockaddr_in *addr = (const struct sockaddr_in*)interface->ifa_addr;
            char addrBuf[ MAX(INET_ADDRSTRLEN, INET6_ADDRSTRLEN) ];
            if(addr && (addr->sin_family==AF_INET || addr->sin_family==AF_INET6)) {
                NSString *name = [NSString stringWithUTF8String:interface->ifa_name];
                NSString *type;
                if(addr->sin_family == AF_INET) {
                    if(inet_ntop(AF_INET, &addr->sin_addr, addrBuf, INET_ADDRSTRLEN)) {
                        type = IP_ADDR_IPv4;
                    }
                } else {
                    const struct sockaddr_in6 *addr6 = (const struct sockaddr_in6*)interface->ifa_addr;
                    if(inet_ntop(AF_INET6, &addr6->sin6_addr, addrBuf, INET6_ADDRSTRLEN)) {
                        type = IP_ADDR_IPv6;
                    }
                }
                if(type) {
                    NSString *key = [NSString stringWithFormat:@"%@/%@", name, type];
                    addresses[key] = [NSString stringWithUTF8String:addrBuf];
                }
            }
        }
        // Free memory
        freeifaddrs(interfaces);
    }
    return [addresses count] ? addresses : nil;
}

+ (BOOL)isIpv6{
    NSArray *searchArray =
    @[ IOS_VPN @"/" IP_ADDR_IPv6,
       IOS_VPN @"/" IP_ADDR_IPv4,
       IOS_WIFI @"/" IP_ADDR_IPv6,
       IOS_WIFI @"/" IP_ADDR_IPv4,
       IOS_CELLULAR @"/" IP_ADDR_IPv6,
       IOS_CELLULAR @"/" IP_ADDR_IPv4 ] ;
    
    NSDictionary *addresses = [self getIPAddresses];
    TKLogInfo(@"addresses: %@", addresses);
    
    __block BOOL isIpv6 = NO;
    [searchArray enumerateObjectsUsingBlock:^(NSString *key, NSUInteger idx, BOOL *stop)
     {
         
         TKLogInfo(@"---%@---%@---",key, addresses[key] );
         
         if ([key rangeOfString:@"ipv6"].length > 0  && addresses[key]) {
             
             if ( ![addresses[key] hasPrefix:@"fe80"]) {
                 isIpv6 = YES;
             }
         }
         
     } ];
    
    return isIpv6;
}


/**
 *  Description 调整拍摄的图片的方向
 *
 *  @param image 需调整的图片对象
 *
 *  @return 返回调整后的图片对象
 */
+ (UIImage *)tkFixOrientation:(UIImage*)image
{
    // No-op if the orientation is already correct
    if (image.imageOrientation == UIImageOrientationUp) return image;
    
    // We need to calculate the proper transformation to make the image upright.
    // We do it in 2 steps: Rotate if Left/Right/Down, and then flip if Mirrored.
    CGAffineTransform transform = CGAffineTransformIdentity;
    
    UIImageOrientation iOrientation = image.imageOrientation;
    
    switch (iOrientation)
    {
        case UIImageOrientationDown:
        case UIImageOrientationDownMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, image.size.height);
            transform = CGAffineTransformRotate(transform, M_PI);
            break;
            
        case UIImageOrientationLeft:
        case UIImageOrientationLeftMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, 0);
            transform = CGAffineTransformRotate(transform, M_PI_2);
            break;
            
        case UIImageOrientationRight:
        case UIImageOrientationRightMirrored:
            transform = CGAffineTransformTranslate(transform, 0, image.size.height);
            transform = CGAffineTransformRotate(transform, -M_PI_2);
            break;
    }
    
    switch (iOrientation)
    {
        case UIImageOrientationUpMirrored:
        case UIImageOrientationDownMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, 0);
            transform = CGAffineTransformScale(transform, -1, 1);
            break;
            
        case UIImageOrientationLeftMirrored:
        case UIImageOrientationRightMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.height, 0);
            transform = CGAffineTransformScale(transform, -1, 1);
            break;
    }
    
    
    CGImageRef imgRef = image.CGImage;
    
    // Now we draw the underlying CGImage into a new context, applying the transform
    // calculated above.
    CGContextRef ctx = CGBitmapContextCreate(NULL, image.size.width, image.size.height,
                                             CGImageGetBitsPerComponent(imgRef), 0,
                                             CGImageGetColorSpace(imgRef),
                                             CGImageGetBitmapInfo(imgRef));
    CGContextConcatCTM(ctx, transform);
    
    switch (iOrientation)
    {
        case UIImageOrientationLeft:
        case UIImageOrientationLeftMirrored:
        case UIImageOrientationRight:
        case UIImageOrientationRightMirrored:
            // Grr...
            CGContextDrawImage(ctx, CGRectMake(0,0,image.size.height,image.size.width), imgRef);
            break;
            
        default:
            CGContextDrawImage(ctx, CGRectMake(0,0,image.size.width,image.size.height), imgRef);
            break;
    }
    
    // And now we just create a new UIImage from the drawing context
    CGImageRef cgimg = CGBitmapContextCreateImage(ctx);
    
    UIImage * img = [[UIImage alloc] initWithCGImage:cgimg];
    
    CGContextRelease(ctx);
    CGImageRelease(cgimg);
    return img;
}

+ (NSString *)fetchAppStatisticsMarker
{
//  return  [NSString stringWithFormat:@"%@",[TKSystemHelper getConfigByKey:@"traffic.appId"]];
    
    return  [NSString stringWithFormat:@"tk.fxcstkkh.app"];
}

+ (UIImage *)imageCropFromView : (UIView *)cView withRect:(CGRect)cRect
{
    //创建一个基于位图的图形上下文并指定大小为CGSizeMake(100,100)
    UIGraphicsBeginImageContextWithOptions(cView.frame.size, NO, [UIScreen mainScreen].scale);
    
    //renderInContext 呈现接受者及其子范围到指定的上下文
    [cView.layer renderInContext:UIGraphicsGetCurrentContext()];
    
    //返回一个基于当前图形上下文的图片
    UIImage *extractImage = UIGraphicsGetImageFromCurrentImageContext();
    
    //移除栈顶的基于当前位图的图形上下文
    UIGraphicsEndImageContext();
    
    //以png格式返回指定图片的数据
    NSData *imageData = UIImagePNGRepresentation(extractImage);
    
    UIImage *image = [UIImage imageWithData:imageData];
    
    CGSize sSize = [UIScreen mainScreen].bounds.size;
    
    CGRect cropRect = CGRectMake(cRect.origin.x/sSize.width*image.size.width, cRect.origin.y/sSize.height*image.size.height, cRect.size.width/sSize.width*image.size.width, cRect.size.height/sSize.height*image.size.height);
    
    return [UIImage imageWithCGImage:CGImageCreateWithImageInRect([image CGImage], cropRect)];
}


//获取当前设备是否静音，iOS5以后使用
-(void)getMute:(TKCommonUtilIsMuteBlock)flagBlock{
    
    if (playbackTimer) {
//        TKLogInfo(@"正在静音检查，不再重复进行");
        return;
    };
    
//    TKLogInfo(@"开始静音检查");
    
    self.isMuteBackBlock=flagBlock;
    // iOS 5+ doesn't allow mute switch detection using state length detection
    // So we need to play a blank 100ms file and detect the playback length
    soundDuration = 0.0;
    if (playbackTimer) {
        [playbackTimer invalidate];
        playbackTimer = nil;
    }
    
    // Get the main bundle for the app
    CFBundleRef mainBundle;
    mainBundle = CFBundleGetMainBundle();

    //读取bundle音频资源文件
    NSString *path = [[NSBundle mainBundle] pathForResource:TK_OPEN_RESOURCE_NAME ofType:@"bundle"];
    NSURL * url =[NSURL fileURLWithPath:[NSString stringWithFormat:@"%@/Resources/TKOpenPlugin60026/tkOpenDetection.aif", path]];
    // Create a system sound object representing the sound file
    OSStatus err =  AudioServicesCreateSystemSoundID (
                                      (__bridge CFURLRef)url,
                                      &soundFileObject
                                      );
    
    if (err) {
        TKLogInfo(@"静音检查加载文件出错 %d", (int)err);
    }
    
    AudioServicesAddSystemSoundCompletion (soundFileObject,NULL,NULL,
                                           tkIsMuteSoundCompletionCallback,
                                           (__bridge void*) self);
    
    // Start the playback timer
    playbackTimer = [NSTimer scheduledTimerWithTimeInterval:0.001 target:self selector:@selector(incrementTimer) userInfo:nil repeats:YES];
    // Play the sound
    AudioServicesPlaySystemSound(soundFileObject);
}

static void tkIsMuteSoundCompletionCallback (SystemSoundID mySSID, void* myself) {
    AudioServicesRemoveSystemSoundCompletion (mySSID);
    [[TKCommonUtil shareInstance] playbackComplete];
    
    
}
- (void)playbackComplete {
//    TKLogInfo(@"静音检查结束，时间 %.3f", soundDuration);
    if (soundDuration < 0.010) {
        //静音
        dispatch_async(dispatch_get_main_queue(), ^{
            self.isMuteBackBlock(YES);
        });
        
        
    }else {
        //未静音
        dispatch_async(dispatch_get_main_queue(), ^{
            self.isMuteBackBlock(NO);
        });
        
    }
    [playbackTimer invalidate];
    playbackTimer = nil;
    /*播放全部结束，因此释放所有资源 */
    AudioServicesDisposeSystemSoundID(soundFileObject);

    CFRunLoopStop(CFRunLoopGetCurrent());
    
}
- (void)incrementTimer {
    soundDuration = soundDuration + 0.001;
}

/**
 *  @description 自动设置音频播放工具
 */
+ (void)autoHeadsetState{
    AVAudioSession *session = [AVAudioSession sharedInstance];
//    AVAudioSessionCategoryOptions options = session.categoryOptions;

    UInt32 allowBluetoothInput = 1;
       
    AudioSessionSetProperty (kAudioSessionProperty_OverrideCategoryEnableBluetoothInput,
          sizeof (allowBluetoothInput),
          &allowBluetoothInput
    );
    
    AVAudioSessionCategoryOptions options = session.categoryOptions;
    options = options | AVAudioSessionCategoryOptionAllowBluetoothA2DP;
    [[AVAudioSession sharedInstance] setCategory:session.category  withOptions:options error:nil];
    [session setActive:YES error:nil];
}

/// 签名单向视频文件
/// @param path 文件路径
+ (NSString *)signOneWayVideo:(NSString *)path {
    
    // 非对称加密(文件md5 + userid + tkkh)
//    NSString *path = [NSHomeDirectory() stringByAppendingFormat:@"/Documents/%@.mp4",TK_ONE_WAY_TEMP_MOVE_NAME];
    NSData *fileData = [NSData dataWithContentsOfFile:path];
    NSString *fileSHA256 = [TKShaHelper sha256:fileData];
    
    
//    NSString *encryptStr = [NSString stringWithFormat:@"%@%@", fileSHA256, @"tkkh"];
    NSString *encryptStr = [NSString stringWithFormat:@"%@%@", fileSHA256, [TKPasswordGenerator generatorPassword]];
    
    NSString *pubKey = @"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhpTXOhDLMlsG6WUaDJ8MEAWLyhnuHo6et1K3T2JThz1voEBuMPR4NwX561dpMf++1hbJZNbA8nMe+aXB8AZemkRPtKRWrszCNyTvdwKZipwvIiAsNKbFvEMWcT8j61H+lOD6HrUdRbSmLtgKIawZnTWcJ/t970IY6uXYq7Qn5ts27e4kbX4UcPHPHWWbcbiMYpqeEQEn/W0wDtkbfHnyMuw1b5sLD9AJcfUh/csd2EsV+vhuQ4+CLP6Mx39F43o1jYPSQ+aOtTFlHD8QXsD3u9fLoYXFkEM3N7EA+mZOfwszuk15sOMSrkIbJoFOlYVVdrWFlDureW/Bi1WMAf6opwIDAQAB";
    
    NSString *encryptedStr = [TKRsaHelper rsaPublicKeyEncryptString:encryptStr pubKey:pubKey padding:kSecPaddingPKCS1];
    if ([TKStringHelper isEmpty:encryptStr]) encryptStr = @"";
    
//    NSLog(@"加密的文件sha256 = %@, 组合后的加密串 = %@, 公钥串 = %@, 生成的加密串 = %@", fileSHA256, encryptStr, pubKey , encryptedStr);
//    NSLog(@"加密的文件数据流 = %@", fileData);
    
    return encryptedStr;
}


/**
 *  @description 字符串同音字替换要求的字
 *  @convertParams  同音字的音标和对应字
 *  @word  需要转换的文本
 */
+(NSString *)convertWords:(NSMutableDictionary *)convertParams withWord:(NSString *)word{
    NSString *string=[word mutableCopy];
    for (int i=0; i<word.length; i++) {
     NSString *pinYin=[TKCommonUtil transformPinYin:[word substringWithRange:NSMakeRange(i, 1)]];
        if ([TKStringHelper isNotEmpty:convertParams[pinYin]]) {
            //需要替换同音字
            string=[string stringByReplacingCharactersInRange:NSMakeRange(i, 1) withString:convertParams[pinYin]];
        }
    }
    return string;
}

//转换为没有音标的拼音小写
+ (NSString *)transformPinYin:(NSString *)chinese
{
    NSMutableString *pinyin = [chinese mutableCopy];
    //用kCFStringTransformMandarinLatin方法转化出来的是带音标的拼音，如果需要去掉音标，则继续使用kCFStringTransformStripCombiningMarks方法即可。
    CFStringTransform((__bridge CFMutableStringRef)pinyin, NULL, kCFStringTransformMandarinLatin, NO);
    CFStringTransform((__bridge CFMutableStringRef)pinyin, NULL, kCFStringTransformStripCombiningMarks, NO);
//    NSLog(@"%@", pinyin);
    return [pinyin lowercaseString];
}

+ (NSString *)url:(NSString *)url appendingParamStr:(NSString *)paramStr{
    if (!url) {
        return nil;
    }
    //兼容Vue框架,Vue框架参数拼接在/#/后面
    NSRange vueRange = [url rangeOfString:@"#/"];
    if (vueRange.length>0) {
        NSRange paramRange = [url rangeOfString:@"?"];
        if (paramRange.length>0 && paramRange.location>vueRange.location) {
            paramStr  = [NSString stringWithFormat:@"&%@",paramStr];
        }else{
            paramStr  = [NSString stringWithFormat:@"?%@",paramStr];
        }
        url = [url stringByAppendingString:paramStr];
    }else{
        NSRange range = [url rangeOfString:@"#"];
        if (range.length>0) {
            NSRange paramRange = [url rangeOfString:@"?"];
            if (paramRange.length>0 && paramRange.location<range.location) {
                paramStr  = [NSString stringWithFormat:@"&%@",paramStr];
            }else{
                paramStr  = [NSString stringWithFormat:@"?%@",paramStr];
            }
            
            url = [url stringByReplacingCharactersInRange:NSMakeRange(range.location, 0) withString:paramStr];
        }else{
            NSRange paramRange = [url rangeOfString:@"?"];
            if (paramRange.length>0) {
                paramStr  = [NSString stringWithFormat:@"&%@",paramStr];
            }else{
                paramStr  = [NSString stringWithFormat:@"?%@",paramStr];
            }
            
            url = [url stringByAppendingString:paramStr];
        }
    }
    return url;
}


//切换div&p标签转换为为span标签
+(NSString *)switchLabelToSpan:(NSString *)string{
    if([string rangeOfString:@"<div"].location !=NSNotFound){
        string=[string stringByReplacingOccurrencesOfString:@"<div" withString:@"<span"];
    }

    if([string rangeOfString:@"/div>"].location !=NSNotFound){
        string=[string stringByReplacingOccurrencesOfString:@"/div>" withString:@"/span>"];
    }

    if([string rangeOfString:@"<p"].location !=NSNotFound){
        string=[string stringByReplacingOccurrencesOfString:@"<p" withString:@"<span"];
    }

    if([string rangeOfString:@"/p>"].location !=NSNotFound){
        string=[string stringByReplacingOccurrencesOfString:@"/p>" withString:@"/span>"];
    }
    
    return string;
}

@end
