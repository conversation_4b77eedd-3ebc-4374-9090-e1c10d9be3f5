//
//  TKFaceDetectManager.h
//  TKOpenAccount-Standard
//
//  Created by Felix on 2021/3/10.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

// 旧版状态码。华西用
//typedef enum : NSUInteger {
//    TKFaceDetectStatusUnknown = 0,  // 未知
//    TKFaceDetectStatusFine,     // 正常
//    TKFaceDetectStatusOutOfRect,    // 不在框
//    TKFaceDetectStatusTooSmall,    // 人脸太小，离的太远
//    TKFaceDetectStatusTooBig,    // 人脸太大，离的太近
//} TKFaceDetectStatus;

typedef enum : NSInteger {
    TKFacePassStatusUnknown = -8,  // 不清楚。可能存在上一个环节不通过或者不支持该功能的情况
    TKFacePassStatusNotPass = 0,  // 不通过
    TKFacePassStatusPass,     // 通过
} TKFacePassStatus; // 人脸在框

typedef enum : NSInteger {
    TKFaceCompareStatusUnknown = -8,  // 不清楚。可能存在上一个环节不通过或者不支持该功能的情况
    TKFaceCompareStatusNotPass = 0,  // 不通过
    TKFaceCompareStatusPass,     // 通过
} TKFaceCompareStatus;  // 人脸比对

typedef enum : NSInteger {
    TKSingleFaceStatusUnknown = -8,  // 不清楚。可能存在上一个环节不通过或者不支持该功能的情况
    TKSingleFaceStatusNotSingle = 0,  // 多张人脸
    TKSingleFaceStatusSingle,     // 单张人脸
} TKSingleFaceStatus;  // 人脸数据比对

typedef enum : NSInteger {
    TKFaceErrorTypeUnknown = 0,  //
    TKFaceErrorTypeFaceDetect,  // 不在框、多张人脸、没有人脸、太远、太近
    TKFaceErrorTypeFaceCompare,     // 人脸比对
} TKFaceErrorType;  // 人脸数据比对

@protocol TKFaceDetectManagerDelegate <NSObject>

@optional


// 人脸检测成功回调
// @param faceDetectStatus 检测状态
/// @param faceCompareStatus 人脸比对状态
/// @param facNumberPassStatus 人脸数量状态
/// @param errorMsg 错误信息
- (void)faceDetectDidComplete:(TKFacePassStatus)facePassStatus faceCompareStatus:(TKFaceCompareStatus)faceCompareStatus facNumberPassStatus:(TKSingleFaceStatus)facNumberPassStatus errorMsg:(NSString *)errorMsg;

// 人脸检测成功回调
// @param faceDetectStatus 检测状态
// @param faceNumber 人脸数量

// 人脸检测成功回调
// @param faceDetectStatus 检测状态
/// @param faceCompareStatus 人脸比对状态
/// @param facNumberPassStatus 人脸数量状态
/// @param errorMsg 错误信息
/// @param image 检测的图片
- (void)faceDetectDidComplete:(TKFacePassStatus)facePassStatus faceCompareStatus:(TKFaceCompareStatus)faceCompareStatus facNumberPassStatus:(TKSingleFaceStatus)facNumberPassStatus errorMsg:(NSString *)errorMsg image:(UIImage *)image;

/// 人脸检测失败回调
- (void)faceDetectDidFail:(NSString *)errorMsg;

/// 失败次数超限
- (void)faceDetectDidExceedLimit:(TKFaceErrorType)faceErrorType errorMsg:(NSString *)errorMsg;


@end

NS_ASSUME_NONNULL_BEGIN

/// 人脸检测工具类
@interface TKFaceDetectManager : NSObject

/// 配置字典
@property (nonatomic, readwrite, copy) NSDictionary *configParam;

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam;

/// 代理对象
@property (nonatomic, readwrite, weak) id<TKFaceDetectManagerDelegate> delegate;

/// 人脸目标框Rect.默认是全屏
@property (nonatomic, readwrite, assign) CGRect targetRect;

/// 人脸预览区域.默认是全屏
@property (nonatomic, readwrite, assign) CGSize avPreviewSize;

/// 人脸在框检测失败次数
@property (nonatomic, readwrite, assign) int faceDetectFailureCount;
/// 人脸比对失败次数
@property (nonatomic, readwrite, assign) int faceCompareFailureCount;
/// 人脸数量失败次数
@property (nonatomic, readwrite, assign) int faceNumberFailureCount;
// 在框检测不是通过云端，而是走本地
@property (nonatomic, readwrite, assign) BOOL detectUseLocalService;
// 开启高级在框检测
@property (nonatomic, readwrite, assign) BOOL enableAdvancedFaceDetect;
// 开启高级在框检测
@property (nonatomic, readwrite, assign) BOOL onlyFaceDetect;
// 是否全图区域检测（目前就数字人全程开户需要）
@property (nonatomic, readwrite, assign) BOOL isFullImgDetect;

/// 启动人脸检测
- (void)startFaceDetect;

/// 检测人脸图片
/// @param image 人脸图片
- (void)detectFace:(UIImage *)image;

/// 检测人脸buffer
/// @param pixelBuffer CVPixelBufferRef
- (void)detectFaceWithPixelBuffer:(CVPixelBufferRef)pixelBuffer;

/// 停止人脸检测
- (void)stopFaceDetect;

/// 暂停人脸检测
- (void)suspendedFaceDetect;

/// 恢复人脸检测
- (void)resumeFaceDetect;

/// 记录失败次数
- (void)startRecordFailCount;

/// 获取人脸检测结果汇总数组
- (NSArray *)getFaceDetectTipArray;

@end

NS_ASSUME_NONNULL_END
