//
//  TKFaceDetectManager.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/3/10.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKFaceDetectManager.h"
#import "TKOpenAccountService.h"

#define bigfaceThreshold 0.8F
#define smallfaceThreshold 0.6F
#define bigFaceThresholdBuffer 0.05F
#define smallFaceThresholdBuffer 0.05F
#define inRectThreshold 0.7f

//导出包或者需要真机时候才走旷视活体
//#if TARGET_IPHONE_SIMULATOR
//
//#else
////旷世face++活体
//#import <MGBaseKit/MGBaseKit.h>
//#import <MGLivenessDetection/MGLivenessDetection.h>
//#endif

//#if TARGET_IPHONE_SIMULATOR
@interface TKFaceDetectManager()
//#else
////旷世face++活体
//@interface TKFaceDetectManager()<MGCustomLivenessProtocolDelegate>
//#endif

{
    dispatch_queue_t _faceDetectQueue;
    dispatch_queue_t _faceCompareQueue;
}

//@property (nonatomic, readwrite, assign) BOOL isFaceDetecting; // 是否正在人脸检测
@property (nonatomic, readwrite, assign) BOOL needFaceDetecting; // 是否需要人脸检测。根据时间间隔定时设置为YES
@property (nonatomic, strong) dispatch_source_t faceDetectTimer; // 人脸检测定时器

@property (nonatomic, readwrite, assign) BOOL needRecordFailCount; // 是否记录失败人脸次数
@property (nonatomic, readwrite, assign) int maxFaceDetectFailureCount; // 最大失败人脸识别次数.默认是3次
@property (nonatomic, readwrite, assign) int maxFaceCompareFailureCount; // 最大失败人脸比对次数.默认是3次
@property (nonatomic, readwrite, assign) int maxFaceNumberFailureCount; // 最大人脸数量失败次数.默认是3次

@property (nonatomic, readwrite, assign) BOOL canDetectFace; // 能否人脸在框检测。商汤支持，其他厂商不一定能支持
@property (nonatomic, readwrite, assign) BOOL canDetectFaceNumber; // 能否检测人脸数量。商汤支持，其他厂商不一定能支持
@property (nonatomic, readwrite, assign) BOOL canCompareFace; // 能否比对人脸

@property (nonatomic, readwrite, assign) NSTimeInterval faceDetectInterval; // 人脸检测时间间隔
@property (nonatomic, readwrite, strong) NSString *faceDetectTimerID; // 记录每一轮定时器的ID.回调的不是当前定时器的结果，抛弃所有结果
@property (nonatomic, readwrite, assign) int faceDetectRequestInterval; // 人脸检测间隔。为了服务器负载均衡，每隔指定时间，先调人脸在框检测接口，下一次调综合检测接口

@property (nonatomic, readwrite, assign) int networkRequestCount; // 当前网络请求数量
@property (nonatomic, readwrite, assign) int maxNetworkRequestCount; // 最大网络请求数量.默认是3次

@property (nonatomic, readwrite, strong) TKOpenAccountService *openAccountService;

//活体不支持模拟器
//#if TARGET_IPHONE_SIMULATOR
//
//#else
//@property (nonatomic, strong) MGCustomLivenessDetector *livenessDetector;
//@property (nonatomic, assign) MGCustomLivenessDetectionType randomLivenessAction;//活体随机动作
//@property (nonatomic, readwrite, strong) MGLiveErrorManager *liveErrorManager;
//
//#endif
@property (nonatomic, readwrite, assign) TKFacePassStatus facePassStauts;
@property (nonatomic, readwrite, strong) UIImage *currentDetectImage; // 当前正在检测的图片
@property (nonatomic, readwrite, strong) UIImage *currentCompareImage; // 当前正在比对的图片
@property (nonatomic, readwrite, assign) int needUpateFaceDetectingFlag; // 刷新needFaceDetecting标记的标记。采用本地在框检测的时候，每两个faceDetectInterval刷新needFaceDetecting

@property (nonatomic, readwrite, assign) BOOL isSuspendFaceDetect;  // 是否挂起检测

@end

@implementation TKFaceDetectManager

#pragma mark - Init && Dealloc
- (void)dealloc {
    // NSLog(@"TKFaceDetectManager dealloc");
}

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam {
    if (self = [self init]) {
        self.configParam = configParam;
        
        if ([self.configParam[@"maxFailureCountPerFaceDetect"] intValue] > 0) {
            self.maxFaceDetectFailureCount = [self.configParam[@"maxFailureCountPerFaceDetect"] intValue];
        }
        
        if ([self.configParam[@"maxFailureCountPerFaceCompare"] intValue] > 0) {
            self.maxFaceCompareFailureCount = [self.configParam[@"maxFailureCountPerFaceCompare"] intValue];
        }
//        self.maxFaceCompareFailureCount = 999;
        if ([self.configParam[@"faceDetectInterval"] doubleValue] > 0) {
            self.faceDetectInterval = [self.configParam[@"faceDetectInterval"] doubleValue];
        }
    }
    
    return  self;
}

- (instancetype)init {
    if (self = [super init]) {
        _faceDetectQueue = dispatch_queue_create("com.thinkive.TKOneWayVideoViewController.faceDetectQueue", DISPATCH_QUEUE_SERIAL);
        _faceCompareQueue = dispatch_queue_create("com.thinkive.TKOneWayVideoViewController.faceCompareQueue", DISPATCH_QUEUE_SERIAL);
        
        self.maxFaceDetectFailureCount = 5;
        self.maxFaceCompareFailureCount = 3;
        self.maxFaceNumberFailureCount = 5;
        
        self.networkRequestCount = 0;
        self.maxNetworkRequestCount = 2;
        
        self.canDetectFace = NO;
        self.canDetectFaceNumber = NO;
        self.canCompareFace = NO;
        
        self.faceDetectInterval = 1.0;
        
        self.targetRect = UIScreen.mainScreen.bounds;
        
        self.detectUseLocalService = NO;
        
        self.avPreviewSize = CGSizeMake(UISCREEN_WIDTH, UISCREEN_HEIGHT);
    }
    
    return  self;
}

#pragma mark - Selector
- (void)startFaceDetect
{
    if (self.detectUseLocalService == NO) self.needFaceDetecting = YES;   // 首次云端检测标记检测
    
    // 重新开始录制需要清除之前的结果。防止上一轮检测的影响
    [self clearFaceDetectRecord];
    
    // 重置网络请求数量。网络请求会滞后。重新开始时才重置最好
    self.networkRequestCount = 0;
    
    // 创建定时器
    [self createTimerToSendRequestToFaceDetect];
    
//    #if TARGET_IPHONE_SIMULATOR
//
//    #else
//    [self.livenessDetector changeDetectionType:MGCUSTOM_DETECTION_TYPE_NONE];
//
//    #endif
}

- (void)detectFaceWithPixelBuffer:(CVPixelBufferRef)pixelBuffer {
    
    // 参数控制不做检测
    NSString *disableFaceDetect = self.configParam[@"disableFaceDetect"];
    if ([TKStringHelper isNotEmpty:disableFaceDetect] && [disableFaceDetect isEqualToString:@"1"]) {
        
        if (pixelBuffer != nil) CVPixelBufferRelease(pixelBuffer);
        
//        TKLogInfo(@"人脸在框检测：disableFaceDetect == 1，不做在框检测");
        // 禁用在框检测，不需要人脸检测
        [self callBackWith:TKFacePassStatusUnknown faceCompareStatus:TKFaceCompareStatusUnknown facNumberPassStatus:TKSingleFaceStatusUnknown errorMsg:@"" image:nil];
        return;
    }
    
    // 这种类型目前只有云端检测的场景
    [self sendRequest:nil pixelBuffer:pixelBuffer];
}

- (UIImage *)convertPixelBufferToImage:(CVPixelBufferRef)pixelBuffer
{
    @autoreleasepool
    {
        CIImage* ciImage = [CIImage imageWithCVPixelBuffer : pixelBuffer];
        
        CIContext* context = [CIContext contextWithOptions : @{kCIContextUseSoftwareRenderer : @(YES)
        }];

        CGRect rect = CGRectMake(0, 0, CVPixelBufferGetWidth(pixelBuffer), CVPixelBufferGetHeight(pixelBuffer));

        CGImageRef videoImage = [context createCGImage : ciImage fromRect : rect];

        UIImage *image = [UIImage imageWithCGImage : videoImage];
        
        // TChat Rtc SDK返回的图片旋转了，上层先临时旋转一下
//        UIImage *rotatedImage = [UIImage imageWithCGImage:image.CGImage
//                                                            scale:image.scale
//                                                      orientation:UIImageOrientationRight];;

//        NSString *imageStr = [TKBase64Helper stringWithEncodeBase64Data:UIImagePNGRepresentation(image)];
//        NSLog(@"imageStr = %@", imageStr);

        CGImageRelease(videoImage);
        CVPixelBufferRelease(pixelBuffer);

        return image;
    }
}

- (void)detectFace:(UIImage *)image {
    
    // 参数控制不做检测
    NSString *disableFaceDetect = self.configParam[@"disableFaceDetect"];
    if ([TKStringHelper isNotEmpty:disableFaceDetect] && [disableFaceDetect isEqualToString:@"1"]) {
        
//        TKLogInfo(@"人脸在框检测：disableFaceDetect == 1，不做在框检测");
        // 禁用在框检测，不需要人脸检测
        [self callBackWith:TKFacePassStatusUnknown faceCompareStatus:TKFaceCompareStatusUnknown facNumberPassStatus:TKSingleFaceStatusUnknown errorMsg:@"" image:nil];
        return;
    }
    
    // 判断检测类型
    if (self.detectUseLocalService == NO) {
        
        // 云端检测
        [self sendRequest:image pixelBuffer:nil];
    } else {
        
        // 本地检测
        [self localDetectFace:image];
    }
}

- (void)localDetectFace:(UIImage *)image
{
    // 活体不支持模拟器
    #if TARGET_IPHONE_SIMULATOR
    return;
      
    #else

    if (image == nil) return; // 传入的图片为空跳过等下一帧
    if (self.faceDetectTimer == nil) return; // 取巧判断。这个是用来判断检测是否已经开始
    if (self.isSuspendFaceDetect == YES) {
        // NSLog(@"人脸在框检测：主动挂起检测，抛弃所有质检请求");
        return;
    }
        
    // 异步处理
    dispatch_async(_faceDetectQueue, ^{
        
        if (_currentDetectImage != nil) return;
        
        // 只要错误一次就停止
//        if (self.facePassStauts != TKFacePassStatusNotPass) {
        // 持续做在框检测
            _currentDetectImage = image;
            
//            NSString *base64 = [TKBase64Helper stringWithEncodeBase64Data:UIImageJPEGRepresentation(image, 0.01)];
//            NSLog(@"localDetectFace = %@", base64);
//            [self.livenessDetector detectWithImage:image];
//        }
    });
    
    #endif
}


- (void)stopFaceDetect
{
//    #if TARGET_IPHONE_SIMULATOR
//
//    #else
//    [self.livenessDetector changeDetectionType:self.randomLivenessAction];
//
//    #endif
    
    // 销毁定时器
    if (self.faceDetectTimer) {
        if (self.isSuspendFaceDetect == YES) {  // suspend之后要先resume再cancel
            self.isSuspendFaceDetect = NO;
            dispatch_resume(self.faceDetectTimer);
        }
        dispatch_source_cancel(self.faceDetectTimer);
        self.faceDetectTimer = nil;
    }
    
    // 重置标识
    self.faceDetectTimerID = nil;
    self.needFaceDetecting = NO;
    self.networkRequestCount = 0;
    
    // 停止记录失败次数
    [self stopRecordFailCount];
    
    // 取消所有的网络请求
    [self.openAccountService cancelAllRequest];
}

/// 记录失败次数
- (void)startRecordFailCount {
    
    // 开始录制时，先停止再重新启动检测
    // NSLog(@"人脸在框检测：开始录制时，取消之前所有的网络请求");
    [self stopFaceDetect];
    [self startFaceDetect];
    
    self.needRecordFailCount = YES;
}

/// 停止失败次数
- (void)stopRecordFailCount {
    self.needRecordFailCount = NO;
    
    // 停止记录时不要清除记录结果。因为后续才会用上
//    [self clearFaceDetectRecord];
}

- (void)clearFaceDetectRecord
{
    self.faceDetectFailureCount = 0; // 重置数量
    self.faceCompareFailureCount = 0; // 重置数量
    self.faceNumberFailureCount = 0; // 重置数量
}

- (void)createTimerToSendRequestToFaceDetect
{
    if (self.faceDetectTimer == nil) {
//        // NSLog(@"定时器调试：createTimerToSendRequestToFaceDetect");
        NSTimeInterval start = 0.0;//开始时间

        dispatch_queue_t queue = dispatch_queue_create("com.thinkive.faceDetectTimerQueue", DISPATCH_QUEUE_SERIAL);
        dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
        dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, start * NSEC_PER_SEC), self.faceDetectInterval * NSEC_PER_SEC, 0);
        dispatch_source_set_event_handler(timer, ^{

//             NSLog(@"定时器调试：dispatch_source_set_event_handler");
            
            // 每隔一段时间重置flag
            if (self.detectUseLocalService) {
                
                // 每一轮刷新一次本地检测flag
                self.facePassStauts = TKFacePassStatusUnknown;
                // 每两轮刷新一次人脸比对flag
                if (self.needUpateFaceDetectingFlag == 1) self.needFaceDetecting = YES;
                self.needUpateFaceDetectingFlag = self.needUpateFaceDetectingFlag == 0 ? 1 : 0; // needUpateFaceDetectingFlag和faceDetectRequestInterval类似。但是前者严格跟随定时器变化，后者会和业务绑定。当云端检测延迟时，会导致faceDetectRequestInterval的更新也延迟
                
//                NSLog(@"人脸在框检测：self.needFaceDetecting = %i, self.needUpateFaceDetectingFlag = %i", self.needFaceDetecting, self.needUpateFaceDetectingFlag);
            } else {
                // 云端检测每一轮都刷新flag
                self.needFaceDetecting = YES;
            }
        });
        self.faceDetectTimer = timer;
        self.faceDetectTimerID = [TKUUIDHelper uuid];
        dispatch_resume(self.faceDetectTimer);
    }
}

// 发送请求
- (void)sendRequest:(UIImage *)image pixelBuffer:(CVPixelBufferRef)pixelBuffer
{
//    return;
    if (image == nil && pixelBuffer == nil) return; // 传入的图片为空跳过等下一帧
    
    // 异步处理
    dispatch_async(_faceDetectQueue, ^{

        if (self.needFaceDetecting == NO) {
//            NSLog(@"人脸在框检测：未开启检测或未到下次检测时间，不重复检测");
            if (pixelBuffer != nil) CVPixelBufferRelease(pixelBuffer);
            return;
        }
        if (self.networkRequestCount >= self.maxNetworkRequestCount) {
//            NSLog(@"人脸在框检测：已发送的请求超限，不再发送，防止堵塞网络");
            if (pixelBuffer != nil) CVPixelBufferRelease(pixelBuffer);
            return;
        }
        if (self.isSuspendFaceDetect == YES) {
            // NSLog(@"人脸在框检测：主动挂起检测，抛弃所有质检请求");
            return;
        }
        
        UIImage *tempImage = image;
        // 转换pixelBuffer
        if (tempImage == nil && pixelBuffer != nil) {
//            NSTimeInterval start = [[NSDate date] timeIntervalSince1970];
            tempImage = [self convertPixelBufferToImage:pixelBuffer];
//            NSTimeInterval end = [[NSDate date] timeIntervalSince1970];
//            NSLog(@"convertPixelBufferToImage:start =%.2f, end = %.2f, 耗时%.2f", start, end, end - start);
            if (tempImage == nil) return;   // 转换后图片为空，抛弃本次处理
        }
        
        // 上传参数
        NSString *url = self.configParam[@"url"];
        
        // 对url做非空处理
        if ([TKStringHelper isEmpty:url]) {
            
            // url为空，不需要人脸检测
            [self callBackWith:TKFacePassStatusUnknown faceCompareStatus:TKFaceCompareStatusUnknown facNumberPassStatus:TKSingleFaceStatusUnknown errorMsg:@"" image:tempImage];
            return;
        }
        
        // 状态更新并拦截
        self.needFaceDetecting = NO;
        self.networkRequestCount++;
        
        // 转换成6ase64
        // 最大压缩，只是为了做在框检测，对质量没要求
        _currentDetectImage = tempImage;
        NSString *faceDetectImage_base64 = [TKBase64Helper stringWithEncodeBase64Data:UIImageJPEGRepresentation(tempImage, 0)];
        
        // 适配微服务接口，需要对faceDetectImage_base64做一次url encode处理
        NSString *isRestFull = [self.configParam getStringWithKey:@"isRestFull"];
        if ([TKStringHelper isNotEmpty:isRestFull] && [isRestFull isEqualToString:@"1"] ) {
            
            faceDetectImage_base64 = [TKStringHelper encodeURL:faceDetectImage_base64];
        }
        
//        NSLog(@"图片流长度 = %ld", faceDetectImage_base64.length);
        
        // 准备发送网络请求
        NSMutableDictionary *param = [TKOpenAccountService filterRequestParam:self.configParam];
        
        // 新版接口参数
        NSString *faceDetectFuncNo = [TKStringHelper isNotEmpty:self.configParam[@"faceDetectFuncNo"]] ? self.configParam[@"faceDetectFuncNo"] : @"********";
        param[@"funcNo"] = faceDetectFuncNo;
        if (self.onlyFaceDetect == NO) {
            NSString *faceDetectCompositeFuncNo = [TKStringHelper isNotEmpty:self.configParam[@"faceDetectCompositeFuncNo"]] ? self.configParam[@"faceDetectCompositeFuncNo"] : @"********";
            param[@"funcNo"] = self.faceDetectRequestInterval == 0 ? faceDetectFuncNo : faceDetectCompositeFuncNo;
        }
        
        param[@"image_data"] = [TKStringHelper isNotEmpty:faceDetectImage_base64] ? faceDetectImage_base64 : @"";
        CGRect rect = [self calculateFaceTargetLocation];
        param[@"rect"] = [NSString stringWithFormat:@"%i,%i,%i,%i", (int)rect.origin.x, (int)rect.origin.y, (int)CGRectGetMaxX(rect), (int)CGRectGetMaxY(rect)];
        param[@"origin"] = @"2"; // 1：安卓 2：iOS 3:h5
        
        // 若参数控制不做比对，此时依然使用在框的接口号
        NSString *disableFaceCompare = self.configParam[@"disableFaceCompare"];
        if (([TKStringHelper isNotEmpty:disableFaceCompare] && [disableFaceCompare isEqualToString:@"1"])) {
            param[@"funcNo"] = faceDetectFuncNo;
        }
        
        if (self.faceDetectRequestInterval == 0 || self.onlyFaceDetect) {  // 仅在框检测传递
            param[@"check"] = self.enableAdvancedFaceDetect == YES ? @"1" : @"0"; // 0：关闭 1：开启
        }
        
//        NSLog(@"self.faceDetectRequestInterval = %i, self.onlyFaceDetect = %i, self.enableAdvancedFaceDetect = %i, param[check] = %@, funcNo= %@", self.faceDetectRequestInterval, self.onlyFaceDetect, self.enableAdvancedFaceDetect, param[@"check"], param[@"funcNo"]);

        // NSLog(@"人脸在框检测：发送请求");
//        NSLog(@"人脸在框检测：发送请求，请求参数：%@", param);
//        NSLog(@"人脸在框检测：传入的图片是否存在%@，base64后是否有值：%@，请求参数中的图片源是否有值：%@", image ? @"是" : @"否", [TKStringHelper isNotEmpty:faceDetectImage_base64] ? @"是" : @"否", [TKStringHelper isNotEmpty:param[@"src"]] ? @"是" : @"否");
        
        // 发送请求检测人脸
        dispatch_async(dispatch_get_main_queue(), ^{
            
//            NSLog(@"人脸在框检测：%@", self.faceDetectRequestInterval == 0 ? @"人脸在框检测" : @"人脸比对检测");
            [self sendRequestToDetectFaceWithUrl:url param:param image:tempImage];
//            [self.openAccountService cancelAllRequest];
//            self.maxNetworkRequestCount = 5;
            
            // 轮询调度不同的人脸检测接口
            self.faceDetectRequestInterval = self.faceDetectRequestInterval == 0 ? 1 : 0;
        });
    });
}

- (CGRect)calculateFaceTargetLocation
{
    float left = self.targetRect.origin.x;
    float top = self.targetRect.origin.y;
    float width = self.targetRect.size.width;
    float height = self.targetRect.size.height;
    
    if (self.isFullImgDetect) {
        
        width = 480;
        height = 640;
        // 目标区域和预览区域宽高一致，直接以该区域为检测目标
        return CGRectMake(0, 0, width, height);
    }else {
        
        // 将摄像头坐标（480 * 640）转换成中间坐标系.
        CGFloat mPreviewWidth = 480.f;
        CGFloat mPreviewHeight = 640.f;
    //    CGFloat mPreviewWidth = 640.f;
    //    CGFloat mPreviewHeight = 480.f;

        CGFloat heightRatio = 1.0f * self.avPreviewSize.height / mPreviewHeight; // 高度拉伸比例
    //    CGFloat scaledWholeHeight = mPreviewHeight * heightRatio; // 服务器图片拉伸到和屏幕等高
        CGFloat scaledWholeWidth = mPreviewWidth * heightRatio;  // 服务器图片等比例拉伸
        CGFloat scaledWholeWidthBeyondScreen = scaledWholeWidth - self.avPreviewSize.width;
        
        // 转换回摄像头坐标
        left = (left + scaledWholeWidthBeyondScreen * 0.5) / scaledWholeWidth * mPreviewWidth;
        top = top / self.avPreviewSize.height * mPreviewHeight;
        width = width / scaledWholeWidth * mPreviewWidth;
        height = height / self.avPreviewSize.height * mPreviewHeight;
        
        return CGRectMake(left, top, width, height);
    }
}

- (void)sendRequestToDetectFaceWithUrl:url param:(NSMutableDictionary *)param image:(UIImage *)image
{
    __weak typeof(self) weakSelf = self;
    
    NSTimeInterval start = [[NSDate date] timeIntervalSince1970];
    [self.openAccountService uploadFileWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
        
//        NSLog(@"人脸在框检测：收到响应，响应结果为%@", resultVo);
//        __strong typeof(weakSelf) strongSelf = self;
//        // 提前中断处理，回调的结果不需要处理
        
        // 处理结果
        [weakSelf handleFaceRequestResult:resultVo image:image startTime:start faceDetectTimerID:weakSelf.faceDetectTimerID];
//            NSLog(@"人脸在框检测：回调处理结束");
        
        // 重置
        if (weakSelf.networkRequestCount > 0) weakSelf.networkRequestCount--;
        if (weakSelf.currentDetectImage == image) weakSelf.currentDetectImage = nil;    // 云端在框和比对检测时需要
        if (weakSelf.currentCompareImage == image) weakSelf.currentCompareImage = nil;  // 本地在框检测，云端比对时需要
    }];
}

// 新版处理网络请求方式
- (void)handleFaceRequestResult:(ResultVo *)resultVo image:(UIImage *)image startTime:(NSTimeInterval)startTime faceDetectTimerID:(NSString *)faceDetectTimerID
{
//    NSLog(@"人脸在框检测： compareFace end");
    
    if (self.isSuspendFaceDetect == YES) {
        // NSLog(@"人脸在框检测：主动挂起检测，抛弃所有请求结果");
        return;
    }
    
    // 主动取消请求拦截
    if (resultVo.errorNo == -112) {
        // NSLog(@"人脸在框检测：请求被主动取消");
        return;
    }
    
    // 开始记录结果（开始录制）时，之前的检测结果全部抛弃。由于主动取消了请求并拦截了取消请求回调，此处不重复处理
    
    // 回调的定时器ID和当前ID不一致，意味着不是本轮检测结果。收到的结果全部抛弃
    if ([TKStringHelper isEmpty:faceDetectTimerID] || (![faceDetectTimerID isEqualToString:_faceDetectTimerID])) {
        // NSLog(@"人脸在框检测：定时器ID和当前ID不一致，不是本轮检测结果，上一轮检测的结果全部抛弃。");
        return;
    }
    
    // 特殊情况拦截
    if (self.faceDetectFailureCount >= self.maxFaceDetectFailureCount ||
        /* self.faceNumberFailureCount >= self.maxFaceNumberFailureCount || */ // 人脸数据不是必须项
        self.faceCompareFailureCount >= self.maxFaceCompareFailureCount) {
        // NSLog(@"人脸在框检测：某一项检测超限，防止重复提示错误。抛弃多余结果。"); // 这是在某些特殊的情况，连续极短时间内返回两次错误
        return;
    }
    
    NSTimeInterval end = [[NSDate date] timeIntervalSince1970];
    NSTimeInterval costTime = end - startTime;  // 耗时
    BOOL beyondTimeLimit = costTime > self.faceDetectInterval * 2;  // 超过轮询间隔的2倍
    
    // NSLog(@"人脸在框检测：开始时间%.0f, 结束时间%.0f, 耗时%.2f, 耗时是否超限%i", startTime, end, costTime, beyondTimeLimit);
    
    NSArray *results = (NSArray *)resultVo.results;
    // NSLog(@"人脸在框检测：结果：%@", results);
    
    if (resultVo.errorNo == 0) {
        
        NSDictionary *dic = results.firstObject;
        
        NSString *face_pass = dic[@"face_pass"];
        face_pass = [TKStringHelper isNotEmpty:face_pass] ? face_pass : [NSString stringWithFormat:@"%i", (int)TKFacePassStatusUnknown];
        
        NSString *is_single_face = dic[@"is_single_face"];
        is_single_face = [TKStringHelper isNotEmpty:is_single_face] ? is_single_face : [NSString stringWithFormat:@"%i", (int)TKSingleFaceStatusUnknown];
        
        NSString *compare_pass = dic[@"compare_pass"];
        compare_pass = [TKStringHelper isNotEmpty:compare_pass] ? compare_pass : [NSString stringWithFormat:@"%i", (int)TKFaceCompareStatusUnknown];
        
        NSString *errorMsg = dic[@"check_desc"];
        
        // 判断服务器人脸能力
        // 返回过一次不是不知道的情况，都能检测人脸数量
        if (is_single_face.intValue != TKFacePassStatusUnknown) self.canDetectFaceNumber = YES;
        // 返回过一次不是不知道的情况，都能检测人脸在框
        if (face_pass.intValue != TKFacePassStatusUnknown) self.canDetectFace = YES;
        // 综合接口(不是综合接口，有可能没值，会有问题) && 返回过一次不是不知道的情况，都能检测人脸在框
        if ([TKStringHelper isNotEmpty:compare_pass] && compare_pass.intValue != TKFacePassStatusUnknown) self.canCompareFace = YES;
        
        // 做人脸数量判断
        if (is_single_face.intValue == TKSingleFaceStatusNotSingle) {
            // 统计失败次数
            if (self.needRecordFailCount == YES) {
                self.faceNumberFailureCount = self.faceNumberFailureCount + 1;
                self.faceDetectFailureCount = self.faceDetectFailureCount + 1; // 多张人脸，人脸检测失败也加1
            }
        }
        
        // 先做人脸在框判断
        if (face_pass.intValue == TKFacePassStatusNotPass) {
            // 统计失败次数
            if (self.needRecordFailCount == YES) {
                self.faceDetectFailureCount = self.faceDetectFailureCount + 1;
            }
        }
        
        // 做人脸比对判断
        if (compare_pass.intValue == TKFaceCompareStatusNotPass) {
            // 统计失败次数
            if (self.needRecordFailCount == YES) {
                self.faceCompareFailureCount = self.faceCompareFailureCount + 1;
            }
        }
        
        // NSLog(@"人脸在框检测：在框检测失败次数%i， 人脸数量失败次数%i， 人脸比对失败次数%i", self.faceDetectFailureCount, self.faceNumberFailureCount, self.faceCompareFailureCount);
        // NSLog(@"人脸在框检测：在框检测失败次数是否超限%i， 人脸数量失败次数是否超限%i， 人脸比对失败次数是否超限%i", self.faceDetectFailureCount >= self.maxFaceDetectFailureCount, self.faceNumberFailureCount >= self.maxFaceNumberFailureCount ,self.faceCompareFailureCount >= self.maxFaceCompareFailureCount);
        
        // 录制过程中，超过时间限制，前台不做展示
        // 但是在框某一项达到最大错误，也要展示。否则突然跳失败页面也会很奇怪
        // 人脸比对也会提示
        if (beyondTimeLimit
            && self.faceDetectFailureCount < self.maxFaceDetectFailureCount
            && self.faceNumberFailureCount < self.maxFaceNumberFailureCount
            && self.faceCompareFailureCount < self.maxFaceCompareFailureCount
            && self.needRecordFailCount == YES) {
            
            if (compare_pass.intValue == TKFaceCompareStatusNotPass) {
                
                // 人脸比对不通过，不管是否延迟也展示
            } else {
//                NSLog(@"人脸在框检测：统计错误，但不需要展示");
                return;
            }
        };
        
         // NSLog(@"人脸在框检测：正常统计和展示");
        
        // 返回结果
        [self callBackWith:face_pass.intValue faceCompareStatus:compare_pass.intValue facNumberPassStatus:is_single_face.intValue errorMsg:errorMsg image:image];
        
        // 判断是否超限
        [self judgeFaceDetectFailureCount];
        [self judgeFaceCompareFailureCount];
    } else {
        
        // 录制过程中，超过时间限制，前台不做展示
        if (beyondTimeLimit && self.needRecordFailCount == YES) {
            // NSLog(@"人脸在框检测：请求报错且超时，不需要展示");
            return;
        };
        
        NSString *errorMsg = [NSString stringWithFormat:@"人脸识别失败(%i)", (int)resultVo.errorNo];
        //        NSDictionary *dic = nil;
//        if (results.count > 0) {
//            dic = results.firstObject;
//            if ([TKStringHelper isNotEmpty:dic[@"error_info"]]) errorMsg = dic[@"error_info"];
//        }
        
//        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidFail:)]) {
                [self.delegate faceDetectDidFail:errorMsg];
            }
//        });
    }
}

- (void)judgeFaceDetectFailureCount
{
    if (self.faceDetectFailureCount >= self.maxFaceDetectFailureCount) {
        
        // 通知失败超限
        if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidExceedLimit:errorMsg:)]) {
            [self.delegate faceDetectDidExceedLimit:TKFaceErrorTypeFaceDetect errorMsg:@"由于长时间未检测到面部在框，本次视频录制失败，请重新录制。"];
        }
    }
}


- (void)judgeFaceCompareFailureCount
{
    if (self.faceCompareFailureCount >= self.maxFaceCompareFailureCount) {
        // 通知失败超限
        if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidExceedLimit:errorMsg:)]) {
            [self.delegate faceDetectDidExceedLimit:TKFaceErrorTypeFaceCompare errorMsg: @"人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。"];
        }
    }
}

/// 暂停人脸检测
- (void)suspendedFaceDetect {
    if (self.faceDetectTimer) {
        if (self.isSuspendFaceDetect == NO) {   // 防止重复设置
            self.isSuspendFaceDetect = YES;
            dispatch_suspend(self.faceDetectTimer);
        }
    }
}

/// 恢复人脸检测
- (void)resumeFaceDetect {
    if (self.faceDetectTimer) {
        if (self.isSuspendFaceDetect == YES) {   // 防止重复设置
            self.isSuspendFaceDetect = NO;
            dispatch_resume(self.faceDetectTimer);
        }
    }
}

#pragma mark - Setter && Getter
- (NSArray *)getFaceDetectTipArray
{
    NSString *successImageName = [NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_end_point_ok.png", TK_OPEN_RESOURCE_NAME];
    NSString *errorImageName = [NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_end_point_warning.png", TK_OPEN_RESOURCE_NAME];
    
    NSString *faceDetectTipImage = @"";
    NSString *faceDetectTipContent = @"";
    
    NSString *faceNumberTipImage = @"";
    NSString *faceNumberTipContent = @"";
    
    NSString *faceCompareTipImage = @"";
    NSString *faceCompareTipContent = @"";
    
    // 调试数据
//    self.faceCompareFailureCount = 1;
//    self.faceDetectFailureCount = 0;
//    self.faceNumberFailureCount = 0;
//    self.canCompareFace = YES;
//    self.canDetectFace = YES;
//    self.canDetectFaceNumber = YES;
    
    if (self.faceCompareFailureCount > 0) {
        
        faceCompareTipContent = @"请确保全程为本人录制";
        faceCompareTipImage = errorImageName;
    } else {
        faceCompareTipContent = @"人脸比对通过";
        faceCompareTipImage = successImageName;
    }
    
    if (self.faceDetectFailureCount > 0) {
        faceDetectTipContent = @"人脸检测分数过低，请确保全脸在画面中";
        faceDetectTipImage = errorImageName;
    } else {
        faceDetectTipContent = @"人脸检测通过";
        faceDetectTipImage = successImageName;
    }
    
    if (self.faceNumberFailureCount > 0) {
        
        faceNumberTipContent = @"视频里不能出现他人";
        faceNumberTipImage = errorImageName;
    } else {
        
        faceNumberTipContent = @"";
        faceNumberTipImage = @"";
    }
    
    NSMutableArray *array = @[
        @{
            @"tipImage" : faceCompareTipImage,
            @"tipContent" : faceCompareTipContent,
        },
        @{
            @"tipImage" : faceDetectTipImage,
            @"tipContent" : faceDetectTipContent,
        },
        @{
            @"tipImage" : faceNumberTipImage,
            @"tipContent" : faceNumberTipContent,
        },
    ].mutableCopy;
    // 过滤特殊情况
    if (self.canDetectFaceNumber == NO || self.faceNumberFailureCount == 0) {
        [array removeObjectAtIndex:2];
    }
    if (self.canDetectFace == NO) {
        [array removeObjectAtIndex:1];
    }
    if (self.canCompareFace == NO) {
        [array removeObjectAtIndex:0];
    }
    
    return array;
}

- (TKOpenAccountService *)openAccountService {
    if (_openAccountService == nil) {
        _openAccountService = [TKOpenAccountService new];
    }
    return _openAccountService;
}

- (void)setDetectUseLocalService:(BOOL)detectUseLocalService {
    _detectUseLocalService = detectUseLocalService;
    
    if (detectUseLocalService == YES) {
        // 本地在框检测初始化
//        [self faceSilent];
        self.facePassStauts = TKFacePassStatusUnknown;
    }
}

//#pragma mark MGCustomLivenessProtocolDelegate
//- (void)faceSilent{
//
//#if TARGET_IPHONE_SIMULATOR
// return;
//#else
//
////        if (![MGLiveManager getLicense]) {
////            [self showAlertView:@"提示" message:@"活体检测授权失败" tag:7001];
////            return;
////        }
//
////         NSString *bundlePath = @"MegLive_model" ? [@"MGLiveResource.bundle" stringByAppendingPathComponent:nil] : FaceBundleKey;
//         NSString *tempPath = [[NSBundle mainBundle] pathForResource:@"MegLive_model" ofType:@"" inDirectory:@"MGLiveResource.bundle"];
//        NSData *modelData = [NSData dataWithContentsOfFile:tempPath];
//         NSDictionary *faceOptions = @{MGCustomLivenessDetectorModelRawData:modelData,
//                                       MGCustomLivenessDetectorStepTimeLimit:@(10),
//                                       MGCustomLivenessDetectorMaxMouthOpen:@(0.1),
//                                       MGCustomLivenessDetectorMaxPitchAngle:@(0.1),
//                                       MGCustomLivenessDetectorMinIntegrity:@(0.6),
//         };
//
////        float liveCenterY = (self.targetRect.origin.y + self.targetRect.size.height * 0.5) / (self.targetRect.origin.y + self.targetRect.size.height);
//        MGLiveErrorManager *errorManager = [[MGLiveErrorManager alloc] initWithFaceCenter:CGPointMake(0, 0)];
//        _liveErrorManager = errorManager;
//        errorManager.qualityManager.faceMaxSizeRatioThreshold = 0.8;
//        errorManager.qualityManager.faceWidthThreshold = 200;
//        errorManager.qualityManager.pitchThreshold = 1.0;
//        errorManager.qualityManager.integrityThreshold = 1.0;
//
//        self.livenessDetector = [MGCustomLivenessDetector detectorOfOptions:faceOptions];
//        [self.livenessDetector setDelegate:self];
//        //SDK支持的动作
//        NSArray *actionArray=[[NSArray alloc] initWithObjects:@(MGCUSTOM_DETECTION_TYPE_BLINK),@(MGCUSTOM_DETECTION_TYPE_MOUTH),@(MGCUSTOM_DETECTION_TYPE_POS_PITCH_UP),@(MGCUSTOM_DETECTION_TYPE_POS_PITCH_DOWN), nil];
//        //在动作数组中产生一个随机数
//        int  actionType = arc4random()%[actionArray count];
//
//        self.randomLivenessAction = (MGCustomLivenessDetectionType)[actionArray[actionType] intValue];
//        [self.livenessDetector changeDetectionType:self.randomLivenessAction];
////        [self.livenessDetector changeDetectionType:MGCUSTOM_DETECTION_TYPE_DONE];
//#endif
//
//}
//
//#if TARGET_IPHONE_SIMULATOR
//
//#else
//- (void)onDetectionFailed:(MGCustomLivenessDetectionFailedType)failedType{
//    dispatch_async(dispatch_get_main_queue(), ^{
//
//        _currentDetectImage = nil;
//
////        NSString *errorMsg = @"人脸识别失败";
////        if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidFail:)]) {
////            [self.delegate faceDetectDidFail:errorMsg];
////        }
//
//        //这边不做活体只管在框检测失败就继续检测
//        [self.livenessDetector reset];
//    });
//
//
//}
//
//- (MGCustomLivenessDetectionType)onDetectionSuccess:(MGCustomLivenessDetectionFrame *)validFrame{
//
////    dispatch_async(dispatch_get_main_queue(), ^{
////         if (self.tkPlayCurrent<=TKOneWayPlayAudioCurrentTypeStart) {
////                 //旷世是活体一直开启做人脸在框中检测，所以活体通过后就不要管成功失败了
////                 //活体成功
////                    TKLogInfo(@"TKSmartOneVideo:活体检测成功");
////
////                    self.tkPlayCurrent=TKOneWayPlayAudioCurrentTypeWait;
////                    self.isGetImg=NO;
////
////
////                    UIImage *img=[UIImage imageWithData:[self.livenessDetector getFaceIDData].images[@"image_best"]];
////                //    (UIImage *)[validFrame getImageDataWithCropping:NO andMaxSize:MAXFLOAT andJPEGCompressionQuality:1 andCode:0 andNeedWatermark:NO andNeedEncrypted:NO].images[@"image_best"];
////                      self.liveSuccessImg=img;
////                    [self uploadFaceImgCheck];
////
////
////             }
////    });
//
//    return MGCUSTOM_DETECTION_TYPE_AIMLESS;
//}
//
///*!
// *  每检测一帧图像，都会调用一次此函数。
// *  @param frame 检测到的人脸的相关信息。
// *  @param timeout 剩余的超时时间
// *  @return 无
// */
//- (void)onFrameDetected:(MGCustomLivenessDetectionFrame *)frame andTimeout:(float) timeout{
//
//    dispatch_async(dispatch_get_main_queue(), ^{
//        NSString *errorMsg = @"";
//
//        if(!frame.attr.has_face){
//
//            errorMsg = @"请您保持全脸在人像框";
//        } else if(frame.attr.face_too_large){
//
//            errorMsg = @"请远离屏幕";
//        }
////        else if(frame.attr.eye_left_occlusion> 0.5){
////
////            errorMsg = @"请勿遮挡脸部";
////        } else if(frame.attr.eye_right_occlusion > 0.5){
////
////            errorMsg = @"请勿遮挡脸部";
////        }
//        else if(frame.attr.mouth_occlusion> 0.5){
//
//            errorMsg = @"请勿遮挡脸部";
//        }
//
////        NSLog(@"compareFace onFrameDetected errorMsg = %@", errorMsg);
////        self.canDetectFace = YES;
//
//        if ([TKStringHelper isEmpty:errorMsg]) {
//            self.facePassStauts = TKFacePassStatusPass;
//
//            // 第二轮才做综合接口检测，第一轮直接本地处理
//            if (self.faceDetectRequestInterval == 0 || self.onlyFaceDetect == YES) {
//
//                // 轮询调度不同的人脸检测接口
//                self.faceDetectRequestInterval = 1;
//
//                [self callBackWith:TKFacePassStatusPass faceCompareStatus:TKFaceCompareStatusUnknown facNumberPassStatus:TKSingleFaceStatusUnknown errorMsg:@"" image:_currentDetectImage];
//            } else if (self.faceDetectRequestInterval == 1) {
//
//                // 参数处理
//                NSString *url = self.configParam[@"url"];
//                NSString *disableFaceCompare = self.configParam[@"disableFaceCompare"];
//                // 对url做非空处理
//                if ([TKStringHelper isEmpty:url]
//                    || ([TKStringHelper isNotEmpty:disableFaceCompare] && [disableFaceCompare isEqualToString:@"1"])) {
//
////                    TKLogInfo(@"人脸在框检测：disableFaceCompare == 1，不做比对");
//
//                    // url为空或者禁用人脸比对，不需要人脸比对
//                    if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidComplete:faceCompareStatus:facNumberPassStatus:errorMsg:)]) {
//
//                        // 能到这里，都表明本地在框检测通过
//                        [self callBackWith:TKFacePassStatusPass faceCompareStatus:TKFaceCompareStatusUnknown facNumberPassStatus:TKSingleFaceStatusUnknown errorMsg:@"" image:_currentDetectImage];
//
//                    }
//                } else {
//
//                    // 人脸比对
//                    [self compareFace:_currentDetectImage];
//                }
//            }
//        } else {
//
//            // 统计失败次数
//            if (self.needRecordFailCount == YES) {
//
//
//                // 1个统计周期内，只记录错误次数1次，允许多次显示错误
//                if (self.facePassStauts != TKFacePassStatusNotPass) {
//
//                    self.faceDetectFailureCount = self.faceDetectFailureCount + 1;
//                }
//            }
//
//            self.facePassStauts = TKFacePassStatusNotPass;
//
//            [self callBackWith:TKFacePassStatusNotPass faceCompareStatus:TKFaceCompareStatusUnknown facNumberPassStatus:TKSingleFaceStatusUnknown errorMsg:errorMsg image:_currentDetectImage];
//        }
//
//        _currentDetectImage = nil;  // 这里不是_currentCompareImage。要清空当前在框检测的图片，否则无法持续在框检测
//    });
//}
//
//#endif

- (void)callBackWith:(TKFacePassStatus)facePassStatus faceCompareStatus:(TKFaceCompareStatus)faceCompareStatus facNumberPassStatus:(TKSingleFaceStatus)facNumberPassStatus errorMsg:(NSString *)errorMsg image:(UIImage *)image
{
    // 不再检测过程中，不需要回调
    if (self.faceDetectTimer == nil) return;
    
//    NSLog(@"人脸在框检测：facePassStatus = %i, faceCompareStatus = %i, facNumberPassStatus = %i", facePassStatus, faceCompareStatus, facNumberPassStatus);
    
//    if (facePassStatus != TKFacePassStatusPass) {
//        errorMsg = [NSString stringWithFormat:@"%@(%i)", errorMsg, self.faceDetectFailureCount];
//    } else if (faceCompareStatus != TKFaceCompareStatusPass) {
//        errorMsg = [NSString stringWithFormat:@"%@(%i)", errorMsg, self.faceCompareFailureCount];
//    } else if (facNumberPassStatus != TKSingleFaceStatusNotSingle) {
//        errorMsg = [NSString stringWithFormat:@"%@(%i)", errorMsg, self.faceDetectFailureCount];
//    }
    
    if ([NSThread isMainThread]) {
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidComplete:faceCompareStatus:facNumberPassStatus:errorMsg:image:)]) {
            
            [self.delegate faceDetectDidComplete:facePassStatus faceCompareStatus:faceCompareStatus facNumberPassStatus:facNumberPassStatus errorMsg:errorMsg image:image];
            
        } else if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidComplete:faceCompareStatus:facNumberPassStatus:errorMsg:)]) {
            
            [self.delegate faceDetectDidComplete:facePassStatus faceCompareStatus:faceCompareStatus facNumberPassStatus:facNumberPassStatus errorMsg:errorMsg];
        }
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidComplete:faceCompareStatus:facNumberPassStatus:errorMsg:image:)]) {
                
                [self.delegate faceDetectDidComplete:facePassStatus faceCompareStatus:faceCompareStatus facNumberPassStatus:facNumberPassStatus errorMsg:errorMsg image:image];
                
            } else if (self.delegate && [self.delegate respondsToSelector:@selector(faceDetectDidComplete:faceCompareStatus:facNumberPassStatus:errorMsg:)]) {
                
                [self.delegate faceDetectDidComplete:facePassStatus faceCompareStatus:faceCompareStatus facNumberPassStatus:facNumberPassStatus errorMsg:errorMsg];
            }
        });
    }
}

- (void)compareFace:(UIImage *)image
{
    // 异步处理
    dispatch_async(_faceCompareQueue, ^{
        
        if (self.needFaceDetecting == NO) {
//            NSLog(@"人脸在框检测：未开启检测或未到下次检测时间，不重复检测");
            return;
        }
        if (self.networkRequestCount >= self.maxNetworkRequestCount) {
//            NSLog(@"人脸在框检测：已发送的请求超限，不再发送，防止堵塞网络");
            return;
        }
        if (self.isSuspendFaceDetect == YES) {
            // NSLog(@"人脸在框检测：主动挂起检测，抛弃所有质检请求");
            return;
        }
//        NSLog(@"compareFace");
        
        if (_currentCompareImage != nil) return;
        _currentCompareImage = image;
        
        // 状态更新并拦截
        self.needFaceDetecting = NO;
        self.networkRequestCount++;
        
//        NSLog(@"人脸在框检测： compareFace");
        
        // 转换成6ase64
        // 最大压缩，只是为了做在框检测，对质量没要求
        NSString *faceDetectImage_base64 = [TKBase64Helper stringWithEncodeBase64Data:UIImageJPEGRepresentation(image, 0)];
        
//        NSLog(@"图片流长度 = %ld", faceDetectImage_base64.length);
        // 上传参数
        NSString *url = self.configParam[@"url"];
        // 准备发送网络请求
        NSMutableDictionary *param = [TKOpenAccountService filterRequestParam:self.configParam];

        // 接口参数
        NSString *faceDetectCompositeFuncNo = [TKStringHelper isNotEmpty:self.configParam[@"faceDetectCompositeFuncNo"]] ? self.configParam[@"faceDetectCompositeFuncNo"] : @"********";
        param[@"funcNo"] = faceDetectCompositeFuncNo;
        
        param[@"image_data"] = [TKStringHelper isNotEmpty:faceDetectImage_base64] ? faceDetectImage_base64 : @"";
        param[@"origin"] = @"2"; // 1：安卓 2：iOS 3:h5

        // NSLog(@"人脸在框检测：发送请求");
//        NSLog(@"人脸在框检测：发送请求，请求参数：%@", param);
//        NSLog(@"人脸在框检测：传入的图片是否存在%@，base64后是否有值：%@，请求参数中的图片源是否有值：%@", image ? @"是" : @"否", [TKStringHelper isNotEmpty:faceDetectImage_base64] ? @"是" : @"否", [TKStringHelper isNotEmpty:param[@"src"]] ? @"是" : @"否");
        
        // 发送请求检测人脸
        dispatch_async(dispatch_get_main_queue(), ^{
            [self sendRequestToDetectFaceWithUrl:url param:param image:image];
//            [self.openAccountService cancelAllRequest];
//            self.maxNetworkRequestCount = 5;
            
            // 轮询调度不同的人脸检测接口
            self.faceDetectRequestInterval = 0;
        });
    });
}

@end
