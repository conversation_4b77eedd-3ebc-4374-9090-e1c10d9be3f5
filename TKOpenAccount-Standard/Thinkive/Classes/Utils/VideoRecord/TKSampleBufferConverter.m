//
//  TKSampleBufferConverter.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/2/15.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKSampleBufferConverter.h"




@interface TKSampleBufferConverter()
{
    char *_emptyBuffer;
}

@property (nonatomic, strong) NSMutableArray *bufferPointers;  // 语音合成buffer数组

@end

@implementation TKSampleBufferConverter

- (void)dealloc {
    NSLog(@"TKSampleBufferConverter dealloc");
    
    if (_emptyBuffer) free(_emptyBuffer);
}

- (instancetype)init {
    if (self = [super init]){
        // 不确定空数据包要多大，建议创建一个较大的空数据包
        int len = 4096;
        _emptyBuffer = (char *)calloc(len, sizeof(char));
        memset(_emptyBuffer, 0, len);
    }
    return self;
}

- (AudioStreamBasicDescription)createOutputFormatWithSampleRate:(double)sampleRate
{
    AudioStreamBasicDescription outputFormat;
    memset(&outputFormat, 0, sizeof(outputFormat));
    outputFormat.mSampleRate = sampleRate;
    outputFormat.mFormatID = kAudioFormatLinearPCM;
    outputFormat.mFormatFlags = kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
    outputFormat.mBitsPerChannel = 16;
    outputFormat.mChannelsPerFrame = 1;
    outputFormat.mBytesPerFrame = 2 * outputFormat.mChannelsPerFrame;
    outputFormat.mFramesPerPacket = 1;
    outputFormat.mBytesPerPacket = outputFormat.mBytesPerFrame * outputFormat.mFramesPerPacket;
    return outputFormat;
}

- (AudioConverterRef)createAudioConverterWithInputFormat:(AudioStreamBasicDescription)inputFormat outputFormat:(AudioStreamBasicDescription)outputFormat {
    AudioConverterRef audioConverter = nil;
    OSStatus status = AudioConverterNew(&inputFormat, &outputFormat, &audioConverter);
    if (status != noErr) {
        NSLog(@"AudioConverterNew error: %d", (int)status);
        AudioConverterDispose(audioConverter);
        return nil;
    }
    
    return audioConverter;
}

- (CMSampleBufferRef)createEmptySampleBufferWithSampleBuffer:(CMSampleBufferRef)sampleBuffer toSampleRate:(double)sampleRate  {

    CMBlockBufferRef blockBuffer = CMSampleBufferGetDataBuffer(sampleBuffer);
    size_t oldLength = CMBlockBufferGetDataLength(blockBuffer);
    UInt32 len = (UInt32)oldLength;
    
    // 样本数
    NSInteger numSamples = len * 0.5;

//        NSData *data1 = [NSData dataWithBytes:buffer length:len];
//        NSLog(@"------ data1 = %@", data1);
//        [TKFileHelper writeContent:data1 inFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test1.pcm"] isExtend:YES];
    
    OSStatus result = noErr;
    CMBlockBufferRef dataBuffer = nil;
    result = CMBlockBufferCreateWithMemoryBlock(kCFAllocatorDefault, _emptyBuffer, len, kCFAllocatorNull, NULL, 0, len, kCMBlockBufferAssureMemoryNowFlag, &dataBuffer);
    if (result != kCMBlockBufferNoErr) {
        
        if (dataBuffer != NULL) {
            CFRelease(dataBuffer);
        }
//            NSLog(@"Failed to create sample buffer with error %@.", @(result));
        return nil;
    }
    
//        size_t len2 = 0;
//        char *buffer2 = NULL;
//        CMBlockBufferGetDataPointer(dataBuffer, 0, NULL, &len2, &buffer2);
//        NSData *data2 = [NSData dataWithBytes:buffer2 length:len2];
//        [TKFileHelper writeContent:data2 inFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test2.pcm"] isExtend:YES];
    
    CMAudioFormatDescriptionRef formatDescription;
    AudioStreamBasicDescription audioDescription;
    audioDescription.mSampleRate  = sampleRate;//采样率
    audioDescription.mFormatID    = kAudioFormatLinearPCM;
    audioDescription.mFormatFlags =  kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
    audioDescription.mChannelsPerFrame = 1;
    audioDescription.mFramesPerPacket  = 1;//每一个packet一侦数据
    audioDescription.mBitsPerChannel   = 16;//av_get_bytes_per_sample(AV_SAMPLE_FMT_S16)*8;//每个采样点16bit量化
    audioDescription.mBytesPerPacket   = 2;
    audioDescription.mBytesPerFrame    = 2;
    audioDescription.mReserved = 0;
    result = CMAudioFormatDescriptionCreate(kCFAllocatorDefault, &audioDescription, 0, NULL, 0, NULL, NULL, &formatDescription);
    if (result != noErr) {
//            NSLog(@"Failed to create sample buffer with error %@.", @(result));
        
        if (dataBuffer != NULL) {
            CFRelease(dataBuffer);
        }
        if (formatDescription != NULL) {
            CFRelease(formatDescription);
        }
    }
    
//    CMTime duration = CMTimeMake(1, audioDescription.mSampleRate);
//    CMTime frameTime = CMTimeMake(CMSampleBufferGetPresentationTimeStamp(sampleBuffer).value / CMSampleBufferGetPresentationTimeStamp(sampleBuffer).timescale * audioDescription.mSampleRate, audioDescription.mSampleRate);
//    CMSampleTimingInfo sampleTimingInfo = { duration, frameTime, kCMTimeInvalid };
    
    CMSampleTimingInfo sampleTimingInfo = {0};
    sampleTimingInfo.decodeTimeStamp = kCMTimeInvalid;
    sampleTimingInfo.presentationTimeStamp = CMSampleBufferGetPresentationTimeStamp(sampleBuffer);
    sampleTimingInfo.duration = CMSampleBufferGetDuration(sampleBuffer);
    
    // 更新 sampleTimingInfo.presentationTimeStamp
//    sampleTimingInfo.presentationTimeStamp = [self addRandomTimeWithLastSampleTime:CMSampleBufferGetPresentationTimeStamp(sampleBuffer)];
    
    size_t sampleSizeArray = {(size_t)2};
    
    CMSampleBufferRef newSampleBuffer;
    result = CMSampleBufferCreateReady(kCFAllocatorDefault,
                                       dataBuffer,
                                       formatDescription,
                                       numSamples, 1, &sampleTimingInfo, 1, &sampleSizeArray,
                                       &newSampleBuffer);
    
    if (dataBuffer != NULL) {
        CFRelease(dataBuffer);
    }
    if (formatDescription != NULL) {
        CFRelease(formatDescription);
    }
//        CMBlockBufferRef tempDataBuffer2 = CMSampleBufferGetDataBuffer(newSampleBuffer);
//        size_t len3 = 0;
//        char *buffer3 = NULL;
//        CMBlockBufferGetDataPointer(tempDataBuffer2, 0, NULL, &len3, &buffer3);
//        NSData *data3 = [NSData dataWithBytes:buffer3 length:len3];
//        [TKFileHelper writeContent:data3 inFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test3.pcm"] isExtend:YES];
    
    if (result == noErr) {
        
//            CFAutorelease(newSampleBuffer);
        return newSampleBuffer;
    } else {
//            NSLog(@"Failed to create sample buffer with error %@.", @(result));
        if (newSampleBuffer != NULL) {
            CFRelease(newSampleBuffer);
        }
        return nil;
    }
}

- (CMSampleBufferRef)convertPCMStrmToCMSampleBufferRefWithBuffer:(char *)buffer len:(int)len lastSampleTime:(CMTime)lastSampleTime toSampleRate:(double)sampleRate
{
    //分配空间
    NSInteger numSamples = len * 0.5;

//        NSData *data1 = [NSData dataWithBytes:buffer length:len];
//        NSLog(@"------ data1 = %@", data1);
//        [TKFileHelper writeContent:data1 inFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test1.pcm"] isExtend:YES];
    
    OSStatus result = noErr;
    CMBlockBufferRef dataBuffer = nil;
    result = CMBlockBufferCreateWithMemoryBlock(kCFAllocatorDefault, buffer, len, kCFAllocatorNull, NULL, 0, len, kCMBlockBufferAssureMemoryNowFlag, &dataBuffer);
    if (result != kCMBlockBufferNoErr) {
        
        if (dataBuffer != NULL) {
            CFRelease(dataBuffer);
        }
//            NSLog(@"Failed to create sample buffer with error %@.", @(result));
        return nil;
    }
    
//        size_t len2 = 0;
//        char *buffer2 = NULL;
//        CMBlockBufferGetDataPointer(dataBuffer, 0, NULL, &len2, &buffer2);
//        NSData *data2 = [NSData dataWithBytes:buffer2 length:len2];
//        [TKFileHelper writeContent:data2 inFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test2.pcm"] isExtend:YES];
    
    CMAudioFormatDescriptionRef formatDescription;
    AudioStreamBasicDescription audioDescription;
    audioDescription.mSampleRate  = sampleRate;//采样率
    audioDescription.mFormatID    = kAudioFormatLinearPCM;
    audioDescription.mFormatFlags =  kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
    audioDescription.mChannelsPerFrame = 1;
    audioDescription.mFramesPerPacket  = 1;//每一个packet一侦数据
    audioDescription.mBitsPerChannel   = 16;//av_get_bytes_per_sample(AV_SAMPLE_FMT_S16)*8;//每个采样点16bit量化
    audioDescription.mBytesPerPacket   = 2;
    audioDescription.mBytesPerFrame    = 2;
    audioDescription.mReserved = 0;
    result = CMAudioFormatDescriptionCreate(kCFAllocatorDefault, &audioDescription, 0, NULL, 0, NULL, NULL, &formatDescription);
    if (result != noErr) {
    //            NSLog(@"Failed to create sample buffer with error %@.", @(result));
        if (dataBuffer != NULL) {
            CFRelease(dataBuffer);
        }
        if (formatDescription != NULL) {
            CFRelease(formatDescription);
        }
        return nil;
    }
    
//        CMTime duration = CMTimeMake(1, audioDescription.mSampleRate);
////        CMTime frameTime = CMTimeAdd(lastSampleTime, duration);
//        CMTime frameTime = CMTimeMake(lastSampleTime.value / lastSampleTime.timescale * audioDescription.mSampleRate, audioDescription.mSampleRate);
    CMTime duration = CMTimeMake(1, lastSampleTime.timescale);
    CMTime frameTime = lastSampleTime;
    CMSampleTimingInfo sampleTimingInfo = { duration, frameTime, kCMTimeInvalid };

    // 更新 sampleTimingInfo.presentationTimeStamp
//    sampleTimingInfo.presentationTimeStamp = [self addRandomTimeWithLastSampleTime:lastSampleTime];
    
    size_t sampleSizeArray = {(size_t)2};
    
    CMSampleBufferRef newSampleBuffer;
    result = CMSampleBufferCreateReady(kCFAllocatorDefault,
                                       dataBuffer,
                                       formatDescription,
                                       numSamples, 1, &sampleTimingInfo, 1, &sampleSizeArray,
                                       &newSampleBuffer);
    
    if (dataBuffer != NULL) {
        CFRelease(dataBuffer);
    }
    if (formatDescription != NULL) {
        CFRelease(formatDescription);
    }
//        CMBlockBufferRef tempDataBuffer2 = CMSampleBufferGetDataBuffer(newSampleBuffer);
//        size_t len3 = 0;
//        char *buffer3 = NULL;
//        CMBlockBufferGetDataPointer(tempDataBuffer2, 0, NULL, &len3, &buffer3);
//        NSData *data3 = [NSData dataWithBytes:buffer3 length:len3];
//        [TKFileHelper writeContent:data3 inFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test3.pcm"] isExtend:YES];
    
    if (result == noErr) {
//            CFAutorelease(newSampleBuffer);
        return newSampleBuffer;
    } else {
//            NSLog(@"Failed to create sample buffer with error %@.", @(result));
        if (newSampleBuffer != NULL) {
            CFRelease(newSampleBuffer);
        }
        return nil;
    }
}

- (CMSampleBufferRef)convertSamplesFromSampleBuffer:(CMSampleBufferRef)sampleBuffer toSampleRate:(double)sampleRate
{
    CMBlockBufferRef blockBuffer = CMSampleBufferGetDataBuffer(sampleBuffer);
    if (blockBuffer == nil) return nil;
    
    size_t length = CMBlockBufferGetDataLength(blockBuffer);
    SInt16 *samples = (SInt16 *)malloc(length);
    CMBlockBufferCopyDataBytes(blockBuffer, 0, length, samples);

    AudioStreamBasicDescription inputFormat = *CMAudioFormatDescriptionGetStreamBasicDescription(CMSampleBufferGetFormatDescription(sampleBuffer));
    AudioStreamBasicDescription outputFormat = [self createOutputFormatWithSampleRate:sampleRate];
    
//    UInt32 outputBufferSize = (UInt32)(length * outputFormat.mSampleRate / inputFormat.mSampleRate + 0.5);
    UInt32 outputBufferSize = (UInt32)(length * outputFormat.mSampleRate / inputFormat.mSampleRate);
    if (outputBufferSize % 2 != 0) {
        // 如果是单数，加1
        outputBufferSize += 1;
    }
    SInt16 *outputBuffer = (SInt16 *)malloc(outputBufferSize);
    
    AudioConverterRef audioConverter = [self createAudioConverterWithInputFormat:inputFormat outputFormat:outputFormat];
    if (audioConverter == nil) {
        free(samples);
        free(outputBuffer);
        return nil;
    }
    
    UInt32 inputDataProcBufferSize = (UInt32)length;
    UInt32 outputDataProcBufferSize = outputBufferSize;
    
    AudioBufferList inputDataProcBufferList;
    inputDataProcBufferList.mNumberBuffers = 1;
    inputDataProcBufferList.mBuffers[0].mNumberChannels = inputFormat.mChannelsPerFrame;
    inputDataProcBufferList.mBuffers[0].mDataByteSize = inputDataProcBufferSize;
    inputDataProcBufferList.mBuffers[0].mData = samples;
    
    AudioBufferList outputDataProcBufferList;
    outputDataProcBufferList.mNumberBuffers = 1;
    outputDataProcBufferList.mBuffers[0].mNumberChannels = outputFormat.mChannelsPerFrame;
    outputDataProcBufferList.mBuffers[0].mDataByteSize = outputDataProcBufferSize;
    outputDataProcBufferList.mBuffers[0].mData = outputBuffer;
    
    UInt32 numOutputDataPackets = outputDataProcBufferSize / outputFormat.mBytesPerPacket;
    OSStatus status = AudioConverterFillComplexBuffer(audioConverter, inputDataProc, &inputDataProcBufferList, &numOutputDataPackets, &outputDataProcBufferList, NULL);
    if (status != noErr) {
//        NSLog(@"AudioConverterFillComplexBuffer error: %d", (int)status);
        free(samples);
        free(outputBuffer);
        AudioConverterDispose(audioConverter);
        return nil;
    }
    
    free(samples);
    AudioConverterDispose(audioConverter);
    
    // 转成新的CMSampleBufferRef
    CMAudioFormatDescriptionRef formatDescription;
    CMAudioFormatDescriptionCreate(kCFAllocatorDefault, &outputFormat, 0, NULL, 0, NULL, NULL, &formatDescription);
    
    if (status != noErr) {
//        NSLog(@"Failed to create sample buffer with error %@.", @(status));
        free(outputBuffer);
        if (formatDescription) CFRelease(formatDescription);
        return nil;
    }
    
    CMBlockBufferRef dataBuffer;
    status = CMBlockBufferCreateWithMemoryBlock(kCFAllocatorDefault, outputDataProcBufferList.mBuffers[0].mData, outputDataProcBufferList.mBuffers[0].mDataByteSize, kCFAllocatorNull, NULL, 0, outputDataProcBufferList.mBuffers[0].mDataByteSize, kCMBlockBufferAssureMemoryNowFlag, &dataBuffer);
    if (status != noErr) {
//        NSLog(@"CMBlockBufferCreateWithMemoryBlock failed with status %d", (int)status);
        if (formatDescription) CFRelease(formatDescription);
        if (dataBuffer) CFRelease(dataBuffer);
        free(outputBuffer);
        return NULL;
    }
    
    CMSampleTimingInfo sampleTimingInfo = {0};
    sampleTimingInfo.decodeTimeStamp = kCMTimeInvalid;
    sampleTimingInfo.presentationTimeStamp = CMSampleBufferGetPresentationTimeStamp(sampleBuffer);
    sampleTimingInfo.duration = CMSampleBufferGetDuration(sampleBuffer);

    // 更新 sampleTimingInfo.presentationTimeStamp
//    sampleTimingInfo.presentationTimeStamp = [self addRandomTimeWithLastSampleTime:CMSampleBufferGetPresentationTimeStamp(sampleBuffer)];

    
    size_t sampleSizeArray = {(size_t)2};
    
    CMSampleBufferRef newSampleBuffer;
    status = CMSampleBufferCreateReady(kCFAllocatorDefault,
                                       dataBuffer,
                                       formatDescription,
                                       outputDataProcBufferSize * 0.5, 1, &sampleTimingInfo, 1, &sampleSizeArray,
                                       &newSampleBuffer);
    
    CFRelease(dataBuffer);
    CFRelease(formatDescription);
    
    // 数据源要在合适时机才能销毁，这里记录记录源
    [self addBufferPointer:outputBuffer];
    
    if (status != noErr) {
        if (newSampleBuffer) CFRelease(newSampleBuffer);
//        NSLog(@"CMSampleBufferCreate failed with status %d", (int)status);
        return nil;
    }
    
//    CFAutorelease(newSampleBuffer);
    return newSampleBuffer;
}

OSStatus inputDataProc(AudioConverterRef inAudioConverter, UInt32 *ioNumberDataPackets, AudioBufferList *ioData, AudioStreamPacketDescription **outDataPacketDescription, void *inUserData)
{
    AudioBufferList *inputData = (AudioBufferList *)inUserData;
    ioData->mBuffers[0].mData = inputData->mBuffers[0].mData;
    ioData->mBuffers[0].mDataByteSize = inputData->mBuffers[0].mDataByteSize;
    ioData->mBuffers[0].mNumberChannels = inputData->mBuffers[0].mNumberChannels;
    *ioNumberDataPackets = inputData->mBuffers[0].mDataByteSize / sizeof(SInt16);
    
    return noErr;
}

- (void)addBufferPointer:(SInt16 *)ptr {
    if (ptr == NULL) return;
    [self.bufferPointers addObject:[NSValue valueWithPointer:ptr]];
}

- (void)releaseAllBufferPointers {
    for (NSValue *addressValue in  self.bufferPointers) {
        SInt16 *ptr = (SInt16 *)[addressValue pointerValue];
        if (ptr) free(ptr);
    }
    [self.bufferPointers removeAllObjects];
}

- (CMTime)addRandomTimeWithLastSampleTime:(CMTime)lastSampleTime
{
    CMTime duration = CMTimeMake(1, lastSampleTime.timescale);
    CMTime frameTime = lastSampleTime;
    CMSampleTimingInfo sampleTimingInfo = { duration, frameTime, kCMTimeInvalid };
    
    // 生成 0 到 1000 的随机时间偏移量
//    NSUInteger randomOffset = (1 + arc4random_uniform(9)) * sampleTimingInfo.presentationTimeStamp.timescale;
    NSUInteger randomOffset = 12 * sampleTimingInfo.presentationTimeStamp.timescale;

    // 将随机时间偏移量加到原始的presentationTimeStamp上
    CMTime newPresentationTimeStamp = CMTimeAdd(sampleTimingInfo.presentationTimeStamp, CMTimeMake(-randomOffset, sampleTimingInfo.presentationTimeStamp.timescale));
    
    return newPresentationTimeStamp;
}


- (NSMutableArray *)bufferPointers {
    if (!_bufferPointers) {
        _bufferPointers = [NSMutableArray array];
    }
    return _bufferPointers;
}
@end
