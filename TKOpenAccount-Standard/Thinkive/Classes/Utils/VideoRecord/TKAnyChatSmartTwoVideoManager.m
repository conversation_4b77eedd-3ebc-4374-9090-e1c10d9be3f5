//
//  TKAnyChatSmartTwoVideoManager.m
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2023/1/28.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKAnyChatSmartTwoVideoManager.h"
#import "TKDirectVideoModel.h"
#import "AnyChatDefine.h"
#import "AnyChatErrorCode.h"
#import "AnyChatPlatform.h"

@interface TKAnyChatSmartTwoVideoManager()<AnyChatNotifyMessageDelegate,AnyChatTransDataDelegate,AnyChatTextMsgDelegate,AnyChatStateChangeDelegate,TKSmartTwoVideoManagerDelegate>
{
    AnyChatPlatform *tkAnychat;
    int tkCountDown;
    NSTimer *tkCountDownTime,*tkUdTimer;
}

@end


@implementation TKAnyChatSmartTwoVideoManager
@synthesize delegate=_delegate;
@synthesize requestParams=_requestParams;
@synthesize contentView=_contentView;
@synthesize remoteContentView=_remoteContentView;

#pragma mark - Init

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSMutableDictionary *)requestParams {
    if (self = [self init]) {
        self.requestParams = requestParams;
    }
    return  self;
}

-(NSString *)getTimeStamp{
    // 设置想要的格式，hh与HH的区别:分别表示12小时制,24小时制
    //把NSDate按formatter格式转成NSString
    NSString *currentTime =[TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
    return currentTime;
}

#pragma mark 视频开启关闭相关
/***
 启动视频见证
 */
- (void)startSmartTwoVideo:(NSString *)sUrl withPort:(int)sPort {
    //初始化
    [TKDirectVideoModel shareInstance].userVideoId=0;
    [TKDirectVideoModel shareInstance].seatVideoId=0;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(AnyChatNotifyHandler:) name:@"ANYCHATNOTIFY" object:nil];
    [AnyChatPlatform InitSDK:0];
    tkAnychat = [[AnyChatPlatform alloc] init];
    tkAnychat.notifyMsgDelegate = self;
    tkAnychat.stateChangeDelegate = self;
    tkAnychat.transDataDelegate = self;
    tkAnychat.textMsgDelegate = self;
#ifdef BRAC_SO_CLOUD_APPGUID
    if (self.requestParams[@"appId"]) {
        [AnyChatPlatform SetSDKOptionString:BRAC_SO_CLOUD_APPGUID : self.requestParams[@"appId"]];
    }
#endif
    [AnyChatPlatform Connect:sUrl : sPort];
    tkCountDown = 20;
    [TKDirectVideoModel shareInstance].isStartingVideo = NO;
    [TKDirectVideoModel shareInstance].isTransBufferMsg = NO;
    
    //记录开始连接视频服务器事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1004:网络情况%@|发起连接|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
}

-(void)stopSmartTwoVideo{
    [self endACVideo];
}

/**
 *
 * @method tkSwitchCameraNewAnyChatWitness
 *
 * @brief 切换摄像头
 *
 */
- (void)switchCameraSmartTwoVideo:(BOOL)isFrontCamera {
    NSMutableArray *cams = [AnyChatPlatform EnumVideoCapture];
    if (isFrontCamera) {
        NSString *camName = (NSString*)[cams objectAtIndex:cams.count-2];
        TKLogInfo(@"切换摄像头为:%@",camName);
        [AnyChatPlatform SelectVideoCapture:camName];
    }else{
        NSString *camName = (NSString*)[cams objectAtIndex:cams.count-1];
        TKLogInfo(@"切换摄像头为:%@",camName);
        [AnyChatPlatform SelectVideoCapture:camName];
    }
}

    
#pragma mark anychat delegate
- (void)AnyChatNotifyHandler:(NSNotification*)notify
{
    NSDictionary* dict = notify.userInfo;
    [tkAnychat OnRecvAnyChatNotify:dict];
}

// 连接服务器消息
- (void) OnAnyChatConnect:(BOOL) bSuccess
{
    //记录开始连接视频服务器返回事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1005:网络情况%@|%d|%@",[TKNetHelper getNetworkTypeInfo],bSuccess,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if(bSuccess){//登录anychat
        
        

        
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            [AnyChatPlatform Login:self.requestParams[@"loginName"] : self.requestParams[@"loginPwd"]?self.requestParams[@"loginPwd"]:@""];
        }else{
            NSString *loginName = [NSString stringWithFormat:@"user%@", self.requestParams[@"user_id"]];
            
            [AnyChatPlatform Login:loginName : @"123456"];
        }
        
        //记录开始登陆视频服务器
        NSString *logString=[NSString stringWithFormat:@"TKMSG1006:网络情况%@|开始登录服务器|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

        if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
            [self.delegate uploadSmartTwoVideoLog:logString];
        }
    }else{
        
        TKLogInfo(@"-----------------connect fail------------");
        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10002";
        }else{
            witnessResult = @"-3";//连接视频服务器失败
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"连接anychat服务器失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
    }
}

// 用户登陆消息
- (void) OnAnyChatLogin:(int) dwUserId : (int) dwErrorCode
{
    //记录开始登陆视频服务器返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1007:网络情况%@|登陆服务器结果%d|%@",[TKNetHelper getNetworkTypeInfo],dwErrorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if(dwErrorCode == GV_ERR_SUCCESS)
    {
        TKLogInfo(@"用户id:%d",dwUserId);
        
        [TKDirectVideoModel shareInstance].userVideoId = dwUserId;
        
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            if (self.requestParams[@"roomId"]) {
                [TKDirectVideoModel shareInstance].witRoomId=self.requestParams[@"roomId"];
                [AnyChatPlatform EnterRoom:[[TKDirectVideoModel shareInstance].witRoomId intValue] :self.requestParams[@"roomPwd"]?self.requestParams[@"roomPwd"]:@""];
            }else if(self.requestParams[@"roomName"]){
                [TKDirectVideoModel shareInstance].witRoomId=self.requestParams[@"roomName"];
                [AnyChatPlatform EnterRoomEx :[TKDirectVideoModel shareInstance].witRoomId :self.requestParams[@"roomPwd"]?self.requestParams[@"roomPwd"]:@""];
            }
        }else{
            
            //不是3.0排队就去走4.0进视频方法
            if ([self.requestParams[@"version"] isEqualToString:@"3.0"]) {
                [AnyChatPlatform EnterRoomEx :[TKDirectVideoModel shareInstance].witRoomId : @""];
            }else{
                [AnyChatPlatform EnterRoom:[[TKDirectVideoModel shareInstance].witRoomId intValue] :@""];
            }
           
        }
        
 

        //记录开始进入房间

        NSString *logString=[NSString stringWithFormat:@"TKMSG1008:网络情况%@|开始进入房间|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

        if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
            [self.delegate uploadSmartTwoVideoLog:logString];
        }
    }else{
        
        TKLogInfo(@"登陆失败");
        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10002";
        }else{
            witnessResult = @"-3";//连接视频服务器失败
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"登录anychat服务器失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
    }
}

// 用户进入房间消息
- (void) OnAnyChatEnterRoom:(int) dwRoomId : (int) dwErrorCode
{
    //记录开始进入房间返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1009:网络情况%@|进入房间结果%d|%@",[TKNetHelper getNetworkTypeInfo],dwErrorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if (dwErrorCode == 0) {
        
        TKLogInfo(@"已进入房间");
        
        // 埋点-双向-视频-进入房间
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressNone result:TKPrivateEventResultSuccess orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressEnterRoom eventDic:eventDic];
        
        
        NSMutableArray *onlineUser = [AnyChatPlatform GetOnlineUser];//获取房间中在线人数
        
        if (onlineUser.count > 0){
            
            for (NSNumber *uid in onlineUser) {
                
                if ([uid intValue] != [TKDirectVideoModel shareInstance].userVideoId) {
                    
                    [TKDirectVideoModel shareInstance].seatVideoId = [uid intValue];
                    
                    break;
                }
            }
        }
        
        if ([TKDirectVideoModel shareInstance].seatVideoId) {
            
            if (![TKDirectVideoModel shareInstance].isStartingVideo) {
                
                [TKDirectVideoModel shareInstance].isStartingVideo = YES;
                
                TKLogInfo(@"坐席id=%d",[TKDirectVideoModel shareInstance].seatVideoId);
                
                NSString *uName = [AnyChatPlatform GetUserName:[TKDirectVideoModel shareInstance].seatVideoId];

                NSString *serviceInfo;
                if ([TKStringHelper isEmpty:[TKDirectVideoModel shareInstance].staffTips]) {
                    serviceInfo=[NSString stringWithFormat:@"%@：%@",[TKDirectVideoModel shareInstance].serviceTipString,uName];
                }else{
                    serviceInfo=[TKDirectVideoModel shareInstance].staffTips;
                }
                
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoServiceInfo:)]) {
                    [self.delegate showSmartTwoVideoServiceInfo:serviceInfo];
                }
               
                [self startACVideo];
            }
            
        }else{
            
            if (tkCountDownTime)
            {
                [tkCountDownTime invalidate];
                tkCountDownTime = nil;
            }
            tkCountDownTime = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(showACCountDown) userInfo:nil repeats:YES];
        }
        
    }else{
        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10002";
        }else{
            witnessResult = @"-4";//进入房间失败
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"进入房间失败" tipTitle:@"视频录制提示" tipDesc:@"视频连接失败,请稍后重试"];
        
    }
}

// 房间在线用户消息
- (void) OnAnyChatOnlineUser:(int) dwUserNum : (int) dwRoomId
{
    TKLogInfo(@"房间在线人数:%d",dwUserNum);
    //记录开始房间人数日志
    NSString *logString=[NSString stringWithFormat:@"TKMSG100901:网络情况%@|当前房间人数%d|%@",[TKNetHelper getNetworkTypeInfo],dwUserNum,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
}

// 用户进入房间消息
- (void) OnAnyChatUserEnterRoom:(int) dwUserId
{
    NSString *l = [NSString stringWithFormat:@"用户[%@]进入房间",[AnyChatPlatform GetUserName:dwUserId]];
    
    TKLogInfo(@"%@", l);
    
    if (tkCountDownTime) {
        [tkCountDownTime invalidate];
        tkCountDownTime = nil;
    }
    
    if (tkUdTimer) {//启动上下行获取定时器
        [tkUdTimer setFireDate:[NSDate date]];
    }
    
    NSMutableArray *onlineUser = [AnyChatPlatform GetOnlineUser];//获取房间中在线人数
    
    if (onlineUser.count > 0){
        
        for (NSNumber *uid in onlineUser) {
            
            if ([uid intValue] != [TKDirectVideoModel shareInstance].userVideoId) {
                
                [TKDirectVideoModel shareInstance].seatVideoId = [uid intValue];
                
                break;
            }
        }
        
        if (![TKDirectVideoModel shareInstance].isStartingVideo && [TKDirectVideoModel shareInstance].seatVideoId) {
            
            [TKDirectVideoModel shareInstance].isStartingVideo = YES;
            
            TKLogInfo(@"坐席id=%d",[TKDirectVideoModel shareInstance].seatVideoId);
            
            NSString *uName = [AnyChatPlatform GetUserName:[TKDirectVideoModel shareInstance].seatVideoId];
            
            NSString *serviceInfo;
            if ([TKStringHelper isEmpty:[TKDirectVideoModel shareInstance].staffTips]) {
                serviceInfo=[NSString stringWithFormat:@"%@：%@",[TKDirectVideoModel shareInstance].serviceTipString,uName];
            }else{
                serviceInfo=[TKDirectVideoModel shareInstance].staffTips;
            }
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoServiceInfo:)]) {
                [self.delegate showSmartTwoVideoServiceInfo:serviceInfo];
            }

            
            [self startACVideo];
        }
        
    }else{
                
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10002";
        }else{
            witnessResult = @"-3"; //连接视频服务器失败
        }
        NSString *msg = [NSString stringWithFormat:@"未找到%@,请重试",[TKDirectVideoModel shareInstance].serviceTipString];
        [self showConnectErrorTip:witnessResult witnessInfo: [NSString stringWithFormat:@"未找到%@",[TKDirectVideoModel shareInstance].serviceTipString] tipTitle:@"视频录制提示" tipDesc:msg];
    }
}

// 用户退出房间消息
- (void) OnAnyChatUserLeaveRoom:(int) dwUserId
{
    NSString *l = [NSString stringWithFormat:@"[%@]离开房间",[AnyChatPlatform GetUserName:dwUserId]];
    
    TKLogInfo(@"%@",l);
    
    if (![TKDirectVideoModel shareInstance].isTransBufferMsg) {
        
        if (tkUdTimer) {//暂停上下行获取定时器
            [tkUdTimer setFireDate:[NSDate distantFuture]];
        }

        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10004";
        }else{
            witnessResult = @"-7";//坐席连接异常
        }
        [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"%@离开房间",[TKDirectVideoModel shareInstance].serviceTipString] tipTitle:@"视频录制提示" tipDesc:[NSString stringWithFormat:@"%@视频连接异常，请重试！",[TKDirectVideoModel shareInstance].serviceTipString]];
    }
}

- (void)showACCountDown{
    
    NSMutableArray *onlineUser = [AnyChatPlatform GetOnlineUser];
    
    if (onlineUser.count < 2) {
        if ([TKDirectVideoModel shareInstance].tkCountDown > 0) {
            TKLogInfo(@"show Count Down = %d", [TKDirectVideoModel shareInstance].tkCountDown);
            if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
                
                [TKDirectVideoModel shareInstance].queueSubMsg=@"";
                
                //提示语h5入参调整位置提示语优先级最高
                //针对h5参数类型容错处理
                NSString *queueWaitStaffSubMsg;
                if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                    queueWaitStaffSubMsg=self.requestParams[@"videoTipMsg"][@"queueWaitStaffSubMsg"];
                }else{
                    queueWaitStaffSubMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueWaitStaffSubMsg"];
                }
                if([TKStringHelper isNotEmpty:queueWaitStaffSubMsg]){
                    [TKDirectVideoModel shareInstance].queueSubMsg=queueWaitStaffSubMsg;
                }
                

                //提示语h5入参调整位置提示语优先级最高
                //针对h5参数类型容错处理
                NSString *queueWaitStaffMsg;
                if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                    queueWaitStaffMsg=self.requestParams[@"videoTipMsg"][@"queueWaitStaffMsg"];
                }else{
                    queueWaitStaffMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueWaitStaffMsg"];
                }
                
                if([TKStringHelper isNotEmpty:queueWaitStaffMsg]){
                    [TKDirectVideoModel shareInstance].queueBigMsg=[NSString stringWithFormat:@"%@（%d秒）",queueWaitStaffMsg,[TKDirectVideoModel shareInstance].tkCountDown];
                }else{
                    [TKDirectVideoModel shareInstance].queueBigMsg=[NSString stringWithFormat:@"正在接入%@…（%d秒）",[TKDirectVideoModel shareInstance].serviceTipString,[TKDirectVideoModel shareInstance].tkCountDown];
                }
                [self.delegate changeSmartTwoVideoTipText:[TKDirectVideoModel shareInstance].queueBigMsg  queueLocation:0 currentStatus:TKOpenQueueStatusGetService];
            }
            [TKDirectVideoModel shareInstance].tkCountDown--;
        }else{
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10003";
            }else{
                witnessResult = @"-5";//客服连接视频超时
            }
            NSString *msg=[NSString stringWithFormat:@"%@匹配失败，请重新开始视频见证！",[TKDirectVideoModel shareInstance].serviceTipString];
            [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"%@超时未进入房间",[TKDirectVideoModel shareInstance].serviceTipString] tipTitle:@"视频录制提示" tipDesc:msg];
        }
    }
    else
    {
        if (tkCountDownTime) {
            [tkCountDownTime invalidate];
            tkCountDownTime = nil;
        }
        
    }
}

// 网络断开消息
- (void) OnAnyChatLinkClose:(int) dwErrorCode
{
    TKLogInfo(@"视频连接断开");
    
    if (![TKDirectVideoModel shareInstance].isShowAlert && ![TKDirectVideoModel shareInstance].isTransBufferMsg) {
        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10009";
        }else{
            witnessResult = @"-6";//客户连接异常
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"客户网络断开连接异常" tipTitle:@"视频录制提示" tipDesc:@"视频连接异常，请重试！"];
    }
}

- (void) OnLocalVideoInit:(id)session
{

    AVCaptureVideoPreviewLayer *localVideoSurface = [AVCaptureVideoPreviewLayer layerWithSession: (AVCaptureSession*)session];
    
    localVideoSurface.frame = CGRectMake(0, 0, _contentView.frame.size.width, _contentView.frame.size.height);
    
    localVideoSurface.videoGravity = AVLayerVideoGravityResizeAspectFill;
    
    localVideoSurface.backgroundColor = [UIColor darkGrayColor].CGColor;
    
    [_contentView.layer addSublayer: localVideoSurface];
    
}

- (void) OnLocalVideoRelease:(id)sender
{}

///////////////////// protocol ///////////////////////////

////////////////////文字信息协议
-(void)OnAnyChatTextMsgCallBack:(int)dwFromUserid :(int)dwToUserid :(BOOL)bSecret :(NSString *)lpMsgBuf{
    
    if([lpMsgBuf rangeOfString:@"USR:1000:"].location != NSNotFound){
        
        NSRange userRange = [lpMsgBuf rangeOfString:@"USR:1000:"];
        NSString *msg = [lpMsgBuf substringFromIndex:userRange.location+userRange.length];
        
        [self updateTipViewText:msg];
        
        return;
    }
    
    if([lpMsgBuf rangeOfString:@"USR:1001:"].location !=NSNotFound){
        //风险协议阅读是否展示指令
        NSRange readAgreeRange=[lpMsgBuf rangeOfString:@"USR:1001:"];
        NSString * readJsonString=[lpMsgBuf substringFromIndex:(readAgreeRange.length)];
        NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
        if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
            readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoRead:)]) {
                [self.delegate showSmartTwoVideoRead:readParm];
            }
        }else{

            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
            }
        }

        return;
    }
    
    if ([lpMsgBuf rangeOfString:@"USR:1002:"].location !=NSNotFound) {
        //toast提示
        NSRange toastRange=[lpMsgBuf rangeOfString:@"USR:1002:"];
        NSString *toastString=[lpMsgBuf substringFromIndex:(toastRange.length)];

        if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
            [self.delegate showSmartTwoVideoToast:toastString];
        }
        return;
    }
    
    NSRange userRange = [lpMsgBuf rangeOfString:@"USR:0:"];
    
    if (userRange.length>0) {
        
        NSString *msg = [lpMsgBuf substringFromIndex:userRange.location+userRange.length];
        [self updateTipViewText:[NSString stringWithFormat:@"%@  %@",[TKDirectVideoModel shareInstance].serviceTipString,msg]];
        
    }else{

        [self updateTipViewText:[NSString stringWithFormat:@"%@  %@",[TKDirectVideoModel shareInstance].serviceTipString,lpMsgBuf]];

    }
}


/**
 *  <AUTHOR> 2019年01月24日13:28:43
 *  更新提示语
 *  @return nil
 */
-(void)updateTipViewText:(NSString *)tip{

    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoSeatMessage:)]) {
        [self.delegate showSmartTwoVideoSeatMessage:tip];
    }
}

////////////////////服务器消息协议
-(void)OnAnyChatSDKFilterDataCallBack:(NSData *)lpBuf{
    TKLogInfo(@"收到服务器数据");
    
}

-(void)OnAnyChatTransFileCallBack:(int)dwUserid :(NSString *)lpFileName :(NSString *)lpTempFilePath :(int)dwFileLength :(int)wParam :(int)lParam :(int)dwTaskId{
    TKLogInfo(@"有文件传输...");
}

-(void)OnAnyChatTransBufferCallBack:(int)dwUserid :(NSData *)lpBuf{
    
    if (![TKDirectVideoModel shareInstance].isTransBufferMsg) {
        
        [TKDirectVideoModel shareInstance].isTransBufferMsg = YES;
        
        TKLogInfo(@"transBuffer callback");
        
        NSString *lpMsgBuf=  [[NSString alloc] initWithData:(lpBuf) encoding:NSUTF8StringEncoding];
        
        if (lpMsgBuf) {
            
            NSString *urlDecode = [lpMsgBuf stringByRemovingPercentEncoding];
            
            if (urlDecode) {
                
                lpMsgBuf = urlDecode;
            }
        }
        
        TKLogInfo(@"返回状态:%@", lpMsgBuf);
        
        [TKDirectVideoModel shareInstance].witnessResult = lpMsgBuf;
        
        // 埋点-单向_请求房间_结果
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
        eventDic[@"message"] = lpMsgBuf;
        TKPrivateEventResult result = TKPrivateEventResultNone;
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventVideoWitnessReceiveCMD
                                 progress:TKPrivateEventProgressNone
                                   result:result
                              orientation:TKPrivateVideoOrientationPortrait
                          oneWayVideoType:TKPrivateOneWayVideoTypeTChatSmart
                     prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                 eventDic:eventDic];
        
        NSRange sysRange = [lpMsgBuf rangeOfString:@"SYS:"];
        
        if (sysRange.length > 0) {  //见证返回的透明信息
            
            [TKDirectVideoModel shareInstance].aExit = NO;
            
        }else{ //其它消息
            
        }
        
        [self endACVideo];
        
    }
    
}

-(void)OnAnyChatTransBufferExCallBack:(int)dwUserid :(NSData *)lpBuf :(int)wParam :(int)lParam :(int)dwTaskId{
    TKLogInfo(@"transBuff Ex callback");
    
    if (![TKDirectVideoModel shareInstance].isTransBufferMsg) {
        
        [TKDirectVideoModel shareInstance].isTransBufferMsg = YES;
        
        NSString *lpMsgBuf=  [[NSString alloc] initWithData:(lpBuf) encoding:NSUTF8StringEncoding];
        
        if (lpMsgBuf) {
            
            NSString *urlDecode = [lpMsgBuf stringByRemovingPercentEncoding];
            
            if (urlDecode) {
                
                lpMsgBuf = urlDecode;
            }
        }
        
        TKLogInfo(@"返回状态:%@", lpMsgBuf);
        
        [TKDirectVideoModel shareInstance].witnessResult = lpMsgBuf;
        
        // 埋点-单向_请求房间_结果
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
        eventDic[@"message"] = lpMsgBuf;
        TKPrivateEventResult result = TKPrivateEventResultNone;
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventVideoWitnessReceiveCMD
                                 progress:TKPrivateEventProgressNone
                                   result:result
                              orientation:TKPrivateVideoOrientationPortrait
                          oneWayVideoType:TKPrivateOneWayVideoTypeTChatSmart
                     prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                 eventDic:eventDic];
        
        NSRange sysRange = [lpMsgBuf rangeOfString:@"SYS:"];
        
        if (sysRange.length > 0) {  //见证返回的透明信息
            
            [TKDirectVideoModel shareInstance].aExit = NO;
            
        }else{ //其它消息
            
        }
        
        [self endACVideo];
    }
    
}

///////////////////////状态改变
-(void)OnAnyChatMicStateChg:(int)dwUserId :(BOOL)bGetMic{
    TKLogInfo(@"[%d]的mic状态改变-[%d]",dwUserId,bGetMic);
}

-(void)OnAnyChatCameraStateChg:(int)dwUserId :(int)dwState{
    TKLogInfo(@"[%d]摄像头状态改变为[%d]",dwUserId,dwState);
    
}

-(void)OnAnyChatActiveStateChg:(int)dwUserId :(int)dwState{
    TKLogInfo(@"[%d]状态改变为[%d]",dwUserId,dwState);
}

-(void)OnAnyChatP2PConnectState:(int)dwUserId :(int)dwState{
    switch (dwState) {
        case 0:
            TKLogInfo(@"当前无连接");
            break;
        case 1:
            TKLogInfo(@"TCP-P2P连接建立");
            break;
        case 2:
            TKLogInfo(@"UDP-P2P连接建立");
            break;
        case 3:
            TKLogInfo(@"UDP&TCP连接建立");
            break;
        default:
            break;
    }
}

-(void)OnAnyChatVideoSizeChg:(int)dwUserId :(int)dwWidth :(int)dwHeight{
    TKLogInfo(@"[%d]摄像头尺寸调整为(%d,%d)",dwUserId,dwWidth,dwHeight);
}

//启动anychat视频
- (void)startACVideo{
    // 埋点-双向-视频-开始
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressStart result:TKPrivateEventResultNone eventDic:eventDic];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(addSmartTwoVideoChatView)]) {
        [self.delegate addSmartTwoVideoChatView];
    }

    NSMutableArray *cams = [AnyChatPlatform EnumVideoCapture];
    
    if(cams.count != 0){
        NSString *camName = (NSString*)[cams objectAtIndex:cams.count-1];
        TKLogInfo(@"切换摄像头为:%@",camName);
        [AnyChatPlatform SelectVideoCapture:camName];
    }else{
        TKLogInfo(@"该设备没有摄像头");
    }
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_NETWORK_P2PPOLITIC : 0];
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_LOCALVIDEO_APPLYPARAM :0];
    //本地摄像影像自动旋转
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_LOCALVIDEO_AUTOROTATION :1];
    //远端摄像影像不自动旋转
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_VIDEOSHOW_AUTOROTATION :0];
    // 静音检测
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_VADCTRL : 1];
    // 回音消除
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_ECHOCTRL : 1];
#ifdef  BRAC_SO_AUDIO_ECHOLEVEL
    //音频回声消除水平参数设置 =4（7.2以后）
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_ECHOLEVEL : 4];
#endif
    // 噪音抑制
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_NSCTRL : 1];
    // 自动增益
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_AUDIO_AGCCTRL : 1];
    

    //开启本地硬件
    [AnyChatPlatform SetSDKOptionInt:BRAC_SO_LOCALVIDEO_OVERLAY :1];
    int selfAudioErrorCode=[AnyChatPlatform UserSpeakControl: -1 : YES];
    NSString *logAudioString=[NSString stringWithFormat:@"TKMSG1011:打开本地麦克风情况%d|%@",selfAudioErrorCode,[self getTimeStamp]];
    
    if (selfAudioErrorCode != 0) {
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10002";
        }else{
            witnessResult = @"-3";//连接视频服务器失败
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"打开本地麦克风失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
    }
    

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logAudioString];
    }
    [AnyChatPlatform SetVideoPos: -1 :self : 0 : 0 : 0 : 0];
    int selfVideoErrorCode=[AnyChatPlatform UserCameraControl: -1 : YES];
    NSString *logVideoString=[NSString stringWithFormat:@"TKMSG1010:打开远本地摄像头情况%d|%@",selfVideoErrorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logVideoString];
    }
    
    if (selfVideoErrorCode != 0) {
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10002";
        }else{
            witnessResult = @"-3";//连接视频服务器失败
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"打开本地麦克风失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
    }

    //请求远端硬件
    int seatAudioErrorCode=[AnyChatPlatform UserSpeakControl: [TKDirectVideoModel shareInstance].seatVideoId : YES];
    NSString *logSeatAudioString=[NSString stringWithFormat:@"TKMSG1011:打开远程麦克风情况%d|%@",seatAudioErrorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logSeatAudioString];
    }
 
    [AnyChatPlatform SetVideoPos: [TKDirectVideoModel shareInstance].seatVideoId : _remoteContentView : 0 : 0 : 0 : 0];

    
    
    
    //请求远端硬件
    int seatVideoErrorCode=[AnyChatPlatform UserCameraControl:  [TKDirectVideoModel shareInstance].seatVideoId : YES];
    NSString *logSeatVideoString=[NSString stringWithFormat:@"TKMSG1010:打开远程摄像头情况%d|%@",seatVideoErrorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logSeatVideoString];
    }
    
    //开始见证计时
    if (self.delegate && [self.delegate respondsToSelector:@selector(startSmartTwoVidoeTime)]) {
        [self.delegate startSmartTwoVidoeTime];
    }
    
    tkUdTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(featchACNetworkSpeed) userInfo:nil repeats:YES];
    
    //显示视频画面
    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoChatView)]) {
        [self.delegate showSmartTwoVideoChatView];
    }
}

- (void)featchACNetworkSpeed
{
    int upstream = [AnyChatPlatform QueryUserStateInt:-1 :BRAC_USERSTATE_VIDEOBITRATE]/1000;

    int downstream = [AnyChatPlatform QueryUserStateInt:[TKDirectVideoModel shareInstance].seatVideoId :BRAC_USERSTATE_VIDEOBITRATE]/1000;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(smartTwoVideoNetWorkUpDownTip:)]) {
        [self.delegate smartTwoVideoNetWorkUpDownTip:[NSString stringWithFormat:@"上行:%dKB/s\n下行:%dKB/s",upstream,downstream]];
    }
}

- (void)showConnectErrorTip:(NSString *)witnessResult witnessInfo:(NSString *)witnessInfo tipTitle:(NSString *)tipTitle tipDesc:(NSString *)tipDesc
{
    if (![TKDirectVideoModel shareInstance].isShowAlert) {
        [TKDirectVideoModel shareInstance].isShowAlert = YES;
        
        [TKDirectVideoModel shareInstance].witnessResult = witnessResult;//连接视频服务器失败
        [TKDirectVideoModel shareInstance].witnessInfo=witnessInfo;
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
            [self.delegate alertSmartTwoVideoTip:tipTitle describe:tipDesc cancelBtnTitle:nil takeBtnTitle:@"确定"];
        }
    }
}

//结束视频
- (void)endACVideo{
    
    TKLogInfo(@"end video");
    
    if (tkCountDownTime) {
        [tkCountDownTime invalidate];
        tkCountDownTime = nil;
    }
    if (tkUdTimer) {
        [tkUdTimer invalidate];
        tkUdTimer = nil;
    }
    

    dispatch_async(dispatch_get_main_queue(), ^{

        
        if ([TKDirectVideoModel shareInstance].witRoomId) {
            if (![TKDirectVideoModel shareInstance].isTransBufferMsg) {
                [AnyChatPlatform TransBuffer:-1 :[@"SYS:10002" dataUsingEncoding:NSUTF8StringEncoding]];
            }
            [AnyChatPlatform UserSpeakControl: -1 : NO];
            [AnyChatPlatform UserCameraControl: -1 : NO];
            [AnyChatPlatform UserSpeakControl: [TKDirectVideoModel shareInstance].seatVideoId : NO];
            [AnyChatPlatform UserCameraControl: [TKDirectVideoModel shareInstance].seatVideoId : NO];
            [AnyChatPlatform LeaveRoom:-1];
            [AnyChatPlatform Logout];
            tkAnychat = nil;
            [[NSNotificationCenter defaultCenter] removeObserver:self name:@"ANYCHATNOTIFY" object:nil];
        }
        
    });
    

    if (self.delegate && [self.delegate respondsToSelector:@selector(endSmartTwoVidoe)]) {
        [self.delegate endSmartTwoVidoe];
    }

}
@end
