//
//  TKChatVideoRecordManager.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/4/17.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKChatVideoRecordManager.h"
#import "TKOpenAccountService.h"

#import <TChat/TChatDefine.h>
#import <TChat/TChatErrorCode.h>
#import <TChat/TChatCore.h>

#import <AVFoundation/AVAsset.h>
#import <AVFoundation/AVAssetImageGenerator.h>
#import <AVFoundation/AVTime.h>

@interface TKChatVideoRecordManager()<TKCCNotifyMessageDelegate, TKCCTextMsgDelegate, TKCCTransDataDelegate, TKCCVideoDataDelegate, TKCCAudioDataDelegate, TKCCSnapShotDelegate>
{
    BOOL isTCStartingVideo;
    int tcSeatId ,tcUserId;
    
    dispatch_queue_t _tchatReleaseQueue;
}

@property (nonatomic, readwrite, assign) BOOL isFirstIntTChat;
@property (nonatomic, readwrite, copy) NSString *authKey;   // 视频密码
@property (nonatomic, readwrite, copy) NSString *loginId;   // 登录id
@property (nonatomic, readwrite, copy) NSString *sercurityNo;   // 视频授权号
@property (nonatomic, readwrite, copy) NSString *signalServer;  // 信令服务地址

@property (nonatomic, readwrite, strong) NSString *centerServerHost;    // TChat服务器主机
@property (nonatomic, readwrite, copy) NSString *centerServerPort;  // TChat服务器端口

// 视频录制路径
@property (nonatomic, readwrite, copy) NSString *originalVideoPath; // 从服务器获取的初始路径
@property (nonatomic, readwrite, copy) NSString *finalVideoPath; // 最终录制的视频录制

@property (nonatomic, readwrite, assign) BOOL isDisconnecting; // 正在断开链接

@property (nonatomic, readwrite, strong) TKOpenAccountService *openAccountService; // 请求类

@property (nonatomic, readwrite, assign) int connectTime; // 连接时长
@property (nonatomic, readwrite, assign) BOOL isSeatAudioDataReady; // 坐席音频是否准备就绪
@property (nonatomic, readwrite, strong) NSTimer *timeoutTimer; // 超时定时器
@property (nonatomic, readwrite, assign) BOOL isReceiveStopTTSMsg; // 是否收到语音播报结束回调
@property (nonatomic, readwrite, assign) BOOL isFirstTTS; // 是否第一次TTS
@property (nonatomic, readwrite, assign) float rate; // 播放速率
@property (nonatomic, readwrite, assign) BOOL isFirstReleaseTChat; // 是否第一次释放tchat

@property (nonatomic, readwrite, strong) NSDate *lastEnterBackgroundDate; // 最近进入后台的时间

@property (nonatomic, readwrite, strong) NSTimer *takePicturesTimer; // 超时定时器
//@property (nonatomic, readwrite, strong) NSFileHandle *handle; // 音频写入工具

@property (atomic, readwrite, assign) int requestID; // tts/虚拟人的事件ID
@property (nonatomic, readwrite, strong) NSArray *synthesisArray;   // 是否正在合成多个语音
@property (nonatomic, readwrite, assign) int currentIndex;

@property (nonatomic, readwrite, strong) UIImage *videoFirstImage; // 视频首帧图片
@property (atomic, readwrite, assign) BOOL takeVideoFirstImage; // 拍第一帧照片

@end

@implementation TKChatVideoRecordManager
@synthesize delegate = _delegate;
@synthesize configParam = _configParam;
@synthesize contentView = _contentView;
@synthesize remoteContentView = _remoteContentView;
@synthesize isLandscape = _isLandscape;
@synthesize isFrontCamera = _isFrontCamera;
@synthesize disableMicrophone = _disableMicrophone;

#pragma mark - Init && Dealloc
//- (void)dealloc {
////    TKLogDebug(@"TKChatVideoRecordManager dealloc");
//
//
//}

- (instancetype)initWithConfig:(NSDictionary *)configParam {
    if (self = [super init]) {
        self.configParam = configParam;
        self.isFirstIntTChat = YES;
        self.isFirstTTS = YES;
        self.rate = [configParam getFloatWithKey:@"rate"] == 0 ? 1 : [configParam getFloatWithKey:@"rate"];
        _tchatReleaseQueue = dispatch_queue_create("com.thinkive.TKChatVideoRecordManager.tchatReleaseQueue", DISPATCH_QUEUE_SERIAL);
    }
    
    return  self;
}

#pragma mark - Public Selector
// 请求视频房间号
- (void)bootDevcie:(BOOL)isFirst {
    __weak typeof(self) weakSelf = self;

    [self requestServerRoomWithUrlStr:self.configParam[@"url"] enableVh:NO callBack:^(BOOL success, NSInteger errorNo, NSString * _Nonnull errorMsg) {

        if (success) {

            if (isFirst) [weakSelf initTChatWitness];

            [weakSelf connectServer];
        } else {

            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:NO errorMsg:[NSString stringWithFormat:@"%@(%d)", errorMsg, (int)errorNo]];
            }
        }
    }];
}

/// 断开视频服务器并销毁释放资源
- (void)stopDevice:(BOOL)needRelease {
    
    [self disconnectServer:needRelease];
}

// 请求视频房间号
- (void)requestServerRoomWithUrlStr:(NSString *)urlStr callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback {
    
    [self requestServerRoomWithUrlStr:urlStr enableVh:NO callBack:callback];
}

// 请求视频房间号
- (void)requestServerRoomWithUrlStr:(NSString *)urlStr enableVh:(BOOL)enableVh callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback {
    
    if (self.isDisconnecting == NO) {
        TKLogInfo(@"思迪服务器录制日志：正在申请房间号");
        [self sendRequestToRequestServerRoomWithUrlStr:urlStr enableVh:enableVh callBack:callback];
    } else {
        // 正在断开服务器，不断轮询，直接可以重新连接
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self requestServerRoomWithUrlStr:urlStr enableVh:enableVh callBack:callback];
        });
    }
}

// 发送网络请求获取房间信息
- (void)sendRequestToRequestServerRoomWithUrlStr:(NSString *)urlStr enableVh:(BOOL)enableVh callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback
{
    NSString *url = [NSString stringWithFormat:@"%@", self.configParam[@"url"]];
    NSMutableDictionary *param = [TKOpenAccountService filterRequestParam:self.configParam];
    param[@"funcNo"] = @"********";
    param[@"enable_vh"] = enableVh ? @"1" : @"0";    // 是否申请虚拟人房间号
    
//    [self showLoading];
    
    __weak typeof(self) weakSelf = self;
    [self.openAccountService requestServerRoomWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
            
        NSString *msg = [TKStringHelper isNotEmpty:resultVo.errorInfo] ? resultVo.errorInfo : @"网络异常，请重试";
//            [weakSelf showLoading];
        
        // 埋点-单向_请求房间_结果
        [self sendRequestRoomEvent:resultVo enableVh:enableVh];
        
        if (resultVo.errorNo == 0) {

            NSArray *arr = (NSArray*)resultVo.results;

            if (arr.count > 0) {

                NSDictionary *resReslut = [arr objectAtIndex:0];
                
                // bus服务版本
                if ([resReslut isKindOfClass:NSDictionary.class] && resReslut.allKeys.count > 0) {
                    
                    weakSelf.authKey = resReslut[@"auth_key"];
                    weakSelf.loginId = resReslut[@"login_id"];
                    weakSelf.sercurityNo = resReslut[@"sercurity_no"];
                    weakSelf.signalServer = resReslut[@"signal_server"];
                    weakSelf.originalVideoPath = resReslut[@"video_path"];
                    weakSelf.centerServerHost = resReslut[@"center_server_host"];
                    weakSelf.centerServerPort = resReslut[@"center_server_port"];
                    
                    // 获取h5传入的中心服务器地址
                    NSString *targetCenterServer = [weakSelf.configParam getStringWithKey:@"centerServer"];
                    if ([TKStringHelper isNotEmpty:targetCenterServer]) {
                        NSArray *tempArr = [TKStringHelper string:targetCenterServer splitWith:@":"];
                        if (tempArr.count == 2) {
                            weakSelf.centerServerHost = tempArr.firstObject;
                            weakSelf.centerServerPort = tempArr.lastObject;
                        }
                    }
                    
                    TKLogInfo(@"思迪服务器录制日志：录制文件地址%@, 中心服务器地址%@", weakSelf.originalVideoPath, weakSelf.centerServerHost);
                    
                    if (callback != nil) {
                        callback(YES, resultVo.errorNo, nil);
                    }
                    
                    return;
                }
            }
        }
        
        TKLogInfo(@"思迪服务器录制日志：正在申请房间号结果信息：%@", msg);
        if (callback != nil) {
            callback(NO, resultVo.errorNo, msg);
        }
    }];
}

- (void)sendRequestRoomEvent:(ResultVo *)resultVo enableVh:(BOOL)enableVh
{
    // 埋点-单向_请求房间_成功
    if (resultVo.errorNo == 0) {
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.configParam];
        eventDic[@"errorNo"] = [NSString stringWithFormat:@"%i", 0];
        eventDic[@"event_err"] = resultVo.errorInfo;
        eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeServer];    // 活体类型
        TKPrivateEventResult result = TKPrivateEventResultNone;
        result = resultVo.errorNo == 0 ? TKPrivateEventResultSuccess : result;
        result = resultVo.errorNo != 0 ? TKPrivateEventResultFail : result;
        if (self.isLiveDetect) {    // 服务端活体
            
            [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect
                                 subEventName:TKPrivateSubEventLiveDetectRequestRoom
                                     progress:TKPrivateEventProgressNone
                                       result:result
                                  orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                              oneWayVideoType:TKPrivateOneWayVideoTypeNone
                         prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                     eventDic:eventDic];
        } else {    // 服务端录制 | 数字人录制
            [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                                 subEventName:TKPrivateSubEventOneWayVideoRequestRoom
                                     progress:TKPrivateEventProgressNone
                                       result:result
                                  orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                              oneWayVideoType:enableVh ? TKPrivateOneWayVideoTypeTChatDigitalMan : TKPrivateOneWayVideoTypeTChatSmart
                         prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                     eventDic:eventDic];
        }
    }
}

// 初始化TChat并连接服务器
- (void)initTChatWitness
{
    TKLogInfo(@"思迪服务器录制日志：正在初始化TChat");
    
    int initFlag = [TChatCore InitSDK];
    
//    [self showLoading];
    
    //直接返回失败的时候就不走回调了
    if (initFlag!=0) {
        TKLogInfo(@"思迪服务器录制日志：初始化TChat失败");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
//            [self hideLoading];
            
            NSString *errorMsg = @"初始化视频出错";
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:NO errorMsg:[NSString stringWithFormat:@"%@(%d)", errorMsg, initFlag]];
            }
        });
        return;
    }
    
    [TChatCore shareTChatCore].notifyMsgDelegate = self;
    [TChatCore shareTChatCore].transDataDelegate = self;
    [TChatCore shareTChatCore].textMsgDelegate = self;
//    [TChatCore shareTChatCore].videodataDelegate = self;
//    [TChatCore shareTChatCore].audiodataDelegate = self;
    [TChatCore shareTChatCore].snapshotDelegate = self;
    [TChatCore SetServerAuthPass: [TKStringHelper isNotEmpty:self.configParam[@"serverAuthPass"]] ? self.configParam[@"serverAuthPass"] : @"123456"];
    
    [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_AUDIO_DATA :1]; // 设置音频输出设备
    [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_VIDEO_DATA :1]; // 设置视频输出设备
//    [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_PEER_AUDIO_DATA :1]; // 获取对端音频数据
    
    [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_FPS : 30]; // 设置视频编码帧率
    [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_AUDIO_AEC_LEVEL : -1]; // 关闭单向视频中的回声消除，降低CPU
    [TChatCore SetSDKOptionInt:TKCC_SO_RECONNECT : 0]; // 关闭重连
    [TChatCore SetSDKOptionInt:TKCC_SO_VIDEO_RC_MODES : 0]; ///< 视频码率控制模式（整形；默认为1；0：质量优先；1：码率优先）
    if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :90];
    
    CGFloat videoWidth = 480;
    CGFloat videoHeight = 640;
    [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_WIDTH : [TKStringHelper isNotEmpty:self.configParam[@"videoWidth"]] ? [self.configParam getIntWithKey:@"videoWidth"] : videoWidth]; // 设置视频输出设备
    [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_HEIGHT : [TKStringHelper isNotEmpty:self.configParam[@"videoHeight"]] ? [self.configParam getIntWithKey:@"videoHeight"] :videoHeight]; // 设置视频输出设备

    // 获取h5传入的中心服务器地址
    NSString *targetMediaServer = [self.configParam getStringWithKey:@"mediaServer"];
    if ([TKStringHelper isNotEmpty:targetMediaServer]) {
        [TChatCore SetMediaServer:targetMediaServer];
    }

    isTCStartingVideo = NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(becomeTChatActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(enterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
}


/// 连接服务器
- (void)connectServer {
    
    // 开始连接到正式录制，媒体还没就绪.需要等OnUserAudioDataReady回调。长时间没有收到回调，断开录制
    // 开启定时器检查回调情况，没有回调关闭服务
    [self createTimeoutTimer];
    
    TKLogInfo(@"思迪服务器录制日志：开始连接服务器，添加超时定时器");
    
    [TChatCore Connect:self.centerServerHost :self.centerServerPort.intValue];
}

- (void)createTimeoutTimer
{
    self.timeoutTimer = [NSTimer timerWithTimeInterval:20 target:self selector:@selector(seatAudioReadyOutTimeBreakLink:) userInfo:nil repeats:NO];
    [[NSRunLoop mainRunLoop] addTimer:self.timeoutTimer forMode:NSRunLoopCommonModes];
}

/// 断开视频服务器并销毁释放资源
- (void)disconnectServer:(BOOL)needRelease {
    
    self.isDisconnecting = YES;
    
    if (self.takePicturesTimer) {
        [self.takePicturesTimer invalidate];
        self.takePicturesTimer = nil;
    }
    
    [TChatCore UserVideoControl:-1 :NO];
    [TChatCore UserAudioControl:-1 :NO];
    [TChatCore UserVideoControl:tcSeatId :NO];
    [TChatCore UserAudioControl:tcSeatId :NO];
    [TChatCore StopUserVideo:-1];
    if (self.remoteContentView) [TChatCore StopUserVideo:tcSeatId];

    // 异步处理
    dispatch_async(_tchatReleaseQueue, ^{
        
        if (self.sercurityNo) {
            
            TKLogInfo(@"思迪服务器录制日志：断开TChat服务器");
            
            // 重置
//            [TChatCore EnableSpeaker:NO];
            [TChatCore LeaveRoom];
            [TChatCore Logout];
            
//            self.contentView = nil;
//            self.remoteContentView = nil;
            self.centerServerHost = nil;
            self.centerServerPort = nil;
            self.sercurityNo = nil;
            self.isFirstIntTChat = YES;
            self.isFirstTTS = YES;
            isTCStartingVideo = NO;
            self.isSeatAudioDataReady = NO;
            tcSeatId = 0;
            tcUserId = 0;
            self.requestID = 0;

            if (self.timeoutTimer) {
                [self.timeoutTimer invalidate];
                self.timeoutTimer = nil;
            }
            
            [[NSNotificationCenter defaultCenter] removeObserver:self];
            
            TKLogInfo(@"思迪服务器录制日志：断开TChat服务器完毕");
        }
        
        if (needRelease && self.isFirstReleaseTChat == NO) {
            
            self.isFirstReleaseTChat = YES;
            
            [TChatCore Release];   // 释放资源
            TKLogInfo(@"思迪服务器录制日志：释放TChat完毕");
        }
        
        self.isDisconnecting = NO;
    });
}

- (NSString *)getFullVideoPath {
    
    NSMutableString *tempUrl = [self.configParam[@"url"] mutableCopy];
    if ([TKStringHelper isNotEmpty:[self.configParam getStringWithKey:@"videoPreviewUrl"]]) {
        tempUrl = [[self.configParam getStringWithKey:@"videoPreviewUrl"] mutableCopy];
        // H5传入
        if ([tempUrl containsString:@"?"]) {
            [tempUrl appendFormat:@"&function=play&video_path=%@", _finalVideoPath];
        } else {
            [tempUrl appendFormat:@"?function=play&video_path=%@", _finalVideoPath];
        }
        
    } else { // SDK处理
        NSRange range = [tempUrl rangeOfString:@"servlet"];
        if (range.location != NSNotFound) {
            tempUrl = [[tempUrl substringToIndex:range.location + range.length] mutableCopy];
        }
        
        tempUrl = [NSMutableString stringWithFormat:@"%@/VideoPlaytAction?function=play&video_path=%@", tempUrl, _finalVideoPath];
    }
    
    NSDictionary *dic = self.configParam[@"requestHeaders"];
    if ([dic isKindOfClass:NSDictionary.class]) {
        if ([dic isKindOfClass:NSString.class]) dic = [TKDataHelper jsonToDictionary:(NSString *)dic];
        
        [dic enumerateKeysAndObjectsUsingBlock:^(NSString *key, id obj, BOOL * _Nonnull stop) {
            
            [tempUrl appendString:[NSString stringWithFormat:@"&%@=%@", key, obj]];
        }];
    }
    
    return [TKStringHelper encodeURL:tempUrl];
}

/// 开始录制视频
- (void)startRecord {
    
//    TKLogDebug(@"请求开始录制  start_record");
    
    NSDictionary *dic = @{
        @"type" : @"start_record",
        @"orientation" : self.isLandscape ? @(1) : @(0),  // 0为竖屏  1为横屏
        @"path" : [TKStringHelper isNotEmpty:_originalVideoPath] ? _originalVideoPath : @""};
    [self sendTransBufferDataWith:dic];
    

    // 重新拍图片首帧画面
    _videoFirstImage = nil;
    _takeVideoFirstImage = YES;
    [self takePictures];
}

/// 停止录制视频
- (void)stopRecord {
    
//    TKLogDebug(@"请求停止录制  stop_record");
    
    NSDictionary *dic = @{
        @"type" : @"stop_record"};
    [self sendTransBufferDataWith:dic];
}

/// 语音播报
/// @param text 播报的内容
- (void)syntheticAndPlay:(NSString *)text tipSpeed:(NSString *)tipSpeed {
    
    TKLogInfo(@"语音播报内容%@", text);
    
//    TKLogDebug(@"请求播报  start_tts");
    if ([TKStringHelper isEmpty:text]) {
        [self ttsStopCallBack];
        return;
    }
    
    NSDictionary *dic = @{
        @"type" : @"start_tts",
        @"text" : [TKStringHelper isNotEmpty:text] ? [TKBase64Helper stringWithEncodeBase64String:text] : @"",
        @"role": @(0), // 播报角色；0为女声，1位男声
        @"request_id": @(++self.requestID)}; // request_id 请求ID；同开始播放本地视频（虚拟视频）接口
    [self sendTransBufferDataWith:dic];
    
    // 第一次tts做超时处理
    if (self.isFirstTTS) {
        self.isFirstTTS = NO;
        self.isReceiveStopTTSMsg = NO;
        
        // 从tts播报开始记录，没有收到结束回调就断开视频
        NSTimeInterval time = text.length * 0.3 / self.rate + 7;
        self.timeoutTimer = [NSTimer timerWithTimeInterval:time target:self selector:@selector(ttsOutTimeBreakLink:) userInfo:@{@"time" : @(time)} repeats:NO];
        [[NSRunLoop mainRunLoop] addTimer:self.timeoutTimer forMode:NSRunLoopCommonModes];
        
        TKLogInfo(@"思迪服务器录制日志：首次TTS播报，增加超时定时器");
    }
}

// 播报多段语音
- (void)syntheticAndPlayContents:(NSArray *)texts tipSpeed:(NSString *)tipSpeed {
    TKLogDebug(@"思迪语音合成调试:准备播报%i段语音", (int)texts.count);
    self.currentIndex = 0;
    self.synthesisArray = texts;
    
    TKLogDebug(@"思迪语音合成调试:语音播放开始, text = %@", texts[self.currentIndex]);
    [self syntheticAndPlay:texts[self.currentIndex] tipSpeed:tipSpeed];
}

/// 停止语音播报
- (void)stopSyntheticAndPlay {
    
//    TKLogDebug(@"请求停止播报  stop_tts");
    
    NSDictionary *dic = @{
        @"type" : @"stop_tts"};
    [self sendTransBufferDataWith:dic];
}

/// 虚拟人语音播报
/// @param textOrFileName 播报的内容或播报的文件
/// @param fileSource 文件来源，1:服务器视频文件，2:视频流
- (void)virtualSyntheticAndPlay:(NSString *)text fileName:(NSString *)fileName fileSource:(NSString *)fileSource {
    if ([fileSource isEqualToString:@"1"]) {
        [self virtualPlayVideo:fileName];
    } else if ([fileSource isEqualToString:@"2"]) {
        [self virtualSyntheticAndPlay:text];
    } else {
        [self ttsStopCallBack];
    }
}

/// 停止虚拟人语音播报
- (void)stopVirtualSyntheticAndPlay {
    [self stopVirtualSynthetic];    // 停止虚拟人合成播报
    [self stopVirtualPlayVideo];    // 停止虚拟人播放云端固定音频
}

/// 虚拟人语音合成
/// @param text 播报的内容
- (void)virtualSyntheticAndPlay:(NSString *)text {
    TKLogInfo(@"语音播报内容%@", text);
    
    //    TKLogDebug(@"请求虚拟人播报  start_vh");
    if ([TKStringHelper isEmpty:text]) {
        [self ttsStopCallBack];
        return;
    }
    
    NSDictionary *dic = @{
        @"type" : @"start_vh",
        @"text" : [TKStringHelper isNotEmpty:text] ? [TKBase64Helper stringWithEncodeBase64String:text] : @"",
        @"request_id": @(++self.requestID)// request_id 请求ID；同开始播放本地视频（虚拟视频）接口
        
    };
    [self sendTransBufferDataWith:dic];
    
//    // 第一次tts做超时处理
//    if (self.isFirstTTS) {
//        self.isFirstTTS = NO;
//        self.isReceiveStopTTSMsg = NO;
//
//        // 从tts播报开始记录，没有收到结束回调就断开视频
//        NSTimeInterval time = text.length * 0.3 / self.rate + 7;
//        self.timeoutTimer = [NSTimer timerWithTimeInterval:time target:self selector:@selector(ttsOutTimeBreakLink:) userInfo:@{@"time" : @(time)} repeats:NO];
//        [[NSRunLoop mainRunLoop] addTimer:self.timeoutTimer forMode:NSRunLoopCommonModes];
//    }
}

/// 停止虚拟人语音合成
- (void)stopVirtualSynthetic {
    
//    TKLogDebug(@"请求停止虚拟人播报  stop_vh");
    
    NSDictionary *dic = @{
        @"type" : @"stop_vh"};
    [self sendTransBufferDataWith:dic];
}

/// 虚拟人播报静态文件
/// @param text 播报的内容
- (void)virtualPlayVideo:(NSString *)fileName {
//    TKLogDebug(@"请求播放虚拟人音频  start_play_video");
    
    NSDictionary *dic = @{
        @"type" : @"start_play_video",
        @"name" : [NSString stringWithFormat:@"%@", fileName],
        @"request_id": @(++self.requestID)// request_id 请求ID；同开始播放本地视频（虚拟视频）接口;
    };
    [self sendTransBufferDataWith:dic];
}

/// 停止虚拟人播报静态文件
- (void)stopVirtualPlayVideo {
//    TKLogDebug(@"请求停止播放虚拟人音频  stop_play_video");
    
    NSDictionary *dic = @{
        @"type" : @"stop_play_video"};
    [self sendTransBufferDataWith:dic];
}

/// 开始语音识别
- (void)startRecognize {
    
//    TKLogDebug(@"请求开始识别  start_asr");
    
    NSDictionary *dic = @{
        @"type" : @"start_asr",
        @"request_id": @(++self.requestID)// request_id 请求ID；同开始播放本地视频（虚拟视频）接口;
    };
    [self sendTransBufferDataWith:dic];
}

/// 停止语音识别
- (void)stopRecognize {
//    TKLogDebug(@"请求停止识别  stop_asr");
    
    NSDictionary *dic = @{
        @"type" : @"stop_asr"};
    [self sendTransBufferDataWith:dic];
}

/// 播放音频
/// @param flag 播放音频索引。和后端保持一致
- (void)playVideo:(NSString *)flag {
//    TKLogDebug(@"请求播放音频  playVideo");
    
    NSDictionary *dic = @{
        @"type" : @"start_play",
        @"flag" : [NSString stringWithFormat:@"%@", flag],
        @"request_id": @(++self.requestID)// request_id 请求ID；同开始播放本地视频（虚拟视频）接口;
    };
    [self sendTransBufferDataWith:dic];
}

/// 停止播放音频
- (void)stopPlayVideo {
//    TKLogDebug(@"请求停止播放音频  stopPlayVideo");
    
    NSDictionary *dic = @{
        @"type" : @"stop_play"};
    [self sendTransBufferDataWith:dic];
}

/// 设置静音
- (void)enableMute:(BOOL)mute {
//    TKLogDebug(@"请求停止播放音频  stopPlayVideo");
    
    NSDictionary *dic = @{
        @"type" : @"enable_mute",
        @"mute" : @(mute),  // 静音标志：0表示收音，1表示静音
        @"request_id": @(++self.requestID)
    };
    [self sendTransBufferDataWith:dic];
    
    // 以下指令会自动实现静音、收音功能
    // 静音：播报文本、播报音频、播放视频
    // 收音：开始录制、开始识别、外部驱动（上层主动设置）
}

// 获取视频第一帧
- (UIImage*)getVideoPreViewImage:(NSURL *)path
{
    TKLogInfo(@"思迪播放器日志: 获取视频第一帧图片， path = %@", path);
    
//    AVURLAsset *asset = [[AVURLAsset alloc] initWithURL:path options:nil];
//    AVAssetImageGenerator *assetGen = [[AVAssetImageGenerator alloc] initWithAsset:asset];
//    
//    assetGen.appliesPreferredTrackTransform = YES;
//    CMTime time = CMTimeMakeWithSeconds(0.0, 600);
//    NSError *error = nil;
//    CMTime actualTime;
//    CGImageRef image = [assetGen copyCGImageAtTime:time actualTime:&actualTime error:&error];
//    UIImage *videoImage = [[UIImage alloc] initWithCGImage:image];
//    CGImageRelease(image);
//    
//    return videoImage;
    return self.videoFirstImage;
}


// 获取本地视频第一帧
-(UIImage *)getLocalVideoPreViewImage:(NSString *)filePath
{
    TKLogInfo(@"思迪播放器日志: 获取本地视频第一帧图片， path = %@", filePath);
    
//    NSURL *sourceURL = [NSURL fileURLWithPath:filePath];
//    AVAsset *asset = [AVAsset assetWithURL:sourceURL];
//    AVAssetImageGenerator *imageGenerator = [[AVAssetImageGenerator alloc]initWithAsset:asset];
//    imageGenerator.appliesPreferredTrackTransform = YES;
//    CMTime time = CMTimeMake(0, 1);
//    NSError *error;
//    CGImageRef imageRef = [imageGenerator copyCGImageAtTime:time actualTime:NULL error:&error];
//    UIImage *thumbnail = [UIImage imageWithCGImage:imageRef];
//    CGImageRelease(imageRef);  // CGImageRef won't be released by ARC
//    return thumbnail;
    return self.videoFirstImage;
}
//切换摄像头
-(void)tkSwitchVideoCamera:(BOOL)isFrontCamera{
    if (isFrontCamera) {
        // 改成后置摄像头
        NSString *camName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :0];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :camName];
        [TChatCore StopUserVideo:-1];
        [TChatCore ShowUserVideo:-1 :self.contentView :NO];    // 前置摄像头镜像，后置不用镜像
        TKLogInfo(@"切换摄像头为:%@",camName);
        if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :270];
    }else{
        // 改成前置置摄像头
        NSString *camName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :camName];
        [TChatCore StopUserVideo:-1];
        [TChatCore ShowUserVideo:-1 :self.contentView :YES];    // 前置摄像头镜像，后置不用镜像
        TKLogInfo(@"切换摄像头为:%@",camName);
        if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :90];
    }
}

#pragma mark - Private Selector
- (void)sendTextMessage:(NSString *)message
{
    [TChatCore SendTextMessage:tcSeatId :NO :message];
}

- (void)sendTransBufferDataWith:(NSDictionary *)dic
{
    NSString *cmd = [NSString stringWithFormat:@"h5cmd@%@", [TKDataHelper objectToJson:dic]];
    [TChatCore TransBuffer:tcSeatId :[cmd dataUsingEncoding:NSUTF8StringEncoding]];
}

/**
 *  <AUTHOR> 2019年09月10日14:03:52
 *  App从后台返回前台
 *  @param notif
 */
- (void)becomeTChatActive:(NSNotification *)notif{
    
//    TKLogDebug(@"从后台回到前台时间%.2f", -[self.lastEnterBackgroundDate timeIntervalSinceNow]);
    // 退到后台时长超过1分钟后，关闭视频
    if (-[self.lastEnterBackgroundDate timeIntervalSinceNow] > 60) {
        dispatch_async(dispatch_get_main_queue(), ^{

//            NSString *errorMsg = @"视频录制异常退出";
//            if (self.delegate && [self.delegate respondsToSelector:@selector(disconnectServerRoomDidComplete:errorMsg:)]) {
//                [self.delegate disconnectServerRoomDidComplete:YES errorMsg:errorMsg];
//            }
            if (self.delegate && [self.delegate respondsToSelector:@selector(TChatBecomeActive)]) {
                [self.delegate TChatBecomeActive];
            }
        });
        return;
    }
    
    // 已经初始化TChat完毕才需要处理。初始化完成之前，收到回调不需要处理
    if (self.isFirstIntTChat == NO) {
        
//        [TChatCore UserVideoControl: -1 : NO];
//        [TChatCore UserVideoControl: -1 : YES];
//        
//
//        [TChatCore ShowUserVideo:-1 :self.contentView :self.isFrontCamera];

    }
}

- (void)enterBackground
{
    self.lastEnterBackgroundDate = [NSDate new];
}

// 启动TChat视频
- (void)startTChatVideo {
    // 控制虚拟坐席的音视频
    [TChatCore UserVideoControl:tcSeatId :YES];
    [TChatCore UserAudioControl:tcSeatId :YES];
}

- (void)ttsOutTimeBreakLink:(NSTimer *)timer
{
    if (self.isReceiveStopTTSMsg == NO) {
        
        NSDictionary *userInfo = timer.userInfo;
        NSTimeInterval time = (NSTimeInterval)[userInfo[@"time"] doubleValue];
        TKLogInfo(@"思迪服务器录制日志：请求播报%.2fs没收到播报结束回调，断开视频", time);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            [self.timeoutTimer invalidate];
            self.timeoutTimer = nil;
            
            [self recordErrorCallBack:@"视频录制异常退出(语音播报超时)"];
        });
    } else {
        TKLogInfo(@"思迪服务器录制日志：tts超时了，但是已收到StopTTSMsg");
    }
}

- (void)seatAudioReadyOutTimeBreakLink:(NSTimer *)timer
{
    if (self.isSeatAudioDataReady == NO) {
        TKLogInfo(@"思迪服务器录制日志：进入房间后10s没收到就绪回调，断开视频");

        dispatch_async(dispatch_get_main_queue(), ^{

            NSString *errorMsg = @"初始化视频出错";
//            [self hideLoading];
            
            [self.timeoutTimer invalidate];
            self.timeoutTimer = nil;

            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:NO errorMsg:[NSString stringWithFormat:@"%@(超时)", errorMsg]];
            }

        });
    } else {
        TKLogInfo(@"思迪服务器录制日志：就绪回调超时了，但是已收到isSeatAudioDataReady");
    }
}

- (void)takePictures
{
    [TChatCore Snapshot:-1 :TKCC_SNAPSHOT_FLAGS_DATA :0 :nil];
//    [TChatCore Snapshot:tcSeatId :TKCC_SNAPSHOT_FLAGS_DATA :0 :@""];
    
//    TKLogDebug(@"takePictures result = %i", result);
}

#pragma mark - TKCCNotifyMessageDelegate
// 连接服务器消息
- (void)OnConnect: (BOOL)success : (int)errorCode
{
    if(errorCode == 0){//连接视频服务器成功
        
        TKLogInfo(@"思迪服务器录制日志：连接服务器成功");
        
        NSString *loginId = [NSString stringWithFormat:@"%@", self.loginId];
        [TChatCore Login:loginId : @""];
        
    }else{
        
        TKLogInfo(@"思迪服务器录制日志：连接服务器失败 errorcode:%d",errorCode);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
//            [self hideLoading];
            
            NSString *errorMsg = @"初始化视频出错";
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:NO errorMsg:[NSString stringWithFormat:@"%@(%d)", errorMsg, errorCode]];
            }
        });
    }
}

// 用户登陆消息
- (void)OnLogin: (int)userId : (int)errorCode
{
    if(errorCode == 0){//登录成功
        
        TKLogInfo(@"思迪服务器录制日志：登录成功");
        
        tcUserId = userId;
        [TChatCore EnterRoom:[self.sercurityNo intValue] : self.authKey];
//        [TChatCore EnterRoom:40000 : @"25116bc3ff4910a96548def19b9c60e7"];
    }else{
        
        TKLogInfo(@"思迪服务器录制日志：登录失败， errorCode = %i", errorCode);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
//            [self hideLoading];
            
            NSString *errorMsg = [NSString stringWithFormat:@"初始化视频出错(%d)", errorCode];
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:NO errorMsg:errorMsg];
            }
        });
    }
}
// 用户进入房间消息
- (void) OnEnterRoom: (int)roomId : (int)errorCode
{
    if (errorCode == 0) {//进入房间成功
        
        TKLogInfo(@"思迪服务器录制日志：已进入房间");
         
		// 视频设备
//        NSString *deviceName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
//        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :deviceName];
        [self tkSwitchVideoCamera:false];
        
        // 音频设备
        if (self.disableMicrophone) {
            [TChatCore SetSDKOptionInt:TKCC_SO_ENABLE_NETEQ:0]; // 关闭neteq
            [TChatCore SelectDevice:TKCC_DT_AUDIOCAPTURE :@""]; // 初始化虚拟音频设备，不录制声音
        }
        else {
            [TChatCore SetSDKOptionInt:TKCC_SO_ENABLE_NETEQ:1]; // 关闭neteq
        }
        [TChatCore UserAudioControl:-1 :YES];
        [TChatCore UserVideoControl: -1 : YES];
//            self.isControlAudio = YES; // 标记控制了音频设备
        
        [TChatCore EnableSpeaker:YES];
        
    }else{
        
        TKLogInfo(@"思迪服务器录制日志：用户进入房间失败, errorCode = %i", errorCode);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *errorMsg = @"初始化视频出错";
//            [self hideLoading];
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:NO errorMsg:[NSString stringWithFormat:@"%@(%d)", errorMsg, errorCode]];
            }
        });
    }
}

//// 初始化通道
- (void) OnInitChannel: (int)errorCode
{
    // 可能同时返回两端都是500错误。需要加锁。否则同时disconnect会闪退
    @synchronized (self) {
        if (self.isFirstIntTChat == YES) {

            TKLogInfo(@"思迪服务器录制日志：TChat初始化完成，错误码%i", errorCode);

            self.isFirstIntTChat = NO;

            NSString *errorMsg = @"初始化视频出错";
            if (errorCode != 0) {

                dispatch_async(dispatch_get_main_queue(), ^{
                    
//                    [self hideLoading];
                    
                    if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                        [self.delegate connectServerRoomDidComplete:NO errorMsg:[NSString stringWithFormat:@"%@(%d)", errorMsg, errorCode]];
                    }
                });
            }
        }
    }
}

// 视频数据就绪
- (void) OnUserVideoDataReady: (int)userId : (int)dataInfo{
    
    int ret = dataInfo & 0x0000FFFF;
    TKLogInfo(@"思迪服务器录制日志：视频数据 userId = %i, dataInfo = %i, ret = %i", userId, dataInfo, ret);
    
    //自己视频画面调整占用屏幕大小，以便展示出底层的白色背景
    float  width = (dataInfo & 0xFFFF0000) >> 16;
    float  height   = (dataInfo & 0x0000FFFF);
    TKLogInfo(@"视频调整前宽度：%f|视频调整前高度：%f",width,height);
    __block float videoWidth;
    __block float videoHeight;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (userId == -1) {
            
            CGPoint center = self.contentView.center;

            float aspectRatio = self.contentView.TKHeight / self.contentView.TKWidth;//高除以宽的比例
            float ratioRequirements=height/width;//高除以宽的要求比例
            //全屏等比拉伸
            if (aspectRatio > ratioRequirements) {
                videoHeight = self.contentView.TKHeight;
                videoWidth = videoHeight/ratioRequirements;
            }else{
                videoWidth = self.contentView.TKWidth;
                videoHeight = videoWidth*ratioRequirements;
            }
            [self.contentView setFrameWidth:videoWidth];
            [self.contentView setFrameHeight:videoHeight];
            
            TKLogInfo(@"视频调整后宽度：%f|视频调整后高度：%f",videoWidth,videoHeight);
            self.contentView.center = center;
            [TChatCore StopUserVideo:-1];
            [TChatCore ShowUserVideo:-1 :self.contentView :self.isFrontCamera];
            
            
            // 创建拍照定时器,获取在框检测图片
            self.takePicturesTimer = [NSTimer timerWithTimeInterval:0.2 target:self selector:@selector(takePictures) userInfo:nil repeats:YES];
            [[NSRunLoop mainRunLoop] addTimer:self.takePicturesTimer forMode:NSRunLoopCommonModes];
    
        } else if (userId == tcSeatId) {

            float aspectRatio = self.remoteContentView.TKHeight / self.remoteContentView.TKWidth;//高除以宽的比例
            float ratioRequirements = height / width; //高除以宽的要求比例
            //全屏等比拉伸
            if (aspectRatio < ratioRequirements) {
                videoHeight = self.remoteContentView.TKHeight;
                videoWidth = videoHeight / ratioRequirements;
            }else{
                videoWidth = self.remoteContentView.TKWidth;
                videoHeight = videoWidth * ratioRequirements;
            }
            
            [self.remoteContentView setFrameWidth:videoWidth];
            [self.remoteContentView setFrameHeight:videoHeight];
    //        TKLogInfo(@"视频调整后宽度：%f|视频调整后高度：%f",videoWidth,videoHeight);
            
            // 设置位置
            if (videoWidth >= UISCREEN_HEIGHT * 0.9) {    // 虚拟人框足够高时居中展示，不够高时浮于底部
                // 居中
                self.remoteContentView.TKLeft = (UIApplication.sharedApplication.keyWindow.TKWidth - videoWidth) * 0.5;
                self.remoteContentView.TKBottom = UIApplication.sharedApplication.keyWindow.TKBottom - IPHONEX_BUTTOM_HEIGHT;
            } else {
                // 依附在左下角
                self.remoteContentView.TKLeft = 10;
                self.remoteContentView.TKBottom =UIApplication.sharedApplication.keyWindow.TKHeight-videoHeight-IPHONEX_BUTTOM_HEIGHT;;
            }

            
            [TChatCore ShowUserVideo:tcSeatId :self.remoteContentView :NO];
        }
    });
}

/*
userid:            用户编号，为-1时，表示本地视频
filepath:            快照标志为0，表示文件路径；否则，表示文件Base64数据
errorcode:        错误代码
flags:            快照标志
param:            用户自定义参数（整型）
userstr:            用户自定义参数（字符串）
 */
- (void) OnSnapShotCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)flags : (int)param : (NSString*)userStr {
    
    if (taskId == -1 && errorCode == 0 && flags == TKCC_SNAPSHOT_FLAGS_DATA) {
        @autoreleasepool
        {
            UIImage *image = [UIImage imageWithData:[TKBase64Helper dataWithDecodeBase64String:filePath]];
    //        TKLogDebug(@"image = %@", image);
            
            if (_takeVideoFirstImage == YES) {
                _videoFirstImage = image;
                _takeVideoFirstImage = NO;
                return;
            }

            dispatch_async(dispatch_get_main_queue(), ^{

                if (self.delegate && [self.delegate respondsToSelector:@selector(OnVideoDataCallBack:)]) {
                    [self.delegate OnVideoDataCallBack:image];
                }
            });
        }
    }
}

//// 音频数据就绪
- (void) OnUserAudioDataReady: (int)userId : (int)dataInfo {

    int ret = dataInfo & 0x0000FFFF;
    TKLogInfo(@"思迪服务器录制日志：音频数据 userId = %i, dataInfo = %i, ret = %i", userId, dataInfo, ret);

//    NSString *errorMsg = @"网络异常,请稍侯重试";
    if (userId == tcSeatId) { // 虚拟坐席音频准备就绪，开始录制
        
//        [self hideLoading];

        // 标记就绪
        self.isSeatAudioDataReady = YES;
        // 销毁定时器
        [self.timeoutTimer invalidate];
        self.timeoutTimer = nil;
        
        TKLogInfo(@"思迪服务器录制日志：对端音频就绪，删除超时定时器");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:YES errorMsg:@""];
            }
        });
    }
}

// 离开房间
- (void) OnLeaveRoom: (int)roomId {
    TKLogInfo(@"离开房间, 房间号:%d" ,roomId);
    
}


// 房间在线用户消息
- (void) OnRoomOnlineUser: (int)userNum : (int)roomId
{
    TKLogInfo(@"房间人数:%d,房间号:%d",userNum,roomId);
    
    if (userNum > 2) {
        dispatch_async(dispatch_get_main_queue(), ^{
            
//            [self hideLoading];
            [self.delegate connectServerRoomDidComplete:NO errorMsg:@"初始化视频出错(房间人数超限)"];
            return;
        });
    }
    
    if (userNum >= 2) {
        
        NSMutableArray *onlineUser = [TChatCore GetRoomOnlineUser];//获取房间中在线用户（坐席）
        
        if (onlineUser && onlineUser.count > 0){
            
            for (int i = 0; i< onlineUser.count; i++) {
                
                if ([[onlineUser objectAtIndex:i] intValue] != tcUserId) {

                    tcSeatId = [[onlineUser objectAtIndex:i] intValue];

                    // 控制虚拟坐席音视频设备
                    if (!isTCStartingVideo) {

                        isTCStartingVideo = YES;

                        dispatch_async(dispatch_get_main_queue(), ^{

                            [self startTChatVideo];
                        });

                    }
                    
                    break;
                }
            }            
        }
        
    }
}

// 网络质量
- (void) OnNetQuality: (int)local : (int)qos {
    [self connectServerStautsCallBack:qos];
}

////有新用户（坐席）进入房间消息
//- (void) OnUserEnterRoom: (int)userId
//{
//    NSString *l = [NSString stringWithFormat:@"坐席[%@]进入房间",[TChatCore GetUserStateString:userId :TKCC_USERSTATE_NICKNAME]];
//
//    TKLogInfo(@"%@", l);
//
//    if (!isTCStartingVideo) {
//
//        tcSeatId = userId;
//
//        isTCStartingVideo = YES;
//
//        dispatch_async(dispatch_get_main_queue(), ^{
//
//            [self startTChatVideo];
//        });
//    }
//}

//-(void)OnLeaveRoom:(int)roomId
//{
//    TKLogInfo(@"退出房间回调:roomId-%d",roomId);
//}

-(void)OnUserAudioCtl:(int)userId :(int)param
{
    // 高 16 位表示控制动作，0 表示关闭，1 表示打开；
    // 低 16 位表示错误代码，0 表示成功，否则，表示失败。
    int ctl = (param & 0xFFFF0000) >> 16;
    int ret = param & 0x0000FFFF;
    TKLogInfo(@"用户音频控制回调,userId:%d ret:%d ctl:%d", userId, ret, ctl);
    
    // TKLogDebug(@"volumeChange OnUserAudioCtl");
}

-(void)OnUserVideoCtl:(int)userId :(int)param
{
    // 高 16 位表示控制动作，0 表示关闭，1 表示打开；
    // 低 16 位表示错误代码，0 表示成功，否则，表示失败。
//    var ctl = (param & 0xFFFF0000) >> 16;
    int ret = param & 0x0000FFFF;
    TKLogInfo(@"用户视频控制回调,userId:%d ret:%d", userId, ret);
}


// 用户退出房间消息
//- (void) OnUserLeaveRoom: (int)userId
//{
//    NSString *l = [NSString stringWithFormat:@"[%@]离开房间",[TChatCore GetUserStateString:userId :TKCC_USERSTATE_NICKNAME]];
//
//    TKLogInfo(@"%@",l);
//}

// 网络断开消息
- (void)OnLinkClose: (int)errorCode
{
    TKLogInfo(@"视频连接断开, errorCode = %i", errorCode);
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        NSString *errorMsg = @"视频录制异常退出";
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(disconnectServerRoomDidComplete:errorMsg:)]) {
            [self.delegate disconnectServerRoomDidComplete:YES errorMsg:[NSString stringWithFormat:@"%@(%d)", errorMsg, errorCode]];
        }
    });
    
}

// 网络码率
- (void) OnNetBitrate: (int)sendBps : (int)recvBps
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(OnNetBitrate:recvBps:)]) {
            [self.delegate OnNetBitrate:sendBps recvBps:recvBps];
        }
    });
    
}

// 音频数据中断
- (void) OnAudioInterrupt: (int)wparam : (int)lparam {
    TKLogInfo(@"OnAudioInterrupt");
}

// 视频数据中断
- (void) OnVideoInterrupt: (int)wparam : (int)lparam {
    TKLogInfo(@"OnVideoInterrupt");
}


//#pragma mark - TKCCTextMsgDelegate
//////////////////////文字信息协议
//- (void) OnTextMessageCallBack: (int)fromUserId : (int)toUserId : (BOOL)secret : (NSString*)msgBuf{
//
//    dispatch_async(dispatch_get_main_queue(), ^{
//        NSRange userRange = [msgBuf rangeOfString:@"USR:0:"];
//        if (userRange.length>0) {
////            NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
////            [self showSeatMsgStr:msg];
//        }else{
////            [self showSeatMsgStr:msgBuf];
//        }
//    });
//
//}

#pragma mark - TKCCTransDataDelegate
- (void) OnTransBufferCallBack: (int)userId : (NSData*)buf{
    
    NSString *lpMsgBuf =  [[NSString alloc] initWithData:(buf) encoding:NSUTF8StringEncoding];
    TKLogInfo(@"来自%d的透明通道消息:%@", userId,lpMsgBuf);
    
    NSRange sysRange = [lpMsgBuf rangeOfString:@"h5ret@"];
    
    if (sysRange.length > 0) {  //见证返回的透明信息
        
        NSString *resultStr = [TKStringHelper subStringWith:lpMsgBuf fromIndex:sysRange.location + sysRange.length count:lpMsgBuf.length - sysRange.location - sysRange.length];
        NSDictionary *dic = [TKDataHelper jsonToDictionary:resultStr];
        
        NSString *request_id = [dic getStringWithKey:@"request_id"];
        NSString *type = dic[@"type"];
        
        NSTimeInterval delayTime = 0;
        // 部分回调需要延迟处理
        if ([type isEqualToString:@"tts_stop"] || [type isEqualToString:@"vh_stop"]) {
            delayTime = 0.5;
        }
        
        // 处理事件
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 若已申请新请求，过滤之前的请求回调
            if ([TKStringHelper isNotEmpty:request_id]) {
                
                int requestID = [dic getIntWithKey:@"request_id"];
                if (requestID < self.requestID) {
                    TKLogInfo(@"id是%@的原请求已被丢弃，不需要处理原请求的回调, 最新请求的id是%d", request_id, (int)self.requestID);
                    return;
                }
            }
            
            if ([dic isKindOfClass:NSDictionary.class] && dic.allKeys.count > 0) {
                
                if ([type isEqualToString:@"record_start"]) {
                    int error_code = [dic[@"error_code"] intValue];
                    if (error_code != 0) {
                        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"视频录制异常退出", error_code]];
                    } else {
                        [self startRecordCallBack];
                    }
                    
                } else if ([type isEqualToString:@"record_error"]) {
                    
                    int error_code = [dic[@"error_code"] intValue];
                    [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"视频录制异常退出", error_code]];
                } else if ([type isEqualToString:@"record_stop"]) {
                    
                    NSString *path = dic[@"path"];
                    int error_code = [dic[@"error_code"] intValue];
                    int videoLength = [dic[@"elapse"] intValue];//视频时长
                    int catonLength = [dic[@"caton"] intValue];//视频卡顿时长
                    [self stopRecordCallBack:path errorCode:error_code videoLength:videoLength catonLength:catonLength];
                    
                } else if ([type isEqualToString:@"tts_start"]) {
                    
    //                int requestID = [dic getIntWithKey:@"request_id"];
    //                if (requestID < self.requestID) return; // 原请求已被丢弃，不需要处理原请求的回调

                    [self ttsStartCallBack];
                } else if ([type isEqualToString:@"tts_stop"]) {
                    [self ttsStopCallBack];
                } else if ([type isEqualToString:@"tts_error"]) {
                    
                    int error_code = [dic[@"error_code"] intValue];
                    [self ttsErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"语音请求失败", error_code]];
                } else if ([type isEqualToString:@"asr_start"]) {
                    
    //                int requestID = [dic getIntWithKey:@"request_id"];
    //                if (requestID < self.requestID) return; // 原请求已被丢弃，不需要处理原请求的回调
                    
                    [self asrStartCallBack];
                } else if ([type isEqualToString:@"asr_stop"]) {
                    [self asrStopCallBack];
                } else if ([type isEqualToString:@"asr_error"]) {
                    int error_code = [dic[@"error_code"] intValue];
                    [self asrErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"语音识别失败", error_code]];
                } else if ([type isEqualToString:@"asr_result"]) {
                    
    //                int requestID = [dic getIntWithKey:@"request_id"];
    //                if (requestID < self.requestID) return; // 原请求已被丢弃，不需要处理原请求的回调
                    
                    NSString *result = [TKBase64Helper stringWithDecodeBase64String:dic[@"text"]];
                    [self asrResultCallBack:result];
                } else if ([type isEqualToString:@"net_qos"]) {
                    int quality = [dic getIntWithKey:@"quality"];
                    [self connectServerStautsCallBack:quality];
                } else if ([type isEqualToString:@"vh_start"]) {
                    
    //                int requestID = [dic getIntWithKey:@"request_id"];
    //                if (requestID < self.requestID) return; // 原请求已被丢弃，不需要处理原请求的回调
                    
                    [self virtualTtsStartCallBack];
                } else if ([type isEqualToString:@"vh_stop"]) {
                    [self ttsStopCallBack];
                } else if ([type isEqualToString:@"vh_error"]) {
                    
                    int error_code = [dic[@"error_code"] intValue];
                    [self virtualTtsErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"语音请求失败", error_code]];
                } else if ([type isEqualToString:@"mute_finish"]) {
                    // 静音完成事件
                    BOOL mute = [dic getBoolWithKey:@"mute"];
                    [self muteFinishCallBack:mute];
                }
            }
        });
    }else{ //其它消息
        
    }
}

- (void)muteFinishCallBack:(BOOL)mute
{
    // 目前只有主动开启收音的情况，对声音录制没有强要求，暂不处理回调
//    dispatch_async(dispatch_get_main_queue(), ^{
//
//
//    });
}

- (void)connectServerStautsCallBack:(int)quality
{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerStautsDidChange:statusString:)]) {
            [self.delegate connectServerStautsDidChange:quality statusString:self.statusStringDic[@(quality)]];
        }
    });
}

- (NSDictionary *)statusStringDic
{
    return @{
        // tchat返回的文案
//        @(TKChatVideoRecordNetworkStatusUnknown) : @"未知",
//        @(TKChatVideoRecordNetworkStatusVeryGood) : @"很好",
//        @(TKChatVideoRecordNetworkStatusGood) : @"较好",
//        @(TKChatVideoRecordNetworkStatusBad) : @"较差",
//        @(TKChatVideoRecordNetworkStatusVeryBad) : @"很差",
        // 项目真实使用的文案
        @(TKChatVideoRecordNetworkStatusUnknown) : @"未知", // 额外增加的。这种状态不展示
        @(TKChatVideoRecordNetworkStatusVeryGood) : @"网络正常",
        @(TKChatVideoRecordNetworkStatusGood) : @"网络正常",
        @(TKChatVideoRecordNetworkStatusBad) : @"网络不稳定",
        @(TKChatVideoRecordNetworkStatusVeryBad) : @"网络卡顿",
    };
}

- (void)startRecordCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(recordStartCallBack)]) {
            [self.delegate recordStartCallBack];
        }
    });
}

- (void)stopRecordCallBack:(NSString *)filePath errorCode:(int)errorCode videoLength:(int)videoLength catonLength:(int)catonLength
{
    if (errorCode == 0) {
            
        _finalVideoPath = filePath;
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopCallBack:fullFilePath:videoLenth:catonLength:)]) {
                [self.delegate recordStopCallBack:filePath fullFilePath:[self getFullVideoPath] videoLenth:videoLength catonLength:catonLength]; // 注意这里调用的是get方法
            }
        });
    } else {
        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"视频录制异常退出", errorCode]];
    }
        
    TKLogInfo(@"思迪服务器录制日志：录制结束。errorCode = %i。生成的文件路径 = %@", errorCode, [self getFullVideoPath]);
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        [_handle closeFile];
//        _handle = nil;
//        TKLogDebug(@"--------------------------文件写入完毕");
//    });
}

- (void)recordErrorCallBack:(NSString *)errorMsg
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopErrocCallBack:)]) {
            [self.delegate recordStopErrocCallBack:[NSString stringWithFormat:@"%@", errorMsg]];
        }
    });
}

- (void)ttsStartCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
            [self.delegate speechSynthesisDidStart];
        }
    });
}

- (void)ttsStopCallBack
{
    // 标记接收到tts播报播放结束回调
    self.isReceiveStopTTSMsg = YES;
    
    if (self.synthesisArray.count) {
//        TKLogDebug(@"handleSpeechSynthesisDidPlayDoneWithIndex ttsStopCallBack index = %i, synthesisArray.count = %i",self.currentIndex, self.synthesisArray.count);
        
        // 回调上层
        int index = self.currentIndex;  // 先记录
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDoneWithIndex:synthesisArray:)]) {
                [self.delegate speechSynthesisDidPlayDoneWithIndex:index synthesisArray:self.synthesisArray];
            }
        });
        
        if (self.currentIndex < self.synthesisArray.count - 1) {
            TKLogDebug(@"思迪语音合成调试:多段语音-继续播放下段语音");
            // 播放下一段语音
            self.currentIndex++;
            [self syntheticAndPlay:self.synthesisArray[self.currentIndex] tipSpeed:@""]; // 这里的tipSpeed没有作用
        } else {
            TKLogDebug(@"思迪语音合成调试:多段语音播放结束");
        }
        
    } else {
        TKLogDebug(@"思迪语音合成调试:播放结束");
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
                [self.delegate speechSynthesisDidPlayDone];
            }
        });
    }
}

- (void)ttsErrorCallBack:(NSString *)errorMsg
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
            [self.delegate speechSynthesisDidFail:errorMsg];
        }
    });
}

- (void)asrStartCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidStart)]) {
            [self.delegate speechRecognizeDidStart];
        }
    });
}

- (void)asrErrorCallBack:(NSString *)errorMsg
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidFail:)]) {
            [self.delegate speechRecognizeDidFail:errorMsg];
        }
    });
}

- (void)asrResultCallBack:(NSString *)result
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeOnSliceRecognize:)]) {
            [self.delegate speechRecognizeOnSliceRecognize:result];
        }
    });
}

- (void)asrStopCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidComplete)]) {
            [self.delegate speechRecognizeDidComplete];
        }
    });
}


- (void)virtualTtsStartCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
            [self.delegate speechSynthesisDidStart];
        }
    });
}

- (void)virtualTtsErrorCallBack:(NSString *)errorMsg
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
            [self.delegate speechSynthesisDidFail:errorMsg];
        }
    });
}

#pragma mark - TKCCVideoDataDelegate
////// 实时视频数据
//- (void) OnVideoDataCallBack: (int)userId : (NSData*)buf :
//(int)len : (int)width : (int)height {
//
//}


#pragma mark - TKCCAudioDataDelegate
// 音频数据数据
//- (void) OnAudioDataCallBack: (int)userId : (NSData*)buf : (int)len : (int)channels : (int)samplesPerSec :
//(int)bitPerSample {
//
//    static long totallen = 0;
//
//    if (_handle == nil) {
//        NSString *path = [NSString stringWithFormat:@"%@/tempTChat.pcm", [TKFileHelper documentFolder]];
//        if ([TKFileHelper isFileExists:path] == YES) {
//
//            [TKFileHelper removeFile:path];
//        }
//        [TKFileHelper createFile:path isDirectory:NO];
//        _handle = [NSFileHandle fileHandleForWritingAtPath:path];
//        TKLogDebug(@"----------------文件写入的路径是%@", path);
//    }
//
//    if (userId == tcSeatId) {
//        
//        NSError *error = nil;
//        [_handle writeData:buf error:&error];
//
//        totallen += len;
//        TKLogDebug(@"----------------已写入的文件数据是%ld, error = %@", totallen, error);
//    }
//
//    if (self.delegate && [self.delegate respondsToSelector:@selector(OnAudioDataCallBack:)]) {
//        [self.delegate OnAudioDataCallBack:buf];
//    }
//}

#pragma mark - Setter && Getter
- (TKOpenAccountService *)openAccountService {
    if (_openAccountService == nil) {
        _openAccountService = [TKOpenAccountService new];
    }
    return _openAccountService;
}

@end
