//
//  TKChatVideoRecordManager.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/4/17.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <AVFoundation/AVAsset.h>
#import "TKRecordManagerProtocol.h"

NS_ASSUME_NONNULL_BEGIN


@protocol TKChatVideoRecordManagerDelegate <TKRecordManagerDelegate>

@optional

// tchat回到前台
//- (void)TChatBecomeActive;

/// 视频服务器连接状态回调
// status:    网络质量；0：未知；1：很好；2：较好；3：较差，4：很差
- (void)connectServerStautsDidChange:(TKChatVideoRecordNetworkStatus)status statusString:(NSString *)statusString;

/// 网络速率回调
/// @param sendBps 上行速率
/// @param recvBps 下行速率
- (void)OnNetBitrate:(int)sendBps recvBps:(int)recvBps;

@end


@interface TKChatVideoRecordManager : NSObject<TKRecordManagerProtocol>

/// 代码
@property (nonatomic, readwrite, weak) id<TKChatVideoRecordManagerDelegate> delegate;

/// 数据统计用，是否服务端活体
@property (nonatomic, readwrite, assign) BOOL isLiveDetect;

// 请求视频房间号
- (void)requestServerRoomWithUrlStr:(NSString *)urlStr enableVh:(BOOL)enableVh callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback;
- (void)requestServerRoomWithUrlStr:(NSString *)urlStr callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback;

/// 初始化TChat并连接服务器
- (void)initTChatWitness;

/// 连接视频服务器
- (void)connectServer;

/// 断开视频服务器并销毁释放资源
- (void)disconnectServer:(BOOL)needRelease;

/// 虚拟人语音播报
/// @param text 播报的内容
/// @param text 播报的文件
/// @param fileName 文件来源，1:服务器视频文件，2:视频流
- (void)virtualSyntheticAndPlay:(NSString *)text fileName:(NSString *)fileName fileSource:(NSString *)fileSource;

/// 停止虚拟人语音播报
- (void)stopVirtualSyntheticAndPlay;

@end

NS_ASSUME_NONNULL_END
