//
//  TKReadingView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/7/28.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKReadingView.h"

@interface TKReadingView () <UITextViewDelegate>

@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UITextView *textView;
//@property (nonatomic, strong) UIProgressView *progressView;
@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UIButton *confirmButton;
@property (nonatomic, strong) NSTimer *countdownTimer;
@property (nonatomic, assign) NSInteger countdownTime;
@property (nonatomic, assign) BOOL scrolledToBottom;
@property (nonatomic, assign) BOOL countdownEnd;
@property (nonatomic, assign) int changeReadTextViewWords; // 变色的字数
@property (nonatomic, readwrite, strong) NSTimer *changeReadTextViewOffSetTimer; // testView滚动定时器

@property (nonatomic, readwrite, strong) TKLayerView *layerView;
@property (nonatomic, strong) NSMutableDictionary *requestParam;
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@property (nonatomic, assign) int instructionNo;//坐席发过来的指令编号，没有的话就不是双向视频需要向坐席回发消息


@end

@implementation TKReadingView

-(instancetype)initWithFrame:(CGRect)frame param:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        self.backgroundColor=[UIColor clearColor];
        self.requestParam=param;
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2F85FF";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
        self.instructionNo=[self.requestParam getIntWithKey:@"instructionNo"];
        self.isReadAll=NO;
        if([self.requestParam getIntWithKey:@"readAll"]==1){
            self.isReadAll=YES;
        }
        [self setupViews];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupViews];
    }
    
    return self;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    // Background view
    self.backgroundView = [[UIView alloc] initWithFrame:CGRectMake(0, CGRectGetHeight(self.bounds), CGRectGetWidth(self.bounds), IPHONEX_BUTTOM_HEIGHT + 342 / 812.0 * UISCREEN_HEIGHT)];
    self.backgroundView.backgroundColor = [UIColor colorWithWhite:1 alpha:0.9];
    self.backgroundView.hidden = YES;
    [self addSubview:self.backgroundView];

    // Content view
    self.contentView = [[UIView alloc] initWithFrame:self.backgroundView.frame];
    [self addSubview:self.contentView];
    // 设置阴影属性
    self.contentView.layer.shadowColor = [UIColor blackColor].CGColor;  // 阴影颜色
    self.contentView.layer.shadowOpacity = 0.5;  // 阴影透明度
    self.contentView.layer.shadowRadius = 50.0;   // 阴影半径
    self.contentView.layer.shadowOffset = CGSizeMake(0, 3);  // 阴影偏移量
    // 设置左上和右上角为圆角
    UIRectCorner corners = UIRectCornerTopLeft | UIRectCornerTopRight;
    CGFloat cornerRadius = 12.0; // 设置圆角的半径
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.path = [UIBezierPath bezierPathWithRoundedRect:self.contentView.bounds
                                           byRoundingCorners:corners
                                                 cornerRadii:CGSizeMake(cornerRadius, cornerRadius)].CGPath;
    // 创建一个子视图并设置阴影属性
    UIView *subview = [[UIView alloc] initWithFrame:self.contentView.bounds];
    subview.backgroundColor = [UIColor colorWithWhite:1 alpha:0.9];
    subview.layer.mask = maskLayer;
    // 将子视图添加到父视图中
    [self.contentView addSubview:subview];
    
    // title label
    self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 12, CGRectGetWidth(self.bounds), 24)];
    self.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    self.titleLabel.textColor = [TKUIHelper colorWithHexString:@"#333333"];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:self.titleLabel];

    // Progress view
//    self.progressView = [[UIProgressView alloc] initWithFrame:CGRectMake(20, 80, CGRectGetWidth(self.contentView.bounds) - 40, 10)];
//    self.progressView.progressTintColor = [UIColor whiteColor];
//    [self.contentView addSubview:self.progressView];

    


    // Confirm button
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.confirmButton.frame = CGRectMake(16, self.contentView.TKHeight - (IPHONEX_BUTTOM_HEIGHT > 0 ? IPHONEX_BUTTOM_HEIGHT : 10) - 44, CGRectGetWidth(self.contentView.bounds) - 40, 44);
    [self.confirmButton addTarget:self action:@selector(confirmButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.confirmButton];
    [self.confirmButton setTitle:[NSString stringWithFormat:@"%@", self.readPromptBtnTitle] forState:UIControlStateNormal];
    
    // Close button
    if([TKStringHelper isNotEmpty:self.requestParam[@"cancel"]]){
        float btnWidth=(self.contentView.TKWidth-40)/2.0f;
        self.closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        self.closeButton.frame = CGRectMake(10, self.confirmButton.TKTop, btnWidth, 44);
        [self.closeButton setTitle:self.requestParam[@"cancel"] forState:UIControlStateNormal];
        [self.closeButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        
        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [self.closeButton setTitleColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]] forState:UIControlStateNormal];
            [self.closeButton setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"] alpha:0.05f]];
        }else{
            [self.closeButton setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
            [self.closeButton setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
        }
        
        [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
        self.closeButton.layer.cornerRadius = self.closeButton.TKHeight / 2.0f;
        [self.contentView addSubview:self.closeButton];
        
        [self.confirmButton setTKWidth:btnWidth];
        [self.confirmButton setTKLeft:self.closeButton.TKRight+20];
    }

    
    
    CGFloat testViewTop = 48;
    self.textView = [[UITextView alloc] initWithFrame:CGRectMake(16, testViewTop, CGRectGetWidth(self.contentView.bounds) - 16 * 2, self.confirmButton.TKTop - 10 - testViewTop)];
    self.textView.textColor = [TKUIHelper colorWithHexString:@"#333333"];
    self.textView.backgroundColor = [UIColor clearColor];
    self.textView.font = [UIFont fontWithName:@"PingFang SC" size:18];
    self.textView.showsVerticalScrollIndicator = YES;
    self.textView.indicatorStyle = UIScrollViewIndicatorStyleDefault;
    self.textView.delegate = self;
    self.textView.editable = NO;
    [self.contentView addSubview:self.textView];
    
    // 检测是否到底，到底则按钮可点击
    [self enableBtn:self.confirmButton isEnable:[self checkScrollViewScrollToBottom:self.textView] || self.countdownEnd];
    
    self.TKHeight = self.backgroundView.TKHeight;
    self.TKTop = UISCREEN_HEIGHT - self.TKHeight;
}

- (void)showWithContent:(NSString *)content countdownTime:(NSInteger)countdownTime type:(TKReadingType)type readTitle:(NSString *)readTitle readConfirmBtnTitle:(NSString *)readConfirmBtnTitle  oneWordSpeed:(NSString *)oneWordSpeed {
    
    self.type = type;
    
    if ([TKStringHelper isNotEmpty:readTitle]) self.readTitle = readTitle;
    self.titleLabel.text = self.readTitle;
    
//    self.textView.text = content;
    content = [TKCommonUtil switchLabelToSpan:content];
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:content textColor:@"#333333"];
    self.textView.attributedText = attStr;
    // 先暂停滚动
    [self stopTextViewScroll];
    
    self.countdownTime = countdownTime;
    
    if ([TKStringHelper isNotEmpty:readConfirmBtnTitle]) self.readConfirmBtnTitle = readConfirmBtnTitle;
    [self.confirmButton setTitle:[NSString stringWithFormat:@"%@(%ds)", self.readConfirmBtnTitle, countdownTime] forState:UIControlStateNormal];
    
    [self enableBtn:self.confirmButton isEnable:NO];

    // Reset progress view and scrolled flag
//    self.progressView.progress = 0;
    self.scrolledToBottom = NO;
    self.countdownEnd = NO;
    
    // 开始滚动
    float durationTime = 0.19f;  // 0.19s/1个字
    if (countdownTime > 0) {
        
        durationTime = countdownTime * 1.0f / attStr.length;
    } else if ([TKStringHelper isNotEmpty:oneWordSpeed]) {
    
        durationTime = oneWordSpeed.floatValue;    // 优先使用外层配置的
    }
    self.wordSpeedDuration = durationTime;

    // Show the view
//    self.backgroundView.hidden = NO;
    [UIView animateWithDuration:0.3 animations:^{
        self.contentView.frame = CGRectMake(0, CGRectGetHeight(self.bounds) - self.contentView.TKHeight, CGRectGetWidth(self.bounds), self.contentView.TKHeight);
    } completion:^(BOOL finished) {
        if(self.autoScroll){
            // 开始滚动
            [self changeReadTextViewOffSet];
        }

    }];
    
    // Start countdown
    [self startCountdownTimer];
}



- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString
{
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)self.textView.font.pointSize, colorString, text];
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

- (void)stopTextViewScroll
{
    self.changeReadTextViewWords = 0;
    [self.changeReadTextViewOffSetTimer invalidate];
    self.changeReadTextViewOffSetTimer = nil;
}

/**
@Auther Vie 2021年07月29日13:56:44
改变阅读文本空间显示
*/
-(void)changeReadTextViewOffSet{
   
   // 防止页面不显示的时候，定时器还在反复调用该功能
   if (!self.window) {
       
       [self stopTextViewScroll];
       
//        NSLog(@"changeReadTextViewOffSet timer invalidate");
       return;
   }
   
   //计算是否要滚动换行
   //多久渐变一个字   
   NSTimeInterval repeactTime = 1.0f;
    if (self.wordSpeedDuration <= 0.05) repeactTime = 0.1;    // 滚动速度变大，刷新频率也更新
   int wordPerSecond = ceilf(repeactTime / self.wordSpeedDuration);

   if (self.changeReadTextViewWords < self.textView.attributedText.string.length) {
       // -1是防止到最后一个字再滚动的话会有点显示问题。会一整行滚动一次，又展示
//        NSLog(@"思迪文案滚动动画：repeactTime = %.2f, wordPerSecond = %i, self.changeReadTextViewWords = %i, self.bottomShowLabel.attributedText.string.length = %i", repeactTime, wordPerSecond, self.changeReadTextViewWords, self.textView.attributedText.string.length);
       
       NSRange rang = NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
       [self.textView scrollRangeToVisible:rang];
       
       //计算是否要滚动换行
//        CGSize labelSize=[self.bottomShowLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, MAXFLOAT)];
//        float diffY=labelSize.height-self.bottomShowLabel.TKHeight;
//        if (diffY>0) {
//            NSRange rang=NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
//            [self.bottomShowLabel scrollRangeToVisible:rang];
//        }
       
       self.changeReadTextViewWords = self.changeReadTextViewWords + wordPerSecond;

//        NSLog(@"思迪文案滚动动画：创建定时器");
       if (self.changeReadTextViewOffSetTimer == nil) {
           
           self.changeReadTextViewOffSetTimer = [NSTimer timerWithTimeInterval:repeactTime target:self selector:@selector(changeReadTextViewOffSet) userInfo:nil repeats:YES];
           [[NSRunLoop mainRunLoop] addTimer:self.changeReadTextViewOffSetTimer forMode:NSRunLoopCommonModes];
       }
   } else {
       
//        NSLog(@"思迪文案滚动动画：销毁定时器");
       [self stopTextViewScroll];
   }
}


- (void)closeButtonTapped {
    [self hide];
    
    if(self.instructionNo==1006){
        NSString *msg=[NSString stringWithFormat:@"RET:1006:%@",[TKDataHelper dictionaryToJson:@{@"action":@"cancel",@"taskId":self.requestParam[@"taskId"]}]];
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(msgToServerClicked:)]) {
            [self.delegate msgToServerClicked:msg];
        }
    }else if(self.instructionNo==1007){
        NSString *msg=[NSString stringWithFormat:@"RET:1007:%@",[TKDataHelper dictionaryToJson:@{@"action":@"cancel",@"taskId":self.requestParam[@"taskId"]}]];
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(msgToServerClicked:)]) {
            [self.delegate msgToServerClicked:msg];
        }
    }else{
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(closeBtnDidClicked:)]) {
            [self.delegate closeBtnDidClicked:self];
        }
    }
    

}

- (void)confirmButtonTapped {
    if (![self checkScrollViewScrollToBottom:self.textView] && self.isReadAll) {
        [self.layerView showTip:[NSString stringWithFormat:@"请完整%@", self.type == TKReadingTypeRead ? @"阅读" : @"朗读"] position:TKLayerPosition_Center textColor:@"#ffffff" bgColor:@"#00000099"];
        return;
    }
    
    [self hide];
    if(self.instructionNo==1006){
        NSString *msg=[NSString stringWithFormat:@"RET:1006:%@",[TKDataHelper dictionaryToJson:@{@"action":@"confirm",@"taskId":self.requestParam[@"taskId"]}]];
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(msgToServerClicked:)]) {
            [self.delegate msgToServerClicked:msg];
        }
    }else if(self.instructionNo==1007){
        NSString *msg=[NSString stringWithFormat:@"RET:1007:%@",[TKDataHelper dictionaryToJson:@{@"action":@"confirm",@"taskId":self.requestParam[@"taskId"]}]];
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(msgToServerClicked:)]) {
            [self.delegate msgToServerClicked:msg];
        }
    }else{
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(confirmBtnDidClicked:)]) {
            [self.delegate confirmBtnDidClicked:self];
        }
    }

}

- (void)startCountdownTimer {
    [self stopCountdownTimer];
    
    self.countdownTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(updateCountdown) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.countdownTimer forMode:NSRunLoopCommonModes];
}

- (void)updateCountdown {
    self.countdownTime--;
    if (self.countdownTime > 0) {

        [self.confirmButton setTitle:[NSString stringWithFormat:@"%@(%ds)", self.readConfirmBtnTitle, (int)self.countdownTime] forState:UIControlStateNormal];
        
    } else {
        [self.confirmButton setTitle:[NSString stringWithFormat:@"%@", self.readConfirmBtnTitle] forState:UIControlStateNormal];
        [self stopCountdownTimer];
        self.countdownEnd = YES;
        
//        if (self.type == TKReadingTypeRead) {
//            [self.layerView showTip:@"请完整阅读" position:TKLayerPosition_Center textColor:@"#ffffff" bgColor:@"#00000099"];
//        }
//        else {
//            [self.layerView showTip:@"请完整朗读" position:TKLayerPosition_Center textColor:@"#ffffff" bgColor:@"#00000099"];
//        }

        // 检测是否到底，到底则按钮可点击
        [self enableBtn:self.confirmButton isEnable:[self checkScrollViewScrollToBottom:self.textView]||self.countdownEnd];
        
//        self.confirmButton.enabled = YES;
//        self.confirmButton.backgroundColor = [UIColor redColor];
    }
}

- (void)stopCountdownTimer {
    [self.countdownTimer invalidate];
    self.countdownTimer = nil;

}

- (void)hide {
    self.backgroundView.hidden = YES;
    [UIView animateWithDuration:0.3 animations:^{
        self.contentView.frame = CGRectMake(0, CGRectGetHeight(self.bounds), CGRectGetWidth(self.bounds), self.contentView.TKHeight);
    }];
    [self stopCountdownTimer];
}

- (void)setButtonBackgroundColor:(UIButton *)button alpha:(CGFloat)alpha
{
//    if (button.window) {
        // 修改按钮渐变色
        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [button setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"] alpha:alpha]];
            button.layer.cornerRadius = button.TKHeight / 2.0f;
        }else{
            // 先移除.注意不要把button的layer也移除了
            for (int i = 0; i < button.layer.sublayers.count; i++) {
                CALayer *layer = button.layer.sublayers[i];
                if ([layer isKindOfClass:CAGradientLayer.class]) {
                    [layer removeFromSuperlayer];
                }
            };
            
            // 再添加
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:alpha].CGColor,(id)[TKUIHelper colorWithHexString:@"#5A92FF" alpha:alpha].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = button.bounds;
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius = button.TKHeight / 2.0f;
            [button.layer insertSublayer:btoGradientLayer atIndex:0]; //设置颜色渐变
            button.layer.cornerRadius = button.TKHeight / 2.0f;
        }
//    }
}

/// 设置是否可以点击确认
/// @param isEnable 是否可以点击
- (void)enableBtn:(UIButton *)btn isEnable:(BOOL)isEnable {
    
    btn.enabled = isEnable;
    if (isEnable) {

        [self setButtonBackgroundColor:btn alpha:1.0f];
    } else {
        
        [self setButtonBackgroundColor:btn alpha:0.3f];
    }
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    [self stopTextViewScroll];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self checkScrollViewScrollToBottom:scrollView];
}

- (BOOL)checkScrollViewScrollToBottom:(UIScrollView *)scrollView
{
    CGFloat contentOffsetY = scrollView.contentOffset.y;
    CGFloat contentHeight = scrollView.contentSize.height;
    CGFloat scrollViewHeight = CGRectGetHeight(scrollView.bounds);
    CGFloat bottomOffset = scrollView.contentInset.bottom;

    // Check if scrolled to the bottom
    if (contentOffsetY >= contentHeight - scrollViewHeight - bottomOffset - 25) {
        self.scrolledToBottom = YES;
        // Enable the confirm button when scrolled to the bottom
        [self enableBtn:self.confirmButton isEnable:self.scrolledToBottom && self.countdownEnd];

        
//        NSLog(@"已滚动到底部");
    } else {
//        self.scrolledToBottom = NO;
        // Disable the confirm button when not scrolled to the bottom
//        self.confirmButton.enabled = NO;
//        NSLog(@"未滚动到底部");
    }
    
    return self.scrolledToBottom;
}

#pragma mark - Setter && Getter
- (NSString *)readPromptBtnTitle
{
    if ([TKStringHelper isNotEmpty:_readPromptBtnTitle]) {
        return _readPromptBtnTitle;
    } else {
        return self.type == TKReadingTypeRead ? @"开始阅读" : @"开始朗读";
    }
}

- (NSString *)readConfirmBtnTitle
{
    if ([TKStringHelper isNotEmpty:_readConfirmBtnTitle]) {
        return _readConfirmBtnTitle;
    } else {
        return self.type == TKReadingTypeRead ? @"我已阅读" : @"我已朗读";
    }
}

- (NSString *)readTitle
{
    if ([TKStringHelper isNotEmpty:_readTitle]) {
        return _readTitle;
    } else {
        return self.type == TKReadingTypeRead ? @"请完整阅读" : @"请使用普通话朗读";
    }
}

- (TKLayerView *)layerView{
    if (!_layerView) {
        _layerView = [[TKLayerView alloc] initContentView:self withBtnTextColor:@"#ffffff" cancelBtnTextColor:nil withWidth:0 withFont:[UIFont fontWithName:@"PingFang SC" size:16]];
    }
    return _layerView;
}

@end

