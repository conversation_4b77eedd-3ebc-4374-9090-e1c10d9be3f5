//
//  TKBaseVideoRecordEndView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/9/2.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKBaseVideoRecordEndView.h"


#define TK_ONEVIEW_TIP_LABEL_END_COLOR_BLUE  [TKUIHelper colorWithHexString:@"#0354C2"]
#define TK_ONEVIEW_TIP_LABEL_END_COLOR_BLACK  [TKUIHelper colorWithHexString:@"#333333"]
#define TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE  [TKUIHelper colorWithHexString:@"#FFFFFF"]
#define TK_OPEN_MIN(a,b,c) (a<b?(a<c?a:c):(b<c?b:c))

@interface TKBaseVideoRecordEndView ()<TKPlayerToolViewDelegate>
@property (nonatomic, strong) UIImageView *bgImgView;//背景视图
@property (nonatomic, strong) UILabel *elderTitleLabel;//适老化标题





@property (nonatomic, strong) UILabel *secondsLabel;//视频多少秒展示


@property (nonatomic, readwrite, strong) UIView *hudView;   // 蒙层

@property (nonatomic, assign) BOOL isPlay;

@end

@implementation TKBaseVideoRecordEndView
@synthesize endType = _endType;
@synthesize secondsString = _secondsString;
@synthesize videoShowImgView = _videoShowImgView;
@synthesize failureString = _failureString;
@synthesize delegate = _delegate;
@synthesize warningTipLabel = _warningTipLabel;
@synthesize warningBackBtn = _warningBackBtn;
@synthesize mainColorString = _mainColorString;
@synthesize resetBtn = _resetBtn;
@synthesize submitBtn = _submitBtn;
@synthesize isLandscape = _isLandscape;
@synthesize playerControlView = _playerControlView;


-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        
        //移除所有子视图
        [[self subviews] makeObjectsPerformSelector:@selector(removeFromSuperview)];
        [self setBackgroundColor:TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE];
        self.requestParam=param;
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2F85FF";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
        [self viewInit];
    }
    return self;
}

/**
 <AUTHOR>
 @初始化单向视频失败界面
 */
-(void)viewErrorInit:(NSString *)errorMsg{

    [self warningTipString:errorMsg];
    [self addSubview:self.bgImgView];
    [self addSubview:self.backBtn];
    [self addSubview:self.voiceWarningImgView];
    [self addSubview:self.warningTitleTipLabel];
    [self addSubview:self.warningTipLabel];
    [self addSubview:self.warningBackBtn];
}

/**
 <AUTHOR>
 @初始化单向视频正常走完流程结果页面
 */
- (void)viewInit{
    [self addSubview:self.bgImgView];
    [self addSubview:self.backBtn];
    if ([TKOpenViewStyleHelper shareInstance].isElder) {
        [self addSubview:self.elderTitleLabel];
    }
    [self addSubview:self.resetBtn];
    [self addSubview:self.submitBtn];
    [self addSubview:self.videoShowBgView];
    [self addSubview:self.bigTipLabel];
    

    [self.videoShowBgView addSubview:self.playerControlView];
    
    [self addSubview:self.titleLabel];
    [self.videoShowBgView addSubview:self.playVideoBtn];
    [self.videoShowBgView addSubview:self.playTipLabel];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self setButtonBackgroundColor:_warningBackBtn];
    [self setButtonBackgroundColor:_submitBtn];
}

- (void)setButtonBackgroundColor:(UIButton *)button
{
    if (button.window) {
        // 修改按钮渐变色
        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [button setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]]];
        }else{
            // 先移除.注意不要把button的layer也移除了
            for (int i = 0; i < button.layer.sublayers.count; i++) {
                CALayer *layer = button.layer.sublayers[i];
                if ([layer isKindOfClass:CAGradientLayer.class]) {
                    [layer removeFromSuperlayer];
                }
            };
            
            // 再添加
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = button.bounds;
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius = button.TKHeight / 2.0f;
            [button.layer insertSublayer:btoGradientLayer atIndex:0]; //设置颜色渐变
            
            button.layer.cornerRadius = button.TKHeight / 2.0f;
        }
    }
}

#pragma mark TKPlayerToolViewDelegate

//重新播放按钮事件
-(void)replayVideoBtnAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(replayVideo)]) {
        [self.delegate replayVideo];
    }
}

//暂停播放按钮事件
-(void)pauseVideoBtnAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(playVideo)]) {
        [self.delegate playVideo];
    }
}


#pragma mark  set or get
/**
 <AUTHOR>
 @修改播放状态页面
 */
-(void)changePlayStatus:(BOOL)isPlay{
    self.isPlay = isPlay;
    if (!isPlay) {
        [self.playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        self.playTipLabel.text = @"点击预览";
    }else{
        [self.playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_stop.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        self.playTipLabel.text = @"点击暂停";
    }
    
    // 设置剩余的播放时间
    self.secondsString = self.secondsString;
}
/**
 <AUTHOR>
 @结果页类型set方法
 */
-(void)setEndType:(TKOneWayVideoEndType)endType{
    
    //移除所有子视图
    [[self subviews] makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self setBackgroundColor:TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE];
    
    NSString *detailMsg = self.failureString;
    if (endType==TKOneWayVideoEndTypeNormal) {
        [self viewInit];
    }else if(endType==TKOneWayVideoEndTypeNoVoiceError){
        [self viewErrorInit:[TKStringHelper isNotEmpty:detailMsg] ? detailMsg : @"回答不通过，本次视频见证失败，您可重新发起录制。"];
        [self warningTitleTipString:@"回答不通过"];
    }else if(endType==TKOneWayVideoEndTypeWrongAnswerError){
        [self viewErrorInit:[TKStringHelper isNotEmpty:detailMsg] ? detailMsg : @"回答不通过，本次视频见证失败，您可重新发起录制。"];
        [self warningTitleTipString:@"回答不通过"];
    }else if(endType==TKOneWayVideoEndTypeFaceDetectError){
        [self viewErrorInit:[TKStringHelper isNotEmpty:detailMsg] ? detailMsg : @"由于长时间未检测到面部在框，本次视频录制失败，请重新录制。"];
        [self warningTitleTipString:@"未检测到面部在框"];
    }else if(endType==TKOneWayVideoEndTypeFaceCompareError){
        [self viewErrorInit:[TKStringHelper isNotEmpty:detailMsg] ? detailMsg : @"人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。"];
        [self warningTitleTipString:@"人脸识别不通过"];
    }
}

/**
 <AUTHOR>
 @视频展示示例图赋值
 */
- (void)setVideoShowImg:(UIImage *)videoShowImg {
    _videoShowImg=videoShowImg;
    [self.videoShowImgView setImage:videoShowImg];
}

/**
 <AUTHOR>
 @视频展示示例图赋值
 */
-(void)setSecondsString:(NSString *)secondsString{
    //    self.secondsLabel.text=secondsString;
    //    CGSize labelSize=[self.secondsLabel sizeThatFits:CGSizeMake(self.TKWidth, MAXFLOAT)];
    //    float secondsLabelX=self.videoShowBgView.TKWidth-labelSize.width-7;
    //    float secondsLabelY=self.videoShowBgView.TKHeight-labelSize.height-4;
    //    self.secondsLabel.frame=CGRectMake(secondsLabelX, secondsLabelY, labelSize.width, labelSize.height);
        
        _secondsString = secondsString;
        
        int recordTime = [secondsString intValue];
        NSDate *d = [NSDate dateWithTimeIntervalSince1970:recordTime];
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
        if (recordTime/3600 >= 1) {
            [formatter setDateFormat:@"HH:mm:ss"];
        } else {
              [formatter setDateFormat:@"mm:ss"];
        }
        if (self.isPlay) {
            self.playTipLabel.text=[NSString stringWithFormat:@"点击暂停 %@",[formatter stringFromDate:d]] ;
        }else{
            self.playTipLabel.text=[NSString stringWithFormat:@"点击预览 %@",[formatter stringFromDate:d]] ;
        }
}

/// 根据tip更新UI
/// @param tipArr tip数组。格式是：@[@{@"tipImage" : @"", @"tipContent" : @""}];
- (void)updateTipViewWithTipArr:(NSArray *)tipArr {
    
    if (![tipArr isKindOfClass:NSArray.class] || tipArr.count == 0) {
        [self addSubview:self.smallTipLabel];
        return;
    }else{
        [self addSubview:self.smallTipView];
    }

    
    // 先移除
    if (self.smallTipView.subviews.count) {
        [self.smallTipView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    }
    
    NSString *tipImage = nil;
    NSString *tipContent = nil;
    CGFloat x = 0;
    CGFloat y = 0;
    CGFloat width = self.smallTipView.TKWidth;
    CGFloat height = 16;
    for (int i = 0; i < tipArr.count; i++) {
        NSDictionary *dic = tipArr[i];
        if ([dic isKindOfClass:NSDictionary.class]) {
            tipImage = dic[@"tipImage"];
            tipContent = dic[@"tipContent"];
            
            // 创建UI
            TKOpenTipView *tipView = [[TKOpenTipView alloc] initWithFrame:CGRectMake(x, y, width, height)];
            [tipView updateUIWithImage:tipImage title:tipContent];
            [self.smallTipView addSubview:tipView];
            
            y = y + tipView.frame.size.height + 15;
        }
    }
    
    // 重新调整smallTipView高度
    self.smallTipView.TKHeight = y - 15;
    self.smallTipView.TKTop = CGRectGetMinY(self.resetBtn.frame) - self.smallTipView.TKHeight - 40;
    
    // 重新布局
    self.bigTipLabel.TKTop = self.smallTipView.frame.origin.y - 20 -  self.bigTipLabel.TKHeight;
    
    // 重新创建图片控件
    UIImage *image = self.videoShowImgView.image;
    [self.videoShowBgView removeFromSuperview];
    self.videoShowBgView = nil;
    [self.videoShowImgView removeFromSuperview];
    self.videoShowImgView = nil;
    [self.tkPlayerToolView removeFromSuperview];
    self.tkPlayerToolView = nil;
    [self.playTipLabel removeFromSuperview];
    self.playTipLabel = nil;
    [self.playVideoBtn removeFromSuperview];
    self.playVideoBtn = nil;
    [self addSubview:self.videoShowBgView];
    [self.videoShowBgView addSubview:self.playerControlView];
    
    BOOL isHidden = self.playVideoBtn.hidden;
    [self.videoShowBgView addSubview:self.playVideoBtn];
    [self.videoShowBgView addSubview:self.playTipLabel];
    self.videoShowImg = image;
    [self showLoadingVideo:isHidden];
}

/**
 @是否展示正在加载视频。YES-展示；NO-隐藏
 */
- (void)showLoadingVideo:(BOOL)isShow {
    if (isShow) {
        
        self.playVideoBtn.hidden = YES;
        self.playTipLabel.hidden = YES;
    } else {
        self.playVideoBtn.hidden = NO;
        self.playTipLabel.hidden = NO;
    }
}

/**
@Auther Vie 2022年11月10日16:44:19
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
-(void)playTime:(float)currentTime longestTime:(float)longestTime{
    [self.playTipLabel setHidden:YES];
    [self.playVideoBtn setHidden:YES];
    if (!_tkPlayerToolView) {
        [self.videoShowImgView addSubview:self.tkPlayerToolView];
    }else{
        [self.tkPlayerToolView setHidden:NO];
        [self.videoShowImgView bringSubviewToFront:self.tkPlayerToolView];
    }
    
    [self.tkPlayerToolView playTime:currentTime longestTime:longestTime];
}


/**
@Auther Vie 2022年11月10日16:54:16
@暂停播放
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
-(void)stopPlay:(float)currentTime longestTime:(float)longestTime{
    [self.playTipLabel setHidden:NO];
    [self.playVideoBtn setHidden:NO];
    [self.tkPlayerToolView stopPlay:currentTime longestTime:longestTime];
}


#pragma mark  事件
/**
 <AUTHOR>
 @返回点击事件
 */
-(void)backAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(endGoBack)]) {
        [self.delegate endGoBack];
    }
}

/**
 <AUTHOR>
 @播放点击事件
 */
-(void)playAction:(UIButton *)sender{
    
    if (self.delegate&&[self.delegate respondsToSelector:@selector(playVideo)]) {
        [self.delegate playVideo];
    }
}

/**
 <AUTHOR>
 @重试点击事件
 */
-(void)resetAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(endReste)]) {
        [self.delegate endReste];
    }
}

/**
 <AUTHOR>
 @提交点击事件
 */
-(void)submitAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(endSubmit)]) {
        [self.delegate endSubmit];
    }
}
/**
 <AUTHOR>
 @修改错误标题提示
 */
-(void)warningTitleTipString:(NSString *)string{
    self.warningTitleTipLabel.text=string;
}

/**
 <AUTHOR>
 @修改错误提示语
 */
-(void)warningTipString:(NSString *)string{
    self.warningTipLabel.text = string;
    CGSize labelSize=[self.warningTipLabel sizeThatFits:CGSizeMake(self.TKWidth-100, MAXFLOAT)];
    float warningTipLabelX=(self.TKWidth-labelSize.width)/2;
    float warningTipLabelY=self.warningTitleTipLabel.TKBottom+12;
    self.warningTipLabel.frame=CGRectMake(warningTipLabelX, warningTipLabelY, labelSize.width, labelSize.height);
}

#pragma makr  lazyloading
/**
 <AUTHOR> 2019年04月13日12:57:01
 @初始化懒加载背景视图
 @return 背景视图
 */
-(UIImageView *)bgImgView{
    if (!_bgImgView) {
        _bgImgView=[[UIImageView alloc] initWithFrame:self.frame];
    }
    return _bgImgView;
}

/**
 <AUTHOR> 2019年04月13日11:36:47
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            float backBtnWidth=71;
            float backBtnheight=32;
            float backBtnX=20.0f;
            float backBtnY=6+STATUSBAR_HEIGHT;
            _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
            _backBtn.clipsToBounds = YES;
            
            
            UIImage *idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]];

          
            idAreaImg = [idAreaImg imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_backBtn setTintColor:[TKUIHelper colorWithHexString:@"#000000"]];
            
            
            [_backBtn setImage:idAreaImg forState:UIControlStateNormal];
            [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -4, 0, 4)]; // 图片往右偏了，需要往左偏回来

            [_backBtn setBackgroundColor:[UIColor clearColor]];
            _backBtn.layer.cornerRadius = 8.0f;
            [_backBtn setTitle:@"返回" forState:UIControlStateNormal];
            [_backBtn setTitleColor:[TKUIHelper colorWithHexString:@"#000000"] forState:UIControlStateNormal];
        }else{
            float backBtnX=20;
            float backBtnY=6+STATUSBAR_HEIGHT;
            float backBtnWidth=32;
            float backBtnheight=32;
            _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
            
            UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_end_back.png", TK_OPEN_RESOURCE_NAME]];
            [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(4, -2, 4, 4)]; // 图片往右偏了，需要往左偏回来
            
            //设计稿返回按钮颜色固定
            
            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_backBtn setTintColor:[TKUIHelper colorWithHexString:@"#000000"]];
            [_backBtn setImage:img forState:UIControlStateNormal];
        }
        _backBtn.titleLabel.font=[UIFont fontWithName:@"PingFangSC-Medium" size:17];
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}

/**
 <AUTHOR> 2022年10月11日09:31:37
 @初始化懒加载elderTitleLabel
 @return elderTitleLabel
 */
-(UILabel *)elderTitleLabel{
    if (!_elderTitleLabel) {
        float width=96;
        float height=34;
        float x=(self.TKWidth-width)/2.0f;
        float y=STATUSBAR_HEIGHT+2.5;
        _elderTitleLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _elderTitleLabel.text=@"视频见证";
        _elderTitleLabel.backgroundColor=[UIColor clearColor];
        _elderTitleLabel.textColor=[TKUIHelper colorWithHexString:@"#000000"];
        _elderTitleLabel.font=[UIFont fontWithName:@"PingFang SC" size:24];
    }
    return _elderTitleLabel;
}

/**
 <AUTHOR> 2019年04月17日20:28:31
 @初始化懒加载视频展示人像视图背景
 @return 视频展示人像视图背景
 */
-(UIView *)videoShowBgView{
    if (!_videoShowBgView) {
        float videoShowBgViewX=20;
        float videoShowBgViewWidth=self.TKWidth-videoShowBgViewX*2;

        float videoShowBgViewY=self.backBtn.TKBottom+42;
        float videoShowBgViewHeight=self.bigTipLabel.TKTop-videoShowBgViewY-10;
        
        _videoShowBgView=[[UIView alloc] initWithFrame:CGRectMake(videoShowBgViewX, videoShowBgViewY, videoShowBgViewWidth, videoShowBgViewHeight)];
        _videoShowBgView.layer.cornerRadius=8.0f;
        _videoShowBgView.backgroundColor=[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.08];
        
        float imgWidth;
        float imgHeight;
        float aspectRatio=videoShowBgViewHeight / videoShowBgViewWidth;//高除以宽的比例
        float ratioRequirements = 4.0f/3.0f;//高除以宽的要求比例
//        if (self.isLandscape) ratioRequirements = 3.0f / 4.0f;
        if (aspectRatio>ratioRequirements) {
            imgWidth=videoShowBgViewWidth;
            imgHeight=imgWidth* ratioRequirements;
        }else{
            imgHeight=videoShowBgViewHeight;
            imgWidth=imgHeight / ratioRequirements;
        }
        float imgX=(videoShowBgViewWidth-imgWidth) / 2;
        float imgY=(videoShowBgViewHeight-imgHeight) / 2;
        UIImageView *imgView=[[UIImageView alloc] initWithFrame:CGRectMake(imgX, imgY, imgWidth, imgHeight)];
        imgView.layer.cornerRadius = 10.0f;
        imgView.layer.masksToBounds = YES;
        
        UIView *shadowView=[[UIView alloc] initWithFrame:imgView.frame];
        shadowView.backgroundColor =TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE;
        shadowView.layer.cornerRadius=10.0f;

        //添加四个边阴影
        shadowView.layer.shadowColor = [UIColor grayColor].CGColor;//阴影颜色
        shadowView.layer.shadowOffset = CGSizeMake(0, 0);//偏移距离
        shadowView.layer.shadowOpacity = 0.5;//不透明度
        shadowView.layer.shadowRadius = 5.0;//半径
        
        [_videoShowBgView addSubview:shadowView];
        [_videoShowBgView addSubview:imgView];
        [imgView setImage:self.videoShowImg];
        imgView.userInteractionEnabled = true;
        self.videoShowImgView=imgView;
        
    }
    return _videoShowBgView;
}





-(UIButton *)playVideoBtn{
//    if (!_playVideoBtn) {
//        _playVideoBtn=[[UIButton alloc] initWithFrame:CGRectMake(0, 0, 44, 44)];
//        [_playVideoBtn setFrameY:(self.videoShowBgView.TKHeight-44)/2.0f];
//        [_playVideoBtn setFrameX:(self.videoShowBgView.TKWidth-44)/2.0f];
//        [_playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
//        [_playVideoBtn addTarget:self action:@selector(playAction:) forControlEvents:UIControlEventTouchUpInside];
//        _playVideoBtn.layer.zPosition = MAXFLOAT;//总是保持在最上面避免被播放视频的view挡住
//    }
//    return _playVideoBtn;
    
    return nil;
}


/**
 <AUTHOR> 2019年04月26日17:31:53
 @初始化懒加载视频播放按钮底部提示文字
 @return 视频播放按钮底部提示文字
 */
-(UILabel *)playTipLabel{
//    if (!_playTipLabel) {
//        _playTipLabel=[[UILabel alloc] init];
//        _playTipLabel.text = @"点击预览";
//        _playTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
//        _playTipLabel.textColor = [UIColor colorWithWhite:1 alpha:0.77];
//        _playTipLabel.textAlignment=NSTextAlignmentCenter;
//
//        float x=(self.videoShowBgView.TKWidth-self.videoShowImgView.TKWidth)/2.0f;
//        _playTipLabel.frame=CGRectMake(x, 0, self.videoShowImgView.TKWidth, 22);
//        [_playTipLabel setFrameY:self.playVideoBtn.TKBottom+17];
//        _playTipLabel.layer.zPosition = MAXFLOAT;//总是保持在最上面避免被播放视频的view挡住
//    }
//    return _playTipLabel;
    return nil;
}

/**
 <AUTHOR> 2019年04月13日13:40:45
 @初始化懒加载大字的提示语
 @return 大字的提示语
 */
-(UILabel *)bigTipLabel{
    if (!_bigTipLabel) {
        _bigTipLabel=[[UILabel alloc] init];
        _bigTipLabel.text = @"请确认影像和声音正确后提交";
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _bigTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _bigTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
        }
        _bigTipLabel.textColor = TK_ONEVIEW_TIP_LABEL_END_COLOR_BLACK;
        
        CGSize labelSize=[_bigTipLabel sizeThatFits:CGSizeMake(self.TKWidth, MAXFLOAT)];
        float bigTipLabelX=(self.TKWidth-labelSize.width)/2;
        float bigTipLabelY=self.smallTipLabel.TKTop-20-labelSize.height;
        _bigTipLabel.frame=CGRectMake(bigTipLabelX, bigTipLabelY, labelSize.width, labelSize.height);
    }
    return _bigTipLabel;
}

//使用该方法不会模糊，根据屏幕密度计算
- (UIImage *)convertViewToImage:(UIView *)view {
    
    UIImage *imageRet = [[UIImage alloc]init];
    //UIGraphicsBeginImageContextWithOptions(区域大小, 是否是非透明的, 屏幕密度);
    UIGraphicsBeginImageContextWithOptions(view.frame.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    imageRet = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return imageRet;
}
/**
 <AUTHOR> 2019年04月13日13:49:36
 @初始化懒加载小字的提示语
 @return 小字的提示语
 */
-(UILabel *)smallTipLabel{
    if (!_smallTipLabel) {
        _smallTipLabel=[[UILabel alloc] init];
        _smallTipLabel.textAlignment=NSTextAlignmentLeft;
        NSString *textString=@"  头像是否完整\n  语音是否清晰\n  视频里不能出现其他人";
        NSMutableAttributedString * attributedString = [[NSMutableAttributedString alloc] initWithString:textString];
        NSMutableParagraphStyle * paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:8];

        [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, [textString length])];

        [attributedString addAttribute:NSForegroundColorAttributeName value:TK_ONEVIEW_TIP_LABEL_END_COLOR_BLACK range:NSMakeRange(0, textString.length)];
        [attributedString addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:16] range:NSMakeRange(0, textString.length)];
        
        UIView *imgView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, 8, 8)];
        imgView.layer.cornerRadius=4;
        imgView.clipsToBounds=YES;
        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            imgView.backgroundColor=[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]];
        }else{

            imgView.backgroundColor=[TKUIHelper colorWithHexString:@"#1061FF"];
        }
        UIImage *img=[self convertViewToImage:imgView];
        // 创建图片图片附件
        NSTextAttachment *attach = [[NSTextAttachment alloc] init];
        attach.image = img;
        attach.bounds = CGRectMake(0, 1, 8, 8);
        NSAttributedString *attachImg = [NSAttributedString attributedStringWithAttachment:attach];
         
        //将图片插入到合适的位置
        [attributedString insertAttributedString:attachImg atIndex:0];
        [attributedString insertAttributedString:attachImg atIndex:10];
        [attributedString insertAttributedString:attachImg atIndex:20];

        _smallTipLabel.attributedText=attributedString;
        _smallTipLabel.numberOfLines=0;
        CGSize labelSize=[_smallTipLabel sizeThatFits:CGSizeMake(self.TKWidth, MAXFLOAT)];
        float smallTipLabelX=(self.TKWidth-labelSize.width)/2;
        float smallTipLabelY=self.resetBtn.TKTop-11-labelSize.height;

        _smallTipLabel.frame=CGRectMake(smallTipLabelX, smallTipLabelY, labelSize.width, labelSize.height);
    }
    return _smallTipLabel;
}



- (UIView *)smallTipView {
    if (!_smallTipView) {
        CGFloat height = 78;
        _smallTipView = [[UIView alloc] initWithFrame:CGRectMake(CGRectGetMinX(self.videoShowBgView.frame), CGRectGetMinY(self.resetBtn.frame) - height - 11, self.videoShowBgView.TKWidth, height)];
    }
    
    return _smallTipView;
}

/**
 <AUTHOR> 2019年04月13日14:01:51
 @初始化懒加载重新录制按钮
 @return 重新录制按钮
 */
-(UIButton *)resetBtn{
    if (!_resetBtn) {
        float resetBtnX = 15;
        float resetBtnWidth = (self.TKWidth-3*resetBtnX)/2;
        float resetBtnHeight = 44;
        float resetBtnY = self.TKHeight-resetBtnHeight-20-IPHONEX_BUTTOM_HEIGHT;
        _resetBtn=[[UIButton alloc] initWithFrame:CGRectMake(resetBtnX, resetBtnY, resetBtnWidth, resetBtnHeight)];
        
        [_resetBtn setTitle:@"重新录制" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _resetBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _resetBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        


        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [_resetBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]] forState:UIControlStateNormal];
            [_resetBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"] alpha:0.05f]];
        }else{
            [_resetBtn setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
            [_resetBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
        }
        _resetBtn.layer.cornerRadius=resetBtnHeight/2.0f;

 
        [_resetBtn addTarget:self action:@selector(resetAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _resetBtn;
}

/**
 <AUTHOR> 2019年04月13日14:02:47
 @初始化懒加载确认提交按钮
 @return 确认提交按钮
 */
-(UIButton *)submitBtn{
    if (!_submitBtn) {
        float submitBtnX=self.resetBtn.TKLeft*2+self.resetBtn.TKWidth;
        float submitBtnWidth=self.resetBtn.TKWidth;
        float submitBtnHeight=self.resetBtn.TKHeight;
        float submitBtnY=self.resetBtn.TKTop;
        _submitBtn=[[UIButton alloc] initWithFrame:CGRectMake(submitBtnX, submitBtnY, submitBtnWidth, submitBtnHeight)];

        [_submitBtn setTitle:@"确认提交" forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        
        [_submitBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        
        [_submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
        
        _submitBtn.layer.cornerRadius=submitBtnHeight/2.0f;
    }
    return _submitBtn;
}

/**
 <AUTHOR> 2019年04月13日14:55:40
 @初始化懒加载语音识别失败警告图
 @return 语音识别失败警告图
 */
-(UIImageView *)voiceWarningImgView{
    if (!_voiceWarningImgView) {
        float voiceWarningImgViewY=self.backBtn.TKBottom+50;
        float voiceWarningImgViewWidth=120;
        float voiceWarningImgViewX=(self.TKWidth-voiceWarningImgViewWidth)/2;
        _voiceWarningImgView=[[UIImageView alloc] initWithFrame:CGRectMake(voiceWarningImgViewX, voiceWarningImgViewY, voiceWarningImgViewWidth, voiceWarningImgViewWidth)];
        [_voiceWarningImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_voice_warning.png", TK_OPEN_RESOURCE_NAME]]];
    }
    return _voiceWarningImgView;
}

/**
 <AUTHOR> 2019年04月13日15:04:44
 @初始化懒加载识别失败标题
 @return 识别失败标题
 */
-(UILabel *)warningTitleTipLabel{
    if (!_warningTitleTipLabel) {
        float widht=self.TKWidth;
        float height=27;
        float y=self.voiceWarningImgView.TKBottom+34;
        _warningTitleTipLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, y, widht, height)];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _warningTitleTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:26];
        }else{
            _warningTitleTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:24];
        }
        
        _warningTitleTipLabel.textColor = [TKUIHelper colorWithHexString:@"#FF4848"];
        _warningTitleTipLabel.numberOfLines=0;
        _warningTitleTipLabel.textAlignment=NSTextAlignmentCenter;
    }
    return _warningTitleTipLabel;
}

/**
 <AUTHOR> 2019年04月13日15:04:44
 @初始化懒加载语音识别失败提示语
 @return 语音识别失败提示语
 */
-(UILabel *)warningTipLabel{
    if (!_warningTipLabel) {
        _warningTipLabel=[[UILabel alloc] init];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _warningTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
        }else{
            _warningTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        }
        _warningTipLabel.textColor = TK_ONEVIEW_TIP_LABEL_END_COLOR_BLACK;
        _warningTipLabel.numberOfLines=0;
        _warningTipLabel.textAlignment=NSTextAlignmentCenter;
    }
    return _warningTipLabel;
}


/**
 <AUTHOR> 2019年04月13日14:02:47
 @初始化懒加载语音识别失败返回按钮
 @return 语音识别失败返回按钮
 */
-(UIButton *)warningBackBtn{
    if (!_warningBackBtn) {
        float warningBackBtnX=16;
        float warningBackBtnWidth=self.TKWidth-warningBackBtnX*2;
        float warningBackBtnHeight=44;
        float warningBackBtnY=self.warningTipLabel.TKBottom+70;
        _warningBackBtn=[[UIButton alloc] initWithFrame:CGRectMake(warningBackBtnX, warningBackBtnY, warningBackBtnWidth, warningBackBtnHeight)];

        [_warningBackBtn setTitle:@"重新录制" forState:UIControlStateNormal];
        [_warningBackBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _warningBackBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _warningBackBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        _warningBackBtn.layer.cornerRadius=warningBackBtnHeight/2.0f;
        
        [_warningBackBtn addTarget:self action:@selector(resetAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _warningBackBtn;
}


/**
 <AUTHOR> 2019年04月18日09:39:25
 @初始化懒加载视频多少秒展示
 @return 视频多少秒展示
 */
-(UILabel *)secondsLabel{
    if (!_secondsLabel) {
        _secondsLabel=[[UILabel alloc] init];
        _secondsLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _secondsLabel.textColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1/1.0];
    }
    return _secondsLabel;
}

- (void)setPlayerControlView:(TKPlayerControlView *)playerControlView {
    if (_playerControlView != playerControlView) {
        _playerControlView = playerControlView;
        [self.videoShowBgView addSubview:playerControlView];
    }
}


/**
 <AUTHOR> 2022年11月11日09:33:54
 @初始化懒加载视频暂停重播工具视图
 @return 视频暂停重播工具视图
 */
- (UILabel *)titleLabel{
    if (!_titleLabel) {
        
        float width = self.TKWidth;
        float height = 24;
        float x = 0;
        float y = 10 + STATUSBAR_HEIGHT;
        _titleLabel= [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _titleLabel.font =  [UIFont fontWithName:@"PingFang SC" size:16];;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.textColor = [TKUIHelper colorWithHexString:@"#000000" alpha:1];
        _titleLabel.text = @"视频录制";
    }
    
    return _titleLabel;
}


- (UIView *)hudView {
    if (!_hudView) {
        
        //        CGFloat y = self.backBtn.TKBottom + 14;
        //        CGRect frame = CGRectMake(10, y, self.TKWidth - 10 * 2, self.TKHeight - y - (IPHONEX_BUTTOM_HEIGHT > 0 ? IPHONEX_BUTTOM_HEIGHT : 10));
        CGRect frame = CGRectMake(0, 0, self.TKWidth, self.TKHeight);
        _hudView = [[UILabel alloc] initWithFrame:frame];
        _hudView.backgroundColor = [TKUIHelper colorWithHexString:@"#ECF0FE"];
        _hudView.layer.cornerRadius = 13;
        _hudView.clipsToBounds = YES;
    }
    
    return _hudView;
}
    
-(TKPlayerToolView *)tkPlayerToolView{
    if (!_tkPlayerToolView) {
        float x=0;
        float height=52;
        float y=self.videoShowImgView.TKHeight-height;
        float width=self.videoShowImgView.TKWidth;
        _tkPlayerToolView=[[TKPlayerToolView alloc] initWithFrame:CGRectMake(x, y, width, height) requestParams:self.requestParam];
        _tkPlayerToolView.delegate=self;
    }

    return _tkPlayerToolView;
}

- (void)setFailureString:(NSString *)failureString {
    _failureString = failureString;
    
    [self warningTipString:failureString];
}

@end
