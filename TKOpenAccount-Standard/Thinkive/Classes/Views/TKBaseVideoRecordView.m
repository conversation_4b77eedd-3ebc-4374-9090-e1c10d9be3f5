//
//  TKBaseVideoRecordView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/8/31.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKBaseVideoRecordView.h"
#import "TKReadingView.h"

#define TK_ONEVIEW_PIECE_COLOR  [UIColor colorWithRed:0/255.0 green:13/255.0 blue:41/255.0 alpha:0.85/1.0]
#define TK_ONEVIEW_TIP_LABEL_COLOR [TKUIHelper colorWithHexString:@"#3CCDFF"]
#define TK_WAIT_COUNT_DOWN 0.9f
#define BottomShowLabelLineHeight  4.0f

@interface TKBaseVideoRecordView()<TKReadingViewDelegate>


@property (nonatomic, strong) TKLayerView  *layerView;//提示layer
@property (nonatomic, strong) UILabel *waitTipLabel;//开始结束提示文本
//@property (nonatomic, strong) UIView *answerRecordView;//回调问题音量波动图

//@property (nonatomic, strong) UIImageView *answerRecordImgView;//回调问题麦克风图


@property (nonatomic, assign) int recordCountDown;//回答倒计时3秒




//@property (nonatomic, strong) TKGIFImageView *fluctuateGifView;//播放语音时候声波动图


@property (nonatomic, strong) NSTimer *recordDownTimer;
//@property (nonatomic, strong) CAShapeLayer *circularLayer;//回答圆圈旋转效果图层

@property (nonatomic, assign) BOOL badgeViewHidden;//记录红点显示隐藏状态




@property (nonatomic, strong) UITextView *readLable;//阅读文本

@property (nonatomic, readwrite, strong) NSTimer *startAutoRecordTimer; // 开始自动录制定时器，防止不做录制占用时间过长
@property (nonatomic, readwrite, assign) int startAutoRecordCountDown; // 开始自动录制倒计时

@property (nonatomic, readwrite, strong) TKReadingView *readingView;


@end

@implementation TKBaseVideoRecordView
@synthesize boxRect = _boxRect;
@synthesize requestParam;
@synthesize takeBtn = _takeBtn;
@synthesize delegate = _delegate;
@synthesize answerPromptImgBg = _answerPromptImgBg;
@synthesize answerPromptLabel = _answerPromptLabel;
@synthesize nextBtn = _nextBtn;
@synthesize avPreviewView = _avPreviewView;
@synthesize isReadVideo = _isReadVideo;

- (void)dealloc {
    [self stopAutoStartRecordTimer];
}

-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        self.requestParam=param;
        
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2772FE";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
        
        if([TKStringHelper isNotEmpty:param[@"isReadVideo"]]){
            self.isReadVideo=[param[@"isReadVideo"] intValue]==1?true:false;
        }
        
        [self viewInit];
    }
    return self;
}

- (void)setButtonBackgroundColor:(UIButton *)button alpha:(CGFloat)alpha
{
    if (button.window) {
        //        // 修改按钮渐变色
        //        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
        //            [button setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]]];
        //        }else{
        // 先移除.注意不要把button的layer也移除了
        for (int i = 0; i < button.layer.sublayers.count; i++) {
            CALayer *layer = button.layer.sublayers[i];
            if ([layer isKindOfClass:CAGradientLayer.class]) {
                [layer removeFromSuperlayer];
            }
        };
        
        // 再添加
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:alpha].CGColor,(id)[TKUIHelper colorWithHexString:@"#5A92FF" alpha:alpha].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
        btoGradientLayer.frame = button.bounds;
        btoGradientLayer.startPoint = CGPointMake(0, 0.5);
        btoGradientLayer.endPoint = CGPointMake(1, 0.5);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        btoGradientLayer.cornerRadius = button.TKHeight / 2.0f;
        [button.layer insertSublayer:btoGradientLayer atIndex:0]; //设置颜色渐变
        
        button.layer.cornerRadius = button.TKHeight / 2.0f;
        //        }
    }
}


/**
 <AUTHOR> 2019年04月03日09:13:34
 @初始化单向视频默认界面
 */
- (void)viewInit{
    
    [self addSubview:self.boxImgBackgroundView];
    [self addSubview:self.boxImgView];
    [self addSubview:self.topView];
    [self addSubview:self.bottomView];
    [self addSubview:self.leftView];
    [self addSubview:self.rightView];
    if (self.requestParam[@"isShowHeadRect"]&&[self.requestParam[@"isShowHeadRect"] integerValue] == 0) {
        //不需要头像框的UI场景
        [self.boxImgBackgroundView setHidden:YES];
        [self.boxImgView setHidden:YES];
        [self.topView setHidden:YES];
        [self.bottomView setHidden:YES];
        [self.leftView setHidden:YES];
        [self.rightView setHidden:YES];
        [self addSubview:self.topBgView];
        [self addSubview:self.bottomBgView];
    }
    
    
    [self addSubview:self.backBtn];
    
    [self addSubview:self.badgeView];
    [self addSubview:self.recordTimeLabel];
    [self.recordTimeLabel setHidden:YES];//进来先不展示录制倒计时
    [self.badgeView setHidden:YES];//进来先不展示录制倒计时
    self.badgeViewHidden=YES;
    
    //    [self addSubview:self.bottomShowTipView];
    //    [self.bottomShowTipView addSubview:self.bottomShowLabel];
    [self addSubview:self.takeBtn];
    
    self.requestParam[@"readString"]=self.requestParam[@"readString"]?self.requestParam[@"readString"]:@"我自愿开立证券账户。";
    self.colorWordsNum=0;
    
    // 默认隐藏下一步按钮
    [self showNextBtn:NO btnTitle:nil];
    
    [self showTakeRecordBtn:YES];
}


#pragma mark 事件方法

/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
-(void)takeAction:(UIButton *)sender{
    
    [self stopAutoStartRecordTimer];
    
    if(self.isReadVideo){
        //朗读单向界面跳转
        // 开始录制之后，5秒内不可点击完成录制
        [self enableFinishTakeRecord:NO];
        //显示录制倒计时
        _recordTimeLabel.hidden=NO;
        
        if (self.delegate&&[self.delegate respondsToSelector:@selector(takeRecord)]) {
            [self.takeBtn removeFromSuperview];
            [self addSubview:self.endBtn];
            [self.delegate takeRecord];
            [self showReadTextView];
        }
    }else{
        // 智能语音单向界面跳转
        [self addSubview:self.bottomShowTipLineView];
        [self.bottomShowTipLineView addSubview:self.bottomShowTipRecordLineView];
        
        if (self.delegate&&[self.delegate respondsToSelector:@selector(takeRecord)]) {
            [self.takeBtn removeFromSuperview];
            self.takeBtn = nil;
            [self.delegate takeRecord];
        }
    }
}

/**
 @Auther Vie 2020年02月28日10:47:55
 @param sender 结束拍照事件
 */
- (void)endAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(endRecord)]) {
        [self.delegate endRecord];
    }
}


/**
 @Auther Vie 2019年04月08日10:46:51
 
 @param sender 返回按钮点击事件
 */
-(void)backAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
        [self.delegate goBack];
    }
}


/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
-(void)nextAction:(UIButton *)sender{
    
    [self.nextBtn removeFromSuperview];
    self.nextBtn = nil;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(nextAction)]) {
        
        [self.delegate nextAction];
    }
}


/**
 <AUTHOR> 2019年05月23日13:37:30
 @活体完成话术播报等待界面
 */
-(void)liveEndWait{
    
    [self liveEndWait:@"请您保持全脸在人像框内。"];
}

- (void)liveEndWait:(NSString *)str {
    
    [self addSubview:self.bottomShowTipView];
    [self.bottomShowTipView addSubview:self.bottomShowLabel];
    _bottomShowLabel.textContainerInset = UIEdgeInsetsMake(5, 0, 5, 0);
    _bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
    //    self.bottomShowLabel.text=@"非常好，视频录制马上开始。";
    self.bottomShowLabel.text = str;
    
    //文本计算高度
    CGSize lableSize = [self.bottomShowLabel sizeThatFits:CGSizeMake(self.bottomShowTipView.TKWidth, MAXFLOAT)];
    [self.bottomShowLabel setTKHeight:lableSize.height];
    [self.bottomShowTipView setTKHeight:lableSize.height];
    
    //    self.countDownNum=3;
    //    self.countDownLabel.text=[NSString stringWithFormat:@"%ld",(long)self.countDownNum];
    //    self.countDownLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:80];
    //    [self addSubview:self.countDownLabel];
    //    [self performSelector:@selector(countDownChangeView) withObject:nil afterDelay:TK_WAIT_COUNT_DOWN];
}


/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting{
    
    [self liveWarning:warningSting forceDisplay:NO];
}

/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting forceDisplay:(BOOL)forceDisplay {
    
    if (!_faceDectTipView) {
        [self addSubview:self.faceDectTipView];
    }
    
    NSTimeInterval faceDetectInterval = 1.0;
    if ([self.requestParam[@"faceDetectInterval"] doubleValue] > 0) {
        faceDetectInterval = [self.requestParam[@"faceDetectInterval"] doubleValue];
    }
    
    //    CGFloat maxWidth = self.TKWidth - (self.bottomShowTipView.TKLeft) * 2;
    CGFloat maxWidth = self.avPreviewView.TKWidth;
    //    [self.faceDectTipView showWarning:warningSting forceDisplay:forceDisplay displayTime:faceDetectInterval maxWidth:maxWidth y:20];
    [self.faceDectTipView showWarning:warningSting forceDisplay:forceDisplay displayTime:faceDetectInterval maxWidth:maxWidth y:0];
    
    // 边框高亮
//    if ([TKStringHelper isEmpty:warningSting]) {
//        self.avPreviewView.layer.borderWidth = 0;
//    } else {
//        
//        self.avPreviewView.layer.borderColor = [TKUIHelper colorWithHexString:@"#FF4848"].CGColor;
//        self.avPreviewView.layer.borderWidth = 3;
//    }
}


/**
 <AUTHOR> 2019年04月15日15:22:00
 @活体继续识别
 */
- (void)liveContinue:(NSString *)string isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed {
    //    [self.spectrumView stop];
    
    //    [self addSubview:self.answerRecordView];
    self.bottomShowTipView.hidden = [TKStringHelper isEmpty:string];
    
    [self updateTipLabel:[NSString stringWithFormat:@"%@",string] textColor:[self.mainColorString isEqualToString:@"#2772FE"]?@"#51B4FE":self.mainColorString cornerRadius:20.0f isOneLineShow:YES isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed autoScroll:NO];
    self.isFinished=NO;
}


/**
 <AUTHOR> 18:13:29
 @视频录制中播放的语音要修改图界面展示波动图
 */
- (void)startRecorderVideoPlay:(NSString *)questionString isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed autoScroll:(BOOL)autoScroll {
    
    [self.answerPromptLabel removeFromSuperview];
    [self.answerPromptImgBg removeFromSuperview];
    [self.countDownLabel removeFromSuperview];
    self.countDownLabel = nil;
    self.answerPromptLabel = nil;
    self.answerPromptImgBg = nil;
    self.isFinished = YES;
    self.countDownType = TKCountDownTypeUnknown;
    
    self.bottomShowTipView.hidden = [TKStringHelper isEmpty:questionString];
    
    [self updateTipLabel:questionString textColor:[self.mainColorString isEqualToString:@"#2772FE"]?@"#51B4FE":self.mainColorString cornerRadius:20.0f isOneLineShow:NO isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed autoScroll:(BOOL)autoScroll];
    
    [self.serviceGifView setImageByName:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceSpeak.gif", TK_OPEN_RESOURCE_NAME]];
}


/**
 <AUTHOR> 2019年04月26日19:12:30
 @回答完毕（识别到了是或否）
 */
- (void)answered:(BOOL)isAnswered displayTime:(int)displayTime {
    
    self.isFinished=YES;
    
    // 回答了YES
    if (isAnswered) {
        
        // 延迟1s是为了展示语音识别结果
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(displayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            self.answerPromptLabel.hidden = YES;
            self.answerPromptImgBg.hidden = YES;
        });
    }
}


/**
 <AUTHOR> 2020年01月07日19:02:54
 @语音问题播放完成停止波动动画
 */
-(void)playEndVoiceView{
    //    [self.fluctuateGifView removeFromSuperview];
    //    self.fluctuateGifView=nil;
    
}


/**
 <AUTHOR> 2019年04月16日20:09:51
 @语音合成播放完成，修改界面
 */
- (void)playEndView:(int)waitTime prompt:(NSString *)string noAnswerPromptTime:(int)noAnswerPromptTime {
    
    if (waitTime<=0) {
        waitTime=5;
    }
    
    self.answerCount=waitTime;
    self.hasAsrResult = NO;
    [self.serviceGifView setImageByName:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceListen.gif", TK_OPEN_RESOURCE_NAME]];
    [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeAnswer];
    [self.bottomShowTipView addSubview:self.countDownLabel];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
    });
}


/**
 <AUTHOR> 2019年04月26日18:39:08
 @问题回答倒计时
 */
- (void)answerCountDown:(int)waitTime noAnswerPromptTime:(int)noAnswerPromptTime {
    
    __weak typeof(self) weakSelf = self;
    
    if (!self.isFinished) {
        self.answerCount = self.answerCount-1;
        
        noAnswerPromptTime = noAnswerPromptTime <= 0 ? 3 : noAnswerPromptTime;
        if ((waitTime - self.answerCount >= noAnswerPromptTime) && self.hasAsrResult == NO) {
            [self showNoAnswerPrompt];
        }
        
        [self updateCountDownLabelText:self.answerCount countDownType:TKCountDownTypeAnswer];
        
        if (self.answerCount>=3) {
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
            });
            
        }else if(self.answerCount<3&&self.answerCount>0){
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
            });
        }else{
            
            if (self.delegate&&[self.delegate respondsToSelector:@selector(answerCountDownEnd)]) {
                [self.delegate answerCountDownEnd];
            }
            
            //            [self.countDownLabel removeFromSuperview];
        }
    }else{
        
        //        [self.countDownLabel removeFromSuperview];
    }
    
}

- (void)recordLongTime:(int)recordTime startRecord:(BOOL)startRecord {
    NSDate *d = [NSDate dateWithTimeIntervalSince1970:recordTime];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
    if (recordTime/3600 >= 1) {
        [formatter setDateFormat:@"HH:mm:ss"];
    } else {
        [formatter setDateFormat:@"mm:ss"];
    }
    
    if (recordTime!=0) {
        
        [UIView animateWithDuration:0.3 animations:^{
            
            self.badgeView.alpha = 0;
        } completion:^(BOOL finished) {
            if (finished) {
                
                [UIView animateWithDuration:0.3 delay:0.4 options:UIViewAnimationOptionCurveLinear animations:^{
                    
                    self.recordTimeLabel.text = [formatter stringFromDate:d];
                    self.badgeView.alpha = 1;
                } completion:nil];
            }
        }];
    } else {
        self.recordTimeLabel.text = [formatter stringFromDate:d];
        self.badgeView.alpha = 1;
    }
}

/**
 @Auther Vie 2020年02月28日12:48:01
 @param recordTime 当前录制时间
 @param longestTime 最长录制时间
 */
-(void)recordTime:(int)recordTime longestTime:(int)longestTime{
    
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
    
    int remaining = longestTime - recordTime; // 剩余时间
    if (remaining / 3600 >= 1) {
        [formatter setDateFormat:@"HH:mm:ss"];
    } else {
        [formatter setDateFormat:@"mm:ss"];
    }
    
    NSDate *d = [NSDate dateWithTimeIntervalSince1970:remaining];
    
    
    // 大于最短录制时间可以点击完成录制
    if (recordTime >= [self.requestParam[@"shortestTime"] intValue]) {
        [self enableFinishTakeRecord:YES];
    }
    
    if (recordTime!=0) {
        
        [UIView animateWithDuration:0.3 animations:^{
            
            self.badgeView.alpha = 0;
        } completion:^(BOOL finished) {
            if (finished) {
                
                [UIView animateWithDuration:0.3 delay:0.4 options:UIViewAnimationOptionCurveLinear animations:^{
                    
                    self.recordTimeLabel.text = [formatter stringFromDate:d];
                    self.badgeView.alpha = 1;
                } completion:nil];
            }
        }];
    } else {
        self.recordTimeLabel.text = [formatter stringFromDate:d];
        self.badgeView.alpha = 1;
    }
    
}

/**
 @Auther Vie 2020年03月10日14:43:37
 @param showWaitTip 中间准备提示文本
 */
-(void)showWaitTip:(NSString *)string{
    self.waitTipLabel.text=string;
    [self addSubview:self.waitTipLabel];
}

/**
 @Auther Vie 2020年03月10日14:46:52
 @隐藏中间准备提示文本
 */
-(void)hideWaitTip{
    [self.waitTipLabel removeFromSuperview];
    [self.recordTimeLabel setHidden:NO];//展示录制倒计时
    [self.badgeView setHidden:NO];//展示录制倒计时
    self.badgeViewHidden=NO;
}

/// 设置是否可以点击开始录制
/// @param isEnable 是否可以点击
- (void)enableTakeRecord:(BOOL)isEnable {
    
    self.takeBtn.enabled = isEnable;
    if (isEnable) {
        _takeBtn.layer.borderWidth=0.0f;
        _takeBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _takeBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#2772FE":self.mainColorString];
        //        [self setButtonBackgroundColor:self.takeBtn alpha:1.0f];
    } else {
        _takeBtn.layer.borderWidth=1.0f;
        _takeBtn.layer.borderColor=[TKUIHelper colorWithHexString:@"#BDDBFA" alpha:1.0f].CGColor;
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#BDDBFA" alpha:1.0f] forState:UIControlStateNormal];
        //        [self setButtonBackgroundColor:self.takeBtn alpha:0.3f];
    }
}

/// 设置是否可以点击结束录制
/// @param isEnable 是否可以点击
- (void)enableFinishTakeRecord:(BOOL)isEnable {
    self.endBtn.enabled = isEnable;
    if (isEnable) {
        
        
        [_endBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        [_endBtn setBackgroundColor:[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#EA4940":self.mainColorString]];
        _endBtn.layer.borderWidth=0.0f;
    } else {
        
        [_endBtn setTitleColor:[TKUIHelper colorWithHexString:@"#666666"] forState:UIControlStateNormal];
        _endBtn.layer.borderColor=[TKUIHelper colorWithHexString:@"#666666"].CGColor;
        [_endBtn setBackgroundColor:[UIColor clearColor]];
    }
}

/**
 <AUTHOR> 2021年07月08日10:30:58
 @修改提示语问题话术
 @string 文本
 @colorString 文本颜色
 @cornerRadius 背景框圆角
 @flag 是否是播放语音话术（y坐标不一样要调整）
 @flag 是否是html文本
 @return 顶部遮罩层
 */
-(void)updateTipLabel:(NSString *)string textColor:(NSString *)colorString cornerRadius:(CGFloat)cornerRadius isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed autoScroll:(BOOL)autoScroll {
    
    //    NSLog(@"---------oneLineWidth ponitSize = %.2f, string= %@", self.bottomShowLabel.font.pointSize, string);
    string = [TKCommonUtil switchLabelToSpan:string];
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:string textColor:colorString];
    
    // 不重复处理
    if (([self.bottomShowLabel.text isEqualToString:string]
        || [self.bottomShowLabel.attributedText isEqualToAttributedString:attStr]
        || [self.bottomShowLabel.attributedText.string isEqualToString:attStr.string]) && self.changeReadTextViewWords > 0) {
        //        NSLog(@"---------oneLineWidth 传入的string = %@重复,不重复处理", string);
        return;
    }
    
    // 先暂停滚动
    [self stopTextViewScroll];
    
    if (!_serviceBg) {
        [self.bottomShowTipView addSubview:self.serviceBg];
        [self.serviceBg addSubview:self.serviceGifView];
        // 此时文案未更新高度，先不展示
        self.serviceBg.hidden = YES;
        self.serviceGifView.hidden = YES;
    }
    
    self.bottomShowLabel.attributedText = nil;
    self.bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 0, 0, 0); // 旧版UI会设置UIEdgeInsetsMake(5, 0, 5, 0).这里要改回来
    
    // 根据富文本调整frame
    [self updateTextViewFrame:string textColor:colorString isOneLineShow:isOneLineShow];
    
    //    self.bottomShowLabel.text = string; // 在11系统，需要设置好frame之后再赋值
    self.bottomShowLabel.attributedText = attStr;
    self.currentOriginHtmlStr = attStr;
    [self addSubview:self.bottomShowTipView];
    [self.bottomShowTipView addSubview:self.bottomShowLabel];
    // 此时文案已更新高度，展示
    self.serviceBg.hidden = NO;
    self.serviceGifView.hidden = NO;
    
    if (autoScroll) {
        //计算是否要滚动换行
        //多久渐变一个字
        float scrollSpeed = 0.19f;
        if ([TKStringHelper isNotEmpty:questionOneWordSpeed]) scrollSpeed = questionOneWordSpeed.floatValue;
        if (scrollSpeed == 0) scrollSpeed = 0.19f;
        NSTimeInterval durationTime = attStr.string.length * 1.0 / scrollSpeed;
        
        //问题播放需要判断是否走
        [self createscrollBottomShowLabelTimer:self.currentOriginHtmlStr startIndex:0 endIndex:0 duration:durationTime isHighlight:YES];
    }
}

- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow
{
    CGFloat margin = 20;
    CGFloat bottomShowLabelX = margin;
    if (UISCREEN_HEIGHT < 812) {
        bottomShowLabelX = 15;
    }
    CGFloat serviceBgRight = _serviceBg.superview ? _serviceBg.TKRight : 0;
    bottomShowLabelX = serviceBgRight + margin;
    CGFloat bottomShowLabelY = 5;
    CGFloat bottomShowLabelWidth = self.bottomShowTipView.TKWidth - serviceBgRight - margin * 2;
    
    self.bottomShowLabel.textContainer.lineBreakMode =  NSLineBreakByWordWrapping;
    
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:string textColor:colorString];
    
    // 计算一行的宽、高度(中文高度包括行高,需要清除换行再计算)
    NSString *tempStr = [string stringByReplacingOccurrencesOfString:@"<br/>" withString:@""];
    NSMutableAttributedString *tempattStr = [self convertTextToHtmlString:tempStr textColor:colorString];
    CGFloat oneLineHeight = [self getHTMLHeightByStr:tempattStr width:CGFLOAT_MAX].height;
    
    // 如果不是播放问题，展示多行问题文本
    if (isOneLineShow == NO) {
        // 计算富文本高度，1行则1行显示，最多显示3行。仅针对问题
        // 计算文本的总高度
        //       CGFloat htmlHeight = [self getHTMLHeightByStr:attStr width:bottomShowLabelWidth].height;
        
        //        CGFloat extHeight = self.bottomShowLabel.textContainerInset.top  + self.bottomShowLabel.textContainerInset.bottom + bottomShowLabelY * 2 + self.bottomShowTipView.layer.borderWidth * 2;
        
        // 少于最大行数（如3行）展示全部
        //        CGFloat maxTotalHeight = oneLineHeight * 3;
        //        if (htmlHeight < maxTotalHeight) {
        //
        //            // 展示超过播报图案的高度，展示两行
        //            self.bottomShowTipView.TKHeight = oneLineHeight * 2 + extHeight;
        //
        //        } else {
        //            // 展示最大高度（>2）
        //            self.bottomShowTipView.TKHeight = maxTotalHeight + extHeight;
        //        }
        // 采用固定高度，不再动态计算
        self.bottomShowTipView.TKHeight = 110;
        
        //文本左右保持留白15
        self.bottomShowLabel.frame = CGRectMake(bottomShowLabelX, bottomShowLabelY, bottomShowLabelWidth, self.bottomShowTipView.TKHeight - 2 * bottomShowLabelY);
        self.bottomShowLabel.textContainer.maximumNumberOfLines = INT_MAX;
        
    } else {    // 如果是播放问题，需要展示多行（>1  问题文本 + 倒计时文本 + 识别结果文本）
        
        // 展示1行高度
        CGFloat oneLineWidth = [self getHTMLHeightByStr:attStr width:CGFLOAT_MAX].width + 16;
        self.bottomShowLabel.frame = CGRectMake(bottomShowLabelX, bottomShowLabelY, oneLineWidth, oneLineHeight * 1);
        self.bottomShowLabel.textContainer.maximumNumberOfLines = 1;
        if (self.bottomShowLabel.TKRight > self.bottomShowTipView.TKRight) {
            
            self.bottomShowLabel.TKWidth = bottomShowLabelWidth;
        }
        
        if (self.countDownType == TKCountDownTypeAnswer) {
            CGFloat margin1 = 7;
            self.countDownLabel.TKTop = self.bottomShowLabel.TKBottom + margin1;    // 换行展示
            
        } else if (self.countDownType == TKCountDownTypeUserAction) {
            
            // countDownLabel在bottomShowLabel(第1行)后拼接展示
            CGFloat margin2 = 0;
            self.countDownLabel.TKLeft = self.bottomShowLabel.TKRight + margin2;
            if ((self.countDownLabel.TKRight + margin) > self.bottomShowTipView.TKWidth) {   // countDownLabel到bottomShowTipView右侧的距离是15
                
                self.countDownLabel.TKRight = self.bottomShowTipView.TKWidth - margin;
                self.bottomShowLabel.TKWidth = self.countDownLabel.TKLeft - margin2 - self.bottomShowLabel.TKLeft;
            }
            self.countDownLabel.center = CGPointMake(self.countDownLabel.center.x, self.bottomShowLabel.center.y);
        }
        
        
        // 要和-(void)answerPromptType:(BOOL)flag identifyString:(NSString *)string中的高度对应
        // 展示最大高度
        self.bottomShowTipView.TKHeight = MAX(self.countDownLabel.TKBottom, self.bottomShowLabel.TKBottom) + 6 + self.answerPromptLabel.font.lineHeight + self.bottomShowLabel.textContainerInset.bottom + bottomShowLabelY + self.bottomShowTipView.layer.borderWidth;
    }
}

- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString
{
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)self.bottomShowLabel.font.pointSize, colorString, text];
    NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
    NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                              NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
    };
    NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

/**
 计算html字符串高度
 
 @param str html 未处理的字符串
 @param font 字体设置
 @param lineSpacing 行高设置
 @param width 容器宽度设置
 @return 富文本高度
 */
- (CGSize)getHTMLHeightByStr:(NSMutableAttributedString *)str width:(CGFloat)width
{
    CGSize contextSize = [str boundingRectWithSize:(CGSize){width, CGFLOAT_MAX} options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size;
    return contextSize;
    
}

/**
 @Auther Vie 2021年07月29日13:56:44
 改变阅读文本空间显示
 */
- (void)changeReadTextViewOffSet:(NSAttributedString *)originHtml startIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration repeatTime:(NSTimeInterval)repeatTime repeatIndex:(int)repeatIndex isHighlight:(BOOL)isHighlight {
        
    // 防止页面不显示的时候，定时器还在反复调用该功能
    if (!self.window) {
        
//        NSLog(@"思迪文案滚动动画：页面不显示,停止滚动");
        [self stopTextViewScroll];
        return;
    }
    
    if (self.changeReadTextViewWords < startIndex) {
//        NSLog(@"思迪文案滚动动画：changeReadTextViewWords少于startIndex，设置changeReadTextViewWords=startIndex(%i)", startIndex);
        self.changeReadTextViewWords = startIndex;
    }
    
    //    NSTimeInterval repeactTime = 1.0f;
    //    CGFloat scrollSpeed = 0.19f;
    //    if (scrollSpeed <= 0.05) repeactTime = 0.1;    // 滚动速度变大，刷新频率也更新
    //    int wordPerSecond = ceilf(repeactTime / scrollSpeed);
    
    int wordPerSecond = 0;
    
    // 若知道要滚动的内容长度和时间
    int totalWordsNeedToScroll = endIndex - startIndex;
    if (totalWordsNeedToScroll > 0) {
        wordPerSecond = ceilf(totalWordsNeedToScroll / duration * repeatTime);
        
        // 根据循环次数计算更精准的滚动字数
        if (repeatIndex > 0) {
            wordPerSecond = startIndex + ceilf(totalWordsNeedToScroll / duration * repeatTime * repeatIndex) - self.changeReadTextViewWords;
        }
        
        // 计算边界
        wordPerSecond = (self.changeReadTextViewWords + wordPerSecond) <= endIndex ? wordPerSecond : (endIndex - self.changeReadTextViewWords);
        if (wordPerSecond <= 0) {
//            NSLog(@"思迪文案滚动动画:超过endIndex，先不处理 wordPerSecond = %i", wordPerSecond);
//            return;
            wordPerSecond = 0;
        };
    } else {
        wordPerSecond = ceilf(self.bottomShowLabel.attributedText.string.length / duration * repeatTime);
    }
    
    if (self.changeReadTextViewWords < self.bottomShowLabel.attributedText.string.length) {
        // -1是防止到最后一个字再滚动的话会有点显示问题。会一整行滚动一次，又展示
        //       NSLog(@"----------555 思迪文案滚动动画：scrollSpeed = %.2f, wordPerSecond = %i, self.changeReadTextViewWords = %i, self.subtitlesTextView.attributedText.string.length = %i", scrollSpeed, wordPerSecond, self.changeReadTextViewWords, self.subtitlesTextView.attributedText.string.length);
//        NSLog(@"思迪文案滚动动画：self.changeReadTextViewWords = %i, wordPerSecond = %i, self.subtitlesTextView.attributedText.string.length = %i, startIndex = %i, endIndex = %i", self.changeReadTextViewWords, wordPerSecond, self.subtitlesTextView.attributedText.string.length, startIndex, endIndex);
        
        // 支持高亮效果
        if (isHighlight) {
                        
            // 高亮文字
            NSMutableAttributedString *html = originHtml.mutableCopy;
            NSInteger lenth = self.changeReadTextViewWords + wordPerSecond - startIndex;
            if (endIndex > 0) {
                lenth = lenth > (endIndex - startIndex) ?  (endIndex - startIndex) : lenth;
            }
//            [html addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FCC006"] range:NSMakeRange(startIndex, lenth)]; // 只高亮句子片段
            lenth = (startIndex + lenth) < self.bottomShowLabel.attributedText.string.length ? lenth : (self.bottomShowLabel.attributedText.string.length - startIndex);
            [html addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FCC006"] range:NSMakeRange(0, startIndex + lenth)];   // 从头到播放内容都高亮
            self.bottomShowLabel.attributedText = html;
        }
        
        // 滚动
//        NSRange rang = NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
        NSInteger lenth = wordPerSecond * 50; // 5s的播放长度
        NSInteger maxLenth = self.bottomShowLabel.attributedText.string.length - self.changeReadTextViewWords;
        lenth = lenth < (maxLenth) ? lenth : maxLenth;
        NSRange range = NSMakeRange(self.changeReadTextViewWords, lenth); // 为了展示效果，多滚动一些内容
        [self.bottomShowLabel scrollRangeToVisible:range];
//        NSLog(@"思迪文案滚动动画:滚动区域 range = %@", NSStringFromRange(range));
        
        // 记录数据
        self.changeReadTextViewWords = self.changeReadTextViewWords + wordPerSecond;
        
    } else {
        NSLog(@"思迪文案滚动动画:已滚动到底部，停止滚动");
        [self stopTextViewScroll];
    }
}

- (void)createscrollBottomShowLabelTimer:(NSAttributedString *)originHtml startIndex:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration isHighlight:(BOOL)isHighlight
{
    if (self.changeReadTextViewOffSetTimer == nil) {
//        NSLog(@"思迪文案滚动动画：创建定时器");
        
        // 文字高亮
        __weak typeof(self) weakSelf = self;
        NSTimeInterval repeatTime = 0.1f;
        __block int repeatIndex = 0; // 当前循环次数
        self.changeReadTextViewOffSetTimer = [NSTimer timerWithTimeInterval:repeatTime repeats:YES block:^(NSTimer * _Nonnull timer) {
            
            int currentRepeatIndex = repeatIndex;   // 只传递值
            [weakSelf changeReadTextViewOffSet:originHtml startIndex:startIndex endIndex:endIndex duration:duration * 0.9 repeatTime:repeatTime repeatIndex:currentRepeatIndex isHighlight:isHighlight];    // duration * 0.9是为了高亮和滚动效果快一点
            repeatIndex++; // 次数加一
        }];
        [[NSRunLoop mainRunLoop] addTimer:self.changeReadTextViewOffSetTimer forMode:NSRunLoopCommonModes];
    }
}

- (void)scrollBottomShowLabel:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration
{
//    NSLog(@"思迪文案滚动动画：滚动文本, startIndex = %i, endIndex = %i, duration = %.2f", startIndex, endIndex, duration);
    // 先暂停滚动
//    [self stopTextViewScroll];
    if (self.changeReadTextViewOffSetTimer) {
//        NSLog(@"思迪文案滚动动画：销毁定时器");
        
//        self.changeReadTextViewWords = 0; // 只需要重置定时器，滚动和高亮位置保留
        [self.changeReadTextViewOffSetTimer invalidate];
        self.changeReadTextViewOffSetTimer = nil;
    }
    
    [self createscrollBottomShowLabelTimer:self.currentOriginHtmlStr startIndex:startIndex endIndex:endIndex duration:duration isHighlight:YES];
}


- (void)stopTextViewScroll
{
    if (self.changeReadTextViewOffSetTimer) {
//        NSLog(@"思迪文案滚动动画：销毁定时器");
        
        self.changeReadTextViewWords = 0;
        [self.changeReadTextViewOffSetTimer invalidate];
        self.changeReadTextViewOffSetTimer = nil;
    }
}

/**
 <AUTHOR> 2021年07月08日16:15:38
 @语音识别过程中识别到的回答小字提示
 @param回答正确，无用回答
 */
-(void)answerPromptType:(BOOL)flag identifyString:(NSString *)string{
    
    [_answerPromptLabel removeFromSuperview];
    _answerPromptLabel=nil;
    [_answerPromptImgBg removeFromSuperview];
    _answerPromptImgBg=nil;
    
    // 更新识别倒计时文案
    self.hasAsrResult = YES;
    [self updateCountDownLabelText:self.answerCount countDownType:TKCountDownTypeAnswer];
    
    self.answerPromptLabel.text=string;
    
    // 要和- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow中的计算对应
    float y = self.countDownLabel.TKBottom + 6;
    
    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, self.answerPromptLabel.font.lineHeight)];
    float width=lableSize.width;
    float x=self.bottomShowLabel.TKLeft;
    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
    [self.bottomShowTipView addSubview:self.answerPromptLabel];
    
    //语音回答旁边小图标
    self.answerPromptImgBg=[[UIView alloc] initWithFrame:CGRectMake(x+width+6, y + 4, 22, 22)];
    self.answerPromptImgBg.layer.cornerRadius=self.answerPromptImgBg.TKWidth/2.0f;
    UIImageView *imgView=[[UIImageView alloc] initWithFrame:CGRectMake(0.0f, 0.0f, 22, 22)];
    [self.answerPromptImgBg addSubview:imgView];
    if (flag) {
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:self.mainColorString];
            self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:self.mainColorString];
        }else{
            self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#2772FE"];
            self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:@"#2772FE"];
        }
        
        
        imgView.image=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_one_answer_ok.png", TK_OPEN_RESOURCE_NAME]];
    }else{
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#EA4940"];
        self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:@"#EA4940"];
        imgView.image=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_one_answer_unknown.png", TK_OPEN_RESOURCE_NAME]];
    }
    [self.bottomShowTipView addSubview:self.answerPromptImgBg];
}

/**
 @Auther Vie 2021年07月28日16:40:17
 @param currentNum 当前进度，
 @param allNum 总进度数目；问题n，结束语1；n+1
 */
-(void)currentNum:(int)currentNum allNum:(int)allNum{
    if (allNum == 0) return;
    
    self.bottomShowTipRecordLineView.hidden = NO;
    
    float width = currentNum * 1.0f / allNum * self.bottomShowTipLineView.TKWidth;
    [self.bottomShowTipRecordLineView setFrameWidth:width > self.bottomShowTipLineView.TKWidth ? self.bottomShowTipLineView.TKWidth : width];
}


///// 是否展示录制按钮
///// @param isShow 是否展示
/// @param btnTitle 按钮文案，传Nil展示默认的“继续播报”
- (void)showNextBtn:(BOOL)isShow btnTitle:(NSString *)btnTitle {
    if (self.nextBtn.superview == nil) [self addSubview:self.nextBtn];
    [self bringSubviewToFront:self.nextBtn];
    self.nextBtn.hidden = !isShow;
    [self setButtonBackgroundColor:self.nextBtn alpha:1.0f];
    if([TKStringHelper isEmpty:btnTitle]){
        [self.nextBtn setTitle:@"继续播报" forState:UIControlStateNormal];
    }else{
        [self.nextBtn setTitle:btnTitle forState:UIControlStateNormal];
    }
}

/// 展示用户动作提示文案，等待用户做动作
/// @param originString 待展示的文案
/// @param htmlFlag 是否是html标签
/// @param waitTime 等待时间
- (void)showUserActionPrompt:(NSString *)originString isHtmlString:(BOOL)htmlFlag waitTime:(int)waitTime questionOneWordSpeed:(NSString *)questionOneWordSpeed {
    if (waitTime <= 0) {
        waitTime = 5;
    }
    
    self.isFinished = NO;
    
    // 防止等待时间文本闪烁，先隐藏，后显示
    self.countDownLabel.hidden = YES;
    [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeUserAction];
    [self updateTipLabel:[NSString stringWithFormat:@"%@",originString] textColor:[self.mainColorString isEqualToString:@"#2772FE"] ? @"#51B4FE":self.mainColorString cornerRadius:20.0f isOneLineShow:YES isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed autoScroll:NO];
    self.countDownLabel.hidden = NO;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self userActionCountDown:waitTime];
    });
}


/**
 <AUTHOR> 2019年04月26日18:39:08
 @强制用户做动作等待倒计时
 */
-(void)userActionCountDown:(int)waitTime {
    
    __weak typeof(self) weakSelf = self;
    
    // 其他业务异常语音打断倒计时
    if (!self.isFinished) {
        
        waitTime--;
        
        [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeUserAction];
        
        if (waitTime > 0) {
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self userActionCountDown:waitTime];
            });
        }else{
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(userActionCountDownEnd)]) {
                [self.delegate userActionCountDownEnd];
            }
        }
    }else{
        
    }
}

- (void)updateCountDownLabelText:(int)answerCount countDownType:(TKCountDownType)countDownType
{
    self.countDownType = countDownType;
    
    [self.bottomShowTipView addSubview:self.countDownLabel];
    
    NSString *tempStr = nil;
    if (countDownType == TKCountDownTypeAnswer) {
        if (self.hasAsrResult == YES) {
            tempStr =[NSString stringWithFormat:@"识别中…(%d)",(int)answerCount] ;
        } else {
            tempStr =[NSString stringWithFormat:@"请回答…(%d)",(int)answerCount] ;
        }
    } else {
        tempStr = [NSString stringWithFormat:@"(%d)",(int)answerCount];
    }
    self.countDownLabel.text = tempStr;
    
    [self.countDownLabel sizeToFit];
    self.countDownLabel.TKHeight = 22;
}

- (void)showNoAnswerPrompt
{
    self.answerPromptLabel.text = @"未检测到您的回答，请再回答一次";
    self.answerPromptLabel.backgroundColor = [TKUIHelper colorWithHexString:@"#FFFFFF"];
    
    float y = self.countDownLabel.TKBottom + 6;
    self.answerPromptLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, self.answerPromptLabel.font.lineHeight)];
    float width = lableSize.width + 12;
    float x = self.bottomShowLabel.TKLeft;
    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
    
    if (![self.mainColorString isEqualToString:@"#2772FE"]) {
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:self.mainColorString];
    }else{
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#2F85FF"];
    }
    
    [self.bottomShowTipView addSubview:self.answerPromptLabel];
}

/**
 <AUTHOR> 2023年06月27日16:57:36
 @朗读单向场景标题文案
 @return 朗读单向场景标题文案
 */
-(void)changeToReadVideoLabel{
    NSString *textString=@" 请使用普通话朗读";
    NSMutableAttributedString *attri =     [[NSMutableAttributedString alloc] initWithString:textString];
    [attri addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FFFFFF" alpha:0.7f] range:NSMakeRange(0, textString.length)];
    [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, textString.length)];
    // 创建图片图片附件
    NSTextAttachment *attach = [[NSTextAttachment alloc] init];
    attach.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_one_read_tip.png", TK_OPEN_RESOURCE_NAME]];;
    attach.bounds = CGRectMake(0, -5, 18, 18);
    NSAttributedString *attachImg = [NSAttributedString attributedStringWithAttachment:attach];
    
    //将图片插入到合适的位置
    [attri insertAttributedString:attachImg atIndex:0];
    
    _bottomShowLabel.attributedText = attri;
    
    
    _bottomShowLabel.textAlignment=NSTextAlignmentCenter;
    
    
    float gap=10;
    //5s等小屏幕机型
    if (UISCREEN_WIDTH==320) {
        gap=6;
    }
    float y=12;
    float height=18;
    
    CGSize lableSize = [_bottomShowLabel sizeThatFits:CGSizeMake(self.TKWidth, height)];
    float width=lableSize.width;
    float x=(self.bottomShowTipView.TKWidth-width)/2.0f;
    _bottomShowLabel.frame=CGRectMake(x, y, width, height);
    
    
}

/**
 <AUTHOR> 2023年06月26日14:25:22
 @初始化单向视频朗读默认界面
 */
-(void)showReadTextView{
    float x=20;
    //5s等小屏幕机型
    if (UISCREEN_WIDTH==320) {
        x=15;
    }
    float width=self.TKWidth-x*2;
    [self changeToReadVideoLabel];
    
    CGSize readLableSize;
    if ([TKStringHelper isEmpty:self.requestParam[@"longReadString"]]) {
        if ([TKStringHelper isNotEmpty:self.requestParam[@"readHtmlString"]]) {
            NSString *text=[TKCommonUtil switchLabelToSpan:self.requestParam[@"readHtmlString"] ];
            NSData *data = [text dataUsingEncoding:NSUnicodeStringEncoding];
            NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType};
            NSAttributedString *html = [[NSAttributedString alloc]initWithData:data options:options documentAttributes:nil error:nil];
            self.readLable.attributedText = html;
            //固定只放三行文本，超过的滚动处理
            readLableSize = [self.readLable sizeThatFits:CGSizeMake(width-30, MAXFLOAT)];
            readLableSize.height=86;
        }else{
            self.readLable.text=self.requestParam[@"readString"];
            //固定只放三行文本，超过的滚动处理
            readLableSize = [self.readLable sizeThatFits:CGSizeMake(width-30, MAXFLOAT)];
            readLableSize.height=86;
        }
        
    }else{
        NSString *text=[TKCommonUtil switchLabelToSpan:self.requestParam[@"longReadString"] ];
        NSData *data = [text dataUsingEncoding:NSUnicodeStringEncoding];
        NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType};
        NSAttributedString *html = [[NSAttributedString alloc]initWithData:data
                                                                   options:options
                                                        documentAttributes:nil
                                                                     error:nil];
        self.readLable.attributedText = html;
        readLableSize=CGSizeMake(width-30, UISCREEN_HEIGHT/3.0f);
    }
    
    
    self.readLable.frame=CGRectMake(15, self.bottomShowLabel.TKBottom+8, width-30, readLableSize.height);
    float height=8+readLableSize.height+self.readLable.TKTop;
    float gap=10;
    //5s等小屏幕机型
    if (UISCREEN_WIDTH==320) {
        gap=6;
    }
    float y=self.backBtn.TKBottom+gap;
    
    self.bottomShowTipView.frame=CGRectMake(x, y, width, height);
    //    [self.bottomShowLabel removeFromSuperview];
    [self.bottomShowTipView addSubview:self.readLable];
}

/**
 @Auther Vie 2021年03月08日13:19:01
 @阅读文本引导
 */
-(void)readingGuideTip{
    NSString *readText;
    if ([TKStringHelper isNotEmpty:self.requestParam[@"readHtmlString"]]) {
        readText= [NSString stringWithFormat:@"%@", self.requestParam[@"readHtmlString"]];
    }else{
        readText= [NSString stringWithFormat:@"%@", self.requestParam[@"readString"]];
    }
    if ([TKStringHelper isEmpty:self.requestParam[@"longReadString"]]) {
        if ([TKStringHelper isNotEmpty:self.requestParam[@"readHtmlString"]]) {
            NSData *data = [readText dataUsingEncoding:NSUnicodeStringEncoding];
            NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType};
            NSMutableAttributedString *html = [[NSMutableAttributedString alloc]initWithData:data options:options documentAttributes:nil error:nil];
            [html addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FCC006"] range:NSMakeRange(0, self.colorWordsNum)];
            self.readLable.attributedText = html;
        }else{
            NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc]initWithString:readText];
            [attributedStr addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:20] range:NSMakeRange(0, readText.length)];
            [attributedStr addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FCC006"] range:NSMakeRange(0, self.colorWordsNum)];
            [attributedStr addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#51B4FE":self.mainColorString] range:NSMakeRange(self.colorWordsNum, readText.length - self.colorWordsNum)];
            self.readLable.attributedText = attributedStr;
        }
        
        //计算是否要滚动换行
        CGSize labelSize=[self.readLable sizeThatFits:CGSizeMake(self.readLable.TKWidth, MAXFLOAT)];
        float diffY=labelSize.height-self.readLable.TKHeight;
        if (diffY>0) {
            NSRange rang=NSMakeRange(self.colorWordsNum, 1);
            [self.readLable scrollRangeToVisible:rang];
        }
        
        if (self.colorWordsNum < self.readLable.attributedText.string.length) {
            self.colorWordsNum++;
            //多久渐变一个字
            float colorWordTime=self.requestParam[@"colorWordTime"]?[self.requestParam[@"colorWordTime"] floatValue]:0.3f;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(colorWordTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self readingGuideTip];
            });
        }
        
    }else{
        //        //html长文本滚动不做处理
        //        NSData *data = [self.requestParam[@"longReadString"] dataUsingEncoding:NSUnicodeStringEncoding];
        //        NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType};
        //        NSAttributedString *html = [[NSAttributedString alloc]initWithData:data
        //                                                                   options:options
        //                                                        documentAttributes:nil
        //                                                                     error:nil];
        //        self.readLable.attributedText = html;
    }
    
    
    
}


/// 展示阅读文案
/// - Parameters:
///   - content: 阅读内容
///   - countdownTime: 倒计时时长
- (void)showWithContent:(NSString *)content countdownTime:(NSInteger)countdownTime type:(TKReadingType)type readTitle:(NSString *)readTitle readConfirmBtnTitle:(NSString *)readConfirmBtnTitle oneWordSpeed:(NSString *)oneWordSpeed {
    [self addSubview:self.readingView];
    [self.readingView showWithContent:content countdownTime:countdownTime type:type readTitle:readTitle readConfirmBtnTitle:readConfirmBtnTitle  oneWordSpeed:oneWordSpeed];
}

///// 是否展示录制按钮
///// @param isShow 是否展示
- (void)showTakeRecordBtn:(BOOL)isShow {
    if (self.takeBtn.superview == nil) [self addSubview:self.takeBtn];
    [self bringSubviewToFront:self.takeBtn];
    self.takeBtn.hidden = !isShow;
}

- (void)atuoStartTakeRecord:(int)countDown
{
    [self createAutoStartRecordTimer:countDown];
    [self updateTakeBtnWithCountDown:countDown];
}

- (void)updateTakeBtnWithCountDown:(int)countDown
{
    NSString *btnTitle;
    if(self.isReadVideo){
        btnTitle=@"◉  开始录制";
    }else{
        btnTitle=@"确认放置好，进入下一步";
    }
    if (countDown > 0) {
        btnTitle = [btnTitle stringByAppendingFormat:@"(%i)",countDown];
    }
    [_takeBtn setTitle:btnTitle forState:UIControlStateNormal];
}

- (void)createAutoStartRecordTimer:(NSTimeInterval)interval
{
    [self stopAutoStartRecordTimer];
    
    if (interval <= 0) return;
    
    // 设置开始录制超时定时器
    self.startAutoRecordTimer = [NSTimer timerWithTimeInterval:1 target:self selector:@selector(startAutoRecord:) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.startAutoRecordTimer forMode:NSRunLoopCommonModes];
    
    self.startAutoRecordCountDown = interval;
}

- (void)stopAutoStartRecordTimer {
    if (_startAutoRecordTimer) {
        [self.startAutoRecordTimer invalidate];
        self.startAutoRecordTimer = nil;
    }
}

- (void)startAutoRecord:(NSTimer *)timer
{
    TKLogInfo(@"思迪录制日志：就绪后达到时间，自动开始录制(%i)", self.startAutoRecordCountDown);
    if (self.startAutoRecordCountDown > 1) {
        self.startAutoRecordCountDown--;
        [self updateTakeBtnWithCountDown:self.startAutoRecordCountDown];
    } else {
        [self stopAutoStartRecordTimer];
        
        [self takeAction:self.takeBtn];
    }
}


#pragma mark - TKReadingViewDelegate
/**
 点击关闭按钮
 */
- (void)closeBtnDidClicked:(TKReadingView *)readingView{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
        [self.readingView removeFromSuperview];
        self.readingView = nil;
        
        [self.delegate goBack];
    }
}

/**
 点击确认按钮
 */
- (void)confirmBtnDidClicked:(TKReadingView *)readingView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(nextAction)]) {
        [self.readingView removeFromSuperview];
        self.readingView = nil;
        [self.delegate nextAction];
    }
}

#pragma mark lazyloading
///**
// <AUTHOR> 2019年08月29日10:18:17
// @初始化懒加载circularLayer
// @return circularLayer
// */
//-(CAShapeLayer *)circularLayer{
//    if (!_circularLayer) {
//
//        float width=self.answerRecordView.frame.size.width+self.answerRecordView.layer.borderWidth;
//        float height=self.answerRecordView.frame.size.height;
//        float x=self.answerRecordView.frame.origin.x-self.answerRecordView.layer.borderWidth/2.0f;
//        float y=self.answerRecordView.frame.origin.y-self.answerRecordView.layer.borderWidth/2.0f;
//
//
//        _circularLayer = [CAShapeLayer layer];
//        _circularLayer.frame = CGRectMake(x, y, width, height);
//        _circularLayer.masksToBounds=NO;
//
//
//
//        UIBezierPath *bezierPath = [UIBezierPath bezierPathWithArcCenter:CGPointMake(width/2, width/2) radius:width/2-3 startAngle:- M_PI_2 endAngle:-M_PI_2 + M_PI * 2 clockwise:YES];
//
//        _circularLayer.path = bezierPath.CGPath;
//
//        _circularLayer.fillColor = [UIColor clearColor].CGColor;
//        _circularLayer.lineWidth = self.answerRecordView.layer.borderWidth;
//        _circularLayer.strokeColor = [TKUIHelper colorWithHexString:@"#EA4940"].CGColor;
//
//    }
//    return _circularLayer;
//}

/**
 <AUTHOR> 2019年04月03日10:47:55
 @初始化懒加载人像取景框矩阵
 @return 人像取景框矩阵
 */
-(CGRect)boxRect{
    if (CGRectIsEmpty(_boxRect)) {
        //因为是多问题，对准框需要固定不变位置，按文本4行高度117弄y坐标
        float boxRectX =38;
        float gap=36;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            boxRectX=32;
            gap=22;
        }
        float boxRectWidth = self.TKWidth-boxRectX*2.0f;
        float boxRectHeight = boxRectWidth / 300.0f * 340.0f; // 图片宽高是300 * 340
        float boxRectY = self.backBtn.TKBottom + gap + 117;
        _boxRect=CGRectMake(boxRectX, boxRectY, boxRectWidth, boxRectHeight);
    }
    return _boxRect;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加载顶部遮罩层
 @return 顶部遮罩层
 */
-(UIView *)topView{
    if (!_topView) {
        _topView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, self.TKWidth, self.boxRect.origin.y)];
        _topView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _topView;
}


/**
 <AUTHOR> 2019年04月03日11:20:41
 @初始化懒加载底部遮罩层
 @return 底部遮罩层
 */
-(UIView *)bottomView{
    if (!_bottomView) {
        _bottomView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.size.height+self.boxRect.origin.y, self.TKWidth, self.TKHeight-self.boxRect.origin.y-self.boxRect.size.height)];
        _bottomView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _bottomView;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加左部遮罩层
 @return 左部遮罩层
 */
-(UIView *)leftView{
    if (!_leftView) {
        _leftView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.origin.y, self.boxRect.origin.x, self.boxRect.size.height)];
        _leftView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _leftView;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加右部遮罩层
 @return 右部遮罩层
 */
-(UIView *)rightView{
    if (!_rightView) {
        _rightView=[[UIView alloc] initWithFrame:CGRectMake(self.boxRect.origin.x+self.boxRect.size.width, self.boxRect.origin.y, self.TKWidth - CGRectGetMaxX(self.boxRect), self.boxRect.size.height)];
        _rightView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
    }
    return _rightView;
}

/**
 <AUTHOR> 2019年12月30日22:05:40
 @初始化懒加载底部文档等提示展示区域
 @return 底部文档等提示展示区域
 */
-(UIView *)bottomShowTipView{
    if (!_bottomShowTipView) {
        //        float x=15;
        //        float width=self.frame.size.width-x*2;
        //        float height=160;
        //        float y=self.frame.size.height-height-20;
        //        if (ISIPHONEX) {
        //            y=y-34;
        //        }
        
        _bottomShowTipView=[[UIView alloc] init];
        _bottomShowTipView.layer.borderWidth = 1;
        //子视图是否局限于视图的边界。
        _bottomShowTipView.clipsToBounds=YES;
        
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            _bottomShowTipView.layer.borderColor=[TKUIHelper colorWithHexString:self.mainColorString alpha:0.2f].CGColor;
        }else{
            _bottomShowTipView.layer.borderColor=[TKUIHelper colorWithHexString:@"#498FD5" alpha:0.2f].CGColor;
        }
        [_bottomShowTipView setBackgroundColor:[TKUIHelper colorWithHexString:@"#040D16" alpha:0.5f]];
        
        float gap =31;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=22;
        }
        
        float bottomShowTipViewX=20;
        if (UISCREEN_WIDTH==320) {
            bottomShowTipViewX=15;
        }
        float bottomShowTipViewY=self.backBtn.TKBottom+gap;
        float bottomShowTipViewWidth=self.TKWidth-2*bottomShowTipViewX;//左右留白15;
        self.bottomShowTipView.layer.cornerRadius=13.0f;
        self.bottomShowTipView.frame=CGRectMake(bottomShowTipViewX, bottomShowTipViewY, bottomShowTipViewWidth, 42);
    }
    return _bottomShowTipView;
}


/**
 <AUTHOR> 2019年12月30日22:20:29
 @初始化懒加载底部文档等提示展示区域横线
 @return 底部文档等提示展示区域横线
 */
-(UIView *)bottomShowTipLineView{
    if (!_bottomShowTipLineView) {
        float x=15;
        float width=self.TKWidth-2*x;
        float height=4;
        float y=self.TKHeight-50-height;
        
        _bottomShowTipLineView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _bottomShowTipLineView.backgroundColor=[TKUIHelper colorWithHexString:@"#FFFFFF " alpha:0.2f];
        _bottomShowTipLineView.layer.cornerRadius=height/2.0f;
    }
    return _bottomShowTipLineView;
}

/**
 <AUTHOR> 2019年12月30日22:20:29
 @初始化懒加载bottomShowTipRecordLineView
 @return bottomShowTipRecordLineView
 */
-(UIView *)bottomShowTipRecordLineView{
    if (!_bottomShowTipRecordLineView) {
        _bottomShowTipRecordLineView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, self.bottomShowTipLineView.TKHeight)];
        
        _bottomShowTipRecordLineView.layer.cornerRadius=self.bottomShowTipLineView.TKHeight/2.0f;
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            
            [_bottomShowTipRecordLineView setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString alpha:0.5f]];
        }else{
            
            [_bottomShowTipRecordLineView setBackgroundColor:[TKUIHelper colorWithHexString:@"#2772FE" alpha:0.5f]];
        }
    }
    return _bottomShowTipRecordLineView;
}


/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载底部文字展示
 @return 底部文字展示
 */
-(UITextView *)bottomShowLabel{
    if (!_bottomShowLabel) {
        
        _bottomShowLabel=[[UITextView alloc] init];
        //        _bottomShowLabel.numberOfLines=0;
        _bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        _bottomShowLabel.textColor=[UIColor whiteColor];
        _bottomShowLabel.textAlignment=NSTextAlignmentCenter;
        _bottomShowLabel.clipsToBounds=YES;//子视图是否局限于视图的边界。
        
        //        _bottomShowLabel.text=@"请您保持全脸在人像框内。";
        _bottomShowLabel.backgroundColor=[UIColor clearColor];
        _bottomShowLabel.frame=CGRectMake(0, 0, self.bottomShowTipView.TKWidth, self.bottomShowTipView.TKHeight);
        _bottomShowLabel.backgroundColor=[UIColor clearColor];
        _bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 0, 0, 0);
        [_bottomShowLabel setEditable:false];
        _bottomShowLabel.showsVerticalScrollIndicator = NO;
        _bottomShowLabel.showsHorizontalScrollIndicator = NO;
    }
    return _bottomShowLabel;
}


/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载提示layer
 @return 提示layer
 */
-(TKLayerView *)layerView{
    if (!_layerView) {
        _layerView=[[TKLayerView alloc] initContentView:self withBtnTextColor:nil cancelBtnTextColor:nil withWidth:self.boxRect.size.width withFont:[UIFont fontWithName:@"PingFangSC-Semibold" size:22]];
        
        [_layerView setShowTipDuration:0.6];
    }
    return _layerView;
}


/**
 <AUTHOR> 2019年04月01日14:48:01
 @初始化懒加载UIImageView人像取景框
 @return UIImageView人像取景框
 */
-(UIImageView *)boxImgView{
    if (!_boxImgView) {
        
        _boxImgView=[[UIImageView alloc] initWithFrame:self.boxRect];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_default_box.png", TK_OPEN_RESOURCE_NAME]];
        
        //        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
        //            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        //            [_boxImgView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        //        }
        [_boxImgView setImage:img];
    }
    return _boxImgView;
}

/**
 @初始化懒加载UIImageView人像取景背景框(外层的黑色边框)
 @return UIImageView人像取景背景框
 */
-(UIImageView *)boxImgBackgroundView{
    if (!_boxImgBackgroundView) {
        
        _boxImgBackgroundView = [[UIImageView alloc] initWithFrame:self.boxRect];
        [_boxImgBackgroundView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/<EMAIL>", TK_OPEN_RESOURCE_NAME]]];
    }
    return _boxImgBackgroundView;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnWidth=32;
        float backBtnheight=32;
        float backBtnX=20.0f;
        float backBtnY=6+STATUSBAR_HEIGHT;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        _backBtn.clipsToBounds = YES;
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来
        
        [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
        _backBtn.layer.cornerRadius = backBtnWidth/2.0f;
        
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}


///**
// <AUTHOR> 2020年01月02日20:40:08
// @初始化懒加载回调问题音量波动图
// @return 回调问题音量波动图
// */
//-(UIView *)answerRecordView{
//    if (!_answerRecordView) {
//        float width=64;
//        float x=(self.frame.size.width-width)/2;
//        float gap=40;
//        //5s等小屏幕机型
//        if (UISCREEN_WIDTH==320) {
//            gap=10;
//        }
//        float y=self.frame.size.height-width-gap;
//        _answerRecordView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, width)];
//
//        _answerRecordView.layer.borderWidth=3.0f;
//        _answerRecordView.layer.borderColor=[TKUIHelper colorWithHexString:@"#FFFFFF" alpha:0.5f].CGColor;
//        _answerRecordView.layer.cornerRadius=width/2;
//
//        float imgWidth=44;
//        float imgX=(width-imgWidth)/2;
//        float imgY=(width-imgWidth)/2;
//        UIImageView *imgView=[[UIImageView alloc] initWithFrame:CGRectMake(imgX, imgY, imgWidth, imgWidth)];
//        [imgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_answer_record.png", TK_OPEN_RESOURCE_NAME]]];
//        self.answerRecordImgView=imgView;
//        [_answerRecordView addSubview:imgView];
//
//    }
//    return _answerRecordView;
//}




/**
 <AUTHOR> 2019年04月26日18:55:02
 @初始化懒加载回答倒计时展示label
 @return 回答倒计时展示label
 */
-(UILabel *)countDownLabel{
    if (!_countDownLabel) {
        float width=self.bottomShowLabel.TKWidth;
        float height=22;
        float x=self.bottomShowLabel.TKLeft;
        float y=40;
        _countDownLabel= [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _countDownLabel.font =  [UIFont fontWithName:@"PingFang SC" size:22];;
        _countDownLabel.textAlignment=NSTextAlignmentLeft;
        _countDownLabel.textColor = [TKUIHelper colorWithHexString:@"#FFFFFF" alpha:0.7f];
    }
    return _countDownLabel;
}

/**
 <AUTHOR> 2020年08月26日10:52:52
 @初始化懒加载takeBtn
 @return takeBtn
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {
        float height=44;
        float width;
        float gap=40;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=10;
        }
        
        if(self.isReadVideo){
            width=175;
        }else{
            width = 255;
        }
        
        
        float y=self.TKHeight-height-gap-IPHONEX_BUTTOM_HEIGHT;
        float x=(self.TKWidth-width)/2;
        
        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [self updateTakeBtnWithCountDown:0];
        
        _takeBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
        [_takeBtn addTarget:self action:@selector(takeAction:) forControlEvents:UIControlEventTouchUpInside];
        _takeBtn.layer.cornerRadius=height/2.0f;
        
        // 默认一开始不可用
        [self enableTakeRecord:NO];
    }
    return _takeBtn;
}


- (UIButton *)nextBtn{
    if (!_nextBtn) {
        
        _nextBtn = [[UIButton alloc] initWithFrame:self.takeBtn.frame];
        NSString *nextBtnTitle = @"继续播报";
        [_nextBtn setTitle:nextBtnTitle forState:UIControlStateNormal];
        
        _nextBtn.titleLabel.font = self.takeBtn.titleLabel.font;
        [_nextBtn addTarget:self action:@selector(nextAction:) forControlEvents:UIControlEventTouchUpInside];
        _nextBtn.layer.cornerRadius = self.takeBtn.TKHeight * 0.5;
        
        _nextBtn.layer.borderWidth = 0.0f;
        _nextBtn.layer.borderColor = [ UIColor clearColor].CGColor;
        [_nextBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _nextBtn.backgroundColor = [TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#2772FE":self.mainColorString];
    }
    return _nextBtn;
}

/**
 <AUTHOR> 2020年08月26日10:52:52
 @初始化懒加载语音回答结果提示
 @return 语音回答结果提示
 */
- (UILabel *)answerPromptLabel{
    if (!_answerPromptLabel) {
        _answerPromptLabel = [[UILabel alloc] init];
        _answerPromptLabel.textAlignment=NSTextAlignmentCenter;
        _answerPromptLabel.font=[UIFont fontWithName:@"PingFang SC" size:22];
    }
    return _answerPromptLabel;
}

- (UIView *)badgeView {
    if (!_badgeView) {
        NSInteger const pointWidth = 6; //小红点的宽高
        CGRect frame = CGRectMake(self.recordTimeLabel.TKLeft - pointWidth, self.recordTimeLabel.TKTop + (self.recordTimeLabel.TKHeight - pointWidth) * 0.5, pointWidth, pointWidth);
        _badgeView = [[UILabel alloc] initWithFrame:frame];
        _badgeView.backgroundColor = [TKUIHelper colorWithHexString:@"ff5153"];
        //圆角为宽度的一半
        _badgeView.layer.cornerRadius = pointWidth / 2;
        //确保可以有圆角
        _badgeView.layer.masksToBounds = YES;
        
    }
    
    return _badgeView;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载recordTimeLabel
 @return recordTimeLabel
 */
-(UILabel *)recordTimeLabel{
    if (!_recordTimeLabel) {
        float y=6+STATUSBAR_HEIGHT;
        //        float width=96;
        float width=75;
        float height=32;
        float x=(self.frame.size.width-width)/2;
        _recordTimeLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _recordTimeLabel.text=@"00:00";
        _recordTimeLabel.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        _recordTimeLabel.textAlignment=NSTextAlignmentCenter;
        _recordTimeLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];;
        
    }
    return _recordTimeLabel;
}

/**
 <AUTHOR> 2021年09月15日15:44:25
 @初始化懒加载serviceBg
 @return serviceBg
 */
-(UIImageView *)serviceBg{
    if (!_serviceBg) {
        float width=36;
        float height=36;
        float x=15;
        float y=15;
        _serviceBg=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_serviceBg setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceBg.png", TK_OPEN_RESOURCE_NAME]]];
    }
    return _serviceBg;
}

/**
 <AUTHOR> 2021年09月15日15:44:34
 @初始化懒加载serviceGifView
 @return serviceGifView
 */
-(TKGIFImageView *)serviceGifView{
    if (!_serviceGifView) {
        float height=20;
        float width=28;
        float x=(self.serviceBg.TKWidth-width)/2.0f;
        float y=(self.serviceBg.TKHeight-height)/2.0f;
        _serviceGifView=[[TKGIFImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_serviceGifView setImageByName:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceSpeak.gif", TK_OPEN_RESOURCE_NAME]];
    }
    return _serviceGifView;
}


- (void)setAvPreviewView:(UIView *)avPreviewView {
    _avPreviewView = avPreviewView;
    [self insertSubview:avPreviewView atIndex:0];
}

- (TKFaceDectTipView *)faceDectTipView {
    if (!_faceDectTipView) {
        _faceDectTipView = [[TKFaceDectTipView alloc] initWithFrame:self.boxRect];
        //        _faceDectTipView.isEnhance = YES;
    }
    
    return _faceDectTipView;
}

/**
 <AUTHOR> 2020年02月28日10:50:51
 @初始化懒加载结束录制按钮
 @return 结束录制按钮
 */
-(UIButton *)endBtn{
    if (!_endBtn) {
        
        float endBtnHeight=44;
        float endBtnWidth=175;
        float endBtnX=(self.TKWidth-endBtnWidth)/2.0f;;
        float gap=40;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=10;
        }
        float endBtnY=self.TKHeight-endBtnHeight-gap-IPHONEX_BUTTOM_HEIGHT;
        _endBtn=[[UIButton alloc] initWithFrame:CGRectMake(endBtnX, endBtnY, endBtnWidth, endBtnHeight)];
        
        [_endBtn setTitle:@"完成录制" forState:UIControlStateNormal];
        [_endBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF" alpha:0.7f] forState:UIControlStateNormal];
        _endBtn.layer.borderWidth=2.0f;
        _endBtn.layer.borderColor=[TKUIHelper colorWithHexString:@"#BDDBFA" alpha:1.0f].CGColor;
        _endBtn.layer.cornerRadius=endBtnHeight/2.0f;
        [_endBtn addTarget:self action:@selector(endAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _endBtn;
}

/**
 <AUTHOR> 2020年01月02日10:16:18
 @初始化懒加载倒计时文本
 @return 倒计时文本
 */
-(UILabel *)waitTipLabel{
    if (!_waitTipLabel) {
        float width=152;
        _waitTipLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, width, width)];
        _waitTipLabel.layer.cornerRadius=width/2;
        _waitTipLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.5];
        _waitTipLabel.textColor=[UIColor whiteColor];
        _waitTipLabel.center=self.boxImgView.center;
        _waitTipLabel.textAlignment=NSTextAlignmentCenter;
        _waitTipLabel.font=[UIFont fontWithName:@"PingFangSC-Medium" size:40];
        _waitTipLabel.clipsToBounds = YES;
    }
    return _waitTipLabel;
}

/**
 <AUTHOR> 2020年02月27日21:21:26
 @初始化懒加载阅读文本
 @return 阅读文本
 */
-(UITextView *)readLable{
    if (!_readLable) {
        _readLable=[[UITextView alloc] init];
        _readLable.font =[UIFont fontWithName:@"PingFang SC" size:20];
        _readLable.textAlignment=NSTextAlignmentLeft;
        //        _readLable.numberOfLines=0;
        _readLable.backgroundColor=[UIColor clearColor];
        _readLable.textColor =[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#51B4FE":self.mainColorString];
        _readLable.textContainerInset=UIEdgeInsetsMake(5, -5, 0, -5);
        [_readLable setEditable:false];
    }
    return _readLable;
}


/**
 *  <AUTHOR> 2023年07月10日13:30:12
 *  @初始化懒加载topBgView
 *  @return  topBgView
 */
-(UIView *)topBgView{
    if (!_topBgView) {
        float gap =31;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=22;
        }
        float height=self.backBtn.TKBottom+gap;
        float width=self.TKWidth;
        float x=0;
        float y=0;
        _topBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f].CGColor,(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.00f].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
        
        btoGradientLayer.frame = CGRectMake(0, 0, width, height);
        btoGradientLayer.startPoint = CGPointMake(0.5, 0);
        btoGradientLayer.endPoint = CGPointMake(0.5, 1);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        [_topBgView.layer addSublayer:btoGradientLayer]; //设置颜色渐变
    }
    return _topBgView;
}


/**
 *  <AUTHOR> 2023年07月10日13:30:12
 *  @初始化懒加载bottomBgView
 *  @return  bottomBgView
 */
-(UIView *)bottomBgView{
    if (!_bottomBgView) {
        float gap =31;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=22;
        }
        float height=self.TKHeight-self.bottomShowTipLineView.TKTop+20;
        float width=self.TKWidth;
        float x=0;
        float y=self.TKHeight-height;
        _bottomBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.00f].CGColor,(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
        
        btoGradientLayer.frame = CGRectMake(0, 0, width, height);
        btoGradientLayer.startPoint = CGPointMake(0.5, 0);
        btoGradientLayer.endPoint = CGPointMake(0.5, 1);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        [_bottomBgView.layer addSublayer:btoGradientLayer]; //设置颜色渐变
    }
    return _bottomBgView;
}

- (TKReadingView *)readingView {
    if (!_readingView) {
        NSMutableDictionary *param=[[NSMutableDictionary alloc] init];
        param[@"readAll"]=@"1";
        TKReadingView *readingView = [[TKReadingView alloc] initWithFrame:self.bounds param:param];
        _readingView = readingView;
        _readingView.delegate = self;
        //        _readingView.autoScroll = YES;
    }
    return _readingView;
}

@end
