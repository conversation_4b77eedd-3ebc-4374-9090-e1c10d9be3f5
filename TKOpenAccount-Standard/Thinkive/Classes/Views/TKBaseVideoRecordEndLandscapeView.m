//
//  TKBaseVideoRecordEndLandscapeView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/11.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKBaseVideoRecordEndLandscapeView.h"
#import "TKOpenTipView.h"
#import "TKPlayerToolView.h"

#define TK_ONEVIEW_TIP_LABEL_END_COLOR_BLUE  [TKUIHelper colorWithHexString:@"#0354C2"]
#define TK_ONEVIEW_TIP_LABEL_END_COLOR_BLACK  [TKUIHelper colorWithHexString:@"#333333"]
#define TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE  [TKUIHelper colorWithHexString:@"#FFFFFF"]
#define TK_OPEN_MIN(a,b,c) (a<b?(a<c?a:c):(b<c?b:c))

@interface TKBaseVideoRecordEndLandscapeView ()<TKPlayerToolViewDelegate>
@property (nonatomic, strong) UIImageView *bgImgView;//背景视图
@property (nonatomic, strong) UIView *videoShowBgView;//视频展示人像视图背景


@property (nonatomic, strong) UILabel *bigTipLabel;//大字的提示语
@property (nonatomic, strong) UILabel *smallTipLabel;//小字的提示语
@property (nonatomic, strong)  UIImageView *smallTipLeftImgView;//小字提示语左边的图案
@property (nonatomic, readwrite, strong) UIView *smallTipView; // 小字提示控件


@property (nonatomic, strong) UILabel *secondsLabel;//视频多少秒展示


@property (nonatomic, assign) BOOL isPlay;

@property (nonatomic, strong) TKPlayerToolView *tkPlayerToolView;//视频暂停重播工具视图

@end

@implementation TKBaseVideoRecordEndLandscapeView
@synthesize endType = _endType;
@synthesize secondsString = _secondsString;
@synthesize videoShowImgView = _videoShowImgView;
@synthesize failureString = _failureString;
@synthesize delegate = _delegate;
@synthesize warningTipLabel = _warningTipLabel;
@synthesize warningBackBtn = _warningBackBtn;
@synthesize mainColorString = _mainColorString;
@synthesize resetBtn = _resetBtn;
@synthesize submitBtn = _submitBtn;
@synthesize isLandscape = _isLandscape;
@synthesize playerControlView = _playerControlView;


-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        
        //移除所有子视图
        [[self subviews] makeObjectsPerformSelector:@selector(removeFromSuperview)];
        [self setBackgroundColor:TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE];
        self.requestParam=param;
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2F85FF";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
        [self viewInit];
    }
    return self;
}

/**
 <AUTHOR>
 @初始化单向视频失败界面
 */
-(void)viewErrorInit:(NSString *)errorMsg{


    [self warningTipString:errorMsg];
    [self addSubview:self.bgImgView];
    [self addSubview:self.backBtn];
    [_backBtn setTintColor:TK_ONEVIEW_TIP_LABEL_END_COLOR_BLACK];
    [self addSubview:self.voiceWarningImgView];
    [self addSubview:self.warningTitleTipLabel];
    [self addSubview:self.warningTipLabel];
    [self addSubview:self.warningBackBtn];
}

/**
 <AUTHOR>
 @初始化单向视频正常走完流程结果页面
 */
- (void)viewInit{
    // 这里是取巧，应该由外层设置，但是预览图像创建很复杂，所以在view里面写死。把比例改成4：3
    // 要在创建预览图层前设置
    self.isLandscape = YES;

    self.backgroundColor = [TKUIHelper colorWithHexString:@"#232323"];
    
    [self addSubview:self.bgImgView];
    [self addSubview:self.backBtn];

    [self addSubview:self.resetBtn];
    [self addSubview:self.submitBtn];
    
    [self addSubview:self.videoShowBgView]; // 需要在bigTipLabel先添加
    [self addSubview:self.bigTipLabel];

    [self.videoShowBgView addSubview:self.playerControlView];
    [self.videoShowBgView addSubview:self.playVideoBtn];
    [self.videoShowBgView addSubview:self.playTipLabel];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self setButtonBackgroundColor:_warningBackBtn];
    [self setButtonBackgroundColor:_submitBtn];
}

- (void)setButtonBackgroundColor:(UIButton *)button
{
    if (button.window) {
        // 修改按钮渐变色
        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [button setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]]];
        }else{
            // 先移除.注意不要把button的layer也移除了
            for (int i = 0; i < button.layer.sublayers.count; i++) {
                CALayer *layer = button.layer.sublayers[i];
                if ([layer isKindOfClass:CAGradientLayer.class]) {
                    [layer removeFromSuperlayer];
                }
            };
            
            // 再添加
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = button.bounds;
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius = button.TKHeight / 2.0f;
            [button.layer insertSublayer:btoGradientLayer atIndex:0]; //设置颜色渐变
            
            button.layer.cornerRadius = button.TKHeight / 2.0f;
        }
    }
}


#pragma mark TKPlayerToolViewDelegate

//重新播放按钮事件
-(void)replayVideoBtnAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(replayVideo)]) {
        [self.delegate replayVideo];
    }
}

//暂停播放按钮事件
-(void)pauseVideoBtnAction{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(playVideo)]) {
        [self.delegate playVideo];
    }
}


#pragma mark  set or get
/**
 <AUTHOR>
 @修改播放状态页面
 */
-(void)changePlayStatus:(BOOL)isPlay{
    if (!isPlay) {
        [self.playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        self.playTipLabel.text = @"点击预览";
    }else{
        [self.playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_stop.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        self.playTipLabel.text = @"点击暂停";
    }
    
    // 设置剩余的播放时间
    self.secondsString = self.secondsString;
}
/**
 <AUTHOR>
 @结果页类型set方法
 */
-(void)setEndType:(TKOneWayVideoEndType)endType{
    
    //移除所有子视图
    [[self subviews] makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self setBackgroundColor:TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE];
    if (endType==TKOneWayVideoEndTypeNormal) {
        [self viewInit];
    }else if(endType==TKOneWayVideoEndTypeNoVoiceError){
        [self viewErrorInit:@"回答不通过，本次视频见证失败，您可重新发起录制。"];
        [self warningTitleTipString:@"回答不通过"];
    }else if(endType==TKOneWayVideoEndTypeWrongAnswerError){
        [self viewErrorInit:@"回答不通过，本次视频见证失败，您可重新发起录制。"];
        [self warningTitleTipString:@"回答不通过"];
    }else if(endType==TKOneWayVideoEndTypeFaceDetectError){
        [self viewErrorInit:@"由于长时间未检测到面部在框，本次视频录制失败，请重新录制。"];
        [self warningTitleTipString:@"未检测到面部在框"];
    }else if(endType==TKOneWayVideoEndTypeFaceCompareError){
        [self viewErrorInit:@"人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。"];
        [self warningTitleTipString:@"人脸识别不通过"];
    }
}

/**
 <AUTHOR>
 @视频展示示例图赋值
 */
- (void)setVideoShowImg:(UIImage *)videoShowImg {
    _videoShowImg=videoShowImg;
    [self.videoShowImgView setImage:videoShowImg];
}

/**
 <AUTHOR>
 @视频展示示例图赋值
 */
-(void)setSecondsString:(NSString *)secondsString{
    //    self.secondsLabel.text=secondsString;
    //    CGSize labelSize=[self.secondsLabel sizeThatFits:CGSizeMake(self.TKWidth, MAXFLOAT)];
    //    float secondsLabelX=self.videoShowBgView.TKWidth-labelSize.width-7;
    //    float secondsLabelY=self.videoShowBgView.TKHeight-labelSize.height-4;
    //    self.secondsLabel.frame=CGRectMake(secondsLabelX, secondsLabelY, labelSize.width, labelSize.height);
        
        _secondsString = secondsString;
        
        int recordTime = [secondsString intValue];
        NSDate *d = [NSDate dateWithTimeIntervalSince1970:recordTime];
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
        if (recordTime/3600 >= 1) {
            [formatter setDateFormat:@"HH:mm:ss"];
        } else {
              [formatter setDateFormat:@"mm:ss"];
        }
        if (self.isPlay) {
            self.playTipLabel.text=[NSString stringWithFormat:@"点击暂停 %@",[formatter stringFromDate:d]] ;
        }else{
            self.playTipLabel.text=[NSString stringWithFormat:@"点击预览 %@",[formatter stringFromDate:d]] ;
        }
}

/// 根据tip更新UI
/// @param tipArr tip数组。格式是：@[@{@"tipImage" : @"", @"tipContent" : @""}];
- (void)updateTipViewWithTipArr:(NSArray *)tipArr {
    
    if (![tipArr isKindOfClass:NSArray.class] || tipArr.count == 0) {
        [self addSubview:self.smallTipLabel];
        [self addSubview:self.smallTipLeftImgView];
        //修改小字提示语x坐标
        [self.smallTipLabel setFrameX:self.smallTipLeftImgView.TKRight+9];
        return;
    }else{
        [self addSubview:self.smallTipView];
    }

    
    // 先移除
    if (self.smallTipView.subviews.count) {
        [self.smallTipView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    }
    
    NSString *tipImage = nil;
    NSString *tipContent = nil;
    CGFloat x = 0;
    CGFloat y = 0;
    CGFloat width = self.smallTipView.TKWidth;
    CGFloat height = 16;
    for (int i = 0; i < tipArr.count; i++) {
        NSDictionary *dic = tipArr[i];
        if ([dic isKindOfClass:NSDictionary.class]) {
            tipImage = dic[@"tipImage"];
            tipContent = dic[@"tipContent"];
            
            // 创建UI
            TKOpenTipView *tipView = [[TKOpenTipView alloc] initWithFrame:CGRectMake(x, y, width, height)];
            [tipView updateUIWithImage:tipImage title:tipContent];
            tipView.label.textColor = [TKUIHelper colorWithHexString:@"#D8D8D8"];
            [self.smallTipView addSubview:tipView];
            
            y = y + tipView.frame.size.height + 12;
        }
    }
    
    // 重新调整smallTipView高度
//    self.smallTipView.TKHeight = y - 15;
//    self.smallTipView.TKTop = CGRectGetMinY(self.resetBtn.frame) - self.smallTipView.TKHeight - 40;
//
    // 重新布局
//    self.bigTipLabel.TKTop = self.smallTipView.frame.origin.y - 20 -  self.bigTipLabel.TKHeight;
    
//    // 重新创建图片控件
//    UIImage *image = self.videoShowImgView.image;
//    [self.videoShowBgView removeFromSuperview];
//    self.videoShowBgView = nil;
//    [self.videoShowImgView removeFromSuperview];
//    self.videoShowImgView = nil;
//    [self.playTipLabel removeFromSuperview];
//    self.playTipLabel = nil;
//    [self.playVideoBtn removeFromSuperview];
//    self.playVideoBtn = nil;
//    [self addSubview:self.videoShowBgView];
//
//    BOOL isHidden = self.playVideoBtn.hidden;
//    [self.videoShowBgView addSubview:self.playVideoBtn];
//    [self.videoShowBgView addSubview:self.playTipLabel];
//    self.videoShowImg = image;
//    [self showLoadingVideo:isHidden];
}

/**
 @是否展示正在加载视频。YES-展示；NO-隐藏
 */
- (void)showLoadingVideo:(BOOL)isShow {
    if (isShow) {
        
        self.playVideoBtn.hidden = YES;
        self.playTipLabel.hidden = YES;
    } else {
        self.playVideoBtn.hidden = NO;
        self.playTipLabel.hidden = NO;
    }
}

/**
@Auther Vie 2022年11月10日16:44:19
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
-(void)playTime:(float)currentTime longestTime:(float)longestTime{
    [self.playTipLabel setHidden:YES];
    [self.playVideoBtn setHidden:YES];
    if (!_tkPlayerToolView) {
        [self.videoShowImgView addSubview:self.tkPlayerToolView];
    }else{
        [self.tkPlayerToolView setHidden:NO];
    }
    
    [self.tkPlayerToolView playTime:currentTime longestTime:longestTime];
}


/**
@Auther Vie 2022年11月10日16:54:16
@暂停播放
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
-(void)stopPlay:(float)currentTime longestTime:(float)longestTime{
    [self.playTipLabel setHidden:NO];
    [self.playVideoBtn setHidden:NO];
    [self.tkPlayerToolView stopPlay:currentTime longestTime:longestTime];
}

#pragma mark  事件
/**
 <AUTHOR>
 @返回点击事件
 */
-(void)backAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(endGoBack)]) {
        [self.delegate endGoBack];
    }
}

/**
 <AUTHOR>
 @播放点击事件
 */
-(void)playAction:(UIButton *)sender{
    
    if (self.delegate&&[self.delegate respondsToSelector:@selector(playVideo)]) {
        [self.delegate playVideo];
    }
}

/**
 <AUTHOR>
 @重试点击事件
 */
-(void)resetAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(endReste)]) {
        [self.delegate endReste];
    }
}

/**
 <AUTHOR>
 @提交点击事件
 */
-(void)submitAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(endSubmit)]) {
        [self.delegate endSubmit];
    }
}
/**
 <AUTHOR>
 @修改错误标题提示
 */
-(void)warningTitleTipString:(NSString *)string{
    self.warningTitleTipLabel.text=string;
}

/**
 <AUTHOR>
 @修改错误提示语
 */
-(void)warningTipString:(NSString *)string{
    self.warningTipLabel.text = string;
    CGSize labelSize=[self.warningTipLabel sizeThatFits:CGSizeMake(self.TKWidth-100, MAXFLOAT)];
    float warningTipLabelX=(self.TKWidth-labelSize.width)/2;
    float warningTipLabelY=self.warningTitleTipLabel.TKBottom+12;
    self.warningTipLabel.frame=CGRectMake(warningTipLabelX, warningTipLabelY, labelSize.width, labelSize.height);
}

#pragma makr - lazyloading
/**
 <AUTHOR> 2019年04月13日12:57:01
 @初始化懒加载背景视图
 @return 背景视图
 */
-(UIImageView *)bgImgView{
    if (!_bgImgView) {
        _bgImgView=[[UIImageView alloc] initWithFrame:self.frame];
    }
    return _bgImgView;
}

/**
 <AUTHOR> 2019年04月13日11:36:47
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnX = 20;
        if (IPHONEX_SAFEAREAINSETS_TOP > 0) {
            backBtnX += IPHONEX_SAFEAREAINSETS_TOP;
        } else {
            backBtnX += 20; // X以下机型，如果隐藏状态栏，IPHONEX_SAFEAREAINSETS_TOP为0，需要加上状态高度
        }
        float backBtnY = 30;
        float backBtnWidth=32;
        float backBtnheight=32;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_end_back.png", TK_OPEN_RESOURCE_NAME]];
        [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(4, -2, 4, 4)]; // 图片往右偏了，需要往左偏回来
        
        //设计稿返回按钮颜色固定
        
        img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
//        [_backBtn setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        [_backBtn setTintColor:[TKUIHelper colorWithHexString:@"#FFFFFF"]];
        [_backBtn setImage:img forState:UIControlStateNormal];
        
        _backBtn.titleLabel.font=[UIFont fontWithName:@"PingFangSC-Medium" size:17];
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}

/**
 <AUTHOR> 2019年04月17日20:28:31
 @初始化懒加载视频展示人像视图背景
 @return 视频展示人像视图背景
 */
-(UIView *)videoShowBgView{
    if (!_videoShowBgView) {
        float videoShowBgViewX = self.backBtn.TKBottom + 30;
        float videoShowBgViewY = self.backBtn.TKBottom + 9;
        float videoShowBgViewHeight = self.resetBtn.TKTop - 14 - videoShowBgViewY;
        float videoShowBgViewWidth = videoShowBgViewHeight / 3 * 4;
        
        _videoShowBgView = [[UIView alloc] initWithFrame:CGRectMake(videoShowBgViewX, videoShowBgViewY, videoShowBgViewWidth, videoShowBgViewHeight)];
        _videoShowBgView.layer.cornerRadius = 3.0f;
        _videoShowBgView.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.08];
        
        UIImageView *imgView = [[UIImageView alloc] initWithFrame:_videoShowBgView.bounds];
//        imgView.layer.cornerRadius = 3.0f;
//        imgView.layer.masksToBounds = YES;
        
//        UIView *shadowView = [[UIView alloc] initWithFrame:imgView.frame];
//        shadowView.backgroundColor = TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE;
//        shadowView.layer.cornerRadius = 3.0f;
//        //添加四个边阴影
//        shadowView.layer.shadowColor = [UIColor grayColor].CGColor;//阴影颜色
//        shadowView.layer.shadowOffset = CGSizeMake(0, 0);//偏移距离
//        shadowView.layer.shadowOpacity = 0.5;//不透明度
//        shadowView.layer.shadowRadius = 5.0;//半径
//
//        [_videoShowBgView addSubview:shadowView];
        [_videoShowBgView addSubview:imgView];
        [imgView setImage:self.videoShowImg];
        imgView.userInteractionEnabled = true;
        self.videoShowImgView = imgView;
        
    }
    return _videoShowBgView;
}





-(UIButton *)playVideoBtn{
    if (!_playVideoBtn) {
        _playVideoBtn=[[UIButton alloc] initWithFrame:CGRectMake(0, 0, 44, 44)];
        [_playVideoBtn setFrameY:(self.videoShowBgView.TKHeight-44)/2.0f];
        [_playVideoBtn setFrameX:(self.videoShowBgView.TKWidth-44)/2.0f];
        [_playVideoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_playVideoBtn addTarget:self action:@selector(playAction:) forControlEvents:UIControlEventTouchUpInside];
        _playVideoBtn.layer.zPosition = MAXFLOAT;//总是保持在最上面避免被播放视频的view挡住
    }
    return _playVideoBtn;
}


/**
 <AUTHOR> 2019年04月26日17:31:53
 @初始化懒加载视频播放按钮底部提示文字
 @return 视频播放按钮底部提示文字
 */
-(UILabel *)playTipLabel{
    if (!_playTipLabel) {
        _playTipLabel=[[UILabel alloc] init];
        _playTipLabel.text = @"点击预览";
        _playTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        _playTipLabel.textColor = [UIColor colorWithWhite:1 alpha:0.77];
        _playTipLabel.textAlignment=NSTextAlignmentCenter;
        float x=(self.videoShowBgView.TKWidth-self.videoShowImgView.TKWidth)/2.0f;
        _playTipLabel.frame=CGRectMake(x, 0, self.videoShowImgView.TKWidth, 22);
        [_playTipLabel setFrameY:self.playVideoBtn.TKBottom+17];
        _playTipLabel.layer.zPosition = MAXFLOAT;//总是保持在最上面避免被播放视频的view挡住
    }
    return _playTipLabel;
}

/**
 <AUTHOR> 2019年04月13日13:40:45
 @初始化懒加载大字的提示语
 @return 大字的提示语
 */
-(UILabel *)bigTipLabel{
    if (!_bigTipLabel) {
        _bigTipLabel = [[UILabel alloc] init];
        _bigTipLabel.text = @"请确认影像和声音正确后提交";
        _bigTipLabel.font = [UIFont fontWithName:@"PingFangSC-Semibold" size:18];
        _bigTipLabel.textColor = TK_ONEVIEW_TIP_LABEL_END_COLOR_WHITE;
        
        CGSize labelSize = [_bigTipLabel sizeThatFits:CGSizeMake(MAXFLOAT, 25)];
        float bigTipLabelX = self.videoShowBgView.TKLeft + (self.videoShowBgView.TKWidth - labelSize.width) / 2;
        float bigTipLabelY = self.videoShowBgView.TKTop - 7 - labelSize.height;
        _bigTipLabel.frame = CGRectMake(bigTipLabelX, bigTipLabelY, labelSize.width, labelSize.height);
    }
    return _bigTipLabel;
}


/**
 <AUTHOR> 2019年04月13日13:49:36
 @初始化懒加载小字的提示语
 @return 小字的提示语
 */
-(UILabel *)smallTipLabel{
    if (!_smallTipLabel) {
        _smallTipLabel=[[UILabel alloc] init];
        NSString *textString=@"  头像是否完整\n  语音是否清晰\n  视频里不能出现其他人";
        NSMutableAttributedString * attributedString = [[NSMutableAttributedString alloc] initWithString:textString];
        NSMutableParagraphStyle * paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:12];

        [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, [textString length])];

        [attributedString addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#D8D8D8"] range:NSMakeRange(0, textString.length)];
        [attributedString addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:16] range:NSMakeRange(0, textString.length)];
        
        float smallTipLabelX = self.videoShowBgView.TKRight + 14;
        float smallTipLabelY = self.videoShowBgView.TKTop;
        _smallTipLabel.attributedText=attributedString;
        _smallTipLabel.numberOfLines=0;
        CGSize labelSize=[_smallTipLabel sizeThatFits:CGSizeMake(self.TKWidth - smallTipLabelX - 50, MAXFLOAT)];

        _smallTipLabel.frame=CGRectMake(smallTipLabelX, smallTipLabelY, labelSize.width, labelSize.height);
    }
    return _smallTipLabel;
}

/**
 <AUTHOR> 2019年04月26日17:44:13
 @初始化懒加载小字提示语左边的图案
 @return 小字提示语左边的图案
 */
-(UIImageView *)smallTipLeftImgView{
    if (!_smallTipLeftImgView) {
        _smallTipLeftImgView = [[UIImageView alloc] initWithFrame:CGRectMake(self.smallTipLabel.TKLeft, self.smallTipLabel.TKTop + 10 , 16, self.smallTipLabel.TKHeight-20)];


        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_end_point_pic.png", TK_OPEN_RESOURCE_NAME]];

        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_smallTipLeftImgView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        }
        [_smallTipLeftImgView setImage:img];
    }
    return _smallTipLeftImgView;
}


- (UIView *)smallTipView {
    if (!_smallTipView) {
        float smallTipViewX = self.videoShowBgView.TKRight + 14;
        _smallTipView = [[UIView alloc] initWithFrame:CGRectMake(smallTipViewX, self.videoShowBgView.TKTop, self.TKWidth - smallTipViewX - 50, self.videoShowBgView.TKHeight)];
    }
    
    return _smallTipView;
}

/**
 <AUTHOR> 2019年04月13日14:01:51
 @初始化懒加载重新录制按钮
 @return 重新录制按钮
 */
-(UIButton *)resetBtn{
    if (!_resetBtn) {
        CGPoint point = self.center;
        float resetBtnWidth = 214;
        float resetBtnX = point.x - resetBtnWidth - 14;
        float resetBtnHeight = 44;
        float resetBtnY = self.TKHeight - 13 - resetBtnHeight;
        
        _resetBtn=[[UIButton alloc] initWithFrame:CGRectMake(resetBtnX, resetBtnY, resetBtnWidth, resetBtnHeight)];
        
        [_resetBtn setTitle:@"重新录制" forState:UIControlStateNormal];
        _resetBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        
        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
            [_resetBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]] forState:UIControlStateNormal];
            _resetBtn.layer.borderColor = [TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]].CGColor;
        }else{
            [_resetBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
            _resetBtn.layer.borderColor = [TKUIHelper colorWithHexString:@"#FFFFFF"].CGColor;
        }
        _resetBtn.layer.cornerRadius = resetBtnHeight/2.0f;
        _resetBtn.layer.borderWidth = 1;
        [_resetBtn addTarget:self action:@selector(resetAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _resetBtn;
}

/**
 <AUTHOR> 2019年04月13日14:02:47
 @初始化懒加载确认提交按钮
 @return 确认提交按钮
 */
-(UIButton *)submitBtn{
    if (!_submitBtn) {
        CGPoint point = self.center;
        float submitBtnX = point.x + 14;
        float submitBtnWidth = self.resetBtn.TKWidth;
        float submitBtnHeight = self.resetBtn.TKHeight;
        float submitBtnY = self.resetBtn.TKTop;
        _submitBtn=[[UIButton alloc] initWithFrame:CGRectMake(submitBtnX, submitBtnY, submitBtnWidth, submitBtnHeight)];

        [_submitBtn setTitle:@"确认提交" forState:UIControlStateNormal];
        _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        [_submitBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];

        [_submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
        
        _submitBtn.layer.cornerRadius=submitBtnHeight/2.0f;
    }
    return _submitBtn;
}

/**
 <AUTHOR> 2019年04月13日14:55:40
 @初始化懒加载语音识别失败警告图
 @return 语音识别失败警告图
 */
-(UIImageView *)voiceWarningImgView{
    if (!_voiceWarningImgView) {
        
        CGPoint point = self.center;
        float voiceWarningImgViewWidth = 70;
        float voiceWarningImgViewX = (self.TKWidth - voiceWarningImgViewWidth) * 0.5;
        float voiceWarningImgViewY = point.y - 70 - 5;
        
        _voiceWarningImgView=[[UIImageView alloc] initWithFrame:CGRectMake(voiceWarningImgViewX, voiceWarningImgViewY, voiceWarningImgViewWidth, voiceWarningImgViewWidth)];
        [_voiceWarningImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_voice_warning.png", TK_OPEN_RESOURCE_NAME]]];
        
    }
    return _voiceWarningImgView;
}

/**
 <AUTHOR> 2019年04月13日15:04:44
 @初始化懒加载识别失败标题
 @return 识别失败标题
 */
-(UILabel *)warningTitleTipLabel{
    if (!_warningTitleTipLabel) {
        
        CGPoint point = self.center;
        float widht = self.TKWidth;
        float height = 27;
        float y = point.y + 15;
        
        _warningTitleTipLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, y, widht, height)];
        _warningTitleTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:24];
        
        _warningTitleTipLabel.textColor = [TKUIHelper colorWithHexString:@"#FF4848"];
        _warningTitleTipLabel.numberOfLines=0;
        _warningTitleTipLabel.textAlignment=NSTextAlignmentCenter;
    }
    return _warningTitleTipLabel;
}

/**
 <AUTHOR> 2019年04月13日15:04:44
 @初始化懒加载语音识别失败提示语
 @return 语音识别失败提示语
 */
-(UILabel *)warningTipLabel{
    if (!_warningTipLabel) {
        _warningTipLabel=[[UILabel alloc] init];
        _warningTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _warningTipLabel.textColor = [TKUIHelper colorWithHexString:@"#666666"];
        _warningTipLabel.numberOfLines=0;
        _warningTipLabel.textAlignment=NSTextAlignmentCenter;
    }
    return _warningTipLabel;
}


/**
 <AUTHOR> 2019年04月13日14:02:47
 @初始化懒加载语音识别失败返回按钮
 @return 语音识别失败返回按钮
 */
-(UIButton *)warningBackBtn{
    if (!_warningBackBtn) {
        float warningBackBtnWidth = 214;
        float warningBackBtnHeight = 44;
        float warningBackBtnX = (self.TKWidth - warningBackBtnWidth) * 0.5;
        float warningBackBtnY = self.TKHeight - 13 - warningBackBtnHeight;
        _warningBackBtn=[[UIButton alloc] initWithFrame:CGRectMake(warningBackBtnX, warningBackBtnY, warningBackBtnWidth, warningBackBtnHeight)];

        [_warningBackBtn setTitle:@"重新录制" forState:UIControlStateNormal];
        [_warningBackBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _warningBackBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        
        _warningBackBtn.layer.cornerRadius=warningBackBtnHeight/2.0f;
        
        [_warningBackBtn addTarget:self action:@selector(resetAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _warningBackBtn;
}


/**
 <AUTHOR> 2019年04月18日09:39:25
 @初始化懒加载视频多少秒展示
 @return 视频多少秒展示
 */
-(UILabel *)secondsLabel{
    if (!_secondsLabel) {
        _secondsLabel=[[UILabel alloc] init];
        _secondsLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _secondsLabel.textColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1/1.0];
    }
    return _secondsLabel;
}

- (void)setPlayerControlView:(TKPlayerControlView *)playerControlView {
    if (_playerControlView != playerControlView) {
        _playerControlView = playerControlView;
        [self.videoShowBgView addSubview:playerControlView];
    }
}

/**
 <AUTHOR> 2022年11月11日09:33:54
 @初始化懒加载视频暂停重播工具视图
 @return 视频暂停重播工具视图
 */
-(TKPlayerToolView *)tkPlayerToolView{
    if (!_tkPlayerToolView) {
        float x=0;
        float height=52;
        float y=self.videoShowImgView.TKHeight-height;
        float width=self.videoShowImgView.TKWidth;
        _tkPlayerToolView=[[TKPlayerToolView alloc] initWithFrame:CGRectMake(x, y, width, height) requestParams:self.requestParam];
        _tkPlayerToolView.delegate=self;
    }
    return _tkPlayerToolView;
}

@end
