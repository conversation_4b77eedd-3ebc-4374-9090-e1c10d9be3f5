//
//  TKBaseVideoRecordView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/8/31.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "TKBaseVideoRecordViewProtocol.h"
#import "TKFaceDectTipView.h"

NS_ASSUME_NONNULL_BEGIN

@interface TKBaseVideoRecordView : UIView<TKBaseVideoRecordViewProtocol>
{
    UIButton *_takeBtn;
    UILabel *_warningLabel;
    UILabel *_recordTimeLabel;
}

@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）

@property (nonatomic, strong) UIView *topView,*bottomView,*leftView,*rightView;//顶部遮罩层,底部遮罩层,左部遮罩层，右部遮罩层
@property (nonatomic, strong) UIImageView *boxImgView;//人像取景框
@property (nonatomic, strong) UIImageView *boxImgBackgroundView;//人像取景背景框
@property (nonatomic, strong) UIView *topBgView;//顶部底层渐变图
@property (nonatomic, strong) UIView *bottomBgView;//底部底层渐变图
@property (nonatomic, readwrite, strong) UIView *badgeView; // 红点
@property (nonatomic, strong) UIButton *backBtn;//返回按钮
@property (nonatomic, strong)  UIView *bottomShowTipLineView;//底部文档等提示展示区域横线
@property (nonatomic, strong) UIView *bottomShowTipRecordLineView;//录制时长占比展示线
@property (nonatomic, strong) UIView *bottomShowTipView;//底部文档等提示展示区域
@property (nonatomic, strong) UITextView *bottomShowLabel;//底部文字展示
@property (nonatomic, strong) UIImageView *serviceBg;//小机器人图标背景图
@property (nonatomic, strong) TKGIFImageView *serviceGifView;//小机器人动作图
@property (nonatomic, strong) UILabel *recordTimeLabel;//录制时长
@property (nonatomic, strong) UILabel *countDownLabel;//倒计时展示label
@property (nonatomic, strong) UIButton *endBtn;//结束录制按钮
@property (nonatomic, assign) int colorWordsNum;//变色的字数
@property (nonatomic, readwrite, strong) TKFaceDectTipView *faceDectTipView; // 质检错误提示框
@property (nonatomic, readwrite, assign) TKCountDownType countDownType; // 倒计时类型
@property (nonatomic, readwrite, assign) BOOL hasAsrResult; // 是否已有asr结果
@property (nonatomic, assign) int changeReadTextViewWords; // 变色的字数
@property (nonatomic, readwrite, copy) NSAttributedString *currentOriginHtmlStr; // 当前正在播放音频对应的Html字符串
@property (nonatomic, readwrite, strong) NSTimer *changeReadTextViewOffSetTimer; // testView滚动定时器
@property (nonatomic, assign) float answerCount;//回答倒计时默认5秒
@property (nonatomic, assign) BOOL isFinished;//是否回答了问题

/// 创建view方法，支持子类重写
- (void)viewInit;

/// 供子类重写
- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow;

@end

NS_ASSUME_NONNULL_END
