//
//  TKBaseVideoRecordViewProtocol.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/3/24.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKRecordManagerProtocol.h"
#import "TKReadingView.h"

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    TKCountDownTypeUnknown,
    TKCountDownTypeAnswer,  // 回答倒计时
    TKCountDownTypeUserAction,  // 用户行为倒计时
} TKCountDownType;



//单向视频视图代理，按钮等事件
@protocol TKBaseVideoRecordViewDelegate <NSObject>

/**
 <AUTHOR> 2019年04月08日10:38:08
 @返回代理
 */
-(void)goBack;

/**
 <AUTHOR> 2020年08月26日11:10:59
 @开始录制视频
 */
-(void)takeRecord;

/**
 <AUTHOR> 2023年02月17日17:05:20
 @切换摄像头
 */
- (void)switchVideoCamera;

@optional
/**
 <AUTHOR> 2019年04月26日19:07:28
 @回答问题倒计时结束
 */
-(void)answerCountDownEnd;

/**
 <AUTHOR> 2021年03月12日18:29:43
 @录制倒计时结束
 */
-(void)recordCountDownEnd;

/// 点击继续按钮，执行下一步
- (void)nextAction;

/**
 <AUTHOR> 2019年04月26日19:07:28
 @等待用户动作倒计时结束
 */
- (void)userActionCountDownEnd;

/**
 <AUTHOR> 2020年02月28日10:47:29
 @结束录制代理。目前只有普通单向才会有该回调。其他都是直接结束录制
 */
- (void)endRecord;

@end


@protocol TKBaseVideoRecordViewProtocol <NSObject>

@property (nonatomic, assign) CGRect boxRect;//人像取景框矩阵
@property (nonatomic, weak) id<TKBaseVideoRecordViewDelegate> delegate;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
//@property (nonatomic, strong) TKOpenSpectrumView *spectrumView;//语音播放频谱动画图

@property (nonatomic, strong) UIButton *_Nullable takeBtn;//开始录制按钮
@property (nonatomic, strong) UILabel * _Nullable answerPromptLabel;//语音回答结果提示
@property (nonatomic, strong) UIView * _Nullable answerPromptImgBg;//语音回答旁边小图标
@property (nonatomic, strong) UIButton *_Nullable nextBtn; //下一步按钮
@property (nonatomic, readwrite, assign) BOOL isReadVideo;   // 是否朗读视频模式

/// 构造方法
/// @param frame frame
/// @param param 参数
- (instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param;

/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
- (void)liveWarning:(NSString *)warningSting forceDisplay:(BOOL)forceDisplay;
- (void)liveWarning:(NSString * _Nullable )warningSting;


/**
 <AUTHOR> 2019年04月16日20:09:51
 @语音合成播放完成，修改界面
 */
- (void)playEndView:(int)waitTime prompt:(NSString *_Nullable)string noAnswerPromptTime:(int)noAnswerPromptTime;


///  <AUTHOR> 18:13:29
///  @视频录制中播放的语音要修改图界面展示波动图
/// @param questionString 展示的文案
/// @param isOneLineShow 是否1行展示
/// @param flag 是否是html文本
/// @param questionOneWordSpeed 文字滚动速度。多少字/0.1s
/// @param autoScroll 是否自动滚动
- (void)startRecorderVideoPlay:(NSString *)questionString isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed autoScroll:(BOOL)autoScroll;

/// 回答完毕（识别到了是或否）
/// @param isAnswered 是否回答
/// @param displayTime 回答结果展示时长
- (void)answered:(BOOL)isAnswered displayTime:(int)displayTime;

/**
<AUTHOR> 2020年01月07日19:02:54
@语音问题播放完成停止波动动画
*/
- (void)playEndVoiceView;

/// 设置是否可以点击开始录制
/// @param isEnable 是否可以点击
- (void)enableTakeRecord:(BOOL)isEnable;

/**
 <AUTHOR> 2021年07月08日16:15:38
 @语音识别过程中识别到的回答小字提示
 @param回答正确，无用回答
 */
- (void)answerPromptType:(BOOL)flag identifyString:(NSString *)string;

/**
@Auther Vie 2020年03月10日14:46:52
@隐藏中间准备提示文本
*/
- (void)hideWaitTip;

/// 设置是否可以点击结束录制
/// @param isEnable 是否可以点击
- (void)enableFinishTakeRecord:(BOOL)isEnable;

/**
<AUTHOR> 2021年03月12日173704
@最长录制时间倒计时
*/
- (void)recordLongTime:(int)recordTime startRecord:(BOOL)startRecord;

/**
@Auther Vie 2021年03月08日13:19:01
@阅读文本引导
*/
-(void)readingGuideTip;

/**
 <AUTHOR> 2023年06月26日14:25:22
 @初始化单向视频朗读默认界面
 */
-(void)showReadTextView;

/**
@Auther Vie 2020年02月28日12:48:01
@param recordTime 当前录制时间
@param longestTime 最长录制时间
*/
-(void)recordTime:(int)recordTime longestTime:(int)longestTime;

/**
@Auther Vie 2020年03月10日14:43:37
@param showWaitTip 中间准备提示文本
*/
-(void)showWaitTip:(NSString *)string;

/// 展示无回答提示
- (void)showNoAnswerPrompt;

/**
<AUTHOR> 2021年07月08日10:30:58
@修改提示语问题话术
@string 文本
@colorString 文本颜色
@cornerRadius 背景框圆角
@flag 是否是播放语音话术（y坐标不一样要调整）
@flag 是否是html文本
@return 顶部遮罩层
*/
- (void)updateTipLabel:(NSString *)string textColor:(NSString *)colorString cornerRadius:(CGFloat)cornerRadius isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed autoScroll:(BOOL)autoScroll;

/// 更新Textview frame
/// @param string string
/// @param colorString colorString
/// @param isOneLineShow isOneLineShow
- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow;

/// 自动录制
/// @param countDown 倒计时，若为0则不显示倒计时
- (void)atuoStartTakeRecord:(int)countDown;

/// 点击录制按钮
/// @param sender UIButton
- (void)takeAction:(UIButton *)sender;

@optional

@property (nonatomic, strong) UIView *avPreviewView;//视频预览视图
@property (nonatomic, strong) UIView *virtualAvPreviewView;//虚拟坐席预览视图


/// 展示加载层（加tk前缀，避免别人写的分类导致问题）
- (void)tkShowLoading;

/// 隐藏加载层（加tk前缀，避免别人写的分类导致问题）
- (void)tkHideLoading;

/// 更新网络信息
/// @param sendBps 上行速率
/// @param recvBps 下行速率
//- (void)updateNetworkInfo:(int)sendBps recvBps:(int)recvBps;

/// 更新网络信息
/// @param sendBps 上行速率
/// @param recvBps 下行速率
- (void)updateNetworkInfo:(TKChatVideoRecordNetworkStatus)status statusString:(NSString *)statusString;

/// 是否展示录制按钮
/// @param isShow 是否展示
- (void)showTakeRecordBtn:(BOOL)isShow;

/**
@Auther Vie 2021年07月28日16:40:17
@param currentNum 当前进度，
@param allNum 总进度数目；问题n，结束语1；n+1
*/
- (void)currentNum:(int)currentNum allNum:(int)allNum;

/**
<AUTHOR> 2021年03月12日173704
@最长录制时间倒计时
*/
- (void)recordLongTime:(int)recordTime startRecord:(BOOL)startRecord;

/// 活体继续识别
/// @param string string
/// @param htmlFlag 是否是html string
- (void)liveContinue:(NSString *)string isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed;
- (void)liveContinue:(NSString *)string;

/**
 <AUTHOR> 2019年05月23日13:37:30
 @活体完成话术播报等待界面
 */
- (void)liveEndWait:(NSString *)str;
- (void)liveEndWait;

///// 是否展示录制按钮
///// @param isShow 是否展示
/// @param btnTitle 按钮文案，传Nil展示默认的“继续播报”
- (void)showNextBtn:(BOOL)isShow btnTitle:(NSString  * _Nullable)btnTitle;

/// 展示用户动作提示文案，等待用户做动作
/// @param originString 待展示的文案
/// @param htmlFlag 是否是html标签
/// @param waitTime 等待时间
- (void)showUserActionPrompt:(NSString *)originString isHtmlString:(BOOL)htmlFlag waitTime:(int)waitTime questionOneWordSpeed:(NSString *)questionOneWordSpeed;

/// 展示阅读文案
///   - content: 阅读内容
///   - countdownTime: 倒计时时长
/// @param type 类型
/// @param readTitle 标题
/// @param readConfirmBtnTitle 确认按钮标题
- (void)showWithContent:(NSString *)content countdownTime:(NSInteger)countdownTime type:(TKReadingType)type readTitle:(NSString *)readTitle readConfirmBtnTitle:(NSString *)readConfirmBtnTitle  oneWordSpeed:(NSString *)oneWordSpeed;

/// 滚动播报内容
/// @param startIndex 开始文字角标
/// @param endIndex 结束文字角标
/// @param duration 播放用时
- (void)scrollBottomShowLabel:(int)startIndex endIndex:(int)endIndex duration:(NSTimeInterval)duration;


/**
@Auther Vie 2025年02月11日10:31:04
@问题回答提示语
*/
- (void)changeAnswerLabel:(NSString *)string;
@end

NS_ASSUME_NONNULL_END
