//
//  TKSmartQuestionModel.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/11/1.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKSmartQuestionModel.h"

//@property (nonatomic, readwrite, strong) NSString *tipTitle;    // 优先展示的文案，存在多音字的发音提示

@implementation TKSmartQuestionModel

/// 构造方法
/// @param dic 字典
- (instancetype)initWithDic:(NSDictionary *)dic {
    if (self = [super init]) {
        self.fileSource = [dic getStringWithKey:@"fileSource"];
        self.fileName = [dic getStringWithKey:@"fileName"];
        self.tipContent = [dic getStringWithKey:@"tipContent"];
        
        self.tipContentList = (NSArray *)[dic getObjectWithKey:@"tipContentList"];
        if (![self.tipContentList isKindOfClass:NSArray.class] || self.tipContentList.count == 0) {
            self.tipContentList = nil;
        }
        
        self.tipTitleStr = [dic getStringWithKey:@"tipTitleStr"];
        self.tipTitleHtml = [dic getStringWithKey:@"tipTitleHtml"];
        if ([TKStringHelper isEmpty:self.tipTitleHtml]) {
            // 兼容旧版本字段
//            self.tipTitle = [dic getStringWithKey:@"tipTitle"];
            self.tipTitleHtml = [dic getStringWithKey:@"tipTitle"];
        }
        self.promptStr = [dic getStringWithKey:@"promptStr"];
        self.promptHtml = [dic getStringWithKey:@"promptHtml"];
        if ([TKStringHelper isEmpty:self.promptHtml]) {
            // 兼容旧版本字段
            // 建投虚拟人用的是promtp，内容是html。所以要做兼容处理
//            self.prompt = [dic getStringWithKey:@"prompt"];
            self.promptHtml = [dic getStringWithKey:@"prompt"];
        }
        
        self.pauseTime = [dic getIntWithKey:@"pauseTime"];
        self.longPauseTime = [dic getStringWithKey:@"longPauseTime"];
        self.longPauseBtn = [dic getStringWithKey:@"longPauseBtn"];
        self.failans = [dic getStringWithKey:@"failans"];
        self.standardans = [dic getStringWithKey:@"standardans"];
        self.waitTime = [dic getStringWithKey:@"waitTime"];
        self.noAnswerPromptTime = [dic getStringWithKey:@"noAnswerPromptTime"];
        self.fileFlag = [dic getStringWithKey:@"fileFlag"];
        
        self.type = (TKRecordType)[dic getIntWithKey:@"type"];
        self.readPromptBtnTitle = [dic getStringWithKey:@"readPromptBtnTitle"];
        self.readContent = [dic getStringWithKey:@"readContent"];
        self.readConfirmBtnTitle = [dic getStringWithKey:@"readConfirmBtnTitle"];
        self.readTitle = [dic getStringWithKey:@"readTitle"];
        self.readTime = [dic getStringWithKey:@"readTime"];
        
        self.tipSpeed = [dic getStringWithKey:@"tipSpeed"];
        self.wordSpeed = [dic getStringWithKey:@"questionOneWordSpeed"];
        self.wordSpeed = [TKStringHelper isEmpty:self.wordSpeed] ? [dic getStringWithKey:@"wordSpeed"] : self.wordSpeed;
        self.voiceUrl = [dic getStringWithKey:@"voiceUrl"];
        self.isNeedVoiceToken = [dic getBoolWithKey:@"isNeedVoiceToken"];
        self.tipPlayitem = [dic getStringWithKey:@"tipPlayitem"];
        
        self.errorReason = [dic getStringWithKey:@"errorReason"];
        
        if (dic[@"errorTip"]) {
            self.errorTipModel = [[TKSmartQuestionModel alloc] initWithDic:(NSDictionary *)[dic getObjectWithKey:@"errorTip"]];
        }
        
        if (dic[@"noVoiceTip"]) {
            self.noVoiceTipModel = [[TKSmartQuestionModel alloc] initWithDic:(NSDictionary *)[dic getObjectWithKey:@"noVoiceTip"]];
        }
        
        if (dic[@"standardansTip"]) {
            self.standardansTipModel = [[TKSmartQuestionModel alloc] initWithDic:(NSDictionary *)[dic getObjectWithKey:@"standardansTip"]];
        }
    }
    
    return self;
}

+ (NSMutableArray *)convertDicArrToModels:(NSArray *)dicArr
{
    if (![dicArr isKindOfClass:NSArray.class]) return @[].mutableCopy;
    
    NSMutableArray *array = [NSMutableArray array];
    for (int i = 0; i < dicArr.count; i++) {
        TKSmartQuestionModel *model = [[self alloc] initWithDic:dicArr[i]];
        [array addObject:model];
    }
    
    return  array;
}

#pragma mark - Selector
/// 是否需要语音识别
- (BOOL)isNeedAsr {
    return ![TKStringHelper isEmpty:self.standardans];
}

- (NSString *)displayTip {
    if (self.isHtmlTip) {
        return _tipTitleHtml;
    } else if ([TKStringHelper isNotEmpty:_tipTitleStr]) {
        return _tipTitleStr;
    } else if (self.tipContentList != nil) {
        NSMutableString *tempStr = [NSMutableString string];
        for (int i = 0; i < self.tipContentList.count; i++) {
            [tempStr appendString:self.tipContentList[i]];
        }
        return tempStr;
    } else {
        return _tipContent;
    }
}

- (NSString *)displayPrompt {
    
    return [TKStringHelper isNotEmpty:_promptHtml] ? _promptHtml : _promptStr;
}

- (BOOL)isHtmlTip{
    return [TKStringHelper isNotEmpty:_tipTitleHtml] ? YES : NO;
}

/// 是否有promptHtml作为html文本
- (BOOL)isHtmlPrompt {
    return [TKStringHelper isNotEmpty:_promptHtml] ? YES : NO;
}

/// 是否需要播放语音识别后的提示
- (BOOL)isNeedPlayAsrResultPrompt {
    
    // 错误回答的提示在视频录制报错环节播放，不在语音识别结果环节播放。
    // 错误回答涉及到马上停止在框检测和录制，和识别成功、没有声音的逻辑不一样。不应该一样处理
    
    // 没有回答重试也不是这里判断。
    // 只有识别通过才播提示音
    return (self.standardansTipModel && self.asrResult == TKAsrResultSuccess);
    
//    return (self.noVoiceTipModel && self.asrResult == TKAsrResultNoIdentify) ||
//           (self.errorTipModel && self.asrResult == TKAsrResultFail) ||
//           (self.standardansTipModel && self.asrResult == TKAsrResultSuccess);
}

- (TKSmartQuestionModel *)asrResultModel {
    if (self.asrResult == TKAsrResultNoIdentify) return self.noVoiceTipModel;
    if (self.asrResult == TKAsrResultFail) return self.errorTipModel;
    if (self.asrResult == TKAsrResultSuccess) return self.standardansTipModel;
    return nil;
}

/// 是否需要暂停
- (BOOL)isNeedPause {
    return [self.longPauseTime isEqualToString:@"1"];
}

/// 是否需要延迟，延迟时间为self.pauseTime
- (BOOL)isNeedDelay {
    return self.pauseTime > 0;
}

- (NSArray *)tipPlayArray {
    if (!_tipPlayArray) {
        // 转字典
        NSDictionary *tipPlayDic = [TKDataHelper jsonToDictionary:self.tipPlayitem];
        
        // 转数组
        NSMutableArray *tepmArray = [NSMutableArray array];
        [tipPlayDic enumerateKeysAndObjectsUsingBlock:^(NSString * _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
            NSMutableDictionary *tempDic = [NSMutableDictionary dictionaryWithDictionary:obj];
            tempDic[@"rangeStart"] = [NSNumber numberWithInteger:key.integerValue];
            [tepmArray addObject:tempDic];
        }];
        
        // 排序
        [tepmArray sortUsingComparator:^NSComparisonResult(NSDictionary * _Nonnull obj1, NSDictionary * _Nonnull obj2) {
            NSNumber *rangeStart1 = obj1[@"rangeStart"];
            NSNumber *rangeStart2 = obj2[@"rangeStart"];
            return [rangeStart1 compare:rangeStart2];
        }];
        
        _tipPlayArray = tepmArray;
    }
    return _tipPlayArray;
}

@end
//// Base
//@property (nonatomic, readwrite, copy) NSString *fileSource;    //文件来源，1:服务器视频文件，2:视频流
//@property (nonatomic, readwrite, copy) NSString *fileName;    //文件来源是服务器视频文件时，传服务器的文件名称
//@property (nonatomic, readwrite, copy) NSString *tipContent;    //语音播报内容和展示文案
//
//// 带问题的
//@property (nonatomic, readwrite, copy) NSString *failans;    //错误回答正则
//@property (nonatomic, readwrite, copy) NSString *standardans;    //正确回答正则
//@property (nonatomic, readwrite, copy) NSString *waitTime;    //语音识别时长
//
//@property (nonatomic, readwrite, copy) TKSmartQuestionModel *errorTipModel;    //错误回答语音模型
//@property (nonatomic, readwrite, copy) TKSmartQuestionModel *noVoiceTipModel;    //无关回答语音模型
//@property (nonatomic, readwrite, copy) TKSmartQuestionModel *standardansTipModel;    //正确回答语音模型


