//
//  TKPlayRangeModel.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/12/25.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKPlayRangeModel.h"

@implementation TKPlayRangeModel

/// 构造方法
/// @param dic 字典
- (instancetype)initWithDic:(NSDictionary *)dic {
    if (self = [super init]) {
        self.rangeStart = [dic getIntegerWithKey:@"rangeStart"];
        self.startEncodeIndex = [dic getIntWithKey:@"s"];
        self.endEncodeIndex = [dic getIntWithKey:@"e"];
        self.startIndex = [dic getIntWithKey:@"s2"];
        self.endIndex = [dic getIntWithKey:@"e2"];
    }
    
    return self;
}

+ (NSMutableArray *)convertDicArrToModels:(NSArray *)dicArr
{
    if (![dicArr isKindOfClass:NSArray.class]) return @[].mutableCopy;
    
    NSMutableArray *array = [NSMutableArray array];
    for (int i = 0; i < dicArr.count; i++) {
        TKPlayRangeModel *model = [[self alloc] initWithDic:dicArr[i]];
        [array addObject:model];
    }
    
    return  array;
}
@end
