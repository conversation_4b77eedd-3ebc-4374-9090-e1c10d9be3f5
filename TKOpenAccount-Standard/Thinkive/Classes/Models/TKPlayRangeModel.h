//
//  TKPlayRangeModel.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/12/25.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TKPlayRangeModel : NSObject

@property (nonatomic, readwrite, assign) NSInteger rangeStart;    // 语音开始的字段位置
@property (nonatomic, readwrite, assign) int startIndex;    // 语音文字开始角标
@property (nonatomic, readwrite, assign) int endIndex;    // 语音文字结束角标

@property (nonatomic, readwrite, assign) int startEncodeIndex;    // 语音文字utf-8编码后的开始角标
@property (nonatomic, readwrite, assign) int endEncodeIndex;    // 语音文字utf-8编码后的结束角标

/// 构造方法
/// @param dic 字典
- (instancetype)initWithDic:(NSDictionary *)dic;

/// 转换字典数组->模型数组
/// @param dicArr 字典数组
+ (NSMutableArray *)convertDicArrToModels:(NSArray *)dicArr;

@end

NS_ASSUME_NONNULL_END
