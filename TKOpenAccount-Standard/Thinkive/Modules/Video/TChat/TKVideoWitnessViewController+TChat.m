//
//  TKVideoWitnessViewController+TChat.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2017/6/12.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "TKVideoWitnessViewController+TChat.h"
#import <TChat/TChatDefine.h>
#import <TChat/TChatErrorCode.h>
#import <TChat/TChatCore.h>

#define WAIT_SEAT_ENTER_ROOM_TIME 15

#define HEIGHT [ [ UIScreen mainScreen ] bounds ].size.height

@interface TKVideoWitnessViewController (private)<TKCCNotifyMessageDelegate,TKCCTransDataDelegate, TKCCTextMsgDelegate, UIAlertViewDelegate>

- (NSMutableDictionary*)requestParams;

- (BOOL)isShowAlert;

- (BOOL)isCancelLineingUp;

- (void)setIsShowAlert:(BOOL)isShowAlert;

- (UIView*)waitView;

- (UIView*)witnessView;

- (NSString*)witRoomId;

- (void)setWitnessResult:(NSString*)witnessResult;

- (NSString*)witnessResult;

- (void)setAExit:(BOOL)aExit;

- (void)stopLineingUp;

- (void)releaseResources;

void TKSwizzle(Class c, SEL org, SEL new);

-(void)startTChatWitness:(NSString*)sUrl withPort:(int)sPort;

-(void)stopTChatWitness;

-(void)uploadLog:(NSString *)logString;

-(NSString *)getTimeStamp;
@end

@implementation TKVideoWitnessViewController (TChat)

int tcSeatId, tcUserId;

BOOL isTCStartingVideo, isTCTransBufferMsg;

NSTimer *waitSeatTimer;

+ (void)load
{
    TKSwizzle([self class], @selector(startTChatWitness:withPort:), @selector(tkStartTChatWitness:withPort:));
    
    TKSwizzle([self class], @selector(stopTChatWitness), @selector(tkStopTChatWitness));
}

-(void)tkStartTChatWitness:(NSString*)sUrl withPort:(int)sPort
{
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    int initFlag=[TChatCore InitSDK];
    //直接返回失败的时候就不走回调了
    if (initFlag!=0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (!self.isShowAlert) {
                
                self.isShowAlert = YES;
                
                UIAlertView *mAlertView = [[UIAlertView alloc] initWithTitle:@"" message:@"初始化异常，请稍侯重试！" delegate:self cancelButtonTitle:@"确定" otherButtonTitles: nil];
                
                mAlertView.tag = 101;
                
                [mAlertView show];
            }
            
        });
    }
    [TChatCore shareTChatCore].notifyMsgDelegate = self;
    [TChatCore shareTChatCore].transDataDelegate = self;
    [TChatCore shareTChatCore].textMsgDelegate = self;
    [TChatCore SetServerAuthPass: @"123456"];
    [TChatCore Connect:sUrl :sPort];
    isTCStartingVideo = NO;
    isTCTransBufferMsg = NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(becomeTChatActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
    
    
    //记录开始连接视频服务器事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1004:网络情况%@|发起连接|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];
    [self uploadLog:logString];
}

/**
 *  <AUTHOR> 2019年09月10日14:03:52
 *  App从后台返回前台
 *  @param notif
 */
-(void)becomeTChatActive:(NSNotification *)notif{
    
    [TChatCore UserVideoControl: -1 : NO];
    [TChatCore UserVideoControl: -1 : YES];
    UIView *uView = [self.witnessView viewWithTag:3001];
    [TChatCore ShowUserVideo:-1 :uView :YES];
}

-(void)tkStopTChatWitness
{
    [self endTChatVideo];
}

/***************** tchat delegate ******************/
// 连接服务器消息
- (void) OnConnect: (BOOL)success : (int)errorCode
{
    //记录开始连接视频服务器返回事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1005:网络情况%@|%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];
    [self uploadLog:logString];
    
    if(errorCode == 0){//连接视频服务器成功
        
        TKLogInfo(@"connect success.");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
            
            linePromptLable.text = @"正在登陆服务器...";
        });
        
        NSString *loginId = [NSString stringWithFormat:@"%@", self.requestParams[@"user_id"]];
        
        [TChatCore Login:loginId : @""];
        
        //记录开始登陆视频服务器
        NSString *logString=[NSString stringWithFormat:@"TKMSG1006:网络情况%@|开始登录服务器|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];
        [self uploadLog:logString];
        
    }else{
        
        TKLogInfo(@"connect fail errorcode:%d",errorCode);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (!self.isShowAlert) {
                
                self.isShowAlert = YES;
                
                UIAlertView *mAlertView = [[UIAlertView alloc] initWithTitle:@"" message:@"服务器异常，请稍侯重试！" delegate:self cancelButtonTitle:@"确定" otherButtonTitles: nil];
                
                mAlertView.tag = 101;
                
                [mAlertView show];
            }
            
        });
    }
}

// 用户登陆消息
- (void) OnLogin: (int)userId : (int)errorCode
{
    
    //记录开始登陆视频服务器返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1007:网络情况%@|登陆服务器结果%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];
    [self uploadLog:logString];
    
    if(errorCode == 0){//登录成功
        
        TKLogInfo(@"login success.");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
            
            linePromptLable.text = @"正在进入房间...";
        });
        
        tcUserId = userId;
        
        [TChatCore EnterRoom:[self.witRoomId intValue] : @""];
        
        //记录开始进入房间

        NSString *logString=[NSString stringWithFormat:@"TKMSG1008:网络情况%@|开始进入房间|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];
        [self uploadLog:logString];
    }else{
        
        TKLogInfo(@"login failed.");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (!self.isShowAlert) {
                
                self.isShowAlert = YES;
                
                NSString *msg = [NSString stringWithFormat:@"登陆服务器异常，请稍候重试！错误码:%d",errorCode];
                UIAlertView *mAlertView = [[UIAlertView alloc] initWithTitle:@"" message:msg delegate:self cancelButtonTitle:@"确定" otherButtonTitles: nil];
                mAlertView.tag = 102;
                [mAlertView show];
            }
            
        });
    }
}
// 用户进入房间消息
- (void) OnEnterRoom: (int)roomId : (int)errorCode
{
    //记录开始进入房间返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1009:网络情况%@|进入房间结果%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];
    [self uploadLog:logString];
    
    if (errorCode == 0) {//进入房间成功
        
        TKLogInfo(@"已进入房间");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *deviceName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
            [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :deviceName];
            
            
            
            int selfVideoErrorCode= [TChatCore UserVideoControl:-1 : YES];
            NSString *logVideoString=[NSString stringWithFormat:@"TKMSG1010:打开远本地摄像头情况%d|%@",selfVideoErrorCode,[self getTimeStamp]];
            [self uploadLog:logVideoString];
            int selfAudioErrorCode= [TChatCore UserAudioControl:-1 :YES];
            NSString *logAudioString=[NSString stringWithFormat:@"TKMSG1011:打开本地麦克风情况%d|%@",selfAudioErrorCode,[self getTimeStamp]];
            [self uploadLog:logAudioString];
            
            [TChatCore EnableSpeaker:YES];
            [[self.witnessView viewWithTag:2001] setHidden:YES];
            UIView *uView = [self.witnessView viewWithTag:3001];
            [TChatCore ShowUserVideo:-1 :uView :YES];
            
            if ([self.requestParams[@"isShowHeadRect"] isEqualToString:@"1"]) {
                //需要人脸指示框
                UIImageView *headIV = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 300, 378)];
                headIV.center=self.view.center;
                headIV.tag = 50000;
                [headIV setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/head_prompt_img.png", TK_OPEN_RESOURCE_NAME]]];
                [self.view addSubview:headIV];
            }

            
        });
        
    }else{
        
        TKLogInfo(@"用户进入房间失败");
        
        dispatch_async(dispatch_get_main_queue(), ^{
        
            if (!self.isShowAlert) {
                
                self.isShowAlert = YES;
                UIAlertView *mAlertView =[[UIAlertView alloc] initWithTitle:@"" message:@"网络异常,请稍侯重试" delegate:self cancelButtonTitle:@"确定" otherButtonTitles:nil, nil];
                mAlertView.tag = 103;
                [mAlertView show];
            }
            
        });
    }
}

#pragma mark -等待坐席进入房间处理
- (void)handleWaitSeatTimeout:(NSTimer*)timer{
    
    if (!self.isShowAlert) {
        
        self.isShowAlert = YES;
        
        NSString *msg = [NSString stringWithFormat:@"未匹配到%@,请稍侯重试",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服"];
        
        if (timer && timer.userInfo) {
            msg =  [NSString stringWithFormat:@"未匹配到%@,请稍侯重试[房间号：%ld]",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服", [timer.userInfo[@"roomId"] integerValue]];
        }
        
        UIAlertView *mAlertView =[[UIAlertView alloc] initWithTitle:@"" message:msg delegate:self cancelButtonTitle:@"确定" otherButtonTitles:nil, nil];
        
        mAlertView.tag = 103;
        
        [mAlertView show];
    }
    
}

// 房间在线用户消息
- (void) OnRoomOnlineUser: (int)userNum : (int)roomId
{
    TKLogInfo(@"房间人数:%d,房间号:%d",userNum,roomId);
    //记录开始房间人数日志
    NSString *logString=[NSString stringWithFormat:@"TKMSG100901:网络情况%@|当前房间人数%d|%@",[TKNetHelper getNetworkTypeInfo],userNum,[self getTimeStamp]];
    [self uploadLog:logString];
    
    if (userNum >= 2) {
        
        NSMutableArray *onlineUser = [TChatCore GetRoomOnlineUser];//获取房间中在线用户（坐席）
        
        if (onlineUser && onlineUser.count > 0){
            
            for (int i = 0; i< onlineUser.count; i++) {
                
                if ([[onlineUser objectAtIndex:i] intValue] != tcUserId) {
                    
                    tcSeatId = [[onlineUser objectAtIndex:i] intValue];
                    
                    break;
                }
            }
    
            TKLogInfo(@"坐席=%d",tcSeatId);
            
            if (!isTCStartingVideo) {
                
                isTCStartingVideo = YES;
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    
                    UILabel *linePromptLable = (UILabel*)[self.waitView viewWithTag:1004];
                    
                    linePromptLable.text = @"视频见证开启中...";
                    
                    [self startTChatVideo];
                });

            }
            
        }
        
    }else{
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            waitSeatTimer = [NSTimer scheduledTimerWithTimeInterval:WAIT_SEAT_ENTER_ROOM_TIME target:self selector:@selector(handleWaitSeatTimeout:) userInfo:[NSDictionary dictionaryWithObjectsAndKeys:[NSNumber numberWithInt:roomId],@"roomId", nil] repeats:NO];
        });
        
    }
    
}

//有新用户（坐席）进入房间消息
- (void) OnUserEnterRoom: (int)userId
{
    NSString *l = [NSString stringWithFormat:@"坐席[%@]进入房间",[TChatCore GetUserStateString:userId :TKCC_USERSTATE_NICKNAME]];
    
    TKLogInfo(@"%@", l);
    
    if (!isTCStartingVideo) {
        
        if (waitSeatTimer) {
            
            [waitSeatTimer invalidate];
            
            waitSeatTimer = nil;
        }
        
        tcSeatId = userId;
        
        isTCStartingVideo = YES;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            [self startTChatVideo];
        });
    }
}

-(void)OnLeaveRoom:(int)roomId
{
    TKLogInfo(@"退出房间回调:roomId-%d",roomId);
}

-(void)OnUserAudioCtl:(int)userId :(int)param
{
    TKLogInfo(@"用户音频控制回调,userId:%d param:%d",userId,param);
}

-(void)OnUserVideoCtl:(int)userId :(int)param
{
    TKLogInfo(@"用户视频控制回调,userId:%d param:%d",userId,param);
}

// 初始化通道
- (void) OnInitChannel: (int)errorCode{
//    [self uploadLog:@"通道初始化完毕： + 错误码"];
}
// 音频数据就绪
- (void) OnUserAudioDataReady: (int)userId : (int)dataInfo{
//    [self uploadLog:@"音频就绪： + 错误码"];
}
// 视频数据就绪
- (void) OnUserVideoDataReady: (int)userId : (int)dataInfo{
//    [self uploadLog:@"视频就绪： + 错误码"];
}

// 用户退出房间消息
- (void) OnUserLeaveRoom: (int)userId
{
    NSString *l = [NSString stringWithFormat:@"[%@]离开房间",[TChatCore GetUserStateString:userId :TKCC_USERSTATE_NICKNAME]];
    
    TKLogInfo(@"%@",l);
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (userId == tcSeatId && !self.isShowAlert && !isTCTransBufferMsg) {
            
            TKLogInfo(@"坐席离开房间");
            
            self.isShowAlert = YES;
            
            UIAlertView *mAlertView =[[UIAlertView alloc] initWithTitle:@"" message:[NSString stringWithFormat:@"%@视频连接异常，请重试！",self.requestParams[@"serviceTipString"]?self.requestParams[@"serviceTipString"]:@"客服"] delegate:self cancelButtonTitle:@"确定" otherButtonTitles:nil, nil];
            mAlertView.tag = 104;
            
            [mAlertView show];
        }
        
    });
}

// 网络断开消息
- (void) OnLinkClose: (int)errorCode
{
    TKLogInfo(@"视频连接断开");
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (!self.isShowAlert && !isTCTransBufferMsg) {
            
            self.isShowAlert = YES;
            
            UIAlertView *mAlertView =[[UIAlertView alloc] initWithTitle:@"" message:@"与视频服务器断开连接" delegate:self cancelButtonTitle:@"确定" otherButtonTitles:nil, nil];
            mAlertView.tag = 105;
            [mAlertView show];
        }
        
    });
    
}

////////////////////文字信息协议
- (void) OnTextMessageCallBack: (int)fromUserId : (int)toUserId : (BOOL)secret : (NSString*)msgBuf{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        NSRange userRange = [msgBuf rangeOfString:@"USR:0:"];
        if (userRange.length>0) {
            NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
            [self showSeatMsgStr:msg];
        }else{
            [self showSeatMsgStr:msgBuf];
        }
    });
    
}

//展示坐席发送的消息
-(void)showSeatMsgStr:(NSString *)serverStr{
    
    if (serverStr != nil) {
        
        UILabel *bottomLab = [self.witnessView viewWithTag:2004];
        
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        
        paragraphStyle.lineBreakMode = NSLineBreakByWordWrapping;
        
        NSDictionary *attributes = @{NSFontAttributeName:bottomLab.font, NSParagraphStyleAttributeName:paragraphStyle.copy};
        
        CGSize lSize = [serverStr boundingRectWithSize:[UIScreen mainScreen].bounds.size
                                               options:
                        NSStringDrawingUsesLineFragmentOrigin |
                        NSStringDrawingUsesFontLeading
                                            attributes:attributes
                                               context:nil].size;
        
        UIView  *seatVideoSurface = [self.witnessView viewWithTag:2000];
        
        seatVideoSurface.frame = CGRectMake(seatVideoSurface.frame.origin.x, seatVideoSurface.frame.origin.y - lSize.height - 10 , seatVideoSurface.frame.size.width, seatVideoSurface.frame.size.height);
        
        UILabel *seatLable = [self.witnessView viewWithTag:2002];
        
        seatLable.frame = CGRectMake(seatLable.frame.origin.x, seatLable.frame.origin.y - lSize.height - 10 , seatLable.frame.size.width, seatLable.frame.size.height);
        
        bottomLab.frame = CGRectMake(0, bottomLab.frame.origin.y-lSize.height -10, self.view.bounds.size.width, lSize.height + 10);
        
        bottomLab.text = [NSString stringWithFormat:@"客服：%@",serverStr];
        
        [self performSelector:@selector(clearSeatMsgStr:) withObject:nil afterDelay:8.0];
    }
}

//清除坐席发送的消息
-(void)clearSeatMsgStr:(NSString *)serverStr{
    UILabel *bottomLab = [self.witnessView viewWithTag:2004];
    bottomLab.text =@"您好，见证开始了，请不要随意挂断或者离开";
    UIView  *seatVideoSurface = [self.witnessView viewWithTag:2000];
    
    seatVideoSurface.frame = CGRectMake(seatVideoSurface.frame.origin.x,  self.view.frame.size.height-237, seatVideoSurface.frame.size.width, seatVideoSurface.frame.size.height);
    
    UILabel *seatLable = [self.witnessView viewWithTag:2002];
    
    seatLable.frame = CGRectMake(seatLable.frame.origin.x, self.view.frame.size.height-117 , seatLable.frame.size.width, seatLable.frame.size.height);
    bottomLab.frame = CGRectMake(0, self.view.frame.size.height-67, self.view.bounds.size.width, 17);
}

- (void) OnTransBufferCallBack: (int)userId : (NSData*)buf{
    
    TKLogInfo(@"transBuffer callback");
    
    isTCTransBufferMsg = YES;
    
    NSString *lpMsgBuf=  [[NSString alloc] initWithData:(buf) encoding:NSUTF8StringEncoding];
    
    TKLogInfo(@"来自%d的透明通道消息:%@", userId,lpMsgBuf);
    
    NSRange sysRange = [lpMsgBuf rangeOfString:@"SYS:"];
    
    self.witnessResult = lpMsgBuf;
    
    if (sysRange.length > 0) {  //见证返回的透明信息
        
    }else{ //其它消息
        
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self endTChatVideo];
    });
    
}

#pragma mark -启动TChat视频
- (void)startTChatVideo{
    
    //请求远端硬件
    int seatVideoErrorCode=[TChatCore UserVideoControl:tcSeatId :YES];
    NSString *logVideoString=[NSString stringWithFormat:@"TKMSG1010:打开远程摄像头情况%d|%@",seatVideoErrorCode,[self getTimeStamp]];
    [self uploadLog:logVideoString];
    
    int seatAudioErrorCode=[TChatCore UserAudioControl:tcSeatId :YES];
    NSString *logAudioString=[NSString stringWithFormat:@"TKMSG1011:打开远程麦克风情况%d|%@",seatAudioErrorCode,[self getTimeStamp]];
    [self uploadLog:logAudioString];
    
    [[self.witnessView viewWithTag:2003] setHidden:YES];
    
    NSString *uName = [TChatCore GetUserStateString:tcSeatId :TKCC_USERSTATE_NICKNAME];
    
    ((UILabel*)[self.witnessView viewWithTag:2002]).text = [NSString stringWithFormat:@"客服：%@",uName];
    
    UIView *sView = [self.witnessView viewWithTag:2000];
    
    [TChatCore ShowUserVideo:tcSeatId :sView :NO];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(300 * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
        
        [self.waitView setHidden:YES];
    });
    
}

//- (void)featchNetworkSpeed
//{
//}

#pragma mark -结束视频
- (void)endTChatVideo{
    
    TKLogInfo(@"end TChat video.");
    
    [UIApplication sharedApplication].idleTimerDisabled = NO;

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(200 * NSEC_PER_MSEC)), dispatch_get_global_queue(0, 0), ^{
        
        if (!self.isCancelLineingUp) {}
        
        if (self.witRoomId) {
            
            if(!isTCTransBufferMsg)
            {
                [TChatCore TransBuffer:tcSeatId :[@"SYS:10002" dataUsingEncoding:NSUTF8StringEncoding]];
            }
            
            
            [TChatCore UserVideoControl:-1 :NO];
            [TChatCore UserAudioControl:-1 :NO];
         
            
            [TChatCore EnableSpeaker:NO];
            [TChatCore UserVideoControl:tcSeatId :NO];
            [TChatCore UserAudioControl:tcSeatId :NO];
            [TChatCore LeaveRoom];
            [TChatCore Logout];
            [TChatCore Release];
        }
    });
    
    if (!isTCTransBufferMsg) {
        //正常通过驳回无需调用退出排队接口
        [self stopLineingUp];
    }
    
    [self dismissViewControllerAnimated:YES completion:^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkVideoWitnessResult:)]) {
            
            [self.delegate tkVideoWitnessResult:self.witnessResult];
        }
        [TKTraffic event:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WDismiss.objectId",@"open"]] eventType:@"2" actionId:[TKSystemHelper getConfigByKey:[NSString stringWithFormat:@"TK%@_Video3.0_WDismiss.actionId",@"open"]] attributes:self.requestParams[@"extParams"]];
    }];
}

@end
