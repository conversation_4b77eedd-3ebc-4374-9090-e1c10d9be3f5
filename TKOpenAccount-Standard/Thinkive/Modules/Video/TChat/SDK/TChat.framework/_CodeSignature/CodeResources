<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/TChatCore.h</key>
		<data>
		9/HnEwpWFbwip8ESQ5cdWoc2LYw=
		</data>
		<key>Headers/TChatDefine.h</key>
		<data>
		2Z1efLcZOqzbMVOa+1CF4kRxH4Q=
		</data>
		<key>Headers/TChatErrorCode.h</key>
		<data>
		czoFL/506jhq0+8aP2GqkqvGjn4=
		</data>
		<key>Info.plist</key>
		<data>
		5dTS7YsdQe4qztCTdzMNtzTCS6M=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/TChatCore.h</key>
		<dict>
			<key>hash</key>
			<data>
			9/HnEwpWFbwip8ESQ5cdWoc2LYw=
			</data>
			<key>hash2</key>
			<data>
			jhb2Vkkmik61ueB7xw7agE4KaecC7LaEjSEkI2il9j8=
			</data>
		</dict>
		<key>Headers/TChatDefine.h</key>
		<dict>
			<key>hash</key>
			<data>
			2Z1efLcZOqzbMVOa+1CF4kRxH4Q=
			</data>
			<key>hash2</key>
			<data>
			EmEdqTHO8NYYJ0fwREp1cRo0VYhN/Dmn8mk5hnCQTR8=
			</data>
		</dict>
		<key>Headers/TChatErrorCode.h</key>
		<dict>
			<key>hash</key>
			<data>
			czoFL/506jhq0+8aP2GqkqvGjn4=
			</data>
			<key>hash2</key>
			<data>
			ihKeMHFW7tXpXvHMnc1CinLKAq5lCL3OqZjxIHF0NTM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
