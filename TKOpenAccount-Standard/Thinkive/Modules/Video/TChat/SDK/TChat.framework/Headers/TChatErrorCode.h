#ifndef TCHAT_ERRORCODE_H_
#define TCHAT_ERRORCODE_H_

// 错误代码定义
#define TKCC_ERR_SUCCESS				0		///< 成功

// 系统错误
#define TKCC_ERR_SYSTEM_UNKNOWN			-1		///< 未知错误
#define TKCC_ERR_SYSTEM_MEMORYFAIL		1		///< 内存不足
#define TKCC_ERR_SYSTEM_BUSY			2		///< 系统繁忙

// 连接部分
#define TKCC_ERR_CONNECT_TIMEOUT		100		///< 连接服务器超时
#define TKCC_ERR_CONNECT_ABORT			101		///< 与服务器的连接中断
#define TKCC_ERR_CONNECT_DNSERROR		102		///< 域名解析失败
#define TKCC_ERR_CONNECT_AUTHFAIL		103		///< 连接服务器认证失败
#define	TKCC_ERR_CONNECT_OLDVERSION 	104		///< 版本太旧，不允许连接
#define TKCC_ERR_CONNECT_EXPIRE			105		///< 与服务器的连接过期
#define TKCC_ERR_CONNECT_CHANGE			106		///< 与服务器的连接变化
#define TKCC_ERR_CONNECT_MAXSIZE		107		///< 连接数超过最大限制

// 登录部分
#define TKCC_ERR_CERTIFY_FAIL			200		///< 认证失败，用户名或密码有误
#define TKCC_ERR_VISITOR_DENY			201		///< 游客登录被禁止
#define TKCC_ERR_ALRADY_LOGIN			202		///< 用户已登录
#define TKCC_ERR_USER_TYPE				203		///< 用户类型不支持

// 房间部分
#define TKCC_ERR_ROOM_PASSERR			300		///< 房间密码错误，禁止进入
#define TKCC_ERR_ROOM_FULLUSER			301		///< 房间已满员，不能进入
#define TKCC_ERR_ROOM_ENTERFAIL			302		///< 禁止进入房间
#define TKCC_ERR_ROOM_IDINVALID			303		///< 房间ID错误
#define TKCC_ERR_ROOM_ALREADYIN			304		///< 用户已在房间内
#define TKCC_ERR_ROOM_NOTEXIST			305		///< 房间不存在
#define TKCC_ERR_ROOM_MAXNUMBER			306		///< 房间数已满

// 用户部分
#define TKCC_ERR_USER_NOTINROOM			400		///< 用户不在房间内
#define TKCC_ERR_USER_OFFLINE			401		///< 用户不在线
#define TKCC_ERR_USER_IDINVALID			402		///< 用户ID错误
#define TKCC_ERR_USER_LOGINED			403		///< 用户已登录

// 通道部分
#define TKCC_ERR_STREAM_TIMEOUT			500		///< 通道建立超时

// 呼叫部分
#define TKCC_ERR_CALL_DENY				600		///< 呼叫受限
#define TKCC_ERR_CALL_BUSY				601		///< 被叫繁忙
#define TKCC_ERR_CALL_REFUSE			602		///< 被叫拒绝
#define TKCC_ERR_CALL_CANCEL			603		///< 主叫取消

// 录像部分
#define TKCC_ERR_RECORD_CREATEFAIL		700		///< 录像创建失败
#define TKCC_ERR_RECORD_AUTHFAIL		701		///< 录像认证失败
#define TKCC_ERR_RECORD_CREATEFILEFAIL	702		///< 文件创建失败
#define TKCC_ERR_RECORD_WRITEFILEFAIL	703		///< 录像写入失败
#define TKCC_ERR_RECORD_ALREADYSTARTED	704		///< 录像已被启动
#define TKCC_ERR_RECORD_TIMEOUT			705		///< 录像响应超时
#define TKCC_ERR_RECORD_INVALID			706		///< 录像任务无效
#define TKCC_ERR_RECORD_NOTSTART		707		///< 录像尚未启动
#define TKCC_ERR_RECORD_INTERRUPT		708		///< 录像媒体断流
#define TKCC_ERR_RECORD_INTERNAL		709		///< 录像内部错误
#define TKCC_ERR_RECORD_DISCONNECTED	710		///< 录像连接中断
#define TKCC_ERR_RECORD_STARTTIMEOUT	711		///< 录像启动超时
#define TKCC_ERR_RECORD_STOPTIMEOUT		712		///< 录像停止超时

// 服务部分
#define TKCC_ERR_SERVER_NOMEDIA   		800		///< 无在线媒体服务器
#define TKCC_ERR_SERVER_NORECORD		801		///< 无在线录像服务器
#define TKCC_ERR_SERVER_MEDIAOFFLINE	802		///< 媒体服务器已下线

// 媒体部分
#define TKCC_ERR_MEDIA_NOTEXIST			900		///< 媒体不存在
#define TKCC_ERR_MEDIA_NOTSUPPORT		901		///< 媒体不支持
#define TKCC_ERR_MEDIA_INITFAIL			902		///< 媒体初始化失败

#endif	// TCHAT_ERRORCODE_H_
