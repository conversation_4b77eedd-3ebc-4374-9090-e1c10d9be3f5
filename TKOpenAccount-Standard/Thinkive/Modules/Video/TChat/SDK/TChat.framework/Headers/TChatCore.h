#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

// 异步通知
@protocol TKCCNotifyMessageDelegate <NSObject>
// 连接服务器
- (void) OnConnect: (BOOL)success : (int)errorCode;
// 登录
- (void) OnLogin: (int)userId : (int)errorCode;
// 进入房间
- (void) OnEnterRoom: (int)roomId : (int)errorCode;
// 房间用户数目
- (void) OnRoomOnlineUser: (int)userNum : (int)roomId;
// 用户进入房间
- (void) OnUserEnterRoom: (int)userId;
// 用户离开房间
- (void) OnUserLeaveRoom: (int)userId;
// 离开房间
- (void) OnLeaveRoom: (int)roomId;
// 连接断开
- (void) OnLinkClose: (int)errorCode;
// 用户音频控制
- (void) OnUserAudioCtl: (int)userId : (int)param;
// 用户视频控制
- (void) OnUserVideoCtl: (int)userId : (int)param;
// 初始化通道
- (void) OnInitChannel: (int)errorCode;
// 初始化通道扩展
- (void) OnInitChannelEx: (int)userId : (int)errorCode;
// 网络质量
- (void) OnNetQuality: (int)local : (int)qos;
// 网络码率
- (void) OnNetBitrate: (int)sendBps : (int)recvBps;
// 音频状态改变
- (void) OnUserAudioStatusChg: (int)userId : (int)status;
// 视频状态改变
- (void) OnUserVideoStatusChg: (int)userId : (int)status;
// 音频数据就绪
- (void) OnUserAudioDataReady: (int)userId : (int)dataInfo;
// 视频数据就绪
- (void) OnUserVideoDataReady: (int)userId : (int)dataInfo;
// 媒体播放完成
- (void) OnStreamPlayFinished: (int)taskId : (int)param;
// 音频数据中断
- (void) OnAudioInterrupt: (int)wparam : (int)lparam;
// 视频数据中断
- (void) OnVideoInterrupt: (int)wparam : (int)lparam;
// 释放成功
- (void) OnReleaseFinished;
@end

// 呼叫事件
@protocol TKCCCallDelegate <NSObject>
// 呼叫事件
- (void) OnCallEventCallBack: (int)callType : (int)eventType : (int)userId : (int)errorCode : (int)param : (NSString*)userStr;
@end

// 文字消息
@protocol TKCCTextMsgDelegate <NSObject>
// 文字消息事件
- (void) OnTextMessageCallBack: (int)fromUserId : (int)toUserId : (BOOL)secret : (NSString*)msgBuf;
@end

// 传输数据
@protocol TKCCTransDataDelegate <NSObject>
// 透明通道
- (void) OnTransBufferCallBack: (int)userId : (NSData*)buf;
@end

// 视频录制
@protocol TKCCRecordDelegate <NSObject>
// 开始录制
- (void) OnStartRecordCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)flags : (int)param : (NSString*)userStr ;
// 停止录制
- (void) OnStopRecordCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)elapse : (int)flags : (int)param : (NSString*)userStr ;
// 录制出错
- (void) OnRecordErrorCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)flags : (int)param : (NSString*)userStr ;
@end

// 快照抓拍
@protocol TKCCSnapShotDelegate <NSObject>
// 快照抓拍
- (void) OnSnapShotCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)flags : (int)param : (NSString*)userStr ;
@end


// 视频数据
@protocol TKCCVideoDataDelegate <NSObject>
// 视频数据
- (void) OnVideoDataCallBack: (int)userId : (NSData*)buf :
(int)len : (int)width : (int)height ;
@end


// 音频数据数据
@protocol TKCCAudioDataDelegate <NSObject>
// 音频数据数据
- (void) OnAudioDataCallBack: (int)userId : (NSData*)buf : (int)len : (int)channels : (int)samplesPerSec :
(int)bitPerSample ;
@end



@interface TChatCore : NSObject

@property (nonatomic, weak) id<TKCCNotifyMessageDelegate> notifyMsgDelegate;
@property (nonatomic, weak) id<TKCCCallDelegate>          callDelegate;
@property (nonatomic, weak) id<TKCCTextMsgDelegate>       textMsgDelegate;
@property (nonatomic, weak) id<TKCCTransDataDelegate>     transDataDelegate;
@property (nonatomic, weak) id<TKCCRecordDelegate>        recordDelegate;
@property (nonatomic, weak) id<TKCCSnapShotDelegate>      snapshotDelegate;
@property (nonatomic, weak) id<TKCCVideoDataDelegate>     videodataDelegate;
@property (nonatomic, weak) id<TKCCAudioDataDelegate>     audiodataDelegate;

// 单例
+ (TChatCore*)shareTChatCore;
// 初始化SDK
+ (int) InitSDK;
// 激活（关闭）SDK调用日志
+ (int) ActiveCallLog: (BOOL)bActive;
// 设置SDK内核参数（INT）
+ (int) SetSDKOptionInt: (int)optName : (int)value;
// 设置SDK内核参数（String）
+ (int) SetSDKOptionString: (int)optName : (NSString*)value;
// 查询SDK内核参数（INT）
+ (int) GetSDKOptionInt: (int)optName;
// 查询SDK内核参数（String）
+ (NSString*) GetSDKOptionString: (int)optName;
// 设置服务器认证密码
+ (int) SetServerAuthPass: (NSString*)password;
// 连接服务器
+ (int) Connect: (NSString*)serverAddr : (int)port;
// 登录
+ (int) Login: (NSString*)userName : (NSString*)password;
// 进入房间
+ (int) EnterRoom: (int)roomId : (NSString*)password;
// 进入房间
+ (int) EnterRoomEx: (NSString*)roomName : (NSString *)password;
// 离开房间
+ (int) LeaveRoom;
// 注销
+ (int) Logout;
// 释放SDK
+ (int) Release;

// 用户音频控制
+ (int) UserAudioControl: (int)userId : (BOOL)open;
// 用户视频控制
+ (int) UserVideoControl: (int)userId : (BOOL)open;
// 打开视频显示
+ (int) ShowUserVideo: (int)userId : (NSObject*)obj : (BOOL)mirror;
// 关闭视频显示
+ (int) StopUserVideo: (int)userId;
// 呼叫控制
+ (int) CallControl: (int)callType : (int)eventType : (int)userId : (int)errorCode : (int)param : (NSString*)userStr;

// 获取当前房间名称
+ (NSString*) GetRoomName;
// 获取当前房间用户列表
+ (NSMutableArray*) GetRoomOnlineUser;
// 获取当前用户状态
+ (NSString*) GetUserStateString: (int)userId : (int)infoName;
// 获取当前用户状态
+ (int) GetUserStateInt: (int)userId : (int)infoName;

// 发送文本消息
+ (int) SendTextMessage: (int)userId : (BOOL)secret : (NSString*)msgBuf;
// 透明通道传输
+ (int) TransBuffer: (int)userId : (NSData*)buf;

// 获取音频设备的音量
+ (int)AudioGetVolume: (int)device;
// 设置音频设备的音量
+ (int)AudioSetVolume: (int)device  : (int)volume;

// 获取设备数量
+ (int) GetDeviceNum : (int)type;
// 获取设备名称
+ (NSString*) GetDeviceName : (int)type : (int)index;
// 选择设备
+ (int) SelectDevice: (int)type : (NSString*)deviceName;
// 获取设备
+ (NSString*) GetCurDevice : (int)type;
// 使能扬声器
+ (int) EnableSpeaker: (BOOL)enable;
// 获取网络状态
+ (int) GetNetQulity: (BOOL)local;
// 获取发送速度
+ (int) GetSendRate: (BOOL)local;
// 获取接收状态
+ (int) GetRecvRate: (BOOL)local;

// 开始录制
+ (int) StartRecord: (int [])userIdArray : (int)num : (int)flags : (int)param :(NSString*)userStr;
// 停止录制
+ (int) StopRecord: (int)taskId;
// 快照抓拍
+ (int) Snapshot: (int)userId : (int)flags : (int)param : (NSString*)userStr;
//设置媒体服务器
+ (int) SetMediaServer: (NSString*)msAddr;
//设置视频旋转度数
+ (int) RotateUserVideo:(int)userid : (int)degree;
//流媒体初始化
+ (int) StreamPlayInit: (int)task_id : (NSString*)stream_path : (int)flags : (NSString*)param;
//音频文件导入
+ (int) StreamPlayInputAudioData: (int)task_id : (int)format : (int)channel : (int)samples_per_second : (int)bits_per_sample : (int)flags : (NSData*)buf : (int)len : (int)time_stamp;
//视频文件导入
+ (int) StreamPlayInputVideoData: (int)task_id : (int)format : (int)width : (int)height : (int)fps : (int)flags : (NSData*)buf : (int)len : (int)time_stamp;
//获取流媒体音频缓存
+ (int) StreamGetAudioCache: (int)task_id;
//释放流媒体资源
+ (int) StreamPlayDestroy: (int)task_id;
@end

