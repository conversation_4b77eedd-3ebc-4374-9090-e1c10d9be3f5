#ifndef TCHAT_DEFINE_H_
#define TCHAT_DEFINE_H_

// 音频设备定义
#define    TKCC_AD_WAVEIN            1        ///< 音频输入设备
#define    TKCC_AD_WAVEOUT            2        ///< 音频输出设备

// 设备类型定义
#define    TKCC_DT_VIDEOCAPTURE    1        ///< 视频采集设备
#define    TKCC_DT_AUDIOCAPTURE    2        ///< 音频采集设备
#define    TKCC_DT_AUDIOPLAYER        3        ///< 音频播放设备

// 图像格式定义
#define TKCC_PF_I420            1        ///< YUVI420
#define TKCC_PF_RGB24            2        ///< 24位RGB
#define TKCC_PF_RGBA            3        ///< RGBA

// SDK内核参数定义
#define    TKCC_SO_CORESDK_MAIN_VERSION            1    ///< SDK主版本号（整型）
#define    TKCC_SO_CORESDK_SUB_VERSION                2    ///< SDK从版本号（整型）
#define    TKCC_SO_CORESDK_BUILD_TIME                3    ///< SDK编译时间（字符串型）
#define    TKCC_SO_TMPDIR_RECORD                    4    ///< 录像临时文件路径（字符串型）
#define    TKCC_SO_TMPDIR_SNAPSHOT                    5    ///< 快照临时文件路径（字符串型）
#define    TKCC_SO_TMPDIR_CORESDK                    6    ///< SDK临时目录（字符串型）
#define    TKCC_SO_RECONNECT                        7    ///< 重连标志（整型）
#define    TKCC_SO_LOG_LEVEL                        8    ///< 日志级别（整型；1:ALL；2:DEBUG；3:INFO；4:WARN；5:ERRO；6:FATAL）
#define TKCC_SO_LOCAL_VIDEO_BITRATE                9    ///< 视频编码码率（整型；单位：kbps）
#define TKCC_SO_LOCAL_VIDEO_FPS                    10    ///< 视频编码帧率（整型）
#define    TKCC_SO_LOCAL_VIDEO_GOP                    11    ///< 视频关键帧间隔（整型）
#define    TKCC_SO_LOCAL_VIDEO_WIDTH                12    ///< 视频采集宽度（整型）
#define    TKCC_SO_LOCAL_VIDEO_HEIGHT                13    ///< 视频采集高度（整型）
#define    TKCC_SO_LOCAL_AUDIO_NOISE_SUPPRESSION    14    ///< 噪音抑制指数（整型；0-3）（0：低；1：中；2：高；3：很高）
#define    TKCC_SO_LOCAL_AUDIO_AEC_LEVEL            15    ///< 回声消除级别（整型：0-2）（0：低；1：中；2：高）
#define    TKCC_SO_LOCAL_AUDIO_GAIN_CONTROL_MODE    16    ///< 语音增益模式（整型；0-2）（0：模拟模式，1：数字模式，2：固定模式）
#define    TKCC_SO_LOCAL_AUDIO_GAIN_CONTROL_CLASS    17    ///< 语音增益级别（整型；0-31）
#define    TKCC_SO_RECORD_VIDEO_BITRATE            18    ///< 录像视频码率（整型；单位：kbps）
#define    TKCC_SO_RECORD_AUDIO_BITRATE            19    ///< 录像音频码率（整型；单位：kbps）
#define    TKCC_SO_RECORD_VIDEO_WIDTH                20    ///< 录像视频宽度（整型）
#define    TKCC_SO_RECORD_VIDEO_HEIGHT                21    ///< 录像视频高度（整型）
#define    TKCC_SO_RECORD_AUDIO_FORMAT                22    ///< 录像音频格式（整型；1：mp3）
#define    TKCC_SO_RECORD_VIDEO_FORMAT                23    ///< 录像视频格式（整型；11：flv；12：mp4）
#define TKCC_SO_WEB_SERVICE_ENABLE                24  ///< 本地Web服务启用（整型）
#define TKCC_SO_WEB_SERVICE_PORT                25  ///< 本地Web服务端口（整型）
#define TKCC_SO_RECORD_SHOW_STYLE                26    ///< 录像显示风格（整型；等于0：左右排列；大于0：画中画排列；1：左上；2：右上；3：右下；4：左下）
#define TKCC_SO_RECORD_SHOW_PIP_RATE            27    ///< 录像显示画中画比例（整型；如为30，表示30%）
#define TKCC_SO_RECORD_FONT_TYPE                28    ///< 录像时间戳字体（整型；0-7）
#define TKCC_SO_RECORD_FONT_COLOR                29    ///< 录像时间戳颜色（整型）
#define TKCC_SO_RECORD_FONT_SCALE                30    ///< 录像时间戳大小*100（整型；为0时，不显示时间戳）
#define TKCC_SO_RECORD_FONT_POS_X                31    ///< 录像时间戳x位置（整型）
#define TKCC_SO_RECORD_FONT_POS_Y                32    ///< 录像时间戳y位置（整型）
#define TKCC_SO_RECORD_ENABLE_CLIP                33    ///< 录像启用剪裁（整形）
#define TKCC_SO_ENABLE_OTM_MODE                    34  ///< 启用一对多模式（整形）
#define TKCC_SO_ENABLE_AUDIO_TRANS                35  ///< 启用音频传输（整形）
#define TKCC_SO_ENABLE_VIDEO_TRANS                36  ///< 启用视频传输（整形）
#define TKCC_SO_RUN_IN_H5_MODE                    37  ///< 启用H5运行模式（整形）
#define TKCC_SO_SHOW_DEBUG_INFO                    38    ///< 显示调试信息（整形）
#define    TKCC_SO_CORESDK_STAGE_VERSION            39    ///< SDK阶段版本号（整型）
#define TKCC_SO_VIDEO_BEAUTY_BUFFING            40  ///< 磨皮（保留，整形；等于0：无；等于1：低；等于2：中；等于3：高）
#define TKCC_SO_VIDEO_BEAUTY_WHITENING            41  ///< 美白（保留，整形；等于0：无；等于1：低；等于2：中；等于3：高）
#define TKCC_SO_VIDEO_STRECH_FILLING            42  ///< 拉伸填充（整形）
#define TKCC_SO_USE_DEFAULT_DEVICE                43    ///< 使用默认设备（整形）
#define TKCC_SO_RECORD_TIMEOUT                    44  ///< 录像超时响应时间（整形）
#define TKCC_SO_STREAM_NEW_TASKID                45    ///< 流媒体新任务ID（字符串型）
#define TKCC_SO_OUTPUT_AUDIO_DATA                46    ///< 导出本地音频数据（整形）
#define TKCC_SO_OUTPUT_VIDEO_DATA                47    ///< 导出本地视频数据（整形）
#define TKCC_SO_OUTPUT_PEER_AUDIO_DATA            48    ///< 导出对端音频数据（整形）
#define TKCC_SO_OUTPUT_PEER_VIDEO_DATA            49    ///< 导出对端视频数据（整形）
#define TKCC_SO_ASR_RETURN_ONCE                    50    ///< 识别返回一次结果（整形；默认为1）
#define TKCC_SO_VIDEO_RC_MODES                    51  ///< 视频码率控制模式（整形；0：质量优先；1：码率优先；默认为1）
#define TKCC_SO_ENABLE_VOICE_COMMUNICATION        52  ///< OPENSLES录音配置参数 （参数为整形 默认开启1）
#define TKCC_SO_ENABLE_NETEQ                    53  ///< NETEQ（整形 默认开启1）
#define TKCC_SO_VIDEO_TRANS_MODE                54  ///< 视频传输模式（整形；0：始终UDP；1：始终TCP；2：优先UDP；3：优先TCP）
#define TKCC_SO_RECONNECT_TIMEOUT               55  ///< 重连超时（整型）
#define TKCC_SO_LOG_MODE                        56  ///< 日志模式（整形；0：普通日志；1：关联日志）
#define TKCC_SO_LOG_COLLECTION                    57  ///< 日志收集（整形）
#define TKCC_SO_MON_COLLECTION                    65    ///< 监控收集（整形；默认为0）
#define TKCC_SO_MUTE_SELF_AUDIO					66  ///< 本地音频静音（整形；默认为0）
#define TKCC_SO_MUTE_PEER_AUDIO					67  ///< 对端音频静音（整形；默认为0）


// 呼叫类型定义
#define    TKCC_CT_VIDEO                    1    ///< 视频呼叫
#define    TKCC_CT_AUDIO                    2    ///< 音频呼叫

// 呼叫事件定义
#define TKCC_CALL_EVENT_REQUEST         1     ///< 呼叫请求事件
#define TKCC_CALL_EVENT_REPLY            2    ///< 呼叫回复事件
#define TKCC_CALL_EVENT_START            3     ///< 呼叫开始事件
#define TKCC_CALL_EVENT_FINISH          4     ///< 呼叫完成事件

// 用户状态定义
#define TKCC_USERSTATE_UNKNOWN            0    ///< 未知用户状态
#define TKCC_USERSTATE_CONNECTED        1    ///< 用户已连接
#define TKCC_USERSTATE_LOGINED            2    ///< 用户已登录
#define TKCC_USERSTATE_INROOM            3    ///< 用户在房间
#define TKCC_USERSTATE_LINKCLOSED        4    ///< 用户断开连接

// 用户状态标志定义
#define TKCC_USERSTATE_USERID            1     ///< 用户ID
#define TKCC_USERSTATE_USERSTATUS        2     ///< 用户状态（0: Unknow, 1: Connected, 2: Logined, 3: In Room, 4: Link Closed）
#define TKCC_USERSTATE_RECORDING        3     ///< 用户录像状态（0: 未录像, 1: 录像中）
#define TKCC_USERSTATE_NICKNAME            4     ///< 用户的昵称
#define TKCC_USERSTATE_DEVICETYPE        5     ///< 用户的终端类型（0: Unknow, 1: Windows, 2: Android, 3: Ios, 4: Web）

// 录像标志定义
#define TKCC_RECORD_FLAGS_CLIENT        0x0001    ///< 客户端录像
#define TKCC_RECORD_FLAGS_STREAM        0x0002    ///< 合成流录像
#define TKCC_RECORD_FLAGS_SERVER        0x0004    ///< 服务器录像
#define TKCC_RECORD_FLAGS_AUDIO            0x0010    ///< 只录制音频
#define TKCC_RECORD_FLAGS_VIDEO            0x0020    ///< 录制音视频

// 媒体播放控制类型定义
#define TKCC_STREAMPLAY_CTRL_PLAY        1    ///< 开始播放
#define TKCC_STREAMPLAY_CTRL_PAUSE        2    ///< 暂停播放
#define TKCC_STREAMPLAY_CTRL_STOP        3    ///< 停止播放

// 媒体播放标志类型定义
#define TKCC_STREAMPLAY_FLAGS_LOOP        0x0001    ///< 循环播放
#define TKCC_STREAMPLAY_FLAGS_INPUT       0X0002    ///<导入播放


// 拍照标志
#define TKCC_SNAPSHOT_FLAGS_FILE        0    ///< 文件路径
#define TKCC_SNAPSHOT_FLAGS_DATA        1    ///< 文件数据（base64）

// 语音播报厂商
#define TKCC_TTS_AL_CLOUD                0    ///< 阿里公有云
#define TKCC_TTS_KD_OFFLINE                1    ///< 科大离线版
#define TKCC_TTS_TX_CLOUD                2    ///< 腾讯公有云
#define TKCC_TTS_KD_PRIVATE                3    ///< 科大私有版
#define TKCC_TTS_TX_PRIVATE                4    ///< 腾讯行业云
#define TKCC_TTS_ZY_PRIVATE                5    ///< 中银私有云
#define TKCC_TTS_BD_PRIVATE                6    ///< 百度私有云
#define TKCC_TTS_DD_PRIVATE                7    ///< 顶点私有云
#define TKCC_TTS_HS_PRIVATE                8    ///< 恒生私有云

// 语音识别厂商
#define TKCC_ASR_AL_CLOUD                0    ///< 阿里公有云
#define TKCC_ASR_TX_CLOUD                1    ///< 腾讯公有云
#define TKCC_ASR_KD_PRIVATE                2    ///< 科大私有版
#define TKCC_ASR_TX_PRIVATE                3    ///< 腾讯行业云
#define TKCC_ASR_ZY_PRIVATE                4    ///< 中银私有云
#define TKCC_ASR_BD_PRIVATE                5    ///< 百度私有云
#define TKCC_ASR_DD_PRIVATE                6    ///< 顶点私有云
#define TKCC_ASR_HS_PRIVATE                7    ///< 恒生私有云

// 消息定义
#define WM_TKCC                            0x400 + 200        ///< 消息开始
#define WM_TKCC_CONNECT                 WM_TKCC + 1        ///< 连接消息
#define WM_TKCC_LOGIN                    WM_TKCC + 2        ///< 登录系统消息
#define WM_TKCC_ENTERROOM                 WM_TKCC + 3        ///< 自己进入房间消息
#define WM_TKCC_ROOMONLINEUSER             WM_TKCC + 4        ///< 房间用户列表消息
#define WM_TKCC_USERENTERROOM             WM_TKCC + 5        ///< 用户进入（离开）房间消息
#define WM_TKCC_LEAVEROOM                WM_TKCC + 6        ///< 自己离开房间消息
#define WM_TKCC_LINKCLOSE                 WM_TKCC + 7        ///< 网络连接关闭消息
#define WM_TKCC_USERAUDIOCTL            WM_TKCC + 8        ///< 用户音频控制消息
#define WM_TKCC_USERVIDEOCTL            WM_TKCC + 9        ///< 用户视频控制消息
#define WM_TKCC_INITCHANNEL                WM_TKCC + 10    ///< 初始化通道消息(弃用)
#define WM_TKCC_LOGOUT                    WM_TKCC + 11    ///< 注销完成消息
#define WM_TKCC_NETQUALITY                WM_TKCC + 12    ///< 网络质量消息
#define WM_TKCC_NETBITRATE                WM_TKCC + 13    ///< 网络码率消息
#define WM_TKCC_USERAUDIOSTATUSCHG        WM_TKCC + 14    ///< 用户音频状态
#define WM_TKCC_USERVIDEOSTATUSCHG        WM_TKCC + 15    ///< 用户视频状态
#define WM_TKCC_USERAUDIODATAREADY        WM_TKCC + 16    ///< 用户音频就绪
#define WM_TKCC_USERVIDEODATAREADY        WM_TKCC + 17    ///< 用户视频就绪
#define WM_TKCC_STREAMPLAYFINISHED        WM_TKCC + 18    ///< 媒体播放完成
#define WM_TKCC_AUDIOINTERRUPT            WM_TKCC + 19    ///< 音频中断消息
#define WM_TKCC_VIDEOINTERRUPT            WM_TKCC + 20    ///< 视频中断消息
#define WM_TKCC_INITCHANNELEX            WM_TKCC + 21    ///< 初始化通道消息

// 回调类型定义
#define TKCC_CBTYPE_NOTIFYMESSAGE        100        ///< 异步消息
#define TKCC_CBTYPE_TEXTMESSAGE            101        ///< 文本信息
#define TKCC_CBTYPE_TRANSBUFFER            102        ///< 透明通道
#define TKCC_CBTYPE_CALLEVENT            103        ///< 视频呼叫
#define TKCC_CBTYPE_STARTRECORD            104        ///< 开始录制
#define TKCC_CBTYPE_STOPRECORD            105        ///< 停止录制
#define TKCC_CBTYPE_SNAPSHOT            106        ///< 快照抓拍
#define TKCC_CBTYPE_AUDIODATA            107        ///< 音频数据
#define TKCC_CBTYPE_VIDEODATA            108        ///< 视频数据
#define TKCC_CBTYPE_SPEAKSTART            109        ///< 开始播报
#define TKCC_CBTYPE_SPEAKSTOP            110        ///< 停止播报
#define TKCC_CBTYPE_SPEAKERROR            111        ///< 播报错误
#define TKCC_CBYTPE_RECORDERROR            112        ///< 录像错误
#define TKCC_CBTYPE_RECOGNIZESTART        113        ///< 识别开始
#define TKCC_CBTYPE_RECOGNIZESTOP        114        ///< 识别停止
#define TKCC_CBTYPE_RECOGNIZERESULT        115        ///< 识别结果
#define TKCC_CBTYPE_RECOGNIZEERROR        116        ///< 识别出错
#define TKCC_CBTYPE_STREAMAUDIO            117        ///< 媒体音频
#define TKCC_CBTYPE_STREAMVIDEO            118        ///< 媒体视频

#endif    // TCHAT_DEFINE_H_
