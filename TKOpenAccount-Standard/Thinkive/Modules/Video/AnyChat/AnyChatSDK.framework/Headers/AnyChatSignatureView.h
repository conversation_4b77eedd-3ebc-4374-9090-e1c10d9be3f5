//
//  AnyChatSignatureView.h
//  AnyChatSDK
//
//  Created by Mac on 2019/3/13.
//  Copyright © 2019 Bairui. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 签名弹窗
 
 @param image 签名后的图片数据
 */
typedef void (^SignatureDoneCallback)(UIImage *image);


@interface AnyChatSignatureManager : NSObject

/**
 *  单例方法
 *
 *  @return 实例对象
 */
+ (AnyChatSignatureManager*)getInstance;

/** 销毁单例 */
+ (void)destory;

- (UIView *)createSignatureViewWithFrame:(CGRect)frame imagePath:(NSString *)imagePath callback:(SignatureDoneCallback)callback;

/**
 *  创建签名弹框
 *  @param frame 弹框的frame
 *  @param imagePath 签名图片的底图
 *  @param origin 签名在签名图片的位置
 *  @param callback 签名回调
 *  @return 签名弹框
 */
- (UIView *)createSignatureViewWithFrame:(CGRect)frame imagePath:(NSString *)imagePath andSignatureOrigin:(CGPoint)origin callback:(SignatureDoneCallback)callback;

@end


NS_ASSUME_NONNULL_END
