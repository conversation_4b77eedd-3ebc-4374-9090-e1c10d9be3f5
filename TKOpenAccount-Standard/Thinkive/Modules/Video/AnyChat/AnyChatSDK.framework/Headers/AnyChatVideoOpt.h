//
//  BRAC_VideoOpt.h
//  NewSDK
//
//  Created by bairuitech on 2017/7/21.
//  Copyright © 2017年 Raindy. All rights reserved.
//

#import <Foundation/Foundation.h>
typedef NS_ENUM(NSInteger, BRAC_VideoMode){
  
    BRAC_VIDEO_MODE_SERVER = 0,
    
    BRAC_VIDEO_MODE_LOCAL  = 1,
};

typedef NS_ENUM(NSInteger, BRAC_VideoQuality){
    
    BRAC_VIDEO_QUALITY_NORMAL = 2,
    
    BRAC_VIDEO_QUALITY_GOOD   = 3,
    
    BRAC_VIDEO_QUALITY_BEST   = 4,
};


typedef NS_ENUM(NSInteger, BRAC_VideoPreset){
    
    //效率优先
    BRAC_VIDEO_PRESET_EFFICIENCY  = 1,
    //性能优先
    BRAC_VIDEO_PRESET_PERFORMANCE = 2,
    //质量优先
    BRAC_VIDEO_PRESET_QUALITY     = 3,
};

@interface AnyChatVideoOpt : NSObject

@property (nonatomic, assign) BRAC_VideoMode mode;

@property (nonatomic, assign) BRAC_VideoQuality quality;

@property (nonatomic, assign) BRAC_VideoPreset preset;

@property (nonatomic, assign) int width;

@property (nonatomic, assign) int height;

@property (nonatomic, assign) int bitRate;

@property (nonatomic, assign) int fps;

@property (nonatomic, assign) int gop;

@property (nonatomic, assign) BOOL overlay;

@property (nonatomic, assign) int rotateMode;

@property (nonatomic, assign) BOOL GPURender;

@property (nonatomic, assign) BOOL autoRotation;

@property (nonatomic, assign) BOOL P2P;

@end
