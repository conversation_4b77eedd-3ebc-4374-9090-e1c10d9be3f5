//
//  AnyChatRecordTag.h
//  AnyChatSDK
//
//  Created by Yu on 2020/3/24.
//  Copyright © 2020 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatRecordOpt.h"
#import "AnyChatDefineT.h"

NS_ASSUME_NONNULL_BEGIN




/// AI标签事件类型
typedef NS_ENUM(NSInteger, AI_TAG_STATE) {
     ///标签参数错误
   AI_TAG_STATE_PARAMS_ERROR = 400000  ,
    ///未开始打标签
   AI_TAG_STATE_NOT_START = 400001  ,
     ///未找到待更新标签
   AI_TAG_STATE_NO_FIND_TO_UPDATE =  400002    ,
    ///任务taskId不唯一
   AI_TAG_STATE_TASKID_NOT_UNIQUE =  400003    ,
    ///录像文件名为空
   AI_TAG_STATE_RECORDFIlENAEME_NULL = 400004  ,
     ///流水号为空
   AI_TAG_STATE_TRADENO_NULL = 400005  ,
    ///打/更新标签成功
   AI_TAG_STATE_SUCCESS = 2000  ,
};


#pragma mark - 开始录像标签

@interface StartRecordTagOpt : NSObject

///流水号 必填参数
@property(nonatomic, strong)NSString* tradeNoStr;
///录像文件名 必填参数
@property(nonatomic, strong)NSString* tagFileNameStr;
/////时间戳字符串ms
//@property(nonatomic, strong)NSString* recordBeginTime;

@end


@interface BaseTagOpt : NSObject
/// 任务ID 必填参数
@property(nonatomic, strong)NSString* taskId;
///标签标题
@property(nonatomic, strong)NSString* tagTitle;
///扩展字段
@property(nonatomic, strong)NSDictionary* userDataDict;
///监测是否通过
@property(nonatomic, assign) int checkStatus;

@property(nonatomic, assign) AI_TAG_TYPE tagtype;
@end

#pragma mark - 开始人脸监测标签
@interface FaceCheckTagOpt : BaseTagOpt
///预期人脸数 人脸数大于0 必填参数
@property(nonatomic, assign)int expectedFaceNum;
///检测内容(人脸返回结果的result) 必填参数
@property(nonatomic, strong) NSString* resultStr;
///左边缩放范围
@property(nonatomic, assign) CGFloat xLeft;
///右边缩放范围
@property(nonatomic, assign) CGFloat xRight;
///上边边缩放范围
@property(nonatomic, assign) CGFloat yTop;
///下边缩放范围
@property(nonatomic, assign) CGFloat yBottom;

@end

#pragma mark - 开始人脸比对标签
@interface FaceCompareTagOpt : BaseTagOpt
///检测人脸结果最低分数 必填参数
@property(nonatomic, strong)NSMutableArray* passScoreArray;
//检测内容(人脸返回结果的result) 更新必填参数
@property(nonatomic, strong)NSString* resultStr;

@end

#pragma mark - 问答标签
@interface QuestionAnswerTagOpt : BaseTagOpt
///问题内容
@property(nonatomic, strong)NSString* checkQuestion;
///预设答案(多个预设答案组成的数组)
@property(nonatomic, strong)NSArray* expectAnswerArray;
///检测内容(问答返回结果的result)
@property(nonatomic, strong) NSMutableString* resultStr;

@end


#pragma mark - 朗读声明标签
@interface ReadRemindTagOpt : BaseTagOpt

///朗读声明内容 必填参数
@property(nonatomic, strong)NSString* readContentStr;
@property(nonatomic, strong)NSString* readTitleStr;
@property(nonatomic, strong)NSString* remindStr;

@end

@interface AnyChatRecordTag : NSObject

//是否开始打标签
@property(nonatomic, assign, getter=isBeginTaging)BOOL beginTaging;
/** 开始录像如果需要打标签 返回标签单例
*/
+(instancetype)shareInstance;
/** 开始录像如果需要打标签,都需要调用该方法，并传入标签初始参数
 @param  startRecordTagOpt tradeNoStr //流水号 tagFileNameStr;//录像文件名
 @return mesStateId //错误状态码
 400000    标签参数错误
 400001    标签未开始
 400002    未找到待更新标签
 400003    taskId不唯一
 */
-(int)startRecordTag:(StartRecordTagOpt*)startRecordTagOpt;

/** 在调用在框检测能力时调用增加在框检测标签
 @param  faceTagOpt 面部监测标签
 @return mesStateId //错误状态码
*/
-(int)addFaceCheckTag:(FaceCheckTagOpt*)faceTagOpt;
//callback:(AIResultCallBack)callback;

/**在拿到在框检测检测结果后更新标签
@param  faceTagOpt 面部监测标签
@return mesStateId //错误状态码
 */
-(int)updateFaceCheckTag:(FaceCheckTagOpt*)faceTagOpt;

/** 在调用人脸比对能力时调用增加人脸比对标签。
 @param  faceCompareTagOpt //人脸比对标签
 @return mesStateId //错误状态码
*/
-(int)addFaceCompareTag:(FaceCompareTagOpt*) faceCompareTagOpt;

/** 在拿到人脸比对检测结果后更新标签
 @param  faceCompareTagOpt //人脸比对标签
 @return mesStateId //错误状态码
*/
-(int)updateFaceCompareTag:(FaceCompareTagOpt*) faceCompareTagOpt;

/** 在语音播放问题时需要进行标签记录
 @param  quesTagOpt //语音问题标签
 @return mesStateId //错误状态码
*/
-(int)addQuestionTag:(QuestionAnswerTagOpt*) quesTagOpt;

///** 在语音播放问题时需要进行标签记录
// @param  quesTagOpt //语音问题标签
// @return mesStateId //错误状态码
//*/
//-(int)addQuestionTag:(QuestionAnswerTagOpt*) quesTagOpt;

/** 在开启语音识别时添加标签
 @param  quesTagOpt //语音问题标签
 @return mesStateId //错误状态码
*/
-(int) addAnswerTag:(QuestionAnswerTagOpt*) quesTagOpt;

/** 在收集完语音识别内容后更新标签。
 @param  quesTagOpt //语音问题标签
 @return mesStateId //错误状态码
*/
-(int) updateAnswerTag:(QuestionAnswerTagOpt*) quesTagOpt;

/** 在视频录制过程中最后朗读声明环节进行标签点的记录
 @param  readTagOpt //语音问题标签
 @return mesStateId //错误状态码
*/
-(int) addReadTag:(ReadRemindTagOpt*) readTagOpt;

/** 打标签结束时的回调*/
-(NSDictionary*)completeRecordTag;

@end

NS_ASSUME_NONNULL_END
