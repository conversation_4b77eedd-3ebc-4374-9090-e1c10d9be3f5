//
//  BRAC_PicMarkOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
@interface AnyChatPicMarkOpt : NSObject

/**
 图片透明度，默认为100，可不传（不传时，将应用默认值）
 */
@property (nonatomic, assign) int alpha;

/**
 图片水印在x轴方向上的起始位置（百分比，范围0~100）
 */
@property (nonatomic, assign) int posx;

/**
 图片水印在y轴方向上的起始位置（百分比，范围0~100）
 */
@property (nonatomic, assign) int posy;

/**
 图片的宽度，可传0，表示应用图片的原始宽度，传其他值时建议按高宽度比例来设置
 */
@property (nonatomic, assign) CGFloat overlayimgwidth;

/**
 图片的高度，可传0，表示应用图片的原始高度，传其他值时建议按高宽度比例来设置
 */
@property (nonatomic, assign) CGFloat overlayimgheight;

/**
 图片的路径，传入图片的本地路径（绝对路径）
 */
@property (nonatomic, copy) NSString *imagepath;
    

@end
