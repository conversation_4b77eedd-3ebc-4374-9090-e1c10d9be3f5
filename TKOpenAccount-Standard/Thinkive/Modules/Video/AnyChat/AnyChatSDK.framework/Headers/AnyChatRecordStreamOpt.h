//
//  BRAC_RecordStreamOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface AnyChatRecordStreamOpt : NSObject

/**
 用户ID
 */
@property (nonatomic, assign) int userId;

/**
 用户的视频流编号，用户可能存在多个摄像头
 */
@property (nonatomic, assign) int streamindex;

/**
 录制画面编号
 */
@property (nonatomic, assign) int recordindex;


@end
