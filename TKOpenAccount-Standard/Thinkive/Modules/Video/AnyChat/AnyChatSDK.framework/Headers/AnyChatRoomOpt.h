//
//  BRAC_RoomOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatResult.h"

@protocol RoomDelegate <NSObject>
    
@optional
//action : 1表示进入房间，0表示退出房间
/**
 用户进出房间通知事件

 @param data userId 用户ID 
             action 进入操作
 */
- (void)onRoomUserInAndOut:(NSDictionary *)data;
    
/**
 房间用户数变化通知事件

 @param data roomId 房间ID 
             userNum房间用户数
 */
- (void)onRoomUserChanged:(NSDictionary *)data;
    
/**
 接收房间内的文本消息通知事件

 @param data fromUserid 发送人ID  
             toUserid 接收人ID
             secret 私密
             msg 文本内容
 */
- (void)onRoomUserMsgReceived:(NSDictionary *)data;
    
@end


@interface AnyChatRoomOpt : NSObject

@property (nonatomic, weak) id<RoomDelegate>roomDelegate;
    
@end
