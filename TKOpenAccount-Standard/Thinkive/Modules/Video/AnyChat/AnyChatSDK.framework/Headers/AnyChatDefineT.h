//
//  AnyChatDefineT.h
//  AnyChatSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2019/4/8.
//  Copyright © 2019年 Bairui. All rights reserved.
//

#ifndef AnyChatDefineT_h
#define AnyChatDefineT_h

#define WM_GV 0x0400 + 200
#define WM_GV_SESSIONKEEP WM_GV + 19

// AI能力厂商
#define AIABILITY_FIRM_BAIRUITECH  0        ///< 佰锐科技
#define AIABILITY_FIRM_ALIYUN      1        ///< 阿里云
#define AIABILITY_FIRM_IFLYTEK     2        ///< 科大讯飞
#define AIABILITY_FIRM_ZHANGSHUTECH 3        ///< 掌数科技
#define AIABILITY_FIRM_MERCHANTSSECURITIES 5 ///< 招商证券
#define AIABILITY_FIRM_SOGOU               7 ///< 搜狗
#define AIABILITY_FIRM_INTSIG        16      ///< 上海合合信息

#define AIABILITY_FIRM_EXOCR        17      ///< 易道博识
#define AIABILITY_FIRM_JD           21      ///<  京东

// AI能力类型定义
#define ANYCHAT_AI_TYPE_ASR 1 ///< 语音识别
//问答模式
#define ANYCHAT_AI_TYPE_TTS 2 ///< 语音合成
#define ANYCHAT_AI_TYPE_VPR 3 ///< 声纹识别
#define ANYCHAT_AI_TYPE_AFR 4 ///< 人脸识别
#define ANYCHAT_AI_TYPE_OCR 5 ///< 文字识别
#define ANYCHAT_AI_TYPE_HWR 6 ///< 手写识别
#define ANYCHAT_AI_TYPE_NLU 7 ///< 语义理解
#define ANYCHAT_AI_TYPE_FPR 8 ///< 指纹识别
#define ANYCHAT_AI_TYPE_AIR 9 ///< 图像识别
#define ANYCHAT_AI_TYPE_VH  10 ///< 虚拟人
#define ANYCHAT_AI_TYPE_LD  11 ///< 活体检测
#define ANYCHAT_AI_TYPE_AIC 100 ///< 自动照片捕获（auto image capture)
#define ANYCHAT_AI_TYPE_APA 101 ///< 自动播报音频

// AI能力控制参数定义
#define ANYCHAT_AI_CTRL_ROBOT_INIT 1 ///< 初始化AI能力机器人
#define ANYCHAT_AI_CTRL_ROBOT_RELEASE 2 ///< 释放AI能力机器人
#define ANYCHAT_AI_CTRL_ROBOT_ACTIVETEST 3 ///< 机器人心跳检测
#define ANYCHAT_AI_CTRL_ABILITY_INVOKE 4 ///< AI能力调用
#define ANYCHAT_AI_CTRL_ABILITY_REQUEST 5 ///< 请求AI能力
#define ANYCHAT_AI_CTRL_ABILITY_RELEASE 6 ///< 释放AI能力
#define ANYCHAT_AI_CTRL_ABILITY_ACTIVETEST 7 ///< AI能力心跳检测
#define ANYCHAT_AI_CTRL_GETOPTION 8 ///< 参数查询
#define ANYCHAT_AI_CTRL_SETOPTION 9 ///< 参数设置

// AI事件通知
#define ANYCHAT_AI_EVENT_ROBOT_INITRESULT 1 ///< 机器人初始化结果
#define ANYCHAT_AI_EVENT_ROBOT_STATUS 2 ///< 机器人状态通知
#define ANYCHAT_AI_EVENT_ROBOT_ACTIVERET 3 ///< 机器人心跳检测结果
#define ANYCHAT_AI_EVENT_ABILITY_RESULT 4 ///< AI能力执行结果
#define ANYCHAT_AI_EVENT_ABILITY_STATUS 5 ///< AI能力状态通知
#define ANYCHAT_AI_EVENT_ABILITY_ACTIVERET 6 ///< AI能力心跳检测结果

// OCR文档类型
#define ANYCHAT_OCRTYPE_UNKNOW 0 ///< 未知文档
#define ANYCHAT_OCRTYPE_NORMALDOC 1 ///< 标准文档
#define ANYCHAT_OCRTYPE_IDCARDFRONT 2 ///< 身份证正面
#define ANYCHAT_OCRTYPE_IDCARDBACK 3 ///< 身份证背面
#define ANYCHAT_OCRTYPE_BINKCARD 4 ///< 银行卡
#define ANYCHAT_OCRTYPE_BUSINESSLICENSE 5 ///< 营业执照

//开关标记
#define ANYCHAT_AI_CTRL_START            1
#define ANYCHAT_AI_CTRL_STOP             0
//TTS人声类型
#define ANYCHAT_TTSTYPE_MENVOICE         1   ///< TTS男声
#define ANYCHAT_TTSTYPE_WOMENVOICE       2   ///< TTS女声
//TTS数据类型
#define AIROBOT_AUDIO_FORMAT_PCM                1        ///< pcm音频
#define AIROBOT_AUDIO_FORMAT_WAV                2        ///< wav音频
#define AIROBOT_AUDIO_FORMAT_MP3                3        ///< mp3音频
//TTS服务类型
#define ANYCHAT_TTSSVC_TTS               1    ///< 语音合成
//TTS工作模式
#define ANYCHAT_TTSMODE_FILE             1          ///< TTS转换后返回文件内容
#define ANYCHAT_TTSMODE_LIVEPLAY         2      ///< TTS转换后在线播放

//TTS

//AFR人脸识别工作模式
#define ANYCHAT_AFRMODE_IMAGEFACEDETECT          1 ///< 照片人脸检测：检测指定照片中是否存在人脸，并返回人脸位置
#define ANYCHAT_AFRMODE_STREAMFACEDETECT         2 ///< 视频流人脸检测：检测指定用户的视频流是否存在人脸，并返回人脸位置
#define ANYCHAT_AFRMODE_IMAGEFACECOMPARE         3 ///< 照片人脸比对：比较两张照片中的人脸是否相似，返回相似度及人脸位置
#define ANYCHAT_AFRMODE_STREAMFACECOMPARE        4 ///< 视频流人脸比对：比较视频流中的人脸和给定照片的人脸是否相似，返回相似度及人脸位置
#define ANYCHAT_AFRMODE_IMAGEFACEREC             5 ///< 照片人脸识别：识别照片中的人脸是谁
#define ANYCHAT_AFRMODE_STREAMFACEREC            6 ///< 视频流人脸识别：识别视频流中的人脸是谁
#define ANYCHAT_AFRMODE_IMAGETWOPERSONCOMPARE    7    ///< 照片两人人脸比对：一张含两人的图片与两张两个人单独的图片比对
#define ANYCHAT_AFRMODE_STREAMTWOPERSONCOMPARE   8    ///< 视频流两人人脸比对：一张含两人的图片与一张视频截图和一张人脸图片比对
#define ANYCHAT_AFRMODE_IMAGEFACEPOSTUREDETECT   9    ///< 图片人脸姿势检测（正脸、侧脸）
#define ANYCHAT_AFRMODE_STREAMFACEPOSTUREDETECT  10    ///< 视频流人脸姿势检测（正脸、侧脸）
/*
 ASR语音转文字
 */
//文本类型
#define ANYCHAT_ASRTYPE_NUMBER                1    ///< 数字
#define ANYCHAT_ASRTYPE_CHINESE               2    ///< 汉语普通话

//语音识别服务类型
#define ANYCHAT_ASRSVC_IAT                     1    ///< 语音识别
#define ANYCHAT_ASRSVC_IATLP                   2    ///< 语音识别+语义解析

//工作模式
#define ANYCHAT_ASRMODE_FILESTREAM            1    ///< 对录音文件进行语音识别
#define ANYCHAT_ASRMODE_LIVESTREAM            2    ///< 对实时流进行语音识别
#define ANYCHAT_ASRMODE_TAGRECORD             3    ///< 对录像文件加标签进行语音识别
#define ANYCHAT_ASRMODE_KEYWORDRECOGNITION    4    ///< 语音关键字识别
#define ANYCHAT_ASRMODE_DIGITRECOGNITION      5    ///< 数字识别

//语句标志
#define ANYCHAT_ASR_SENTENCE_STARTED          1    ///< 语句开始
#define ANYCHAT_ASR_SENTENCE_CHANGED          2    ///< 语句改变
#define ANYCHAT_ASR_SENTENCE_ENDED            3    ///< 语句结束

/*
 AIC 自动图片抓拍
 */
#define ANYCHAT_AICMODE_FACECAPTURE           1    ///< 自动抓拍人脸，人脸位置居中检测
#define ANYCHAT_AICMODE_FACECOMPARE           2    ///< 抓拍时进行人脸比对，和指令中的"content"数据进行比对
#define ANYCHAT_AICMODE_IDCARDFRONTOCR        3    ///< 抓拍时进行身份证正面的OCR识别
#define ANYCHAT_AICMODE_IDCARDBACKOCR         4    ///< 抓拍时进行身份证背面的OCR识别
#define ANYCHAT_AICMODE_BANKCARDOCR           5    ///< 抓拍时进行银行卡的OCR识别
#define ANYCHAT_AICMODE_NORMALDOCOCR          6    ///< 抓拍时进行标准文档OCR识别

/*
APM工作模式
*/
#define ANYCHAT_APMMODE_AUDIOPLAY            1    ///< 音频播放
#define ANYCHAT_APMMODE_VIDEOPLAY            2    ///< 视频播放
#define ANYCHAT_APMMODE_AUDIOVIDEOPLAY       3    ///< 音视频播放

/**
 APM控制编码
 */
#define ANYCHAT_STREAMPLAY_CTRL_PLAY         0    ///< 开始播放
#define ANYCHAT_STREAMPLAY_CTRL_START        1    ///< 继续播放
#define ANYCHAT_STREAMPLAY_CTRL_PAUSE        2    ///< 暂停播放
#define ANYCHAT_STREAMPLAY_CTRL_STOP         3    ///< 停止播放
#define ANYCHAT_STREAMPLAY_CTRL_SEEK         4    ///< 位置拖动
#define ANYCHAT_STREAMPLAY_CTRL_SPEEDCTRL    5    ///< 速度调整


// 虚拟人工作模式
#define ANYCHAT_VHMODE_RTMP                        1    ///< RTMP流地址播放

// 虚拟人控制码
#define ANYCHAT_VH_CTRL_PLAY                    1    ///< 播放虚拟人
#define ANYCHAT_VH_CTRL_TEXT                    2    ///< 发送文本
#define ANYCHAT_VH_CTRL_STOP                    999 ///< 释放虚拟人(内部使用)
//自定义虚拟客服控制码
#define ANYCHAT_STREAMPLAY_CTRL_STARTVH 101       ///< 开始播放虚拟形象
#define ANYCHAT_STREAMPLAY_CTRL_STOPVH 102        ///< 结束播放虚拟形象


/*
LD工作模式
*/
#define ANYCHAT_LDMODE_FILE              1    ///< 视频文件
#define ANYCHAT_LDMODE_STREAM            2    ///< 视频流

//Cloud SDK业务数据传输控制错误代码
#define AC_ERROR_BUSINESSTRANSFER_TIMEOUT            1000010        ///< 业务请求超时
#define AC_ERROR_BUSINESSTRANSFER_PARAMERROR         1000011        ///< 业务请求参数错误

//机器人错误码
#define AC_ERROR_AI_ABILITY_AIROBOTIDEXIST            200000        ///< 机器人ID已存在
#define AC_ERROR_AI_ABILITY_AITASKIDEXIST             200001        ///< ai任务ID已存在
#define AC_ERROR_AI_ABILITY_AIOBJNOTEXIST             200002        ///< ai对象不存在
#define AC_ERROR_AI_ABILITY_PARAMINVALID              200003        ///< ai参数错误
#define AC_ERROR_AI_ABILITY_FUNCTIONNOTSUPPORT        200004        ///< ai能力不支持
#define AC_ERROR_AI_ABILITY_UNKNOWAIFUNCTION          200005        ///< 未知AI能力
#define AC_ERROR_AI_ABILITY_HTTPREQUESTFAIL           200006        ///< HTTP请求失败
#define AC_ERROR_AI_ABILITY_AIREQUESTINITFAIL         200007        ///< ai请求初始化失败
#define AC_ERROR_AI_ABILITY_AIREQUESTFAIL             200008        ///< ai请求失败
#define AC_ERROR_AI_ABILITY_AIREQUESTTIMEOUT          200009        ///< ai请求超时
#define AC_ERROR_AI_ABILITY_AITHREADDATAINVALID       200010        ///< ai线程数据信息无效
#define AC_ERROR_AI_ABILITY_AITHREADOBJINVALID        200011        ///< ai线程对象无效
#define AC_ERROR_AI_ABILITY_AIRELATEDATAINVALID       200012        ///< ai关联数据无效
#define AC_ERROR_AI_ABILITY_HEARTBEATTIMEOUT          200013        ///< 心跳超时
#define AC_ERROR_AI_ABILITY_ROBOTOFFLINE              200014        ///< 机器人离线
#define AC_ERROR_AI_ABILITY_CONTENTOVERLENGTH         200015        ///< 内容超长
#define AC_ERROR_AI_ABILITY_COMMANDOVERLENGTH         200016        ///< 信令超长
#define AC_ERROR_AI_ABILITY_AIOVERCUNCURRENCY         200017        ///< ai请求超过并发数
#define AC_ERROR_AI_ABILITY_MEDIAPARAMINVALID         200018        ///< 媒体参数无效
#define AC_ERROR_AI_ABILITY_REQUESTIDEXIST            200019        ///< 请求ID已存在
#define AC_ERROR_AI_ABILITY_ABNORMALERROR             200020        ///< ai异常错误
#define AC_ERROR_AI_ABILITY_AIPARAMSETOBJINVALID      200021        ///< ai参数设置对象不存在
#define AC_ERROR_AI_ABILITY_UNSUPPORTAIMODE           200022        ///< 不支持的AI处理模式
#define AC_ERROR_AI_ABILITY_INVALIDHOSTADDR           200023        ///< 无效地址


/// AI标签事件类型
typedef NS_ENUM(NSInteger, AI_TAG_TYPE) {
    ///人脸监测标签
   AI_TAG_TYPE_FACECHECK = 1  ,
    ///人脸对比标签
   AI_TAG_TYPE_FACECOMPARE = 2  ,
    ///问题标签
   AI_TAG_TYPE_QUESTION =  3    ,
    ///回答便签
   AI_TAG_TYPE_ANSWER =  4   ,
    ///朗读便签
   AI_TAG_TYPE_READ  = 5    ,
};

#define ZhaoShangTTS 0
#define HUAXIN_FACECOMPARE 1

// AI能力厂商
typedef NS_ENUM(NSInteger, BRAC_AI_FIRM) {
    
    BRAC_AI_FIRM_BAIRUITECH = AIABILITY_FIRM_BAIRUITECH, //佰锐科技
    BRAC_AI_FIRM_ALIYUN = AIABILITY_FIRM_ALIYUN,         //阿里云
    BRAC_AI_FIRM_IFLYTEK =  AIABILITY_FIRM_IFLYTEK,       //科大讯飞
    BRAC_AI_FIRM_ZHANGSHUTECH = AIABILITY_FIRM_ZHANGSHUTECH, //掌数科技
    BRAC_AI_FIRM_MERCHANTSSECURITIES = AIABILITY_FIRM_MERCHANTSSECURITIES, //招商证券
    BRAC_AI_FIRM_SOGOU = AIABILITY_FIRM_SOGOU, //SOGOU
    BRAC_AI_FIRM_INTSIG = AIABILITY_FIRM_INTSIG, //上海合合信息
    BRAC_AI_FIRM_EXOCR = AIABILITY_FIRM_EXOCR, //易道博识
    BRAC_AI_FIRM_JD = AIABILITY_FIRM_JD, //京东
};

// OCR文档类型
typedef NS_ENUM(NSInteger, BRAC_AI_OCRTYPE){
    BRAC_AI_OCRTYPE_UNKNOW          = 0,       // 未知文档
    
    BRAC_AI_OCRTYPE_NORMALDOC       = 1,       // 标准文档
    
    BRAC_AI_OCRTYPE_IDCARDFRONT     = 2,       // 身份证正面
    
    BRAC_AI_OCRTYPE_IDCARDBACK      = 3,       // 身份证背面
    
    BRAC_AI_OCRTYPE_BINKCARD        = 4,       // 银行卡
    
    BRAC_AI_OCRTYPE_BUSINESSLICENSE = 5,       // 营业执照
};

// TTS声音类型
typedef NS_ENUM(NSInteger, BRAC_AI_TTSTYPE){
    BRAC_AI_TTSTYPE_MENVOICE         = ANYCHAT_TTSTYPE_MENVOICE,       // 男声
    BRAC_AI_TTSTYPE_WOMENVOICE       = ANYCHAT_TTSTYPE_WOMENVOICE,     // 女声
};

// TTS文件数据类型
typedef NS_ENUM(NSInteger, BRAC_AI_TTSAUDIO_FORMAT){
    BRAC_AI_TTSAUDIO_FORMAT_PCM       = AIROBOT_AUDIO_FORMAT_PCM,    // pcm
    BRAC_AI_TTSAUDIO_FORMAT_MP3       = AIROBOT_AUDIO_FORMAT_MP3,     //mp3
    BRAC_AI_TTSAUDIO_FORMAT_WAV       = AIROBOT_AUDIO_FORMAT_WAV     //wav
};

// TTS工作模式
typedef NS_ENUM(NSInteger, BRAC_AI_TTSMODE){
    BRAC_AI_TTSMODE_FILE         = ANYCHAT_TTSMODE_FILE,            // 返回音频文件
    BRAC_AI_TTSMODE_LIVEPLAY     = ANYCHAT_TTSMODE_LIVEPLAY,        // 在线播放
};

//AFR人脸识别工作类型
typedef NS_ENUM(NSInteger, BRAC_AI_AFRTYPE){
    BRAC_AI_AFRTYPE_BASE64      = 1,
    BRAC_AI_AFRTYPE_URL         = 2,        //暂不支持
};
//AFR人脸识别工作模式
typedef NS_ENUM(NSInteger, BRAC_AI_AFRMODE){
    BRAC_AI_AFRMODE_IMAGEFACEDETECT         = ANYCHAT_AFRMODE_IMAGEFACEDETECT,          ///< 照片人脸检测：检测指定照片中是否存在人脸，并返回人脸位置
    BRAC_AI_AFRMODE_STREAMFACEDETECT        = ANYCHAT_AFRMODE_STREAMFACEDETECT,         ///< 视频流人脸检测：检测指定用户的视频流是否存在人脸，并返回人脸位置
    BRAC_AI_AFRMODE_IMAGEFACECOMPARE        = ANYCHAT_AFRMODE_IMAGEFACECOMPARE,         ///< 照片人脸比对：比较两张照片中的人脸是否相似，返回相似度及人脸位置
    BRAC_AI_AFRMODE_STREAMFACECOMPARE       = ANYCHAT_AFRMODE_STREAMFACECOMPARE,        ///< 视频流人脸比对：比较视频流中的人脸和给定照片的人脸是否相似，返回相似度及人脸位置
    BRAC_AI_AFRMODE_IMAGEFACEREC            = ANYCHAT_AFRMODE_IMAGEFACEREC,             ///< 照片人脸识别：识别照片中的人脸是谁
    BRAC_AI_AFRMODE_STREAMFACEREC           = ANYCHAT_AFRMODE_STREAMFACEREC,            ///< 视频流人脸识别：识别视频流中的人脸是谁
    BRAC_AI_AFRMODE_IMAGETWOPERSONCOMPARE   = ANYCHAT_AFRMODE_IMAGETWOPERSONCOMPARE,     ///< 照片两人人脸比对：
    BRAC_AI_AFRMODE_STREAMTWOPERSONCOMPARE  = ANYCHAT_AFRMODE_STREAMTWOPERSONCOMPARE,   ///< 视频流两人人脸比对：
    BRAC_AI_AFRMODE_IMAGEFACEPOSTUREDETECT  = ANYCHAT_AFRMODE_IMAGEFACEPOSTUREDETECT,    ///< 图片人脸正侧脸检测
    BRAC_AI_AFRMODE_STREAMFACEPOSTUREDETECT = ANYCHAT_AFRMODE_STREAMFACEPOSTUREDETECT,   ///< 视频流人脸正侧脸检测
};

//ASR工作模式
typedef NS_ENUM(NSInteger, BRAC_AI_ASRMODE){
    BRAC_AI_ASRMODE_FILESTREAM          = ANYCHAT_ASRMODE_FILESTREAM,          ///< 对录音文件进行语音识别
    BRAC_AI_ASRMODE_LIVESTREAM          = ANYCHAT_ASRMODE_LIVESTREAM,          ///< 对实时流进行语音识别
    BRAC_AI_ASRMODE_TAGRECORD           = ANYCHAT_ASRMODE_TAGRECORD,           ///< 对录像文件加标签进行语音识别
    BRAC_AI_ASRMODE_KEYWORDRECOGNITION  = ANYCHAT_ASRMODE_KEYWORDRECOGNITION,  ///< 语音关键字识别
    BRAC_AI_ASRMODE_DIGITRECOGNITION    = ANYCHAT_ASRMODE_DIGITRECOGNITION,    ///< 数字识别
};

//ASR服务类型
typedef NS_ENUM(NSInteger, BRAC_AI_ASRSVC){
    BRAC_AI_ASRSVC_IAT = ANYCHAT_ASRSVC_IAT,              ///< 语音识别
    BRAC_AI_ASRSVC_IATLP = ANYCHAT_ASRSVC_IATLP           ///< 语音识别+语义理解
};

//ASR识别文本类型
typedef NS_ENUM(NSInteger, BRAC_AI_ASRTYPE){
    BRAC_AI_ASRTYPE_NUMBER           = ANYCHAT_ASRTYPE_NUMBER,          ///< 数字
    BRAC_AI_ASRTYPE_CHINESE          = ANYCHAT_ASRTYPE_CHINESE,         ///< 汉语普通
};

//AIC工作模式
typedef NS_ENUM(NSInteger, BRAC_AI_AICMODE){
    BRAC_AI_AICMODE_FACECAPTURE          = ANYCHAT_AICMODE_FACECAPTURE,          ///< 自动抓拍人脸，人脸位置居中检测
    BRAC_AI_AICMODE_FACECOMPARE          = ANYCHAT_AICMODE_FACECOMPARE,          ///< 抓拍时进行人脸比对，和指令中的"content"数据进行比对
    BRAC_AI_AICMODE_IDCARDFRONTOCR       = ANYCHAT_AICMODE_IDCARDFRONTOCR,       ///< 抓拍时进行身份证正面的OCR识别
    BRAC_AI_AICMODE_IDCARDBACKOCR        = ANYCHAT_AICMODE_IDCARDBACKOCR,        ///< 抓拍时进行身份证背面的OCR识别
    BRAC_AI_AICMODE_BANKCARDOCR          = ANYCHAT_AICMODE_BANKCARDOCR,          ///< 抓拍时进行银行卡的OCR识别
    BRAC_AI_AICMODE_NORMALDOCOCR         = ANYCHAT_AICMODE_NORMALDOCOCR,         ///< 抓拍时进行标准文档OCR识别
};

//APM工作模式
typedef NS_ENUM(NSInteger, BRAC_AI_APMMODE){
    BRAC_AI_APMMODE_AUDIOPLAY          = ANYCHAT_APMMODE_AUDIOPLAY,            ///< 音频播放
    BRAC_AI_APMMODE_VIDEOPLAY          = ANYCHAT_APMMODE_VIDEOPLAY,            ///< 视频播放
    BRAC_AI_APMMODE_AUDIOVIDEOPLAY     = ANYCHAT_APMMODE_AUDIOVIDEOPLAY,       ///<音视频播放
};

//APM播放控制编码
typedef NS_ENUM(NSInteger, BRAC_AI_STREAMPLAYMODE){
    BRAC_AI_STREAMPLAYMODE_PLAY    = ANYCHAT_STREAMPLAY_CTRL_PLAY,///< 开始播放
    BRAC_AI_STREAMPLAYMODE_START   = ANYCHAT_STREAMPLAY_CTRL_START,///< 继续播放
    BRAC_AI_STREAMPLAYMODE_PAUSE   = ANYCHAT_STREAMPLAY_CTRL_PAUSE,///<暂停播放
    BRAC_AI_STREAMPLAYMODE_STOP    = ANYCHAT_STREAMPLAY_CTRL_STOP,///<停止播放
    BRAC_AI_STREAMPLAYMODE_SEEK    = ANYCHAT_STREAMPLAY_CTRL_SEEK,///<位置拖动
    BRAC_AI_STREAMPLAYMODE_SPEED   = ANYCHAT_STREAMPLAY_CTRL_SPEEDCTRL,///<速度调整
};

//AI工作类型
typedef NS_ENUM(NSInteger, BRAC_AI_WORKMODE){
    BRAC_AI_WORKMODE_NORMAL      = 0,        //正常模式，OCR、TTS、ASR可配置，该模式下，返回的结果为真实检测结果
    BRAC_AI_WORKMODE_TEST        = 1,        //测试模式，OCR、TTS、ASR可配置，该模式下，返回的结果为固定内容
};
// VH工作模式
typedef NS_ENUM(NSInteger, BRAC_AI_VHMODE){
    BRAC_AI_VHMODE_RTMP         = ANYCHAT_VHMODE_RTMP,            // RTMP流地址播放
};

// 虚拟人控制码
typedef NS_ENUM(NSInteger, BRAC_AI_VHCTRLCODE){
    BRAC_AI_VHCTRLCODE_PLAY    = ANYCHAT_VH_CTRL_PLAY    ,///< 播放虚拟人
    BRAC_AI_VHCTRLCODE_TEXT   = ANYCHAT_VH_CTRL_TEXT,///< 发送文本
    BRAC_AI_VHCTRLCODE_STOP   = ANYCHAT_VH_CTRL_STOP,///<释放虚拟人(内部使用)
    BRAC_AI_VHCTRLCODE_CUSTOMERAGENTSTART  = ANYCHAT_STREAMPLAY_CTRL_STARTVH,///< 开始播放虚拟形象
    BRAC_AI_VHCTRLCODE_CUSTOMERAGENTSTOP = ANYCHAT_STREAMPLAY_CTRL_STOPVH,///< 结束播放虚拟形象
};

// LD工作模式
typedef NS_ENUM(NSInteger, BRAC_AI_LDMODE){
    BRAC_AI_LDMODE_FILE       = ANYCHAT_LDMODE_FILE,            // 视频文件
    BRAC_AI_LDMODE_STREAM     = ANYCHAT_LDMODE_STREAM,        // 视频流
};

/**
 * LD 动作类型。1.眨眨眼2.张张嘴3.向左摇头4.向右摇头5.点点头6.摇头
 */
typedef NS_ENUM(NSInteger, BRAC_AI_LDACTION_TYPE) {
    /** 眨眼检测 */
    BRAC_AI_LDACTION_BLINK = 1,
    /** 张嘴检测 */
    BRAC_AI_LDACTION_MOUTH,
    /** 向左转头检测 */
    BRAC_AI_LDACTION_HEAD_TO_LEFT,
    /** 向右转头检测 */
    BRAC_AI_LDACTION_HEAD_TO_RIGHT,
    /** 上下点头检测 */
    BRAC_AI_LDACTION_NOD,
    /** 摇头检测 */
    BRAC_AI_LDACTION_YAW
};

@class AnyChatResult;
typedef void (^AICallBack)(NSDictionary *data);

typedef void (^AIResultCallBack)(AnyChatResult *result, NSDictionary *data);

////标签回调
//typedef void (^RecordTagCallBack)(NSDictionary *data);
#endif /* AnyChatDefineT_h */
