//
//  BRAC_VideoDevice.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>

@interface AnyChatCamera : UIImageView//NSObject

@property (nonatomic, assign) BOOL disableOverLay;
/**
 编号
 */
@property (nonatomic, assign) int no;

///**
// 当前系统是否选用了该设备
// */
//@property (nonatomic, assign) BOOL isUsed;

/**
 名称
 */
@property (nonatomic, copy) NSString *name;


/**
 打开本地摄像头,并在页面上显示视频画面

 @param renderView 显示视频的视图
 */
- (void)openWithRenderView:(UIView *)renderView;

/**
 关闭视频
 */
- (void)close;
    

@end
