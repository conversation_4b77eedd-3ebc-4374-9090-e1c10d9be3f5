//
//  BRAC_AudioDevice.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface AnyChatMicrophone : NSObject
/**
 编号
 */
@property (nonatomic, assign) int no;
///**
// 当前系统是否选用了该设备
// */
//@property (nonatomic, assign) BOOL isUsed;
/**
 音频静音检测控制
 */
@property (nonatomic, copy) NSString *vadctrl;

/**
 音频设备名称
 */
@property (nonatomic, copy) NSString *name;

/**
 打开麦克风
 */
- (void)open;
    
/**
 关闭麦克风
 */
- (void)close;
@end
