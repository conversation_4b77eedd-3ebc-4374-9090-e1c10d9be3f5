//
//  BRAC_FileOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatResult.h"
@protocol FileDelegate <NSObject>
    
@optional
    
/**
 接收文件回调

 @param data userid 文件发送人ID
             fileLength 文件本地存储路径
             fileLength 文件大小
             filename 文件名
 */
- (void)onFileReceived:(NSDictionary *)data;
    
/**
 接收透明通道指令回调

 @param data userId 发送人ID 
             buffer 透明通道内容
 */
- (void)onReceiveBuffer:(NSDictionary *)data;
    
@end
@interface AnyChatFileOpt : NSObject
    

@property (nonatomic, weak) id<FileDelegate>fileDelegate;

@end
