//
//  AnyChatDetachableService.h
//  AnyChatSDK
//
//  Created by lemon on 2020/3/20.
//  Copyright © 2020年 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@protocol AnyChatDetachableServiceDelegate <NSObject>

/**
 连接插拔式服务器回调

 @param data 返回数据
 */
- (void)onAnyChatDetachableServiceConnect:(NSDictionary *)data;

/**
 插拔式接口返回数据回调
 
 @param data 返回数据
 */
- (void)onAnyChatDetachableServiceReceiveData:(NSDictionary *)data;

/**
 插拔式接口断开连接回调
 
 @param data 返回数据
 */
- (void)onAnyChatDetachableServiceDisConnect:(NSDictionary *)data;
@end


@interface AnyChatDetachableService : NSObject
@property (nonatomic, weak) id<AnyChatDetachableServiceDelegate> delegate;
@property (nonatomic, copy, readonly) NSString *detachableId;

/**
 连接插拔式服务

 @param detachableId guid,连接唯一标识符
 @param serverFlag 服务标识符
 @return 连接结果
 */
- (NSString *)connectToDetachableServer:(NSString *)detachableId serverFlag:(NSString *)serverFlag;

/**
 向服务器发送数据
 
 @param data 发送数据
 @return 返回结果
 */
- (NSString *)sendData:(NSDictionary *)data;

/**
 断开插拔式服务器连接
 如非必要，不需要主动调用，SDK断开连接之后会自动断开连接
 
 @return 断开结果
 */
- (NSString *)closeDetachableServer;


/**
 处理服务器返回的信息

 @param data 返回信息
 */
- (void)handleDetachableServiceData:(NSString *)data;
@end

NS_ASSUME_NONNULL_END
