//
//  BRAC_InitOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatResult.h"

@protocol LoginDelegate <NSObject>
    
@required
    
/**
 登录成功回调
 @param data userId 用户ID
 */
- (void)onLogin:(NSDictionary *)data;
    
/**
 连接失败回调
 @param result 返回错误信息
 */
- (void)onDisconnect:(AnyChatResult *)result;

/**
 连接断开回调
 @param result 返回错误信息
 */
- (void)OnLinkClose:(AnyChatResult *)result;
@optional
/**
 会话保持回调
 @param data status 表示会话保持状态：0 会话保持开始， 1 会话保持结束
 */
- (void)onSessionKeep:(NSDictionary *)data;

@end


@interface AnyChatInitOpt : NSObject

@property (nonatomic, weak) id<LoginDelegate>loginDelegate;
    
/**
 注册AnyChat的应用ID
 */
@property (nonatomic, copy) NSString *appId;

/**
 用户字符串ID
 */
@property (nonatomic, copy) NSString *strUserId;

/**
 用户ID
 */
@property (nonatomic, assign) int userId;

/**
 密码
 */
@property (nonatomic, copy) NSString *passWord;

/**
 用户昵称
 */
@property (nonatomic, copy) NSString *nickName;

/**
 应用签名
 */
@property (nonatomic, copy) NSString *sign;

/**
 时间戳
 */
@property (nonatomic, copy) NSString *timeStamp;
    
/**
 服务器IP
 */
@property (nonatomic, copy) NSString *serverIp;
    
/**
 服务器端口
 */
@property (nonatomic, copy) NSString *serverPort;

/**
 额外参数
 */
@property (nonatomic, copy) NSString *exParam;

@end
