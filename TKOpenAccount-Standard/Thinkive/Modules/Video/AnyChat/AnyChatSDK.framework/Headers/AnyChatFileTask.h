//
//  BRAC_FileTask.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatResult.h"
#import "AnyChatFileUploadOpt.h"
@protocol TaskDelegate <NSObject>
    
@optional

    
/**
 文件传输完成回调

 @param result 传输操作结果
 @param data data
 */
- (void)onFileTransferDone:(AnyChatResult *)result data:(NSDictionary *)data;
  
//status 1--准备; 2--传输中; 3--完成; 4--任务被取消
/**
 任务状态回调

 @param data taskId 任务ID 
             process 进度 
             bitRate 位率 
             status  状态
 */
- (void)onTaskStatusChanged:(NSDictionary *)data;
    
/**
 文件上传完成回调

 @param result 上传操作结果
 @param data  filePath 文件上传远程路径  
              fileLength 文件大小  
              filename 文件名
 */
- (void)onFileUploadDone:(AnyChatResult *)result data:(NSDictionary *)data;


/**
 文件下载进度回调

 @param data taskId 任务ID 
             fileid fileid
             process 进度
 */
- (void)onFileDownloadStatusChanged:(NSDictionary *)data;

/**
 文件下载完成回调

 @param result  下载操作结果
 @param data fileid 文件id
 */
- (void)onFileDownloadDone:(AnyChatResult *)result data:(NSDictionary *)data;

@end

@interface AnyChatFileTask : NSObject

/**
 代理
 */
@property (nonatomic, weak) id<TaskDelegate>delegate;
/**
 */
@property (nonatomic, assign) int userId;
/**
 task唯一标识
 */
@property (nonatomic, copy,readonly) NSString *guid;
@property (nonatomic, assign) int taskId;//已弃用，使用guid

/**
 上传文件本地路径
 */
@property (nonatomic, copy) NSString *filePath;
/**
 返回文件状态时间间隔，单位为秒
 */
@property (nonatomic, assign) int intervalTime;
/**
 指定文件上传后的目标文件名。
 */
@property (nonatomic, copy) NSString *filename;
/**
 文件上传分类子目录，通过设置该字段的值可以将文件上传到不同的分类子目录中。
 */
@property (nonatomic, copy) NSString *category;
/**
 自定义参数:json字符串
 */
@property (nonatomic,copy)NSString *strJson;
/**
 * 解密秘钥
 */
@property (nonatomic, copy) NSString *encryptionKey;
/**
* 是否覆盖上传(默认相同文件不覆盖上传)
*/
@property (nonatomic, assign) BOOL isOverlayUpload;

/**
 * 传输最大码率（参数为int型，0 不限制，以最快速率传输[默认]， 否则表示限制码率，单位为：bps）
 */
@property (nonatomic, assign) int maxBitrate;

/**
 任务开始
 */
- (void)start;
/**
 任务取消
 */
- (void)cancel;
/**
 获取任务状态

 @return 任务状态
 */
- (NSDictionary *)getStatus;


@end
