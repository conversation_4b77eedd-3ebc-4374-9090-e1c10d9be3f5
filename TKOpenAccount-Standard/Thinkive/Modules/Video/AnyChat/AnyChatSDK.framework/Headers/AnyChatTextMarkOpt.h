//
//  BRAC_TextMarkOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface AnyChatTextMarkOpt : NSObject

/**
 文字颜色，文字默认为白色（0xffffff，颜色值采用十六进制rgb格式），可不传（不传时，将应用默认值）
 */
@property (nonatomic, copy) NSString *fontcolor;

/**
 文字的透明度，默认为100，可不传（不传时，将应用默认值）
 */
@property (nonatomic, assign) int alpha;

/**
 文字水印在x轴方向上的起始位置（百分比，范围0~100)
 */
@property (nonatomic, assign) int posx;

/**
 文字水印在y轴方向上的起始位置（百分比，范围0~100）
 */
@property (nonatomic, assign) int posy;

/**
 文字大小，默认为23号大小，可不传（不传时，将应用默认值）
 */
@property (nonatomic, assign) int fontsize;

/**
 文字内容，若加上[timestamp]，则表示增加时间戳
 */
@property (nonatomic, copy) NSString *text;

/**
 字体库路径
 */
@property (nonatomic, copy) NSString *fontfile;

/**
 使用服务器时间:0使用设备本地时间,1使用anychat服务器时间
 */
@property (nonatomic, assign) int useservertime;

@end
