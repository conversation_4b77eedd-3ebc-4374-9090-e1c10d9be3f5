//
//  BRAC_AudioOpt.h
//  NewSDK
//
//  Created by bairuitech on 2017/7/21.
//  Copyright © 2017年 Raindy. All rights reserved.
//


#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, BRAC_CaptureMode){
    
    
    //发言模式
    BRAC_CAPTURE_MODE_SPEAK    = 0,
    
    //放歌模式
    BRAC_CAPTURE_MODE_SING     = 1,
    
    //卡拉OK模式
    BRAC_CAPTURE_MODE_KARAOKE  = 2,
    
    //线路输入模式
    BRAC_CAPTURE_MODE_LINE     = 3,
    
};


typedef NS_ENUM(NSInteger, BRAC_MicBoost){
    
    
    //取消
    BRAC_MIC_BOOST_CANCEL  = 0,
    
    //选中
    BRAC_MIC_BOOST_SELECT  = 1,
    
    //设备不存在[查询时返回值]
    BRAC_MIC_BOOST_NONE    = 2,
    
};


typedef NS_ENUM(NSInteger, BRAC_PlayDrvCtrl){
    
    
    //默认驱动
    BRAC_PLAYDRV_CTRL_DEFAULT    = 0,
    
    //DSound驱动
    BRAC_PLAYDRV_CTRL_DSOUND     = 1,
    
    //WaveOut驱动
    BRAC_PLAYDRV_CTRL_WAVEOUT    = 2,
    
};


typedef NS_ENUM(NSInteger, BRAC_RecordDrvCtrl){
    
    
    //默认驱动
    BRAC_RECORDDRV_CTRL_DEFAULT    = 0,
    
    //DSound驱动
    BRAC_RECORDDRV_CTRL_DSOUND     = 1,
    
    //WaveIn驱动
    BRAC_RECORDDRV_CTRL_WAVEOUT    = 2,
    
};

@interface AnyChatAudioOpt : NSObject

// 音频静音检测控制（参数为：int型：1打开，0关闭）
@property (nonatomic, assign) BOOL vadCtrl;

// 音频噪音抑制控制（参数为：int型：1打开，0关闭）
@property (nonatomic, assign) BOOL nsCtrl;

// 音频回音消除控制
@property (nonatomic, assign) BOOL echoCtrl;

// 音频自动增益控制
@property (nonatomic, assign) BOOL agcCtrl;


//音频采集模式设置（参数为：int型：0 发言模式，1 放歌模式，2 卡拉OK模式，3线路输入模式
@property (nonatomic, assign) BRAC_CaptureMode captureMode;

//音频采集Mic增强控制
@property (nonatomic, assign) BRAC_MicBoost micBoost;

// /< 根据音频采集模式，自动选择合适的相关参数，包括编码器、采样参数、码率参数等（参数为int型：1
// 启用，0 关闭[默认]）
@property (nonatomic, assign) BOOL autoParam;

// /< 设置单声道模式下音频编码目标码率（参数为：int型，单位：bps）
@property (nonatomic, assign) int monoBitRate;

// /< 设置双声道模式下音频编码目标码率（参数为：int型，单位：bps）
@property (nonatomic, assign) int stereoBitRate;

// /< 音频播放驱动选择（参数为：int型，0默认驱动， 1 DSound驱动， 2
// WaveOut驱动， 3 Java播放[Android平台使用]）
@property (nonatomic, assign) BRAC_PlayDrvCtrl playDrvCtrl;

// 设置软件音量模式控制（参数为int型，1打开，0关闭[默认]），使用软件音量模式，将不会改变系统的音量设置
@property (nonatomic, assign) BOOL softVolMode;

// /< 音频采集驱动控制（参数为int型，0默认驱动， 1 DSound驱动， 2
// WaveIn驱动， 3 Java采集[Android平台使用]）
@property (nonatomic, assign) BRAC_RecordDrvCtrl recordDrvCtrl;

// /< 音频回声消除延迟参数设置（参数为int型，单位：ms）
@property (nonatomic, assign) int echoDelay;

// < 音频噪音抑制水平参数设置（参数为int型，0 - 3，默认为2，值越大抑制水平越高，抑制噪音的能力越强）
@property (nonatomic, assign) int nsLevel;

// /< 录像音频码率设置（参数为：int型，单位：bps）
@property (nonatomic, assign) int recordAudiobr;



@end
