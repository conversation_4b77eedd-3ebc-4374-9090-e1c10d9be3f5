//
//  AIInterface.h
//  AnyChatSDK
//
//  Created by Yu on 2020/6/8.
//  Copyright © 2020 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatDefineT.h"

@class AIAsrOpt,AITtsOpt,AIAicOpt,AIAfrOpt,GestureDetectOpt;

NS_ASSUME_NONNULL_BEGIN

@protocol AIAbilityInterface <NSObject>

#pragma mark - AI新接口
/**语音识别 （新接口）
 @param  opt ASR配置参数
 @param  targetUserId 对房间内指定的人声进行识别，如果不传或0值是对发起操作者的人声进行识别。
 @param  callback 结果回调
 @return taskId
 */
- (NSString *)startASRWithOption:(AIAsrOpt*)opt targetUserId:(int)targetUserId callback:(AIResultCallBack)callback;

/**语音检测关闭
 @param  taskId 语音检测开启返回的taskId
 */
- (void)stopASR:(NSString *)taskId;

/**文本转语音 （新接口）
 @param  opt TTS配置参数
 @param  callback 结果回调
 @return taskId
 */
- (NSString *)startTTSWithOption:(AITtsOpt*)opt callback:(AIResultCallBack)callback;

/**人脸捕获开始 （新接口）
 @param  opt     参数
 @param  targetUserId 对房间内指定的用户的视频流进行人脸捕获，不传或为0值是对发起操作者的视频流进行人脸捕获。
 @param  callback 结果回调
 @return 返回taskId
 */
- (NSString *)startFaceCaptureWithOption:(AIAicOpt*)opt targetUserId:(int)targetUserId callback:(AIResultCallBack)callback;


/**
 人脸识别开始  （新接口）

 @param opt 参数
 @param targetUserId 对房间内指定的用户的视频流进行人脸检测，不传或者为0值是对发起操作者的视频流进行人脸检测
 @param callback 结果回调
 @return 返回taskId
 */
- (NSString *)startFaceDetectWithOption:(AIAfrOpt *)opt targetUserId:(int)targetUserId callback:(AIResultCallBack)callback;

/**视频流人脸检测关闭
 @param  taskId 人脸检测开启返回的taskId
 */
-(void)stopFaceDetect:(NSString *)taskId;
/**
 手势识别开始  （新接口）

@param opt 参数
@param targetUserId 对房间内指定的用户的视频流进行手势检测，不传或者为0值是对发起操作者的视频流进行人手势检测
@param callback 结果回调
@return 返回taskId
*/
-(NSString*)gestureDetectWithOption:(GestureDetectOpt*)opt targetUserId:(int)targetUserId callback:(AIResultCallBack)callback;

/**手势检测关闭
 @param  taskId 手势检测开启返回的taskId
 */
- (void)stopGestureDetect:(NSString *)taskId;

/**OCR 能力
 @param  image 需要识别的图片,建议图片不大于5M
 @param  ocrType  需要识别的图片的类型
 @param  callback 结果回调
 @return taskId
 */
- (NSString*)doOcrImage:(UIImage*)image WithType:(BRAC_AI_OCRTYPE)ocrType WithCallback:(AIResultCallBack)callback;


@end

@interface AIInterface : NSObject

@end

NS_ASSUME_NONNULL_END
