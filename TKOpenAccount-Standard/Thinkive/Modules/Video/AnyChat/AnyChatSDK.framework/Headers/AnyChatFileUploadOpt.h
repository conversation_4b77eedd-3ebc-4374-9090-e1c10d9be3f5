//
//  AnyChatFileTaskOpt.h
//  AnyChatSDK
//
//  Created by b<PERSON><PERSON> on 2019/7/15.
//  Copyright © 2019 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface AnyChatFileUploadOpt : NSObject

/**
 文件本地路径
 */
@property (nonatomic, copy) NSString *filePath;

/**
 返回文件状态时间间隔，单位为秒
 */
@property (nonatomic, assign) int intervalTime;

/**
 指定文件上传后的目标文件名。
 */
@property (nonatomic, copy) NSString *filename;

/**
 文件上传分类子目录，通过设置该字段的值可以将文件上传到不同的分类子目录中
 */
@property (nonatomic, copy) NSString *category;

/**
 自定义参数:json字符串
 */
@property (nonatomic,copy)NSString *strJson;

/**
 * 解密秘钥
 */
@property (nonatomic, copy) NSString *encryptionKey;


/**
* 是否覆盖上传(默认相同文件不覆盖上传)
*/
@property (nonatomic, assign) BOOL isOverlayUpload;

/**
 * 传输最大码率（参数为int型，0 不限制，以最快速率传输[默认]， 否则表示限制码率，单位为：bps）
 */
@property (nonatomic, assign) int maxBitrate;

@end

NS_ASSUME_NONNULL_END
