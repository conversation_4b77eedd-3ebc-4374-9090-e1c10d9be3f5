//
//  AnyChatMediaDataOpt.h
//  AnyChatSDK
//
//  Created by zhong chen on 2019/4/28.
//  Copyright © 2019 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef struct tagAnyChatBitmapInfoHeader {
    uint32_t biSize;
    uint32_t biWidth;
    uint32_t biHeight;
    uint16_t biPlanes;
    uint16_t biBitCount;
    uint32_t biCompression;
    uint32_t biSizeImage;
    uint32_t biXPelsPerMeter;
    uint32_t biYPelsPerMeter;
    uint32_t biClrUsed;
    uint32_t biClrImportant;
}__attribute__((packed)) AnyChatBitmapInfoHeader;

typedef struct tagAnyChatWaveFormatEx {
    uint16_t wFormatTag;
    uint16_t nChannels;
    uint32_t nSamplesPerSec;
    uint32_t nAvgBytesPerSec;
    uint16_t nBlockAlign;
    uint16_t wBitsPerSample;
    uint16_t cbSize;
}__attribute__((packed)) AnyChatWaveFormatEx, *pAnyChatWaveFormatEx;

typedef NS_ENUM(NSUInteger, AnyChatMediaDataType) {
    AnyChatMediaDataTypeAudio, ///< 音频回调
    AnyChatMediaDataTypeVideo, ///< 视频回调
};

typedef NS_ENUM(NSUInteger, AnyChatMediaDataOptionKey) {
    AnyChatMediaDataOptionKeyAudio = 239, ///< 音频数据回调编码器类型
    AnyChatMediaDataOptionKeyVideo = 87, ///< 视频数据回调格式
};

typedef NS_ENUM(NSUInteger, AnyChatVideoPixelFormat) {
    AnyChatVideoPixelFormatRGB24 = 0,
    AnyChatVideoPixelFormatRGB32,
    AnyChatVideoPixelFormatYV12,
    AnyChatVideoPixelFormatYUY2,
    AnyChatVideoPixelFormatYUV420P,
    AnyChatVideoPixelFormatRGB565,
    AnyChatVideoPixelFormatRGB555,
    AnyChatVideoPixelFormatNV12,
    AnyChatVideoPixelFormatNV21,
    AnyChatVideoPixelFormatNV16,
    AnyChatVideoPixelFormatBGR32,
    
    AnyChatVideoPixelFormatMJPEG = 200,
    AnyChatVideoPixelFormatH264,
};

NS_ASSUME_NONNULL_BEGIN

@protocol AnyChatMediaDataExDelegate <NSObject>

@optional
///< 视频数据回调
- (void)AnyChatVideoDataExCallBack:(int)userId bufData:(NSData *)bufData bitMapInfoHeader:(AnyChatBitmapInfoHeader)bitMapInfoHeader timeStamp:(int)timeStamp;

///< 音频数据回调
- (void)AnyChatAudioDataExCallBack:(int)userId bufData:(NSData *)bufData waveFormateEx:(AnyChatWaveFormatEx)waveFormatEx timeStamp:(int)timeStamp;


@end

@interface AnyChatMediaDataOpt : NSObject


@property (nonatomic, weak) id<AnyChatMediaDataExDelegate> mediaDataExDelegate;
@property (nonatomic, assign) AnyChatMediaDataOptionKey optionKey; ///< 内核参数对应的键
@property (nonatomic, assign) AnyChatMediaDataOptionKey optionKey2; ///< 内核参数对应的键
@property (nonatomic, assign) int optionValue; ///< 内核参数对应的值
@property (nonatomic, assign) int optionValue2; ///< 内核参数对应的值

@end

NS_ASSUME_NONNULL_END
