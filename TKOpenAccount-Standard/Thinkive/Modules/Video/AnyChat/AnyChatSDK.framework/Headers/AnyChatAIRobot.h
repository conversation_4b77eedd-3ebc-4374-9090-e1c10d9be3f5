//
//  AnyChatAIRobot.h
//  AnyChatSDK
//
//  Created by 佰锐 on 2019/4/19.
//  Copyright © 2019 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "AnyChatDefineT.h"
#import "AIInterface.h"

@interface AfrTagOpt : NSObject
//tag新增
@property (nonatomic, assign) AI_TAG_TYPE tagMode; // 标签业务类型
@property (nonatomic, assign) int exceptFaceNum;// 预期监测人脸数
@property (nonatomic,strong) NSString * tagtitle;//标签标题
@property (nonatomic, strong) NSArray *pictureScoreArray;//人脸比对通过分数

///左边缩放范围 默认0.12
@property(nonatomic, assign) CGFloat xLeft;
///右边缩放范围 默认0.12
@property(nonatomic, assign) CGFloat xRight;
///上边边缩放范围 默认0.22
@property(nonatomic, assign) CGFloat yTop;
///下边缩放范围 默认0.06
@property(nonatomic, assign) CGFloat yBottom;

@end
@interface AsrTagOpt :NSObject
//tag新增
@property (nonatomic, assign) AI_TAG_TYPE tagMode; // 标签业务类型

@property (nonatomic, strong) NSString *questr;//标签问题
@property (nonatomic, strong) NSArray *expectAnswerArray;//期望答案
@property (nonatomic, strong) NSString *readContentStr;//用户朗读内容

@property (nonatomic,strong) NSString * tagtitle;//标签标题
@property (nonatomic,strong) NSString * userData;//扩展字段
@end

@interface MediaTagOpt :NSObject

//tag新增
@property (nonatomic, assign) AI_TAG_TYPE tagMode; // 标签业务类型
@property (nonatomic, strong) NSString *questr;//标签问题
@property (nonatomic, strong) NSString *contentStr;//标签问题
@property (nonatomic, strong) NSArray *expectAnswerArray;//期望答案
@property (nonatomic,strong) NSString * tagtitle;//标签标题
@property (nonatomic,strong) NSString * userData;//扩展字段
@end

#pragma mark - 配置类
@interface AIAfrOpt: NSObject

@property (nonatomic, assign) BRAC_AI_FIRM firm;//afr能力厂商
@property (nonatomic, strong) NSString * aiaddrurl;//人脸比对服务器地址，若不传则采用集群后台配置地址
@property (nonatomic, assign) BRAC_AI_AFRMODE mode; // 人脸检测模式
@property (nonatomic, strong) UIImage *image1;  //image1 需要识别的图片1，如果是视频检测则传nil
@property (nonatomic, strong) UIImage *image2;  //image2 需要识别的图片2，如果是视频检测则传nil
@property (nonatomic, strong) UIImage *image3;  //image3 需要识别的图片3，如果是两人图片人脸比较需要传该参数
@property (nonatomic, assign) CGFloat xscore;    //人脸姿势检测左右角度阈值
@property (nonatomic, assign) CGFloat zscore;    //人脸姿势检测倾斜角度阈值
@property (nonatomic, assign) NSTimeInterval timeInterval;//图片抓拍间隔时间，单位ms，服务器默认1000ms间隔
///超时时间，单位ms，服务器默认10000ms，AI能力服务器连续报错时间，超过该时间则关闭能力。
@property (nonatomic, assign) NSTimeInterval timeout;
///是否开启无限人脸比对
@property (nonatomic, assign) BOOL  captureUnlimit;

/**
 @deprecated 使用startFaceDetectWithOption会使用image1属性，不会使用 img1Str
*/
@property (nonatomic, copy) NSString *img1Str __attribute__((deprecated("")));
/**
 @deprecated 使用startFaceDetectWithOption会使用image2属性，不会使用 img2Str
*/
@property (nonatomic, copy) NSString *img2Str __attribute__((deprecated("")));
/**
 @deprecated 使用startFaceDetectWithOption会使用image3属性，不会使用 img3Str
*/
@property (nonatomic, copy) NSString *img3Str __attribute__((deprecated("")));

@property(nonatomic,strong) NSDictionary *extraParam;

@property(nonatomic,strong) AfrTagOpt *tagOpt;

@end

@interface AIOcrOpt : NSObject

@property (nonatomic, assign) BRAC_AI_OCRTYPE ocrtype; // OCR文档类型
@property (nonatomic, assign) BRAC_AI_FIRM firm; // OCR能力厂商
@property (nonatomic, strong) UIImage *image; // 识别的图片
@property(nonatomic,strong) NSDictionary *extraParam;//拓展参数
///超时时间，单位ms，服务器默认10000ms，AI能力服务器连续报错时间，超过该时间则关闭能力。
@property (nonatomic, assign) NSTimeInterval timeout;
@end

@interface AIAsrOpt: NSObject

@property (nonatomic, assign)BRAC_AI_ASRMODE mode; //asr模式
@property (nonatomic, assign)BRAC_AI_FIRM firm;//asr能力厂商
@property (nonatomic, assign)BRAC_AI_ASRSVC svctype;//服务器类型，默认ANYCHAT_ASRSVC_IAT
@property (nonatomic, copy)NSString *appid;//私有云系统分配的应用ID
@property (nonatomic, copy)NSString *token;//授权码，由私有云后台系统分配
@property (nonatomic, copy)NSString *aiaddrurl;//私有云地址url（ip:prot）
@property (nonatomic, copy)NSString *ability; //能力字串，私有云随机生成
///超时时间，单位为ms，默认为10000ms。
@property (nonatomic, assign)NSTimeInterval timeout;
@property (nonatomic, assign)BRAC_AI_ASRTYPE type; // BRAC_AI_ASRMODE_LIVESTREAM模式下需要填写
@property (nonatomic, assign)NSTimeInterval silencetime;  // BRAC_AI_ASRMODE_LIVESTREAM模式下需要填写，断句静默时长（单位：ms）,服务器默认800ms
@property (nonatomic, assign)NSTimeInterval vadchecktime;  //静默检测时长，每超过间隔时长通知前端（单位：ms）,服务器默认5000ms BRAC_AI_ASRMODE_LIVESTREAM模式下需要填写
@property (nonatomic, assign)BOOL isFlags;         // BRAC_AI_ASRMODE_LIVESTREAM模式下需要填写
@property (nonatomic, assign)BOOL isSymbol;        // BRAC_AI_ASRMODE_LIVESTREAM模式下需要填写
@property (nonatomic, copy) NSString *content;     // ANYCHAT_ASRMODE_KEYWORDRECOGNITION模式下需要填写，其他模式不需要填写
@property(nonatomic,strong) AsrTagOpt *tagOpt;
@property(nonatomic,strong) NSDictionary *extraParam;//拓展参数

@end

@interface AITtsOpt : NSObject

@property (nonatomic, assign) BRAC_AI_TTSTYPE type; //tts声音类型
@property (nonatomic, assign) BRAC_AI_TTSMODE mode; //tts模式
@property (nonatomic, assign) BRAC_AI_FIRM firm; //tts能力厂商
@property (nonatomic, assign) BRAC_AI_TTSAUDIO_FORMAT audioFormat;//返回语音文件类型
@property (nonatomic, assign) BOOL isLongText;   //是否是长文本，默认为NO
@property (nonatomic, assign) int volume;        //在线播放音量(0-20)，仅适用于BRAC_AI_FIRM_IFLYTEK
@property (nonatomic, assign) int speechRate;   //语速，范围（-500-500，默认0）
///超时时间，单位为ms，默认为10000ms。
@property (nonatomic, assign)NSTimeInterval timeout;
@property (nonatomic, assign)NSInteger svctype;//服务器类型，默认ANYCHAT_TTSSVC_TTS
@property (nonatomic, copy)NSString *appid;//私有云系统分配的应用ID
@property (nonatomic, copy)NSString *token;//授权码，由私有云后台系统分配
@property (nonatomic, copy)NSString *aiaddrurl;//私有云地址url（ip:prot）
@property (nonatomic, copy)NSString *ability; //能力字串，私有云随机生成
@property (nonatomic, copy) NSString *content; //文本内容
@property (nonatomic, copy) NSString *voiceStr; //文本内容
@property(nonatomic,strong) MediaTagOpt *tagOpt;
@property(nonatomic,strong) NSDictionary *extraParam;//拓展参数

@end


@interface AIAicOpt: NSObject

@property (nonatomic, assign)BRAC_AI_AICMODE mode;//aic模式

@property (nonatomic, assign) float comparescore; //BRAC_AI_AICMODE_FACECOMPARE模式可配置，相识度0-100，不传默认85.0
@property (nonatomic, strong) UIImage *image;    //BRAC_AI_AICMODE_FACECOMPARE模式必需配置，比对人像的图片
///超时时间，单位为ms，默认为10000ms。
@property (nonatomic, assign)NSTimeInterval timeout;
@property(nonatomic,strong) NSDictionary *extraParam;//拓展参数

@end

@interface AIApmOpt: NSObject

@property (nonatomic, assign) BRAC_AI_APMMODE mode;//APM工作模式
@property (nonatomic, assign) BRAC_AI_STREAMPLAYMODE ctrlMode;//APM播放控制模式
@property (nonatomic, assign) int filetype;//1->base64数据（暂不支持），2->URL，3->绝对路径
@property (nonatomic, assign) int targetUserId;//目标userId
@property (nonatomic, copy) NSString *filepath;//文件路径，绝对路径（机器人所在的服务器）
@property (nonatomic, copy) NSString *taskid;//任务ID
@property (nonatomic, assign) int streamindex;//流编号
///< 播放到进度时间 （单位：毫秒， 整秒的数值）
@property (nonatomic, assign) int playtime;
///< 播放速度（整数， 0 ：最快速度播放； 正负1：正常速度播放； 2：2倍速度播放；-2：1/2速度播放。）
@property (nonatomic, assign) int playspeed;


@end

@interface AIVHOpt : NSObject
@property (nonatomic, assign) BRAC_AI_VHMODE mode; //模式
@property (nonatomic, copy) NSString *taskid;//任务ID
@property (nonatomic, copy) NSString *content; //文本内容
@property(nonatomic,strong) NSDictionary *extraParam;//拓展参数
//@property (nonatomic, assign) BRAC_AI_TTSMODE ttsMode; //模式(deprecated)


@property (nonatomic, assign) int subtitles; //字幕：1->开启, 0->关闭(暂不支持)
@property (nonatomic, assign) int interact_type; //互动类型：1 - 问答，2 - 播报，目前只支持播报
@property (nonatomic, copy) NSString *protocol; //AI主播视频流类型，可填参数：rtmp, rtsp，目前只支持rtmp

@property (nonatomic, assign) int videowidth;    //    视频宽(暂不支持)
@property (nonatomic, assign) int videoheight;    //    视频高(暂不支持)
@property (nonatomic, assign) int videofps;    //    视频帧率(暂不支持)
@property (nonatomic, assign) int videocodec;    //    视频编码器(暂不支持)
@property (nonatomic, assign) int audiosample;    //    音频采样率(暂不支持)
@property (nonatomic, assign) int audiobits;    //    音频采样位数(暂不支持)
@property (nonatomic, assign) int channels;    //    音频通道数(暂不支持)
@property (nonatomic, assign) int audiocodec;    //    音频编码器(暂不支持)
@property (nonatomic, assign) int userid;    //    用户ID，默认操作用户ID

@end

@interface AIBRVHOpt : NSObject

@property (nonatomic, assign) BRAC_AI_TTSMODE mode; //模式
@property (nonatomic, copy) NSString *content; //虚拟形象参数；内容填空(“”)时 采用默认参数
@property(nonatomic,strong) NSDictionary *extraParam;//拓展参数

@end


@interface AIBRLiveTTSOptParam : NSObject

/// 声音类型，设置该值后 AIBRLiveTTSOpt ttstype 属性将不生效，以voice为准 默认"Siyue"
@property (nonatomic, copy) NSString *voice;
/// 合成用户姓名个数，如“张三”为2
@property (nonatomic, assign) NSInteger namenum;
@end

@interface AIBRLiveTTSOpt : NSObject
/// 需要转换为语音的文本信息。如：”我自愿开户！”
@property (nonatomic, copy) NSString *content;
/// AI能力：1->开始，0->停止 默认开始
@property (nonatomic, assign) BOOL start;
/// 语速，范围（-500-500，服务器默认0）
@property (nonatomic, assign) NSInteger speechrate;
/// TTS语音类型（男、女等）1->男声，2->女声，默认2
@property (nonatomic, assign) NSInteger ttstype;
/// 2 -> 视频，1 -> 音频 默认2
@property (nonatomic, assign) NSInteger streamtype;

/// 扩展参数 默认已初始化
@property (nonatomic, strong) AIBRLiveTTSOptParam *param;

/// 音量 默认50
@property (nonatomic, assign) NSInteger volume;
@end

@interface GestureDetectOpt : NSObject
@property (nonatomic, assign) BOOL startGesture;
@end

//BAIRUITECH 动作活体检测参数
@interface AILiveDetectionParamOpt: NSObject
@property (nonatomic, assign) BRAC_AI_LDACTION_TYPE actiontype; //动作类型。1.眨眨眼2.张张嘴3.向左摇头4.向右摇头5.点点头6.摇头
@property (nonatomic, assign) int passlevel;//通过等级，1~10，越高越严格
@property (nonatomic, assign) int circlex; //人脸框圆心坐标x（宽）
@property (nonatomic, assign) int circley; //人脸框圆心坐标y（高）
@property (nonatomic, assign) int circler; //人脸框圆心半径r

- (NSString *)toJsonString;

- (NSDictionary *)toDictionary;

@end

@interface AILiveDetectionOpt: NSObject

@property (nonatomic, assign) BRAC_AI_LDMODE mode;//工作模式
@property (nonatomic, assign) BRAC_AI_FIRM firm; //能力厂商
@property (nonatomic, assign) NSTimeInterval timeInterval;//图片抓拍间隔时间，单位ms，服务器默认1000ms间隔
@property (nonatomic, assign) NSTimeInterval timeout; //超时时间，单位ms，服务器默认10000ms，AI能力服务器连续报错时间，超过该时间则关闭能力。
@property (nonatomic, assign) int targetUserId;//目标userId
@property (nonatomic, strong) id param;//动作类型相关参数
@property(nonatomic,strong) NSDictionary *extraParam;//拓展参数

@end

#pragma mark - 机器人类
@protocol AnyChatAIDelegate <NSObject>
@optional
- (void)onRobotCreate:(NSDictionary*)dic;
- (void)onRobotStatusChange:(NSDictionary*)dic;//机器人状态改变时自动通知上层；若存在错误码时（不为0的情况），机器人失效
@end


@interface AnyChatAIRobot : NSObject <AIAbilityInterface>

@property (nonatomic, weak) id<AnyChatAIDelegate>   delegate;               //机器人创建、销毁的事件代理对象
@property (nonatomic, assign, readonly) int         robotUserId ;            //机器人用户ID
@property (nonatomic, copy, readonly) NSString*     robotId;                //机器人ID
@property (nonatomic, assign, readonly) BOOL        isAlive;                //机器人当前是否还存活



-(BOOL)robotIsAlive:(AIResultCallBack)callback;
- (NSString*)taskid;
- (NSString*)sendOrder:(NSMutableDictionary*)paramsDict;
- (BOOL)isSendMessageSuccess:(NSString*)resultCode callBack:(AIResultCallBack)callback;
- (void)addTaskToList:(NSString*)taskId CallBack:(AIResultCallBack)callback;
-(void)aiResultProc:(NSDictionary*)dic;
- (void)resetState;
-(BOOL)imageIsBelowLimit:(NSString*)imageStr callBack:(AIResultCallBack)callback;
#pragma mark - AI旧接口
/**OCR 能力
 @param  image 需要识别的图片,建议图片不大于5M
 @param  ocrType  需要识别的图片的类型
 @param  callback 结果回调
 @return taskId
 */
- (NSString*)doOcrImage:(UIImage*)image WithType:(BRAC_AI_OCRTYPE)ocrType WithCallback:(AIResultCallBack)callback;

/**语音检测开启
 @param  targetUserId 对房间内指定的人声进行识别，如果不传或0值是对发起操作者的人声进行识别。
 @param  callback 结果回调
 @return taskId
 */
- (NSString*)startASRWithTargetUserId:(int)targetUserId WithCallback:(AIResultCallBack)callback;

/**语音检测关闭
 @param  taskId 语音检测开启返回的taskId
 */
- (void)stopASR:(NSString *)taskId;

/**人脸检测开启
 @param  opt     参数
 @param  targetUserId 对房间内指定的用户的视频流进行人脸识别，不传或为0值是对发起操作者的视频流进行人脸识别。
 @param  afrMode 模式
 @param  callback 结果回调
 @return taskId
 */
-(NSString*)startFaceDetectImg:(AIAfrOpt*)opt WithTargetUserId:(int)targetUserId WithMode:(BRAC_AI_AFRMODE)afrMode WithCallback:(AIResultCallBack)callback;

/**视频流人脸检测关闭
 @param  taskId 人脸检测开启返回的taskId
 */
-(void)stopFaceDetect:(NSString *)taskId;


/**语音合成
 @param  contentStr 文字,不能超过300个字符
 @param  ttsType 类型
 @param  ttsMode 模式
 @param  callback 结果回调
 @return taskId
 */
- (NSString*)doTTS:(NSString*)contentStr WithType:(BRAC_AI_TTSTYPE)ttsType WithMode:(BRAC_AI_TTSMODE)ttsMode WithCallback:(AIResultCallBack)callback;


/**人脸捕获开始
 @param  targetUserId 对房间内指定的用户的视频流进行人脸捕获，不传或为0值是对发起操作者的视频流进行人脸捕获。
 @param  callback 结果回调
 @return 返回taskId
 */
-(NSString*)startFaceCaptureWithTargetUserId:(int)targetUserId WithCallback:(AIResultCallBack)callback;




/**人脸捕获停止
 */
-(void)stopFaceCapture:(NSString *)taskId;

#pragma mark - AI新接口
/**文字识别 （新接口）
 @param  opt OCR配置参数
 @param  callback 结果回调
 @return taskId
 */
- (NSString *)startOCRWithOption:(AIOcrOpt*)opt callback:(AIResultCallBack)callback;

/**语音识别 （活体读数检测）
 @param  opt ASR配置参数
 @param  targetUserId 对房间内指定的人声进行识别，如果不传或0值是对发起操作者的人声进行识别。
 @param  callback 结果回调
 @return taskId
 */
- (NSString *)startLiveCheckASRWithOption:(AIAsrOpt*)opt targetUserId:(int)targetUserId callback:(AIResultCallBack)callback;

//语音检测关闭
- (void)stopLiveCheckASR:(NSString *)taskId;

/**语音识别 （新接口）
 @param  opt ASR配置参数
 @param  targetUserId 对房间内指定的人声进行识别，如果不传或0值是对发起操作者的人声进行识别。
 @param  callback 结果回调
 @return taskId
 */
- (NSString *)startASRWithOption:(AIAsrOpt*)opt targetUserId:(int)targetUserId callback:(AIResultCallBack)callback;

/**语音合成/在线播放 （新接口）
 @param  opt TTS配置参数
 @param  callback 结果回调
 @return taskId
 */
- (NSString *)startTTSWithOption:(AITtsOpt*)opt callback:(AIResultCallBack)callback;

-(void)resumeTTSWithTaskId:(NSString*)taskId;
-(void)pauseTTSWithTaskId:(NSString*)taskId;

#if ZhaoShangTTS
/**文本转语音 （ZhaoShangTTS接口）
 @param  opt TTS配置参数
 @param  callback 结果回调
 @return taskId
 */
- (NSString *)startTTSByCmsWithOption:(AITtsOpt*)opt callback:(AIResultCallBack)callback;
#endif


#if HUAXIN_FACECOMPARE

/**人脸捕获开始 （新接口）
 @param  opt     参数
 @param  targetUserId 对房间内指定的用户的视频流进行人脸捕获，不传或为0值是对发起操作者的视频流进行人脸捕获。
 @param  callback 结果回调
 @return 返回taskId
 */
- (NSString *)startFaceByCmsDetectWithaiAddrurl:(NSString *)opt targetUserId:(int)targetUserId params:(NSString*)params  flags:(int)flag callback:(AIResultCallBack)callback;

#endif

/**人脸捕获开始 （新接口）
 @param  opt     参数
 @param  targetUserId 对房间内指定的用户的视频流进行人脸捕获，不传或为0值是对发起操作者的视频流进行人脸捕获。
 @param  callback 结果回调
 @return 返回taskId
 */
- (NSString *)startFaceCaptureWithOption:(AIAicOpt*)opt targetUserId:(int)targetUserId callback:(AIResultCallBack)callback;


/**
 人脸识别开始  （新接口）

 @param opt 参数
 @param targetUserId 对房间内指定的用户的视频流进行人脸检测，不传或者为0值是对发起操作者的视频流进行人脸检测
 @param callback 结果回调
 @return 返回taskId
 */
- (NSString *)startFaceDetectWithOption:(AIAfrOpt *)opt targetUserId:(int)targetUserId callback:(AIResultCallBack)callback;

/**
开始播放

@param opt 参数
@param callback 结果回调
@return 返回taskId
*/
- (NSString *)aiPlayMediaWithOption:(AIApmOpt *)opt callback:(AIResultCallBack)callback;

/** 继续播放 */
- (void)aiStartMedia:(AIApmOpt *)opt;
/** 暂停播放 */
- (void)aiPauseMedia:(AIApmOpt *)opt;
/** 结束播放 */
- (void)aiStopMedia:(AIApmOpt *)opt;
/** 拖动播放 */
- (void)aiSeekMedia:(AIApmOpt *)opt;
/** 倍速播放 */
- (void)aiSpeedMedia:(AIApmOpt *)opt;

/// *******.3数字人-tts合成在线播放
- (NSString*)startLiveTTS:(AIBRLiveTTSOpt*)opt callback:(AIResultCallBack)callback;

/**开始自定义虚拟客服
@param  opt 配置参数
@param  callback 结果回调
@return taskId
*/
-(NSString*)startCustomerAgentWithOption:(AIBRVHOpt*)opt callback:(AIResultCallBack)callback;

/**关闭义虚拟客服
@param taskId 任务ID
*/
-(void)stopCustomerAgentWithTaskId:(NSString*)taskId;

/**
开始VH流

@param opt 参数
@param callback 结果回调
@return 返回taskId
*/
- (NSString *)startVirtualAgentWithOption:(AIVHOpt *)opt callback:(AIResultCallBack)callback;
/** 结束播放 */
- (void) stopVirtualAgentWithOption:(AIVHOpt *)opt;

/**播放文本
 @param  opt VH配置参数
 @return taskId
 */
- (NSString *)sendVirtualAgentReadContent:(AIVHOpt*)opt;


/**开始动作配合式活体检测
 @param  opt 配置参数
 @param  callback 结果回调
 @return taskId
 */
- (NSString *)startLiveDetectionWithOption:(AILiveDetectionOpt*)opt callback:(AIResultCallBack)callback;


/**活体检测关闭
 @param  taskId 活体检测开启返回的taskId
 */
- (void)stopLiveDetection:(NSString *)taskId;

@end
