//
//  AnyChatFocusingOpt.h
//  AnyChatSDK
//
//  Created by bairuitech on 2020/6/29.
//  Copyright © 2020 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface AnyChatFocusingOpt : NSObject

/**
 对焦点X轴坐标
 */
@property (nonatomic, assign) CGFloat focusPointX;

/**
 对焦点Y轴坐标
 */
@property (nonatomic, assign) CGFloat focusPointY;

/**
 对焦区域宽度
 */
@property (nonatomic, assign) CGFloat focusAreaWidth;

/**
 对焦区域高度
 */
@property (nonatomic, assign) CGFloat focusAreaHeight;

@end

NS_ASSUME_NONNULL_END
