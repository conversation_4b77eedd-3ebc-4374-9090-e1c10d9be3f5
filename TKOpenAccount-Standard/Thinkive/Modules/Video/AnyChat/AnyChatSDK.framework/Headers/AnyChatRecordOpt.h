//
//  BRAC_RecordOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatRecordStreamOpt.h"
#import "AnyChatPicMarkOpt.h"
#import "AnyChatTextMarkOpt.h"
#import "AnyChatResult.h"

typedef void (^RecordStatusnotify)(NSDictionary * _Nullable data);
/**
 开始录制回调
 */
typedef void (^RecordStartCallback)(AnyChatResult * _Nullable result,NSDictionary * _Nullable data);

typedef NS_ENUM(NSInteger, BRAC_RecordLayoutType){
    
    /**
     *  单画面(默认)
     */
    BRAC_RECORD_LAYOUT_SINGLE_VIEW               = 1,
    /**
     *  双画面并列
     */
    BRAC_RECORD_LAYOUT_DOUBLE_VIEW               = 2,
    /**
     *  双画面画中画
     */
    BRAC_RECORD_LAYOUT_DOUBLE_PIP_VIEW           = 3,
    /**
     *  三画面，左侧一个大画面，右侧由上下两个小画面
     */
    BRAC_RECORD_LAYOUT_THREE_VIEW                = 4,
    /**
     *  三画面，左侧一个大画面，右侧一个画中画画面
     */
    BRAC_RECORD_LAYOUT_THREE_VIEW_AND_PIP_VIEW   = 5,
    /**
     *  四画面，按田字分布
     */
    BRAC_RECORD_LAYOUT_FOUR_VIEW                 = 6,
    /**
     *  九宫格
     */
    BRAC_RECORD_LAYOUT_NINE_VIEW                 = 7,
    /**
     *  十六宫格
     */
    BRAC_RECORD_LAYOUT_SIXTEEN_VIEW              = 8,

};

typedef NS_ENUM(NSInteger, BRAC_RecordFileType){
    
    /**
     *  MP4
     */
    BRAC_RECORD_FILE_TYPE_MP4   = 1,
    /**
     *  WMV
     */
    BRAC_RECORD_FILE_TYPE_WMV   = 2,
    /**
     *  FLV
     */
    BRAC_RECORD_FILE_TYPE_FLV   = 3,
    
    /**
     *  MP3
     */
    BRAC_RECORD_FILE_TYPE_MP3   = 4,

};

typedef NS_ENUM(NSInteger, BRAC_RecordMode){
    
    /**
     *  本地录制(默认)
     */
    BRAC_RECORD_LOCAL_MODE          = 1,
    /**
     *  服务器端录制
     */
    BRAC_RECORD_SERVER_MODE         = 2,
    /**
     *  服务器端合成流录制
     */
    BRAC_RECORD_STREAM_MODE         = 3,
    /**
     *  本地合成流录制
     */
    BRAC_RECORD_LOCAL_STREAM_MODE   = 4,
    
};

typedef NS_ENUM(NSInteger, BRAC_RecordContent){
    /**
     *  既录音又录像(默认)
     */
    BRAC_RECORD_DEFAULT_CONTENT   = 1,
    /**
     *  只录音
     */
    BRAC_RECORD_AUDIO             = 2,
    /**
     *  只录像
     */
    BRAC_RECORD_VIDEO             = 3,
    
};

typedef NS_ENUM(NSInteger, BRAC_RecordClipMode){
    /**
     *  未知模式，不需要做裁剪时使用
     */
    BRAC_RECORD_CLIPMODE_UNKNOW                  = 1,
    /**
     *  默认模式，以最大比例进行裁剪，然后再整体拉伸，画面保持比例，但被裁剪画面较大
     */
    BRAC_RECORD_CLIPMODE_AUTO	                 = 2,
    /**
     *  重叠模式，只取最大有效部分，对边缘进行裁剪
     */
    BRAC_RECORD_CLIPMODE_OVERLAP                 = 3,
    /**
     *  缩小模式，缩小到合适的比例，不进行裁剪
     */
    BRAC_RECORD_CLIPMODE_SHRINK                  = 4,
    /**
     *  平铺模式，不进行裁剪，但可能导致画面不成比例
     */
    BRAC_RECORD_CLIPMODE_STRETCH	             = 5,
    /**
     *  动态模式，由上层应用根据分辩率来调整显示表面，保持画面不变形
     */
    BRAC_RECORD_CLIPMODE_DYNAMIC                 = 6,
    
};

@interface AnyChatRecordLayoutOpt : NSObject

@property (nonatomic, assign) int recordlayout; //视频布局，视频流数量，即多少个视频画面
@property (nonatomic, assign) int layoutstyle; //三路流和四路流的视频画面布局风格：0-并列风格（默认） ，1-画中画风格，2-三画面并列风格。
@property (nonatomic, copy) NSArray * _Nullable streamlist; //AnyChatRecordStreamOpt集合

- (NSDictionary *_Nullable)getDic;
@end

@interface AnyChatRecordOpt : NSObject

/**
 用户ID (必须传入)
 */
@property (nonatomic, assign) int userId;

/**
 视频布局对象
*/
@property (nonatomic, strong) AnyChatRecordLayoutOpt * _Nullable recordLayoutOpt;
/**
  @deprecated 录制画面结构布局,请使用recordLayoutOpt对象
 */
@property (nonatomic, assign) BRAC_RecordLayoutType layout __attribute__((deprecated("")));

/**
 @deprecated 录制画面各个区域对应的视频流,请使用recordLayoutOpt对象
 */
@property (nonatomic, copy) NSArray * _Nullable layoutStreams __attribute__((deprecated("")));

/**
 录制画面宽度
 */
@property (nonatomic, assign) CGFloat width;

/**
 录制画面高度
 */
@property (nonatomic, assign) CGFloat height;

/**
 录制视频码率
 */
@property (nonatomic, assign) int videobitrate;

/**
 录制音频码率
 */
@property (nonatomic, assign) int audiobitrate;

/**
 录像帧率
 */
@property (nonatomic, assign) int fps;

/**
 录制音频通道： 1 单通道， 2双通道
 */
@property (nonatomic, assign) int channels;

/**
 录制音频采样率：16000，48000等
 */
@property (nonatomic, assign) int samplepersec;

/**
 录制模式
 
 屏幕共享必须设置mode为BRAC_RECORD_LOCAL_STREAM_MODE或BRAC_RECORD_STREAM_MODE
 如果服务器录制包含文字水印、图片水印功能，必须设置mode为BRAC_RECORD_STREAM_MODE
 
 */
@property (nonatomic, assign) BRAC_RecordMode mode;

/**
 录制内容
 */
@property (nonatomic, assign) BRAC_RecordContent content;

/**
 录制文件类型
 */
@property (nonatomic, assign) BRAC_RecordFileType fileType;

/**
 录制文件名
 */
@property (nonatomic, copy) NSString * _Nullable fileName;

/**
 合成流录制目录参数
 */
@property (nonatomic, copy) NSString *category;

/**
 本地录制文件存放目录
 */
@property (nonatomic, copy) NSString *localFilePath;

/**
 裁剪模式
 */
@property (nonatomic, assign) BRAC_RecordClipMode recordClipMode;

/**
 在录像文件中添加图片水印
 */
@property (nonatomic, strong) AnyChatPicMarkOpt *picOpt;

/**
 在录像文件中添加文字水印
 */
@property (nonatomic, strong) AnyChatTextMarkOpt *textOpt;

/**
 加密秘钥
 */
@property (nonatomic, copy) NSString *encryptionKey;

/**
 设置每隔多少秒触发一次异步回调通知核心事件，通知上层录像状态。
 对服务器录制以及服务器合成流录制有效
 */
@property (nonatomic, assign) int statusnotify;

/**
 录制心跳回调 需要先设置statusnotify,对服务器录制以及服务器合成流录制有效
 
 RecordStatusnotifyBlock 录制心跳回调
     data[@"userid"] 用户id
     data[@"errorcode"] 错误码
 */
@property (nonatomic,copy) RecordStatusnotify RecordStatusnotifyBlock;

/**
 录像开始回调

 recordStartCallback 录像开始回调
     data[@"userid"] 用户id
     data[@"status"] 录像状态，值为"prepare" "start"
     data[@"statuscode"] 录像状态，1为prepare 2为start
 */
 @property (nonatomic, copy) RecordStartCallback recordStartCallback;
/**
 获取录像设置参数信息

 @return 录像参数，如果没有设置的话则为空
 */
- (nullable NSDictionary *)getRecordParameters;

@property (nonatomic,assign)int recordflags;

/**
自定义录制扩展参数
 */
@property (nonatomic, copy) NSString *strJson;

//tag  新增
@property (nonatomic,strong)NSString * tradeNoStr;//流水号;
@property (nonatomic,assign,getter=isTagFlags) BOOL tagFlages;//是否开启标签功能


@end
