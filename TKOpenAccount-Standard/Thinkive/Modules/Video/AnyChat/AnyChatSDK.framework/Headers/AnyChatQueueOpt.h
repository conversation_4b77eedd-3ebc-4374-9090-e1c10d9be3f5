//
//  BRAC_QueueOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatResult.h"


/**
 角色
 */
typedef NS_ENUM(NSInteger, BRAC_QueueRole){
    /**
     *  客户
     */
    BRAC_QUEUE_OPT_ROLE_CLIENT  = 1,
    
    /**
     *  坐席
     */
    BRAC_QUEUE_OPT_ROLE_AGENT   = 2,
    
};


@protocol QueueDelegate <NSObject>
    
@optional


/**
 营业厅变化回调

 @param data agentcount 坐席总人数
             idleagentcount 空闲坐席人数
 */
- (void)onAreaChanged:(NSDictionary *)data;


/**
 收到开始服务回调

 @param data agentId 坐席ID 
             customerId 客户ID 
             queueId 队列ID
 */
- (void)onServiceNotify:(NSDictionary *)data;
    

/**
 队列变化回调

 @param data data userNumInQueue 当前队列总人数
                  currentPos 当前排队位置
                  waitingTime 排队时长
 */
- (void)onProcessChanged:(NSDictionary *)data;
    
/**
 坐席状态变化回调 
 
 @param data  status 坐席状态  (0 关闭，不对外提供服务,  1 等待中，可随时接受用户服务,  2 工作中，正在为用户服务,   3 暂停服务,  10 离线)
 */
- (void)onAgentStatusChanged:(NSDictionary *)data;
    
 /**
 坐席信息变化回调

 @param data serviceBeginTime 开始服务时间
             serviceUserCount 服务人数
             serviceTotalTime 服务总时长
 */
- (void)onAgentServiceInfoNotify:(NSDictionary *)data;
/**
 队列用户信息变化回调
 
 @param data queueId 队列id
 userList 用户信息列表
 
 */
- (void)OnAnyChatQueueUserInfoChanged:(NSDictionary *)data;
@end

@interface AnyChatQueueOpt : NSObject


@property (nonatomic, weak) id<QueueDelegate>queueDelegate;

/**
 角色
 */
@property (nonatomic, assign) BRAC_QueueRole role;

/**
 优先级
 */
@property (nonatomic, copy) NSString *priority;
    

/**
 业务属性，可以根据业务需求传入JSON对象
 */
@property (nonatomic, copy) NSString *attribute;
    
/**
 主动路由
 */
@property (nonatomic, assign) BOOL autoRoute;

@end
