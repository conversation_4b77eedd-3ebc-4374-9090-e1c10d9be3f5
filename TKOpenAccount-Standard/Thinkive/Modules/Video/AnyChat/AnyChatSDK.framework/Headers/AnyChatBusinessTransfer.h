//
//  AnyChatBusinessTransfer.h
//  AnyChatSDK
//
//  Created by Mac on 2019/2/22.
//  Copyright © 2019 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatResult.h"

#define ANYCHAT_BUSBUF_DATATYPE_APP 10000 ///< APP(上层业务)
/**
 业务数据传输回调
 
 @param data 业务数据
 */
typedef void (^BusinessTransferCallBack)(AnyChatResult *result, NSDictionary *data);

@interface AnyChatBusinessTransfer : NSObject

@property (nonatomic,assign) int timeout;//超时时间 单位:秒
/**
 *  单例方法
 *
 *  @return 实例对象
 */
+ (AnyChatBusinessTransfer*)getInstance;


/**
 同步传输业务数据

 @param json 数据
 @return 返回json格式数据
 */
- (NSString *) syncTransfer: (NSString*)json;


/**
 异步传输业务数据

 @param json 数据
 @param callback 回调
 @return 返回
 */
- (NSString *) asyncTransfer:(NSString*)json callback:(BusinessTransferCallBack)callback;

@end


