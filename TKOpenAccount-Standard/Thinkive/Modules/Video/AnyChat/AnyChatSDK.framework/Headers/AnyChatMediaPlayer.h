//
//  AnyChatMediaPlayer.h
//  BRAC_Project
//
//  Created by bairuitech on 2018/4/13.
//  Copyright © 2018年 GuangZhou BaiRui NetWork Technology Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "AnyChatMediaPlayerOpt.h"
@class AnyChatMediaPlayer;

#pragma mark - 播放资源相关回调协议 AnyChatMediaPlayerDelegate
@protocol AnyChatMediaPlayerDelegate <NSObject>

@optional

/**
 * 播放状态回调
 */
-(void)AnyChatMediaPlayer:(AnyChatMediaPlayer *)AnyChatPlayer obtainPlayStatusWithDict:(NSDictionary *)dict playStatus:(int)status;

@end

@interface AnyChatMediaPlayer : NSObject
@property (nonatomic, weak) id<AnyChatMediaPlayerDelegate>delegate;
/**
 *  初始化AnyChatMediaPlayer
 *  @param path 媒体文件路径
 *  @return 播放器对象
 */
- (AnyChatMediaPlayer *) initAnyChatMediaPlayerWithAudioPath:(NSString*)path;

/**
 *  初始化AnyChatMediaPlayer
 *  @param path 媒体文件路径
 *  @param video 播放视频视图容器
 *  @return 播放器对象
 */
- (AnyChatMediaPlayer*) initAnyChatMediaPlayerWithMediaPath:(NSString*)path videoView:(UIImageView *)video;


/**
 *  初始化AnyChatMediaPlayer
 *  @param mediaOpt  配置类  可选择加密方式
 *  @return 播放器对象
 */
- (AnyChatMediaPlayer*) initAnyChatMediaPlayerWithOpt:(AnyChatMediaPlayerOpt *)mediaOpt;
#pragma mark - 播放资源操作方法

/**
 @brief 播放资源
 @return 播放操作状态返回信息
 */
- (int) play;


/**
 @brief 暂停资源播放
 @return 暂停操作状态返回信息
 */
- (int) pause;


/**
 @brief 恢复资源播放
 @return 恢复操作状态返回信息
 */
- (int) resume;

/**
 @brief 停止资源播放
 @return 停止操作状态返回信息
 */
- (int) stop;

/**
 @brief 销毁
 @return 销毁操作状态返回信息
 */
- (int) destroy;

/**
 @brief 获取当前播放状态信息
 @return 当前播放状态信息
 */
- (NSDictionary*) getPlayStatus;

/**
 @brief 获取当前播放任务id
 @return 当前播放任务id
 */
- (NSString *)getTaskID;

/**
 @brief 控制PPT切换到下一帧
 @return 切换到下一帧状态返回信息
 */
- (int)nextFrame;

/**
 @brief 控制PPT切换到上一帧
 @return 切换到上一帧状态返回信息
 */
- (int)previousFrame;

/**
 @brief 跳转到指定时间
 @return 跳转到指定时间 状态码返回值
 */
- (int)seekTo:(int)s;

@end
