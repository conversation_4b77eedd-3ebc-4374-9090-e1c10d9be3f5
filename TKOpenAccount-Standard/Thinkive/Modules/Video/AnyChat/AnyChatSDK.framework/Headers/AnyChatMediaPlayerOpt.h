//
//  AnyChatMediaPlayerOpt.h
//  AnyChatSDK
//
//  Created by b<PERSON><PERSON> on 2019/7/15.
//  Copyright © 2019 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "AnyChatDefineT.h"
#import "AnyChatAIRobot.h"
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, BRAC_MediaType) {
    /**
     *  音频
     */
    BRAC_MediaType_Audio   = 1,
    /**
     *  视频
     */
    BRAC_MediaType_Video   = 2,
    /**
     *  PPT文件夹
     */
    BRAC_MediaType_PPT     = 3,
    
};

@interface AnyChatMediaPlayerOpt : NSObject

@property (nonatomic, copy)   NSString *path;
@property (nonatomic, strong) UIImageView  *videoView;
@property (nonatomic, assign) int streamIndex;
@property (nonatomic, assign) BRAC_MediaType mediaType; // 媒体资源类型
@property (nonatomic, strong) NSDictionary *mediaDetailsInfo; // 详细信息


@property (nonatomic, strong) MediaTagOpt *tagOpt; // 详细信息
/**
 解密秘钥
 */
@property (nonatomic, copy) NSString *encryptionKey;

/**
 是否支持音频输入(默认不支持)
*/
@property (nonatomic, assign) BOOL supportAudioInput;


@end

NS_ASSUME_NONNULL_END
