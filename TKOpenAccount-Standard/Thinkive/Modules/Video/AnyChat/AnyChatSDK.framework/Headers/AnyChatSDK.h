//
//  BRAC.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "AnyChatInitOpt.h"
#import "AnyChatRoomOpt.h"
#import "AnyChatFileOpt.h"
#import "AnyChatVideoCallOpt.h"
#import "AnyChatMicrophone.h"
#import "AnyChatCamera.h"
#import "AnyChatQueueOpt.h"
#import "AnyChatResult.h"
#import "AnyChatRecordOpt.h"
#import "AnyChatSnapshotOpt.h"
#import "AnyChatFileTask.h"
#import "AnyChatVideoOpt.h"
#import "AnyChatAudioOpt.h"
#import "AnyChatRecordStreamOpt.h"
#import "AnyChatTextMarkOpt.h"
#import "AnyChatPicMarkOpt.h"
#import "AnyChatPlayer.h"
#import "AnyChatDownload.h"
#import "AnyChatMediaPlayer.h"
#import "AnyChatMediaDataOpt.h"
#import "AnyChatBusinessTransfer.h"
#import "AnyChatSignatureView.h"
#import "AnyChatAIRobot.h"
#import "AnyChatFileUploadOpt.h"
#import "AnyChatRecordTag.h"
#import "AnyChatFocusingOpt.h"
#import "AnyChatAiConfig.h"
#import "AnyChatDetachableService.h"
#import "AnyChatWhiteBoardOpt.h"
#import "AnyChatMeetingOpt.h"

/**
 进入房间操作回调

 @param result 返回操作结果
 @param roomId 房间ID
 */
typedef void (^EnterRoomCallback)(AnyChatResult *result,NSString *roomId);

/**
 录制完成回调

 @param result 返回操作结果
 @param data filePath 录制文件存储路径 
             elapse 录像时长
 */
typedef void (^RecordDoneCallback)(AnyChatResult *result,NSDictionary *data);

/**
 拍照结果回调

 @param result 返回操作结果
 @param filePath 图片存储路径
 */
typedef void (^SnapshotCallback)(AnyChatResult *result,NSString *filePath);

/**
 透明通道回调
 
 @param result 返回操作结果
 @param data userId 接收人ID
 */
typedef void (^TransBufferBlock)(AnyChatResult *result, NSDictionary *data);

/**
 获取营业厅列表操作回调

 @param result 返回操作结果 
 @param data  areas 营业厅列表
 
 */
typedef void (^SyncAreasCallback)(AnyChatResult *result,NSDictionary *data);

/**
 进入营业厅操作回调

 @param result 返回操作结果
 @param data  queueCount 营业厅数目
              queues 队列列表
 */
typedef void (^EnterAreaCallback)(AnyChatResult *result,NSDictionary *data);

/**
 进入队列操作回调

 @param result 返回操作结果
 @param data userNumInQueue 排队的人数
             currentPos 用户当前的位置 
             waitingTime 排队等待时长
 */
typedef void (^EnterQueueCallback)(AnyChatResult *result,NSDictionary *data);


/**
 取消排队操作回调

 @param result 返回操作结果
 */
typedef void (^CancelQueuingCallback)(AnyChatResult *result);

/**
 离开营业厅操作回调

 @param result 返回操作结果
 */
typedef void (^LeaveAreaCallback)(AnyChatResult *result);

/**
 坐席服务操作回调

 @param result 返回操作结果
 @param data   null
 */
typedef void (^ServiceCtrlCallback)(AnyChatResult *result,NSDictionary *data);


typedef NS_ENUM(NSInteger, BRAC_AgentServiceCtrlCode){
    /**
     *  示闲
     */
    BRAC_AGENT_SERVICE_WAITTING        = 0,
    /**
     *  结束服务
     */
    BRAC_AGENT_SERVICE_FINISHSERVICE   = 1,
    /**
     *  示忙
     */
    BRAC_AGENT_SERVICE_PAUSED          = 2,
    /**
     * 转移下一个坐席服务
     */
    BRAC_AGENT_SERVICE_FINISHSERVICE_TIMEOUT = 3,
    
};


@interface AnyChatSDK : NSObject

/**
 定义白板设置
 */
@property (nonatomic, strong) AnyChatWhiteBoardOpt *whiteBoardOpt;

/**
 定义会议设置
 */
@property (nonatomic, strong) AnyChatMeetingOpt *meetingOpt;


/**
  定义房间相关配置
 */
@property (nonatomic, strong) AnyChatRoomOpt *roomOpt;

/**
 定义文件上传/下载相关配置
 */
@property (nonatomic, strong) AnyChatFileOpt *fileOpt;

/**
 定义视频呼叫相关配置
 */
@property (nonatomic, strong) AnyChatVideoCallOpt *videoCallOpt;
    
/**
 定义智能排队相关配置
 */
@property (nonatomic, strong) AnyChatQueueOpt *queueOpt;

/**
 定义音视频格式回调相关配置
 */
@property (nonatomic, strong) AnyChatMediaDataOpt *mediaDataOpt;

/**
 自己的ID
 */
@property (nonatomic, assign) int theMyUserID;

/**
 登录状态 成功:YES 失败:NO
 */
@property (nonatomic, assign) BOOL theLoginState;

/**
 登录AnyChat的应用ID
 */
@property (nonatomic, copy) NSString *appId;

/**
 是否开启了屏幕共享
 */
@property (nonatomic, assign, readonly) BOOL isOpenScreenShare;

/**
 是否使用base64编解码透明通道数据，默认为YES
 */
@property (nonatomic, assign) BOOL isTransBufferUseBase64;

/**
 初始化以及登录

 @param initOpt 初始化参数设置
 @return BRAC实例对象
 */
+ (instancetype)sdkInit:(AnyChatInitOpt *)initOpt;
    
/**
 获得全局单例

 @return 返回全局单例
 */
+ (instancetype)getInstance;
    
/**
 退出登录
 */
- (void)logout;

/**
 释放资源
 */
- (void)bracRelease;
    

/**
 获取用户信息

 @param userId 用户ID
 @return 用户信息
 */
- (NSDictionary *)getUserInfoWithUserId:(int)userId;

/**
 获取用户信息
 
 @param userName 用户名
 @return 用户信息
 */
- (NSDictionary *)getUserInfoWithUserName:(NSString*)userName;

/**
 查询指定用户互联网IP地址 (userId -1表示自己，需在登录完成后调用获取，其他用户需要在进入房间后调用获取)
 
 @param userId 用户ID
 @return ip地址
 */
- (NSString *)getUserIpAddress:(int)userId;

/**
 设置用户APP信息

 @param strJson 自定义参数

 */
- (int)setUserAppInfo:(NSString*)strJson;

/**
 获得版本信息

 @return 版本信息
 */
- (NSString *)getVersionInfo;
/*-----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 房间管理
 * 1. 注册房间管理事件
 * 2. 进入房间
 * 3. 获取房间中的用户列表
 * 4. 房间内的文字交流
 * 5. 退出房间
 *
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */
    
/**
 进入房间操作

 @param roomId 房间ID
 @param pass 房间密码
 @param callback 进入房间结果回调
 */
- (void)enterRoomWithRoomId:(NSString *)roomId
                   passWord:(NSString *)pass
                 completion:(EnterRoomCallback)callback;

/**
 获得房间里所有用户

 @return 返回用户数组
 */
- (NSArray *)getRoomUsers;
    
/**
 发送文字消息

 @param msg 文字内容
 @param users 接收人数组
 */
- (void)sendMsg:(NSString *)msg targetUsers:(NSArray *)users;
    
/**
 离开房间操作
 */
- (void)leaveRoom;
    
/*-----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 音视频通话
 * 注意:通话双方只有进入到同一个房间之后，才能进行音视频通话
 * 1. 本地麦克风管理，音视频通话时需打开麦克风，结束时关闭麦克风
 * 2. 本地摄像头管理，音视频通话时需打开摄像头，结束时关闭摄像头
 * 3. 接收/终止对方音频流
 * 4. 接收/终止对方音频流
 *
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */

//设置视频参数
- (void)setVideoOpt:(AnyChatVideoOpt *)opt;
//获取视频参数
- (AnyChatVideoOpt *)getVideoOpt;

/**
 SDK内核参数设置
 */
- (int) SetSDKOption:(int)optname withValue:(int)value;
// SDK内核参数设置
- (int) SetSDKOptionString:(int) optname : (NSString*) value;
// SDK内核参数状态查询
- (int) GetSDKOptionInt:(int) optname;
// SDK内核参数状态查询
- (NSString*) GetSDKOptionString:(int) optname;

// SDK控制
- (NSString*) SDKControl: (int) dwCtrlCode : (NSString*) lpInParam;

// 设置指定用户音视频流相关参数
- (int) SetUserStreamInfoInt: (int) dwUserId : (int) dwStreamIndex : (int) infoname : (int) value;
// 设置指定用户音视频流相关参数
- (int) SetUserStreamInfoString: (int) dwUserId : (int) dwStreamIndex : (int) infoname : (NSString*) value;
// 获取指定用户音视频流相关参数
- (int) GetUserStreamInfoInt: (int) dwUserId : (int) dwStreamIndex : (int) infoname;
// 获取指定用户音视频流相关参数
- (NSString*) GetUserStreamInfoString: (int) dwUserId : (int) dwStreamIndex : (int) infoname;

//设置音频参数
- (void)setAudioOpt:(AnyChatAudioOpt *)opt;
//获取音频参数
- (AnyChatAudioOpt *)getAudioOpt;

/**
 获取音频设备列表

 @return 返回音频设备列表
 */
- (NSArray *)getMicrophones;

/**
 获取麦克风
 
 @return 返回麦克风对象
 */
- (AnyChatMicrophone *)microphone;

/**
 获取视频设备列表

 @return 返回视频设备列表
 */
- (NSArray *)getCameras;

/**
 获取前置摄像头
 
 @return 返回前置摄像头对象
 */
- (AnyChatCamera *)frontFacingCamera;

/**
 获取后置摄像头
 
 @return 返回后置摄像头对象
 */
- (AnyChatCamera *)backFacingCamera;

/**
 开始接收对方音频流

 @param remoteUserId 对方ID
 */
- (void)getRemoteAudioStreamWithUserId:(int)remoteUserId;

/**
 停止接收对方音频流

 @param remoteUserId 对方ID
 */
- (void)cancelRemoteAudioStreamWithUserId:(int)remoteUserId;

/**
 开始接收对方视频流

 @param remoteUserId 对方ID
 @param imageView 显示远程视频视图
 */
- (void)getRemoteVideoStreamWithUserId:(int)remoteUserId renderView:(UIImageView *)imageView;

/**
 停止接收对方视频流

 @param remoteUserId 对方ID
 */
- (void)cancelRemoteVideoStreamWithUserId:(int)remoteUserId;


/**
 开始接收对方音频流扩展
 
 @param remoteUserId 对方ID
 @param streamIndex 流序号
 @param flags 标志位，可传0
 @param strParam 扩展参数，可以空字符串
 */
- (void)getRemoteAudioStreamExWithUserId:(int)remoteUserId streamIndex:(int)streamIndex flags:(int)flags strParam:(NSString *)strParam;

/**
 停止接收对方音频流扩展
 
 @param remoteUserId 对方ID
 @param streamIndex 流序号
 @param flags 标志位，可传0
 @param strParam 扩展参数，可以空字符串
 */
- (void)cancelRemoteAudioStreamExWithUserId:(int)remoteUserId streamIndex:(int)streamIndex flags:(int)flags strParam:(NSString *)strParam;

/**
 开始接收对方视频流扩展
 
 @param remoteUserId 对方ID
 @param imageView 显示对方视频视图
 @param streamIndex 流序号
 @param flags 标志位，可传0
 @param strParam 扩展参数，可以空字符串
 */
- (void)getRemoteVideoStreamExWithUserId:(int)remoteUserId renderView:(UIImageView *)imageView streamIndex:(int)streamIndex flags:(int)flags strParam:(NSString *)strParam;

/**
 停止接收对方视频流扩展
 
 @param remoteUserId 对方ID
 @param streamIndex 流序号
 @param flags 标志位，可传0
 @param strParam 扩展参数，可以空字符串
 */
- (void)cancelRemoteVideoStreamExWithUserId:(int)remoteUserId streamIndex:(int)streamIndex flags:(int)flags strParam:(NSString *)strParam;


/*-----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 录制(录音录像)
 * 注意:录制时，请确保正在视频通话中
 * 1. 开始录制
 * 2. 停止录制
 * 3. 在录像中添加水印
 * 4. 在录像中插入图片
 * 5. 在录像过程中进行截图
 *
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */
    
    
/**
 开始录制操作

 @param opt 录制相关配置
 */
- (int)startRecord:(AnyChatRecordOpt *)opt;

/**
 更新录制参数

 @param opt 录制相关配置
 */
- (NSString*)updateRecordParam:(AnyChatRecordOpt *)opt;

/**
 完成录制操作

 @param callback 录制结果回调
         data[@"filePath"] 存储路径
         data[@"elapse"] 文件时长
         data[@"filemd5"] 录像文件md5值(用于校验录像文件完整性等)
         data[@"filelength"] 录像文件大小
 */
- (void)completeRecord:(RecordDoneCallback)callback;

/**
 录制过程插入图片

 @param streamIndex 视频流画面编号 默认传1
 @param imagePath 图片路径
 */
- (void)insertImageDuringRecordWithStreamIndex:(NSInteger)streamIndex
                                           path:(NSString *)imagePath;
/**
 拍照操作

 @param userId 用户ID
 @param streamIndex 视频流画面编号
 @param fileName 文件名字
 @param localFilePath 图片存储路径
 @param callback 拍照操作结果回调
 */
- (void)takeSnapShotByUserId:(int)userId
                 streamIndex:(NSInteger)streamIndex
                    fileName:(NSString *)fileName
               localFilePath:(NSString *)localFilePath
                  completion:(SnapshotCallback)callback;

/**
 拍照操作

 @param opt 拍照相关配置
 @param callback 拍照操作结果回调
 */
- (void)takeSnapShot:(AnyChatSnapshotOpt *)opt
          completion:(SnapshotCallback)callback;

/**
 屏幕共享开关
 
 @param isOpen 是否开启屏幕共享
 @param streamIndex 视频流画面编号 默认传1
 @return 开启/关闭屏幕共享成功或失败
 */
- (BOOL)openScreenShare:(BOOL)isOpen StreamId:(int)streamIndex;

///设置流参数
-(int)SetInputStream:(int)stramIndex dwWidth:(int)Width dwHeight:(int)Height dwFps:(int)Fps;
///插入流数据
- (int)inputVideoStreamWith:(NSData *)viewData streamIndex:(int)streamIndex;
/**
 接收用户共享屏幕视频流
 
 @param userid 远程用户id
 @param imageView 显示远程视频视图
 @param streamIndex 视频流画面编号
 */
- (int)getScreenStreamWithUserId:(int)userid renderView:(UIImageView*)imageView StreamId:(int)streamIndex;

/*----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 文件传输与文件上传
 * 1. 注册文件传输/下载状态事件
 * 2. 发送文件给对方
 * 3. 上传文件到AnyChat服务器
 * 4. 文件下载
 *
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */
    
/**
 传送文件操作

 @param userId 接收人ID
 @param localPath 文件路径
 @return 任务对象信息 用于查询当前任务状态
 */
- (AnyChatFileTask *)creatFileTramsferTaskWihtUserId:(int)userId
                                              localPath:(NSString *)localPath;

/**
 文件上传服务器操作

 @param localPath 文件路径
 @return 任务对象信息 用于查询当前任务状态
 */
- (AnyChatFileTask *)createFileUploadTaskWithLocalPath:(NSString *)localPath;

/**
 文件上传服务器操作
 
 @param localPath 文件路径
 @param intervalTime 返回文件状态时间间隔，单位为秒
 @param filename 指定文件上传后的目标文件名。
 @param category 文件上传分类子目录，通过设置该字段的值可以将文件上传到不同的分类子目录中。
 @return 任务对象信息 用于查询当前任务状态
 */
- (AnyChatFileTask *)createFileUploadTaskWithLocalPath:(NSString *)localPath intervalTime:(int)intervalTime filename:(NSString *)filename category:(NSString *)category;

/**
 文件上传服务器操作
 
 @param taskOpt 配置类  可选择加密
 @return 任务对象信息 用于查询当前任务状态
 */
- (AnyChatFileTask *)createFileUploadTaskWithOpt:(AnyChatFileUploadOpt *)taskOpt;




/**
 文件下载操作
 
 @param path 下载保存路径
 @param fileId 文件id
 @param fileUrl 文件下载地址
 @param md5 文件md5
 @param type 1:ppt文件,2:视频文件,3:音频文件,4：普通zip文件
 @param intervalTime 返回文件状态时间间隔，单位为秒
 @return 任务对象信息
 */
- (AnyChatFileTask *)createFileDownloadTaskWithSavePath:(NSString *)path fileId:(NSString *)fileId fileUrl:(NSString *)fileUrl fileMD5:(NSString *)md5 fileType:(int)type intervalTime:(int)intervalTime;


/*----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 透明通道传输
 * 透明通道为用户提供数据通信的加密通道，数据将在用户之间透传，可用于用户之间的沟通协作
 * 或二进制数据传输
 *
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */

/**
 发送透明通道操作

 @param data 发送内容(NSData类型)
 @param targetUsers 接收人ID数组
 */
- (void)transBufferWithData:(NSData *)data targetUsers:(NSArray *)targetUsers;

/*----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 透明通道传输
 * 透明通道为用户提供数据通信的加密通道，数据将在用户之间透传，可用于用户之间的沟通协作
 * 或二进制数据传输
 *
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */

/**
 透明通道为用户提供数据通信的加密通道，数据将在用户之间透传。可发送的数据量限制为1K
 
 @param msg 发送的内容
 @param targetUsers 接收者ID列表
 @param time 判断是否发送成功的时间间隔，单位：s
 @param callback 发送消息是否成功的回调
 */
- (void)transBufferWithMsg:(NSString *)msg targetUsers:(NSArray *)targetUsers time:(int)time callback:(TransBufferBlock)callback;

/*----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 透明通道扩展传输
 * 透明通道为用户提供数据通信的加密通道，数据将在用户之间透传，可用于用户之间的沟通协作
 * 或二进制数据传输
 *
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */

/**
 透明通道扩展为用户提供数据通信的加密通道，数据将在用户之间透传。
 
 @param msg 发送的内容
 @param targetUsers 接收者ID列表
 @param time 判断是否发送成功的时间间隔，单位：s
 @param callback 发送消息是否成功的回调
 */
- (void)transBufferExWithMsg:(NSString *)msg targetUsers:(NSArray *)targetUsers time:(int)time callback:(TransBufferBlock)callback;

/*----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 视频呼叫
 * 视频呼叫业务逻辑主要实现两个终端（PC、手机、Pad等）之间的通话请求流程控制，
 * 包括请求（Request）、回复（accept,reject）、开始（Start）以及结束（Finish）等过程，
 * 可以形象理解为打电话的流程：拨号、等待、通话、挂断
 *
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */
    
/**
 呼叫操作

 @param userId 接听者ID (对方ID)
 @param str 扩展参数
 */
- (int)requestVideoCallWithUserId:(int)userId lpUserStr:(NSString *)str;
    
/**
 同意接听操作

 @param userId 呼叫者ID (对方ID)
 */
- (void)acceptVideoCallWithUserId:(int)userId;

/**
 同意接听操作

 @param userId 呼叫者ID (对方ID)
 @param str 扩展参数
 */
- (void)acceptVideoCallWithUserId:(int)userId lpUserStr:(NSString *)str;

/**
 拒绝接听操作

 @param userId 呼叫者ID (对方ID)
 */
- (void)rejectVideoCallWithUserId:(int)userId;

/**
 拒绝接听操作

 @param userId 呼叫者ID (对方ID)
 @param str 扩展参数
 */
- (void)rejectVideoCallWithUserId:(int)userId lpUserStr:(NSString *)str;

/**
 通话结束操作

 @param userId 对方ID
 */
- (int)hungUpVideoCallWithUserId:(int)userId;
    
/**
 呼叫取消操作

 @param userId 接听者ID (对方ID)
 */
- (int)cancelVideoCallWithUserId:(int)userId;
    
    
/*----------------------------------------------------------
 *-----------------------------------------------------------
 *
 * AnyChat SDK 智能排队
 *
 * 客户端排队流程: 获取营业厅列表->进入营业厅->获取营业厅队列列表->进入队列排队
 * 座席端排队流程: 获取营业厅列表->进入营业厅->开始服务->结束服务
 * ----------------------------------------------------------
 * ----------------------------------------------------------
 */

/**
 获得营业厅列表

 @param callback 返回营业厅列表
 */
- (void)getAreasWithCompletion:(SyncAreasCallback)callback;

/**
 获得营业厅列表
 @param parameter 自定义参数
 @param callback 返回营业厅列表
 */
- (void)getAreasWithParam:(NSDictionary*)parameter withCompletion:(SyncAreasCallback)callback;

/**
 进入营业厅操作

 @param areaId 营业厅ID
 @param callback 返回营业厅信息和队列列表
 */
- (void)enterAreaWithid:(NSString *)areaId
             completion:(EnterAreaCallback)callback;


/**
 查询排队信息

 @param queueId 队列id
 @return 信息
 */
- (NSDictionary *)getQueueInfoWihtId:(NSString *)queueId;

/**
 进入队列排队操作

 @param queueId 队列ID
 @param callback 返回队列信息
 */
- (void)enterQueueWithid:(NSString *)queueId
               completion:(EnterQueueCallback)callback;

/**
 获得队列排队人数

 @param queueId 队列ID
 @return 返回队列人数
 */
- (int)getQueuelengthWithid:(NSString *)queueId;

/**
 获得队列等待时间

 @param queueId 队列ID
 @return 返回等待时间
 */
- (int)getQueueTimeWithid:(NSString *)queueId;
/**
 取消排队操作

 @param callback 返回操作结果
 */
- (void)cancelQueuingWithCompletion:(CancelQueuingCallback)callback;

/**
 离开营业厅操作

 @param callback 返回操作结果
 */
- (void)leaveAreaWithCompletion:(LeaveAreaCallback)callback;
    
/**
 坐席服务操作

 @param code 操作控制 (开始服务 结束服务 暂停服务)
 @param callback 操作结果回调
 */
- (void)agentServiceCtrlWithCtrlCode:(BRAC_AgentServiceCtrlCode)code
                          completion:(ServiceCtrlCallback)callback;

/**
 获得坐席操作状态

 @return 操作状态
 */
- (NSDictionary *)getAgentStatus;

/**
 获得网络状态
 
 @return bitRatedown 下行
         bitRateUp   上行
         state       网络状态：0 优良，1 较好，2 一般，3 较差，4 非常差
 */
- (NSDictionary *)getNetWorkStatusWithUserId:(int)userId;

/**
 获取音频或者视频的上下行网络数据

 @param userId 用户ID
 @param type 0代表视频，1代表音频
 @return bitRatedown 下行
 bitRateUp   上行
 state       网络状态：0 优良，1 较好，2 一般，3 较差，4 非常差
 */
- (NSDictionary *)getNetworkStatusWithUserId:(int)userId mediaType:(int)type;

/**
 根据用户id获取网络状态
 
 @param  userId   用户id  
 
 @return 网络状态   0 优良，1 较好，2 一般，3 较差，4 非常差， 5 未知
 
 说明：对方进入房间并打开音视频才能查询对方网络状态，否则返回值为  5：未知状态
 */
- (int)getNetworkStatusByUserId:(int)userId;


/**
 查询队列排队用户的详细信息
 
 @param queueId 队列id
 @return 队列用户信息
 */
- (NSDictionary *)getQueueUserInfo:(NSString *)queueId;


#pragma mark- 音量控制
typedef NS_ENUM(NSInteger, BRAC_AudioDeviceType){
    /**
     *  输入设备
     */
    BRAC_AUDIO_DEVICE_TYPE_IN    = 0,
    /**
     *  输出设备
     */
    BRAC_AUDIO_DEVICE_TYPE_OUT   = 1,
};

/**
 设置音量大小
 
 @param device 设备类型
 @param value 音量(0~100)
 */
- (void)setAudioVolume:(BRAC_AudioDeviceType)device volume:(int)value;

/**
 获取音量大小
 
 @param device 设备类型
 @return 返回音量(0~100)
 */
- (int)getAudioVolume:(BRAC_AudioDeviceType)device;

/**
 查询用户的摄像头状态
 
 @param userId 用户ID
 @return 返回用户的摄像头状态 1 摄像头关闭， 2 摄像头开启
 */
- (int)getUserCameraState:(int)userId;
/**
 查询用户的音频采集状态
 
 @param userId 用户ID
 @return 返回用户的音频采集状态 0 音频采集关闭， 1 音频采集开启
 */
- (int)getUserMicState:(int)userId;
/**
 用户当前说话声浪音量
 
 @param userId 用户ID
 @return 返回当前声浪音量大小(0~100)
 */
- (int)getUserSpeakVolume:(int)userId;


/**
 关闭或者打开手机外放声音

 @param enable 是否打开或关闭
 */
- (int)setSpeakVolumeEnable:(BOOL)enable;

#pragma mark- 签名
/**
 签名弹窗 (默认生成的签名在底图的右下角)
 
 @param containerView 弹窗父控件
 @param size 弹窗的size
 @param imagePath 签名底图
 @param callback 签名回调
 @return 签名弹窗
 */
- (UIView *)showSignatureInView:(UIView *)containerView
                           size:(CGSize)size
                      imagePath:(NSString *)imagePath
                     completion:(SignatureDoneCallback)callback;
/**
 签名弹窗 (默认生成的签名在底图的右下角)
 
 @param containerView 弹窗父控件
 @param size 弹窗的size
 @param imagePath 签名底图
 @param positionX 手写签名文字展示位置在模板图片的x轴坐标(由于签名模板图分辨的要求，参数范围在0-570之间)
 @param positionY 手写签名文字展示位置在模板图片的y轴坐标坐标(由于签名模板图分辨的要求，参数范围在0-480之间) 
 @param callback 签名回调
 @return 签名弹窗
 */
- (UIView *)showSignatureInView:(UIView *)containerView
                           size:(CGSize)size
                      imagePath:(NSString *)imagePath
             signaturePositionX:(int)positionX
             signaturePositionY:(int)positionY
                     completion:(SignatureDoneCallback)callback;

#pragma mark- 业务数据传输


/**
 设置业务数据传输超时时间
 
 @param timeout 超时时间 单位:秒
 */
- (void)setBusinessTransferTimeout:(int)timeout;

/**
 异步传输业务数据
 
 @param json 数据
 @param callback 回调
 @return 返回
 */
- (NSString *) asyncTransfer:(NSString*)json callback:(BusinessTransferCallBack)callback;

#pragma mark- 写日志

- (void)coresdkWriteLog:(NSString *)msg;

#pragma mark - 本地日志打印开关

/**
 是否在控制台打印日志

 @param Open YES 或者 NO
 */
- (void)openDebugLog:(BOOL)Open;

#pragma mark- 聚焦
- (int)setFocusingWithOpt:(AnyChatFocusingOpt *)focusingOpt;

#pragma mark- AI

/**
 创建机器人

 @param delegate 创建/销毁的回调代理
 @return 返回创建的机器人
 */
- (AnyChatAIRobot*)createRobotWithTarget:(id<AnyChatAIDelegate>)delegate;

-(AnyChatAIRobot*)createRobotWithConfig:(AnyChatAiConfig*)congfigOpt target:(id<AnyChatAIDelegate>)delegate;
/**
 获取当前所有机器人
 @return 返回所有当前存活的机器人
 */
- (NSArray<AnyChatAIRobot*>*)getAllRobot;

/**
 销毁指定机器人：没有回调，默认必定销毁成功
 @param robotId 机器人id
 */
- (void)destroyRobot:(NSString*)robotId;

/**
 销毁所有机器人
 */
- (void)destroyAllRobot;

#pragma mark - 插拔式服务
/**
 获取GUId

 @return 生成新的guid
 */
- (NSString *)getGUID;


/**
 注册插拔式服务

 @param service 插拔式服务对象
 */
- (void)registeDetachableService:(AnyChatDetachableService *)service;


/**
 注销插拔式服务

 @param service 插拔式服务对象
 */
- (void)resignDetachableService:(AnyChatDetachableService *)service;

@end
