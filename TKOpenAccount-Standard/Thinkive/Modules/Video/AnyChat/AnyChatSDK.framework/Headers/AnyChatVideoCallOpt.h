//
//  BRAC_VideoCallOpt.h
//  AnyChatInterviewIphone
//
//  Created by bairuitech on 2017/7/8.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AnyChatResult.h"
@protocol VideoCallDelegate <NSObject>
    
@optional
    
/**
 收到呼叫请求回调

 @param data userId 用户ID
             lpUserStr 扩展字符串
 */
- (void)onReceiveVideoCallRequest:(NSDictionary *)data;
    
/**
 呼叫请求响应回调 

 @param result 响应信息
 */
- (void)onReceiveVideoCallError:(AnyChatResult *)result;
    
/**
 音视频通话开始回调

 @param data userId 用户ID 
             roomId 房间ID
             lpUserStr 扩展字符串
 */
- (void)onReceiveVideoCallStart:(NSDictionary *)data;
    
/**
 音视频通话结束回调

 @param data userId 用户ID
             lpUserStr 扩展字符串
 */
- (void)onReceiveVideoCallFinish:(NSDictionary *)data;
    
@end
@interface AnyChatVideoCallOpt : NSObject
    

@property (nonatomic, weak) id<VideoCallDelegate>videoCallDelegate;

@end
