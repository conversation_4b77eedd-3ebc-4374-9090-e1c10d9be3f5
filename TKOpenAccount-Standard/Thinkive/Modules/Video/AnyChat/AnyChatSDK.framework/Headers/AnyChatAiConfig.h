//
//  AnyChatAiConfig.h
//  AnyChatSDK
//
//  Created by <PERSON> on 2020/6/8.
//  Copyright © 2020 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, AIAbilityFirm) {
    AIAbilityFirmBR = 0,//百锐AI
    AIAbilityFirmAL = 1,
};

typedef NS_ENUM(NSUInteger, RobotType) {
    RobotTypeOnline = 0,
    RobotTypeOffline = 1,
};

NS_ASSUME_NONNULL_BEGIN

@interface AnyChatAiConfig : NSObject
//
@property (nonatomic, assign) AIAbilityFirm firm;
@property (nonatomic, assign) RobotType mode;

@end

NS_ASSUME_NONNULL_END
