//
//  AnyChatRecordTagData.h
//  AnyChatSDK
//
//  Created by Yu on 2020/3/25.
//  Copyright © 2020 Bairui. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface AnyChatRecordTagData : NSObject
///打标签任务ID
@property(nonatomic,strong)NSString *taskId;
///相对录像时间
@property(nonatomic,assign)int timestamp;
///相对上次标签时间间隔
@property(nonatomic,assign)int elapse;
///标签下标(打标签次数)
@property(nonatomic,assign) int index;
///流水号
@property(nonatomic,strong)NSString *tradeNo;
///录像文件名
@property(nonatomic,strong)NSString *tagFileName;
///标签类型
@property(nonatomic,assign)int type;
///标签标题
@property(nonatomic,strong)NSString *title;
///标签检测内容结果
@property(nonatomic,strong)NSString *result;
///拓展字段
@property(nonatomic,strong)NSString *userData;

///检测是否通过 0:通过;1:不通过
@property(nonatomic,assign, getter=isCheckStatus) BOOL checkStatus;
///问题的内容 type为3,4,5时有值
@property(nonatomic,strong)NSString *checkQuestion;
///预设答案 type为3,4,5时有值
@property(nonatomic,strong)NSArray *expectAnswer;


@end

NS_ASSUME_NONNULL_END
