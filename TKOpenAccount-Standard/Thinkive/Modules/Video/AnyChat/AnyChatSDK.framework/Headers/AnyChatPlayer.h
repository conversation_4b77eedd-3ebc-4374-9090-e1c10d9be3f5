//
//  AnyChatPlayer.h
//  AnyChatPlayer
//
//  Created by j<PERSON><PERSON><PERSON><PERSON> on 2017/5/22.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@class AnyChatPlayer;

#pragma mark - 播放资源相关回调协议 AnyChatPlayerDelegate
@protocol AnyChatPlayerDelegate <NSObject>

@optional

/**
 * 播放状态回调
 */
-(void)AnyChatPlayer:(AnyChatPlayer *)AnyChatPlayer obtainPlayStatusWithDict:(NSDictionary *)dict playStatus:(int)status;

/**
 * 播放翻页回调
 */
-(void)AnyChatPlayer:(AnyChatPlayer *)AnyChatPlayer didScrollToIndex:(NSInteger)index imageFilePath:(NSString *)path;



@end

@interface AnyChatPlayer : NSObject

@property (strong, nonatomic) NSMutableArray   * pptMutableArray;//ppt图片数据

@property (nonatomic, weak) id<AnyChatPlayerDelegate>anyChatPlayerDelegate;

#pragma mark -
#pragma mark - 初使化及设置方法
/**
 *  初始化AnyChatPlayer
 *  @param fileId 资源id
 *  @param playView 播放视图容器
 *  @return 播放器对象
 */
- (AnyChatPlayer*) initAnyChatPlayerWithFileId: (NSString *) fileId withPlayView:(UIView*)playView type:(int)type;

/**
 *  初始化AnyChatPlayer
 *  @param dict 初始化字典参数
 *  @param playView 播放视图容器
 *  @param type 类型，1为自助双录，2为远程双录
 *  @return 播放器对象
 */
- (AnyChatPlayer*)initAnyChatPlayerWithDict:(NSDictionary*)dict playView:(UIView*)playView type:(int)type;

#pragma mark - 播放资源操作方法

/**
 @brief 播放资源
 @return 播放操作状态返回信息
 */
- (int) play;


/**
 @brief 暂停资源播放
 @return 暂停操作状态返回信息
 */
- (int) pause;


/**
 @brief 恢复资源播放
 @return 恢复操作状态返回信息
 */
- (int) resume;

/**
 @brief 停止资源播放
 @return 停止操作状态返回信息
 */
- (int) stop;


/**
 @brief 获取当前播放状态信息
 @return 当前播放状态信息
 */
- (NSDictionary*) getPlayStatus;

#pragma mark -
#pragma mark - 控制PPT操作相关操作方法

/**
 @brief 控制切换到下一帧
 @return 切换到下一帧状态返回信息
 */
- (int) nextFrame;


/**
 @brief 控制切换到上一帧
 @return 切换到上一帧状态返回信息
 */
- (int) previousFrame;

/**
 @brief 跳转到指定时间
 @return 跳转到指定时间 状态码返回值
 */
- (int) seekTo:(int)s;


@end




