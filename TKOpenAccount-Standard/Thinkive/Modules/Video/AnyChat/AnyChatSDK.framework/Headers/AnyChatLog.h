//
//  BRAC_Log.h
//
//
//  Created by bairuitech on 2017/7/17.
//  Copyright © 2017年 Coneboy_K. All rights reserved.
//

#import <Foundation/Foundation.h>
//Debug
#define BRAC_LogD(desStr) [BRAC_Log logD:[NSString stringWithFormat:@":%s Des:%@",__func__,desStr],@""];
//Info
#define BRAC_LogI(desStr) [BRAC_Log logI:[NSString stringWithFormat:@":%s Des:%@",__func__,desStr],@""];

//Error
#define BRAC_LogE(desStr) [BRAC_Log logE:[NSString stringWithFormat:@":%s Des:%@",__func__,,desStr],@""];
//通过这个宏来打印日志
#define BRACDebugLog(log,...) [AnyChatLog printLocalLog:[NSString stringWithFormat:(log),##__VA_ARGS__]]

typedef NS_ENUM(NSInteger, BRAC_LogLevel){
    
    BRAC_LOGLEVEL_INFO  = 1,
    BRAC_LOGLEVEL_DEBUG = 2,
    BRAC_LOGLEVEL_ERROR = 3,
};

@interface AnyChatLog : NSObject

/**
 是否打开日志
 */
@property (nonatomic, assign) BOOL isOpenDebugLog;


/**
 单例方法

 @return 实例对象
 */
+ (instancetype)shareInstance;


/**
 打印本地日志

 @param log 日志内容
 */
+ (void)printLocalLog:(NSString *)log;

/**
 *  log初始化函数，在系统启动时调用
 */

/**
 *  log初始化函数，在系统启动时调用
 */
+ (void)logIntial;
    
/**
 *  设置要记录的log级别
 *
 *  @param level level 要设置的log级别
 */
+ (void)setLogLevel:(BRAC_LogLevel)level;
    
/**
 *  记录系统crash的Log函数
 *
 *  @param exception 系统异常
 */
+ (void)logCrash:(NSException*)exception;
    
/**
 *  log记录函数
 *
 *  @param level  log所属的等级
 *  @param format 具体记录log的格式以及内容
 */
+ (void)logLevel:(BRAC_LogLevel)level LogInfo:(NSString*)format,... NS_FORMAT_FUNCTION(2,3);
    
/**
 *  LOGLEVELD级Log记录函数
 *
 *  @param format 具体记录log的格式以及内容
 */
+ (void)logD:(NSString*)format,... NS_FORMAT_FUNCTION(1,2);
    
/**
 *  LOGLEVELI级Log记录函数
 *
 *  @param format 具体记录log的格式以及内容
 */
+ (void)logI:(NSString*)format,... NS_FORMAT_FUNCTION(1,2);

/**
 *  LOGLEVELE级Log记录函数
 *
 *  @param format 具体记录log的格式以及内容
 */
+ (void)logE:(NSString*)format,... NS_FORMAT_FUNCTION(1,2);

@end
