//
//  AnyChatDownload.h
//  AnyChatPPTOperationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/5/23.
//  Copyright © 2017年 anychat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#import "AnyChatFileTask.h"
#import "AnyChatDefine.h"
#import "AnyChatErrorCode.h"


@class AnyChatDownload;
/**
 * @brief 下载相关回调协议
 */
@protocol AnyChatDownloadDelegate <NSObject>

@optional

/**
 * @brief 下载结果接口回调
 */
-(void)AnyChatDownload:(AnyChatDownload *)anyChatDownload didDownloadFinishWithDict:(NSDictionary *)downloadDict downloadStatus:(int)status;


/**
 * @brief 下载进度接口回调
 */
-(void)AnyChatDownload:(AnyChatDownload *)anyChatDownload didDownloadProgressWithDict:(NSDictionary *)downloadDict;


@end

@interface AnyChatDownload : NSObject

@property (nonatomic, weak) id<AnyChatDownloadDelegate> downloadDelegate;

/**
 *  单例方法
 *
 *  @return 实例对象
 */
+ (AnyChatDownload*)getInstance;


/**
 *  初始化AnyChatDownload
 *  @param dict 初始化下载参数,包含本地保存路径，以及其他拓展参数等
 *  @return 初始化状态码
 */
- (NSString *) AnyChatDownload: (NSDictionary*) dict;

/**
 *  开始下载资源
 *  @param dict 下载任务参数
 *  @return 下载状态码
 */
- (NSString *) start: (NSDictionary*) dict;


/**
 *  取消下载指定资源
 *  @param fileId 文件ID
 *  @return 下载状态码
 */
- (NSString *) cancel:(NSString*)fileId;


/**
 *  @brief 查询某个资源的下载状态
 *  @param fileId 文件ID
 *  @return 下载状态信息
 */
- (NSString *) getStatus:(NSString*)fileId;


/**
 *  @brief 查询某个资源的详细信息
 *  @param fileId 文件ID
 *  @return 详细信息
 */
- (NSDictionary *) getInfo:(NSString*)fileId;

- (AnyChatFileTask *)createFileDownloadTaskWithSavePath:(NSString *)path fileId:(NSString *)fileId fileUrl:(NSString *)fileUrl fileMD5:(NSString *)md5 fileType:(int)type intervalTime:(int)intervalTime;

@end





