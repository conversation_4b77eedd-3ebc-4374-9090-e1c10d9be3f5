//
//  STDetectorConfig.h
//  STDetector
//
//  Created by Sensetime Inc. on 2021/1/18.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 人脸质量配置
__attribute__((visibility("default")))
@interface STCommonFaceQualityConfig : NSObject

/// 是否开启角度检测
@property (nonatomic, assign) BOOL angleEnabled;

/// 是否开启眼睛遮挡检测
@property (nonatomic, assign) BOOL eyeOcclusionEnabled;

/// 闭眼模式，是否允许检测过程中闭眼
@property (nonatomic, assign) BOOL allowEyeClose;

/// 是否开启嘴巴遮挡检测
@property (nonatomic, assign) BOOL mouthOcclusionEnabled;

/// 是否开启眉毛遮挡检测
@property (nonatomic, assign) BOOL browOcclusionEnabled;

/// 是否开启张嘴检测
@property (nonatomic, assign) BOOL mouthOpenEnabled;

/// 亮度下限
@property (nonatomic, assign) float overDarkThreshold;

/// 亮度上限
@property (nonatomic, assign) float overGlareThreshold;

/// 模糊
@property (nonatomic, assign) float blurThreshold;

/// 是否支持多人脸检测
@property (nonatomic, assign) BOOL multiTargetsEnabled;

@end

/// 炫彩配置
@interface STLivenessCommonColorfulConfig : NSObject

//炫彩模式开关，默认关闭
@property (nonatomic, assign) BOOL colorfulEnable;

///颜色数量，默认为5
@property (nonatomic, assign) int colorfulNumber;

///每种颜色时间，默认为1000毫秒，就是1秒
@property (nonatomic, assign) int colorfulTimeInterval;

///每种颜色抽取帧数，默认为2.
@property (nonatomic, assign) int colorfulFrame;

@end

/// 检测器配置
@interface STDetectorCommonConfig : NSObject

/// 授权文件路径
@property (nonatomic, copy) NSString *licenseFilePath;

/// 模型文件配置
@property (nonatomic, copy) NSDictionary *modelConfig;

@end

NS_ASSUME_NONNULL_END
