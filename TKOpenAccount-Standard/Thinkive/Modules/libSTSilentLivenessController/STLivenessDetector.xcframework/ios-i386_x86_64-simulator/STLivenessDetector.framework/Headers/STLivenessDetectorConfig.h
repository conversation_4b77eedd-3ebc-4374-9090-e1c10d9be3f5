//
//  STLivenessDetectorConfig.h
//  Pods
//
//  Created by Sensetime Inc. on 2021/1/14.
//

#import <Foundation/Foundation.h>
#import "STDetectorCommonConfig.h"

NS_ASSUME_NONNULL_BEGIN

// MARK: - STLivenessDetectorConfig
__attribute__ ((visibility ("default")))
@interface STLivenessDetectorConfig : STDetectorCommonConfig

///  质量控制配置
@property (nonatomic, strong) STCommonFaceQualityConfig *qualityConfig;

/// 炫彩配置
@property (nonatomic, strong) STLivenessCommonColorfulConfig *colorfulConfig;

/// 活体分数阈值
@property (nonatomic, assign) float livenessScoreThreshold;

@end

NS_ASSUME_NONNULL_END
