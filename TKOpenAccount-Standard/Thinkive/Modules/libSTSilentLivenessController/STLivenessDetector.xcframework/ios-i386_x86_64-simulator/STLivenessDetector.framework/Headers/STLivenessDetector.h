//
//  STLivenessDetector.h
//  STDetector
//
//  Created by Sensetime Inc. on 2021/1/14.
//

#import <Foundation/Foundation.h>
#import <CoreMedia/CoreMedia.h>

#import "STLivenessDetectorConfig.h"
#import "STLivenessDetectorDelegate.h"
#import "STLivenessMotionType.h"
#import "STCommonDetector.h"

NS_ASSUME_NONNULL_BEGIN

__attribute__ ((visibility ("default")))
@interface STLivenessDetector : STCommonDetector

/// 初始化方法
/// @param config 配置
/// @param delegate 检测结果回调
/// @param error 初始化异常
- (instancetype)initWithConfig: (STLivenessDetectorConfig *)config
                   andDelegate: (id<STLivenessDetectorDelegate>)delegate
                       onError: (NSError **)error;

/// 人脸预检测超时
/// @param interval 超时时间，单位毫秒
- (void)setReadyTimeoutDuration: (NSInteger)interval;

/// 检测超时时间
/// @param interval 超时时间，单位毫秒
- (void)setMotionTimeoutDuration: (NSInteger)interval;

/// 设置预览框大小
/// @param targetRect 预览框大小
- (void)setTargetRect: (CGRect)targetRect;

/// 检测动作
/// @param motionTypes 动作数组，按照数组顺序检测
- (void)setDetectMotionTypes: (NSArray *)motionTypes;

/// 开始检测
- (void)start;

/// 取消检测
- (void)cancel;

/// 重新开始
- (void)reStart;

/// 输入视频帧
/// @param sampleBuffer 视频帧
- (void)input: (CMSampleBufferRef)sampleBuffer;

/// 输入视频帧
/// @param image 视频帧
- (BOOL)inputCIImage: (CIImage *)image;

/// 获取版本
+ (NSString *)getVersion;

/// 是否开启内部Log
/// @param isEnabled 是否开启
/// @param callback log 回调
+ (void)enableLog: (BOOL)isEnabled
  withLogCallback:(void (^)(NSString *log))callback;

@end

NS_ASSUME_NONNULL_END
