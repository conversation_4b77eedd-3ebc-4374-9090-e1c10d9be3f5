//
//  STResultImage.h
//  STDataModel
//
//  Created by Sensetime Inc. on 2020/12/17.
//

#import <Foundation/Foundation.h>
#import "STLivenessRect.h"

NS_ASSUME_NONNULL_BEGIN
__attribute__((visibility("default")))
@interface STLivenessResultImage : NSObject

@property (nonatomic, copy, readonly) NSData *rawData;

@property (nonatomic, copy, readonly) NSString *signature;

@property (nonatomic, copy, readonly) UIImage *image;

@property (nonatomic, copy, readonly) NSArray<STLivenessRect *> *rects;

- (instancetype)init NS_UNAVAILABLE;

- (instancetype)initWithUIImage: (UIImage *)image andRects: (NSArray<STLivenessRect *>*)rects;

- (instancetype)initWithImageRawData: (NSData *)imageRawData andRects: (NSArray<STLivenessRect *>*)rects;

- (instancetype)initWithImageRawData: (NSData *)imageRawData andSignature: (NSString *)signature andRects: (NSArray<STLivenessRect *>*)rects;

@end

NS_ASSUME_NONNULL_END
