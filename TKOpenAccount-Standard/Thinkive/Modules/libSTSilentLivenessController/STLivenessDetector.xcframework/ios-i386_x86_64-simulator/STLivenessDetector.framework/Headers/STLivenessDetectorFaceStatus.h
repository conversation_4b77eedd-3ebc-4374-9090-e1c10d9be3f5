//
//  STLivenessDetectorFaceStatus.h
//  Pods
//
//  Created by Sensetime Inc. on 2021/1/14.
//

#ifndef STDetectorFaceStatus_h
#define STDetectorFaceStatus_h

typedef NS_ENUM(NSUInteger, STLivenessDetectorFaceStatus) {
    /// 未检测到人脸
    kSTLivenessDetectorFaceStatusNoFaceFound = 0,
    
    /// 人脸丢失
    kSTLivenessDetectorFaceStatusTargetLost,
    
    /// 眉毛有遮挡
    kSTLivenessDetectorFaceStatusFaceOcclusionBrow,
    
    /// 眼睛有遮挡
    kSTLivenessDetectorFaceStatusFaceOcclusionEye,
    
    /// 鼻子有遮挡
    kSTLivenessDetectorFaceStatusFaceOcclusionNose,
    
    /// 嘴部有遮挡
    kSTLivenessDetectorFaceStatusFaceOcclusionMouth,
    
    /// 脸颊有遮挡
    kSTLivenessDetectorFaceStatusFaceOcclusionCheek,
    
    /// 人脸框太小，人脸距离屏幕太远
    kSTLivenessDetectorFaceStatusFaceTooFar,
    
    /// 人脸框太大，人脸距离屏幕太近
    kSTLivenessDetectorFaceStatusFaceTooClose,
    
    /// 人脸框不在定位框范围内
    kSTLivenessDetectorFaceStatusFaceOutBound,
    
    /// 人脸角度偏差过大
    kSTLivenessDetectorFaceStatusAngleFail,
    
    /// 图像过暗
    kSTLivenessDetectorFaceStatusTooDark,
    
    /// 过亮
    kSTLivenessDetectorFaceStatusOverExposure,
    
    /// 模糊
    kSTLivenessDetectorFaceStatusBlur,
    
    /// 闭眼
    kSTLivenessDetectorFaceStatusEyeClosed,
    
    /// 检测到多人
    kSTLivenessDetectorFaceStatusTooManyTargetFound,
    
    /// 正常
    kSTLivenessDetectorFaceStatusNormal,
    
    /// 眨眼
    kSTLivenessDetectorFaceStatusMotionBlink,
    
    /// 张嘴
    kSTLivenessDetectorFaceStatusMotionMouth,
    
    /// 眼晴睁开
    kSTLivenessFaceStatusEyes,
    
    /// 准备状态 嘴闭合
    kSTLivenessReadyFaceStatusMouth,
    
    /// 准备状态 头部正对并摆正
    kSTLivenessReadyFaceStatusHeadPose,
    
};

#endif /* STDetectorFaceStatus_h */
