//
//  STLivenessMotionType.h
//  Pods
//
//  Created by Sensetime Inc. on 2021/1/15.
//

#ifndef STLivenessMotionType_h
#define STLivenessMotionType_h

// 交互活体检测动作类型
typedef NS_ENUM(NSUInteger, STLivenessMotionType) {
    
    // 眨眼
    kSTLivenessMotionTypeBlink = 0,
    
    // 张嘴
    kSTLivenessMotionTypeMouth,
    
    // 上下点头
    kSTLivenessMotionTypeNod,
    
    // 左右摇头
    kSTLivenessMotionTypeYaw
};

typedef NS_ENUM(NSUInteger, STLivenessFacePositionCalibration) {
    kSTLivenessFacePositionCalibrationOK = 0,

    // 人脸在左
    kSTLivenessFacePositionCalibrationLeft,
    
    // 
    kSTLivenessFacePositionCalibrationRight,
    kSTLivenessFacePositionCalibrationTop,
    kSTLivenessFacePositionCalibrationBottom,
        
    kSTLivenessFacePositionCalibrationRotateRight,
    kSTLivenessFacePositionCalibrationRotateLeft,
    
    kSTLivenessFacePositionCalibrationHeadLeft,
    kSTLivenessFacePositionCalibrationHeadRight,
    
    kSTLivenessFacePositionCalibrationHeadUp,
    kSTLivenessFacePositionCalibrationHeadDown
};

#endif /* STLivenessMotionType_h */
