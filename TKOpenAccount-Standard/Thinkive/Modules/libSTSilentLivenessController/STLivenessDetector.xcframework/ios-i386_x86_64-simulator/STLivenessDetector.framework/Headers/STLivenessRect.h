//
//  STRect.h
//  STDataModel
//
//  Created by Sensetime Inc. on 2020/12/17.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// Rect
__attribute__((visibility("default")))
@interface STLivenessRect : NSObject

- (instancetype)init NS_UNAVAILABLE;

- (instancetype)initWithCGRect: (CGRect)rect;

- (instancetype)initWithLeft: (float)left top:(float)top width:(float)width height:(float)height;

- (instancetype)initWithLeft: (float)left top:(float)top right:(float)right bottom:(float)bottom;

- (CGRect)toCGRect;

- (float)left;

- (float) right;

- (float) top;

- (float) bottom;

- (float) width;

- (float) height;

@end

NS_ASSUME_NONNULL_END
