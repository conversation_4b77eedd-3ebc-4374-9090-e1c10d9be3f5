<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/STCommonDetector.h</key>
		<data>
		WtpVHB8Vv2R4e0KtUzGGhZ/45H8=
		</data>
		<key>Headers/STDetectorCommonConfig.h</key>
		<data>
		2uLHuow8fgS3M5umEjFH5vYXF40=
		</data>
		<key>Headers/STLivenessDetector-umbrella.h</key>
		<data>
		n5N1qsK9ntwJ6u8CT0rZ1X9HHPc=
		</data>
		<key>Headers/STLivenessDetector.h</key>
		<data>
		IuJMCaUK4YQMpx46DcE70Ntwbak=
		</data>
		<key>Headers/STLivenessDetectorConfig.h</key>
		<data>
		2sG/+mRS74ugkvjOdcn42lT9Dzk=
		</data>
		<key>Headers/STLivenessDetectorDelegate.h</key>
		<data>
		ZMmTAZPjviboUqmvqQNiSl/r9Ik=
		</data>
		<key>Headers/STLivenessDetectorFaceStatus.h</key>
		<data>
		4aUlYHiHq7h7ZM6xm/vwNYUZtNA=
		</data>
		<key>Headers/STLivenessMotionType.h</key>
		<data>
		jk93iy8bQcapDUbVE1CdFmwIJR0=
		</data>
		<key>Headers/STLivenessRect.h</key>
		<data>
		Q24H18Xr8OBELHBY7dc37/LbFDg=
		</data>
		<key>Headers/STLivenessResultImage.h</key>
		<data>
		2Pxbt3JJBqUNBeJiXSpYUqREd4Q=
		</data>
		<key>Info.plist</key>
		<data>
		7JfNzCEg+VwqsU9nUC2n4aJ79W4=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		xLT6SOI4/EVh6Aa+XMSG9JcmLF8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/STCommonDetector.h</key>
		<dict>
			<key>hash</key>
			<data>
			WtpVHB8Vv2R4e0KtUzGGhZ/45H8=
			</data>
			<key>hash2</key>
			<data>
			+rz+SKm9dIjgv9x5zFtn76J31u47eLCwQcFd82wfFVs=
			</data>
		</dict>
		<key>Headers/STDetectorCommonConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			2uLHuow8fgS3M5umEjFH5vYXF40=
			</data>
			<key>hash2</key>
			<data>
			OR8O4yiMr43jB10NSgyPXrGZp3iuZpzqWe3eYbksE04=
			</data>
		</dict>
		<key>Headers/STLivenessDetector-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			n5N1qsK9ntwJ6u8CT0rZ1X9HHPc=
			</data>
			<key>hash2</key>
			<data>
			H3/OawQ9CNLgPnwtV9MhsD//GgEE/Qupg/NOJswMFEY=
			</data>
		</dict>
		<key>Headers/STLivenessDetector.h</key>
		<dict>
			<key>hash</key>
			<data>
			IuJMCaUK4YQMpx46DcE70Ntwbak=
			</data>
			<key>hash2</key>
			<data>
			cUMD/uwc3Cp5ryw5OyYZPEEZUVe6rsujThm1Dt2Oq4s=
			</data>
		</dict>
		<key>Headers/STLivenessDetectorConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			2sG/+mRS74ugkvjOdcn42lT9Dzk=
			</data>
			<key>hash2</key>
			<data>
			Y9t3XQQOyKgAxVrERDWAinLDQ/Gqk03lIkRqWuG088A=
			</data>
		</dict>
		<key>Headers/STLivenessDetectorDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZMmTAZPjviboUqmvqQNiSl/r9Ik=
			</data>
			<key>hash2</key>
			<data>
			MiovFa1unCamKbA/DkjTgEDixlPrf6m6+QG5oVgHhjw=
			</data>
		</dict>
		<key>Headers/STLivenessDetectorFaceStatus.h</key>
		<dict>
			<key>hash</key>
			<data>
			4aUlYHiHq7h7ZM6xm/vwNYUZtNA=
			</data>
			<key>hash2</key>
			<data>
			lMZ5jDJ2NrQuXAfDf8T7X8YNADBBFoaO2laci5Iv/AQ=
			</data>
		</dict>
		<key>Headers/STLivenessMotionType.h</key>
		<dict>
			<key>hash</key>
			<data>
			jk93iy8bQcapDUbVE1CdFmwIJR0=
			</data>
			<key>hash2</key>
			<data>
			6aRiHmgsQyw4SrpPBFDVVq8ICyuIbFavHfnmT8vfnEI=
			</data>
		</dict>
		<key>Headers/STLivenessRect.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q24H18Xr8OBELHBY7dc37/LbFDg=
			</data>
			<key>hash2</key>
			<data>
			8BvWRD5OwwI52EJW8GO/HNE02DJle2JWmxHtoCXGfYo=
			</data>
		</dict>
		<key>Headers/STLivenessResultImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			2Pxbt3JJBqUNBeJiXSpYUqREd4Q=
			</data>
			<key>hash2</key>
			<data>
			hayEGO3UXRhvIwMUOFGj0yYrpvUograqG3MKW+2n/Ho=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			xLT6SOI4/EVh6Aa+XMSG9JcmLF8=
			</data>
			<key>hash2</key>
			<data>
			qCrpFP+UDII1PVM/BPyVbSTfCx3arLemvWnBVH/AgPk=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
