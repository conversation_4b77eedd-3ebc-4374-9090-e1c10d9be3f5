#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif

#import "STLivenessDetector.h"
#import "STCommonDetector.h"
#import "STDetectorCommonConfig.h"
#import "STLivenessDetectorFaceStatus.h"
#import "STLivenessRect.h"
#import "STLivenessResultImage.h"
#import "STLivenessDetectorConfig.h"
#import "STLivenessDetectorDelegate.h"
#import "STLivenessMotionType.h"

FOUNDATION_EXPORT double STLivenessDetectorVersionNumber;
FOUNDATION_EXPORT const unsigned char STLivenessDetectorVersionString[];

