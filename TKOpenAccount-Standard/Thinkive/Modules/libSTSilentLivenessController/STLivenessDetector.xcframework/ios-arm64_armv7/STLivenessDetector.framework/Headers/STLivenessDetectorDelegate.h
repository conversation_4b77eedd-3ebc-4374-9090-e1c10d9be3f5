//
//  STLivenessDetectorDelegate.h
//  Pods
//
//  Created by Sensetime Inc. on 2021/1/15.
//

#ifndef STLivenessDetectorDelegate_h
#define STLivenessDetectorDelegate_h

#import "STLivenessResultImage.h"
#import "STLivenessMotionType.h"
#import "STLivenessDetectorFaceStatus.h"

NS_ASSUME_NONNULL_BEGIN

/// 活体检测结果 Delegate
@protocol STLivenessDetectorDelegate <NSObject>

@required

/// 活体检测成功
/// @param resultImage 检测过程中最佳结果图
/// @param resultCroppedImage 最佳结果人脸裁剪图
/// @param motionImages 检测过程中最佳动作图
/// @param motionCroppedImages 每个最佳动作图人脸裁剪图
- (void)livenessDidSuccessWithResultImage: (nonnull STLivenessResultImage *)resultImage
                    andResultCroppedImage: (nonnull STLivenessResultImage *)resultCroppedImage
                          andMotionImages: (nullable NSArray<STLivenessResultImage *> *)motionImages
                   andMotionCroppedImages: (nullable NSArray<STLivenessResultImage *> *)motionCroppedImages;

/// 活体检测失败
/// @param error 失败原因
- (void)livenessDidFailWithError: (NSError *)error __deprecated_msg("Use -livenessDidFailWithError:andResultImage:andResultCropedImage:andMotionImages:andMotionCroppedImages: instead.");

@optional

/// 活体检测失败
/// @param error 失败原因
/// @param resultImage 检测过程中的最佳图片
/// @param resultCroppedImage 检测过程中最佳图片人脸裁剪图
/// @param motionImages 检测过程动作图
/// @param motionCroppedImages 检测过程动作图人脸裁剪图
- (void)livenessDidFailWithError: (NSError *)error
                  andResultImage: (nullable STLivenessResultImage *)resultImage
            andResultCropedImage: (nullable STLivenessResultImage *)resultCroppedImage
                 andMotionImages: (nullable NSArray<STLivenessResultImage *> *)motionImages
          andMotionCroppedImages: (nullable NSArray<STLivenessResultImage *> *)motionCroppedImages;

/// 开始检测动作
/// @param motionType 开始检测的动作类型
/// @param index 开始检测的动作 index
- (void)livenessDidStartDetectMotionType: (STLivenessMotionType)motionType
                                andIndex: (NSInteger)index;

/// 检测过程中的状态返回
/// @param status 检测状态
- (void)livenessUpdateStatus: (STLivenessDetectorFaceStatus)status;

/// 检测过程中的实时人脸位置
/// @param faceRect 人脸位置框
- (void)livenessUpdateFaceRect: (CGRect)faceRect;

/// 检测过程中的人脸数量
/// @param faceCount 数量
- (void)livenessUpdateFaceCount: (int)faceCount;

/// 准备阶段人脸位置校准
/// @param positionCalibration 校准方向
- (void)livenessUpdateFacePositionCalibrate: (STLivenessFacePositionCalibration)positionCalibration;

/// 炫彩活体开始
- (void)colorLivenessStart;

/// 炫彩活体结束
- (void)colorLivenessEnd;

@end

NS_ASSUME_NONNULL_END

#endif /* STLivenessDetectorDelegate_h */
