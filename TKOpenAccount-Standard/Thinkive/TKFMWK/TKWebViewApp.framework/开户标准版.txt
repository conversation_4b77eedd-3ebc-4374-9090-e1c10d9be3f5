1.V1.0.0初始化版本
2.V1.0.1
  1.解决webview中a标签的BUG
  2.增加视频播放的插件
  3.增加获取设备软件信息的插件，插件配置文件跟着进行了修改
  4.支持测速的配置文件的修改
  5.扩展自定义键盘类型，新增顺序加强版键盘类型
  6.修改TKBaseWebViewController，扩展属性isH5LoadFinish，代表H5是否加载完成，增加方法h5LoadFinish
    webView加载完成后自动触发。
  7.优化自定义键盘和系统键盘的混合应用
  8.修改视频播放控件在8.0系统的旋转bug
  9.解决URL编码中#号的问题。
  10.增加打开原生webview的插件
  11.增加关闭原生模块的插件
  12.修改调用插件的协议
  13.优化打开原生webbiew插件
  14.解决调用插件跨域BUG
3.V1.0.2
  1.解决了打开pdf插件中webview不能自动缩放的问题
  2.增加了自定义的TKTabBarViewController的切换代理触发，目前触发的方法是
    -(void)tabBarController:(UITabBarController *)tabBarController didSelectViewController:(UIViewController *)viewController;
  3.优化数字自定义键盘
  4.完善分割栏样式，支持圆角设置
  5.优化app启动的流程，异步加载H5到安全沙箱document里面
  6.优化自定义键盘，修复自定义键盘的bug
  7.修复TKAppEngine关于模块跳转的bug
  8.自定义webview的UserAgent
  9.修复TKTabBarViewController的代理执行bug
  10.修复TKBaseWebViewController关于界面显示带参数的问题
4.V1.0.3
  1.修复TKWebViewHelper获取webview的加载路径的bug
  2.修复基础插件中currentViewCtrl的bug
  3.修复修复TKAppEngine关于模块跳转中弹出界面的bug
  4.扩展网络站点测速模块
  5.修改状态栏的颜色，支持图片(导航栏和webview)
  6.滑动翻页控制器TKGesturePagerController的优化。
  7.修复下载组件的功能
  8.扩展导航图组件
  10.解决键盘每次弹出需要随机的问题
  11.解决滑动返回的BUG
  12.扩展TKTabBarViewController添加TKTabBarViewControllerDelegate代理
  13.优化TKBaseViewController监听通知的逻辑
5.V1.0.4
  1.完善通信底层包，支持行情等长连接机制
  2.完善TKBaseWebViewController，支持修改控制器的名称的时候，同时修改webview的名称
  3.完善TKBaseWebViewController，根据tabbar自动改变高度自适应
  4.修复通信包关于行情V3协议的bug，新增期货推送管理类TKQHQuotePushManager，用法和TKQuotePushManager类似，注意发出的通知名称略有不同.
  5.修改TKAppEngine,默认不展示网通提示，只有网络发生改变才展示。
  6.增加应用切换到后台的毛玻璃效果
  7.修复请求流水号的类型为int（原来是uint）
  8.增加上传图片支持进度的代理
  9.完善滑动翻页组件，支持tab标签的css设置。
  10.增加滚动选择器组件
  11.TKThemeManager增加取CSS配置文件里面属性值得方法
      -(NICSSRuleset *)getCssRulesetByCssKey:(NSString *)cssKey;
6.V1.0.5
  1.完善行情的长短连接模式
  2.优化滑动返回组件TKGestureNavigationController
  3.优化文件查找的方法
  4.修改对字符串trim格式
  5.修改基础插件关于json格式入参的定义，兼容json字符串格式和json对象格式。
  6.修复TKNumberHelper的bug
  7.修复长连接断线重连机制
  8.解决滚动选择器组件的bug
  9.增加滚动广告组件TKBannerView
  10.修改Configration.xml,增加配置项
     <item name="isBlurBackground" value="1" description="是否在切换到后台时候模糊背景(0:否,1:是)" />
  11.TKTabBarViewController增加设置角标的方法，值为空就消失
     - (void) setItemBadge:(NSString *)badge forIndex:(NSInteger )index;
  12.增加自动识别二维码图片插件（一般用于长按图片触发）
  13.集成UINavigationController+FDFullscreenPopGesture，实现仿照系统的丝滑滑动返回
7.V1.0.6
  1.修复TKBaseViewController的内存警告bug
  2.修复TKLayerView的计算高度的BUG
  3.增加网络提示的类型，支持通知栏显示和下部tip显示两种模式
  <item name="showNetChangeMode" value="0" description="显示网络连接提醒的模式(0:底部提示,1:通知栏提示)" />
  4.修复日期选择组件关于宽度自适应的BUG
  5.完善读取配置文件的值问题，兼容使用者不小心中间加入空格
  6.发现集成UINavigationController+FDFullscreenPopGesture在源代码开发界面的使用场景中，针对隐藏导航栏然后在push子控制器这种场景，不太适用，会有导航栏延迟隐藏的问题，目前可以暂时使用在不隐藏导航栏的业务场景上。TKGesturePagerController尚未发现以上问题，但性能目前还有优化空间。
  7.对日期控件TKDatePicker和TKDataPicker控件增加样式支持
    TKDatePicker{
       -ios-bar-title-color:#FFFFFF;  //按钮文字颜色
       -ios-bar-tint-color:#FFFFFF;   //按钮背景颜色
       background-color:#FFFFFF; //控件背景颜色
    }
    TKDataPicker{
       -ios-bar-title-color:#FFFFFF;  //按钮文字颜色
       -ios-bar-tint-color:#FFFFFF;   //按钮背景颜色
       background-color:#FFFFFF; //控件背景颜色
       color:#FFFFFF; //控件文字颜色
    }
  8.扩展完善TKBaseWebViewController,增强支持页面的跳转
    /**
     *
     *  是否需要进行模块初始化检测的备用条件,这个和isUseSSO配合使用,在isUseSSO是Yes，并且isNeedReInitJSModule也是Yes的情况下，才会主动调用一次js的50113功能号，进行模块初始化检测,默认是YES
     */
    @property(nonatomic,assign)BOOL isNeedReInitJSModule;
  9.修复50115插件的BUG
  10.支持H5目录的修改，saveName代表H5的目录，可以修改，单必须和包里面的H5路径保存一直。
     <catalog name="update" description="版本管理配置">
        <item name="saveName" value="www.zip" description="下载H5压缩包名称" />
  11.修复自定义键盘，兼容ios9
  12.修复Get请求传参bug
8.V1.0.7
  1.增加webview缓存池,进行webview的缓存优化
    <catalog name="webViewPool" description="webView链接池">
       <item name="isUse" value="是否启用" description="是否开启(0:否,1:是)" />
       <item name="poolInitSize" value="3" description="池子初始大小" />
       <item name="poolMaxSize" value="5" description="池子最多大小" />
    </catalog>
  2.修改50201接口，增加入参tip，代表下载更新提示语。
  3.RequestVo增加设置请求超时时间的属性，默认30秒
     @property(nonatomic,assign)NSInteger timeOut;
  4.下拉刷新组件增加自动刷新的方法。- (void)autoPullDown;
     执行完成后通过 (void)stopLoadWithState:(LoadState)state;方法结束
  5.状态通知栏支持颜色换肤功能。
    .JDStatusBarStyle{
       background-color:#00FF00; //背景色
       color:#FFFFFF; //文字颜色
    }
  6.集成了MJRefresh组件，实现滑动刷新，这个为新组件，滑动刷新的另一种实现，具体用法可以百度查看，比较简单。
  7.修改了50240打开pdf的插件，支持word，ppt等其他的附件格式。
  8.修复webview缓存池的BUG
  9.修复二维码扫描组件在iPhone6上的样式问题。
  10.修复TKAppEngine模块跳转的BUG
  11.ReqParamVo对象增加缓存相关的设置，我们的服务器请求支持前端的缓存，可以进行内存或者本地文件的缓存，另外可以设置缓存的时间，单位秒
     /**
      *  缓存时间，单位是秒
      */
     @property(nonatomic,assign)NSInteger cacheTime;
     /**
      *  缓存类型
      */
     @property(nonatomic,assign)CacheType cacheType;
     /**
      *  是否缓存
      */
     @property(nonatomic,assign)BOOL isCache;
   12.增加TKCacheManager的缓存管理器对象，里面进行缓存对象的管理，支持设置缓存对象的时间，支持内存缓存，文件缓存和数据库缓存等，以后不建议用TKCacheHelper和TKSystemHelper里面关于操作文件缓存和内存的缓存方法，原来的方法不支持设置缓存时间。新增的这个对象可以统一管理所有类型的缓存。内核基于TKCacheHelper和TKSystemHelper的缓存方法做了扩展支持。
   13.修复广告栏组件滚动卡顿的BUG
   14.修复webview缓存的jscallback对象的获取bug
   15.TKGesturePagerDelegate代理对象增加重新计算各个TabItemWidth的代理方法，可以实现滑动翻页组件tabitem的宽度不一样的效果。
     /**
      *
      *  重新计算各个TabItemWidth
      *
      *  @param viewPager 翻页组件
      *  @param index 索引
      *
      *  @return
      */
      - (CGFloat)viewPagerTabItemWidth:(TKGesturePagerController *)viewPager forIndex:(NSInteger)index;
    16.解决键盘组件空格的处理问题
    17.扩展ReqParamVo对象，支持自定义的通信dao
      /**
       *  自定义通信dao名称
       */
     @property(nonatomic,copy)NSString* daoName;
    18.增加更新下载相关提示语配置
       <catalog name="update" description="版本管理配置">
          <item name="isReboot" value="0" description="版本更新后是否关闭应用(0:否,1:是)" />
          <item name="version" value="V1.1.1" description="版本名称" />
          <item name="versionSn" value="0" description="版本内部序号" />
          <item name="saveName" value="www.zip" description="下载H5压缩包名称" />
          <item name="password" value="123" description="解压H5压缩包得密码" />
          <item name="isEncrypt" value="0" description="H5下载压缩包是有密码(0:否,1:是)" />
          <item name="checkUrl" value="http://*************:443/servlet/trade/json?funcNo=901916&channel=2&soft_no=com.thinkive.investHljj&versionsn=8" description="版本自动检测的服务器地址" />
          <item name="updateTip" value="" description="更新过程提示语" />
          <item name="errorTip" value="" description="更新错误提示语" />
          <item name="successTip" value="" description="更新成功提示语" />
        </catalog>
    19.解决H5下载完成后，不自动刷新的BUG
    20.优化完善手势密码插件
    21.TKTabBarViewController增加属性isHiddenSegLine用来控制是否隐藏顶部分割线
9.V1.0.8
    1.修复视频播放组件的BUG
    2.控制基础控制器的屏幕旋转，基础控制器目前都禁止了屏幕旋转功能。
    3.扩展TKBaseWebViewController，支持在导航栏控制器下面的自动布局
    4.扩展插件50261，支持验证手势密码默认不显示的功能
    5.扩展TKCommonService,支持IOS调用JS的批量群调用
    6.修复选择通讯录组件的兼容性BUG

10.V2.0.0
    1.初始化版本
    2.ZipArchive.mm,TBXML+*.m,Async*.m,SimplePing.m,ASI*.m,OpenUDID.m,KeychainItemWrapper.m
    上述相关文件为非ARC模式，需要在编译属性上面添加-fno-objc-arc的标示
    3.扩展TKBaseWebViewController支持title显示在导航栏头，并支持返回
    4.扩展TKAppEngine方法里面的页面跳转逻辑，支持在弹出层上继续弹层，原来是先关掉自己的弹出层，再弹出别的控制器。
    5.修复启动引导图组件分页的标示BUG
    6.修复选择通讯录组件的兼容性BUG
    7.优化滑动翻页效果
    8.优化缓存命中逻辑
    9.修复TKBaseWebViewController初始化调用50113的BUG
    10.增加H5增量更新的支持标志，采用增量更新模式兼容原来的全量更新，但是用增量更新模式执行全量的话，效率稍微低下，后续服务器全部升级为增量更新模式，就不会有啥问题了
       <catalog name="update" description="版本管理配置">
           <item name="mode" value="0" description="更新模式0:全量更新,1:增量更新" />
    11.增加网络缓存命中的ResultVo的属性标志
       /**
        *  <AUTHOR> 2016-12-31 10:12:26
        *
        *  是否缓存命中的数据
        */
       @property (nonatomic,assign)BOOL isCacheData;
    12.优化行情长连接逻辑
    13.增加请求头Accept-Encoding配置gzip,deflate,sdch
    14.增加配置项
       <catalog name="system" description="系统配置">
         <item name="isShowFMWKVersion" value="1" description="是否长按显示框架版本号(0:否,1:是)默认是1" />
    15.增加配置项
       <catalog name="system" description="系统配置">
          <item name="isRequestURLEncode" value="1" description="是否对请求入参进行URL编码(0:否,1:是)默认是1" />
    16.增加插件50118代理发送http/https相关的网络请求
    17.修复手势密码显示返回图片的问题
    18.修复TKBaseWebViewController里面对50114插件中NOTE_CLOSE_MODULE关闭通知的处理BUG
       注意：我们TKBaseWebViewController里面已经加了对NOTE_CLOSE_MODULE的监听，子类不需要重复添加了，原生TKBaseViewController这个控制器是没有监听实现的，子类要自己扩展监听。
    19.解决了提示框，菊花，tip等被键盘遮挡的问题
    20.加了判断解决ASI在ios9.3系统有些证书不能发送HTTPS请求的问题
    21.修改了升级覆盖安装引起的主题没有修改的逻辑
    22.增加了请求签名加密相关配置
       <catalog name="system" description="系统配置">
            <item name="isRequestURLSign" value="0" description="是否对请求入参进行签名(0:否,1:是)默认是0" />
            <item name="requestSignKey" value="nPeaYkREFgwhlz6/UN582NYDd3ySKKTSM4jTiMrZtmYeT9CVhloh0e0kmq2PagBU" description="请求签名的Key" />
            <item name="requestSignAppId" value="00000001" description="请求签名的APPId" />
            <item name="isRequestURLEncrypt" value="0" description="是否对请求入参进行加密(0:否,1:是)默认是0" />
            <item name="requestEncryptMode" value="des" description="加密的类型" />
    23.修改了获取IP卡UI的逻辑
    24.修改了测试站点的策略
        <servers speedUrl="/speed.jsp" updateUrl="">
           speedUrl为测试的具体地址，updateUrl是更新配置的地址
    25.增加了招商的键盘样式
    26.修复了测速站点的逻辑漏洞，导致一个站点的时候耗时一秒
    27.集成了日志系统
    28.增加了行情站点测速的功能配置，updateUrl
    29.升级为AFNetworking替换ASIHttpRequest，修改更新下载还有上传相关代码
    30.增加了https的本地证书校验
      <catalog name="ssl" description="ssl证书配置">
          <item name="zh.sczq.com.cn" value="1|app.sczq.com.cn.cer" description="格式为：是否校验证书(0:不校验,1:校验)|证书地址|证书密码" />
          <item name="************" value="0" description="格式为：是否校验证书(0:不校验,1:校验)|证书地址|证书密码" />
      </catalog>
    31.增加更新的相关配置项目，删除冗余的配置项目
      <catalog name="update" description="版本管理配置">
         <item name="mode" value="1" description="更新模式0:全量更新,1:增量更新" />
         <item name="saveName" value="www.zip" description="下载H5压缩包名称" />
         <item name="password" value="" description="解压H5压缩包得密码" />
         <item name="isEncrypt" value="0" description="H5下载压缩包是有密码(0:否,1:是,2:是加密，同时配置的password字段是加密后的密码)" />
         <item name="checkUrl" value="https://************/mappuat/system/queryVersion?versionsn=1&channel=2&soft_no=com.logansoft.zcbao" description="版本自动检测的服务器地址" />
         <item name="updateTip" value="" description="更新过程提示语" />
         <item name="errorTip" value="" description="更新错误提示语" />
         <item name="successTip" value="" description="更新成功提示语" />
         <item name="isShowUpdateTip" value="1" description="是否显示更新相关弹层进度提示" />
         <item name="isReloadWebView" value="1" description="是否更新完成后进行webview的刷新动作" />
      </catalog>
   32.解决弹层的一些插件，导致关闭弹层时候触发50113的接口问题，比如上传图片，扫描二维码等等。
   33.增加滑动翻页组件支持设置分割标示的高度，和标示的位置等
   34.解决行情未连接服务器的时候卡死的问题。
   35.解决弹出框自适应和标题不换行的问题
   36.系统存储支持加密存储模式
   37.增加支持三方H5返回的功能
   38.解决钥匙链存储导致崩溃的问题
   39.TKBaseWebViewController 增加属性解决webView不需缓存的需求
     /**
      *  是否使用webView缓冲池机制。在系统开启缓冲池策略的时候，这个属性才有作用
      */
     @property(nonatomic,assign)BOOL isUseWebViewCachePool;
   40.TKBaseWebViewController 增加属性解决webView识别图片二维码的需求
     /**
      *  是否支持图片识别
      */
     @property (nonatomic,assign)BOOL isSupportReadQRCodeImage;
   41.解决弹层webview引起的电话号码问题
   42.完善滑动翻页组件的滚动机制
   43.集成思迪统计的SDK
   44.解决招商键盘按下提示背景的bug
   45.解决导航栏组件Navbar在ios10下面状态栏背景色不兼容的问题。
   46.解决了三方包的问题，集成其他厂商用的我们原来的包，要上层自己引入，平台不再管理了
   47.解决了数据库加密的问题，需要在编译配置里面加other c的选项为-DSQLITE_HAS_CODEC

   2016-09-21:更新内容
   48.支持多机房测速方案，要更新Server.xml和对应服务器
   49.解决左右抽屉组件关闭的BUG，如果本身关闭，他原来的逻辑是自动打开的。
   50.解决和第三方集成的时候NSNRLCache冲突的问题

   2016-10-19:更新内容
   1.解决行情服务器通信层因为并发异步读写缓存区导致的偶然崩溃的问题。
   2.增加一创期货交易键盘
   3.解决webview调用原生键盘插件导致返回时候崩溃的问题
   4.扩展TKGesturePagerController滑动翻页类，增加属性tabsViewFrame可以设置滑动翻页组件顶部导航菜单的位置。

   2016-10-28:更新内容
   1.解决TKBaseWebViewContrller改变名字导致没有重新注册webview，从而引起不能回调h5的方法的BUG

   2016-11-07:更新内容
   1、修改50270,50271,50273接口，增加入参statusColor，实现修改状态栏，导航栏的颜色

   2016-11-10:更新内容
   1、解决TKBaseWebViewCtroller里面调用service.iosCallJs的方法导致控制器释放不了的BUG

   2016-11-23:更新内容
   1、优化了多机房测速的逻辑
   2、完善网络通信包，支持天风新版协议，优化长连接的断线重连机制和心跳检测机制
   3、增加获取通讯录的插件
   4、替换友盟新版的库
   5、增强代理网络请求插件的功能，支持新版本http协议和socket协议
   6、解决网络通信层关于AFNetworking的部分逻辑缺陷处理。
   7、解决滑动翻页组件的重新加载不释放顶部View的BUG

   2016-11-29:更新内容
   1.解决滑动翻页组件重新加载显示上次分割线的BUG
   2.解决socket长连接关于包体长度为0时候不执行回调的BUG
   3.解决数字键盘，股票键盘等自定义键盘键盘按下时候背景色发生改变引起的安全漏洞
   4.扩展UITableView的nimbus的css支持
   5.支持本地校验的ssl证书放到安全沙箱里面，方便进行证书的更新操作

   2016-12-10：更新内容
   1.解决Toast提示的阴影问题
   2.解决隐藏Tabbar插件的BUG，原来的版本会导致H5预加载的时候影响其他模块的Tabbar的展示状态
   3.修改行为统计的逻辑，app的使用周期为APP进入前台到APP切入后台直接的时间

   2016-12-13：更新内容
   1.解决网络通信层由于过度释放导致的偶然崩溃的问题

   2016-12-27：更新内容
   1.解决H5在线更新以后丢失参数的问题
   2.解决Webview加载url地址对https的请求不拦截校验的安全问题
   3.发送短信的接口支持传空的手机号和内容

   2017-01-13：更新内容
   1.优化招商版本的键盘
   2.处理公司长连接超时的问题
   3.增加模块间通信的消息引擎，增加Module.xml配置文件
   4.增加图像验证码组件
   5.修改Configuration.xml 增加配置项目，支持模块配置和放篡改配置
     <item name="modulePath" value="Module.xml" description="系统模块配置文件地址,多个用|分割" />
     <catalog name="h5Filter" description="H5资源的请求加载拦截配置">
        <item name="isUseURLCache" value="0" description="是否启动所有URL请求资源的缓存机制(0:否,1:是)默认是0" />
        <item name="urlFilterEncryptKey" value="miNjYDthPMOsKnXWNaWBvpojY2A7YTzDrCp11jWlgb4eT9CVhloh0e0kmq2PagBU" description="URL请求资源放篡改的加密Key" />
      </catalog>
   6.增加对统计SDK的插件调用

   2017-02-19：更新内容
   1.解决服务器C版本返回JSON的数据里面含有特殊字符导致解析失败的问题
   2.解决发送短信的插件手机号穿空显示彩信的问题

   2017-02-27：更新内容
   1.更新了C版本统一接入服务器的包头协议，解决华西项目偶然遇到的在移动网络下面收不到服务器包的问题
   2.增加上传插件和识别二维码的插件对相册以及摄像头的权限判断
   3.打开webview的插件，PDF的插件等增加了对头部文字颜色设置的入参titleColor
   4.打开webview的插件支持返回和关闭两个操作菜单的设置
   5.解决TKFormatHelper导致的验证邮箱正则过慢的问题
   6.打开PDF插件增加了阅读时间的设置
   7.发送短信的插件支持批量发送的设置

   2017-03-23：更新内容
   1.扩展打开webview的插件支持分享功能，完善返回按钮的逻辑。
   2.支持原生调用H5通过uuid来识别，此处涉及到H5的修改，修改后H5调用原生的插件，回调H5的时候不用太关注moduleName了
   3.解决TKBaseWebViewController打开页面时候无网络的情况，访问about:blank空白页面导致的缩放bug
   4.还原键盘，支持键盘点击的时候效果
   5.解决TKGestureNavigationController导航控制器截屏时候，由于界面未渲染完导致的崩溃问题
   6.完善统计SDK对崩溃日志的崩溃堆栈的收集功能。
   7.解决控制器在显示过程中改名字导致TKRootViewController获取当前控制器为空的逻辑BUG（记住我们的外层大控制器要起名字，name这个东东要设置）
   8.优化隐藏tabbar插件的逻辑
   9：优化长连接的异常处理容错
  10：支持Webview要求实现仿照微信的滑动返回的效果
  11：解决行情关于中文搜索的BUG问题
  12：完善相册，拍照，通讯录等相关插件的权限判断逻辑

  2017-03-25：更新内容
  1.优化测速逻辑
  2.优化部分框架逻辑
  3.去掉基础控制器和tklog的tag属性改成tktag
  4.解决更新密码错误导致解压失败导致的解压文件都是空文件的BUG

  2017-03-29：更新内容
  1.修改控制器isShow的属性
  2.优化关于控制器是否弹层的判断
  3.优化关于控制器是否在导航控制器的判断
  4.优化关于控制器是否在tabbar控制器的判断
  5.更新版本的功能和更新服务器列表的功能支持socket的接口模式
     http/https格式: http://地址:端口/servlet/json?key=value&key=value
     socket格式：    socket://busconfig.xml中的serverID?companyId=THINKIVE&systemId=MALL&key=value
     server.xml格式：server://server.xml中的serverID?key=value&key=value
  6.优化手势密码
  7.新增保存通信录的插件50228

  2017-04-21：更新内容
  1.新增天风键盘
  2.键盘是否防止截屏可以配置
    <catalog name="system" description="系统配置">
      <item name="keyboradForNoCutScreen" value="1" description="原生键盘是否支持防止截屏(0:不处理，1：处理)，默认0，处理的时候键盘会去掉点击背景色" />
  3.行情协议支持证书双向认证模式
  3.增加webview网络不好时候重试的功能
  4.内存和缓存获取值的插件支持加密返回，以前默认是返回明文，现在支持返回密文
  5.测速策略的优化，排除已经挂掉的服务器
  6.打开PDF插件优化增加加载失败的提示语
  7.解决bitcode打包问题 Other C Flags 添加-fembed-bitcode 
  8.解决TKGestureNavigationController的滑动返回的截图BUG

  2017-04-25：更新内容
  1：优化天风键盘的点击后的效果
  2：解决三方集成公司两份SDK的插件功能号冲突的问题
  3：完善引导页，支持底部pagecontrl的扩展控制定义
  4：TKAsset包里面的图片重新命名，并且删除了无用的图片
  5：socket增加错误号的翻译工作
    -900  请求功能号不能为空
    -990 服务器连接认证失败
    -991 服务器获取连接异常
    -992 服务器建立连接中断
    -993 服务器建立连接超时
    -994 服务器建立连接拒绝
    -995 服务器建立连接网络异常
    -996 客户端数据请求超时
    -999  服务器返回的数据格式错误

    行情 0：正常
    -1：找不到股票代码
    -2：包体长度有误
    -100：服务器正在初始化
    -101：行情服务器返回数据包与客户端配置文件不匹配
  6：webview重新加载的时候清除H5加载完成的标示
  7：优化内存缓存的处理，支持设置内存阀值
  8：解决自定义键盘输入框在tableCell里面内存过大的问题。
  9：优化滑动返回的逻辑

  2017-05-05：更新内容
    1：上传图片的插件支持扩展上传身份证的模板
    2：Http返回的ResultVo对象里面增加了熟悉用于获取响应头和coockie
       /**
        *  Http的响应的头
        */
        @property (nonatomic,retain)NSDictionary *respHeaderFiledDic;

       /**
        *  Http的返回coockie数组
        */
        @property (nonatomic,retain)NSArray *coockies;
    3：框架兼容2.0的H5版本，支持webview缓冲池的策略，在2.0的版本上也可以识别调用50113
    4: 支持调试日志上传服务器的功能
    5: 解决TKBaseWebViewController控制器无网络时候加载默认图片的BUG，控制器隐藏的时候图片会消失掉的问题
    6: 优化GIF图片组件的逻辑
    7：解决CSS样式编写规范的容错机制
    8：支持日志调试功能
    9: TKRootViewController扩展注册模块的功能。

    2017-05-15：更新内容
    1：修复上个版本导致的CSS样式的问题，针对:selected类似情况
    2：解决拍照上传插件的Bug，某些图片崩溃+上次图片方向问题
    3：完善调试日志的一个逻辑，屏蔽了部分垃圾打印日志
    4：容错TKStringHelper对其他类型入参的处理
    5：解决行情socket协议的errorNo的处理BUG
    6：扩展miniui的Navbar支持，需要大家更新三方库的目录
    7：支持TKTextField切换系统键盘，设置tkKeyBoardType = TKKeyBoardTypeNone变成系统，其他类型为自定义的。
    8：扩展插件50010，支持返回原生版本号和版本序号
    9：完善webview的滑动返回的效果
    10：扩展TKBaseWebViewController的功能支持对父控制器为导航栏控制器的标题颜色和背景色等的设置

    2017-05-18：更新内容
    1：TKAppEngine扩展API支持判断是否是第一次安装
        /**
        *  <AUTHOR> 2017-05-17 16:05:54
        *
        *  是否APP第一次安装
        *
        *  @return
        */
    -(BOOL)isAppFirstInstall;
    2：支持更新的三种策略（信达项目）
        第一个维度：更新模式，0：非强制更新，1：强制更新，2：静默更新
        第二个维度：网络环境，wifi，蜂窝网络等
        第三个维度：灰度升级：按照设备号，ip地址，手机号，下载时间等限制下载
    3：BusConfig.xml,Server.xml支持重命名的配置新特性，优化多次测速的问题。
    4：解决引导页组件关于读取图片变形的问题
    5：完善webview管理器里面对名称的管理机制
    6：更新完善加解密的帮助类，针对TKRSAHelper的API的完善和注释的完善
    7：修复上个版本切换系统键盘导致的初始化默认键盘变化的问题。

    2017-05-25：更新内容
    1：优化H5更新下载逻辑，关于初始化复制www包的时候同时更新性能问题。
    2：对TKNetHelper和Dock类的关于华安崩溃问题的新增逻辑判断，尚未知崩溃实际原因。
    3：对思迪统计系统关于崩溃日志的统计信息的完善，记录了DSYM的UUID和CPUType，可以用DSYM工具进行分析。
    4：对TKDownload相关类的文件名称进行整理，解决内容和文件名称不一样的问题。
    5: 解决控制器注册监听通知重复注册的问题。
    6: 支持自定义Configration.xml和Plugin.Xml文件切换。
    7：关于类似[[TKThemeManager shareInstance]getCssRulesetByCssKey:@"aaaaa"].xxxxxxxxx 加载Css未配置的属性崩溃的问题，因为minibus加了断言判断，没有属性提取的动作是不被允许的。
    解决方案：使用前先判断即可。例如：if ([ruleSet hasTextColor]) { self.textColor = ruleSet.textColor; }
    8：关于加载Css内存管理问题，只要是继承了TKBaseViewController的会自动在控制器释放的时候也释放view包含子View的样式引用
    如果不是，就要再需要释放自己时候，自己调用View的removeCssAndSubViewsCss这个方法即可，这个会递归删除当前包含子View
    的样式引用,如果调动View的removeCss方法只会删除自己的样式引用，不包含子View。
    9:解决老版本的股票键盘切换英文键盘后切换不回来的BUG
    10:解决上个版本升级后，导致获取TKImageHelper获取图片的方法不兼容ios7的问题

    2017-06-01：更新内容
    1：解决H5调用更新插件功能号funcNo错误的问题
    2：修改新版本的推送逻辑，支持推送功能的配置文件描述
    3: 修改了插件50001。（此功能已经集成了50010-50031范围的插件功能，建议使用50001）
    4：解决子线程调用Service进行Socket网络通信无回调的问题
    5：打开webview插件返回按钮的颜色支持自定义，和titleColor保持一致。
    6：解决TKBaseWebViewController第一次打开触发50113的问题
    7: 解决调用50118插件由于模块名覆盖导致的不能回调的问题
    8：解决统一接入长连接的客户端私钥证书支持的格式问题，目前支持p12和pem两种格式。后面大家就不用找我转证书格式了。直接用鑫文发的就好了。
    9：解决H5静默更新刷新当前页面的问题。
    10：增加了HttpToBusAuto的协议，用来转换http请求到我们的统一接入后面
    11：解决TKGesturePagerController切换当前页面的时候userinfo没有携带的问题

    2017-06-10：更新内容
    1:完善HttpToBusAuto的协议，框架会根据头部传入的域名自动维护会话
    2：解决大数据版本统计SDK关于visitPage的逻辑问题
    3: 解决行情长连接问题(切入后台断线重连的时候导致的数据解析为空的问题)。
    4：完善了50118插件的协议定义，增加了通用Socket长连接协议定义7，用来代替1到5的其他长连接定义。
    5：完善了长连接测速回调的功能
    6：TKNetHelper增加获取域名，是否ipV6等功能API

    2017-06-22：更新内容
    1：解决自定义无序的键盘在ios8上面的兼容BUG
    2：完善TKBaseWebViewController对是否采用webview缓存池的机制的判断。
    3：兼容某些服务器接口没有返回标准格式JSON串结果集的问题
    4：完善TKDatePicker和TKDataPicker展示体验的问题。
    5：完善支持华安关于TabBarController里面的TKBaseWebViewController控制器对H5初始化的调用(预加载过后，第一次进去也会触发50113)

    2017-07-01：更新内容
    1：消息引擎的修改，模块需要设置代理服务，消息引擎不再对打开模块等进行每次实例化的操作。
    2：灰度升级策略的完善，增加设备的相关入参属性
    3: 解决版本升级的BUG（覆盖安装的时候，马上提示升级H5，这种场景的话，会导致第一次升级以后修改的H5是生效的，但是杀死APP以后，再启动H5又被还原了,如果是覆盖安装以后，中间杀死过app然后再提示升级的H5就不会有这个BUG)
    4：完善调用插件返回的结果集的处理，防止写插件的时候直接把HTTP请求返回的ResultVo当成插件返回的ResultVo，这种情况下会出现问题，导致崩溃。
    5：解决调用插件对系统导航栏修改颜色的时候，导航栏颜色有模糊的效果，这种通过设置self.navigationController.navigationBar.translucent = NO;来解决
    6：解决苹果审核拒绝私有API的问题
    7：完善测速的逻辑，加入机房缓存的机制。
    8: 解决框架TKImageHelper的截图崩溃BUG

    2017-07-26：更新内容
    1:完善Socket长连接的测速逻辑
    2：解决Socket多个长连接时候，一个长连接中断清除掉了所有长连接回调的BUG
    3: 解决WebView播放mp4等流媒体文件时候的BUG
    4：优化配置文件copy到沙箱时候进行内容加密存储，保证安全性
    5：优化了网络通信层的日志输出，更清晰的打印每个请求流水的整个流程

    2017-08-03：更新内容
    1: 优化了框架的启动逻辑
    2：解决TKRootViewController中关于当前控制器的获取逻辑
    3：优化了隐藏Tabbar的插件逻辑
    4：解决TKTextField用系统键盘时候，关于limitLength设置不生效的问题
    5: 完善H5放篡改和在线更新策略
    6：完善50115插件，自动识别支持Push和present

    2017-08-19：更新内容
    1:Socket测速机制的完善，支持域名和IP以及站点描述，测速支持域名优先策略
    2:统计SDK的相关bug修复
    3:支持防篡改，H5在线升级等机制。
    4:增加重复请求过滤拦截的回调
    5:支持请求入参为数组或者数据字典类型的容错机制。（进行格式序列化为字符串）
    6:解决多机房地址更新后的站点选择的问题。

    2017-08-30：更新内容
    1：解决打印日志内存泄漏的问题。生产环境还是建议关闭日志
    2：解决原生回调H5引起的入参里面为多维结构的问题。（上次升级导致了value变成了字符串结构，以前是对象结构）
    3：解决多机房配置更新服务器不支持post请求的问题。
    4：解决随机自定义键盘切换不改变的问题
    5：解决TKNetHelper关于域名解析API在无网络情况下崩溃的问题。

    2017-09-3：更新内容
    1:解决打开pdf插件和打开webview插件中执行Push控制器时候重复隐藏Tabbar的问题。
    2：解决重用webview缓存池时候，由于H5的某些样式导致WebView重新加载到控制器View时候产生的崩溃问题。(以前是通过关闭webview缓冲池来解决的，现在不用了)
    3：H5Filter.xml新增配置项目,用于优化第一次安装App需要copy包里面的H5到Document下面的问题。现在不需要了，并且完美支持H5增量升级和在线升级。
    <item name="isLoadPackageH5" value="0" description="是否默认加载安装包里面的H5(0:否,1:是)默认是0"></item>

    2017-09-10：更新内容
    1：解决TKBaseWebViewController网络异常显示图片添加的返回按钮和webview控制器导航栏的返回按钮重复的问题。
    2：解决Socket长连接证书双向认证时候对服务器下发证书解密的问题。某些情况下，服务器下发证书的加密串最后128位是000000
    3：增加TKBaseWebViewController，支持加载过渡页功能isShowLoading,默认是NO
    新增配置文件项目：
    <catalog name="webViewPool" description="webView链接池">
    <item name="isShowLoading" value="0" description="是否显示WebView加载过渡效果(0:否,1:是)默认是0" />
    4: 特别声明，在试用本次升级框架版本之后，由于业务需要继承TKBaseWebViewController的子类，实现WebView代理方法的时候，记得要调用super，此次更新解决以前继承的TKBaseWebViewController子类的webView代理有时候不执行的问题。
    5：新增三板股票键盘类型
    6：优化50108隐藏Tabbar插件的处理逻辑，防止多次调用出现的frame不对的问题。
    7：解决TKNumberHelper对多位小数点转成科学技术法引起的格式化错误的问题。
    8：解决TKNetHelper获取网络类型再IPhoneX手机不兼容的问题。

    2017-09-18：更新内容
    1：兼容IPhoneX手机对状态栏高度的处理问题
    2：优化华泰期货项目长连接测速选择和断线重连的机制逻辑
    3：ReqParamVo加了一个请求头控制入参：isFilterRepeatRequest，控制是否拦截重复请求。
    4: Service类里面新增-(void)cancelAllRequest 和 -(void)cancelRequest:(NSString *)flowNo的API满足取消请求，然后执行回调函数的需求。以前的-(void)clearAllRequest 和 -(void)clearRequest:(NSString *)flowNo的API是不会执行回调函数的。
    5: 完善TKBaseWebViewController控制器关于50114关闭通知的处理逻辑。
    6：暴漏TKNetworkManager获取当前机房指定站点配置的API -(NSMutableDictionary *)getCurrentRoomServer:(NSString *)serverName;
    7：完善了华泰期货关于TKBaseWebViewController的webview自适应局部大小页面的问题。
    8：完善了TKDeviceHelper对iphone8，X设备的翻译功能。
    9：解决了日期插件的标题显示不对的BUG

    2017-09-26：更新内容
    1:扩展行情长连接支持kfield动态字段功能
    2:修改框架用到的Tabbar的高度，适配iphoneX，新增定义：
    #define STATUSBAR_HEIGHT ([[UIApplication sharedApplication] statusBarFrame].size.height)
    #define TABBAR_HEIGHT (([TKDeviceHelper getDeviceResoluation] == TKUIDeviceResolution_iPhoneX) ? 83 : 49)
    #define NAVBAR_HEIGHT 44
    #define ISIPHONEX ([TKDeviceHelper getDeviceResoluation] == TKUIDeviceResolution_iPhoneX)
    3:修改TKBaseWebViewController，适配iphoneX
    4:修改JDStatusBarNotification，适配iphoneX
    5:修改TKTabBarViewController，适配iphoneX
    6:新增插件50123，实现H5通知原生webview加载失败
    7:解决sqlite加密库重命名冲突问题
    8:解决长连接双向认证时候RSA加解密导致的崩溃问题
    9:Navbar执行背景色的透明度设置
    - (void)tk_setElementsAlpha:(CGFloat)alpha;
    10:TKTabBarViewController解决毛玻璃背景色的问题

    2017-09-30：更新内容
    1：修复 #define TABBAR_HEIGHT (([TKDeviceHelper getDeviceResoluation] == TKUIDeviceResolution_iPhoneX) ? 83 : 49)的问题
    2：优化GIF组件的实现，解决GIF播放帧数周期不固定的问题。
    3：优化了Socket长连接网络虚链路断开客户端无感知的问题。
    4：优化了50119的设置webview状态栏颜色的逻辑

    2017-10-12：更新内容
    1：修复框架invoke跳转模块的崩溃问题
    UIViewController transitionFromViewController:toViewController:duration:options:animations:completion:]
    2：优化TKBaseWebViewController无网络刷新和返回按钮是否显示的逻辑
    3：优化socket长连接手动选择站点，可以自动登录的逻辑。
    4：解决集成大数据统计SDK以后影响APP的table点击的问题
    5：优化TKLayerView的Alert弹框提示的逻辑，原来的提示的内容没用做换行。
    6：优化插件50273，支持拍照时候设置闪光灯的开启模式
    7：优化Navbar组件按钮文字自适应的问题。
    8：解决长连接检测响应超时进行重连的机制，在APP切到后台，再回到前端时候的问题。

    2017-10-21：更新内容
    1：解决自定义键盘切换时候闪动的问题。
    2：解决自定义TKTabBarViewController透明的问题
    3：解决TKBaseWebViewController在IOS11的适配问题。
    4：解决TKLayerView弹层遮罩层的BUG
    5：解决TKHttpDao请求回调子线程操作状态栏网络状态的问题。
    6：站点更新支持更新对应站点名称的功能
    7：TKBaseWebViewController扩展属性tabBarExtendHeight,用来支持H5本身的问题导致调用50108隐藏tabbar时候高度不对的问题。
    8：扩展打开PDF的50240插件支持设置进度条的功能。
    9：完善验证码的展示
    10: 优化TKBaseWebViewController控制器的isSupportReadQRCodeImage属性打开的性能问题。
    11：优化框架根据TKBaseWebViewController控制器的userInfo属性触发50113的逻辑

    2017-10-27：更新内容
    1：完善TKBaseWebViewController网络重连时候reload webview的问题
    2：优化框架初始化的速度
    3：优化模块消息引擎的逻辑，支持配置模块初始化单利对象
    <item name="trade" value="TKTradeController" isProxy="1" description="交易模块" ></item>
    isProxy="1"时候框架启动便会储初始化相应模块代理
    4：解决长连接返回的JSON格式的数据解析失败时候，没有回调给应用失败的问题。
    5：解决长连接返回的JSON格式的数据是混合编码的问题（GBK +UTF-8）
    6: 取消原来的版本去掉TKTabBarViewController删除背景view的逻辑
    7：解决TKBaseWebViewController里面页面重定向的逻辑漏洞，针对H51.0的框架H5模块切换引起的
    8：解决sqlite数据文件损坏的修复问题。
    9：完善消息引擎代理模块可以不用继承控制器，而只用实现模块代理接口的支持。
    10：解决频繁测速socket，引起的回调结果不准确的问题。
    11：优化股票键盘按键的位置

    2017-11-04：更新内容
    1：适配键盘到IphoneX
    2：适配webView控制器到iphoneX
    3：解决行情长连接接收网络请求数据包过大对缓存区的影响，导致乱码问题
    4：完善TKGesturePagerController的换肤支持
    5: 扩展TKAESHelper对CBC加密算法的支持
    6：修复大数据统计对Label的触摸事件的影响

    2017-11-24：更新内容
    1：修复大数据的GPS定位坐标问题,增加高德获取IP服务的授权Key配置
    <catalog name="traffic" description="思迪统计配置">
    <item name="ipKey" value="KfZAnJoqEDtmwhTjxZ+C3JhhR1r9/yRrLlDXduxkb/YeT9CVhloh0e0kmq2PagBU" description="高德获取IP服务授权key(加密后)" />
    2：优化长连接测速逻辑,修复行情推送长连接断线重连可能会导致的性能问题。
    3: 优化TKTabBarViewController，支持根据Dock的背景色修改UITabbar的背景色
    4：优化TKNetworkManager，兼容增量更新模式
    5：重新命名某些框架缓存Key，解决海通证券商城发现的冲突问题。
    6：优化Layer的底部Toast提示信息适配iphoneX
    7：优化通讯录插件，支持排序

    2017-11-28：更新内容
    1：适配键盘到IphoneX
    2: 解决TKGestureNavigationController二级页面返回时候显示tabbar的问题
    3：完善webview控制器在iphoneX的适配（解决弹层控制器，然后返回时候引起安全区改变引起webview的页面高度不对的问题）
    4：解决换肤的效率问题，很多控制器不释放时候进行换肤导致效率较低有卡顿
    5：解决自定义键盘和系统键盘的切换问题
    6：解决大数据初始化获取地理位置的问题。改成启动时候获取
    7：修复TKStringHelper里面替换字符串时候，目标字符串为nil导致崩溃的问题
    8：修复TKFormatHelper关于手机号的校验规则
    9：TKLayerView支持设字体宽度等属性

    2017-12-19：更新内容
    1：解决TKFormatHelper对URL的正则校验BUG
    2：增加大数据sdk的插件，支持H5设置账户值和账户类别
    3：增加Http协议对Post的body内容为json字符串的支持
    4：完善获取通讯录的插件，增加名称为空时候显示公司名称的功能
    5：新增BusConfig的更新模式，支持期货云服务器的地址更新配置格式
    6：解决2017-11-24的版本更新导致的当次H5升级后不立即生效，需要重启生效的问题。
    7：优化TKBaseWebViewController的loadding效果的支持逻辑。
    8：优化50115插件对webview的loading效果的控制参数

    2017-12-27：更新内容
    1：优化自定义导航组件TKGestureNavigationController是否支持滑动返回的控制逻辑
    2：优化本地H5在线升级的逻辑
    3：解决启动socket测速导致的偶然崩溃的BUG
    4：解决框架中关于URLEncode的API平台系统不建议使用的问题。
    5：优化网络通信层对服务器异常数据为空数据的报错的日志打印处理。
    6：解决打开PDF插件50240对URL中包含%这种特殊字符的处理
    7：扩展TKBaseWebViewController，支持点击查看H5图片，放大的功能。
        /**  是否支持图片放大识别*/
        @property (nonatomic,assign)BOOL isSupportReadLargerImage;
        /***  图片放大识别的回调方法,子类可以重写*/
        -(void)processReadedBiggerImage:(UIImage *)image
    8：优化自定义Tabbar中有消息提示的小红点的样式
    9：支持行情对行情Field字段描述的读取

    2018-2-26：更新内容
    1：配置文件支持bundle包任意目录存放
    2：支持H5的根目录自定义名称，默认是www
    3：增加对国密SM相关算法的支持
    4：修复了TKNumberHelper中关于格式化小数，小数点位数为0时候不生效的BUG
    5：解决长连接站点切换时候，由于判断站点是否存活的内存状态，导致服务器恢复，客户端还认为不可以连接的逻辑问题。
    6：支持行情sfileds字段的定义
    7：完善网络请求时候请求参数为空的容错处理。例如：null,<null>,undefined,NSNull等
    8: 完善插件50001支持ios返回设备id，字段为deviceIMEI
    10: 完善键盘支持确定按键的自定义设置，H5调用的插件50210也支持
    11：换肤框架支持图片放到资源包中，支持递归查找图片
    12：完善TKBaseWebviewController判断请求URL是否发生跳转，从而引起H5是否加载完成状态重置的逻辑。
    13: 完善H5在线升级的机制，支持入口页面在本地目录不存在的情况下，也可以在线升级
    14：完善TKRootViewController的currentTabBarController方法逻辑，支持UINavagationController下面的控制器为当前控制器的场景。
    15: 优化TKFileHelper的+(NSString *)getBundleFilePathByName:(NSString *)name;方法，兼容ios7以下的版本
    16：优化TKStringHelper的encodeURL方法，解决ios6兼容问题，以及向下兼容。
    17：优化TKWebViewHelper关于获取加载URL的逻辑，升级此版本后记得验证一下当前版本的在线升级更新H5是否收到影响

    2018-4-11：更新内容
    1:支持期货交易日志功能
    2:支持期货推送返回的数据包增加时间搓的功能timeInterval
    3:支持行情对二维结果集的支持，支持行情入参用|分割的配置
    4:支持框架的版本升级增加手机号的入参，需要上层把需要的手机号设置到update.mobile这个本地文件缓存key中。
    5:去掉了通信层关于虚拟网络中断的检测机制。

    2018-4-20：更新内容
    1：支持框架在X8以后bitcode的支持，以前支持的模式在升级Xcode以后不支持了。
    2：修改手势密码的存储位置为本地缓存。
    3：千人千面容器的支持
    4：框架配置文件中相关的URL支持扩展入参配置，例如内存，缓存入参
    [当前数据源参数]key=${name} [内存缓存参数]key=@{name} [文件缓存参数] key=#{name}

    2018-05-07：更新内容
    1：解决自定义键盘Title的处理BUG，50210打开键盘原来有title的时候，同时设置确定按键，会导致title被遮住
    2：优化通信层缓存区变量的释放
    3: 优化行情推送通信层入参用|分割的配置
    4：解决加载本地不存在的Html页面时候导致的程序崩溃问题
    5：service增加getRequestQueueLength的API用于获取Service当前请求的队列长度。
    6：优化多机房测速里面的随机机房选择模式
    7：优化键盘中文切换的样式

    2018-05-16：更新内容
    1：使用malloc分配内存解决calloc存在的安全问题
    2：优化统计SDK切入后台开启后台任务的问题，改成执行完上传动作就停止后台任务模式。
    3: 解决2018-05-07：更新内容（优化通信层缓存区变量的释放）从而导致崩溃的问题
    4：打开pdf的插件兼容iphoneX的适配

    2018-06-21：更新内容
    1：解决千人千面容器的部分bug
    2：优化AppEngine初始化代理模块的时机，解决代理模块处理请求时候，框架还未初始化完成的BUG
    3：优化行情通信层，支持自定义filed类型
    4：优化默认构建ResultVo的数据加工类，适配服务器非标准数据结构的返回
    5：发布统计SDK V2.1.2版本
    6：解决50500重复调用导致的BUG,参数会被覆盖
    7: 优化版本更新管理器，支持自定义UI
    8: 解决多机房更新socket地址导致重复地址的问题。
    9：优化确认弹框的展示
    10：修复2018-05-07的版本导致键盘不能点击切换的BUG
    11: 解决socket长连接多地址测速在ios12上卡顿的问题
    12：优化TKRootController里面获取当前控制器的逻辑

2018-07-30：更新内容
1：优化框架中使用包路径，包名路径中有中文的处理问题
2：优化行情推送、行情请求网络层的逻辑，兼容客户端和服务器返回字段不一致的问题，这样客户端不再强依赖服务器的版本，也不会导致崩溃了。
3：支持WKWebView的兼容支持
（一）配置文件增加：
<catalog name="webViewPool" description="webView链接池">
（二）TKBaseWebViewController属性修改：
isHasHeader------->名称修改为isUseNavCtrlHeader
webView------->类型进行修改为TKWebView
loadTimeOut----->新增属性，代表加载连接的超时时间，一般无需设置
（三）TKBaseWebViewController代理方法修改：
webView代理方法为TKWebViewDelegate类型，记得继承TKBaseWebViewController时候，重写代理方法时(TKWebView*)webView入参类型需要修改
- (void)webViewDidStartLoad:(TKWebView*)webView;
- (void)webViewDidFinishLoad:(TKWebView*)webView;
- (void)webView:(TKWebView*)webView didFailLoadWithError:(NSError*)error;
- (BOOL)webView:(TKWebView*)webView shouldStartLoadWithRequest:(NSURLRequest*)request navigationType:(WKWebViewNavigationType)navigationType;
-(void)webView:(TKWebView *)webView changeTitle:(NSString *)title;
-(void)webView:(TKWebView *)webView changeLoading:(BOOL)isLoading;
-(void)webView:(TKWebView *)webView changeProgress:(double)progress;
（四）H5框架需要对应升级修改一个小文件
4：解决50225获取通信录插件，特殊表情字符导致崩溃的问题
5：优化数据库操作库，支持事物的线程安全提交，API做如下修改
/**
*  执行事务的相关动作
*  isRollback代表是否要回滚，设置为YES代表要进行回滚，否则正常执行提交
*/
-(BOOL)executeTrans:(void (^)(BOOL *isRollback))action;
6：大数据统计地理位置定位实现自动适配，有定位权限就走GPS权限定位，么有就走高德IP服务定位
7：优化框架数据字典对NSNull的兼容处理
8：优化TKNetHelper获取运营商的逻辑
9：TKWebViewHelper 增加对WebView缓存大小读取和清理的API
//获取WebView的缓存空间大小
+(NSString *) getWebViewCacheSize;
//清除WebView的缓存空间
+(void) clearWebViewCache;
10:TKImageHelper 增加获取屏幕截图View的API，以前只有获取屏幕截图Image的API
//截取整个屏幕
+(UIImage *) imageByCaptureScreen;
//截取整个屏幕
+(UIView *) viewByCaptureScreen;
11:优化TKRSAHelper支持解密时候只用私钥解密，原来的需要公钥+私钥

2018-09-06：更新内容
1:修复了TKNumberHelper中关于格式化小数并转成百分比模式的BUG
2:解决WKWebView适配时候引起的高度问题
3:解决50500插件results结果为nil引起崩溃的BUG
4:增加公共参数@@_ID,@@_OP代表请求序列号和操作站点信息
5: 完善WKWebView适配自动补全插件UUID的逻辑

2018-09-30：更新内容
1：支持交易SDK渠道本地证书安全校验，BusConfig.xml里面增加配置支持
<property name="isEncrytCer" value="0" description="是否加密证书(0:否，1:是)，默认是0" />
2：socket长连接全面支持国密证书和国密加密算法，BusConfig.xml里面增加配置支持
<property name="vityifyMode" value="2" description="服务器认证模式（0:不认证，1：密码认证，2：普通证书双向认证，3：普通证书双向认证+国密加密算法，4：国密证书双向认证）" />
3: 解决WKWebView框架在ios8上面的崩溃(本地资源加载的支持问题)
4：优化Http的机房测速逻辑，优化启动速度，以前是强制卡顿指定时间，现在是测速完成后，直接进入
5：TKImageHelper支持截取全部屏幕,超出屏幕范围也截取，比如UIScrollView，WKWebView,WKWebView等
+(UIImage *) imageByFullCaptureScreen:(UIView *)view;
6: 去掉三方库minzip,解决和其他三方集成可能的冲突问题
7：去掉三方库RKLayout,解决和其他三方集成可能的冲突问题,RK开头修改为---->TK开头
8: 去掉三方库qrcoder，解决和其他三方集成可能的冲突问题
9: 修改Dock和DockItem位TKDock和TKDockItem,更新后注意对主题样式CSS文件是否有影响
10：修改Navbar为TKNavBar,更新后注意对主题CSS样式文件是否有影响
11：增加配置项 <item name="isSaveLastTheme" value="0" description="覆盖安装时，是否缓存上次的主题(0:不缓存,1:缓存)，默认是0" />
12：解决App应用同步可能导致的本地H5加载失败问题

2018-10-30：更新内容
1:修复2018-09-30：更新内容导致的长连接重复认证的BUG
2：更新Y友盟SDK为最新版本，三方库也跟着更新
3：优化socket长连接断线重连自动登录的逻辑
4：优化PDF的阅读时长
5：解决WKWebView的cookie同步问题，服务器依赖取消cookie的httponly属性，实现原生同步Webview的cookie
6：优化大数据SDK，增加app切换的时长阀值限制
7：解决站点更新乱序的问题
10：优化二维码识别的插件，原来的版本选择相册的二维码时候经常识别不到
11：优化webview加载特殊错误号的处理
12：优化TKLayerView的loading的文字显示问题
13：修复自定义键盘内存泄漏问题
14: 更新资源库里面部分图片的名称
15: 优化RSA的内存泄漏问题
16：解决地理定位在ios12系统中不返回的bug

2018-11-26：更新内容
1：优化排查框架里面存在的野指针监听通知导致崩溃的问题
2：增加了#define IPHONEX_BUTTOM_HEIGHT 34的宏定义
3：优化了H5通知原生加载完成的通知逻辑，保证先修改webview的状态再出发h5finishload的方法
4：解决键盘中英文切换点击没有反应的bug
5：优化TKNetHelper获取运营商的逻辑，做了全局缓存，保证app启动的时候获取一次即可
6：优化TKSystemHelper的获取配置文件的逻辑，原来的有问题，会导致死循环读取一个不存在的配置文件。
7：优化框架的webview控制器关于自定义导航栏返回关闭按钮的问题
8: 增加<property name="serverTag" value="SD" description="服务器TAG标示" />在busconfig.xml用于修改协议头
9: 修复由于增加公共参数@@_ID,@@_OP导致的重复请求拦截失效的问题。
10：APP更新支持强制更新+次数限制的功能

2018-12-24：更新内容
2：TKAppEngine消息引擎支持全局拦截机制，比如要对消息做一些权限校验，可以设置自定义的拦截器
//注册模块拦截器
-(void)onRegisterModuleMessageFilter:(id<TKModuleMessageFilterDelegate>)messageFilter;
3: 优化socket通信中压缩部分逻辑，提供TKZlibHelper支持压缩和解压的操作
4: 优化TKBaseWebViewController支持全屏浸入式H5，增加属性：
//是否使用全屏H5模式 默认是NO,YES的时候代表webview撑满整个控制器的View，一般情况下用于全屏WebView模式或者中间局部小的WebView模式
@property(nonatomic,assign)BOOL isUseFullScreenH5;
5: 完善TKTabBarViewController控制器支持默认的delegate中的部分方法
6：优化TKWebView解决TKBaseWebViewController的h5LoadFinish方法和isH5LoadFinish属性的先后执行逻辑问题
7：优化屏幕全局触摸的拦截事件，控制器里面可以监听屏幕触摸的开始，移动，结束事件
8：优化机房测速的逻辑，解决失败站点超时的时候的选择逻辑
9：增加TKUIHelper里面关于一下UI全局的宏定义
10: 实现TKBaseWebViewController被Push进去隐藏或者显示导航栏后，在pop出去还原的效果
11：原生调用插件的方法名有部分调整，为了和h5调用的api保持一致,如下：
-(ResultVo *)callPlugin:(NSString *)funcNo param:(id)param moduleName:(NSString *)moduleName;
-(ResultVo *)callPlugin:(NSString *)funcNo param:(id)param moduleName:(NSString *)moduleName callBackFunc:(TKPluginCallBackFunc)callBackFunc;
12: 开户依赖的TKCertLib改成TKCertLibHelper，解决冲突问题
13：取消TKApplication类，main方法用到的话，请还原成系统默认的UIApplication对象
14：BusConfig.xml配置地址的时候支持配置分组，主要目前是期货云服务器要用，格式为ip:port:groupId|ip:port:groupId|....
15：支持在线换肤功能，三方minibus库需要更新
16：优化了密码键盘的方法命名
17：支持IPV6地址
18: TKBaseWebViewController支持设置是否开启URL编码的属性,isEncodeWebViewUrl

2019-03-14：更新内容
1:修改框架内部的TKBase64Helper,支持iOS7以上走系统的API
2:新增TKURLRequestHelper类型，支持常见的GET,POST,UPLOAD等功能，多用于三方系统对接上。
3:完善消息引擎，支持常见错误的结果回调
*   模块消息引擎系统错误号定义：
*  -1000:消息号不能为空!
*  -1001:目标模块名称不能为空!
*  -1002:消息被拦截!
*  -1003:来源模块实例对象不能为空!
*  -1004:来源模块未在导航控制器中!
*  -1005:目标模块实例对象不能为空!
*  -1006:目标模块对应的类不存在!
*  -1007:目标模块对应的类未配置定义!
4:解决TKShaHelper的部分Bug
5:webview控制器增加默认加载超时时间为30秒
6：优化换肤框架，兼容原来2018-12-24版本之前的同步换肤逻辑
7: 更新配置文件支持前后台任务和超时时间的自定义
<catalog name="update" description="版本管理配置">
<item name="timeOut" value="0" description="下载超时时间，单位秒，默认是0，代表不限制" />
<item name="backgroundTaskEnabled" value="1" description="设置是否支持后台任务模式(0:否,1:是)默认1" />
8：优化长连接断线重连自动登录的逻辑
10: 优化Http请求对参数进行URL编码的逻辑
11：优化TKTabBarViewController,允许动态修改分割线的颜色 segLineColor
12：优化TKStringHelper对URL编码的处理 encodeURL escapeURL
13: 优化TKBaseWebViewController自适应逻辑，底部tabbar的模糊属性影响兼容和三方强制修改view大小的逻辑兼容
14：优化TKNumberHelpr，解决Float精度丢失问题

2019-05-07：更新内容
1：TKAppEngine增加isApplicationDidEnterBackground方法判断当前应用是否进入后台
2：新增插件50124支持H5获取当前应用的状态是否进入后台
3：解决TKBaseWebViewController加载本地页面异常时候没有显示异常界面的问题
4：扩展TKBaseWebViewController支持监听单页面跳转API
-(void)webView:(TKWebView *)webView changeURL:(NSURL *)url
-(void)webView:(TKWebView *)webView changeTitle:(NSString *)title
5：优化TKNavBar组件返回按钮的显示位置
6: 解决TKBaseWebViewController返回处理还原导航栏展示的问题
7: 优化TKBaseWebViewController加载的loading效果图

2019-05-16：更新内容
1：完善TKNetHelper中getPhoneIMSI的方法，没有插卡的情况下直接返回空
2: 完成TKNumberHelper的方法，支持格式化百分比是否分组的功能
3：TKBaseWebViewController适配状态栏变化，例如电话进来，或者热点进来之类的。
4: 完善TKLayerView弹框适配\n换行符，自动适配最大高度和宽度的问题。
5：修改配置文件，进行分类配置，并增加配置文件检测配置功能，详细配置调整如下：
//是否开启行情推送通道服务(0:否,1:是)，默认是0
system.isHqPush------>system.isStartHqPush
//是否监听全局屏幕触发(0:否,1:是),默认是0
system.listenTouch---->system.listenScreenTouch
//默认主题名称
system.theme-------->theme.currentTheme
//覆盖安装时，是否缓存上次的主题(0:不缓存,1:缓存)，默认是0
system.isSaveLastTheme-->theme.isSaveLastTheme
//原生键盘主题类型(0:浅色,1:深色)，默认是0
system.keyborad------>keyborad.theme
//原生键盘是否支持防止截屏(0:不处理，1：处理)，默认0，处理的时候键盘会去掉点击背景色
system.keyboradForNoCutScreen-->keyborad.isNoCutScreen
//是否显示网络连接提醒(0:否,1:是)，默认是1
system.isShowNetChange--->networkChange.isShowNetChange
//显示网络连接提醒的模式(0:底部提示,1:通知栏提示),默认是0
system.showNetChangeMode---->networkChange.showNetChangeMode
//请求渠道ID
system.requestChannelId--->networkRequest.requestChannelId
//是否对请求入参进行URL编码(0:否,1:是)默认是1
system.isRequestURLEncode-->networkRequest.isRequestURLEncode
//是否对请求入参进行签名(0:否,1:是)默认是0
system.isRequestURLSign-->networkRequest.isRequestURLSign
//请求签名的Key
system.requestSignKey-->networkRequest.requestSignKey
//请求签名的APPId
system.requestSignAppId-->networkRequest.requestSignAppId
//是否对请求入参进行加密(0:否,1:是)默认是0
system.isRequestURLEncrypt-->networkRequest.isRequestURLEncrypt
//加密的类型
system.requestEncryptMode-->networkRequest.requestEncryptMode
//请求加密的Key
system.requestEncryptKey--->networkRequest.requestEncryptKey
//请求签名加密相关配置
requestSign.xxx---->networkRequestSign.xxx
//ssl证书配置
ssl.xxx------>networkRequestSSL.xxx
//请求常见的错误的提示信息配置
newworkErrorInfo.xxx---->networkErrorInfo.xxx
//html的加载模式(0:从安全沙箱加载,1:从安装包加载)，默认是0
system.htmlLoadMode--->webViewPool.htmlLoadMode
webViewPool.mode--->webViewPool.webviewMode
//是否启动检测版本更新(0:否,1:是)，默认是0
system.isCheckUpdate--->update.isOpen
6: 支持行情请求和行情推送联动功能
7: 修改数据定义：
废弃DaoType_BUSV1，DaoType_BUSV2，DaoType_QUOTEV3，DaoType_TRADEV3，DaoType_NEWSV3，DaoType_TFBUSV3，DaoType_SDBUSV3，DaoType_BUSAUTO，修改为DaoType_Socket
废弃DaoType_HttpToBusAuto修改为DaoType_HttpToSocket
8：适配TKBaseWebViewController关于IphoneX底部毛玻璃挡板的问题。
9：优化断线重连自动登录的逻辑，增加参数来区分是否是自动登录的场景:
reconnectLoginFlag(0:手动登录，1：自动登录，默认0)
10：优化长连接请求，服务器20秒内没有响应包需重新进行断线重连逻辑
11: 优化大数据崩溃日志上传Slide的问题
12：优化长连接签名的逻辑
13：解决WKWebView同步调用JS假死的问题
14：支持TKBaseWebViewController 关于iPhoneX底部遮罩层透明度的设置
15：优化长连接支持定制心跳包的时间间隔
<property name="heartTime" value="5" description="心跳检测间隔时间，单位秒" />
16：优化长连接支持手工选择站点保存的功能
<property name="saveCacheTime" value="7" description="手工选择模式下站点缓存本地的时间，单位天，0代表永久缓存，默认永久缓存" />
TKGatewayManager增加API
//设置服务器是否开启手动保存模式,默认是NO，代表不开启
-(void)setServerUseAddressMode:(NSString *)serverName isManualMode:(BOOL)isManualMode;
//获取服务器是否开启手动保存模式
-(BOOL)isServerUseAddressManualMode:(NSString *)serverName;
17: 优化期货测速的逻辑，针对服务器负载打满的情况
18：满足支持华龙证券项目，站点不参与自动选择的需求,格式为---->ip:port::0

2019-06-21：更新内容
1:扩展TKRSAHelper支持公私钥加解密
2:扩展网络切换提示状态的配置
<catalog name="noteBar" description="通知栏提示层配置">
<item name="barColor" value="#ff0000" description="背景色" />
<item name="textColor" value="#ffffff" description="字体颜色" />
<item name="textPosition" value="1" description="字体的位置，0：left，1：center，2：right，默认是1" />
<item name="barAnimation" value="1" description="显示动画，0：无动画，1：上下移动，2：淡入淡出，默认是1" />
<item name="showTime" value="2" description="显示时间，单位秒，默认是2秒" />
</catalog>
3：优化框架的启动速度
4：扩展自定义键盘支持科创版
5：优化提高了框架里面查找资源文件的效率。
6: 优化TKBaseWebViewController忽略无用的加载请求错误

2019-07-24：更新内容
1：WKWebView加载本地页面适配ios13
2：请求公共参数@@OP增加字段
//拼接操作站点信息：应用类型(1:安卓,2:IOS)|应用包名|升级版本名称|升级版本序号|设备UUID|设备MAC地址|设备操作系统版本|IP地址|网络运营商(0:中国移动,1:中国联通,2:中国电信,3:中国铁通)|网络制式(1:2G,2:3G,3:4G,4:wifi)|时间戳|流水号|模块编号
3：图片选择器解决异常相册图片选择导致崩溃问题
4: TKCacheManager文件缓存管理器支持自定义缓存文件名称
//根据名称缓存文件数据对象
-(void)saveFileCacheData:(NSObject *)data withKey:(NSString *)key isEncrypt:(BOOL)isEncrypt fileName:(NSString *)fileName;
//根据关键字获取文件缓存数据
-(NSObject *)getFileCacheDataWithKey:(NSString *)key fileName:(NSString *)fileName;
5: 优化请求返回结果，可以获取原始的数据结构

2019-08-14：更新内容
1：长连接支持防外挂机制，BusConfig.xml的Server节点增加配置项目
<property name="isGameGuard" value="0" description="是否开启防外挂机制(0:否，1:是)，默认是0" />
2：长连接支持证书发送服务器的格式为base64编码格式
<property name="isSendEncrytBase64Cer" value="0" description="是否发送Base64格式的加密证书到服务器认证(0:否，1:是)，默认是0" />
3：TKBaseWebViewController支持title和左右侧的按钮定制颜色
//左侧，右侧按钮的颜色，如果没有，默认取titleColor
@property(nonatomic,retain)UIColor *btnColor;
//如果Title不为空，用来设置Ttile的文字颜色
@property(nonatomic,retain)UIColor *titleColor;
4：TKBaseWebViewController支持导航栏控制器Pop出来的时候还原原来的背景色，文字颜色等属性
5：TKJSCallBackManager支持获取指定名称的所有浏览器列表
//获取所有指定名称的浏览器对象
-(NSArray *)getAllJSCallBack:(NSString *)moduleName;
6：TKPlugin50115支持对按钮颜色的设置：btnColor
7: TKCacheManager支持自定义本地缓存文件名称
//根据名称缓存文件数据对象
-(void)saveFileCacheData:(NSObject *)data withKey:(NSString *)key isEncrypt:(BOOL)isEncrypt fileName:(NSString *)fileName;
//根据关键字获取文件缓存数据
-(NSObject *)getFileCacheDataWithKey:(NSString *)key fileName:(NSString *)fileName;
//根据关键字删除文件缓存数据
-(void)removeFileCacheDataWithKey:(NSString *)key fileName:(NSString *)fileName;

2019-08-30：更新内容
1:框架新增TKGCDWeakTimer,实现GCD版本的Timer，兼容子线程创建Timer，比TKWeakTimer更好用，API保持是一致
2:框架支持TKCommonQueue，以前就支持，这次单独列一下，这个很方便创建自定义队列，支持并发和串行，并且不会引起死锁隐患
3:网络通信层升级Socket通信库，提供网络通信处理性能，性能大幅度提升
4:修复TKBaseWebViewController支持title和左右侧的按钮定制颜色功能
5:TKUIHelper 支持颜色透明度的设置
//颜色转换 IOS中十六进制的颜色转换为UIColor
+ (UIColor *) colorWithHexString:(NSString *)color alpha:(CGFloat)alpha;
+ (UIColor *) colorWithRed:(CGFloat)r green:(CGFloat)g blue:(CGFloat)b alpha:(CGFloat)alpha;
6:TKWebView关于cookie同步问题的优化，兼容ios11
7：TKBaseWebViewController关于changeTitle的优化

2019-09-20：更新内容
1:优化期货最优站点测速机制，如果服务器负载已经满了，也认为不可连
2：TKBaseWebViewController 修复设置状态为图片背景的机制
3：优化长连接，支持认证过程中无响应包时候进行断线重连机制，此时的请求包会继续发送。
4：解决x11上面ios13上获取状态栏崩溃的问题，x10打的包无此问题。
5：优化TKNetHelper获取本地IP的逻辑
6：优化自定义NavBar返回按钮的样式
7：优化发请求时候自定义异常的逻辑，去掉自定义异常，改完直接回调

2019-10-18：更新内容
1: 引入UIView+TKFrameSupport兼容库，目录为THFMWK/component下，兼容以前的View关于width等属性扩展
2：支持弹框控制器对ios13的适配
3：解决安全扫描MD5算法，SHA1算法的提示弱加密的问题
4：解决TKBaseWebViewController里面关于H5ZF的相关代码引起上架可能被拒的问题。
5：解决random相关不安全随机数的使用问题
6：更新影响H5下载解压操作，SM2国密算法，设备编号的生成，期货更新站点配置文件，网络通信协议
7：换肤框架对UISegment组件兼容ios13特性
8: 设备名称支持iPhone11系列
9：解决AFNetWorking的Get请求编码问题
10：修改RSA加密算法解决三方冲突问题
11:TKUIHelper增加颜色转16进制字符串的功能API
12：TKTabBarViewController的背景层兼容ios13.2

2019-11-01：更新内容
1：优化网络通信的逻辑，解决偶发的线程死锁卡死问题
2：修改弹框控制器对ios13的适配，修改为初始化的时候设置
3：修改完善TKBaseWebViewController导航栏恢复逻辑，新增属性
*  是否在控制器弹出的时候恢复导航控制器的状态
*  默认是YES
@property(nonatomic,assign)BOOL isResetParentNavCtrlHeader;
4：修复TKBaseWebViewController切换内核没有重新赋值tkuuid的问题
5: 修改TKImageHelper中查找图片的逻辑，替换系统的为YYImage库，提高加载速度
6：TKBaseViewController增加功能属性如下：
//是否隐藏或显示系统的导航栏(0:显示，1：隐藏）控制器消失后恢复原状，默认为空不处理，保持默认现状
@property(nonatomic,copy)NSString *isHideSystemNavigationBar;
//是否使用导航栏悬浮效果(0:不使用，1：使用）控制器消失后恢复原状，默认为空不处理，保持默认现状
@property(nonatomic,copy)NSString *isUseSystemNavigationTranslucent;
//是否隐藏或显示系统状态栏(0:显示，1：隐藏）控制器消失后恢复原状，默认为空不处理，保持默认现状
@property(nonatomic,copy)NSString *isHideSystemStatusBar;

2019-11-15：更新内容
1: 消息引擎增加传当前控制器的相关API
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule param:(NSMutableDictionary *)param currentViewController:(UIViewController *)currentViewController
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule param:(NSMutableDictionary *)param currentViewController:(UIViewController *)currentViewController moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc
2：TKModuleMessage对象增加currentViewController这个属性
//当前控制器，先获取传过来的ViewController,获取不到再根据sourceModule获取ViewController，再获取不到取框架当前控制器对象
@property(nonatomic,retain)UIViewController *currentViewController;
3：50274插件回调H5时候增加图片类型的出参fileExtension
4：TKImageHelper增加NSData转Image的API，支持GIF格式
5：TKImageHelper增加获取NSData图片类型的API，支持GIF格式
6：解决TKDock和TKLayView里面内存释放回收崩溃的问题
7：TKImageHelper增加获取相册图片的AIP，支持根据相册地址获取图片流对象
8: 行情服务器通信兼容处理行情服务器数据异常的情况，解决返回一个超级大的count的问题

2019-12-12：更新内容
1:升级自定义键盘组件，支持键盘随意自定义设置。新增键盘配置文件KeyBoard.xml
2:升级行情通道，支持行情二维固定字段的解析模板
3:升级TKDatePicker组件，兼容某些机型上面，日期字体颜色变白的问题
4: TKBaseWebViewController 支持状态栏样式的回滚机制
//是否页面关闭时候恢复状态栏的样式
@property(nonatomic,assign)BOOL isResetParentStatusBarStyle;
5: 完善TKNavBar组件关于隐藏状态栏后续恢复时候的换肤问题
6：优化TKNetHelper的getIP的逻辑
7：优化框架日志对中文打印的支持，unicode转成utf-8正常显示
8：TKServer增加站点固定模式

2019-12-27：更新内容
1: 优化加解密和转JSON的逻辑，框架自动去掉特殊字符和结束符
2：优化AES的CBCNoPadding的模式支持
3：优化TKRootViewController的控制器卸载逻辑,并进行线程安全加锁控制
4：TKLayerView组件增加设置Tip显示时间的API
5：解决扫描二维码插件子线程操作UI引起崩溃的问题
6: TKWebViewHelper增加WKWebView和原生cookie相互同步的相关API
7: 优化长连接站点测速选择的策略，增加配置项目，默认是0
<property name="LBPolicy" value="0" description="0:普通,1:期货,2:多公司云站点" />
8：完善测速失败选择站点的策略
9：PDF阅读器插件支持加载图和重试的机制
10: 修复期货最优站点测速选择的策略
11：50118支持上传下载文件代理
12: 完善长连接非最优测速负载模式下的测速逻辑和站点手动选择的逻辑，解决手动设置站点无效的问题

2020-02-23：更新内容
1:完善键盘支持华安新版键盘，并增加完善部分功能
2:增加App全局配置管理，增加配置项目和配置文件定义AppConfig.xml
<catalog name="appConfig" description="应用配置">
<item name="path" value="AppConfig.xml" description="应用配置文件地址,多个用|分割,各自模块可以分别配置" />
<item name="updateVersionUrl" value="http://*************/stat/AppConfigVersion.xml" description="配置版本自动检测的服务器地址" />
<item name="updateContentUrl" value="http://*************/stat/AppConfig.xml" description="配置内容自动检测的服务器地址" />
</catalog>
3:全面q废弃老版WebView的相关引入和API
4:socket长连接支持分组策略的配置机制
<property name="LBGroupMode" value="3" description="0:Random(随机轮询) 1：Roundrobin(顺序轮询，缺省) 2：Backup(主备模式) 3:Best(最优智能测速模式)" />
<property name="LBMode" value="3" description="0:Random(随机轮询) 1：Roundrobin(顺序轮询，缺省) 2：Backup(主备模式) 3:Best(最优智能测速模式)" />
<property name="LBPolicy" value="0" description="0:普通,1:期货,2:期货云" />
5:增加安全SDK的本地授权校验

2020-03-18：更新内容
1：修改Socket统一接入网络通信层加密算法，和服务器保持一致采用NoPading模式
2：50118插件支持重复请求过滤和URL编码的入参
3：解决WKWebView和原生的会话同步问题
4：增加50125获取系统主题的插件
5: 增加网络诊断工具，支持应用网络诊断和基础网络诊断
6：完善TKNetHelper类和TKDeviceHelper类，支持详细设备和网络相关信息的获取
7：50115插件支持全屏沉浸式的入参控制
8：旧版资讯长连接支持期货推送逻辑，新湖期货需求
9：解决TKBaseWebViewController隐藏状态栏时候导致没有状态栏颜色的问题
10：解决大数据SDK引起的H5报错的异常处理
11：解决TKBaseWebViewController左右按钮颜色设置问题和自动根据WebView的title切换标题的功能
12：解决打开PDF插件在ios13上面展示乱码的问题
13：解决原生调用js走service的invoke方法导致重复请求拦截的问题。

2020-03-27：更新内容
1：修复行情socket的通信协议，兼容字段大小写的问题
2：支持选择弹出系统相册的插件ios11以上无需授权的功能
3：支持选择弹出系统通讯录ios8以上无需授权的功能
4：解决开户项目WKWebView和原生的会话同步问题，以最后更新时间为准
5：优化行情长连接长时间切后台后，进入前台有段时间线程卡死的问题。
6：优化TKBaseWebViewController沉浸式全屏在tabbar控制器上面的适配
7：优化WKWebView和原生的会话同步逻辑，TKBaseWebViewController控制器增加属性isForceSyncWebViewCookieToNativeCookie
8：优化拨打电话的插件功能，取消跳转到电话界面的功能，兼容为直接打电话的功能

2020-04-11：更新内容
1：修复2020-03-18修改Socket统一接入网络通信层加密算法采用NoPading模式引起的BUG
2：支持H5ZF的功能和新增插件50235实现无中间页ZF
3：实现ios13以后状态栏样式在暗黑模式下的兼容，用TKUIStatusBarStyleDefault代替UIStatusBarStyleDefault
4：对TKDataHelper中JSON转换做异常兼容，防止崩溃
5：在线版本升级支持服务器下发解压包密码功能
6：TKCommonService 增加API支持群发消息时候排除某个当前显示的模块，例如登录完成后群发消息，排除当前模块，当前模块接收其他消息
/**
*  IOS调用JS，群发调用，排除某个模块WebView当前对象
*  @param param       入参
*  @param webViewName 排除WebView模块名称
*/
-(void)iosCallJsWithParam:(NSMutableDictionary *)param excludeName:(NSString *)webViewName;
7：增加配置文件Configration.xml中ThirdZF支持，修改info.plist里面内容：LSApplicationQueriesSchemes 和 URL types
8：解决新版原生键盘在ios12中切换键盘的位置问题
9：网络通信层增加@@_OP的公共参数，拼接操作站点信息：应用类型(1:安卓,2:IOS)|应用包名|升级版本名称|升级版本序号|设备UUID|设备MAC地址|设备操作系统版本|IP地址|网络运营商(0:中国移动,1:中国联通,2:中国电信,3:中国铁通)|网络制式(1:2G,2:3G,3:4G,4:wifi)|时间戳|流水号|模块编号|内网IP地址
10：TKSystemHelper增加API
//是否安装指定的软件
+(BOOL)isInstallAppWithURLStr:(NSString *)urlStr;
//打开应用的URL
+(void)openAppURLStr:(NSString *)urlStr completionHandler:(void (^)(BOOL success))completion;
11：优化H5在线升级的逻辑，补充假如初始化copy失败的时候，在线升级也不应该生效的机制，一般这个不可能发生
12：50115插件支持选择浏览器内核
13：期货交易日志和运行日志的需求满足
14: UISegmentedControl ios13的适配
15：大数据SDK低版本兼容修改
16：TKGesturePagerController在某些机型下面换肤出现UIScroll的contentOffset偏移的问题（信达证券）
17：框架里面控制器中listNotificationInter方法中支持定义的通知只在控制器可见的时候执行，格式：通知名称|1

2020-04-24：更新内容
1：优化TKBaseWebViewController对键盘的优化处理
2：优化框架的插件机制，考虑后面某些场景并发调用插件引起的隐患
3：优化框架的插件机制，删除无用的插件通知，删除的通知列表如下：
NOTE_H5LOAD_FINISH，NOTE_HIDDEN_TABBAR，NOTE_CLOSE_MODULE, NOTE_LOAD_WEBVIEW_FAILED，NOTE_H5_SWIPTBACK
这些动作默认由框架完成，上层一般无需重写，需要重写的话，可以重写TKBaseWebViewController相应的方法
NOTE_H5LOAD_FINISH-------->-(void)h5LoadFinish;
NOTE_HIDDEN_TABBAR-------->-(void)goHideTabBar:(UITabBarController *)tabBarCtrl isHidden:(BOOL)isHidden;
NOTE_CLOSE_MODULE--------->-(void)goClose:(BOOL)isShowAnimation;
NOTE_LOAD_WEBVIEW_FAILED-->-(void)goShowLoadFailedView;
NOTE_H5_SWIPTBACK--------->-(void)recordWebViewHistoryWithFromPageUrl:(NSString *)fromPageUrl toPageUrl:(NSString *)toPageUrl;
4：TKBaseWebViewController支持原生下拉刷新重新reload浏览器，增加属性控制
*  是否支持下拉刷新webview
*  默认是NO,YES的时候代表开启原生下拉刷新webview的机制，所谓刷新webview就是reload当前的webview页面
@property(nonatomic,assign)BOOL isSupportPullDownRefresh;
5：50115增加入参控制是否开启原生下拉刷新WebView的功能
isSupportPullDownRefresh  //是否支持原生下拉刷新WebView机制(0:否，1：是)
6：TKDataHelper增加API支持URL参数的解析功能
//url参数字符串转成数据字典
+(NSDictionary *)urlToDictionay:(NSString *)url;
//数据字典转url参数字符串
+(NSString *)dictionayToUrl:(NSDictionary *)dic;
7：TKModuleDelegate模块的定义废弃-(void)onModuleMessage:(TKModuleMessage *)message 替换为如下API:
-(void)onModuleOpenMessage:(TKModuleMessage *)message; //处理消息引擎新开模块消息
-(void)onModuleCloseMessage:(TKModuleMessage *)message; //处理消息引擎关闭模块消息
-(void)onModuleChangeMessage:(TKModuleMessage *)message; //处理消息引擎切换模块消息
-(void)onModuleNotifyMessage:(TKModuleMessage *)message; //处理消息引擎通知模块消息
8：修复TKTextField关于limitLength限制的Bug
9：优化TKBaseWebViewController控制器在局部webview的情况下对H5调用隐藏显示tabbar的逻辑，这种情况下不处理
10: 50115支持设置TKBaseWebViewController在显示的时候触发50113的逻辑 isSupportReInitH5

2020-05-18：更新内容
1：优化TKBaseWebViewController在显示的时候触发50113的逻辑，防止三方H5由于没有通知原生H5加载完成导致没有触发50113
2：优化网络通信层断线重连清理数据解析队列的问题，华龙和信达遇到过，断线重连时候要清除数据解析队列任务
3：框架的插件支持ios13暗黑模式的适配
4: 50500消息引擎插件增加通用系统消息号003支持H5直接给其他H5模块发消息
5: 消息引擎支持群发消息的时候排除某些模块的功能
6：50118支持入参控制是否添加公共系统参数isAutoAddSysComParam，默认是添加
7：ReqParamVo对象添加isAutoAddSysComParam控制是否添加公共系统参数，默认是添加
8: 增加50502，50503，50504插件支持H5和原生通过通知模式进行数据交互
9: 解决FileHelper里面潜在的崩溃问题
10：优化网络切换的逻辑，解决wifi切换的网络监听
11：优化socket长连接断线重连没有停止处理数据线程的问题
12：优化WKWebview修改userAgent的逻辑
13：优化WKWebView支持loadRequest的Api提供Post模式加载页面

2020-06-09：更新内容
1:框架增加获取外网IP的备用站点，同时支持控制App启动时候是否自动获取外网IP，西南项目要去掉APP获取外网IP的全部逻辑
<catalog name="system" description="系统配置">
<item name="isAutoGetIp" value="1" description="是否自动获取外网IP(0:否,1:是)，默认是1" />
2：TKBaseWebViewController优化WKWebView在内存不足时释放绘制界面时，可以自动恢复重建WKWebView
3: 配置文件项目listenScreenTouch修改为isListenScreenTouch
<catalog name="system" description="系统配置">
<item name="isListenScreenTouch" value="1" description="是否监听全局屏幕触发(0:否,1:是)，默认是0" />
4：TKBaseWebViewController增加属性控制修改加载WebView失败的图片
//重新加载webview的界面背景图片，用于失败后重新加载webview
@property (nonatomic,copy)NSString *reloadWebViewImageName;
5：TKBaseWebViewController增加头部按钮文件图片显示的模式
//如果Title不为空,返回或者关闭按钮的模式，默认是文本模式
//（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
@property(nonatomic,copy)NSString *btnMode;
6: 50240和50115增加loadFailedImage参数控制修改webview加载失败的展示图片
7: 优化TKGestureNavigationController滑动返回卡死的BUG

2020-06-15：更新内容
1: nimibus重命名后放入框架，解决和三方冲突的问题,加了TK前缀
2: ResultVo增加errorType属性，用于告诉上层，是网络异常还是业务异常
3：解决TKBaseWebViewController优化WKWebView在内存不足时释放绘制界面时，自动恢复重建WKWebView引起的Bug
4：修复2020-04-24更新版本对系统键盘的有些异常影响
5: 50115增加入参isH5GoBack控制是否执行H5的JS返回方法(0：不执行，走浏览器默认返回，1：执行，走H5的50107插件)
6：50115增加入参containerClass，WebView容器类名，不为空就以此参数来实例化容器，默认是TKBaseWebViewController
7: TKBaseWebViewController 增加入参isH5GoBack控制是否执行H5的JS返回方法(0：不执行，走浏览器默认返回，1：执行，走H5的50107插件)
8：解决TKBaseWebViewController请求全部走原生50118的时候，同步会话引起的问题
9：优化TKBaseWebViewController修复H5的window.innerHeight的问题，并解决可能引起的崩溃（国盛证券）
10: 50261新增userDefaultImage参数控制默认用户头像图片的占位图
11：新增50210打开自定义键盘，隐藏键盘的时候回调H5功能
12：ReqParamVo新增filterRepeatRequestTimeOut属性实现重复请求，进行请求拦截时间，单位毫秒
13：50118增加filterRepeatRequestTimeOut字段实现重复请求，进行请求拦截时间，单位毫秒
14：优化断线重连处理历史队列数据的机制

2020-07-09：更新内容
1：重构增加框架异常捕获的安全处理机制,框架内部已经处理，上层建议做相应的改造如下：
定时器改造：
NSTimer------->TKWeakTimer/TKGCDWeakTimer ,TKWeakTimer建议在主线程，TKGCDWeakTimer支持主线程和子线程，天然避免异常安全和内存泄漏问题
自定义队列改造：
dispatch_queue_create----->TKCommonQueue,天然避免死锁问题和异常安全问题
线程切换改造：
使用TKSafeThreadHandleHelper类的API，天然避免死锁问题和异常安全问题
//同步主线程安全切换
+(void)safe_dispatch_sync_main_queue:(dispatch_block_t)block;
//异步主线程安全切换
+(void)safe_dispatch_async_main_queue:(dispatch_block_t)block;
//异步主线程安全切换，如果当前已经是主线程了就同步执行，否则异步执行
+(void)safe_dispatch_noforce_async_main_queue:(dispatch_block_t)block;
//主线程延迟执行安全切换,延迟单位秒
+(void)safe_dispatch_after_main_queue:(dispatch_block_t)block delayTime:(CGFloat)delayInSeconds;
//异步子线程安全切换
+(void)safe_dispatch_async_global_queue:(dispatch_block_t)block;
//子线程延迟执行安全切换
+(void)safe_dispatch_after_global_queue:(dispatch_block_t)block delayTime:(CGFloat)delayInSeconds;
//同步自定义线程安全切换
+(void)safe_dispatch_sync_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;
//异步自定义线程安全切换
+(void)safe_dispatch_async_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;
//自定义线程延迟执行安全切换
+(void)safe_dispatch_after_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block delayTime:(CGFloat)delayInSeconds;
//异步自定义线程组安全切换
+(void)safe_dispatch_group_async:(dispatch_group_t)group queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;
//同步自定义线程栅栏函数安全切换
+(void)safe_dispatch_barrier_sync_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;
//异步自定义线程栅栏函数安全切换
+(void)safe_dispatch_barrier_async_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;
2:优化TKDataHelper数据转换的异常处理
3:优化TKLog框架日志打印中文偶然由于特殊字符不打印的问题
4：TKWebViewBaseController和TKWebView的代理增加changeURL监听返回原始地址和新地址的功能
-(void)webView:(TKWebView *)webView changeURL:(NSURL *)url fromURL:(id)fromUrl;
5:增加插件50130增加copy文字到粘贴板的功能
6:增加插件50131增加保存图片到相册的功能
7:优化50120插件，检测H5需要插件是否在原生完成配置
8:TKDateHelper帮组类的容错完善
9:修复6月15号发布的版本关于TKBaseWebViewController修复H5的window.innerHeight的问题

2020-07-29：更新内容
1:对枚举类型的数据变量增加TK前缀，防止和三方冲突
2：UIViewController的扩展类，修改isShow属性和name属性,修改为isTKShow,tkName
3：更新TKBasevViewController优化导航栏push的时候隐藏tabbar的逻辑，已经隐藏了，就不用再设置隐藏了，不然返回的时候tabbar会重新出现
4：优化多机房测速机制
5：优化打开PDF插件加载loading的效果兼容ios13
6：优化TKWebView实现WKWebView的代理，预留校验服务器SSL证书逻辑
7：优化网络通信层的测速机制和生僻字返回的优化
<property name="LBMode" value="3" description="0:Random(随机轮询) 1：Roundrobin(顺序轮询，缺省) 2：Backup(主备模式) 3:Best(最优智能测试模式) 4：期货最优模式 5：NoChange(固定站点模式) 6:FZBest(负载最优模式)" />
8：优化完善手工模式和自动模式逻辑
9：优化取消Socket请求中dataType关于AES和SM4的区别，统一称为加密，至于走AES还是SM4由BusConfig.xml中证书配置的认证类型为准,是3，4的类型是国密算法，2是AES的算法
<property name="vityifyMode" value="1" description="服务器认证模式（0:不认证，1：密码认证，2：普通证书双向认证，3：普通证书双向认证+国密加密算法，4：国密证书双向认证）" />
10:TKBaseWebViewController默认状态栏颜色优化为获取主题TKNavBar的配置
11：修复处理解决-[WKContentView isSecureTextEntry]系统兼容的问题
12: 优化手势密码，支持图片不显示和字体调整优化

2020-09-09：更新内容
1:优化TKBaseWebViewController滑动返回的效果，isH5GoBack代表滑动返回走H5的返回方法，否则走浏览器的goBack方法
2:优化TKWebViewHelper拼接随机数的逻辑
3:系统权限申请弹框支持自定义内容描述，不配就走系统默认的弹框样式
<catalog name="systemRightAlert" description="权限申请自定义框">
    <item name="title" value="权限用途说明" description="弹框标题描述" />
    <item name="button" value="我知道了" description="弹框底部按钮描述" />
    <item name="header" value="&lt;font color='#505050'&gt;我们深知个人信息对您的重要性，会尽全力保护您的个人信息安全。为向您提供优质的服务，我们需要获取以下权限及信息。&lt;/font&gt;&lt;br/&gt;" description="内容头部描述，支持Html格式" />
    <item name="footer" value="&lt;font color='#505050'&gt;系统将弹窗请求，请允许中山证券App获取相关权限&lt;/font&gt;&lt;br/&gt;" description="内容底部描述，支持Html格式" />
    <item name="audio" value="" description="麦克风权限描述，支持Html格式" />
    <item name="photo" value="" description="相册权限描述，支持Html格式" />
    <item name="camera" value="" description="相机权限描述，支持Html格式" />
    <item name="addressBook" value="" description="通讯录权限描述，支持Html格式" />
    <item name="location" value="&lt;b&gt;&lt;font color='#363636'&gt;位置信息&lt;/font&gt;&lt;/b&gt;&lt;br/&gt;&lt;font color='#6f6f6f'&gt;用于查看营业网点时向您提供定位附近的营业部位置，拒绝授权后，您将无法匹配到附近营业部的信息。&lt;/font&gt;&lt;br/&gt;" description="定位权限描述，支持Html格式" />
</catalog>
4:WebView滑动返回优化手势

2020-09-19：更新内容
1：日期控件IOS14兼容性适配
2：UILabel组件IOS14兼容性适配
3：自定义弹框控件宽高优化
4：@@_OP增加设备名称
5：TKLayerView支持Tip文字颜色和背景色的设置
6：iphone12等新机型状态栏高度的适配
7：优化插件调用，支持凡泰小程序接口适配转接
8：50118支持设置全局请求参数isGlobRequest，ReqParamVo对象也支持此属性
9: 50273/50277插件原生回调和H5保持一致，base64Image/base64Images代表上传的图片，用于界面回显
   需要把base64字符串转成NSData二进制以后再转成UIImage对象，影响项目里面原生上传头像等功能
10: Http网络通信层支持上传进度条展示，ReqParamVo里面uploadDelegate的代理可以监听
11：50273和50277增加入参loadingMode 上传文件加载模式(0:菊花，1：进度条)
12：文件上传压缩逻辑优化，开启文件剪切功能，就会自动先根据手机屏幕进行压缩，再裁剪
13：TKBaseWebViewController针对状态栏处理逻辑优化，颜色为UIColor clearColor或者透明度为0的时候隐藏
14: TKBaseWebViewController针对状态栏处理逻辑优化，isUseFullScreenH5为YES，代表沉浸式状态栏，隐藏自定义状态栏
15：三方要求去掉换肤框架对UIPopoverController的引用

2020-11-12：更新内容
1: 框架内站点更新，站点加载以及相关地址获取主机的帮组类兼容IPV6协议格式
2: 移动开户的证书相关帮助类到框架中，解决后续打动态库的问题，openssl
3: 换肤框架进行性能优化，解决反复执行addcss的方法引起的性能下降
4：ResultVo做容错处理，兼容服务器异常的返回结果集
5: 50115增加打开拦截器机制，TKOpenWebViewControllerFilterDelegate
//打开webview控制器的拦截器，如果返回NO，代表被拦截不继续原来的功能，YES会继续向下执行
-(BOOL)onFilterOpenWebViewController:(NSString *)moduleName param:(NSDictionary *)param currentPlugin:(TKBasePlugin *)currentPlugin currentController:(UIViewController *)currentController;
6：站点测速优化，支持站点测速完成的回调，同时也优化启动站点测速的逻辑
7: 优化获取本地IP地址的逻辑，IPV4地址获取不到，就取IPV6的本地地址
8: 优化TKNetHelper关于域名解析的逻辑，支持IPV6解析
  +(NSString*)getIPAddressByHostName:(NSString*)strHostName
9: 本地缓存的地址支持ipv6的地址匹配规则，解决手动保存本地的IPV6地址下次不能生效的问题
10：框架中获取window的逻辑优化，适配ios13
11: 50240增加isShowDownLoadBtn入参，控制是否显示下载按钮
12：增加50242插件，实现PDF文件下载保存按钮回调H5
13：Http网络异常时候有响应包的情况处理优化
14：插件50001增加deviceName字段
15：优化TKUIHelper定义状态栏高度的宏，适配iphone刘海屏
16: 优化TKBaseWebViewController 沉浸式状态栏对URL的加载的优化，处理#号等情况
17: 优化TKWebView同步Cookie到原生的逻辑，增加系统配置项目ignoreWebViewCookie
18：50240支持下载PDF功能，H5模式下发送通知到外围
#define NOTE_DOWNLOAD_P  DF  @"note_download_pdf"

2021-01-01：更新内容
1: webview针对加载空的url的容错机制
2：TKGesturePagerController分割线指示条增加弧度美化
3: tkwebview切换后台再进入前台时候销毁重建容错并保存现场
4：优化打开PDF插件的功能，支持底部按钮颜色和文字颜色定制
5: 优化打开PDF插件的功能，40241在有阅读按钮的功能时候才进行回调执行
6: TKDatePicker注册设置日期滚轮效果在ios14.2系统上面Bug容错
7：TKGIFImageView切换GIF图片y缓存问题优化
8：TKLayer异步局部变量若引用完善
9: 只保留搜狐获取IP的逻辑
10: 优化中泰沉浸式状态栏键盘在ios12上面的隐藏不回弹问题
11: 修复2020-09-19兼容凡泰接口引起的插件回调H5，入参为空时候不回调的BUG
12：TKGIFImageView初始化单张GIF图片不显示的Bug

2021-04-01：更新内容
1: 删除TKDesHelper类，解决弱加密扫描的安全漏洞
2：删除SHA1签名算法，解决弱加密扫描的安全漏洞
3：TKBaseWebViewController新增属性isUseCustomTabBarView，支持三方使用自定义的单页面TabBarView，不走系统
4：TKBasePlugin获取当前控制器currentViewCtrl的API，取消对moduleName的判断，以uuid为准即可，因为wk以后必然有uuid
5: 50115增加入参isUseWebViewAutoResize判断webview是否使用系统自动的安全区适配机制
6：系统配置文件增加配置项system.isUserAgreeRight判断用户是否已经同意隐私协议
   <item name="isUserAgreeRight" value="1" description="用户是否已经同意隐私协议(0:否,1:是)，默认是1" />
7: 50240打开PDF插件兼容URL重定向场景，满足中和应泰需求
8：Http底层支持对JSESSSIONID的Key的定制
9：TKNimbus优化沙箱SDK渠道对接冲突宏定义
10: 华安隐私协议的需求支持
11：50273上传图片裁剪组件优化，并且支持拍照图片自动保存本地相册
12：底层socket通信支持获取测速配置BusConfig.xml的Server节点新增配置项目isGetSpeedConfig
   <property name="isGetSpeedConfig" value="1" description="是否发送测速配置请求包（0:不发送，1:发送）默认是0" />
13: 优化底层通信，解决SM4国密算法崩溃问题
14: 50240打开PDF插件优化打开非PDF的格式，优先走WKWebView的请求加载方式
15: 50240新增修改字段isCallBackForClose设置是否开启关闭回调机制
16: Http网络通信层支持加密加签响应包加密的机制

2021-06-01：更新内容
1：解决TKBaseWebViewController加载PDF切换后台再进入前台的时候，加载空白的问题
2：解决TKBaseWebViewController在iphone11的机型上面加载某些小字体页面失败的问题
3：50277插件增加参数isCanTakePhoto支持拍照上传
4：移除ReqParamVo的设置是否异步的属性，默认就是异步，废弃了同步请求
5：TKDownloadDataManager下载文件时候没有传路径，加了对服务器返回文件名称的兼容处理机制
6：TKWebView也扩展支持双向证书认证，配置和Http请求的配置共用一个
   <catalog name="networkRequestSSL" description="ssl证书配置">
       <item name="wskh.hfzq.com.cn" value="1|https.cer" description="格式为：是否校验服务器证书(0:不校验,1:校验)|客户端打包证书地址(为空就不校验)|客户端打包证书密码" />
   </catalog>
7: 更新TKDeviceHelper支持iPhone12系列机型的名称获取
8: 50273上传图片插件选择拍照或者相册的选项兼容iPad
9: H5防篡改机制优化，初始化环境的检测支持
<catalog name="system" description="系统配置">
   <item name="isCheckPhoneRunEnv" value="0" description="是否检测运行在模拟器或者越狱环境(0:否,1:是)，默认是0" />
10： 优化图片批量上传插件图片缩略图的加载展示速度问题
11： 优化TKBaseWebViewController，优化自定义tabbar+沉浸式全屏的展示效果
12: 优化TKImageHelper，TKStringHelper简化一下逻辑，提高频繁调用的执行效率
13: 优化TKBaseWebViewController，优化自定义tabbar隐藏显示tabbar，并解决iphonex挡板高度的问题
14: TKNetHelper支持5G的判断

2021-07-17：更新内容
1：优化键盘，支持中英文切换配置，KeyBoard.xml也要跟着更新
2：优化图片批量上传50277插件，兼容ios13以后的系统
3: 优化手势密码插件，手势密码输入错误时候会自动取消清空手势密码
4：优化TKWebView加载对需要OpenURL打开的链接请求进行过滤优化，配合webViewPool.disableScheme配置项
5：优化App版本更新的插件请求为GET请求模式 
6：优化图片批量上传插件，支持最大数目的设置
7：优化初始化键盘配置文件解析偶发的崩溃问题
8: Http网络通信协议适配SpringBoot的restFull协议
9：优化TKBaseWebViewController里面非常规webview，大小自定义的场景，导致每次显示不记住offset滚动偏移的问题
10: 优化TKBaseWebViewController滑动返回机制
11: 优化网络诊断显示当前用户使用站点的功能
12：覆盖安装清理缓存逻辑优化

2021-09-06：更新内容
1: 扩展自定义键盘支持华安输入金额的键盘
2：扩展自定义键盘支持晋金所安全键盘
3：优化TKShaHelper，支持对NSData数据进行摘要计算
4: 扩展TKQuoteDomainVo支持根据field的name关键key查询field的功能
5: 优化Socket智能测速策略，要等所有地址完成后才认为是完成，以前是以速度优先，测速只要回来一个就认为站点OK
6: 优化PDF加载插件，兼容IOS12
7：PDF插件支持右侧按钮的定义Action，发送通知NOTE_ACTION_PDF
8：TKLayerView支持Loading效果的自定义文字颜色和背景色
9: H5发生更新支持通知发送NOTE_APP_ISUPDATE
10: 增加文件断点续传功能
11：修复2021-07-17中Http网络通信协议适配SpringBoot的restFull协议适配时候的Bug
12：UIAlertView的兼容处理，兼容IOS8以后走UIAlertViewController
13: Http请求兼容ipv6格式的cookie域名
14：优化打开PDF插件，合并以前的下载逻辑到通用的右侧按钮逻辑

2021-10-25：更新内容
1:修改TKNavBar的扩展方法，解决三方冲突问题，增加TK前缀修饰
2:TKDeviceHelper支持新版本iphone13系统手机类型的获取
3:TKNetHelper兼容ios12获取手机多卡的逻辑
4：在线升级优化对提示框和提示语进行展示，根据配置文件update.isShowUpdateTip来决定
5: 修复优化iPhone11ProMax启动图获取不准确的Bug
6：50240打开PDF插件支持查看图片格式，优化支持放大缩小和自适应
7: 优化获取网关相关的设备信息，只有在网络变化时候才重新获取一次

2021-12-14：更新内容
1: 优化socket网络通信底层逻辑，支持格尔三方的socket国密改造对接
2: TKAuthorizationHelper增加获取系统授权的状态查询API
3：键盘配置兼容中金财富的部分优化逻辑
4：优化2021-01以后框架对TKBaseWebViewController监听键盘的处理逻辑，只有在控制器显示的时候才进行相关逻辑处理
5: 优化TKBaseWebViewController监听键盘的处理逻辑，根据是否启用思迪自定义键盘标记，来决定是否拦截系统键盘显示隐藏逻辑
6: 优化TKShaHelper关于HMAC256的算法
7: 换肤框架支持#FFFFFFFF格式设置透明度
8: Http请求根据响应包的编码集进行自动适配
9: 优化键盘初始化配置文件的加载性能，减少CPU的消耗,并把初始化切换改造成子线程，加快启动速度
10: 兼容沉浸式底部安全区的处理逻辑,根据isUseWebViewAutoResize参数控制是否由原生处理底部安全区自适应问题
11: 修改为IOS11以后强制进行相册的动态权限申请，因为IOS15以后不强制申请会出现第一次使用相册的权限异常
12: 优化TKWebView加载页面自适应问题
13: 解决2021-07-17版本以后覆盖安装时候清理缓存后没有重新初始化相关服务配置的BUG
14: 增加WKWebView与系统原生cookie同步时候过滤失效cookie的配置项目webViewPool.isFilterExpiresCookie

2022-02-24：更新内容
1: 键盘插件50210支持控制键盘title是否隐藏显示
2：Http响应包优化网络异常的类型返回TKResultErrorType_Network
3: 优化system.embIdentifier签名Id配置，支持多个用|分割，配置的值需要加密后进行配置
4: 优化NavigationBar等适配ios15的样式设置
5: 网络通信层支持微服务RestFull接口协议及加密加签,ReqParamVo增加isRestFull属性设置，代表是否微服务接口
6: 50001,50003插件网络类型支持5G类型
7: 优化打开PDF插件的loading效果
8: 优化交互分类的方法支持类方法交换
9: Configuration.xml增加框架配置文件是否使用加载的开关控制
   全局配置 AppConfig.xml     ----> appConfig.isUse，默认是1，配置0关闭
   消息引擎 Module.xml        ----> system.isUseModuleEngine ，默认是1，配置0关闭
   思迪C接入 BusConfig.xml    ----> system.isLinstenGateway, 默认是0，配置1开启
   H5加载拦截器 H5Filter.xml  ----> webViewPool.isOpenH5Filter, 默认是1，配置0关闭
   多机房站点配置 Server.xml  ----> system.isUseServerTestSpeed , 默认是0，配置1开启
   自定义键盘 KeyBoard.xml    ----> keyborad.isUse , 默认是1，配置0关闭
   其他换肤配置文件           ----> theme节点配置决定，里面没有配置就没有对应的换肤配置
10: 优化框架未初始化时候由于分类执行框架内某些逻辑的优化
11：优化WKWebview后台回收重建的逻辑，判断进入前台时候是否存在title属性
12: 网络通信层支持微服务RestFull接口协议及加密加签及签名key为密文模式
13: 优化网络通信Http多机房测速逻辑，保证测速回来的机房可以被本地缓存
14：优化文件断点续传的逻辑，同时兼容微服务返回的结果只有code,msg没有data结果集这种场景，判断这种也是微服务的接口

2022-06-28：更新内容
1: 框架支持信安国密算法通道，新增配置networkRequest.gmDaoName，配置国密网络链路适配Dao名称
2：插件50118支持国密算法通道，新增入参netLinkMode，网络链路(0:普通链路，1：国密链路)，默认是0
3: TKLayerView弹框提示针对内容做适配，里面有<p> 或者<br/>的则认为走Html解析逻辑
4: 在线检测更新支持微服务模式update.reqMode
5：TKBaseWebViewController支持触屏事件监听
6：TKCommonService增加server://协议支持URL链接路径配置
7：换肤框架支持View和控制器的onChangeTKTheme方法，皮肤切换时候会自动触发
8: TKCommonService优化默认微服务接口的参数为不进行URLEncode编码模式
9: 优化50119插件新增webview扩展配置，支持是否开启原生下拉刷新配置入参isSupportPullDownRefresh(0:禁用，1：开启)
10： 优化TKAuthorizationHelper，对相册授权修改为ios8以后强制授权提示
11：优化插件回调H5的逻辑优化，兼容判断是否原生调用还是H5调用
12：优化自定义键盘按钮的阴影效果

2022-08-16：更新内容
1：Socket网络通信层支持国密站点选择需求
   BusConfig.xml里面的站点配置格式为==》 地址:端口:分组ID:是否加入测速站点:是否国密站点
2：优化TKSwizzlingMethod方法，解决交换默认影响父类的问题
3：优化自定义键盘自动识别切换返回上一个键盘的逻辑
4: ReqParamVo新增contentType入参来控制请求头类型（ReqParamVo增加isRestFull属性设置，代表是否微服务接口，这个以前已经支持了）
5：TKFormatHelper新增强密码判断的帮组API+(int)checkPasswordStrength:(NSString *)str;
6: 修改socket网络通信层支持国密站点修改引起的Bug，原因是增加了GMBest站点的测速类型，导致了普通模式下站点选择逻辑无效，走了随机站点逻辑了
7: 优化换肤框架，支持控制器或者view的onChangeTKTheme方法
8: 优化isRestFull微服务接口，修改为默认Post请求就是不编码，Get请求进行URL编码
9: 优化框架NavBar在ios16的兼容问题，可能出现初始化空白和切换title错乱问题
10：WKWebView禁用回弹效果的兼容适配
11: TKBaseWebViewController支持系统WKWebview的返回前进手势控制属性
    //是否支持后退
    @property (nonatomic,assign) BOOL allowsBackNavigationGestures;
    //是否支持前进
    @property (nonatomic,assign) BOOL allowsForwardNavigationGestures;
12：Socket网络通信层支持国密测速请求
13: 优化自定义键盘图片加载，图片地址为空的时候异常处理
14: 优化Socket网络通信层单站点测速逻辑，默认就认为站点是存活的
15：优化TKBaseWebViewController支持兼容加载失败时候，发生网络变化时候，重新刷新webview页面
16: 框架防止二次打包证书校验逻辑优化，支持多证书模式
17：优化TKBaseWebViewController关于WebView长时间切后台回收时候的逻辑，对外暴漏-(void)goRebuildWebView 及-(void)goRestSocketNetWorkLink函数
18：打开PDF插件优化，禁止复制操作

2022-10-13：更新内容
1：优化国密账号切换时候的网络通信层逻辑，延迟关闭
2：优化换肤逻辑，本地没有配置对应主题时候也发通知，兼容开户需求
3：优化GCDSocket类适配IOS16崩溃的问题，kCFStreamNetworkServiceTypeVoIP已废弃
4: 优化手势密码进入前台时候关闭键盘功能
5：优化打开PDF插件，解决doc打开乱码问题
6：支持自定义WKWebview的userAgent头
   <catalog name="webViewPool" description="webView链接池">
      <item name="userAgent" value="" description="浏览器扩展头" />
7: 框架网络请求、下载等组件均支持userAgent头自定义
8：优化Socket网络通信层支持格尔国密证书服务测速请求
9: 优化Socket网络通信层国密场景下站点切换的场景支持，判断是否已经建立了长连接
10：优化微服务响应包tk-encrypt-response加密逻辑，防止崩溃
11: 优化Socket站点国密模式切换的逻辑，TKGatewayManager新增API
   -(void)setServerUseAddressGMMode:(NSString *)serverName isUseGMSite:(BOOL)isUseGMSite;
   -(BOOL)isServerUseAddressGMMode:(NSString *)serverName;
12：优化Socket网络通信层国密场景下账号切换的场景支持，延迟释放逻辑放在同一个线程中，防止多线程操作请求队列引发的崩溃问题

2022-12-05：更新内容
1: iPhone14Pro等新版本手机的拍照焦距适配
2：设备名称翻译支持iPhone14等新机型
3: 增加update.isCancelOnNetworkException配置项目
   <item name="isCancelOnNetworkException" value="1" description="网络异常时是否自动取消下载任务(0:否,1:是)默认0" />
4: 优化socket站点测速和Http站点测速，实现两个测速模块并行执行效果，加快启动速度
5：优化国密改造账户切换时候释放旧账户Socket连接请求时候的逻辑，解决偶发的崩溃问题
6：支持国密改造底层socket重试机制，排除当前站点，用其他站点重试
7：取消搜狐获取IP的相关代码功能，API还是保留兼容，返回空
8: 增加50282插件，支持视频文件的上传
9: 大数据sdk更新，新增添加公共参数API
10：优化TKBaseWebViewController的goClose逻辑，只是移除自己在Navgation堆栈里面
11：iPhone14关于灵动岛状态栏的适配问题，影响范围涉及TKWebViewController关于webview高度异常，会多算5个像素

2023-02-21：更新内容
1：TKBaseWebviewController关于二维码图片识别和保存的功能优化，JS增强兼容性
2：50273图片上传逻辑优化，原来的图片上传开启裁剪以后有过度压缩导致模糊的问题
3: Http测速机制优化，满足甬兴需求，仿造C接入站点相关功能
4: 50115支持导航栏背景图片设置，左右按钮颜色的设置
5：框架环境切换进行持久化缓存，满足海通多环境切换需求
6: 支持App应用防截屏放录像功能，TKBaseViewController需要重写-(void)handleCheckScreenShot;方法进行截屏逻辑处理，一般提示用户即可
    <catalog name="system" description="系统配置">
        <item name="isCheckViewHijack" value="1" description="是否检测界面劫持攻击(0:否,1:是)，默认是0" />
        <item name="isCheckScreenShot" value="1" description="是否检测截屏录屏攻击(0:否,1:是)，默认是0" />
7: 华安Socket测速崩溃逻辑优化，配套国密Socket也跟着调整
8: TKWebView loading图片不存在容错处理以及判断SSL通道安全认证方式的主线程容错机制处理
9：解决SM2算法偶发加密异常导致解密不成功的问题
10：优化框架引用<Photos/Photos.h>文件，改成按需引入
11：解决TKBaseViewController 设置statusBarStyle不生效的问题
12: 打开PDF插件增加加载超时的参数控制loadTimeOut
13: 50115和TKBaseWebViewController支持设置透明度的属性参数alpha
14:Http测速机制优化，满足甬兴需求引起的bug优化
15：打开PDF插件重试机制优化

2023-06-21：更新内容
1：TKWebView增加属性isSupportLocalFileAccess控制是否开启本地化支持兼容，默认开启
2：手势密码支持加密存储逻辑
3: 文件上传支持上传文件数组，格式为 key@@F: [nsdata数组]
4: 主题换肤方法进行容错处理，解决偶发崩溃问题 -(void)refreshCssForView:(UIView *)view
5：大数据埋点本地缓存支持加密存储
    <catalog name="traffic" description="思迪统计配置">
       <item name="encryptCacheEnabled" value="0" description="设置是否对缓存的埋点数据进行加密(0:否,1:是)默认0" />
6: 支持App应用防截屏放录像功能
   TKBaseViewController 通过-(void)handleCheckScreenShotRecord:(TKCheckScreenType)checkScreenType;方法进行截屏逻辑处理，默认框架会提示用户,子类可以复写实现自定义逻辑
   TKBaseViewController 支持isCheckScreenShotRecord属性控制当前控制器是否监听防截屏通知
     <catalog name="system" description="系统配置">
        <item name="isCheckViewHijack" value="1" description="是否检测界面劫持攻击(0:否,1:是)，默认是0" />
        <item name="isCheckScreenShotRecord" value="1" description="是否检测截屏录屏攻击(0:否,1:是)，默认是0" />
7: 50273,50277支持压缩大小参数 compressSize 质量压缩大小（KB)
8: 优化插件50282,50131,50228,50222,50225,50271,50273,50277相关通信录、拍照、视频、二维码等插件，支持返回回调H5以及没有权限时候提示用户进行设置授权
9: 优化框架网络底层库及TKURLRequestHelper对json格式头的兼容优化逻辑处理
10: 优化app切入后台毛玻璃效果，并在TKBaseViewController中增加属性控制是否开启
    //是否开启后台模糊效果
    @property (nonatomic,assign) BOOL isBlurBackground;

2023-09-02：更新内容
1：新增WKWebview的调试配置功能，兼容ios16.4
    <catalog name="webViewPool" description="webView链接池">
       <item name="inspectable" value="1" description="是否开启WK调试(0:否,1:是)默认是0" />
2: 修复沉浸式状态栏下面TKBaseWebViewController加载失败返回键位置异常问题
3：优化国密测速逻辑，服务器下发非国密状态下，不进行国密站点的测速
   TKGatewayManager 增加saveCacheGlobalGMFlag getCacheGlobalGMFlag API
4: 优化WebView本地包加载安全权限控制的逻辑优化 解决2023-06-21：TKWebView增加属性isSupportLocalFileAccess控制是否开启本地化逻辑
5: 优化App应用防截屏放录像功能TKBaseViewController 支持isCheckScreenShotRecord属性控制当前控制器是否监听防截屏通知
6：优化TKBaseWebViewController自定义滑动返回机制，页面关闭的时候也进行H5的回调通知

2023-11-15：更新内容
1：TKCertLibHelper 支持TK_SIGN_TYPE_ATTACH和TK_SIGN_TYPE_DETACH 两种签名方式
    + (NSString*)sign:(NSString*)sourceStr signType:(TK_SIGN_TYPE)sType userId:(NSString*)userId;
2：TKCertLibHelper 申请证书支持2048位
   + (TK_ERR_CREATEP10)createPKCS10:(NSMutableString *)pk10 pwd:(NSString *)pwd userId:(NSString *)userId keyLength:(int)keyLength;
3：解决Http多机房测速由于2023-02-21：满足甬兴需求引发的历史app覆盖安装不兼容的问题，引发崩溃
4：解决TKNetHelper获取网络类型的时候断网时由于缓存导致类型未更新的问题
5：自定义键盘防截屏配置功能开启
6：新增Http多机房测速满足甬兴手动切换站点模式
7：50277增加maxNum最大可选照片的限制及ios13系统下照片无法展示问题
8: 手势密码相关插件新增防截屏控制
9: 优化TKWebViewController支持H5调用50100通知原生加载完成的时候隐藏进度条
10：数据字典支持获取值的时候可以设置默认值
11：TKBaseWebViewController增加属性allowsInlineMediaPlayback，是否开启音视频播放，默认是YES
12：TKBaseWebViewController增加属性isUseSecurityDomain，是否开启安全域名，默认是NO
    <catalog name="webViewPool" description="webView链接池">
        <item name="inspectable" value="1" description="是否开启WK调试(0:否,1:是)默认是0" />
        <item name="isCheckSecurityDomain" value="1" description="是否开启安全域名校验(0:否,1:是)默认是0" />
        <item name="securityDomain" value="quanshang.test.jiniutech.cn" description="安全域名,多个用|分割" />
        <item name="illegalDomainUrl" value="www/index.html" description="非法域名的重定向地址" />

2024-3-29：更新内容
1:50115支持自定义userAgent，新增属性customUserAgent，同步TKBaseWebViewController也增加此属性支持
2：TKBaseWebViewController 优化子视图布局逻辑，viewWillLayoutSubviews
3：C接入网络通信层逻辑优化，防止测速时候线程过多导致的死锁崩溃问题
4: 优化WebView缓冲池机制，解决缓冲池耗尽时候，引发的缓冲池计数逻辑问题
5：优化TKWebViewHelper优化服务器资源请求缓存机制，本地资源不缓存
6: TKBaseWebViewController优化关闭时候音视频播放问题
7：继续优化C接入网络通信层逻辑，缓存测速链接不释放
8：TKBaseWebViewController 继续优化子视图布局逻辑，viewWillLayoutSubviews优化底部挡板逻辑
9: TKAFNetworkding 暴漏头优化
10：换肤框架支持NavagationBar设置字体
11：换肤框架兼容url()空配置容错处理
12：优化http站点测速的逻辑，随机时候优先选择ipv4站点
13：优化主题切换，覆盖安装上次的主题不存在的场景，取默认主题
14: TKBaseWebViewController 支持白名单及黑名单安全域名配置
15：TKDeviceHelper支持设备信息获取的代理模式
   //设置代理类  +(void)setTKDeviceInfoDelegate:(id<TKDeviceInfoDelegate>) delegate;
16：TKWebView代理增加安全域名的处理API：-(void)webView:(TKWebView *)webView didFailLoadWithIllegalDomain:(NSString *)illegalDomain
17：TKNavBar换肤逻辑优化，支持tintColor设置
18：优化TKBaseWebViewController隐藏时候停止音视频播放引发的偶发bug问题

2024-11-27：更新内容
1：升级xcode16，并适配构建
2：设备类型采集支持到iphone16相关机型
3: 优化TKImageHelper,新增compressImageData 图片压缩到指定大小的API
4: 新增50127,50128,50129插件
5: 50277选择相册兼容ios18系统
6: 新增50132插件
7：优化SSL本地证书校验逻辑，兼容Web和Http请求
8：优化TKBaseViewController逻辑，兼容FDFullscreenPopGesture三方库导致的bug问题
9: 网络通信文件上传接口，针对文件传路径时候，进行逻辑优化，自动走底层获取文件名称和类型
10: TKReqParamVO增加属性isKeepOriginalParam，解决访问三方接口需要保持原始的参数格式不做容错的处理，公司标准的默认会把value为json格式的对象转成json字符串
    另外也不会拼接@@_OP等参数,相当于isAutoAddSysComParam=false
11: TKBaseWebViewController对底部安全区挡板进行兜底容错判断，如果不在安全区范围就强制关闭
12: 50273,50277插件支持选择视频上传功能 0:照片+拍照,1:照片,2:拍照，3:视频，9:照片+拍照+视频
13：优化TKBaseWebViewController逻辑，isUseWebViewAutoResize 自动和 isUseFullScreenH5属性保持一致，要不全屏全部H5做兼容处理，要不全部进行原生安全区适配

2025-06-05：更新内容
1：系统权限提示语支持自定义，涉及50131,50222,50225,50228,50271,50273,50277,50282等插件
2：增加系统权限拒绝时候的通知开关
    <catalog name="systemRightRejectAlert" description="权限拒绝自定义弹框">
        <item name="audio" value="0" description="麦克风权限拒绝是否自定义弹框(0:否，1:是),默认0" />
        <item name="photo" value="0" description="相册权限拒绝是否自定义弹框(0:否，1:是),默认0" />
        <item name="camera" value="0" description="相机权限拒绝是否自定义弹框(0:否，1:是),默认0" />
        <item name="people" value="0" description="通讯录权限拒绝是否自定义弹框(0:否，1:是),默认0" />
        <item name="location" value="0" description="定位权限拒绝是否自定义弹框(0:否，1:是),默认0" />
    </catalog>
3：TKAuthorizationHelper 的回调函数TKAuthorizationStatusCallBack进行修改，不向下兼容，注意：
   //授权回调，isDialogShownResult用来标识是不是第一次授权弹框
   typedef void(^TKAuthorizationStatusCallBack)(TKAuthorizationStatus status, BOOL isDialogShownResult);
4: 新增50140,50141插件，支持获取和修改当前签署的隐私协议状态
5：修改三方库TKOpenUUID关于剪切板的依赖问题
6: 增加防截屏录屏攻击独立配置，取消system.isCheckScreenShotRecord
   <catalog name="checkScreenShotRecord" description="防截屏录屏攻击配置">
        <item name="isOpen" value="0" description="是否检测截屏录屏攻击(0:否,1:是)，默认是0" />
        <item name="screenCaptureTip" value="当前界面正在被截屏，请注意您的安全！" description="防截屏提示语" />
        <item name="screenRecordingTip" value="当前界面正在被录屏，请注意您的安全！" description="防录屏提示语" />
    </catalog>
7: 修复50273和50277增加视频选择引起的和取消按钮的冲突问题
