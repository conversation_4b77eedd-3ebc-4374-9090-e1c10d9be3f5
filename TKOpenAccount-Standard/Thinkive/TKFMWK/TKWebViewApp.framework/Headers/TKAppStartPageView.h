//
//  TKAPPStartPageView.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-6-8.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  <AUTHOR> 2015-06-08 09:06:17
 *
 *  引导页控件
 */
@interface TKAppStartPageView : UIView<UIScrollViewDelegate>

/**
 *  <AUTHOR> 2015-06-08 10:06:48
 *
 *  引导页内容
 */
@property (nonatomic,strong)NSArray *pageViews;

/**
 *  <AUTHOR> 2015-06-08 10:06:43
 *
 *  是否显示翻页标签
 */
@property (nonatomic,assign)BOOL showPageIndicator;

/**
 *  <AUTHOR> 2016-05-03 20:05:01
 *
 *  间隔时间
 */
@property(nonatomic,assign)float invTime;

/**
 *  <AUTHOR> 2016-05-19 18:05:40
 *
 *  是否支持手工滚动翻页，默认是支持，可以禁止设置为NO
 */
@property(nonatomic,assign)BOOL scrollEnabled;

/**
 *  <AUTHOR> 2017-04-26 14:04:47
 *
 *  是否翻页标签到最后一个点的时候自动隐藏
 */
@property(nonatomic,assign)BOOL isLastPageIndicatorHidden;

/**
 *  <AUTHOR> 2017-04-26 14:04:22
 *
 *  翻页标签的位置
 */
@property(nonatomic,assign)CGRect pageIndicatorFrame;

@end
