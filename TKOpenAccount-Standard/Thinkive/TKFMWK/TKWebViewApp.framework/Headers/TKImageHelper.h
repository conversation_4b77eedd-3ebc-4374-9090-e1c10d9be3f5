//
//  TKImageHelper.h
//  ios4
//
//  Created by l<PERSON><PERSON> on 14-10-28.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#define MIRRORED ((image.imageOrientation == UIImageOrientationUpMirrored) || (image.imageOrientation == UIImageOrientationLeftMirrored) || (image.imageOrientation == UIImageOrientationRightMirrored) || (image.imageOrientation == UIImageOrientationDownMirrored))
#define ROTATED90 ((image.imageOrientation == UIImageOrientationLeft) || (image.imageOrientation == UIImageOrientationLeftMirrored) || (image.imageOrientation == UIImageOrientationRight) || (image.imageOrientation == UIImageOrientationRightMirrored))

/**
 *  图片处理帮助类
 */
@interface TKImageHelper : NSObject

/**
 *  在指定的目录里面递归查找文件，包含子目录查询
 *
 *  @param name   文件名称
 *  @param folder 目录路径
 *
 *  @return 图片
 */
+(UIImage *) imageByName:(NSString *)name inFolder:(NSString *) folder;

/**
 *  根据图片名称查找文件，先在应用包路径下找，找不到就在沙盒里面找
 *
 *  @param name 图片名称
 *
 *  @return 图片
 */
+(UIImage *) imageByName:(NSString *)name;

/**
 *  从网络url里面获取图片
 *
 *  @param url 网络地址
 *
 *  @return 图片
 */
+(UIImage *) imageByUrl:(NSString *) url;

/**
 *  缩放图像
 *
 *  @param image 原图片
 *  @param size  缩放的矩形
 *
 *  @return 缩放后的图片
 */
+(UIImage *) imageByImage:(UIImage *)image fillSize:(CGSize)size;

/**
 *  缩放图像
 *
 *  @param image        原图片
 *  @param size         缩放的矩形
 *  @param isKeepScale  是否保持等比例
 *
 *  @return 缩放后的图片
 */
+(UIImage *) imageByImage:(UIImage *)image fillSize:(CGSize)size isKeepScale:(BOOL)isKeepScale;

/**
 *  缩放图像
 *
 *  @param image        原图片
 *  @param size         缩放的矩形
 *  @param isKeepScale  是否保持等比例
 *  @param alpha        图片透明度
 *
 *  @return 缩放后的图片
 */
+(UIImage *) imageByImage:(UIImage *)image fillSize:(CGSize)size isKeepScale:(BOOL)isKeepScale alpha:(CGFloat)alpha;

/**
 *  <AUTHOR> 2017-05-07 21:05:41
 *
 *  缩放图片,会处理图片的方向
 *
 *  @param image   原图片
 *  @param size    缩放的矩形
 *  @param quality 质量
 *
 *  @return
 */
+(UIImage *)imageByImage:(UIImage *)image fillSize:(CGSize)size interpolationQuality:(CGInterpolationQuality)quality;

/**
 *  <AUTHOR> 2017-05-07 21:05:11
 *
 *  缩放图片,会处理图片的方向
 *
 *  @param image       原图
 *  @param contentMode 模式
 *  @param size        缩放的矩形
 *  @param quality     质量
 *
 *  @return
 */
+(UIImage *)imageByImage:(UIImage *)image contentMode:(UIViewContentMode)contentMode fillSize:(CGSize)size interpolationQuality:(CGInterpolationQuality)quality;

/**
 *  取消图片的旋转，获得正常显示的图片
 *
 *  @param image 原图片
 *
 *  @return 还原后的图片
 */
+(UIImage *) fixOrientation:(UIImage *)image;

/**
 *  <AUTHOR> 2017-05-06 15:05:26
 *
 *  剪切图片
 *
 *  @param image  原图片
 *  @param frame  剪切范围
 *
 *  @return
 */
+(UIImage *)croppedImage:(UIImage *)image frame:(CGRect)frame;

/**
 *  <AUTHOR> 2017-05-06 15:05:54
 *
 *  旋转图片
 *
 *  @param image   原图
 *  @param degrees 旋转角度
 *
 *  @return
 */
+ (UIImage *)rotatedImage:(UIImage *)image withDegrees:(CGFloat)degrees;

/**
 *  <AUTHOR> 15-05-13 13:05:38
 *
 *  获取Default.png 自动判断iPhone 5
 *
 *  @return
 */
+ (UIImage *)defaultLaunchImage;

/**
 *  <AUTHOR> 15-05-13 13:05:38
 *
 *  获取Default.png 自动判断iPhone 5
 *
 *  @param orientation 程序界面的当前旋转方向 
 *
 *  @return
 */
+ (UIImage *)defaultLaunchImage:(UIInterfaceOrientation)orientation;

/**
 *  截取屏幕
 *
 *  @param view 屏幕的View
 *
 *  @return 截屏的图片
 */
+(UIImage *) imageByView:(UIView *)view;

/**
 *  <AUTHOR> 2015-06-04 19:06:01
 *
 *  截取整个屏幕,超出屏幕范围不截取
 *
 *  @return 截屏的图片
 */
+(UIImage *) imageByCaptureScreen;

/**
 *  <AUTHOR> 2015-06-04 19:06:01
 *
 *  截取整个屏幕,超出屏幕范围不截取
 *
 *  @return 截屏的图片
 */
+(UIView *) viewByCaptureScreen;

/**
 *  <AUTHOR> 2015-06-04 19:06:01
 *
 *  截取全部屏幕,超出屏幕范围也截取
 *
 *  @return 截屏的图片
 */
+(UIImage *) imageByFullCaptureScreen:(UIView *)view;

/**
 *  得到图片的位图数据
 *
 *  @param image 图片
 *
 *  @return 位图数据
 */
+(unsigned char *)bitmapFromImage:(UIImage *)image;

/**
 *  灰化图片
 *
 *  @param image 原图片
 *
 *  @return 灰化后的图片
 */
+(UIImage *) grayImage:(UIImage *)image;

/**
 *  生成纯色的直角图片
 *
 *  @param color 颜色
 *  @param size  大小
 *
 *  @return 图片
 */
+ (UIImage *)imageByColor:(UIColor *)color size:(CGSize)size;

/**
 *  生成纯色的圆角图片
 *
 *  @param color 颜色
 *  @param size  大小
 *
 *  @return 图片
 */
+ (UIImage *)roundRectImageByColor:(UIColor *)color size:(CGSize)size;

/**
 *  生成纯色的圆角图片
 *
 *  @param color 颜色
 *  @param size  大小
 *  @param round 弧度
 *
 *  @return 图片
 */
+ (UIImage *)roundRectImageByColor:(UIColor *)color size:(CGSize)size round:(CGFloat)round;

/**
 * 获取图片内容格式
 */
+ (NSString *)getImageContentType:(NSData *)imageData;

/**
 * 获取图片
 */
+ (UIImage *)imageWithData:(NSData *)imageData;

/**
 * 缩放GIF图片
 */
+ (UIImage *)imageByGIFImage:(UIImage *)image fillSize:(CGSize)size;
    
/**
 * 相册里面获取图片
 */
+ (void)imageWithPhotoURL:(NSURL *)photoURL callBackFunc:(void (^)(NSData *imageData,NSError *error))callBackFunc;

/*!
 *  @brief 使图片压缩后刚好小于指定大小
 *
 *  @param image 当前要压缩的图 maxLength 压缩后的大小
 *
 *  @return 图片对象
 */
+ (UIImage *)compressImageSize:(UIImage *)image toByte:(NSUInteger)maxLength;

/*!
 *  使图片压缩后刚好小于指定大小
 *
 *  @param image 当前要压缩的图 maxLength 压缩后的大小
 *
 *  @return 图片对象二进制
 */
+ (NSData *)compressImageData:(UIImage *)image toByte:(NSUInteger)maxLength;

@end
