//
//  TKMessageInvoke.h
//  TKApp
//
//  Created by l<PERSON><PERSON> on 14-12-1.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 界面切换动画效果
 */
typedef enum
{
    /**
     *  <AUTHOR> 2015-03-31 15:03:32
     *
     *  无效果
     */
    TKAnimate_None,
    /**
     *  <AUTHOR> 2015-03-31 15:03:39
     *
     *  从上往下
     */
    TKAnimate_UpToDown,
    /**
     *  <AUTHOR> 2015-03-31 15:03:45
     *
     *  从下往上
     */
    TKAnimate_DownToUp,
    /**
     *  <AUTHOR> 2015-03-31 15:03:52
     *
     *  渐变
     */
    TKAnimate_Fade,
    /**
     *  <AUTHOR> 2015-03-31 15:03:58
     *
     *  从左向右
     */
    TKAnimate_LeftToRight,
    /**
     *  <AUTHOR> 2015-03-31 15:03:04
     *
     *  从右向左
     */
    TKAnimate_RightToLeft
} TKAnimate;

/**
 *  <AUTHOR> 2014-12-01 15:12:51
 *
 *  消息引擎处理
 */
@protocol TKAppInvokeDelegate <NSObject>

/**
 *  <AUTHOR> 2014-12-09 21:12:58
 *
 *  页面跳转切换
 *
 *  @param pageCode   模块路径名称 例如xxxx/xxxx
 *  @param param      参数
 */
-(void)invokeSwitchPage:(NSString *)pageCode param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2014-12-09 21:12:58
 *
 *  页面跳转切换
 *
 *  @param pageCode   模块路径名称 例如xxxx/xxxx
 *  @param param      参数
 *  @param animate    切换动画
 */
-(void)invokeSwitchPage:(NSString *)pageCode param:(NSMutableDictionary *)param animate:(TKAnimate)animate;

/**
 *  <AUTHOR> 2015-06-11 10:06:37
 *
 *  页面跳转切换
 *
 *  @param srcPageCode   模块路径名称 例如xxxx/xxxx
 *  @param destPageCode  模块路径名称 例如xxxx/xxxx
 *  @param param         参数
 */
-(void)invokeSwitchPageFrom:(NSString *)srcPageCode to:(NSString *)destPageCode param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-06-11 10:06:37
 *
 *  页面跳转切换
 *
 *  @param srcPageCode   模块路径名称 例如xxxx/xxxx
 *  @param destPageCode  模块路径名称 例如xxxx/xxxx
 *  @param param         参数
 *  @param dismissSrcPageFlag 是否在源目标是弹出层的时候关闭
 */
-(void)invokeSwitchPageFrom:(NSString *)srcPageCode to:(NSString *)destPageCode param:(NSMutableDictionary *)param dismissSrcPageFlag:(BOOL)flag;

/**
 *  <AUTHOR> 2014-12-09 21:12:58
 *
 *  页面跳转切换
 *
 *  @param srcPageCode   模块路径名称 例如xxxx/xxxx
 *  @param destPageCode   模块路径名称 例如xxxx/xxxx
 *  @param param      参数
 *  @param animate    切换动画
 */
-(void)invokeSwitchPageFrom:(NSString *)srcPageCode to:(NSString *)destPageCode param:(NSMutableDictionary *)param animate:(TKAnimate)animate;

/**
 *  <AUTHOR> 2014-12-09 21:12:58
 *
 *  页面跳转切换
 *
 *  @param srcPageCode   模块路径名称 例如xxxx/xxxx
 *  @param destPageCode   模块路径名称 例如xxxx/xxxx
 *  @param param      参数
 *  @param animate    切换动画
 *  @param dismissSrcPageFlag 是否在源目标是弹出层的时候关闭
 */
-(void)invokeSwitchPageFrom:(NSString *)srcPageCode to:(NSString *)destPageCode param:(NSMutableDictionary *)param animate:(TKAnimate)animate dismissSrcPageFlag:(BOOL)flag;

@end
