//
//  TKComBusV3Client.h
//  TKAppBase_V2
//
//  Created by <PERSON><PERSON><PERSON> on 16-3-3.
//  Copyright (c) 2016年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKComBusClient.h"

/**
 基础请求功能号
 */
typedef enum
{
    /**
     *  <AUTHOR> 2015-05-04 17:05:11
     *
     *  心跳包
     */
    TKComBusV3MsgType_Heart = 0,
    
    /**
     *  <AUTHOR> 2015-05-04 17:05:19
     *
     *  会话包
     */
    TKComBusV3MsgType_SessionKey = 1,
    
    /**
     *  <AUTHOR> 2015-05-04 17:05:33
     *
     *  连接认证包
     */
    TKComBusV3MsgType_AuthLogin = 2,
    
    /**
     *  <AUTHOR> 2015-05-04 17:05:33
     *
     *  连接认证返回包
     */
    TKComBusV3MsgType_AuthLogin_Return = 3,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:35
     *
     *  验证客户端证书
     */
    TKComBusV3MsgType_VitifyClientCer = 4,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:26
     *
     *  验证客户端证书响应包
     */
    TKComBusV3MsgType_VitifyClientCer_Return = 5,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:23
     *
     *  验证AES加密密钥
     */
    TKComBusV3MsgType_VitifyAesKey = 6,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:51
     *
     *  验证AES加密密钥响应包
     */
    TKComBusV3MsgType_VitifyAesKey_Return = 7,
    /**
     *  <AUTHOR> 2016-11-16 20:11:23
     *
     *  验证SM4加密密钥
     */
    TKComBusV3MsgType_VitifySM4Key = 8,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:51
     *
     *  验证SM4加密密钥响应包
     */
    TKComBusV3MsgType_VitifySM4Key_Return = 9,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:35
     *
     *  验证国密客户端证书
     */
    TKComBusV3MsgType_VitifyClientSMCer = 10,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:26
     *
     *  验证国密客户端证书响应包
     */
    TKComBusV3MsgType_VitifyClientSMCer_Return = 11,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:26
     *
     * 申请服务器挑战码请求包
     */
    TKComBusV3MsgType_ApplyGameGuardChallenge = 20,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:26
     *
     *  申请服务器挑战码响应包
     */
    TKComBusV3MsgType_ApplyGameGuardChallenge_Return = 21,
    
    /**
     *  <AUTHOR> 2016-11-16 20:11:26
     *
     *  验证服务器挑战吗请求包
     */
    TKComBusV3MsgType_VitifyGameGuardChallenge = 22
}TKComBusV3MsgType;

/**
 *  <AUTHOR> 2015-06-30 14:06:39
 *
 *  3.0拉取请求通信层
 */
@interface TKComBusV3Client : TKComBusClient<TKBusClientDataDelegate>

#pragma mark 工具类

/**
 *  <AUTHOR> 2015-05-04 16:05:29
 *
 *  补全字符串,工具类
 *
 *  @param str    字符串
 *  @param length 长度
 *
 *  @return
 */
-(NSString *)assign:(NSString *)str length:(int)length;

/**
 *  <AUTHOR> 2015-05-04 16:05:29
 *
 *  过滤生僻字,工具类
 *
 *  @param content    字符串
 *
 *  @return
 */
-(NSString *)processOtherChineseChar:(NSString *)content;

/**
 * 转换数据类型
 */
-(TKDataVoType)convertToTKBusClientDataVoType:(int)dataType;

#pragma mark 发送请求

/**
 *  <AUTHOR> 2017-06-06 00:06:35
 *
 *  获取服务器业务标示
 *
 *  @return
 */
-(NSString *)getTag;

/**
 *  <AUTHOR> 2017-06-06 21:06:03
 *
 *  获取加密Key
 *
 *  @return
 */
-(NSString *)getEncrytKey;

/**
 *  <AUTHOR> 2015-06-30 14:06:06
 *
 *  获取支持证书认证模式的响应头长度
 *
 *  @return
 */
-(int)getCertVitifyResponseHeaderLength;

/**
 *  <AUTHOR> 2015-05-04 16:05:02
 *
 *  构造请求数据
 *
 *  @param messageType       消息类型
 *  @param bodydata          消息体
 *  @param origDataLength    消息体原始长度
 *
 *  @return 发送的消息数据
 */
-(NSData *)buildTHSendMessage:(int)messageType flowNo:(int)flowNo bodyData:(NSData *)bodydata origDataLength:(UInt32)origDataLength;

/**
 *  <AUTHOR> 2015-05-04 18:05:22
 *
 *  发送请求信息
 *
 *  @param messageType 消息类型
 *  @param bodydata    消息体
 *  @param origDataLength    消息体原始长度
 */
-(void)sendTHMessage:(int)messageType flowNo:(int)flowNo bodyData:(NSData *)bodydata origDataLength:(UInt32)origDataLength;

/**
 *  <AUTHOR> 2015-05-04 16:05:02
 *
 *  构造请求数据
 *
 *  @param messageType 消息类型
 *  @param bodydata    消息体
 *  @param origDataLength    消息体原始长度
 *
 *  @return 发送的消息数据
 */
-(NSData *)buildTKSendMessage:(int)messageType flowNo:(int)flowNo bodyData:(NSData *)bodydata origDataLength:(UInt32)origDataLength;

/**
 *  <AUTHOR> 2015-05-04 18:05:22
 *
 *  发送请求信息
 *
 *  @param messageType 消息类型
 *  @param bodydata    消息体
 */
-(void)sendTKMessage:(int)messageType flowNo:(int)flowNo bodyData:(NSData *)bodydata origDataLength:(UInt32)origDataLength;

#pragma 解析请求数据

/**
 *  <AUTHOR> 2015-06-30 15:06:03
 *
 *  转换结果数据到最后的resultVo的数据字典对象
 *
 *  @param resultData 结果数据
 *
 *  @return
 */
-(NSDictionary *)convertTHResultDataToResultDic:(NSData *)resultData;

/**
 *  <AUTHOR> 2016-11-19 13:11:17
 *
 *  解析认证包体
 *
 *  @param bodyData 认证包体
 */
-(void)parseTHResultData:(int)messageType bodyData:(NSData*)bodyData origDataLength:(UInt32)origDataLength;

/**
 *  <AUTHOR> 2015-06-30 15:06:03
 *
 *  转换结果数据到最后的resultVo的数据字典对象
 *
 *  @param resultData 结果数据
 *
 *  @return
 */
-(NSDictionary *)convertTKResultDataToResultDic:(NSData *)resultData;

#pragma mark 扩展业务

/**
 *  <AUTHOR> 2016-03-03 16:03:35
 *
 *  服务器认证成功以后的数据处理，data是包体内容，不包括错误号的字段
 *
 *  @param data
 */
-(void)doServerVitifySuccess:(NSData *)data;

@end
