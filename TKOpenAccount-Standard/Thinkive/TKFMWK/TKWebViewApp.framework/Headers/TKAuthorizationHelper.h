//
//  TKAuthorizationHelper.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2017/3/3.
//  Copyright © 2017年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef enum {
    TKAuthorizationStatus_Authorized = 0,        // 已授权
    TKAuthorizationStatus_Denied = 1,            // 拒绝
    TKAuthorizationStatus_Restricted = 2,        // 应用没有相关权限，且当前用户无法改变这个权限，比如:家长控制
    TKAuthorizationStatus_NotSupport = 3,        // 硬件等不支持
    TKAuthorizationStatus_NotDetermined = 4      // 尚未授权
}TKAuthorizationStatus;

typedef enum {
    TKAuthorizationType_Photo = 0,             // 相册权限
    TKAuthorizationType_Camera = 1,            // 相机权限
    TKAuthorizationType_Audio = 2,             // 麦克风权限
    TKAuthorizationType_AddressBook = 3,       // 通信录权限
    TKAuthorizationType_Location = 4           // 定位权限
}TKAuthorizationType;

/**
 *  麦克风权限拒绝通知
 */
#define NOTE_RIGHT_REJECT_AUDIO @"note_right_reject_audio"

/**
 * 相册权限拒绝通知
 */
#define NOTE_RIGHT_REJECT_PHOTO @"note_right_reject_photo"

/**
 * 相机权限拒绝通知
 */
#define NOTE_RIGHT_REJECT_CAMERA @"note_right_reject_camera"

/**
 * 地理位置权限拒绝通知
 */
#define NOTE_RIGHT_REJECT_LOCATION @"note_right_reject_location"

/**
 * 通讯录权权限拒绝通知
 */
#define NOTE_RIGHT_REJECT_PEOPLE @"note_right_reject_people"

/**
 *  <AUTHOR> 2015-11-06 18:11:31
 *
 *  读取通信录回调
 *
 *  @param TelAddressAry 通信录列表
 */
typedef void(^TKReadTelAddressBookComplete)(NSArray * telAddressAry);

/**
 *  <AUTHOR> 2015-11-06 18:11:31
 *
 *  授权回调
 */
typedef void(^TKAuthorizationStatusCallBack)(TKAuthorizationStatus status, BOOL isDialogShownResult);

/**
 *  <AUTHOR> 2015-11-06 18:11:31
 *
 *  按钮回调
 */
typedef void(^TKAuthorizationAlertBtnCallBack)(void);

/**
 *  <AUTHOR> 2017-03-03 23:03:00
 *
 *  权限认证判断帮助类
 */
@interface TKAuthorizationHelper : NSObject

/**
 *  请求相册访问权限
 *
 *  @param callback
 */
+ (void)requestPhotoAuthorization:(TKAuthorizationStatusCallBack)callback;

/**
 *  请求访问相机权限
 *
 *  @param callback
 */
+ (void)requestCameraAuthorization:(TKAuthorizationStatusCallBack)callback;

/**
 *  请求访问麦克风权限
 *
 *  @param callback
 */
+ (void)requestAudioAuthorization:(TKAuthorizationStatusCallBack)callback;

/**
 *  <AUTHOR> 2017-03-03 23:03:01
 *
 *  请求访问通信录权限
 *
 *  @param callback 
 */
+ (void)requestAddressBookAuthorization:(TKAuthorizationStatusCallBack)callback;

/**
 *  <AUTHOR> 2017-03-03 23:03:01
 *
 *  请求地理位置定位权限
 *
 *  @param callback
 */
+ (void)requestLocationAuthorization:(TKAuthorizationStatusCallBack)callback;

/**
 *  <AUTHOR> 2017-03-03 23:03:01
 *
 *  请求权限组
 *  @param authTypes      权限组
 *  @param authCallBacks  权限回调组，和authTypes对应，如果传nil就不自动申请系统权限
 *  @param btnCallBack    权限弹框按钮回调，不为空，弹框按钮点击的时候会触发
 */
+ (void)requestAuthorization:(NSArray<NSNumber *>*)authTypes authCallBacks:(NSArray<TKAuthorizationStatusCallBack>*)authCallBacks btnCallBack:(TKAuthorizationAlertBtnCallBack)btnCallBack;

/**
 * 获取相册的授权状态
 */
+ (TKAuthorizationStatus)photoAuthorization;

/**
 * 获取相机的授权状态
 */
+ (TKAuthorizationStatus)cameraAuthorization;

/**
 * 获取麦克风的授权状态
 */
+ (TKAuthorizationStatus)audioAuthorization;

/**
 * 获取通信录的授权状态
 */
+ (TKAuthorizationStatus)addressBookAuthorization;

/**
 * 获取地理位置的授权状态
 */
+ (TKAuthorizationStatus)locationAuthorization;

/**
 *  <AUTHOR> 2017-03-04 12:03:19
 *
 *  是否后置摄像头可用
 *
 *  @return
 */
+ (BOOL) isRearCameraAvailable;

/**
 *  <AUTHOR> 2017-03-04 12:03:40
 *
 *  是否前置摄像头可用
 *
 *  @return
 */
+ (BOOL) isFrontCameraAvailable;

/**
 *  <AUTHOR> 2014-12-22 19:12:12
 *
 *  打电话
 *
 *  @param telPhone 电话号码
 *  @param isImmediately 是否立即拨打电话,(NO：进入拨打界面，YES：直接拨打)
 */
+(void)callTelPhone:(NSString *)telPhone isImmediately:(BOOL)isImmediately;

/**
 *  <AUTHOR> 2014-12-23 10:12:26
 *
 *  读取通信录
 *
 *  @return 通讯录列表
 */
+(NSArray *)readTelAddressBook;

/**
 *  <AUTHOR> 2014-12-23 10:12:26
 *
 *  读取通信录
 *
 *  @return 通讯录列表
 */
+(void)readTelAddressBook:(TKReadTelAddressBookComplete)readTelAddressBookComplete;

@end
