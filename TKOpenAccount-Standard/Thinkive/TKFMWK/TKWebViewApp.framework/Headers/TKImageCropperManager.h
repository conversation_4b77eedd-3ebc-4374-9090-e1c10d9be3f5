//
//  TKImageCropperManager.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-7-23.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>


typedef enum {
    /**
     *  <AUTHOR> 2015-07-23 14:07:31
     *
     *  简单剪切版本
     */
    TKImageCropperMode_Simple = 0,
    
    /**
     *  <AUTHOR> 2015-07-23 14:07:04
     *
     *  加强剪切版本
     */
    TKImageCropperMode_Strong = 1,
    
    /**
     *  <AUTHOR> 2015-07-23 14:07:17
     *
     *  综合功能版本
     */
    TKImageCropperMode_All = 2
}TKImageCropperMode;

/**
 *  <AUTHOR> 2015-07-23 16:07:20
 *
 *  代理类
 */
@protocol TKImageCropperManagerDelegate <NSObject>

/**
 *  <AUTHOR> 2015-07-23 16:07:04
 *
 *  处理最后的视频文件
 *
 *  @param image
 */
-(void)processCropperMovie:(NSURL *)movieUrl;

/**
 *  <AUTHOR> 2015-07-23 16:07:04
 *
 *  处理最后的图片
 *
 *  @param image
 */
-(void)processCropperImage:(UIImage *)image;

/**
 *  <AUTHOR> 2017-02-27 12:02:52
 *
 *  处理照片的异常情况，例如没有权限
 *
 *  @param errorNo    错误号
 *  @param errorInfo  错误信息
 */
-(void)processCropperImageWithErrorNo:(int)errorNo errorInfo:(NSString *)errorInfo isDialogShownResult:(BOOL)isDialogShownResult;

@end

/**
 *  <AUTHOR> 2015-07-23 10:07:08
 *
 *  图片上传管理器
 */
@interface TKImageCropperManager : NSObject

/**
 *  <AUTHOR> 2015-07-23 14:07:10
 *
 *  模式，默认为简单剪切模式
 */
@property(nonatomic,assign)TKImageCropperMode imageCropperMode;

/**
 *  <AUTHOR> 2015-07-23 16:07:58
 *
 *  代理
 */
@property(nonatomic,weak)id<TKImageCropperManagerDelegate> delegate;

/**
 *  <AUTHOR> 2016-06-13 21:06:42
 *
 *  是否要剪切图片
 */
@property(nonatomic,assign)BOOL isCutImage;

/**
 *  <AUTHOR> 2017-05-09 12:05:10
 *
 *  是否用身份证拍照模式
 */
@property(nonatomic,assign)BOOL isUseIdCardCamera;

/**
 *  <AUTHOR> 2017-05-09 12:05:10
 *
 *  是否自动保存拍照图片到相册
 */
@property(nonatomic,assign)BOOL isAutoSavePhoto;

/**
 *  <AUTHOR> 2017-05-09 12:05:10
 *
 *  是否包含视频相册选择
 */
@property(nonatomic,assign)BOOL isSupportMoviePhoto;

/**
 *  <AUTHOR> 2016-06-13 21:06:42
 *
 *  是否要压缩视频
 */
@property(nonatomic,assign)BOOL isCompressMovie;

/**
 *  <AUTHOR> 2016-07-19 22:07:53
 *
 *  当前控制器
 */
@property(nonatomic,retain)UIViewController *currentViewCtrl;

/**
 *  <AUTHOR> 2015-07-28 13:07:00
 *
 *  状态栏背景颜色
 */
@property(nonatomic,retain)UIColor *statusBarBgColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  如果Title不为空，用来设置Ttile的文字颜色
 */
@property(nonatomic,retain)UIColor *titleColor;

/**
 *  <AUTHOR> 2016-11-08 19:11:21
 *
 *  是否前置摄像头
 */
@property(nonatomic,assign)BOOL isFrontCamera;

/**
 闪光灯的模式
 */
@property(nonatomic,assign) UIImagePickerControllerCameraFlashMode cameraFlashMode;

/**
 *  <AUTHOR> 2015-02-03 18:02:30
 *
 *  单例模式
 *
 *  @return
 */
+(TKImageCropperManager *)shareInstance;

/**
 *  <AUTHOR> 2015-07-23 10:07:08
 *
 *  显示选择菜单
 */
-(void)showChoiceSheet;

/**
 *  <AUTHOR> 2016-07-07 20:07:44
 *
 *  选择模式
 *
 *  @param type 1:照片，2：拍照
 */
-(void)showChoiceSheet:(NSString *)type;

/**
 *  <AUTHOR> 2015-07-23 15:07:30
 *
 *  直接编辑图片
 *
 *  @param image
 */
-(void)editImage:(UIImage *)image;

@end
