//
//  TKBaseComWebViewController.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/7/24.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseViewController.h"
#import "TKWebView.h"
#import "TKJSCallBackManager.h"

/**
 *  <AUTHOR> 2017-03-10 15:03:25
 *
 *  分享通知
 *
 */
#define NOTE_WEBVIEW_SHARE @"note_webview_share"

/**
 *  浏览器动作事件代理处理
 */
@protocol TKWebViewActionEventDelegate <NSObject>

@optional

/**
 *  webview返回方法，如果到顶部就关闭
 */
-(void)goBack;

/**
 *  webView关闭
 */
-(void)goClose;

/**
 *  webView关闭，子类复写的时候，要复写此方法，goClose内部调用的也是此方法，50114也是触发此带参数的方法
 *  @param isShowAnimation 是否带动画
 */
-(void)goClose:(BOOL)isShowAnimation;

/**
 *  webView分享
 */
-(void)goShare:(NSDictionary *)param;

/**
 *  隐藏显示底部tabbar
 */
-(void)goHideTabBar:(UITabBarController *)tabBarCtrl isHidden:(BOOL)isHidden;

/**
 *  显示加载失败重试界面
 */
-(void)goShowLoadFailedView;

/**
 * webview回收后，重新构建webview对象前监听
 */
-(void)goRebuildWebView;

/**
 * webview长时间切换到后台后，进入前台时候进行socket网络链路
 */
-(void)goRestSocketNetWorkLink;

@end

/**
 * 基础共用webview
 */
@interface TKBaseComWebViewController : TKBaseViewController<TKWebViewDelegate,TKWebViewActionEventDelegate>

#pragma mark 属性定义

/**
 *  <AUTHOR> 2015-07-27 16:07:43
 *
 *  是否使用思迪自定义键盘(默认是YES)
 */
@property(nonatomic,assign)BOOL isUseTKKeyboard;

/**
 *  <AUTHOR> 2016-07-20 18:07:09
 *
 *  是否使用webView缓冲池机制。在系统开启缓冲池策略的时候，这个属性才有作用
 */
@property(nonatomic,assign)BOOL isUseWebViewCachePool;

/**
 是否每次初始化都执行加载H5的URL
 */
@property(nonatomic,assign)BOOL isLoadH5Forever;

/**
 *  <AUTHOR> 2015-12-22 20:12:17
 *
 *  是否在控制器释放时候重置webview的url
 */
@property(nonatomic,assign)BOOL isResetWebViewAfterDealloc;

/**
 *  <AUTHOR> 2017-03-13 20:03:12
 *
 *  当支持滑动返回的时候，加载的页面是否是思迪开发的H5，YES：走思迪特殊的滑动返回效果 NO：其他默认三方的H5页面
 */
@property (nonatomic,assign)BOOL isTKH5;

/**
 *是否执行H5的JS返回方法(0：不执行，走浏览器默认返回，1：执行，走H5的50107插件)
 */
@property (nonatomic,assign)BOOL isH5GoBack;

/**
 *是否开启H5进行App前后台监听，开启后，APP前后台切换状态通过50129插件通知H5
 */
@property (nonatomic,assign)BOOL isH5ListenAppStatus;

/**
 *  <AUTHOR> 2015-10-13 14:10:30
 *
 *  浏览器的名称
 */
@property(nonatomic,readonly)NSString *webViewName;

/**
 *  <AUTHOR> 2015-07-27 17:07:24
 *
 *  浏览器
 */
@property(nonatomic,readonly)TKWebView *webView;

/**
 *  <AUTHOR> 2015-09-29 21:09:53
 *
 *  js对象
 */
@property(nonatomic,readonly)JSCallBack *jsCallBack;

/**
 *  <AUTHOR> 2015-07-27 17:07:53
 *
 *  webView的地址，服务器端的格式如:http://www.baidu.com?name=lubao
 本地端的格式如:www/m/mall/index.html#!/business/index.html
 */
@property(nonatomic,copy)NSString *webViewUrl;

/**
 *  是否进行URL地址编码，默认是YES
 */
@property (nonatomic,assign)BOOL isEncodeWebViewUrl;

/**
 *  辅助参数，例如随机数和uuid插入的位置标示符号，可能是#!/或者#或者其他未来的符号，默认是#!/
 */
@property (nonatomic,copy)NSString *exParamInsertBeforeFlagChars;

/**
 *  <AUTHOR> 2015-07-27 17:07:19
 *
 *  加载超时时间，单位秒
 */
@property(nonatomic,assign)double loadTimeOut;

/**
 *  <AUTHOR> 2015-11-17 11:11:22
 *
 *  H5网页是否使用原生系统的导航栏头，这种场景一般用在我们的webview控制器外面包了一层navigation控制器，然后需要用navgaton控制器的头作为显示的头
 默认是NO，代表用的是H5页面自带的导航栏头
 如果是YES，代表用的是Navigation控制器的导航栏头
 */
@property(nonatomic,assign)BOOL isUseNavCtrlHeader;

/**
 *  <AUTHOR> 2015-11-17 11:11:22
 *
 *  是否在控制器弹出的时候恢复导航控制器的状态
 *  默认是YES
 */
@property(nonatomic,assign)BOOL isResetParentNavCtrlHeader;

/**
 *  <AUTHOR> 2015-11-17 11:11:22
 *
 *  是否使用全屏H5模式
 *  默认是NO,YES的时候代表webview撑满整个控制器的View，一般情况下用于全屏WebView模式或者中间局部小的WebView模式
 */
@property(nonatomic,assign)BOOL isUseFullScreenH5;

/**
 *  <AUTHOR> 2015-11-17 11:11:22
 *
 *  是否支持下拉刷新webview
 *  默认是NO,YES的时候代表开启原生下拉刷新webview的机制，所谓刷新webview就是reload当前的webview页面
 */
@property(nonatomic,assign)BOOL isSupportPullDownRefresh;

/**
 *  <AUTHOR> 2015-04-10 16:04:26
 *
 *  是否支持webview的滚动监听
 */
@property (nonatomic,assign)BOOL isListenWebviewTouchEvent;

/**
 *  <AUTHOR> 2015-07-28 13:07:00
 *
 *  状态栏背景颜色
 */
@property(nonatomic,retain)UIColor *statusBarBgColor;

/**
 *  <AUTHOR> 2015-08-24 18:08:34
 *
 *  状态栏背景图片
 */
@property(nonatomic,retain)UIImage *statusBarBgImage;

/**
 *  <AUTHOR> 2015-08-24 18:08:34
 *
 *  导航栏栏背景图片
 */
@property(nonatomic,retain)UIImage *navBarBgImage;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  如果Title不为空，用来设置Ttile的文字颜色
 */
@property(nonatomic,retain)UIColor *titleColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  左侧按钮的颜色，如果没有，默认取titleColor
 */
@property(nonatomic,retain)UIColor *leftBtnColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  右侧按钮的颜色，如果没有，默认取titleColor
 */
@property(nonatomic,retain)UIColor *rightBtnColor;

/**
 *  <AUTHOR> 2014-12-11 13:12:50
 *
 *  WebView进度条颜色
 */
@property (nonatomic,retain)UIColor *progressColor;

/**
 *  <AUTHOR> 2014-12-11 13:12:50
 *
 *  WebView背景色颜色
 */
@property (nonatomic,retain)UIColor *backgroundColor;

/**
 *  <AUTHOR> 2014-12-11 13:12:50
 *
 *  WebView透明度
 */
@property (nonatomic,assign)CGFloat alpha;

/**
 *  <AUTHOR> 2014-12-11 13:12:50
 *
 *  iPhoneX底部非安全区颜色
 */
@property (nonatomic,retain)UIColor *iphoneXBottomColor;

/**
 *  <AUTHOR> 2014-12-11 13:12:50
 *
 *  iPhoneX底部非安全区透明度
 */
@property (nonatomic,assign)CGFloat iphoneXBottomAlpha;

/**
 *  <AUTHOR> 2015-12-08 14:12:21
 *
 *  没有导航栏的时候，状态栏View，此时的导航栏为H5的导航栏,statusBarView在loadView以后才会有值
 */
@property (nonatomic,readonly)TKUIView *statusBarView;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  是否显示加载过渡loading图
 */
@property(nonatomic,assign)BOOL isShowLoading;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  是否隐藏加载过渡loading图，在webview加载完成后，默认是YES，如果要自定义隐藏时机，需要设置为NO
 */
@property(nonatomic,assign)BOOL isHideLoadingAfterWebViewFinished;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  如果Title不为空，是否还要显示返回的按钮
 */
@property(nonatomic,assign)BOOL isShowBackBtn;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  如果Title不为空，是否还要显示关闭的按钮
 */
@property(nonatomic,assign)BOOL isShowCloseBtn;

/**
 *  <AUTHOR> 2017-03-07 20:03:58
 *
 *  是否显示分享按钮
 */
@property(nonatomic,assign)BOOL isShowShareBtn;

/**
 *  <AUTHOR> 2017-03-15 23:03:55
 *
 *  分享入参
 */
@property(nonatomic,copy)NSDictionary *shareParam;

/**
 *  <AUTHOR> 2017-02-15 10:02:38
 *
 *  如果Title不为空,返回或者关闭按钮的模式，默认是文本模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
 */
@property(nonatomic,copy)NSString *btnMode;

/**
 *  <AUTHOR> 2017-02-18 18:02:45
 *
 *  标题文字是否根据webview的Title而变化（0：固定，1：改变）
 */
@property(nonatomic,assign)BOOL isChangeTitle;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  重新加载webview的界面背景图片，用于失败后重新加载webview
 */
@property (nonatomic,copy)NSString *reloadWebViewImageName;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  重新加载webview的界面，用于失败后重新加载webview
 */
@property (nonatomic,retain)UIView *reloadWebViewUI;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  加载webview的过渡界面的动画图片名称，loadingWebViewGIFName和完全自定义的loadingWebViewUI，设置一个就可以
 */
@property (nonatomic,copy)NSString *loadingWebViewGIFName;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  加载webview的过渡界面
 */
@property (nonatomic,retain)UIView *loadingWebViewUI;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  是否启动webview的默认自适应机制，自动适配iphoneX和ios11等
 */
@property (nonatomic,assign)BOOL isUseWebViewAutoResize;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  H5隐藏tabbar的标示
 */
@property (nonatomic,assign)BOOL isH5HideTabbarFlag;

/**
 是否接收H5插件调用强制同步Cookie会话
 */
@property(nonatomic,assign)BOOL isForceSyncWebViewCookieToNativeCookie;

/**
 * 是否使用自定义的单页面TabBarView，没有走系统
 */
@property(nonatomic,assign)BOOL isUseCustomTabBarView;

/**
 * 是否开启支持本地资源访问，默认是YES
 */
@property (nonatomic,assign) BOOL isSupportLocalFileAccess;

/**
 * 是否开启音视频播放，默认是YES
 */
@property (nonatomic,assign) BOOL allowsInlineMediaPlayback;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  是否开启安全域名校验
 */
@property(nonatomic,assign) BOOL isUseSecurityDomain;

/**
 *  是否常用全屏WebView场景，而非局部webviwe场景（行情F10一般用到）
 */
@property(nonatomic,assign,readonly) BOOL isCommonWebview;

/**
 * 自定义扩展头
 */
@property(nonatomic,copy)NSString *customUserAgent;

#pragma mark webview加载相关方法

/**
 *  <AUTHOR> 2015-07-27 18:07:42
 *
 *  进行WebView预加载
 */
-(void)prepareLoadWebView;

/**
 *  <AUTHOR> 2017-04-11 01:04:49
 *
 *  重新加载webview方法，用于加载失败后重新加载webview触发的方法
 */
-(void)reloadWebView;

/**
 *  <AUTHOR> 2017-04-11 01:04:49
 *
 *  关闭加载效果
 */
-(void)clearLoadingWebViewUI;

/**
 *  <AUTHOR> 2017-04-11 01:04:49
 *
 *  是否忽略处理的请求错误
 */
-(BOOL)isIgnoreWebViewRequestError:(NSError *)error;

#pragma mark 原生调用JS的相关方法

/**
 *  <AUTHOR> 2015-04-21 00:04:56
 *
 *  IOS调用JS
 *
 *  @param param       参数
 */
-(void)iosCallJsWithParam:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-04-22 10:04:09
 *
 *  IOS调用JS
 *
 *  @param function    函数名称
 *  @param param       Json格式的JS入参
 */
-(void)iosCallJsWithFunction:(NSString *)function param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-04-22 10:04:53
 *
 *  IOS调用JS
 *
 *  @param function    函数名称
 *  @param params      多个JS入参用,分割的，这里用数组表示
 */
-(void)iosCallJsWithFunction:(NSString *)function params:(NSArray *)params;

@end
