//
//  TKBaseHttpDao.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2022/6/27.
//  Copyright © 2022 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseDao.h"

/**
 * 基础Http请求类
 */
@interface TKBaseHttpDao : TKBaseDao

/**
 * 扩展属性
 */
@property (nonatomic, strong) NSDictionary *userInfo;

/**
 *  <AUTHOR> 2014-11-25 15:11:33
 *
 *  获取单例
 *
 *  @return 单例对象
 */
+(instancetype)shareInstance;

/**
 *  <AUTHOR> 2015-08-03 19:08:36
 *
 *  获取加密加签请求入参
 *
 *  @ReqParamVo reqParamVo
 *
 *  @return
 */
-(NSMutableDictionary *)getRequestParam:(ReqParamVo *)reqParamVo;

/**
 *  <AUTHOR> 2015-08-03 19:08:36
 *
 *  获取加密加签请求加密Key
 *
 *  @ReqParamVo reqParamVo
 *
 *  @return
 */
-(NSString *)getRequestEncryptKey:(ReqParamVo *)reqParamVo;

/**
 *  <AUTHOR> 2015-08-03 19:08:36
 *
 *  获取请求的头
 *
 *  @ReqParamVo reqParamVo
 *
 *  @return
 */
-(NSMutableDictionary *)getRequestHeader:(ReqParamVo *)reqParamVo;

/**
 *  <AUTHOR> 2015-08-03 19:08:36
 *
 *  获取Get请求入参
 *
 *  @ReqParamVo reqParamVo
 *
 *  @return
 */
-(NSString *)buildGetRequestURL:(NSString *)url param:(NSDictionary *)param;

/**
 *  <AUTHOR> 2014-11-25 16:11:34
 *
 *  请求开始
 *
 *  @param request 请求对象
 */
-(void)requestStarted:(ReqParamVo *)reqParamVo;

/**
 *  <AUTHOR> 2014-11-25 16:11:31
 *
 *  请求失败
 *
 *  @param request 请求对象
 */
/**
 *  <AUTHOR> 2014-11-25 16:11:31
 *
 *  请求失败
 *
 *  @param request 请求对象
 */
-(void)requestFailed:(ReqParamVo *)reqParamVo error:(NSError *)error responseData:(NSData *)responseData response:(NSHTTPURLResponse *)response;

/**
 *  <AUTHOR> 2014-11-25 16:11:46
 *
 *  请求成功
 *
 *  @param request 请求对象
 */
-(void)requestFinished:(ReqParamVo *)reqParamVo request:(NSURLRequest *)request responseData:(NSData *)responseData response:(NSHTTPURLResponse *)response;

/**
 *  <AUTHOR> 2014-11-25 19:11:21
 *
 *  接收数据
 *
 *  @param request 请求对象
 *  @param bytes    数据
 */
-(void)request:(ReqParamVo *)reqParamVo didSendBytes:(long long)sendDataLength totalDataLength:(long long)totalDataLength;

/**
 *  <AUTHOR> 2014-11-26 11:11:13
 *
 *  请求关闭
 */
-(void)close:(ReqParamVo *)reqParamVo;

@end
