//
//  TKTrafficManager.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 16/8/10.
//  Copyright © 2016年 com.thinkive. All rights reserved.
//
//  V2.0.0 修改时间：2017-07-28
//  1:修改了公共参数里面关于app_version的版本号的定义，现在这个字段传的是原生版本号，以前会涉及H5版本号
//  2:新增了h5_version代表H5的版本号

//  V2.0.1 修改时间：2017-08-04
//  1:增加了方法+ (void)changeUserType:(NSString *)userType userAccount:(NSString *)userAccount;
//    实现了切换账户的功能

//  V2.0.2 修改时间：2017-08-07
//  1:修改了统计SDK，切入后台时候未提交埋点数据的问题。
//  2:解决统计SDK线程安全导致的SUID为空的问题
//  3:解决了统计SDK计算结束时间的逻辑误差。

//  V2.0.3 修改时间：2017-08-18
//  1:修改了统计SDK切入后台时候部分埋点数据没有提交的问题。
//  2:修改了统计SDK关于APP杀死时候丢失部分数据的问题
//  3:修改了统计SDK关于页面统计时长的问题，由浮点型改成整形

//  V2.0.4 修改时间：2017-08-28
//  1:修改了初始化拉配置的逻辑（修改为初始化就拉取配置，原来是启动统计才拉取)

//  V2.0.5 修改时间：2017-08-30
//  1:修改了动态埋点对Control组件的支持

//  V2.0.6 修改时间：2017-11-16
//  1:修改了地图定位的BUG
//  2:支持高德IP定位功能，配置文件增加高德IP定位授权Key配置
//    <item name="ipKey" value="KfZAnJoqEDtmwhTjxZ+C3JhhR1r9/yRrLlDXduxkb/YeT9CVhloh0e0kmq2PagBU" description="高德获取IP服务授权key(加密后)" />

//  V2.1.0 修改时间：2017-12-14
//  1：修改了动态埋点的保存接口入参
//  2: 修改了切换账户的逻辑，用userType和clientId组合标示各种账号，增强扩展性

//  V2.1.1 修改时间：2018-03-27
//  1：支持事件的访问周期统计
//  2: 优化了页面的访问周期统计逻辑
//  3: 统计App访问周期事件，增加入参menu_id,记录当前访问的页面

//  V2.1.2 修改时间：2018-06-04
//  1：优化SDK后台运行任务的机制策略
//  2: 崩溃和网络异常统计，增加入参menu_id,记录当前访问的页面
//  3: 增加地理位置为()的过滤
//  4: 去掉xpath_ios的错误入参
//  5: 优化崩溃的处理逻辑
//  6: 优化网络异常的统计逻辑
//  7: 优化页面访问ID的记录逻辑

//  V2.1.3 修改时间：2018-11-04
//  1:增加切到后台的阀值
//  2:优化策略更新逻辑，支持实时生效
//  3:支持数据压缩
//  4:优化超过缓存最大条数的逻辑处理
//  5:增加地理位置省市的统一处理，统一去掉省，市字眼
//  6：支持上传时间段控制,例如：09:00-10:00|14:00-15:00,为空代表不限制

//  V2.1.4 修改时间：2019-02-26
//  1:增加对上传崩溃日志内容的大小限制
//  2:优化埋点调试工具

//  V2.1.5 修改时间：2019-03-26
//  1:增加+ (void)visitPage:(NSString *)objectID attributes:(NSDictionary *)attributes;API
//    支持页面访问时候增加自定义页面访问属性

//  V2.1.6 修改时间：2019-08-26
//  1:增加页面开始访问的事件记录，原来的版本只是记录页面结束时候的事件

//  V2.1.7 修改时间：2019-09-12
//  1:完善页面开始访问的结束事件，允许带参数ObjectId
//  2:优化上传策略配置重服务器更新时候实时生效的功能
//  3:在线调试模式适配ios13系统

//  V2.1.8 修改时间：2019-12-10
//  1:完善APP进入后台产生的结束事件中关于应用上次结束时间的处理逻辑
//  2:引入系统启动时间来代替系统时间，统计页面访问周期时长等功能，解决客户改系统时间引起的统计不准的问题。
//  3:优化H5后台加载，触发页面访问事件，引起页面统计时长很大的问题。
//  4:优化获取IP的逻辑
//  5:修改退出切换游客的逻辑，不强制情况客户名称和ID，以上面传的为准

//  V2.1.9 修改时间：2020-02-24
//  1:优化去掉老版本SDK获取stampid和suid的逻辑，统一用设备id来代替

//  V2.2.0 修改时间：2023-01-10
//  1:增加统计的公共自定义参数

#import <Foundation/Foundation.h>
#import "UIView+TKPunctuate.h"
#import "UIControl+TKPunctuate.h"
#import "TKServiceDelegate.h"

/**
 *关于模块旁路的配置文件
 */
#define CACHE_MODULE_BYPASS_CONF @"cache_module_bypass"

/**
 REALTIME只在“模拟器”设备模式下有效，其它情况下的REALTIME会改为使用最小间隔或数目发送策略。
 */
typedef enum {
    TKTrafficReportPolicy_RealTime = 0,          //实时发送              (只在“模拟器”设备下有效)
    TKTrafficReportPolicy_Batch = 1,             //启动发送
    TKTrafficReportPolicy_Send_Interval = 2,     //最小间隔发送           (默认是 90s)
    TKTrafficReportPolicy_Send_Num = 3,          //最小数目发送           (默认是 10条)
    TKTrafficReportPolicy_Send_IntervalOrNum = 4,//最小间隔或数目发送      (默认是 满足10条记录或者90s钟就发送一次)
    TKTrafficReportPolicy_SendOnBackgound = 5,   //APP切入后台时发送
    TKTrafficReportPolicy_None                   //默认策略
} TKTrafficReportPolicy;

static void *const TKTrafficGlobalLoggingQueueIdentityKey = (void *)&TKTrafficGlobalLoggingQueueIdentityKey;

/**
 *  <AUTHOR> 2016-08-10 18:08:19
 *
 *  流量统计,使用步骤为：调用相关设置-------->启动统计-------->统计页面+事件
 */
@interface TKTraffic : NSObject

/**
 *  禁用默认的初始化动作，全部采用静态调用
 */
- (instancetype)init NS_UNAVAILABLE;

/**
 *  <AUTHOR> 2016-08-15 10:08:51
 *
 *  统计队列
 *
 *  @return
 */
+ (dispatch_queue_t)trafficQueue;

#pragma mark 初始化配置

/**
 * 设置请求公共参数
 */
+(void)setCommonReqParam:(NSMutableDictionary *)commonReqParam;

/**
 *  <AUTHOR> 2016-08-10 23:08:18
 *
 *  设置APP的版本
 *
 *  @param appVersion
 */
+ (void)setAppVersion:(NSString *)appVersion;

/**
 *  <AUTHOR> 2016-08-11 09:08:00
 *
 *   开启CrashReport收集, 默认YES(开启状态).
 *
 *  @param value 设置为NO,可关闭CrashReport收集功能.
 */
+ (void)setCrashReportEnabled:(BOOL)value;

/**
 *  <AUTHOR> 2016-08-11 09:08:21
 *
 *  设置是否开启background模式, 默认YES.
 *
 *  @param value 为YES,SDK会确保在app进入后台的短暂时间保存日志信息的完整性，对于已支持background模式和一般app不会有影响.如果该模式影响某些App在切换到后台的功能，也可将该值设置为NO.
 */
+ (void)setBackgroundTaskEnabled:(BOOL)value;

/**
 *  <AUTHOR> 2016-12-01 13:12:01
 *
 *  是否仅Wifi环境才发送请求，默认NO
 *
 *  @param value
 */
+ (void)setOnlyWifiEnabled:(BOOL)value;

/**
 *  <AUTHOR> 2016-08-11 10:08:37
 *
 *  设置是否对日志信息进行压缩, 默认NO(不压缩).
 *
 *  @param value
 */
+ (void)setCompressEnabled:(BOOL)value;

/**
 *  <AUTHOR> 2016-08-11 10:08:37
 *
 *  设置是否对日志信息进行加密, 默认NO(不加密).
 *
 *  @param value
 */
+ (void)setEncryptEnabled:(BOOL)value;

/**
 *  <AUTHOR> 2016-08-12 13:08:28
 *
 *  加密key
 *
 *  @param key 加密key
 */
+ (void)setEncryptKey:(NSString *)key;

/**
 *  <AUTHOR> 2016-08-16 22:08:11
 *
 *  发送策略
 *
 *  @param reportPolicy 上传策略
 */
+ (void)setLogReportPolicy:(TKTrafficReportPolicy)reportPolicy;

/**
 *  <AUTHOR> 2016-08-11 10:08:39
 *
 *  当reportPolicy == TKTrafficReportPolicy_Send_Interval 时设定log发送间隔
 *
 *  @param second 单位为秒
 */
+ (void)setLogSendInterval:(double)second;

/**
 *  <AUTHOR> 2016-08-11 10:08:13
 *
 *  当reportPolicy == TKTrafficReportPolicy_Send_Num 时设定log发送数目间隔
 *
 *  @param num 数目
 */
+ (void)setLogSendNum:(NSUInteger)num;

/**
 *  <AUTHOR> 2016-12-13 16:12:51
 *
 *  设置最大缓存的数据数目
 *
 *  @param cacheMaxNum
 */
+ (void)setLogCacheMaxNum:(NSUInteger)cacheMaxNum;

/**
 *  <AUTHOR> 2016-12-13 16:12:51
 *
 *  设置上传时间段,例如：09:00-10:00|14:00-15:00,为空代表不限制
 *
 *  @param uploadTime
 */
+ (void)setLogUploadTime:(NSString *)uploadTime;

/**
 *  <AUTHOR> 2016-12-13 16:12:51
 *
 *  设置进入后台重算启动时间间隔(单位:秒)，默认是30
 *
 *  @param restartAppTime
 */
+ (void)setLogRestartAppTime:(double)restartAppTime;

/**
 *  <AUTHOR> 2017-03-28 00:03:55
 *
 *  设置需要统计上传的请求超时时间 单位毫秒
 *
 *  @param milliSeconds 毫秒数
 */
+ (void)setReqErrorTimeOut:(double)milliSeconds;

/**
 *  <AUTHOR> 2017-03-28 00:03:03
 *
 *  获取需要统计上传的请求超时时间  单位毫秒
 */
+ (double)reqErrorTimeOut;

/**
 *  <AUTHOR> 2017-03-27 15:03:21
 *
 *  设置操作会话sid
 *
 *  @param sid 操作会话sid
 */
+ (void)setSID:(NSString *)sid;

/**
 *  <AUTHOR> 2017-03-27 15:03:13
 *
 *  获取操作会话的sid
 *
 *  @return 操作会话的sid
 */
+ (NSString *)SID;

/**
 *  <AUTHOR> 2016-12-06 00:12:18
 *
 *  设置用户的SUID
 *
 *  @param suid
 */
+ (void)setSUID:(NSString *)suid;

/**
 *  <AUTHOR> 2016-12-11 00:12:49
 *
 *  获取用户的SUID
 *
 *  @return
 */
+ (NSString *)SUID;

/**
 *  <AUTHOR> 2016-12-06 00:12:41
 *
 *  设置设备的StampId
 *
 *  @param stampId
 */
+ (void)setStampId:(NSString *)stampId;

/**
 *  <AUTHOR> 2016-12-11 00:12:15
 *
 *  获取设备的StampId
 *
 *  @return
 */
+ (NSString *)stampId;

/**
 *  <AUTHOR> 2016-12-05 10:12:26
 *
 *  设置用户的登录状态
 *
 *  @param isLogin 是否登录
 */
+ (void)setUserLoginState:(BOOL)isLogin;

/**
 *  <AUTHOR> 2016-12-11 00:12:34
 *
 *  获取用户的登录状态
 *
 *  @return 是否登录
 */
+ (BOOL)userLoginState;

/**
 *  <AUTHOR> 2016-12-06 00:12:26
 *
 *  设置登录用户类别（1:资金账号 2:QQ 3:Wechat 4:游客 5:手机号 6:员工账号）
 *
 *  @param userType
 */
+ (void)setUserType:(NSString *)userType;

/**
 *  <AUTHOR> 2016-12-11 00:12:18
 *
 *  获取登录用户类别
 *
 *  @return
 */
+ (NSString *)userType;

/**
 *  <AUTHOR> 2016-12-06 00:12:24
 *
 *  设置用户的资金账号
 *
 *  @param clientId
 */
+ (void)setClientId:(NSString *)clientId;

/**
 *  <AUTHOR> 2016-12-11 00:12:51
 *
 *  获取用户的资金账户
 *
 *  @return
 */
+ (NSString *)clientId;

/**
 *  <AUTHOR> 2016-12-06 00:12:31
 *
 *  设置用户的微信账号
 *
 *  @param wxOpenId
 */
+ (void)setWXOpenId:(NSString *)wxOpenId;

/**
 *  <AUTHOR> 2016-12-11 00:12:21
 *
 *  获取用户微信账户
 *
 *  @return
 */
+ (NSString *)wxOpenId;

/**
 *  <AUTHOR> 2016-12-06 00:12:22
 *
 *  设置用户的QQ号码
 *
 *  @param qqOpenId
 */
+ (void)setQQOpenId:(NSString *)qqOpenId;

/**
 *  <AUTHOR> 2016-12-11 00:12:49
 *
 *  获取用户QQ号码
 *
 *  @return
 */
+ (NSString *)qqOpenId;

/**
 *  <AUTHOR> 2016-12-06 00:12:03
 *
 *  设置用户登录名称
 *
 *  @param loginName
 */
+ (void)setLoginName:(NSString *)loginName;

/**
 *  <AUTHOR> 2016-12-11 00:12:17
 *
 *  获取用户登录名称
 *
 *  @return
 */
+ (NSString *)loginName;

/**
 *  <AUTHOR> 2016-12-07 10:12:29
 *
 *  设置回流码
 *
 *  @param backCode
 */
+ (void)setBackCode:(NSString *)backCode;

/**
 *  <AUTHOR> 2016-12-11 00:12:54
 *
 *  获取回流码
 *
 *  @return
 */
+ (NSString *)backCode;

/**
 *  <AUTHOR> 2016-12-11 12:12:44
 *
 *  获取全部埋点行为的配置Map  key为：ObjectId value为Map
 *
 *  @return
 */
+ (NSDictionary *)trafficConfMap;

/**
 *  <AUTHOR> 2016-12-11 12:12:44
 *
 *  获取可视化自动埋点行为的配置Map  key为：ClassPath_XPath或者H5Url_XPath  value为Map
 *
 *  @return
 */
+ (NSDictionary *)trafficKSHConfMap;

/**
 *  <AUTHOR> 2016-12-11 12:12:17
 *
 *  获取固定埋点行为的配置Map  key为：ObjectId_EventType_ActionId  value为Map
 *
 *  @return
 */
+ (NSDictionary *)trafficGDConfMap;

/**
 *  <AUTHOR> 2016-12-11 13:12:00
 *
 *  获取H5可视化埋点行为的配置Map key为：H5Url  value为数组
 *
 *  @return
 */
+ (NSDictionary *)trafficH5ConfMap;

/**
 *  <AUTHOR> 2016-12-29 21:12:26
 *
 *  获取模块旁路配置
 *
 *  @return
 */
+ (NSDictionary *)moduleByPassMap;

/**
 *  <AUTHOR> 2016-12-29 21:12:51
 *
 *  是否旁路
 *
 *  @param moduleId 模块名称
 *
 *  @return
 */
+ (BOOL)isByPassForModule:(NSString *)moduleId;

#pragma mark 开启统计

/**
 *  <AUTHOR> 2016-08-11 10:08:05
 *
 *  检测配置
 *
 *  @return
 */
+ (void)checkAppTrafficConfigVersion;

/**
 *  <AUTHOR> 2016-08-12 14:08:16
 *  初始化统计模块
 *
 */
+ (void)start;

/**
 *  <AUTHOR> 2016-08-11 10:08:21
 *
 *  初始化统计模块
 *
 *  @param appKey 授权key
 */
+ (void)startWithAppkey:(NSString *)appKey appCode:(NSString *)appCode;

/**
 *  <AUTHOR> 2016-08-11 10:08:03
 *
 *  初始化统计模块
 *
 *  @param appKey 授权key
 *  @param rp     发送策略, 默认值为：Send_IntervalOrNum，即“最小间隔或数目发送”模式
 *  @param cid    渠道名称,为nil或@""时, 默认为@"App Store"渠道
 */
+ (void)startWithAppkey:(NSString *)appKey appCode:(NSString *)appCode reportPolicy:(TKTrafficReportPolicy)rp channelId:(NSString *)cid;

#pragma mark 切换账户

/**
 *  <AUTHOR> 2016-12-06 00:12:26
 *
 *  切换登录用户类别（1:资金账号 2:QQ 3:Wechat 4:游客 5:手机号 6:员工账号）
 *
 *  @param userType     用户类别
 *  @param userAccount  用户账号
 */
+ (void)changeUserType:(NSString *)userType userAccount:(NSString *)userAccount;

/**
 *  <AUTHOR> 2016-12-06 00:12:26
 *
 *  切换登录用户类别（1:资金账号 2:QQ 3:Wechat 4:游客 5:手机号 6:员工账号）
 *
 *  @param userType     用户类别
 *  @param userAccount  用户账号
 *  @param userName     用户名称
 */
+ (void)changeUserType:(NSString *)userType userAccount:(NSString *)userAccount userName:(NSString *)userName;

#pragma mark 页面访问统计

/**
 *  <AUTHOR> 2016-08-11 10:08:26
 *
 *  访问页面轨迹设置
 *
 *  @param objectID   统计的页面的ObjectID
 *  @param attributes 统计的页面的业务属性字段
 */
+ (void)visitPage:(NSString *)objectID attributes:(NSDictionary *)attributes;

/**
 *  <AUTHOR> 2016-08-11 10:08:26
 *
 *  访问页面轨迹设置
 *
 *  @param objectID 统计的页面的ObjectID
 */
+ (void)visitPage:(NSString *)objectID;

/**
 *  <AUTHOR> 2016-08-11 10:08:26
 *
 *  访问页面结束
 *
 */
+ (void)visitPageFinish:(NSString *)objectID;

/**
 *  <AUTHOR> 2016-08-11 10:08:26
 *
 *  访问页面结束
 *
 */
+ (void)visitPageFinish;

/**
 *  <AUTHOR> 2016-08-11 10:08:26
 *
 *  刷新页面控制器的状态
 *
 */
+ (void)freshVisitPageCtrlState:(NSString *)ctrlUUID isShow:(BOOL)isShow;

#pragma mark 事件统计

/**
 *  <AUTHOR> 2016-12-08 14:12:42
 *
 *  统计可视化埋点的事件
 *
 *  @param pagePath    组件所在容器的类名
 *  @param xpath       组件唯一路径值
 *  @param attributes  自定义属性
 */
+ (void)event:(NSString *)pagePath xpath:(NSString *)xpath attributes:(NSDictionary *)attributes;

/**
 *  <AUTHOR> 2016-12-08 14:12:48
 *
 *  统计固定埋点的事件
 *
 *  @param objectId   控件对象ID
 *  @param eventType  事件类型
 *  @param actionId   事件ID
 *  @param attributes 自定义属性
 */
+ (void)event:(NSString *)objectId eventType:(NSString *)eventType actionId:(NSString *)actionId attributes:(NSDictionary *)attributes;

#pragma mark 事件访问统计

/**
 *  <AUTHOR> 2016-12-08 14:12:48
 *
 *  开始统计固定埋点的访问
 *
 *  @param objectId   控件对象ID
 *  @param eventType  事件类型
 *  @param actionId   事件ID
 *  @param attributes 自定义属性
 *  @param timeKey    自定义时间Key
 */
+ (void)visitEvent:(NSString *)objectId eventType:(NSString *)eventType actionId:(NSString *)actionId attributes:(NSDictionary *)attributes timeKey:(NSString *)timeKey;

/**
 *  <AUTHOR> 2016-12-08 14:12:48
 *
 *  结束统计固定埋点的访问
 *
 *  @param objectId   控件对象ID
 *  @param eventType  事件类型
 *  @param actionId   事件ID
 */
+ (void)visitEventFinish:(NSString *)objectId eventType:(NSString *)eventType actionId:(NSString *)actionId;

#pragma mark 崩溃异常统计

/**
 *  <AUTHOR> 2016-08-11 17:08:41
 *
 *  统计崩溃，这个要同步操作
 *
 *  @param exception_type    崩溃日志标题
 *  @param exception_content 崩溃日志内容
 */
+ (void)logCrashException:(NSString *)exception_type content:(NSString *)exception_content;

#pragma mark 请求错误事件统计

/**
 *  <AUTHOR> 2017-03-28 01:03:12
 *
 *  统计请求错误事件
 *
 *  @param reqUrl     请求URL
 *  @param funcNo     功能号
 *  @param errorNo    错误号
 *  @param errorInfo  错误信息
 *  @param errorState 错误类型   0:网络超时 1:业务超时 2:网络异常
 *  @param beginTime  请求开始时间
 *  @param endTime    请求结束时间
 *  @param serverTime 服务器执行时间
 */
+ (void)logRequestError:(NSString *)reqUrl funcNo:(NSString*)funcNo errorNo:(int)errorNo errorInfo:(NSString *)errorInfo errorState:(NSString *)errorState beginTime:(NSTimeInterval)beginTime endTime:(NSTimeInterval)endTime serverTime:(NSTimeInterval)serverTime;

#pragma mark 可视化埋点保存数据

/**
 *  <AUTHOR> 2016-12-09 20:12:39
 *
 *  保存配置
 *
 *  @param objectId        控件ID
 *  @param xpath           组件唯一路径XPath
 *  @param classPath       组件所在的容器类
 *  @param h5Path          H5的URL路径
 *  @param callBackFunc    回调函数
 */
+ (void)saveTrafficConf:(NSString *)objectId xpath:(NSString *)xpath classPath:(NSString *)classPath h5Path:(NSString *)h5Path callBackFunc:(CallBackFunc)callBackFunc;

#pragma mark 刷新内存埋点缓存

/**
 *  <AUTHOR> 2016-08-25 19:08:24
 *
 *  刷新内存缓冲区数据，保存到本地给下次发送
 */
+ (void)flush;

/**
 *  <AUTHOR> 2016-08-25 19:08:24
 *
 *  恢复内存缓冲区数据
 */
+ (void)recover;

/**
 *  <AUTHOR> 2016-08-25 19:08:24
 *
 *  删除内存缓冲区数据
 */
+ (void)clear;

#pragma mark 埋点管理编辑器

/**
 *  <AUTHOR> 2016-12-09 09:12:10
 *
 * 开启可视化埋点编辑器
 */
+ (void)startEditor;

/**
 *  <AUTHOR> 2016-12-09 09:12:10
 *
 * 关闭可视化埋点编辑器
 */
+ (void)stopEditor;

/**
 *  <AUTHOR> 2016-12-09 09:12:10
 *
 *  注册浏览器对象，一般在webViewDidFinishLoad的事件中调用
 */
+(void)registerWebview:(id)webview;

#pragma mark 云测操作日志记录

/**
 *  <AUTHOR> 2016-12-09 09:12:10
 *
 *  写操作日志到文件中，方便云测模式
 */
+(void)writeTrafficOperLogWithTitle:(NSString *)title data:(NSObject *)data isAsync:(BOOL)isAsync;

@end
