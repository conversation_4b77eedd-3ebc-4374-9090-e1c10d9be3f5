//
//  TabView+TKNIStyleable.h
//  TKAppBase_V1
//
//  Created by liubao on 15-9-10.
//  Copyright (c) 2015年 liubao. All rights reserved.
//
#import "TKTabView.h"

@class TKNICSSRuleset;
@class TKNIDOM;

@interface TKTabView (TKNIStyleable)

/**
 * Applies the given rule set to this navigation bar. Use tkapplyNavigationBarStyleWithRuleSet:inDOM: instead
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplyTabViewStyleWithRuleSet:(TKNICSSRuleset *)ruleSet DEPRECATED_ATTRIBUTE;

/**
 * Applies the given rule set to this navigation bar.
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplyTabViewStyleWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

@end
