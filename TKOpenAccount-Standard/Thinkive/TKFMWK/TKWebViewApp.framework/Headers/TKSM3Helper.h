//
//  TKSM3Helper.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/2/26.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface TKSM3Helper : NSObject

/**
 *  SM3摘要算法
 *
 *  @param str 要编码的字符串
 *
 *  @return 编码后的字符串
 */
+(NSString *)sm3Hash:(NSString *)str;

/**
 *  <AUTHOR> 2016-11-16 10:11:31
 *
 *  SM3摘要算法
 *
 *  @param data 要编码的二进制
 *
 *  @return 编码后的字符串
 */
+(NSString *)sm3HashData:(NSData *)data;

/**
 *  <AUTHOR> 2016-11-16 10:11:31
 *
 *  MD5摘要算法
 *
 *  @param data   要编码的二进制
 *  @param length 要编码的二进制长度
 *
 *  @return 编码后的字符串
 */
+(NSString *)sm3HashBytes:(char *)bytes length:(uint32_t)length;

@end
