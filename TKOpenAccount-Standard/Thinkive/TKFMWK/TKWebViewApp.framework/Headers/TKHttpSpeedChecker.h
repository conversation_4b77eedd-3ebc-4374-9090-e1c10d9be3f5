//
//  TKHttpSpeedChecker.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2023/3/3.
//  Copyright © 2023 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKHttpAddress.h"
#import "TKHttpServer.h"

@class TKHttpSpeedChecker;

/**
 *  <AUTHOR> 2015-08-24 17:08:42
 *
 *  网络监听代理
 */
@protocol TKHttpSpeedCheckerDelegate <NSObject>

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  检测结果
 *
 *  @param address
 */
-(void)httpSpeedCheckResult:(TKHttpSpeedChecker *)httpSpeedChecker;

@end

/**
 * 测速工具类
 */
@interface TKHttpSpeedChecker : NSObject

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  网络测速代理
 */
@property(nonatomic,weak)id<TKHttpSpeedCheckerDelegate> delegate;

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  服务器名称
 */
@property(nonatomic,readonly)TKHttpAddress *netAddress;

/**
 * 当前类名
 */
@property(nonatomic,readonly)NSString *className;

/**
 *  <AUTHOR> 2016-01-12 01:01:53
 *
 *  初始化启动测速标示
 */
@property(nonatomic,assign)TKHttpTestSpeedMode testSpeedMode;

/**
 *  <AUTHOR> 2015-03-28 18:03:42
 *
 *  初始化
 *
 *  @param netAddress 地址
 *  @param serverName 服务器名称
 *
 *  @return
 */
-(TKHttpSpeedChecker *)initWithNetAddress:(TKHttpAddress *)netAddress;

/**
 * 站点测速
 */
-(void)startTest;

/**
 * 取消测速
 */
-(void)stopTest;

@end
