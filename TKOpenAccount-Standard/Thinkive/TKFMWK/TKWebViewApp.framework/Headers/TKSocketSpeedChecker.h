//
//  TKSocketSpeedChecker.h
//  TKAppBase_V1
//
//  Created by liubao on 15-3-28.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKNetAddress.h"
#import "TKServer.h"
#import "TKCommonQueue.h"

@class TKSocketSpeedChecker;

/**
 *  <AUTHOR> 2015-08-24 17:08:42
 *
 *  网络监听代理
 */
@protocol TKSocketSpeedCheckerDelegate <NSObject>

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  检测结果
 *
 *  @param address
 */
-(void)socketSpeedCheckResult:(TKSocketSpeedChecker *)socketSpeedChecker;

@end

/**
 *  <AUTHOR> 2016-08-11 11:08:45
 *
 *  异步队列
 */
static TKCommonQueue *_TKFastSocketQueue;

static NSMutableDictionary *_TKFastSocketDic;

/**
 *  <AUTHOR> 2015-03-28 18:03:12
 *
 *  心跳检测
 */
@interface TKSocketSpeedChecker : NSObject

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  网络测速代理
 */
@property(nonatomic,weak)id<TKSocketSpeedCheckerDelegate> delegate;

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  服务器名称
 */
@property(nonatomic,readonly)NSString *gateWayName;

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  服务器名称
 */
@property(nonatomic,readonly)TKNetAddress *netAddress;

/**
 * 当前类名
 */
@property(nonatomic,readonly)NSString *className;

/**
 *  <AUTHOR> 2016-01-12 01:01:53
 *
 *  初始化启动测速标示
 */
@property(nonatomic,assign)TKSocketTestSpeedMode testSpeedMode;

/**
 *  <AUTHOR> 2015-03-28 18:03:42
 *
 *  初始化
 *
 *  @param netAddress 地址
 *  @param gateWayName 服务器名称
 *  @param scanInterval 心跳时间
 *
 *  @return
 */
-(TKSocketSpeedChecker *)initWithNetAddress:(TKNetAddress *)netAddress gateWayName:(NSString *)gateWayName;

/**
 * 初始化Socket
 */
-(void)initInnerSocket;

/**
 * 释放Socket
 */
-(void)releaseInnerSocket;

/**
 *  <AUTHOR> 2015-03-28 18:03:35
 *
 *  重置检测状态
 */
-(void)resetCheckTestFlag;

/**
 *  <AUTHOR> 2015-03-28 18:03:35
 *
 *  开始线程心跳检测
 */
-(void)start;

/**
 * 进行线程心跳检测逻辑
 */
-(void)check;

/**
 *  <AUTHOR> 2015-03-28 18:03:35
 *
 *  停止线程心跳检测
 */
-(void)stop;

/**
 * 保存最快站点到缓存
 */
-(void)saveFastSocket;

/**
 * 回调测速结果
 */
-(void)callbackSocketSpeedCheckResult;

@end
