//
//  TKUpdateManager.h
//  TKApp
//
//  Created by liu<PERSON> on 15-2-3.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "TKDownloadDelegate.h"

/**
 *  <AUTHOR> 2015-04-30 12:04:22
 *
 *  是否第一次装软件:软件升级重装，软件卸载重装,软件第一次装 等都算第一次
 */
#define CACHE_ISFIRSTINSTALL @"isFirstInstall"

/**
 *  <AUTHOR> 2016-04-28 18:04:29
 *  是否已经完成了初始化的动作，copy完成H5
 */
#define CACHE_APP_FINISH_INIT @"cache_tkAppFinishInit"

/**
 *  <AUTHOR> 2015-06-05 12:06:41
 *
 *  更新完成通知
 *
 */
#define NOTE_UPDATE_SUCCESS @"note_update_success"

/**
 *  <AUTHOR> 2015-06-05 12:06:41
 *
 *  更新失败通知
 *
 */
#define NOTE_UPDATE_FAILED @"note_update_failed"

/**
 *  <AUTHOR> 2015-06-05 12:06:41
 *
 *  更新模式
 *
 */
typedef enum {
    TKUpdateMode_Alert,  //强制更新
    TKUpdateMode_Confirm //非强制更新
}TKUpdateMode;

/**
 *  <AUTHOR> 2015-06-05 12:06:41
 *
 *  按钮点击函数
 *
 */
typedef void(^UpdateBtnFunc)(void);

/**
 *  <AUTHOR> 2015-06-05 12:06:41
 *
 *  自定义UI代理
 *
 */
@protocol TKUpdateUIDelegate <NSObject>

@required

/**
 *  <AUTHOR> 2015-06-05 12:06:41
 *
 *  自定义UI提示框
 *  @param updateMode 更新模式
 *  @param updateInfo 更新信息
 *  @param updateBtnFunc 更新按钮执行函数
 *
 */
-(void)showUpdateUIWindow:(TKUpdateMode)updateMode updateInfo:(NSDictionary *)updateInfo updateBtnFunc:(UpdateBtnFunc)updateBtnFunc;

@optional

/**
 *  <AUTHOR> 2015-06-05 12:06:41
 *
 *  更新完成---注意：这个不是下载完成，下载完成并非更新完成，下载完成后，需要解压覆盖，全部动作完成后才触发更新完成回调
 *
 */
-(void)updateCompleted;

/**
 *  <AUTHOR> 2015-06-05 12:06:41
 *
 *  更新失败---注意：这个不仅仅是下载失败，除了下载失败以外，下载完成后解压失败，覆盖失败等都算更新失败
 *
 */
-(void)updateFailed;

@end

/**
 *  <AUTHOR> 2015-02-03 16:02:19
 *
 *  下载组件
 */
@interface TKUpdateManager : NSObject

/**
 *  <AUTHOR> 2015-02-04 14:02:27
 *
 *  初始化H5的环境
 */
-(void)initH5Context;

/**
 *  <AUTHOR> 2015-02-03 18:02:30
 *
 *  单例模式
 *
 *  @return
 */
+(TKUpdateManager *)shareInstance;

/**
 *  <AUTHOR> 2015-02-03 19:02:29
 *
 *  加载的容器
 */
@property(nonatomic,retain)UIView *contentView;

/**
 *  <AUTHOR> 2016-03-08 16:03:47
 *
 *  是否显示更新框
 */
@property(nonatomic,assign)BOOL isShowUpdateTip;

/**
 *  <AUTHOR> 2016-07-20 10:07:04
 *
 *  更新完成后是否重新加载webview
 */
@property(nonatomic,assign)BOOL isReloadWebView;

/**
 *  <AUTHOR> 2016-07-20 10:07:04
 *
 *  更新解压zip包的密码
 */
@property(nonatomic,copy)NSString *unzippwd;

/**
 *  <AUTHOR> 2016-07-12 21:07:04
 *
 *  下载文件代理
 */
@property (nonatomic,weak) id<TKDownloadDelegate> downloadDelegate;

/**
 *  <AUTHOR> 2016-07-12 21:07:04
 *
 *  更新UI代理
 */
@property (nonatomic,weak) id<TKUpdateUIDelegate> updateUIDelegate;

/**
 *  <AUTHOR> 2015-02-03 16:02:31
 *
 *  更新服务器H5版本
 *
 *  @param url 下载地址
 *  @param version 展示版本号
 *  @param newVersionSn 版本下载内部序号
 *  @param newVersionMd5 版本下载MD5值
 *  @param isUpdateH5 是否更新H5
 *  @param tip 显示的提示框
 */
-(void)updateSoftware:(NSString *)url newVersion:(NSString *)version newVersionSn:(NSString *)versionSn  newVersionMd5:(NSString *)newVersionMd5 isUpdateH5:(BOOL)isUpdateH5 tip:(NSString *)tip;

/**
 *  <AUTHOR> 2015-02-03 16:02:31
 *
 *  更新服务器H5版本
 *
 *  @param url 下载地址
 *  @param version 展示版本号
 *  @param newVersionSn 版本下载内部序号
 *  @param isUpdateH5 是否更新H5
 *  @param tip 显示的提示框
 */
-(void)updateSoftware:(NSString *)url newVersion:(NSString *)version newVersionSn:(NSString *)versionSn isUpdateH5:(BOOL)isUpdateH5 tip:(NSString *)tip;

/**
 *  <AUTHOR> 2015-02-03 16:02:31
 *
 *  更新服务器H5版本
 *
 *  @param url 下载地址
 *  @param version 展示版本号
 *  @param newVersionSn 版本下载内部序号
 *  @param isUpdateH5 是否更新H5
 */
-(void)updateSoftware:(NSString *)url newVersion:(NSString *)version newVersionSn:(NSString *)versionSn isUpdateH5:(BOOL)isUpdateH5;

@end
