//
//  TKURLRequestHelper.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2019/3/2.
//  Copyright © 2019年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

//请求帮组类
@interface TKURLRequestHelper : NSObject

/**
 * 功能描述：get请求
 * @param url               请求的地址
 * @param isEncodeUrl       是否进行URL编码
 * @param paramDic          请求入参
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param isAsync           是否异步请求
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)getRequest:(NSString *)url isEncodeUrl:(BOOL)isEncodeUrl paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout isAsync:(BOOL)isAsync completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：get请求
 * @param url               请求的地址
 * @param isEncodeUrl       是否进行URL编码
 * @param paramDic          请求入参
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
* @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)getRequest:(NSString *)url isEncodeUrl:(BOOL)isEncodeUrl paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：get请求 ，默认不进行URL编码
 * @param url               请求的地址
 * @param paramDic          请求入参
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)getRequest:(NSString *)url paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：get请求 ，默认不进行URL编码
 * @param url               请求的地址
 * @param paramDic          请求入参
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)getRequest:(NSString *)url paramDic:(NSDictionary *)paramDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：get请求 ，默认不进行URL编码
 * @param url               请求的地址
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)getRequest:(NSString *)url timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：get请求 ，默认不进行URL编码，超时时间30秒
 * @param url               请求的地址
 * @param paramDic          请求入参
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)getRequest:(NSString *)url paramDic:(NSDictionary *)paramDic completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：get请求 ，默认不进行URL编码，超时时间30秒
 * @param url               请求的地址
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)getRequest:(NSString *)url completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：post请求
 * @param url               请求的地址
 * @param isEncodeUrl       是否进行URL编码
 * @param paramDic          请求入参
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param isAsync           是否异步请求
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)postRequest:(NSString *)url isEncodeUrl:(BOOL)isEncodeUrl paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout isAsync:(BOOL)isAsync completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：post请求
 * @param url               请求的地址
 * @param isEncodeUrl       是否进行URL编码
 * @param paramDic          请求入参
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)postRequest:(NSString *)url isEncodeUrl:(BOOL)isEncodeUrl paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：post请求 ，默认不进行URL编码
 * @param url               请求的地址
 * @param paramDic          请求入参
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)postRequest:(NSString *)url paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：post请求，默认不进行URL编码
 * @param url               请求的地址
 * @param paramDic          请求入参
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)postRequest:(NSString *)url paramDic:(NSDictionary *)paramDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：post请求，默认不进行URL编码
 * @param url               请求的地址
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)postRequest:(NSString *)url timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：post请求，默认不进行URL编码，超时时间30秒
 * @param url               请求的地址
 * @param paramDic          请求入参
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)postRequest:(NSString *)url paramDic:(NSDictionary *)paramDic completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：post请求，默认不进行URL编码，超时时间30秒
 * @param url               请求的地址
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)postRequest:(NSString *)url completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：上传文件
 * @param url               请求的地址
 * @param isEncodeUrl       是否进行URL编码
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 * @param uploadProgressHandler  上传进度回调函数
 */
+(NSURLSessionTask *)uploadRequest:(NSString *)url isEncodeUrl:(BOOL)isEncodeUrl paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler uploadProgressHandler:(void (^)(NSUInteger bytesTotal,NSUInteger bytesLoaded, CGFloat progress))uploadProgressHandler;

/**
 * 功能描述：上传文件
 * @param url               请求的地址
 * @param isEncodeUrl       是否进行URL编码
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)uploadRequest:(NSString *)url isEncodeUrl:(BOOL)isEncodeUrl paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：上传文件，默认不进行URL编码
 * @param url               请求的地址
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)uploadRequest:(NSString *)url paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：上传文件，默认不进行URL编码
 * @param url               请求的地址
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)uploadRequest:(NSString *)url paramDic:(NSDictionary *)paramDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：上传文件，默认不进行URL编码，超时时间30秒
 * @param url               请求的地址
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)uploadRequest:(NSString *)url paramDic:(NSDictionary *)paramDic completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：下载请求
 * @param url               请求的地址
 * @param isEncodeUrl        是否进行URL编码
 * @param paramDic          请求入参
 * @param headerFiledDic     请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler   下载完成回调函数
 * @param downloadProgressHandler  下载进度回调函数
 */
+(NSURLSessionTask *)downloadRequest:(NSString *)url isEncodeUrl:(BOOL)isEncodeUrl paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout  completionHandler:(void (^)(NSDictionary *result))completionHandler downloadProgressHandler:(void (^)(NSUInteger bytesTotal,NSUInteger bytesLoaded, CGFloat progress))downloadProgressHandler;

/**
 * 功能描述：下载文件
 * @param url               请求的地址
 * @param isEncodeUrl       是否进行URL编码
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)downloadRequest:(NSString *)url isEncodeUrl:(BOOL)isEncodeUrl paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：下载文件，默认不进行URL编码
 * @param url               请求的地址
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param headerFiledDic    请求头字段
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)downloadRequest:(NSString *)url paramDic:(NSDictionary *)paramDic headerFiledDic:(NSDictionary *)headerFiledDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;

/**
 * 功能描述：下载文件，默认不进行URL编码
 * @param url               请求的地址
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param timeout           请求的超时时间，单位秒
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)downloadRequest:(NSString *)url paramDic:(NSDictionary *)paramDic timeout:(float)timeout completionHandler:(void (^)(NSDictionary *result))completionHandler;
/**
 * 功能描述：下载文件，默认不进行URL编码，超时时间30秒
 * @param url               请求的地址
 * @param paramDic          请求入参，文件的key以@@F结尾
 * @param completionHandler  请求完成回调函数
 */
+(NSURLSessionTask *)downloadRequest:(NSString *)url paramDic:(NSDictionary *)paramDic completionHandler:(void (^)(NSDictionary *result))completionHandler;

@end
