//
//  TKGIFImage.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2017/9/30.
//  Copyright © 2017年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface TKGIFImage : UIImage

///-----------------------
/// @name Image Attributes
///-----------------------

/**
 A C array containing the frame durations.
 
 The number of frames is defined by the count of the `images` array property.
 */
@property (nonatomic, readonly) NSTimeInterval *frameDurations;

/**
 Total duration of the animated image.
 */
@property (nonatomic, readonly) NSTimeInterval totalDuration;

/**
 Number of loops the image can do before it stops
 */
@property (nonatomic, readwrite) NSUInteger loopCount;

@property (nonatomic, readonly) NSMutableArray *images;

- (UIImage*)getFrameWithIndex:(NSUInteger)idx;

@end
