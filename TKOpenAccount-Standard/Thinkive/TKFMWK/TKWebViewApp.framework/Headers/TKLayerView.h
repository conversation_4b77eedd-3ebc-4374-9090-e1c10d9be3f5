//
//  TKLayerView.h
//  TKComponent
//
//  Created by liubao on 14-11-15.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKMBProgressHUD.h"

/**
 位置
 */
typedef enum {
    /**
     * 顶部
     */
    TKLayerPosition_Top,
    /**
     * 中间
     */
    TKLayerPosition_Center,
    /**
     *  <AUTHOR> 2016-03-01 18:03:20
     *
     *  底部
     */
    TKLayerPosition_Bottom
}TKLayerPosition;

/**
 *  相关弹层组件
 */
@interface TKLayerView:NSObject

/**
 *  <AUTHOR> 2015-01-04 14:01:32
 *
 *  是否显示加深的背景色
 */
@property(nonatomic,assign)BOOL isDimBackground;

/**
 *  显示缓冲等待层
 *
 *  @param content   显示文本
 */
-(void)showLoading:(NSString *)content;

/**
 *  显示缓冲等待层
 *
 *  @param content   显示文本
 */
-(void)showLoading:(NSString *)content textColor:(NSString *)textColor bgColor:(NSString *)bgColor;

/**
 *  显示缓冲等待层
 *
 *  @param content   显示文本
 */
-(UIView *)showMLoading:(NSString *)content;

/**
 *  显示缓冲等待层
 *
 *  @param content   显示文本
 */
-(UIView *)showMLoading:(NSString *)content textColor:(NSString *)textColor bgColor:(NSString *)bgColor;

/**
 *  显示缓冲等待层
 */
-(void)showLoading;

/**
 * 隐藏缓冲等待层
 */
-(void)hideLoading;

/**
 *  显示短消息提示
 *
 *  @param content 提示的信息
 */
-(void)showTip:(NSString *)content;

/**
 *  显示短消息提示
 *
 *  @param content 提示的信息
 */
-(void)showTip:(NSString *)content position:(TKLayerPosition)position;

/**
 *  显示短消息提示
 *
 *  @param content 提示的信息
 */
-(void)showTip:(NSString *)content textColor:(NSString *)textColor bgColor:(NSString *)bgColor;

/**
 *  显示短消息提示
 *
 *  @param content 提示的信息
 */
-(void)showTip:(NSString *)content position:(TKLayerPosition)position textColor:(NSString *)textColor bgColor:(NSString *)bgColor;

/**
 *  显示短消息提示
 *
 *  @param content 提示的信息
 */
-(UIView *)showMTip:(NSString *)content;

/**
 *  <AUTHOR> 2015-01-04 14:01:10
 *
 *  显示文本提示
 *
 *  @param content 内容
 */
-(void)showText:(NSString *)content;

/**
 *  <AUTHOR> 2015-01-04 14:01:10
 *
 *  显示文本提示
 *
 *  @param content 内容
 */
-(UIView *)showMText:(NSString *)content;

/**
 *  <AUTHOR> 2015-01-04 14:01:17
 *
 *  显示进度条,记得用完要隐藏进度条
 *
 *  @return
 */
-(TKMBProgressHUD *)showProgressHUD;

/**
 *  弹出确认框
 *
 *  @param content 内容
 *  @param title 标题
 *  @param confirmAction 确认动作
 *  @param cancelAction  取消动作
 */
-(void)showConfirm:(NSString *)content title:(NSString *)title confirmAction:(void(^)(void))confirmAction cancelAction:(void(^)(void))cancelAction;

/**
 *  <AUTHOR> 2015-05-06 22:05:01
 *
 *  弹出确认框
 *
 *  @param content       内容
 *  @param title         标题
 *  @param confirmAction 确认动作
 *  @param cancelAction  取消动作
 *  @param confirmBtn    确认按钮
 *  @param cancelBtn     取消按钮
 */
-(void)showConfirm:(NSString *)content title:(NSString *)title confirmAction:(void(^)(void))confirmAction cancelAction:(void(^)(void))cancelAction confirmBtn:(NSString *)confirmBtn cancelBtn:(NSString *)cancelBtn;

/**
 *  弹出提示框
 *
 *  @param content 内容
 *  @param title 标题
 *  @param closeAction  关闭动作
 */
-(void)showAlert:(NSString *)content title:(NSString *)title closeAction:(void(^)(void))closeAction;

/**
 *  弹出提示框
 *
 *  @param content 内容
 *  @param title 标题
 *  @param closeAction  关闭动作
 *  @param isClose      是否关闭
 */
-(void)showAlert:(NSString *)content title:(NSString *)title closeAction:(void(^)(void))closeAction isClose:(BOOL)isClose;

/**
 *  弹出提示框
 *
 *  @param content 内容
 *  @param title 标题
 *
 */
-(void)showAlert:(NSString *)content title:(NSString *)title;

/**
 *  弹出提示框
 *
 *  @param content      内容
 *  @param title        标题
 *  @param buttonText   按钮文本
 *  @param closeAction  关闭动作
 */
-(void)showAlert:(NSString *)content title:(NSString *)title buttonText:(NSString *)buttonText closeAction:(void(^)(void))closeAction;

/**
 *  弹出提示框
 *
 *  @param content      内容
 *  @param title        标题
 *  @param buttonText   按钮文本
 *  @param closeAction  关闭动作
 *  @param isClose      是否关闭
 */
-(void)showAlert:(NSString *)content title:(NSString *)title buttonText:(NSString *)buttonText closeAction:(void(^)(void))closeAction isClose:(BOOL)isClose;

/**
 *  初始化弹出组件
 *
 *  @param frame 区域范围
 *  @param contentView 操作内容面板
 *  @param btnTextColor  按钮文字颜色
 *
 *  @return
 */
-(id)initContentView:(UIView *)contentView withBtnTextColor:(NSString *)btnTextColor;

/**
 *  初始化弹出组件
 *
 *  @param frame 区域范围
 *  @param contentView 操作内容面板
 *  @param btnTextColor  按钮文字颜色
 *  @param cancelBtnTextColor  取消按钮文字颜色
 *
 *  @return
 */
-(id)initContentView:(UIView *)contentView withBtnTextColor:(NSString *)btnTextColor cancelBtnTextColor:(NSString *)cancelBtnTextColor;

/**
 *  初始化弹出组件
 *
 *  @param frame 区域范围
 *  @param contentView 操作内容面板
 *  @param btnTextColor  按钮文字颜色
 *  @param cancelBtnTextColor  取消按钮文字颜色
 *  @param width  宽
 *  @param font  内容字体
 *
 *  @return
 */
-(id)initContentView:(UIView *)contentView withBtnTextColor:(NSString *)btnTextColor cancelBtnTextColor:(NSString *)cancelBtnTextColor withWidth:(CGFloat)width withFont:(UIFont *)font;

/**
 *  <AUTHOR> 2015-06-10 20:06:49
 *
 *  设置弹出层的最大宽度和最大宽度
 *
 *  @param maxWidth  最大宽度
 *  @param maxHeight 最大高度
 */
-(void)setAlertConfirmMaxWidth:(CGFloat)maxWidth maxHeight:(CGFloat)maxHeight;

/**
 *  <AUTHOR> 2015-06-10 20:06:49
 *
 *  设置相关提示的显示时长
 */
-(void)setShowTipDuration:(CGFloat)duration;

@end
