//
//  TKWebView.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/7/3.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <WebKit/WebKit.h>

@class TKWebView;

typedef NS_ENUM(NSInteger, TKWebViewNavigationType)
{
    TKWebViewNavigationTypeLinkActivated,
    TKWebViewNavigationTypeFormSubmitted,
    TKWebViewNavigationTypeBackForward,
    TKWebViewNavigationTypeReload,
    TKWebViewNavigationTypeFormResubmitted,
    TKWebViewNavigationTypeOther = -1
};

/**
 *  兼容代理
 */
@protocol TKWebViewDelegate <NSObject>

@optional

- (void)webViewDidClose:(TKWebView *)webView;

- (void)webViewDidStartLoad:(TKWebView*)webView;

- (void)webViewDidFinishLoad:(TKWebView*)webView;

- (void)webView:(TKWebView*)webView didFailLoadWithError:(NSError*)error;

- (void)webView:(TKWebView*)webView didFailLoadWithIllegalDomain:(NSString*)illegalDomain;

- (BOOL)webView:(TKWebView*)webView shouldStartLoadWithRequest:(NSURLRequest*)request navigationType:(TKWebViewNavigationType)navigationType;

- (void)webView:(TKWebView *)webView changeTitle:(NSString *)title;

- (void)webView:(TKWebView *)webView changeLoading:(BOOL)isLoading;

- (void)webView:(TKWebView *)webView changeProgress:(double)progress;

- (void)webView:(TKWebView *)webView changeURL:(NSURL *)url;

- (void)webView:(TKWebView *)webView changeURL:(NSURL *)url fromURL:(NSURL *)fromUrl;

-(void)webView:(TKWebView *)webView canGoBack:(BOOL)canGoBack;

-(void)webView:(TKWebView *)webView canGoForward:(BOOL)canGoForward;

@end

/**
 *  思迪TKWebView
 */
@interface TKWebView : UIView

/*
 * 内部使用的webView
 */
@property (nonatomic,readonly) id realWebView;

/**
 *  <AUTHOR> 2014-11-27 16:11:52
 *
 * 浏览器对象的名称
 */
@property (nonatomic,copy) NSString *webViewName;

/**
 * 会转接 WKUIDelegate，WKNavigationDelegate 内部未实现的回调
 */
@property (nonatomic,weak) id<TKWebViewDelegate> delegate;

/**
 * 网页标题
 */
@property (nonatomic,copy) NSString *title;

/**
 * 网页加载进度
 */
@property (nonatomic,assign,readonly) double estimatedProgress;

/**
 *  <AUTHOR> 2014-12-11 13:12:50
 *
 *  WebView进度条颜色
 */
@property (nonatomic,strong) UIColor *progressColor;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  是否显示加载过渡loading图
 */
@property(nonatomic,assign) BOOL isShowLoadingWebViewUI;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  是否隐藏加载过渡loading图，在浏览器加载完成后，默认是YES，如果要自己控制隐藏的时机设置为NO
 */
@property(nonatomic,assign) BOOL isHideLoadingUIAfterWebViewFinished;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  加载webview的过渡界面的动画图片名称，loadingWebViewGIFName和完全自定义的loadingWebViewUI，设置一个就可以
 */
@property (nonatomic,copy) NSString *loadingWebViewGIFName;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  加载webview的过渡界面
 */
@property (nonatomic,strong) UIView *loadingWebViewUI;

/**
 * 是否开启支持本地资源访问，默认是YES
 */
@property (nonatomic,assign) BOOL isSupportLocalFileAccess;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  是否支持音视频播放
 */
@property(nonatomic,assign) BOOL allowsInlineMediaPlayback;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  是否开启安全域名校验
 */
@property(nonatomic,assign) BOOL isUseSecurityDomain;

/**
 * 原始请求
 */
@property (nonatomic,strong,readonly) NSURLRequest* originRequest;

/**
 * 当前请求
 */
@property (nonatomic,strong,readonly) NSURLRequest* request;

/**
 * 当前请求地址
 */
@property (nonatomic,strong,readonly) NSURL* URL;

/**
 * 是否在加载中
 */
@property (nonatomic,assign,readonly) BOOL isLoading;

/**
 * 是否可以后退
 */
@property (nonatomic,assign,readonly) BOOL canGoBack;

/**
 * 是否可以前进
 */
@property (nonatomic,assign,readonly) BOOL canGoForward;

/**
 * 历史堆栈
 */
@property (nonatomic,assign,readonly) NSInteger historyCount;

/**
 * 滚动条
 */
@property (nonatomic,strong,readonly) UIScrollView* scrollView;

/**
 * 是否根据视图大小来缩放页面  默认为YES
 */
@property (nonatomic,assign) BOOL scalesPageToFit;

/**
 * 是否可以滑动返回
 */
@property (nonatomic,assign) BOOL allowsBackForwardNavigationGestures;

/**
 * 是否支持后退
 */
@property (nonatomic,assign) BOOL allowsBackNavigationGestures;

/**
 * 是否支持前进
 */
@property (nonatomic,assign) BOOL allowsForwardNavigationGestures;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  加载请求
 */
- (id)loadRequest:(NSURLRequest*)request;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  加载请求
 */
- (id)loadHTMLString:(NSString*)string baseURL:(NSURL*)baseURL;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  加载请求
 */
- (id)loadData:(NSData *)data MIMEType:(NSString *)MIMEType textEncodingName:(NSString *)textEncodingName baseURL:(NSURL *)baseURL;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  回退
 */
- (id)goBack;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  回退
 */
- (void)goBackWithStep:(NSInteger)step;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  前进
 */
- (id)goForward;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  重新加载
 */
- (id)reload;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  重新加载原始URL
 */
- (id)reloadFromOrigin;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  停止加载
 */
- (void)stopLoading;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  显示加载效果
 */
- (void)showLoading:(BOOL)loadedFlag;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  隐藏加载效果
 */
- (void)hideLoading;

/**
 * 改变进度条
 */
-(void)changeLoadingProgress:(CGFloat)progress;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  WKWebView 添加和网页进行交互的方法
 */
- (void)addScriptMessageHandler:(id<WKScriptMessageHandler>)scriptMessageHandler name:(NSString*)name;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  WKWebView 删除和网页进行交互的方法
 */
- (void)removeScriptMessageHandlerForName:(NSString*)name;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  WKWebView 添加自定义JavaScript处理逻辑
 */
- (void)addUserScript:(WKUserScript *)userScript;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  WKWebView 删除自定义JavaScript处理逻辑
 */
- (void)removeAllUserScripts;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  执行JS
 */
- (void)evaluateJavaScript:(NSString*)javaScriptString completionHandler:(void (^)(id r, NSError* e))completionHandler;

/**
 *  <AUTHOR> 2015-09-30 17:09:39
 *
 *  不建议使用这个办法  因为会在内部等待webView 的执行结果
 */
- (NSString*)stringByEvaluatingJavaScriptFromString:(NSString*)javaScriptString __deprecated_msg("Method deprecated. Use [evaluateJavaScript:completionHandler:]");

@end
