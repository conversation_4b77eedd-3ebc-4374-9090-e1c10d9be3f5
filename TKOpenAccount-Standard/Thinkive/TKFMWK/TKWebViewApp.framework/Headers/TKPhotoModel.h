//
//  TKPhotoModel.h
//  TKPluginDemo
//
//  Created by liupm on 16/1/12.
//  Copyright © 2016年 liupm. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKImagePickerDefinition.h"
#import <Photos/PHAsset.h>
#import <Photos/PHImageManager.h>

@interface TKPhotoModel : NSObject
//一个相册照片的对象
@property(nonatomic,strong) ALAsset *asset;
@property(nonatomic,strong) PHAsset *phAsset;

// 缩略图
@property(nonatomic,strong)  UIImage *thumbImage;
//原图
@property(nonatomic,strong)  UIImage *originImage;
//类型是否是视频
@property(nonatomic,assign) BOOL isVideoType;
//是否被选中
@property(nonatomic,assign)BOOL isSelected;
@end
