//
//  TKPhotoSelectHelper.h
//  TKPluginDemo
//
//  Created by liupm on 16/1/12.
//  Copyright © 2016年 liupm. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKPhotoModel.h"
#import "TKAlbumModel.h"

@interface TKPhotoSelectHelper : NSObject

/**
 *  <AUTHOR> 16-01-12 10:01:12
 *
 *  @brief  获得相册组
 *
 *  @return 返回的数据中的子元素为TKAlbumModel
 */
-(void)getAlbumGroups:(TKImagePickerCallBack)callBack;



/**
 *  <AUTHOR> 16-01-12 10:01:43
 *
 *  @brief  获得手机中的所有的图
 *
 *  @return  返回的数据中的子元素为TKPhotoModel
 */
-(void)getAllPhotos:(TKAlbumModel *)album  callBack:(TKImagePickerCallBack)callBack;



@end
