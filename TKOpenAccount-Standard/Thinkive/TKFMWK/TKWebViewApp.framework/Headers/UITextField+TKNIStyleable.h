//
//  UITextField+TKNIStyleable.h
//  Nimbus
//
//  Created by Metral, Max on 2/22/13.
//  Copyright (c) 2013 <PERSON>. All rights reserved.
//

#import <UIKit/UIKit.h>

@class TKNICSSRuleset;
@class TKNIDOM;

@interface UITextField (TKNIStyleable)

/**
 * Applies the given rule set to this text field.
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplyTextFieldStyleWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

/**
 * Applies the given rule set to this label.
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable. Since some of the view
 * styles (e.g. positioning) may rely on some label elements (like text), this is called
 * before the view styling is done.
 */
- (void)tkapplyTextFieldStyleBeforeViewWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

/**
 * Tells the CSS engine a set of pseudo classes that apply to views of this class.
 * In the case of UITextField, this is :empty.
 * In CSS, you specify these with selectors like UITextField:empty.
 *
 * Make sure to include the leading colon.
 */
- (NSArray*) tkpseudoClasses;

/**
 * Applies the given rule set to this text field but for a pseudo class. Thus it only supports the subset of
 * properties that can be set on states of the button. (There's no fancy stuff that applies the styles
 * manually on state transitions.
 *
 * Since UIView doesn't have psuedo's, we don't need the specific version for UITextField like we do with
 * tkapplyTextFieldStyleWithRuleSet.
 */
- (void)tkapplyStyleWithRuleSet:(TKNICSSRuleset *)ruleSet forPseudoClass: (NSString*) pseudo inDOM: (TKNIDOM*) dom;

@end
