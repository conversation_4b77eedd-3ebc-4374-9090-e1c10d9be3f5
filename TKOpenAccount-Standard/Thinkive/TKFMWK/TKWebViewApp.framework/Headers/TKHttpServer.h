//
//  TKHttpServer.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2023/3/1.
//  Copyright © 2023 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKHttpAddress.h"

typedef enum {
    //测试机房
    TKHttpTestSpeedMode_Room,
    //初始化测速
    TKHttpTestSpeedMode_Init,
    //检测测速
    TKHttpTestSpeedMode_Test,
    //界面测速
    TKHttpTestSpeedMode_View
}TKHttpTestSpeedMode;

/**
 *  <AUTHOR> 2015-08-24 17:08:42
 *
 *  网络监听代理
 */
@protocol TKHttpSpeedServerDelegate <NSObject>

@optional

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  初始化机房测速结束
 *
 *  @param address
 */
-(void)roomCheckSpeedFinished:(NSString *)roomName serverName:(NSString *)serverName result:(BOOL)result;

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  测速结果
 *
 *  @param address
 */
-(void)testSpeed:(TKHttpAddress *)address testSpeedMode:(TKHttpTestSpeedMode)testSpeedMode;

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  测速结束
 *
 *  @param address
 */
-(void)testSpeedFinished:(NSString *)serverName testSpeedMode:(TKHttpTestSpeedMode)testSpeedMode;

@end

/**
 * Http服务站点对象
 */
@interface TKHttpServer : NSObject

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  网络测速代理
 */
@property(nonatomic,weak)id<TKHttpSpeedServerDelegate> delegate;

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 * 机房ID
 */
@property(nonatomic,copy)NSString *roomName;

/**
 *  <AUTHOR> 2015-02-28 16:02:33
 *
 *  服务名称
 */
@property(nonatomic,copy)NSString *serverName;

/**
*  <AUTHOR> 2015-02-28 16:02:41
*
*  域名地址列表
*/
@property(nonatomic,retain)NSMutableArray *netDomainArr;
    
/**
 *  <AUTHOR> 2015-02-28 16:02:41
 *
 * IP地址列表
 */
@property(nonatomic,retain)NSMutableArray *netAddressArr;

/**
 *  <AUTHOR> 2016-01-12 03:01:50
 *
 *  用户选择设置的地址
 */
@property(nonatomic,retain)TKHttpAddress *useNetAddress;

/**
 *  <AUTHOR> 2016-01-12 03:01:50
 *
 *  当前正在使用的地址
 */
@property(nonatomic,retain)TKHttpAddress *curNetAddress;

/**
 * 测速链接
 */
@property(nonatomic,copy)NSString *speedUrl;

/**
 *  <AUTHOR> 2015-06-26 20:06:40
 *
 *  用户信息
 */
@property (nonatomic,retain)id userInfo;

/**
 *  <AUTHOR> 2015-02-28 17:02:00
 *
 *  初始化
 *
 *  @param name      名称
 *  @param configMap 配置文件
 *
 *  @return
 */
-(id)initWithServerName:(NSString *)name configMap:(NSMutableDictionary *)configMap;

/**
 *  <AUTHOR> 2015-02-28 17:02:26
 *
 *  获取配置所有的服务器地址（IP+域名），也就是属性netAddressArr + netDomainArr的集合
 *
 *  @return
 */
-(NSMutableArray *)getAllNetworkAddresses;

/**
 *  <AUTHOR> 2015-02-28 17:02:26
 *
 *  获取使用的服务器地址
 *
 *  @return
 */
-(TKHttpAddress *)getRmdServer;

/**
 *  <AUTHOR> 2015-02-28 17:02:26
 *
 *  获取指定地址和端口的服务器地址
 *  @param address 地址，格式为http://ip:port的字符串
 *
 *  @return
 */
-(TKHttpAddress *)getServerAddress:(NSString*)address;

/**
 * 站点测速
 */
-(void)startTest:(TKHttpTestSpeedMode)testSpeedMode;

/**
 * 取消测速
 */
-(void)stopTest;

@end
