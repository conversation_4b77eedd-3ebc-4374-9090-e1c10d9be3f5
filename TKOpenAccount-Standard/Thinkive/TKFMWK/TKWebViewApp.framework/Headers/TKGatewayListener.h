//
//  TKGatewayListener.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-2-28.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKNetAddress.h"
#import "TKServer.h"

/**
 *  <AUTHOR> 2015-08-24 17:08:42
 *
 *  网络监听代理
 */
@protocol TKSocketServerTestSpeedDelegate <NSObject>

@optional

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  测速结果
 *
 *  @param address
 */
-(void)testSpeed:(TKNetAddress *)address;

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  测速结束
 *
 *  @param address
 */
-(void)testSpeedFinished:(NSString *)serverName;

@end

/**
 *  <AUTHOR> 2015-03-02 16:03:40
 *
 *  网关监听器
 */
@interface TKGatewayListener : NSObject

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  网络测速代理
 */
@property(nonatomic,weak)id<TKSocketServerTestSpeedDelegate> delegate;

/**
 *  <AUTHOR> 2015-03-02 16:03:37
 *
 *  单例模式
 *
 *  @return
 */
+(TKGatewayListener *)shareInstance;

/**
 *  <AUTHOR> 2015-03-02 16:03:55
 *
 *  启动监听
 */
-(void)start;

/**
 *  <AUTHOR> 2015-09-23 01:09:15
 *
 *  关闭监听
 */
-(void)stop;

/**
 *  <AUTHOR> 2015-09-23 01:09:26
 *
 *  是否在运行
 */
-(BOOL)isRuning;

/**
 *  <AUTHOR> 2015-03-02 16:03:55
 *
 *  启动某个服务的测速监听
 */
-(void)start:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-03-02 16:03:55
 *
 *  启动某个服务的测速监听
 */
-(void)start:(NSString *)serverName ipMode:(TKIPMode)ipMode;

/**
 *  <AUTHOR> 2015-03-02 16:03:55
 *
 *  停止某个服务的测速监听
 */
-(void)stop:(NSString *)serverName;

@end
