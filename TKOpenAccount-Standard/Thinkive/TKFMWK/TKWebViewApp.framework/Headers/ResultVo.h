//
//  ResultVo.h
//  TKApp
//
//  Created by liu<PERSON> on 14-11-24.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "ReqParamVo.h"

/**
 *  错误y异常类型
 */
typedef enum {
    //业务异常错误
    TKResultErrorType_Business = 0,
    //网络异常错误
    TKResultErrorType_Network = 1
}TKResultErrorType;

/**
 *  结果对象
 */
@interface ResultVo : DynModel

/**
 *  错误类型,用于区分网络异常还是业务异常
 */
@property (nonatomic,assign)TKResultErrorType errorType;

/**
 *  错误号
 */
@property (nonatomic,assign)NSInteger errorNo;

/**
 *  错误信息
 */
@property (nonatomic,copy)NSString *errorInfo;

/**
 *  结果集
 */
@property (nonatomic,retain)NSObject *results;

/**
 *  结果集名称集合
 */
@property (nonatomic,retain)NSArray *dsName;

/**
 *  结果集名称集合
 */
@property (nonatomic,assign)double serverTime;

/**
 *  请求对象
 */
@property (nonatomic,retain)ReqParamVo *reqParamVo;

/**
 *  Http的响应的头
 */
@property (nonatomic,retain)NSDictionary *respHeaderFieldDic;

/**
 *  Http的返回coockie数组
 */
@property (nonatomic,retain)NSArray *coockies;

/**
 *  请求结果集的头，使用于单结果集
 */
@property (nonatomic,retain)NSArray *fields;

/**
 *  <AUTHOR> 2016-12-31 10:12:26
 *
 *  是否缓存命中的数据
 */
@property (nonatomic,assign)BOOL isCacheData;

/**
 *  <AUTHOR> 2017-02-28 13:02:25
 *
 *  是否思迪标准的数据结构
 */
@property (nonatomic,assign)BOOL isStandardResult;

/**
 *  获取指定的结果集
 *
 *  @param dsName 结果集名称
 *
 *  @return 结果集
 */
-(NSObject *)results:(NSString *)dsName;

/**
 *  设置指定的结果集
 *
 *  @param results 结果集
 *  @param dsName  结果集名称
 */
-(void)setResults:(NSObject *)results dsName:(NSString *)dsName;

@end
