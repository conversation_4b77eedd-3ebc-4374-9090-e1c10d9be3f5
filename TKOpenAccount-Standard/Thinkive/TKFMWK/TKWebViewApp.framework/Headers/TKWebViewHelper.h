//
//  TKWebViewHelper.h
//  TKUtil
//
//  Created by liubao on 14-11-11.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef enum{
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  其他
     */
    TKH5Version_Other = 0,
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  H5的1.0框架
     */
    TKH5Version_V1 = 1,
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  H5的2.0框架
     */
    TKH5Version_V2 = 2,
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  H5的3.0框架
     */
    TKH5Version_V3 = 3
}TKH5Version;

/**
 *  WebView相关帮组类
 */
@interface TKWebViewHelper : NSObject

/**
 *  拼接webview加载html的地址
 *
 *  @param dirPath  文件夹路径 如：www/m/mall
 *  @param pageName 页面名称  index
 *  @param queryStr 携带的url参数字符串 #!/business/index.html 或者?name=liubao&age=10
 *
 *  @return 请求对象
 */
+(NSURLRequest *)getWebViewLoadUrl:(NSString *)dirPath htmlName:(NSString *)pageName queryStr:(NSString *)queryStr __deprecated_msg("方法被建议取消使用，请用‘ +(NSURLRequest *)getWebViewLoadUrl:(NSString *)url isUseRandom:(BOOL)isUseRandom ’方法代替，强烈建议");

/**
 *  <AUTHOR> 2015-07-29 11:07:39
 *
 *  通用处理webview的url地址，支持服务器页面路径，支持本地页面路径，强烈建议用
 *
 *  @param url          url地址
 例如：www/m/mall/index.html#!/business/index.html
 例如：file:///xxxx/xxx/www/m/mall/index.html#!/business/index.html
 例如：http://www.baidu.com?name=liubao
 *  @param isEncodeURL              是否进行URL编码
 *  @param isUseRandom              是否增加随机数，用于清除缓存
 *  @param timeOut                  超时时间，单位秒
 *  @param randomUUIDInserBoforeFlagChars  扩展参数，例如随机数和uuid插入的位置标示符号，可能是#!/或者#或者其他未来的符号，默认是#!/
 *
 *  @return
 */
+(NSURLRequest *)getWebViewLoadUrl:(NSString *)url isEncodeURL:(BOOL)isEncodeURL isUseRandom:(BOOL)isUseRandom timeOut:(double)timeOut randomUUIDInserBoforeFlagChars:(NSString *)randomUUIDInserBoforeFlagChars;

/**
 *  <AUTHOR> 2015-07-29 11:07:39
 *
 *  通用处理webview的url地址，支持服务器页面路径，支持本地页面路径，强烈建议用
 *
 *  @param url          url地址
 例如：www/m/mall/index.html#!/business/index.html
 例如：file:///xxxx/xxx/www/m/mall/index.html#!/business/index.html
 例如：http://www.baidu.com?name=liubao
 *  @param isEncodeURL  是否进行URL编码
 *  @param isUseRandom  是否增加随机数，用于清除缓存
 *  @param timeOut      超时时间，单位秒
 *
 *  @return
 */
+(NSURLRequest *)getWebViewLoadUrl:(NSString *)url isEncodeURL:(BOOL)isEncodeURL isUseRandom:(BOOL)isUseRandom timeOut:(double)timeOut;

/**
 *  <AUTHOR> 2015-07-29 11:07:39
 *
 *  通用处理webview的url地址，支持服务器页面路径，支持本地页面路径，强烈建议用
 *
 *  @param url          url地址
                        例如：www/m/mall/index.html#!/business/index.html
                        例如：file:///xxxx/xxx/www/m/mall/index.html#!/business/index.html
                        例如：http://www.baidu.com?name=liubao
 *  @param isUseRandom  是否增加随机数，用于清除缓存
 *  @param timeOut      超时时间，单位秒
 *
 *  @return
 */
+(NSURLRequest *)getWebViewLoadUrl:(NSString *)url isUseRandom:(BOOL)isUseRandom timeOut:(double)timeOut;

/**
 *  <AUTHOR> 2015-07-29 11:07:39
 *
 *  通用处理webview的url地址，支持服务器页面路径，支持本地页面路径，强烈建议用
 *
 *  @param url          url地址
                        例如：www/m/mall/index.html#!/business/index.html
                        例如：file:///xxxx/xxx/www/m/mall/index.html#!/business/index.html
                        例如：http://www.baidu.com?name=liubao
 *  @param isUseRandom  是否增加随机数，用于清除缓存
 *
 *  @return
 */
+(NSURLRequest *)getWebViewLoadUrl:(NSString *)url isUseRandom:(BOOL)isUseRandom;


/**
 *  <AUTHOR> 2015-07-29 11:07:39
 *
 *  通用处理webview的url地址，支持服务器页面路径，支持本地页面路径，强烈建议用
 *
 *  @param url          url地址
                        例如：www/m/mall/index.html#!/business/index.html
                        例如：file:///xxxx/xxx/www/m/mall/index.html#!/business/index.html
                        例如：http://www.baidu.com?name=liubao
 *
 *  @return
 */
+(NSURLRequest *)getWebViewLoadUrl:(NSString *)url;

/**
 获取思迪H5的框架版本
 @param pageUrl
 @return
 */
+(TKH5Version)getTKH5Version:(NSString *)pageUrl;

/**
 获取跳转的URL地址

 @param pageUrl  基础地址
 @param pageCode 跳转页面pageCode
 @param param 参数
 @return
 */
+(NSString *)getTKH5URL:(NSString *)pageUrl pageCode:(NSString *)pageCode param:(NSDictionary *)param;

/**
 *  <AUTHOR> 2016-07-15 20:07:18
 *
 *  构建H5插件的URL
 *
 *  @param funcNo 功能号
 *  @param param  入参
 *
 *  @return
 */
+(NSString *)buildH5PluginURL:(NSString *)funcNo param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2016-07-15 20:07:18
 *
 *  获取H5在App安装包中的根目录
 *
 *  @return
 */
+(NSString *)getH5WWWBundlePath;

/**
 *  <AUTHOR> 2016-07-15 20:07:18
 *
 *  获取H5在App安装包中的根目录名称
 *
 *  @return
 */
+(NSString *)getH5WWWName;

/**
 *  <AUTHOR> 2016-07-15 20:07:18
 *
 *  获取WebView的缓存空间大小
 *
 *  @return
 */
+(NSString *) getWebViewCacheSize;

/**
 *  <AUTHOR> 2016-07-15 20:07:18
 *
 *  清除WebView的缓存空间
 *
 *  @return
 */
+(void) clearWebViewCache;

/**
 * 同步WK浏览器的cookie到原生
 */
+(void) syncWKWebViewCookieToNativeCookie:(id)webView;

/**
 * 同步原生指定域名的cookie到浏览器
 * 返回原生同步过去的cookie数组
 */
+(NSArray *) syncNativeCookieToWKWebViewCookie:(id)webView domain:(NSURL*)domainURL;

/**
 * 同步原生的cookie到浏览器
 * 返回原生同步过去的cookie数组
 */
+(NSArray *) syncAllNativeCookieToWKWebViewCookie:(id)webView;

/**
 * 同步浏览器Response的cookie到原生
 * 返回浏览器Response同步过去的cookie数组
 */
+(NSArray *) syncHttpResponseCookieToNativeCookie:(NSHTTPURLResponse *)response;

/**
 * 同步浏览器Response的cookie到原生
 * 返回原生同步过去的cookie数组
 */
+(NSArray *) syncNativeCookieToHttpRequestCookie:(NSMutableURLRequest *)request;

/**
 * cookie转JS脚本
 */
+(NSString *) cookieJavaScript:(NSHTTPCookie *)cookie;

/**
 * 获取webview的调用堆栈
 */
+(NSArray *) getWebViewCaller;

/**
* 根据Domain获取cookie请求头
*/
+(NSString *)getCookieHeaderByDomain:(NSString *)domain;

@end
