//
// Copyright 2011-2014 NimbusKit
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#import "TKNIActions.h"

@interface TKNIObjectActions : NSObject

@property (nonatomic, copy) TKNIActionBlock tapAction;
@property (nonatomic, copy) TKNIActionBlock detailAction;
@property (nonatomic, copy) TKNIActionBlock navigateAction;

@property (nonatomic) SEL tapSelector;
@property (nonatomic) SEL detailSelector;
@property (nonatomic) SEL navigateSelector;

@end

@interface TKNIActions ()

@property (nonatomic, weak) id target;

- (TKNIObjectActions *)actionForObjectOrClassOfObject:(id<NSObject>)object;

@end
