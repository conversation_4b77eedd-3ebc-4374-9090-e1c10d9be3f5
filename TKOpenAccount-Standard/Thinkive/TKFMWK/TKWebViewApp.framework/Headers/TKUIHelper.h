//
//  TKUIHelper.h
//  ios4
//
//  Created by liu<PERSON> on 14-10-30.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "TKDeviceHelper.h"

#define IOS6_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 6.0f)
#define IOS7_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 7.0f)
#define IOS8_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 8.0f)
#define IOS9_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 9.0f)
#define IOS10_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 10.0f)
#define IOS11_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 11.0f)
#define IOS12_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 12.0f)
#define IOS13_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 13.0f)
#define IOS14_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 14.0f)
#define IOS15_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 15.0f)
#define IOS16_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 16.0f)
#define IOS17_OR_LATER ([UIDevice currentDevice].systemVersion.floatValue >= 17.0f)

#define IPHONEX_SAFEAREAINSETS_BOTTOM (@available(iOS 11.0, *) ? [TKUIHelper mainApplicationWindow].safeAreaInsets.bottom : 0.0)
#define IPHONEX_SAFEAREAINSETS_TOP (@available(iOS 11.0, *) ?  [TKUIHelper mainApplicationWindow].safeAreaInsets.top : 0.0)
#define ISIPHONEX (IPHONEX_SAFEAREAINSETS_BOTTOM > 0.0)
#define STATUSBAR_HEIGHT (ISIPHONEX ? (IPHONEX_SAFEAREAINSETS_TOP > 0 ? IPHONEX_SAFEAREAINSETS_TOP : 44) : 20)
#define STATUSBAR_REAL_HEIGHT ([UIApplication sharedApplication].statusBarFrame.size.height)
#define TABBAR_HEIGHT (ISIPHONEX ? (IPHONEX_BUTTOM_HEIGHT + 49) : 49)
#define NAVBAR_HEIGHT 44
#define IPHONEX_BUTTOM_HEIGHT (ISIPHONEX ? (IPHONEX_SAFEAREAINSETS_BOTTOM > 0 ? IPHONEX_SAFEAREAINSETS_BOTTOM : 34) : 0)
#define TKUIStatusBarStyleDarkContent 3
#define TKUIStatusBarStyleDefault (IOS13_OR_LATER ? TKUIStatusBarStyleDarkContent : UIStatusBarStyleDefault)

#define UISCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define UISCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height

/**
 *  基础UI操作帮助类
 */
@interface TKUIHelper : NSObject

/**
 * 在父窗口中移动子窗口，限制在父窗口区域内。
 *
 *  @param view      窗口
 *  @param srcPoint  起点
 *  @param destPoint 终点
 */
+(void) moveView:(UIView *)view inSuperViewFromPoint:(CGPoint)srcPoint toPoint:(CGPoint)destPoint;

/**
 *  缩放矩形
 *
 *  @param source 原矩形
 *  @param dest   目标矩形
 *
 *  @return 实际缩放后的矩形
 */
+(CGSize) fitSouceSize:(CGSize)source toSize:(CGSize) dest;

/**
 *  颜色转换 IOS中十六进制的颜色转换为UIColor
 *
 *  @param color 16进制字符串
 *
 *  @return UIColor
 */
+ (UIColor *) colorWithHexString:(NSString *)color;

/**
 *  颜色转换 IOS中十六进制的颜色转换为UIColor
 *
 *  @param color 16进制字符串
 *  @param alpha 透明度
 *
 *  @return UIColor
 */
+ (UIColor *) colorWithHexString:(NSString *)color alpha:(CGFloat)alpha;

/**
 *  颜色转换，IOS中的UIColor转16进制
 *
 *  @param color iOS颜色对象
 *
 *  @return UIColor的16进制字符串
 */
+ (NSString *)hexStringWithColor:(UIColor *)color;

/**
 *  颜色转换，IOS中的UIColor转16进制
 *
 *  @param color iOS颜色对象
 *  @param color 是否包含透明度
 *
 *  @return UIColor的16进制字符串
 */
+ (NSString *)hexStringWithColor:(UIColor *)color hasAlpha:(BOOL)hasAlpha;

/**
 *  生成颜色
 *
 *  @param r 红色
 *  @param g 绿色
 *  @param b 蓝色
 *
 *  @return 颜色
 */
+ (UIColor *) colorWithRed:(CGFloat)r green:(CGFloat)g blue:(CGFloat)b;

/**
 *  生成颜色
 *
 *  @param r 红色
 *  @param g 绿色
 *  @param b 蓝色
 *  @param alpha 透明度
 *
 *  @return 颜色
 */
+ (UIColor *) colorWithRed:(CGFloat)r green:(CGFloat)g blue:(CGFloat)b alpha:(CGFloat)alpha;

/**
 *  获取图片的指点坐标的颜色
 *
 *  @param image 图片
 *  @param point 指定坐标
 *
 *  @return UIColor
 */
+ (UIColor *) colorWithImage:(UIImage *)image atPixel:(CGPoint)point;

/**
 *  设置导航栏的文字，颜色
 *
 *  @param color 颜色
 *  @param size  文字大小
 */
+(void)setUINavigationBarAppearance:(UIColor *)color fontSize:(float)size;

/**
 *  设置所有分割栏的颜色以及文字的颜色
 *
 *  @param selectedColor     选中的颜色
 *  @param normalColor       正常的颜色
 *  @param selectedTextColor 文字选中的颜色
 *  @param normalTextColor   文字正常的颜色
 */
+(void)setAllUISegmentControlAppearance:(UIColor *)selectedColor normalColor:(UIColor *)normalColor selectedTextColor:(UIColor *)selectedTextColor normalColor:(UIColor *)normalTextColor;

/**
 *  设置单个分割栏的颜色以及文字的颜色
 *
 *  @param seg               分割栏组件控制器
 *  @param selectedColor     选中的颜色
 *  @param normalColor       正常的颜色
 *  @param selectedTextColor 文字选中的颜色
 *  @param normalTextColor   文字正常的颜色
 */
+(void)setUISegmentControlAppearance:(UISegmentedControl *)seg selectedColor:(UIColor *)selectedColor normalColor:(UIColor *)normalColor selectedTextColor:(UIColor *)selectedTextColor normalColor:(UIColor *)normalTextColor;

/**
 *  清空所有分割栏样式 颜色
 */
+(void)resetAllUISegmentControlAppearance;

/**
 *  根据主window的坐标获取对应的元素View
 */
+(UIView *)findViewByPointInWindow:(CGPoint)point;

/**
 *  根据指定window的坐标获取对应的元素View
 */
+(UIView *)findViewByPointInWindow:(CGPoint)point window:(UIView *)window;

/**
 *  根据主window的坐标来判断是否在指定view的区域内
 */
+(BOOL)isContainPointInView:(CGPoint)point view:(UIView *)view;

/**
 *  根据指定window的坐标来判断是否在指定view的区域内
 */
+(BOOL)isContainPointInView:(CGPoint)point view:(UIView *)view window:(UIView *)window;

/**
 *  获取主window
 */
+ (UIWindow*)mainApplicationWindow;

@end
