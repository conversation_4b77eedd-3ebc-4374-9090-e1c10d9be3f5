//
//  TKAlertHelper.h
//  TKUtil_V1
//
//  Created by liubao on 14-11-8.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

/**
 * 按钮处理Block
 */
typedef void (^TKAlertBtnHandler)(NSInteger buttonIndex);

/**
 *  弹出窗口帮组类
 */
@interface TKAlertHelper : NSObject

/**
 *   弹出提示框
 *
 *  @param content    内容
 *  @param title      标题
 *  @param okBtnText  按钮文本
 *  @param btnHandler 按钮点击处理器
 *  @param parentViewController 父控制器
 */
+(void)showAlert:(NSString *)content title:(NSString *)title okBtnText:(NSString *)okBtnText btnHandler:(TKAlertBtnHandler)btnHandler parentViewController:(UIViewController *)parentViewController;

/**
*   弹出确认框
*
*  @param content       内容
*  @param title         标题
*  @param okBtnText     确定按钮文本
*  @param cancelBtnText 取消按钮文本
*  @param btnHandler    按钮点击处理器
*  @param parentViewController 父控制器
*/
+(void)showConfirm:(NSString *)content title:(NSString *)title okBtnText:(NSString *)okBtnText cancelBtnText:(NSString *)cancelBtnText btnHandler:(TKAlertBtnHandler)btnHandler parentViewController:(UIViewController *)parentViewController;

@end
