//
//  TKPhotoSelect.h
//  TKPluginDemo
//
//  Created by liupm on 16/1/12.
//  Copyright © 2016年 liupm. All rights reserved.
//
#import <AssetsLibrary/AssetsLibrary.h>
#import <UIKit/UIKit.h>
#import "UIResponder+TKImageRouter.h"

/**
 *  <AUTHOR> 16-01-12 10:01:16
 *
 *  @brief  回调
 */
typedef void(^TKImagePickerCallBack)(NSMutableArray *array);
//屏幕宽
#define TKPhotoScreenWidth [UIScreen mainScreen].bounds.size.width
#define TKPhotoName(file)   [[[NSBundle mainBundle] bundlePath] stringByAppendingPathComponent:file]
//选择确定事件
#define TKImageConfirmEvent @"TKImageConfirmEvent"
//关闭选择器
#define TKImagePickerCloseEvent @"TKImagePickerCloseEvent"
//选择预览事件
#define TKImagePreviewEvent @"TKImagePreviewEvent"
//浏览图片隐藏navbar事件
#define TKImageBrowersHideNavBarEvent @"TKImageBrowersHideNavBarEvent"
#import "TKImagePickerSet.h"
