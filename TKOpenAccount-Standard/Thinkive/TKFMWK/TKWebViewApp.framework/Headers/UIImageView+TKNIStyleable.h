//
//  UIImageView+TKNIStyleable.h
//  TKAppBase_V1
//
//  Created by liubao on 15-5-5.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <UIKit/UIKit.h>

@class TKNICSSRuleset;
@class TKNIDOM;

@interface UIImageView (TKNIStyleable)

/**
 * Applies the given rule set to this navigation bar. Use tkapplyNavigationBarStyleWithRuleSet:inDOM: instead
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplyImageViewStyleWithRuleSet:(TKNICSSRuleset *)ruleSet DEPRECATED_ATTRIBUTE;

/**
 * Applies the given rule set to this navigation bar.
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplyImageViewStyleWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

@end
