//
//  CacheVo.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-10-12.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DynModel.h"

/**
 缓存类型
 */
typedef enum {
    /**
     *  <AUTHOR> 2015-10-12 12:10:11
     *
     *  内存
     */
    TKCacheType_Memo,
    /**
     *  <AUTHOR> 2015-10-12 12:10:39
     *
     *  文件
     */
    TKCacheType_File,
    /**
     *  <AUTHOR> 2015-10-12 12:10:59
     *
     *  数据库
     */
    TKCacheType_DB,
    /**
     *  <AUTHOR> 2015-10-12 12:10:11
     *
     *  内存，命中时候自动更新
     */
    TKCacheType_Memo_AutoUpdate,
    
    /**
     *  <AUTHOR> 2015-10-12 12:10:11
     *
     *  文件，命中时候自动更新
     */
    TKCacheType_File_AutoUpdate,
    /**
     *  <AUTHOR> 2015-10-12 12:10:11
     *
     *  数据库，命中时候自动更新
     */
    TKCacheType_DB_AutoUpdate
}TKCacheType;

/**
 *  <AUTHOR> 2015-10-12 12:10:53
 *
 *  缓存对象
 */
@interface TKCacheVo :DynModel

/**
 *  <AUTHOR> 2015-10-12 12:10:38
 *
 *  缓存类型
 */
@property(nonatomic,assign)TKCacheType type;

/**
 *  <AUTHOR> 2015-10-12 12:10:16
 *
 *  缓存的时间,单位秒，0代表永久缓存
 */
@property(nonatomic,assign)NSInteger cacheTime;

/**
 *  <AUTHOR> 2015-10-12 12:10:44
 *
 *  缓存对象创建时间
 */
@property(nonatomic,assign)NSTimeInterval beginTime;

/**
 *  <AUTHOR> 2015-10-12 12:10:19
 *
 *  缓存的数据对象
 */
@property(nonatomic,strong)NSObject *data;

/**
 *  <AUTHOR> 2015-10-14 00:10:09
 *
 *  key
 */
@property(nonatomic,copy)NSString *key;

/**
 *  <AUTHOR> 2015-10-12 12:10:38
 *
 *  缓存文件名称
 */
@property(nonatomic,copy)NSString *fileName;

@end
