//
//  TKHttpServerManager.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2023/3/2.
//  Copyright © 2023 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKHttpServer.h"
#import "TKHttpRoom.h"

/**
 *  <AUTHOR> 2015-02-28 16:02:17
 *
 *  Http服务管理器
 */
@interface TKHttpRoomServerManager : NSObject

/**
 *  <AUTHOR> 2015-02-28 16:02:15
 *
 *  单例模式
 *
 *  @return
 */
+(TKHttpRoomServerManager *)shareInstance;

/**
 *  <AUTHOR> 2015-02-28 16:02:41
 *
 *  获取所有机房列表
 *
 *  @return
 */
-(NSMutableDictionary *)getRooms;

/**
 *  <AUTHOR> 2015-02-28 16:02:41
 *
 *  获取机房
 *  
 *  @param roomName 机房名称
 *
 *  @return
 */
-(TKHttpRoom *)getRoom:(NSString *)roomName;

/**
 *  <AUTHOR> 2015-02-28 16:02:41
 *
 *  获取所有服务网关列表
 *
 *  @param roomName 机房名称
 *
 *  @return
 */
-(NSMutableDictionary *)getRoomServers:(NSString *)roomName;

/**
 *  <AUTHOR> 2015-03-02 16:03:43
 *
 *  获取服务器
 *
 *  @param roomName 机房名称
 *  @param serverName 服务器名称
 *
 *  @return
 */
-(TKHttpServer *)getRoomServer:(NSString *)roomName serverName:(NSString *)serverName;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  设置服务器需要使用的站点地址
 *
 *  @param roomName 机房名称
 *  @param serverName 服务名称
 *  @param address    站点地址
 */
-(void)setRoomServerUseAddress:(NSString *)roomName serverName:(NSString *)serverName address:(TKHttpAddress *)address;

/*
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  设置服务器是否开启手动保存模式,默认是NO，代表不开启
 *
 *  @param roomName 机房名称
 *  @param serverName      服务名称
 *  @param isManualMode    是否手动模式
 */
-(void)setRoomServerUseAddressMode:(NSString *)roomName serverName:(NSString *)serverName isManualMode:(BOOL)isManualMode;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  获取服务器是否开启手动保存模式
 *
 *  @param roomName 机房名称
 *  @param serverName      服务名称
 */
-(BOOL)isRoomServerUseAddressManualMode:(NSString *)roomName serverName:(NSString *)serverName;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  获取手动模式下缓存的未过期的站点地址
 *
 *  @param roomName 机房名称
 *  @param serverName      服务名称
 */
-(NSString *)getRoomCacheNoExpireServerUseAddress:(NSString *)roomName serverName:(NSString *)serverName;

@end
