//
//  TKUploadManager.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2021/9/23.
//  Copyright © 2021 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "LoadInfo.h"

//上传结果回调
typedef void(^TKUploadCallBackFunc)(NSDictionary *result);

//上传进度回调
typedef void(^TKUploadProgressFunc)(LoadInfo *loadInfo);

@interface TKUploadManager : NSObject

/**
 *  单例模式
 */
+(TKUploadManager *)shareInstance;

/**
 * 初始化上下文
 */
-(void)initContext:(NSString *)appKey;

/**
 * 功能描述：根据文件绝对路径上传文件
 *
 * @param uploadUrl:           上传文件服务器地址
 * @param filePath：           文件本地存储路径
 * @param callBackFunc：       上传结果回调函数，入参为result，格式为：{code:错误号,msg:错误信息,data:”文件路径”}
 * @param uploadProgressFunc： 上传进度回调函数，入参为loadInfo,格式为：{bytesTotal:总共大小,bytesLoaded:已上传大小,progress:进度比例,例如0.1}
 */
-(void)uploadFile:(NSString *)uploadUrl filePath:(NSString *)filePath callBackFunc:(TKUploadCallBackFunc)callBackFunc uploadProgressFunc:(TKUploadProgressFunc)uploadProgressFunc;

/**
 * 功能描述：上传二进制文件流
 *
 * uploadUrl:   上传文件服务器地址
 * fileData：    二进制文件数据
 *fileName:    文件名称 test.jpeg
 * callBackFunc：上传结果回调函数，入参为result，格式为：{code:错误号,msg:错误信息,data:”文件路径”}
 * uploadProgressFunc： 上传进度回调函数，入参为loadInfo,格式为：{bytesTotal:总共大小,bytesLoaded:已上传大小,progress:进度比例,例如0.1}
 */
-(void)uploadFileData:(NSString *)uploadUrl fileData:(NSData *)fileData fileName:(NSString *)fileName callBackFunc:(TKUploadCallBackFunc)callBackFunc uploadProgressFunc:(TKUploadProgressFunc)uploadProgressFunc;

@end
