//
//  CCLocationManager.h
//  MMLocationManager
//
//  Created by WangZeKeJi on 14-12-10.
//  Copyright (c) 2014年 Chen Yaoqiang. All rights reserved.
//

/*集成说明：
 1、在plist添加
 NSLocationAlwaysUsageDescription ＝ YES
 NSLocationWhenInUseUsageDescription ＝ YES
 2、导入TKCLLocationManager.h头文件
 3、通过block回调获取经纬度、地理位置等
 */

#import <Foundation/Foundation.h>
#import <MapKit/MapKit.h>
#import <CoreLocation/CoreLocation.h>

/**
 *  本地缓存经度
 */
#define  Cache_LastLongitude @"TKCLLastLongitude"

/**
 *  本地缓存纬度
 */
#define  Cache_LastLatitude  @"TKCLLastLatitude"

/**
 *
 *  本地缓存国家
 */
#define  Cache_LastCountry   @"TKCLLastCountry"

/**
 *
 *  本地缓存省份
 */
#define  Cache_LastProvince   @"TKCLLastProvince"

/**
 *  本地缓存的城市
 */
#define  Cache_LastCity      @"TKCLLastCity"

/**
 *  本地缓存的街道
 */
#define  C<PERSON>_LastAddress   @"TKCLLastAddress"

/**
 *  <AUTHOR> 2016-12-02 17:12:44
 *
 *  定位成功回调函数
 */
typedef void (^TKCLLocationBlock)(CLLocationCoordinate2D locationCorrrdinate);

/**
 *  <AUTHOR> 2016-12-02 17:12:07
 *
 *  定位失败的回调函数
 *
 *  @param error
 */
typedef void (^TKCLLocationErrorBlock) (NSError *error);

/**
 *  <AUTHOR> 2016-12-02 17:12:40
 *
 *  获取城市/街道的回调函数
 *
 *  @param address
 */
typedef void(^TKNSStringBlock)(NSString *address);

/**
 *  <AUTHOR> 2016-12-02 17:12:05
 *
 *  定位管理器
 */
@interface TKCLLocationManager : NSObject<CLLocationManagerDelegate>

/**
 线程队列
 */
@property (nonatomic,strong)dispatch_queue_t locationQueue;

/**
 是否使用系统定位
 */
@property (nonatomic,assign) BOOL isUseSystemLocation;

/**
 *  <AUTHOR> 2016-12-02 17:12:48
 *
 *  最近的经纬度
 */
@property (nonatomic,assign) CLLocationCoordinate2D lastCoordinate;

/**
 *  <AUTHOR> 2016-12-03 15:12:53
 *
 *  最近的国家信息
 */
@property (nonatomic,copy)NSString *lastCountry;

/**
 *  <AUTHOR> 2016-12-03 15:12:41
 *
 *  最近的省份信息
 */
@property (nonatomic,copy)NSString *lastProvince;

/**
 *  <AUTHOR> 2016-12-02 17:12:15
 *
 *  最近的城市信息
 */
@property(nonatomic,copy)NSString *lastCity;

/**
 *  <AUTHOR> 2016-12-02 17:12:27
 *
 *  最近的街道信息
 */
@property (nonatomic,copy) NSString *lastAddress;

/**
 *  <AUTHOR> 2016-12-02 17:12:36
 *
 *  单例对象
 *
 *  @return
 */
+ (TKCLLocationManager *)shareLocation;

/**
 *  设置IP定位Key
 */
-(void)setAMapIPKey:(NSString *)amapIPKey;

/**
 *  获取坐标
 *
 *  @param locaiontBlock locaiontBlock description
 */
- (void) getLocationCoordinate:(TKCLLocationBlock) locaiontBlock ;

/**
 *  获取坐标和详细地址
 *
 *  @param locaiontBlock locaiontBlock description
 *  @param addressBlock  addressBlock description
 */
- (void) getLocationCoordinate:(TKCLLocationBlock) locaiontBlock  withAddress:(TKNSStringBlock) addressBlock;

/**
 *  获取详细地址
 *
 *  @param addressBlock addressBlock description
 */
- (void) getAddress:(TKNSStringBlock)addressBlock;

/**
 *  获取城市
 *
 *  @param cityBlock cityBlock description
 */
- (void) getCity:(TKNSStringBlock)cityBlock;

@end
