//
// Copyright 2011-2014 NimbusKit
// Originally written by <PERSON>
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#import "TKNITextField.h"

@class TKNICSSRuleset;
@class TKNIDOM;

@interface TKNITextField (TKNIStyleable)

/**
 * Applies the given rule set to this text field.
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplyNITextFieldStyleWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

/**
 * Applies the given rule set to this label.
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable. Since some of the view
 * styles (e.g. positioning) may rely on some label elements (like text), this is called
 * before the view styling is done.
 */
- (void)tkapplyNITextFieldStyleBeforeViewWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

/**
 * Applies the given rule set to this text field but for a pseudo class. Thus it only supports the subset of
 * properties that can be set on states of the button. (There's no fancy stuff that applies the styles
 * manually on state transitions.
 *
 * Since UIView doesn't have psuedo's, we don't need the specific version for UIButton like we do with
 * tkapplyButtonStyleWithRuleSet.
 */
- (void)tkapplyStyleWithRuleSet:(TKNICSSRuleset *)ruleSet forPseudoClass: (NSString*) pseudo inDOM: (TKNIDOM*) dom;


@end
