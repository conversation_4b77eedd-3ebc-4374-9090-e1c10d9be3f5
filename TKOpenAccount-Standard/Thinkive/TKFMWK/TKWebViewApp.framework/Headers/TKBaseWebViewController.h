//
//  TKBaseWebViewController.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-4-10.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseSwipeBackWebViewController.h"

/**
 *  <AUTHOR> 2015-04-10 12:04:39
 *
 *  自定义webview控制器
 */
@interface TKBaseWebViewController : TKBaseSwipeBackWebViewController

#pragma mark 属性定义

/**
 *  <AUTHOR> 2015-07-29 09:07:54
 *
 *  是否支持统一登录，支持统一登录的话，每次切换webview模块时候，会主动调用一次js的50113功能号，进行模块初始化检测
 *  这个属性可以解决以前需要reload页面的问题，默认是NO
 */
@property(nonatomic,assign)BOOL isUseSSO;

/**
 *  <AUTHOR> 2015-09-23 13:09:33
 *
 *  是否需要进行模块初始化检测的备用条件,这个和isUseSSO配合使用,在isUseSSO是Yes，并且isNeedReInitJSModule也是Yes的情况下，才会主动调用一次js的50113功能号，进行模块初始化检测,默认是YES，如果设置为NO，也只是当次不触发50113，执行过后，这个变量会自动还原为YES
 */
@property(nonatomic,assign)BOOL isNeedReInitJSModule;

/**
 *  <AUTHOR> 2015-08-06 10:08:33
 *
 *  H5是否加载完成
 */
@property(nonatomic,readonly)BOOL isH5LoadFinish;

/**
 *  <AUTHOR> 2015-08-06 10:08:22
 *
 *  h5加载完成自动触发函数
 */
-(void)h5LoadFinish;

/**
 * 是否安全WebView
 */
-(BOOL)isSafeWebView;

@end
