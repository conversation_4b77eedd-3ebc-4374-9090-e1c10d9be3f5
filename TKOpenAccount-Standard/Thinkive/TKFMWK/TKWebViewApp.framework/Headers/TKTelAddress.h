//
//  TelAddress.h
//  TKUtil_V1
//
//  Created by l<PERSON><PERSON> on 14-12-23.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DynModel.h"
#import <UIKit/UIKit.h>

/**
 *  <AUTHOR> 2014-12-23 11:12:01
 *
 *  通讯录
 */
@interface TKTelAddress:DynModel

/**
 *  <AUTHOR> 2014-12-23 11:12:38
 *
 *  姓名索引
 */
@property (nonatomic,copy)NSString *nameIndex;

/**
 *  <AUTHOR> 2014-12-23 11:12:38
 *
 *  完整姓名
 */
@property (nonatomic,copy)NSString *fullName;

/**
 *  <AUTHOR> 2014-12-23 11:12:38
 *
 *  首姓名
 */
@property (nonatomic,copy)NSString *firstName;

/**
 *  <AUTHOR> 2014-12-23 13:12:25
 *
 *  中间名称
 */
@property (nonatomic,copy)NSString *middleName;

/**
 *  <AUTHOR> 2014-12-23 11:12:34
 *
 *  尾姓名
 */
@property (nonatomic,copy)NSString *lastName;

/**
 *  <AUTHOR> 2014-12-23 11:12:11
 *
 *  昵称
 */
@property (nonatomic,copy)NSString *nickName;

/**
 *  <AUTHOR> 2014-12-23 11:12:56
 *
 *  公司名称
 */
@property (nonatomic,copy)NSString *companyName;

/**
 *  <AUTHOR> 2014-12-23 11:12:49
 *
 *  职位
 */
@property (nonatomic,copy)NSString *jobTitle;

/**
 *  <AUTHOR> 2014-12-23 11:12:16
 *
 *  部门
 */
@property (nonatomic,copy)NSString *departmentName;

/**
 *  <AUTHOR> 2014-12-23 11:12:47
 *
 *  联系邮箱集合
 */
@property (nonatomic,retain)NSArray *emails;

/**
 *  <AUTHOR> 2014-12-23 11:12:46
 *
 *  生日
 */
@property (nonatomic,copy)NSString *birthday;

/**
 *  <AUTHOR> 2014-12-23 11:12:15
 *
 *  备注
 */
@property (nonatomic,copy)NSString *note;

/**
 *  <AUTHOR> 2014-12-23 11:12:57
 *
 *  联系电话集合
 */
@property (nonatomic,retain)NSArray *telPhones;

/**
 *  <AUTHOR> 2014-12-23 13:12:34
 *
 *  网址集合
 */
@property (nonatomic,retain)NSArray *urls;

/**
 *  <AUTHOR> 2014-12-23 13:12:43
 *
 *  照片
 */
@property (nonatomic,retain)UIImage *image;

/**
 *  <AUTHOR> 2014-12-23 13:12:32
 *
 *  地址集合
 */
@property (nonatomic,retain)NSArray *addresses;

/**
 *  <AUTHOR> 2014-12-23 13:12:31
 *
 *  邮政编码集合
 */
@property (nonatomic,retain)NSArray *zips;
@end
