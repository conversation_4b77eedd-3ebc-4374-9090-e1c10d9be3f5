//
//  TKNetHelper.h
//  TKUtil
//
//  Created by liubao on 14-11-7.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

typedef void(^GetIPCallBackFunc)(NSString * ip);

/**
 *  移动网络运营商
 */
typedef enum{
    /**
     *  中国移动
     */
    China_Mobile,
    /**
     *  中国联通
     */
    China_Unicom,
    /**
     *  中国电信
     */
    China_Telecom,
    /**
     *  中国铁通
     */
    China_Tietong,
    /**
     *  未定义的
     */
    China_UnDefined
}PhoneOperator;

/**
 *  网络类型
 */
typedef enum{
    /**
     *  无网络
     */
    Network_No = 0,
    /**
     *  2G
     */
    Network_2G = 1,
    /**
     *  3G
     */
    Network_3G = 2,
    /**
     *  4G
     */
    Network_4G = 3,
    /**
     *  wifi
     */
    Network_WIFI = 4,
    /**
     *  WWAN
     */
    Network_WWAN = 5,
    /**
     *  5G
     */
    Network_5G = 6,
    /**
     *  未知
     */
    Network_UnDefined = 10
}Network_Type;

#import <Foundation/Foundation.h>
#import "TKNetworkInfo.h"
#import "TKDeviceHelper.h"

/**
 *  网络帮组类
 */
@interface TKNetHelper : NSObject

/**
 设置代理类
 */
+(void)setTKDeviceInfoDelegate:(id<TKDeviceInfoDelegate>) delegate;

/**
 *  是否可以连接上网络
 *
 *  @return 是，否
 */
+(BOOL)isConnectionAvailable;

/**
 *  获取手机网络运营商
 *
 *  @return 网络运营商
 */
+(PhoneOperator)getPhoneOperator;

/**
 *  获取手机网络运营商
 *
 *  @return 网络运营商
 */
+(NSString *)getPhoneOperatorInfo;

/**
 *  根据状态栏获取网络类型
 *
 *  @return 网络类型
 */
+(Network_Type)getNetworkTypeByStatusBar;

/**
 *  根据网络检测状态获取网络类型
 *
 *  @return 网络类型
 */
+(Network_Type)getNetworkType;

/**
 *  根据网络检测状态获取网络类型
 *
 *  @return 网络类型
 */
+(NSString *)getNetworkTypeInfo;

/**
 *  获取运营商信息(IMSI)
 *
 *  @return 获取运营商信息(IMSI)
 */
+(NSString *)getPhoneIMSI;

/**
 *  <AUTHOR> 2015-04-20 19:04:50
 *
 *  获取手机设备的外网IP地址
 *
 *  @return 手机设备的外网IP地址
 */
+(NSString *)getIP;

/**
 *  <AUTHOR> 2015-04-20 19:04:50
 *
 *  重新获取手机设备的外网IP地址
 */
+(void)resetIP;

/**
 *  <AUTHOR> 2015-04-20 19:04:50
 *
 *  获取手机设备的外网IP地址
 *
 *  @return 手机设备的IP地址
 */
+(void)getIP:(GetIPCallBackFunc)callBackFunc;

/**
 *  <AUTHOR> 2015-04-20 19:04:50
 *
 *  获取手机设备的本地IP地址
 *
 *  @return 手机设备的IP地址
 */
+(NSString *)getLocalIP;

/**
 *  <AUTHOR> 2015-04-20 19:04:50
 *
 *  获取手机设备的移动蜂窝网络信息
 *
 *  @return 手机设备的移动蜂窝网络信息
 */
+(TKNetworkInfo *)getCellNetworkInfo;

/**
 *  <AUTHOR> 2015-04-20 19:04:50
 *
 *  获取手机设备的移动WIFI网络信息
 *
 *  @return 手机设备的移动WIFI网络信息
 */
+(TKNetworkInfo *)getWifiNetworkInfo;

/*
 * 获取当前网络DNS服务器地址
 */
+(NSArray *)getOutPutDNSServers;

/**
 *  <AUTHOR> 2017-05-11 12:05:46
 *
 *  根据URL获取IP，根据域名获取IP
 *
 *  @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json或者wwww.baidu.com等格式都支持,如果是IP就直接返回IP
 *
 *  @return 域名对应的IP地址
 */
+(NSString*)getIPAddressByHostName:(NSString*)url;

/**
 *  <AUTHOR> 2017-05-11 12:05:46
 *
 *  根据URL获取IP，根据域名获取IP列表
 *
 *  @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json或者wwww.baidu.com等格式都支持,如果是IP就直接返回IP
 *
 *  @return 域名对应的IP地址
 */
+ (NSArray *)getDNSIPV4AddressByHostName:(NSString *)hostName;

/**
 *  <AUTHOR> 2017-05-11 12:05:46
 *
 *  根据URL获取IP，根据域名获取IP列表
 *
 *  @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json或者wwww.baidu.com等格式都支持,如果是IP就直接返回IP
 *
 *  @return 域名对应的IP地址
 */
+ (NSArray *)getDNSIPV6AddressByHostName:(NSString *)hostName;

/**
 *  <AUTHOR> 2017-06-14 22:06:41
 *
 *  根据URL获取协议+HOST+端口
 *
 *  @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json
 *
 *  @return http://www.baidu.com:8080
 */
+(NSString*)getSchemeHostPortByURL:(NSString*)url;

/**
 *  <AUTHOR> 2017-06-14 22:06:41
 *
 *  根据URL获取HOST+端口
 *
 *  @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json
 *
 *  @return www.baidu.com:8080
 */
+(NSString*)getHostPortByURL:(NSString*)url;

/**
 *  <AUTHOR> 2017-06-14 22:06:41
 *
 *  根据URL获取主机名称
 *
 *  @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json
 *
 *  @return www.baidu.com
 */
+(NSString*)getHostByURL:(NSString*)url;

/**
 *  <AUTHOR> 2017-06-14 22:06:13
 *
 *  是否IPV6的地址
 *
 *  @return
 */
+(BOOL)isIPV6URL:(NSString *)url;

/**
 *  <AUTHOR> 2017-06-14 22:06:13
 *
 *  是否IPV6的网络
 *
 *  @return 
 */
+(BOOL)isIPV6;

/**
 * 获取连接的WIFI名称
 */
+(NSString *)getWiFiName;

/**
 * 是否支持VOIP
 */
+(BOOL)carrierAllowsVOIP;

/**
 *  格式化地址，兼容ipv6的处理,里面返回相关host，port等组成一个数组
 */
+ (NSArray *)formatAddress:(NSString *)address;

/**
 * 获取当前网络代理
 */
+ (NSString *)fetchHttpProxy;

/**
 * 重置网络缓存信息
 */
+ (void) resetNetCacheInfo;

/**
 * 重置网络类型缓存信息
 */
+ (void) resetNetTypeCacheInfo;

@end
