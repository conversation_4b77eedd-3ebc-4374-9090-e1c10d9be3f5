//
//  TKBaseKeyBoardView.h
//  TKApp
//
//  Created by 刘宝 on 2019/10/28.
//  Copyright © 2019年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#import "TKKeyBoardVo.h"
#import "TKKeyBoardBoxVo.h"
#import "TKKeyBoardItemVo.h"
#import "TKKeyBoardItemView.h"
#import "TKKeyBoardEventDelegate.h"
#import "UIView+TKBaseView.h"

/**
 *  <AUTHOR> 2015-04-01 09:04:26
 *
 *  键盘确认按钮的相关配置
 */
@interface TKKeyBoardConfirmConfig : NSObject

/**
 *  <AUTHOR> 2015-04-01 09:04:26
 *
 *  确定按钮是否可用
 */
@property(nonatomic,assign) BOOL enable;

/**
 *  <AUTHOR> 2015-04-01 09:04:13
 *
 *  确定按钮内容
 */
@property(nonatomic,copy) NSString *text;

/**
 *  <AUTHOR> 2015-04-01 09:04:13
 *
 *  确定按钮背景色
 */
@property(nonatomic,strong) UIColor *bgColor;

/**
 *  <AUTHOR> 2015-04-01 09:04:13
 *
 *  确定按文字景色
 */
@property(nonatomic,strong) UIColor *textColor;

/**
 *  <AUTHOR> 2015-04-01 09:04:13
 *
 *  确定按钮文字字体
 */
@property(nonatomic,strong) UIFont *textFont;

@end

/**
 *  基础键盘界面对象
 */
@interface TKBaseKeyBoardView : UIView

#pragma mark 键盘相关属性

/**
 *  <AUTHOR> 2015-03-31 16:03:06
 *
 *  键盘类型
 */
@property(nonatomic,copy,readonly) NSString *keyBoardType;

/**
 *  <AUTHOR> 2015-04-07 09:04:02
 *
 *  确定键盘相关配置
 */
@property(nonatomic,strong)TKKeyBoardConfirmConfig *confirmConfig;

/**
 *  <AUTHOR> 2015-04-03 20:04:16
 *
 *  键盘代理
 */
@property(nonatomic,weak) id<TKKeyBoardEventDelegate>delegate;

/**
 * 原始输入框
 */
@property(nonatomic,weak) UITextField *oldInputTextField;

/**
 * 键盘标题是否默认显示
 */
@property(nonatomic,assign)BOOL isHideTitle;

/**
 * 标题容器
 */
@property(nonatomic,strong,readonly)UIView *titleView;

/**
 * 内容容器
 */
@property(nonatomic,strong,readonly)UIView *contentView;

/**
 *  根据类型初始化键盘
 */
-(instancetype)initWithKeyBoardType:(NSString *)keyBoardType;

#pragma mark 重构键盘按钮

/**
 * 构建键盘标题区域相关按钮
 */
-(void)buildKeyBoardTitleItemView:(TKKeyBoardItemView *)keyBoardTitleItemView forKeyBoardVo:(TKKeyBoardVo *)keyBoardVo;

/**
 * 构建键盘内容区域相关按钮
 */
-(void)buildKeyBoardContentItemView:(TKKeyBoardItemView *)keyBoardContentItemView forKeyBoardVo:(TKKeyBoardVo *)keyBoardVo;

/**
 * 构建键盘标题区域相关自定义元素
 */
-(void)buildKeyBoardTitleItemVo:(TKKeyBoardItemVo *)keyBoardTitleItemVo forKeyBoardVo:(TKKeyBoardVo *)keyBoardVo;

/**
 * 构建键盘内容区域相关自定义元素
 */
-(void)buildKeyBoardContentItemVo:(TKKeyBoardItemVo *)keyBoardContentItemVo forKeyBoardVo:(TKKeyBoardVo *)keyBoardVo;

#pragma mark 键盘事件响应处理

/**
 * 处理键盘点击事件
 */
-(void)processKeyBoardItemViewTouchUpInsideEvent:(id)sender;

/**
 * 处理键盘按下事件
 */
-(void)processKeyBoardItemViewTouchDownEvent:(id)sender;

/**
 * 处理键盘按开事件
 */
-(void)processKeyBoardItemViewTouchUpEvent:(id)sender;

/**
 * 获取焦点执行的动作
 */
-(void)becomeFirstResponderAction:(UITextField *)textField;

/**
 * 重新设置键盘
 */
- (void)resetKeyBoard;

/**
 * 隐藏键盘
 */
- (void)hideKeyBoard;

/**
 * 修改元素的值
 */
-(void)setKeyBoardItemText:(NSString *)text forTag:(NSString *)tag;

@end
