//
//  TKKeyBoardManager.h
//  TKApp
//
//  Created by 刘宝 on 2019/9/25.
//  Copyright © 2019年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKKeyBoardVo.h"

/**
 *键盘管理器
 */
@interface TKKeyBoardVoManager : NSObject

/**
 *  <AUTHOR> 2015-05-07 19:05:32
 *
 *  单例对象
 *
 *  @return
 */
+(TKKeyBoardVoManager *)shareInstance;

/**
 *  <AUTHOR> 2015-05-07 19:05:32
 *
 *  单例对象
 *
 *  @return
 */
+(TKKeyBoardVoManager *)shareInstance:(BOOL)isIphoneX;

/**
 *  获取键盘配置对象
 */
-(TKKeyBoardVo *)getKeyBoardVo:(NSString *)keyBoardType;

@end
