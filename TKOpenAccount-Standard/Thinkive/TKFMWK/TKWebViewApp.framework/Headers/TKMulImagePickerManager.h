//
//  TKMulImagePickerManager.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2021/6/2.
//  Copyright © 2021 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
/**
 *  <AUTHOR> 2015-07-23 16:07:20
 *
 *  代理类
 */
@protocol TKMulImagePickerManagerDelegate <NSObject>

/**
 *  <AUTHOR> 2015-07-23 16:07:04
 *
 *  处理最后的视频
 *
 *  @param images
 */
-(void)processPickerMovies:(NSArray *)movies;

/**
 *  <AUTHOR> 2015-07-23 16:07:04
 *
 *  处理最后的图片
 *
 *  @param images
 */
-(void)processPickerImages:(NSArray *)images;

/**
 *  <AUTHOR> 2017-02-27 12:02:52
 *
 *  处理照片的异常情况，例如没有权限
 *
 *  @param errorNo    错误号
 *  @param errorInfo  错误信息
 */
-(void)processPickerImagesWithErrorNo:(int)errorNo errorInfo:(NSString *)errorInfo isDialogShownResult:(BOOL)isDialogShownResult;

@end

/**
 *  <AUTHOR> 2015-07-23 10:07:08
 *
 *  批量图片上传管理器
 */
@interface TKMulImagePickerManager : NSObject

/**
 *  <AUTHOR> 2015-07-23 16:07:58
 *
 *  代理
 */
@property(nonatomic,weak)id<TKMulImagePickerManagerDelegate> delegate;

/**
 *  <AUTHOR> 2017-05-09 12:05:10
 *
 *  是否自动保存拍照图片到相册
 */
@property(nonatomic,assign)BOOL isAutoSavePhoto;

/**
 *  <AUTHOR> 2017-05-09 12:05:10
 *
 *  是否包含视频相册选择
 */
@property(nonatomic,assign)BOOL isSupportMoviePhoto;

/**
 *  <AUTHOR> 2016-06-13 21:06:42
 *
 *  是否要压缩视频
 */
@property(nonatomic,assign)BOOL isCompressMovie;

/**
 *  <AUTHOR> 2016-07-19 22:07:53
 *
 *  当前控制器
 */
@property(nonatomic,retain)UIViewController *currentViewCtrl;

/**
 *  <AUTHOR> 2015-07-28 13:07:00
 *
 *  状态栏背景颜色
 */
@property(nonatomic,retain)UIColor *statusBarBgColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  如果Title不为空，用来设置Ttile的文字颜色
 */
@property(nonatomic,retain)UIColor *titleColor;

/**
 *  <AUTHOR> 2016-11-08 19:11:21
 *
 *  是否前置摄像头
 */
@property(nonatomic,assign)BOOL isFrontCamera;

/**
 闪光灯的模式
 */
@property(nonatomic,assign) UIImagePickerControllerCameraFlashMode cameraFlashMode;

/**
 * 最大选择图片数
 */
@property(nonatomic,assign) NSInteger maxNum;

/**
 *  <AUTHOR> 2015-02-03 18:02:30
 *
 *  单例模式
 *
 *  @return
 */
+(TKMulImagePickerManager *)shareInstance;

/**
 *  <AUTHOR> 2015-07-23 10:07:08
 *
 *  显示选择菜单
 */
-(void)showChoiceSheet;

/**
 *  <AUTHOR> 2016-07-07 20:07:44
 *
 *  选择模式
 *
 *  @param type 1:照片，2：拍照
 */
-(void)showChoiceSheet:(NSString *)type;

@end

