//
//  TKHttpRoom.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2023/3/1.
//  Copyright © 2023 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKHttpServer.h"

/**
 * Http多机房对象
 */
@interface TKHttpRoom : NSObject

/**
 *  <AUTHOR> 2015-02-28 16:02:33
 *
 *  机房名称
 */
@property(nonatomic,readonly,copy)NSString *roomName;

/**
 *  <AUTHOR> 2015-02-28 16:02:33
 *
 *  机房状态
 */
@property(nonatomic,copy)NSString *roomState;

/**
 *  <AUTHOR> 2015-02-28 16:02:33
 *
 *  主要站点
 */
@property(nonatomic,readonly,copy)NSString *mainSite;

/**
 *  <AUTHOR> 2015-02-28 16:02:33
 *
 *  机房描述
 */
@property(nonatomic,readonly,copy)NSString *desc;

/**
 * 测速链接
 */
@property(nonatomic,readonly,copy)NSString *speedUrl;

/**
 *  <AUTHOR> 2015-02-28 17:02:00
 *
 *  初始化
 *
 *  @param name      名称
 *  @param configMap 配置文件
 *
 *  @return
 */
-(id)initWithRoomName:(NSString *)name configMap:(NSMutableDictionary *)configMap;

/**
 *  <AUTHOR> 2015-02-28 16:02:41
 *
 *  获取所有服务网关列表
 *
 *  @return
 */
-(NSMutableDictionary *)getServers;

/**
 *  <AUTHOR> 2015-03-02 16:03:43
 *
 *  获取服务器
 *
 *  @param serverName 服务器名称
 *
 *  @return
 */
-(TKHttpServer *)getServer:(NSString *)serverName;

@end
