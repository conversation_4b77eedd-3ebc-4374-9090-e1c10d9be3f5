//
//  TKNetworkInfo.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2020/3/13.
//  Copyright © 2020年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DynModel.h"

/**
 *  网络对象
 */
@interface TKNetworkInfo : DynModel

/**
 *  网络名称
 */
@property (nonatomic,copy)NSString *networkName;

/**
 *  ipv4本地地址
 */
@property (nonatomic,copy)NSString *ipv4LocalAddress;

/**
 *  ipv4子网掩码地址
 */
@property (nonatomic,copy)NSString *ipv4NetmaskAddress;

/**
 *  ipv4广播地址
 */
@property (nonatomic,copy)NSString *ipv4BroadcastAddress;

/**
 *  ipv4路由地址
 */
@property (nonatomic,copy)NSString *ipv4RouterAddress;

/**
 *  ipv6本地地址
 */
@property (nonatomic,copy)NSString *ipv6LocalAddress;

/**
 *  ipv6子网掩码地址
 */
@property (nonatomic,copy)NSString *ipv6NetmaskAddress;

/**
 *  ipv6广播地址
 */
@property (nonatomic,copy)NSString *ipv6BroadcastAddress;

/**
 *  ipv6路由地址
 */
@property (nonatomic,copy)NSString *ipv6RouterAddress;

@end
