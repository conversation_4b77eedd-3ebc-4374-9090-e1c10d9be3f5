//
// Copyright 2011-2014 NimbusKit
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#include <stdio.h>

typedef enum {
  TKCSSFIRST_TOKEN = 0x100,
  TKCSSSTRING = TKCSSFIRST_TOKEN,
  TKCSSIDENT,
  TKCSSHASH,
  T<PERSON><PERSON>SEMS,
  T<PERSON><PERSON>SEXS,
  TK<PERSON>SLENGTH,
  TKCSSANGLE,
  TKCSSTIME,
  TKCSSFREQ,
  TK<PERSON><PERSON>IMEN,
  TKCSSPERCENTAGE,
  TKCSSNUMBER,
  TKCSSURI,
  TKCSSFUNCTION,
  TKCSSUNICODERANGE,
  T<PERSON><PERSON>SIMPORT,
  TKCSSUNKNOWN,
  TKCSSMEDIA
} TKCssParserCodes;

extern const char* tkcssnames[];

#ifndef TKYY_TYPEDEF_TKYY_SCANNER_T
#define TKYY_TYPEDEF_TKYY_SCANNER_T
typedef void* tkyyscan_t;
#endif

extern FILE *tkcssin;

int tkcsslex(void);
int tkcssConsume(char* text, int token);
int tkcssget_lineno(void);
