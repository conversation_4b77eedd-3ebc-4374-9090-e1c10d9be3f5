//
//  TKKeyBoardButton.h
//  TKApp
//
//  Created by 刘宝 on 2019/11/11.
//  Copyright © 2019年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKKeyBoardItemVo.h"

@class TKKeyBoardItemView;

/**
 *触摸相关委托
 */
@protocol TKTouchKeyBoardItemViewDelegate <NSObject>

@optional
/**
 *  <AUTHOR> 2015-01-31 19:01:21
 *
 *  触摸屏幕
 *
 *  @param tableView 表格
 *  @param touches
 *  @param event
 */
- (void)keyBoardItemView:(TKKeyBoardItemView *)keyBoardItemView touchesBegan:(NSSet *)touches  withEvent:(UIEvent *)event;

/**
 *  <AUTHOR> 2015-01-31 19:01:48
 *
 *  取消触摸
 *
 *  @param tableView 表格
 *  @param touches
 *  @param event
 */
- (void)keyBoardItemView:(TKKeyBoardItemView *)keyBoardItemView touchesCancelled:(NSSet *)touches withEvent:(UIEvent *)event;

/**
 *  <AUTHOR> 2015-01-31 19:01:39
 *
 *  结束触摸
 *
 *  @param tableView 表格
 *  @param touches
 *  @param event
 */
- (void)keyBoardItemView:(TKKeyBoardItemView *)keyBoardItemView touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event;

/**
 *  <AUTHOR> 2015-01-31 19:01:04
 *
 *  滑动
 *
 *  @param tableView 表格
 *  @param touches
 *  @param event
 */
- (void)keyBoardItemView:(TKKeyBoardItemView *)keyBoardItemView touchesMoved:(NSSet *)touches withEvent:(UIEvent *)event;

@end

/**
 * 键盘按钮
 */
@interface TKKeyBoardItemView : UIButton

@property(nonatomic,retain)TKKeyBoardItemVo *keyBoardItem;

@property(nonatomic,weak) id<TKTouchKeyBoardItemViewDelegate> touchDelegate;

@end
