//
//  SCCaptureSessionManager.h
//  SCCaptureCameraDemo
//
//  Created by Aevitx on 14-1-16.
//  Copyright (c) 2014年 Aevitx. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>

/**
 *  最大缩放比例3倍
 */
#define TK_MAX_PINCH_SCALE_NUM   3.f

/**
 *
 *  最新缩放比例1倍
 *
 */
#define TK_MIN_PINCH_SCALE_NUM   1.f

typedef void(^TKDidCapturePhotoBlock)(UIImage *stillImage);

/**
 *  <AUTHOR> 2017-05-07 08:05:40
 *
 *  拍照管理器
 */
@interface TKCaptureSessionManager : NSObject

/**
 *  <AUTHOR> 2017-05-07 09:05:32
 *
 *  缩放比例
 */
@property(nonatomic,assign)CGFloat scaleNum;

/**
 *  <AUTHOR> 2017-05-07 08:05:26
 *
 *  初始化配置
 *
 *  @param parent           父容器
 *  @param preivewRect      范围
 *  @param isUseFrontCamara 是否使用前置摄像头
 */
- (void)configureWithParentLayer:(UIView*)parent previewRect:(CGRect)preivewRect isUseFrontCamara:(BOOL)isUseFrontCamara captureFlashMode:(AVCaptureFlashMode)captureFlashMode;

/**
 *  <AUTHOR> 2017-05-07 09:05:46
 *
 *  开始拍照
 */
-(void)startRunning;

/**
 *  <AUTHOR> 2017-05-07 09:05:55
 *
 *  结束拍照
 */
-(void)stopRunning;

/**
 *  <AUTHOR> 2017-05-07 08:05:58
 *
 *  拍照
 *
 *  @param block 拍照回调
 */
- (void)takePicture:(TKDidCapturePhotoBlock)block;

/**
 *  <AUTHOR> 2017-05-07 08:05:14
 *
 *  切换摄像头
 *
 *  @param isFrontCamera
 */
- (void)switchCamera:(BOOL)isFrontCamera;

/**
 *  <AUTHOR> 2017-05-07 08:05:37
 *
 *  缩放倍数
 *
 *  @param scale
 */
- (void)pinchCameraViewWithScalNum:(CGFloat)scale;

/**
 *  <AUTHOR> 2017-05-07 08:05:53
 *
 *  根据手势缩放倍数
 *
 *  @param gesture
 */
- (void)pinchCameraView:(UIPinchGestureRecognizer*)gesture;

/**
 *  <AUTHOR> 2017-05-07 08:05:36
 *
 *  切换闪光灯
 *
 *  @param sender
 */
- (void)switchFlashMode:(UIButton*)sender;

/**
 *  <AUTHOR> 2017-05-07 08:05:37
 *
 *  对焦
 *
 *  @param devicePoint
 */
- (void)focusInPoint:(CGPoint)devicePoint;

/**
 *  <AUTHOR> 2017-05-07 09:05:36
 *
 *  是否包含聚焦点
 *
 *  @param devicePoint
 *
 *  @return
 */
-(BOOL)containFocusPoint:(CGPoint)devicePoint;

@end
