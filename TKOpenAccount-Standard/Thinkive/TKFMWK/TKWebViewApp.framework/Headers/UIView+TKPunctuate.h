//
//  UIView+TKPunctuate.h
//  BsBacktraceLogger
//
//  Created by liupm on 16/10/13.
//  Copyright © 2016年 bestswifter. All rights reserved.
//

#import <UIKit/UIKit.h>

/**
 *  <AUTHOR> 2016-12-08 17:12:11
 *
 *  扩展UIView可以获取xPath
 */
@interface UIView (TKPunctuate)

/**
 *  <AUTHOR> 2016-12-08 17:12:03
 *
 *  当前页面路径
 */
@property(nonatomic,copy)NSString *TKPagePath;

/**
 *  <AUTHOR> 2016-12-08 17:12:03
 *
 *  当前组件路径
 */
@property(nonatomic,copy) NSString *TKViewPath;

/**
 *  <AUTHOR> 2016-12-08 17:12:37
 *
 *  同级界面索引
 */
@property(nonatomic,copy) NSString *TKViewIndex;

@property(nonatomic,copy) NSString *TKSubViewChangeUUID;

/**
 *  获得当前控制器
 */
- (UIViewController *)getCurrentViewController;

/**
 执行拦截事件

 @param sender 
 */
-(void)tk_excute_action:(id)sender;

@end
