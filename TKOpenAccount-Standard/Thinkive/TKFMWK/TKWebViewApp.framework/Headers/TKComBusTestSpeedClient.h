//
//  TKComBusTestSpeedClient.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2023/3/8.
//  Copyright © 2023 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKComBusClient.h"

/**
消息类型
*/
typedef enum
{
   /**
    *  <AUTHOR> 2015-05-04 17:05:19
    *
    *  会话包
    */
   TKSocketMsgType_SessionKey = 1,
   
   /**
    *  <AUTHOR> 2015-05-04 17:05:33
    *
    *  连接认证包
    */
   TKSocketMsgType_AuthLogin = 2,
   
   /**
    *  <AUTHOR> 2015-05-04 17:05:41
    *
    *  连接认证回报包
    */
   TKSocketMsgType_AuthLoginReturn = 3,
   
   /**
    *  <AUTHOR> 2015-05-04 17:05:56
    *
    *  配置请求包
    */
   TKSocketMsgType_TestSpeedConfig = 90,
   
   /**
    *  <AUTHOR> 2015-05-04 17:05:56
    *
    *  配置响应包
    */
   TKSocketMsgType_TestSpeedConfigReturn = 91,
   
   /**
    *  <AUTHOR> 2015-05-04 17:05:56
    *
    *  测试请求包
    */
   TKSocketMsgType_TestSpeed = 98,
   
   /**
    *  <AUTHOR> 2015-05-04 17:05:29
    *
    *  测试响应包
    */
   TKSocketMsgType_TestSpeedReturn = 99
}TKSocketMsgType;

/**
 * 测速客户端
 */
@interface TKComBusTestSpeedClient : NSObject
{
    @protected
    /**
     *  <AUTHOR> 2015-07-07 15:07:38
     *
     *  类名
     */
    NSString *_className;
}

/**
 *  <AUTHOR> 2015-07-02 18:07:37
 *
 *  主机UUID
 */
@property (nonatomic,readonly)NSString *uuid;

/**
 *  <AUTHOR> 2015-07-02 18:07:37
 *
 *  主机名
 */
@property (nonatomic,readonly)NSString *host;

/**
 *  <AUTHOR> 2015-07-02 18:07:56
 *
 *  端口号
 */
@property (nonatomic,readonly)int port;

/**
 *  <AUTHOR> 2015-07-02 18:07:56
 *
 *  最终连接主机IP
 */
@property (nonatomic,readonly)NSString *connectedHost;

/**
 *  <AUTHOR> 2015-07-02 18:07:56
 *
 *  最终连接主机端口
 */
@property (nonatomic,readonly)int connectedPort;

/**
 *  <AUTHOR> 2015-05-11 13:05:16
 *
 *  网络通信代理协议
 */
@property (nonatomic,weak)id<TKBusClientDelegate> delegate;

/**
 *  <AUTHOR> 2015-06-26 20:06:40
 *
 *  用户信息
 */
@property (nonatomic,retain)id userInfo;

/**
 *  <AUTHOR> 2015-06-30 19:06:56
 *
 *  服务器名称
 */
@property (nonatomic,copy)NSString *serverName;

/**
* socket的当前客户端的地址
*/
@property(nonatomic,copy, readonly)NSString *clientAddress;

/**
 *  <AUTHOR> 2015-06-30 23:06:36
 *
 *  是否初始化
 */
@property (nonatomic,assign)BOOL isInitCTX;

/**
 *  <AUTHOR> 2015-06-30 23:06:36
 *
 *  是否连接成功
 */
@property (nonatomic,assign)BOOL isConnect;

/**
 *  <AUTHOR> 2017-06-12 01:06:45
 *
 *  是否正在连接
 */
@property (nonatomic,assign)BOOL isConnecting;

/**
 *  <AUTHOR> 2015-05-11 12:05:43
 *
 *  初始化上下文
 */
- (void)initCTX;

/**
 *  <AUTHOR> 2015-05-11 12:05:16
 *
 *  建立连接，连接成功以后进行服务器认证
 *
 *  @param host 服务器地址
 *  @param port 服务器端口
 */
- (void)connect:(NSString *)host :(int)port;

/**
 *  <AUTHOR> 2015-03-28 17:03:52
 *
 *  断开socket连接
 */
-(void)disConnect;

/**
 *  <AUTHOR> 2015-05-11 13:05:37
 *
 *  释放上下文
 */
- (void)releaseCTX;

/**
 * 发起测速接口
 */
- (void) doSendTestSpeed;

/**
 * 拉取配置接口
 */
- (void)doSendTestSpeedConfig;
@end
