//
//  TKPhotoSelectViewController.h
//  TKPluginDemo
//
//  Created by liupm on 16/1/12.
//  Copyright © 2016年 liupm. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKImagePickerDefinition.h"

@interface TKImagePickerViewController : UINavigationController
//是否显示相册列表
@property(nonatomic,assign) BOOL showAblumList;
//是否允许多选
@property(nonatomic,assign) BOOL allowsMultipleSelection;
//最多允许选几个
@property(nonatomic,assign) NSInteger maxAllowSelect;
//图片回调
@property(nonatomic,copy) TKImagePickerCallBack imageCallBck;
/**
 *  <AUTHOR> 2015-07-28 13:07:00
 *
 *  状态栏背景颜色
 */
@property(nonatomic,retain)UIColor *statusBarBgColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  如果Title不为空，用来设置Ttile的文字颜色
 */
@property(nonatomic,retain)UIColor *titleColor;

@end
