//
//  JSCallBack.h
//  TKApp
//
//  Created by l<PERSON><PERSON> on 14-11-26.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "TKWebView.h"

/**
 *  <AUTHOR> 2014-11-26 23:11:07
 *
 *  js交互类
 */
@interface JSCallBack : NSObject

/**
 *  <AUTHOR> 2014-11-27 10:11:39
 *
 *  当前的webview对象
 */
@property (nonatomic,strong)TKWebView *webView;

/**
 *  <AUTHOR> 2014-11-27 10:11:51
 *
 *  当前webView所在的控制器
 */
@property (nonatomic,weak,readonly)UIViewController *viewCtrl;

/**
 *  <AUTHOR> 2014-11-27 16:11:52
 *
 * 浏览器对象的名称
 */
@property (nonatomic,copy)NSString *webViewName;

/**
 *  <AUTHOR> 2015-08-06 10:08:33
 *
 *  H5是否加载完成
 */
@property(nonatomic,assign)BOOL isH5LoadFinish;

/**
 *  <AUTHOR> 2015-09-30 12:09:58
 *
 *  是否已经被使用
 */
@property(nonatomic,assign)BOOL isUsed;

/**
 *  <AUTHOR> 2015-10-08 19:10:50
 *
 *  是否被缓存
 */
@property(nonatomic,assign)BOOL isCached;

/**
 是否每次初始化都执行加载H5的URL
 */
@property(nonatomic,assign)BOOL isLoadH5Forever;

/**
 *  <AUTHOR> 2015-10-08 22:10:11
 *
 *  唯一id
 */
@property(nonatomic,copy)NSString *uuid;

/**
 *  是否进行URL地址编码，默认是YES
 */
@property (nonatomic,assign)BOOL isEncodeWebViewUrl;

/**
 *  是否添加随机数和UUID，默认是YES
 */
@property (nonatomic,assign)BOOL isInsertRandomUUID;

/**
 *  辅助参数，例如随机数和uuid插入的位置标示符号，可能是#!/或者#或者其他未来的符号，默认是#!/
 */
@property (nonatomic,copy)NSString *exParamInsertBeforeFlagChars;

/**
 *  <AUTHOR> 2015-07-27 17:07:19
 *
 *  加载超时时间，单位秒
 */
@property(nonatomic,assign)double loadTimeOut;

/**
 *  <AUTHOR> 2014-11-26 23:11:31
 *
 *  初始化对象
 *
 *  @param webView WebView对象
 *
 *  @return
 */
-(id)initWithWebView:(TKWebView *)webView;

/**
 *  <AUTHOR> 2014-11-26 23:11:52
 *
 *  原生调用js
 *
 *  @param funcName          函数名
 *  @param paramMap          json参数
 *  @param isAsync           是否异步，此处的异步的含义是代表业务上面的异步，需要H5处理完后再回调原生的一个标示
 *  @param completionHandler 回调函数
 */
-(void)iosCallJSFunction:(NSString *)funcName paramMap:(NSMutableDictionary *)paramMap isAsync:(BOOL)isAsync completionHandler:(void (^)(id result, NSError* error))completionHandler;

/**
 *  <AUTHOR> 2014-11-26 23:11:52
 *
 *  原生调用js
 *
 *  @param funcName 函数名
 *  @param paramMap json参数
 *  @param isAsync  是否异步，此处的异步的含义是代表业务上面的异步，需要H5处理完后再回调原生的一个标示
 */
-(void)iosCallJSFunction:(NSString *)funcName paramMap:(NSMutableDictionary *)paramMap isAsync:(BOOL)isAsync;

/**
 *  <AUTHOR> 2014-11-26 23:11:52
 *
 *  原生调用js同步方法
 *
 *  @param funcName 函数名
 *  @param paramMap json参数
 */
-(void)iosCallJSFunction:(NSString *)funcName paramMap:(NSMutableDictionary *)paramMap;

/**
 *  <AUTHOR> 2014-11-26 23:11:52
 *
 *  原生调用js
 *
 *  @param funcName 函数名
 *  @param params   参数数组
 *  @param isAsync  是否异步，此处的异步的含义是代表业务上面的异步，需要H5处理完后再回调原生的一个标示
 *  @param completionHandler 回调函数
 */
-(void)iosCallJSFunction:(NSString *)funcName params:(NSArray *)params isAsync:(BOOL)isAsync completionHandler:(void (^)(id result, NSError* error))completionHandler;

/**
 *  <AUTHOR> 2014-11-26 23:11:52
 *
 *  原生调用js
 *
 *  @param funcName 函数名
 *  @param params   参数数组
 *  @param isAsync  是否异步，此处的异步的含义是代表业务上面的异步，需要H5处理完后再回调原生的一个标示
 */
-(void)iosCallJSFunction:(NSString *)funcName params:(NSArray *)params isAsync:(BOOL)isAsync;

/**
 *  <AUTHOR> 2014-11-26 23:11:52
 *
 *  原生调用js同步方法
 *
 *  @param funcName 函数名
 *  @param params   参数数组
 */
-(void)iosCallJSFunction:(NSString *)funcName params:(NSArray *)params;

/**
 *  <AUTHOR> 2015-09-30 02:09:16
 *
 *  预加载函数
 *
 *  @param pageUrl 加载地址
 *  @param isEncodeURL 是否进行URL编码
 *  @param timeOut 超时时间
 *  @param isInsertRandomUUID 是否插入随机随机数和UUID
 *  @param randomUUIDInserBoforeFlagChars   扩展参数，例如随机数和uuid插入的位置标示符号，可能是#!/或者#或者其他未来的符号，默认是#!/
 *  @param completionHandler 回调函数
 */
-(void)prepareLoadUrl:(NSString *)pageUrl isEncodeURL:(BOOL)isEncodeURL timeOut:(double)timeOut isInsertRandomUUID:(BOOL)isInsertRandomUUID randomUUIDInserBoforeFlagChars:(NSString *)randomUUIDInserBoforeFlagChars completionHandler:(void (^)(id result))completionHandler;

/**
 *  <AUTHOR> 2015-09-30 02:09:16
 *
 *  预加载函数
 *
 *  @param pageUrl 加载地址
 *  @param isEncodeURL 是否进行URL编码
 *  @param timeOut 超时时间
 *  @param completionHandler 回调函数
 */
-(void)prepareLoadUrl:(NSString *)pageUrl isEncodeURL:(BOOL)isEncodeURL timeOut:(double)timeOut completionHandler:(void (^)(id result))completionHandler;

/**
 *  <AUTHOR> 2015-09-30 02:09:16
 *
 *  预加载函数
 *
 *  @param pageUrl 加载地址
 *  @param timeOut 超时时间
 *  @param completionHandler 回调函数
 */
-(void)prepareLoadUrl:(NSString *)pageUrl timeOut:(double)timeOut completionHandler:(void (^)(id result))completionHandler;

/**
 *  <AUTHOR> 2015-09-30 02:09:16
 *
 *  预加载函数
 *
 *  @param pageUrl 加载地址
 *  @param completionHandler 回调函数
 */
-(void)prepareLoadUrl:(NSString *)pageUrl completionHandler:(void (^)(id result))completionHandler;

/**
 *  <AUTHOR> 2015-09-30 02:09:16
 *
 *  预加载函数
 *
 *  @param pageUrl 加载地址
 */
-(void)prepareLoadUrl:(NSString *)pageUrl;

/**
 *  <AUTHOR> 2015-09-30 00:09:22
 *
 *  根据页面里面pageCode进行初始化模块
 *
 *  @param pageUrl
 */
-(void)initH5JSModuleForPageUrl:(NSString *)pageUrl;

@end
