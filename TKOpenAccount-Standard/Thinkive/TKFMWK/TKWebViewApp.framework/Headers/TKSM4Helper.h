//
//  TKSM4Helper.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/2/27.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  SM4帮助类
 */
@interface TKSM4Helper : NSObject

/**
 *  SM4加密ECB模式，先做SM4加密，再做Base64编码
 *
 *  @param string 字符串
 *  @param key    秘钥
 *
 *  @return 加密后的字符串
 */
+(NSString *)stringWithSM4EncryptString:(NSString *)string withKey:(NSString *)key;

/**
 *  SM4加密ECB模式
 *
 *  @param string 字符串
 *  @param key    秘钥
 *
 *  @return 加密后的数据
 */
+(NSData *)dataWithSM4EncryptString:(NSString *)string withKey:(NSString *)key;

/**
 *  SM4加密ECB模式
 *
 *  @param data 数据
 *  @param key    秘钥
 *
 *  @return 加密后的数据
 */
+(NSData *)dataWithSM4EncryptData:(NSData *)data withKey:(NSString *)key;

/**
 *  SM4解密ECB模式，先做base64解码，再做SM4解密
 *
 *  @param string 字符串
 *  @param key    秘钥
 *
 *  @return 解密后的字符串
 */
+(NSString *)stringWithSM4DecryptString:(NSString *)string withKey:(NSString *)key;

/**
 *  SM4解密ECB模式，先做base64解码，再做aes解密
 *
 *  @param string 字符串
 *  @param key    秘钥
 *
 *  @return 解密后的数据
 */
+(NSData *)dataWithSM4DecryptString:(NSString *)string withKey:(NSString *)key;

/**
 *  SM4解密ECB模式
 *
 *  @param data 数据
 *  @param key  秘钥
 *
 *  @return 解密后的数据
 */
+(NSData *)dataWithSM4DecryptData:(NSData *)data withKey:(NSString *)key;

/**
 *  SM4解密ECB模式
 *
 *  @param data 数据
 *  @param key  秘钥
 *
 *  @return 解密后的数据
 */
+(NSData *)dataWithSM4DecryptData:(NSData *)data withKeyData:(NSData *)key;

/**
 *  SM4加密CBC模式，先做SM4加密，再做Base64编码
 *
 *  @param string 字符串
 *  @param key    秘钥
 *  @param vector 矢量
 *
 *  @return 加密后的字符串
 */
+(NSString *)stringWithSM4EncryptString:(NSString *)string withKey:(NSString *)key vector:(NSString *)vector;

/**
 *  SM4加密CBC模式
 *
 *  @param string 字符串
 *  @param key    秘钥
 *  @param vector 矢量
 *
 *  @return 加密后的数据
 */
+(NSData *)dataWithSM4EncryptString:(NSString *)string withKey:(NSString *)key vector:(NSString *)vector;

/**
 *  SM4加密CBC模式
 *
 *  @param data 数据
 *  @param key    秘钥
 *  @param vector 矢量
 *
 *  @return 加密后的数据
 */
+(NSData *)dataWithSM4EncryptData:(NSData *)data withKey:(NSString *)key vector:(NSString *)vector;

/**
 *  SM4解密CBC模式，先做base64解码，再做SM4解密
 *
 *  @param string 字符串
 *  @param key    秘钥
 *  @param vector 矢量
 *
 *  @return 解密后的字符串
 */
+(NSString *)stringWithSM4DecryptString:(NSString *)string withKey:(NSString *)key vector:(NSString *)vector;

/**
 *  SM4解密CBC模式，先做base64解码，再做SM4解密
 *
 *  @param string 字符串
 *  @param key    秘钥
 *  @param vector 矢量
 *
 *  @return 解密后的数据
 */
+(NSData *)dataWithSM4DecryptString:(NSString *)string withKey:(NSString *)key vector:(NSString *)vector;

/**
 *  SM4解密CBC模式
 *
 *  @param data 数据
 *  @param key    秘钥
 *  @param vector 矢量
 *
 *  @return 解密后的数据
 */
+(NSData *)dataWithSM4DecryptData:(NSData *)data withKey:(NSString *)key vector:(NSString *)vector;

/**
 *  SM4解密CBC模式
 *
 *  @param data 数据
 *  @param key    秘钥
 *  @param vector 矢量
 *
 *  @return 解密后的数据
 */
+(NSData *)dataWithSM4DecryptData:(NSData *)data withKeyData:(NSData *)key vectorData:(NSData *)vector;

@end
