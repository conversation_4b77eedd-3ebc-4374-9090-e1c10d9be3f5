//
//  TKSM2Helper.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/3/21.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface TKSM2Helper : NSObject

/**
 *  SM2生成公私秘钥对
 *
 *
 *  @return 随机秘钥对[pubX,pubY,pri]
 */
+ (NSArray <NSString *>*)randomSM2KeyPairs;

/**
 *  <AUTHOR> 2015-07-14 13:07:06
 *
 *  SM2公钥加密，对应要用SM2私钥解密,加密后转成16进制字符串
 *
 *  @param content         需要加密的原始内容
 *  @param cerFilePath     公钥证书文件
 *  @param isEncryptCer    是否加密证书
 *
 *  @return 加密后内容
 */
+(NSString *)sm2PublicKeyEncryptString:(NSString *)content cerFilePath:(NSString *)cerFilePath isEncryptCer:(BOOL)isEncryptCer;

/**
 *  <AUTHOR> 2015-07-14 13:07:06
 *
 *  SM2公钥加密，对应要用SM2私钥解密
 *
 *  @param data            需要加密的原始二进制
 *  @param cerFilePath     公钥证书文件
 *  @param isEncryptCer    是否加密证书
 *
 *  @return 加密后内容
 */
+(NSData *)sm2PublicKeyEncryptData:(NSData *)data cerFilePath:(NSString *)cerFilePath isEncryptCer:(BOOL)isEncryptCer;

/**
 *  <AUTHOR> 2015-07-14 13:07:06
 *
 *  SM2公钥加密，对应要用SM2私钥解密,加密后转成16进制字符串
 *
 *  @param content         需要加密的原始内容
 *  @param cerFilePath     公钥证书文件
 *
 *  @return 加密后内容
 */
+(NSString *)sm2PublicKeyEncryptString:(NSString *)content cerFilePath:(NSString *)cerFilePath;

/**
 *  <AUTHOR> 2015-07-14 13:07:06
 *
 *  SM2公钥加密，对应要用SM2私钥解密
 *
 *  @param data            需要加密的原始二进制
 *  @param cerFilePath     公钥证书文件
 *
 *  @return 加密后内容
 */
+(NSData *)sm2PublicKeyEncryptData:(NSData *)data cerFilePath:(NSString *)cerFilePath;

/**
 *  <AUTHOR> 2015-07-14 13:07:06
 *
 *  SM2公钥加密，对应要用SM2私钥解密,加密后转成16进制字符串
 *
 *  @param content 需要加密的原始内容
 *  @param pubX    公钥X
 *  @param pubY    公钥Y
 *
 *  @return 加密后内容
 */
+(NSString *)sm2PublicKeyEncryptString:(NSString *)content pubX:(NSString *)pubX pubY:(NSString *)pubY;

/**
 *  <AUTHOR> 2015-07-14 13:07:06
 *
 *  SM2公钥加密，对应要用SM2私钥解密
 *
 *  @param data    需要加密的原始二进制
 *  @param pubX    公钥X
 *  @param pubY    公钥Y
 *
 *  @return 加密后内容
 */
+(NSData *)sm2PublicKeyEncryptData:(NSData *)data pubX:(NSString *)pubX pubY:(NSString *)pubY;

/**
 *  <AUTHOR> 2015-07-14 13:07:02
 *
 *  SM2私钥解密，先解码16进制，再解密
 *
 *  @param content      需要解密的密文内容
 *  @param pemFilePath  私钥证书文件
 *  @param password     私钥证书密码
 *
 *  @return 解密后内容
 */
+(NSString *)sm2PrivateKeyDecryptString:(NSString *)content pemFilePath:(NSString *)pemFilePath password:(NSString *)password;

/**
 *  <AUTHOR> 2015-07-14 13:07:02
 *
 *  SM2私钥解密
 *
 *  @param data         需要解密的密文二进制
 *  @param pemFilePath  私钥证书文件
 *  @param password     私钥证书密码
 *
 *  @return 解密后内容
 */
+(NSData *)sm2PrivateKeyDecryptData:(NSData *)data pemFilePath:(NSString *)pemFilePath password:(NSString *)password;

/**
 *  <AUTHOR> 2015-07-14 13:07:02
 *
 *  SM2私钥解密，先解码16进制，再解密
 *
 *  @param content      需要解密的密文内容
 *  @param pemFilePath  私钥证书文件
 *
 *  @return 解密后内容
 */
+(NSString *)sm2PrivateKeyDecryptString:(NSString *)content pemFilePath:(NSString *)pemFilePath;

/**
 *  <AUTHOR> 2015-07-14 13:07:02
 *
 *  SM2私钥解密
 *
 *  @param data         需要解密的密文二进制
 *  @param pemFilePath  私钥证书文件
 *
 *  @return 解密后内容
 */
+(NSData *)sm2PrivateKeyDecryptData:(NSData *)data pemFilePath:(NSString *)pemFilePath;

/**
 *  <AUTHOR> 2015-07-14 13:07:02
 *
 *  SM2私钥解密，先解码16进制，再解密
 *
 *  @param content  需要解密的密文内容
 *  @param privKey  私钥
 *
 *  @return 解密后内容
 */
+(NSString *)sm2PrivateKeyDecryptString:(NSString *)content privKey:(NSString *)privKey;

/**
 *  <AUTHOR> 2015-07-14 13:07:02
 *
 *  SM2私钥解密
 *
 *  @param data     需要解密的密文二进制
 *  @param privKey  私钥
 *
 *  @return 解密后内容
 */
+(NSData *)sm2PrivateKeyDecryptData:(NSData *)data privKey:(NSString *)privKey;

/**
 *  <AUTHOR> 2015-07-14 13:07:02
 *
 *  SM2私钥签名
 *
 *  @param content  原文
 *  @param uid
 *  @param privKey  私钥
 *
 *  @return 签名后内容
 */
+(NSString *)sm2PrivateKeySignString:(NSString *)content withUID:(NSString *)uid withPrivKey:(NSString *)privKey;

/**
 *  <AUTHOR> 2015-07-14 13:07:02
 *
 *  SM2公钥验证签名
 *
 *  @param content  原文
 *  @param sign     签名串
 *  @param uid
 *  @param pubX    公钥X
 *  @param pubY    公钥Y
 *
 *  @return 签名后内容
 */
+(BOOL)sm2PublicKeyVerifyString:(NSString *)content withSigned:(NSString *)sign withUID:(NSString *)uid withPubX:(NSString *)pubX withPubY:(NSString *)pubY;

@end
