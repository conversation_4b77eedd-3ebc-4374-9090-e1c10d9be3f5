//
//  TKKeyBoard.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2019/12/10.
//  Copyright © 2019年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "TKBaseKeyBoardView.h"
#import "TKKeyBoardEventDelegate.h"

/**
 * 自定义原生键盘
 */
@interface TKKeyBoard:UIView<TKKeyBoardEventDelegate>

/**
 *  <AUTHOR> 2015-03-31 16:03:57
 *
 *  键盘代理
 */
@property(nonatomic,weak) id<TKKeyBoardEventDelegate> delegate;

/**
 *  键盘类型
 */
@property(nonatomic,copy) NSString *keyBoardType;

/**
 *  最近一次键盘类型
 */
@property(nonatomic,copy) NSString *lastKeyBoardType;

/**
 *  确定按钮配置
 */
@property(nonatomic,strong) TKKeyBoardConfirmConfig *confirmConfig;

/**
 * 键盘标题是否默认显示
 */
@property(nonatomic,assign)BOOL isHideTitle;

/**
 * 标题容器
 */
@property(nonatomic,strong,readonly)UIView *titleView;

/**
 * 内容容器
 */
@property(nonatomic,strong,readonly)UIView *contentView;

/**
 * 原始输入框
 */
@property(nonatomic,weak) UITextField *oldInputTextField;

/**
 *  根据类型初始化键盘
 */
-(instancetype)initWithKeyBoardType:(NSString *)keyBoardType;

/**
 * 重新设置键盘
 */
- (void)resetKeyBoard;

/**
 * 获取焦点执行的动作
 */
-(void)becomeFirstResponderAction:(UITextField *)textField;

/**
 * 修改元素的值
 */
-(void)setKeyBoardItemText:(NSString *)text forTag:(NSString *)tag;

@end
