//
//  TKKeyBoardInputItemView.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2021/9/3.
//  Copyright © 2021 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

/**
 * 输入框元素
 */
@interface TKKeyBoardInputItemView : UITextField

/**
 *  <AUTHOR> 2015-03-31 21:03:21
 *
 *  限制长度
 */
@property(nonatomic,assign) NSInteger limitLength;

/**
 * 光标定位到最后
 */
-(void)setFocusEnd;

/**
 *  <AUTHOR> 2015-03-31 13:03:19
 *
 *  追加字符
 *
 *  @param str 内容
 */
- (void)appendChar:(NSString *)charStr;

/**
 * <AUTHOR> 15-03-18 10:03:14
 *
 * @brief  退格删除字符
 */
- (void)deleteChar;

/**
 * <AUTHOR> 14-11-27 15:11:20
 *
 * 清空值
 * @param str
 */
-(void)clearValue;

/**
 * 前进键
 */
-(void)doForward;

/**
 * 后退键
 */
-(void)doGoBack;

@end

