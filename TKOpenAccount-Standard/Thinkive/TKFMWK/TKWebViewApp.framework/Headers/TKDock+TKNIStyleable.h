//
//  Dock+TKNIStyleable.h
//  TKAppBase_V1
//
//  Created by liubao on 15-5-6.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

/**
 *  <AUTHOR> 2015-05-06 11:05:19
 *
 *  自定义tabbar
 */
#import "TKDock.h"

@class TKNICSSRuleset;
@class TKNIDOM;

@interface TKDock (TKNIStyleable)

/**
 * Applies the given rule set to this navigation bar. Use tkapplyNavigationBarStyleWithRuleSet:inDOM: instead
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplyDockStyleWithRuleSet:(TKNICSSRuleset *)ruleSet DEPRECATED_ATTRIBUTE;

/**
 * Applies the given rule set to this navigation bar.
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplyDockStyleWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

@end
