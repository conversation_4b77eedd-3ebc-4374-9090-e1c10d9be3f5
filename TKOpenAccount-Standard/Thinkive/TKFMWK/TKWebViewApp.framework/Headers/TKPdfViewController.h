//
//  TKPdfViewController.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-4-21.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseViewController.h"

typedef void(^TKPDFCloseBlock)(int flag);

typedef void(^TKPDFActionBlock)(NSString *action, NSDictionary *param);

/**
 *  <AUTHOR> 2015-04-21 09:04:55
 *
 *  查看Pdf文件
 */
@interface TKPdfViewController : TKBaseViewController

/**
 *  <AUTHOR> 2015-04-21 09:04:47
 *
 *  pdf地址
 */
@property(nonatomic,copy)NSString *url;

/**
 *  <AUTHOR> 2015-07-28 13:07:00
 *
 *  状态栏背景颜色
 */
@property(nonatomic,retain)UIColor *statusBarBgColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  如果Title不为空，用来设置Ttile的文字颜色
 */
@property(nonatomic,retain)UIColor *titleColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  加载超时时间
 */
@property(nonatomic,assign)CGFloat loadTimeOut;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  进度条的颜色
 */
@property(nonatomic,retain)UIColor *progressColor;

/**
 *  <AUTHOR> 2017-02-15 10:02:35
 *
 *  是否显示加载过渡loading图
 */
@property(nonatomic,assign)BOOL isShowLoading;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  重新加载webview的界面背景图片，用于失败后重新加载webview
 */
@property (nonatomic,copy)NSString *reloadWebViewImageName;

/**
 *  <AUTHOR> 2017-04-11 01:04:52
 *
 *  加载webview的过渡界面的动画图片名称
 */
@property (nonatomic,copy)NSString *loadingWebViewGIFName;

/**
 *  <AUTHOR> 2016-09-12 17:09:10
 *
 *  前缀
 */
@property(nonatomic,copy)NSString *suffix;

/**
 *  <AUTHOR> 2017-02-18 13:02:35
 *
 *  停留查看最少时间(单位秒)
 */
@property(nonatomic,assign)int readTime;

/**
 *  <AUTHOR> 2017-02-15 10:02:38
 *
 *  如果Title不为空,返回或者关闭按钮的模式，默认是文本模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
 */
@property(nonatomic,copy)NSString *btnMode;

/**
 * 阅读后确定按钮的文字
 */
@property(nonatomic,copy)NSString *okBtnReadText;

/**
 * 确认按钮背景颜色
 */
@property(nonatomic,retain)UIColor *okBtnReadBgColor;

/**
 * 确认按钮文字颜色
 */
@property(nonatomic,retain)UIColor *okBtnReadTextColor;

/**
 * 确认按钮倒计时背景颜色
 */
@property(nonatomic,retain)UIColor *okBtnReadTimerBgColor;

/**
 * 确认按钮倒计时文字颜色
 */
@property(nonatomic,retain)UIColor *okBtnReadTimerTextColor;

/**
 * 用户操作回调
 */
@property(nonatomic,copy)TKPDFCloseBlock PDFCloseBlock;

/**
 * 用户Action回调
 */
@property(nonatomic,copy)TKPDFActionBlock PDFActionBlock;

/**
 * 右侧按钮（右侧按钮是文本模式传文本内容；是图片模式传图片名称）
 */
@property(nonatomic,copy)NSString *rightBtnTxt;

/**
 * 右侧按钮模式（0：文本，1：图片）
 */
@property(nonatomic,copy)NSString *rightBtnMode;

/**
 * 右侧按钮动作，方便上层区分按钮点击逻辑
 */
@property(nonatomic,copy)NSString *rightBtnAction;

/**
* 右侧按钮动作附加JSON参数
*/
@property(nonatomic,retain)NSDictionary *rightBtnActionParam;

/**
 *  返回模式，0：直接关闭，1:到栈顶再关闭
 */
@property(nonatomic,copy)NSString *goBackMode;

/**
 * 关闭界面
 */
-(void)closeView;

@end
