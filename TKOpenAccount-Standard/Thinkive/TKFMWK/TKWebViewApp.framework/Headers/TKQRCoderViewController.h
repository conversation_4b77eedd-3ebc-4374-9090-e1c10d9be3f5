//
//  TKQRCoderViewController.h
//  TKAppBase_V1
//
//  Created by liubao on 15-6-23.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseViewController.h"

/**
 *  <AUTHOR> 2015-06-23 15:06:41
 *
 *  生成图片二维码
 */
@interface TKQRCoderViewController : TKBaseViewController

/**
 *  <AUTHOR> 2015-04-21 09:04:47
 *
 *  图片二维码内容
 */
@property(nonatomic,copy)NSString *content;

/**
 *  <AUTHOR> 2015-07-28 13:07:00
 *
 *  状态栏背景颜色
 */
@property(nonatomic,retain)UIColor *statusBarBgColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  如果Title不为空，用来设置Ttile的文字颜色
 */
@property(nonatomic,retain)UIColor *titleColor;

/**
 *  <AUTHOR> 2017-02-15 10:02:38
 *
 *  如果Title不为空,返回或者关闭按钮的模式，默认是文本模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
 */
@property(nonatomic,copy)NSString *btnMode;

@end
