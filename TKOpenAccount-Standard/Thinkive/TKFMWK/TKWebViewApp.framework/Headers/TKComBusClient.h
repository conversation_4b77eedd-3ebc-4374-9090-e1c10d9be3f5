//
//  TKComBusClient.h
//  TKASClient
//
//  Created by liu<PERSON> on 15-6-30.
//  Copyright (c) 2015年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKDataVo.h"
#import "TKCommonQueue.h"

/**
 bus版本
 */
typedef enum {
    TKBusClientVersion_BusV1 = 3,
    TKBusClientVersion_BusV2 = 4,
    TKBusClientVersion_QuoteV3 = 5,
    TKBusClientVersion_TradeV3 = 6,
    TKBusClientVersion_NewsV3 = 7,
    TKBusClientVersion_TFBusV3 = 8,
    TKBusClientVersion_SDBusV3 = 9,
    TKBusClientVersion_QuotePushV3 = 20
}TKBusClientVersion;

/**
 消息类型
 */
typedef enum
{
    /**
     *  <AUTHOR> 2015-05-11 13:05:26
     *
     *  服务器连接认证成功消息
     */
    TKBusMsgType_Connect = 10001,
    
    /**
     *  <AUTHOR> 2015-05-11 13:05:59
     *
     *  连接断开消息
     */
    TKBusMsgType_DisConnect = 10002,
    
    /**
     *  <AUTHOR> 2015-05-11 13:05:59
     *
     *  请求完成消息
     */
    TKBusMsgType_Result = 10003,
    
    /**
     *  <AUTHOR> 2016-01-13 13:01:27
     *
     *  请求出错信息
     */
    TKBusMsgType_Error = 10004
    
}TKBusMsgType;

/**
 网络通信层代理协议
 */
@protocol TKBusClientDelegate <NSObject>

@required

/**
 *  <AUTHOR> 2015-05-11 13:05:16
 *
 *  接收各种状态通知消息
 
 msgType为TKBusV1MsgType_Connect：（服务器连接状态消息）
 wparam：流水号
 lparam：备用
 说明：当客户端连接服务器时被触发
 
 msgType为TKBusV1MsgType_DisConnect：（连接断开消息）
 wparam：流水号
 lparam：表示连接断开的原因，NSString类型；
 
 *
 *  @param msgType 消息类型
 *  @param wparam  参数1
 *  @param lparam  参数2
 */
- (void)onNotifyMessage:(id)object :(TKBusMsgType)msgType :(id)wparam :(id)lparam;

@end

/**
 数据解析函数代理
 */
@protocol TKBusClientDataDelegate <NSObject>

@required

/**
 *  <AUTHOR> 2015-06-30 14:06:06
 *
 *  获取响应头长度
 *
 *  @return
 */
-(int)getResponseHeaderLength;

/**
 *  <AUTHOR> 2016-11-19 15:11:54
 *
 *  获取请求对象
 *
 *  @param flowNo   流水号
 *  @param data     数据
 *  @param userInfo 扩展字段
 *
 *  @return
 */
-(TKDataVo *)getSendData:(NSString *)flowNo data:(NSObject *)data userInfo:(NSDictionary *)userInfo;

/**
 *  <AUTHOR> 2016-11-17 20:11:56
 *
 *  处理加工发送数据
 *
 *  @param data 数据
 *
 *  @return 处理后的结果
 */
-(NSData *)processSendData:(TKDataVo *)dataVo;

/**
 *  <AUTHOR> 2015-06-30 14:06:28
 *
 *  从整个结果数据中截取需要的数据包
 *
 *  @param data 数据
 *
 *  @return
 */
-(NSData *)getResultData:(NSData *)data;

/**
 *  <AUTHOR> 2015-06-30 15:06:03
 *
 *  转换结果数据到最后的resultVo的数据字典对象
 *
 *  @param resultData 结果数据
 *
 *  @return
 */
-(NSDictionary *)convertResultDataToResultDic:(NSData *)resultData;

@end

/**
 编码格式
 */
typedef enum {
    TKCharSet_DEFAULT = 0,
    TKCharSet_UTF_8,
    TKCharSet_GBK
}TKCharSet;

/**
 *  <AUTHOR> 2015-05-11 12:05:44
 *
 *  BUS网络通信层 ，用法按照以下步骤：
 //初始化上下文
 1、initCTX;
 //连接服务器
 2、connect:(NSString *)host :(int)port;
 //发送消息
 3、sendData
 //读取消息
 4、readData
 //断开连接
 5、disConnect;
 //释放上下文
 6、releaseCTX;
 */
@interface TKComBusClient : NSObject<TKBusClientDataDelegate>
{
    @protected
    /**
     *  <AUTHOR> 2015-07-07 15:07:38
     *
     *  类名
     */
    NSString *_className;
}

/**
 *  <AUTHOR> 2015-07-02 18:07:37
 *
 *  主机UUID
 */
@property (nonatomic,readonly)NSString *uuid;

/**
 *  <AUTHOR> 2015-07-02 18:07:37
 *
 *  主机名
 */
@property (nonatomic,readonly)NSString *host;

/**
 *  <AUTHOR> 2015-07-02 18:07:56
 *
 *  端口号
 */
@property (nonatomic,readonly)int port;

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 *  分组ID
 */
@property(nonatomic,copy)NSString *groupId;

/**
 *  <AUTHOR> 2015-07-02 18:07:56
 *
 *  最终连接主机IP
 */
@property (nonatomic,readonly)NSString *connectedHost;

/**
 *  <AUTHOR> 2015-07-02 18:07:56
 *
 *  最终连接主机端口
 */
@property (nonatomic,readonly)int connectedPort;

/**
 *  <AUTHOR> 2016-11-22 00:11:39
 *
 *  超时时间
 */
@property (nonatomic,assign)int timeOut;

/**
 *  <AUTHOR> 2016-11-22 00:11:39
 *
 *  心跳间隔时间
 */
@property (nonatomic,assign)int heartTime;

/**
 *  <AUTHOR> 2015-05-11 13:05:16
 *
 *  网络通信代理协议
 */
@property (nonatomic,weak)id<TKBusClientDelegate> delegate;

/**
 *  <AUTHOR> 2015-06-29 23:06:59
 *
 *  是否长连接
 */
@property (nonatomic,assign)BOOL isLongConnect;

/**
 *  <AUTHOR> 2015-06-26 20:06:40
 *
 *  用户信息
 */
@property (nonatomic,retain)id userInfo;

/**
 *  <AUTHOR> 2015-06-29 23:06:59
 *
 *  是否正在登陆中,防止重复登录
 */
@property (nonatomic,assign)BOOL isLogining;

/**
 *  <AUTHOR> 2015-06-30 19:06:56
 *
 *  服务器名称
 */
@property (nonatomic,copy)NSString *serverName;

/**
 * socket的实现类
 */
@property (nonatomic,copy)NSString *socketClassName;

/**
 * 是否使用自定义实现类
 */
@property (nonatomic,assign)BOOL isUseSocketClassName;

/**
* socket的当前客户端的地址
*/
@property(nonatomic,copy, readonly)NSString *clientAddress;

/**
 *  <AUTHOR> 2015-06-30 23:06:27
 *
 *  bus版本号
 */
@property (nonatomic,assign)TKBusClientVersion version;

/**
 *  <AUTHOR> 2015-06-30 23:06:36
 *
 *  是否初始化
 */
@property (nonatomic,assign)BOOL isInitCTX;

/**
 *  <AUTHOR> 2015-06-30 23:06:36
 *
 *  是否连接成功
 */
@property (nonatomic,assign)BOOL isConnect;

/**
 *  <AUTHOR> 2017-06-12 01:06:45
 *
 *  是否正在连接
 */
@property (nonatomic,assign)BOOL isConnecting;

/**
 *  <AUTHOR> 2015-06-30 23:06:36
 *
 *  是否认证成功
 */
@property (nonatomic,assign)BOOL isVitify;

/**
 *  <AUTHOR> 2016-01-29 00:01:16
 *
 *  返回字符编码集
 */
@property(nonatomic,assign)TKCharSet charSet;

/**
 *  <AUTHOR> 2016-01-29 00:01:16
 *
 *  收到响应包的最后时间搓，可以用来检测客户端是否超过一定时间未收到响应包，然后根据需要主动断开长连接进行重连
 */
@property(nonatomic,assign)NSTimeInterval respDataTimeStamp;

/**
 *  <AUTHOR> 2016-01-29 00:01:16
 *
 *  当前运行线程队列
 */
@property(nonatomic,readonly)TKCommonQueue *delegateQueue;

/**
 *  <AUTHOR> 2016-01-29 00:01:16
 *
 *  当前数据请求队列
 */
@property(nonatomic,readonly)NSMutableArray *reqDataQueue;

/**
 *  <AUTHOR> 2016-01-29 00:01:16
 *
 *  当前数据数据写队列
 */
@property(nonatomic,readonly)NSMutableArray *writeBufferData;

/**
 *  <AUTHOR> 2015-05-11 12:05:43
 *
 *  初始化上下文
 */
- (void)initCTX;

/**
 *  <AUTHOR> 2015-05-11 12:05:16
 *
 *  建立连接，连接成功以后进行服务器认证
 *
 *  @param host 服务器地址
 *  @param port 服务器端口
 */
- (void)connect:(NSString *)host :(int)port;

/**
 *  <AUTHOR> 2015-07-02 17:07:47
 *
 *  建立连接成功，尚未认证的状态
 */
- (void)connectReady;

/**
 *  <AUTHOR> 2015-07-02 17:07:47
 *
 *  建立连接并且认证成功
 */
- (void)connectOk;

/**
 *  <AUTHOR> 2015-07-02 19:07:42
 *
 *  连接认证失败
 *
 *  @param errorInfo 错误信息
 */
- (void)connectError:(NSString *)errorInfo;

/**
 *  <AUTHOR> 2015-07-01 02:07:06
 *
 *  发送心跳检测
 */
- (void)startHeart;

/**
 *  <AUTHOR> 2015-07-01 02:07:30
 *
 *  发送心跳检测
 */
- (void)sendHeart;

/**
 *  <AUTHOR> 2015-07-01 02:07:56
 *
 *  停止心跳检测
 */
- (void)stopHeart;

/**
 *  <AUTHOR> 2015-06-30 13:06:47
 *
 *  发送数据
 *
 *  @param dataVo
 */
-(void)sendData:(TKDataVo *)dataVo;

/**
 *  <AUTHOR> 2015-03-28 17:03:52
 *
 *  断开socket连接
 */
-(void)disConnect;

/**
 *  <AUTHOR> 2015-05-11 13:05:37
 *
 *  释放上下文
 */
- (void)releaseCTX;

@end
