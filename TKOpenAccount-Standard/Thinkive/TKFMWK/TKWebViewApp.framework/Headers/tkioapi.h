/* ioapi.h -- IO base function header for compress/uncompress .zip
   files using zlib + zip or unzip API

   Version 1.01e, February 12th, 2005

   Copyright (C) 1998-2005 <PERSON>
*/

#ifndef TK_ZLIBIOAPI_H
#define TK_ZLIBIOAPI_H


#define TK_ZLIB_FILEFUNC_SEEK_CUR (1)
#define TK_ZLIB_FILEFUNC_SEEK_END (2)
#define TK_ZLIB_FILEFUNC_SEEK_SET (0)

#define TK_ZLIB_FILEFUNC_MODE_READ      (1)
#define TK_ZLIB_FILEFUNC_MODE_WRITE     (2)
#define TK_ZLIB_FILEFUNC_MODE_READWRITEFILTER (3)

#define TK_ZLIB_FILEFUNC_MODE_EXISTING (4)
#define TK_ZLIB_FILEFUNC_MODE_CREATE   (8)


#ifndef TK_ZCALLBACK

#if (defined(WIN32) || defined (WINDOWS) || defined (_WINDOWS)) && defined(CALLBACK) && defined (USEWINDOWS_CALLBACK)
#define TK_ZCALLBACK CALLBACK
#else
#define TK_ZCALLBACK
#endif
#endif

#ifdef __cplusplus
extern "C" {
#endif

typedef voidpf (TK_ZCALLBACK *tkzip_open_file_func) OF((voidpf opaque, const char* filename, int mode));
typedef uLong  (TK_ZCALLBACK *tkzip_read_file_func) OF((voidpf opaque, voidpf stream, void* buf, uLong size));
typedef uLong  (TK_ZCALLBACK *tkzip_write_file_func) OF((voidpf opaque, voidpf stream, const void* buf, uLong size));
typedef long   (TK_ZCALLBACK *tkzip_tell_file_func) OF((voidpf opaque, voidpf stream));
typedef long   (TK_ZCALLBACK *tkzip_seek_file_func) OF((voidpf opaque, voidpf stream, uLong offset, int origin));
typedef int    (TK_ZCALLBACK *tkzip_close_file_func) OF((voidpf opaque, voidpf stream));
typedef int    (TK_ZCALLBACK *tkzip_testerror_file_func) OF((voidpf opaque, voidpf stream));

typedef struct tk_zlib_filefunc_def_s
{
    tkzip_open_file_func      zopen_file;
    tkzip_read_file_func      zread_file;
    tkzip_write_file_func     zwrite_file;
    tkzip_tell_file_func      ztell_file;
    tkzip_seek_file_func      zseek_file;
    tkzip_close_file_func     zclose_file;
    tkzip_testerror_file_func zerror_file;
    voidpf                    opaque;
} tk_zlib_filefunc_def;

void tkzip_fill_fopen_filefunc OF((tk_zlib_filefunc_def* pzlib_filefunc_def));

#define TK_ZREAD(filefunc,filestream,buf,size) ((*((filefunc).zread_file))((filefunc).opaque,filestream,buf,size))
#define TK_ZWRITE(filefunc,filestream,buf,size) ((*((filefunc).zwrite_file))((filefunc).opaque,filestream,buf,size))
#define TK_ZTELL(filefunc,filestream) ((*((filefunc).ztell_file))((filefunc).opaque,filestream))
#define TK_ZSEEK(filefunc,filestream,pos,mode) ((*((filefunc).zseek_file))((filefunc).opaque,filestream,pos,mode))
#define TK_ZCLOSE(filefunc,filestream) ((*((filefunc).zclose_file))((filefunc).opaque,filestream))
#define TK_ZERROR(filefunc,filestream) ((*((filefunc).zerror_file))((filefunc).opaque,filestream))


#ifdef __cplusplus
}
#endif

#endif
