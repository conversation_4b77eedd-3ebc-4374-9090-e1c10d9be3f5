//
//  TKHttpAddress.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2023/3/1.
//  Copyright © 2023 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  <AUTHOR> 2015-02-28 16:02:51
 *
 *  网络地址
 */
@interface TKHttpAddress : NSObject

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 * 机房ID
 */
@property(nonatomic,copy)NSString *roomName;

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 *  服务ID
 */
@property(nonatomic,copy)NSString *serverName;

/**
 是否域名
 */
@property(nonatomic,assign)BOOL isDomain;

/**
 是否IPV6
 */
@property(nonatomic,assign)BOOL isIPV6;

/**
 *  <AUTHOR> 2015-02-28 16:02:26
 *
 *  url地址
 */
@property(nonatomic,copy)NSString *url;

/**
 * 测速链接
 */
@property(nonatomic,copy)NSString *speedUrl;

/**
 地址描述
 */
@property(nonatomic,copy)NSString *desc;

/**
 *  <AUTHOR> 2015-02-28 16:02:09
 *
 *  是否存活
 */
@property(nonatomic,assign)BOOL isAlive;

/**
 *  <AUTHOR> 2016-01-07 10:01:37
 *
 *  测速的综合得分,分值越低表示越快
 */
@property(nonatomic,assign)double speed;

/**
 *  <AUTHOR> 2016-01-12 01:01:58
 *
 *  开始测试时间
 */
@property(nonatomic,assign)NSTimeInterval btime;

/**
 *  <AUTHOR> 2016-01-12 01:01:05
 *
 *  测试花费时间
 */
@property(nonatomic,assign)NSTimeInterval time;

@end
