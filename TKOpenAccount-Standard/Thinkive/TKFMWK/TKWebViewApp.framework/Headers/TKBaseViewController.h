//
//  TKBaseViewController.h
//  TKAppBase_V1
//
//  Created by liubao on 14-12-16.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UIViewController+TKBaseViewController.h"

typedef enum{
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  截屏
     */
    TKCheckScreenType_Shot = 0,
    /**
     *  <AUTHOR> 2015-09-15 09:09:12
     *
     *  录屏
     */
    TKCheckScreenType_Record = 1
}TKCheckScreenType;

@interface UINavigationController(TKBaseUINavigationController)

@end

@interface UITabBarController(TKBaseUITabBarController)

@end

/**
 *  <AUTHOR> 2014-12-16 20:12:06
 *
 *  基础控制器
 */
@interface TKBaseViewController : UIViewController

/**
 * 是否隐藏或显示系统的导航栏(0:显示，1：隐藏）控制器消失后恢复原状，默认为空不处理，保持默认现状
 */
@property(nonatomic,copy)NSString *isHideSystemNavigationBar;

/**
 * 是否使用导航栏悬浮效果(0:不使用，1：使用）控制器消失后恢复原状，默认为空不处理，保持默认现状
 */
@property(nonatomic,copy)NSString *isUseSystemNavigationTranslucent;

/**
 * 是否隐藏或显示系统状态栏(0:显示，1：隐藏）控制器消失后恢复原状，默认为空不处理，保持默认现状
 */
@property(nonatomic,copy)NSString *isHideSystemStatusBar;

/**
 * 当前控制器执行WillDisappear时，是否正在被父导航栏控制器Pop出来
 */
@property(nonatomic,assign)BOOL isByParentNavgationPopFlag;

/**
 *  状态栏样式
 */
@property(nonatomic,assign) UIStatusBarStyle statusBarStyle;

/**
 * 是否页面关闭时候恢复状态栏的样式
 */
@property(nonatomic,assign)BOOL isResetParentStatusBarStyle;

/**
 * 检测父控制器是否为导航栏控制器
 */
@property(nonatomic,readonly,assign)BOOL isInParentNavigationController;

/**
 * 检测父控制器是否为TabBar控制器
 */
@property(nonatomic,readonly,assign)BOOL isInParentTabBarController;

/**
 *  获取当前导航控制器
 */
@property(nonatomic,readonly,retain)UINavigationController *currentNavigationController;

/**
 * 获取当前TabBar控制器
 */
@property(nonatomic,readonly,retain)UITabBarController *currentTabBarController;

/**
 * 防截屏防录像检测提示界面
 */
@property(nonatomic,retain) UIView *checkScreenShotRecordView;

/**
 * 是否开启防截屏防录像检测
 */
@property(nonatomic,assign)BOOL isCheckScreenShotRecord;

/**
 *是否开启H5监听录屏变化通知，开启后，录屏发生改变会通过50132插件通知H5
 */
@property (nonatomic,assign)BOOL isListenScreenShotChange;

/**
 *  <AUTHOR> 2016-03-17 00:03:13
 *
 *  截屏提示语
 */
@property (nonatomic,copy)NSString *screenCaptureTip;

/**
 *  <AUTHOR> 2016-03-17 00:03:13
 *
 *  录屏提示语
 */
@property (nonatomic,copy)NSString *screenRecordingTip;

/**
 *  <AUTHOR> 2017-01-10 13:01:35
 *
 *  是否开启后台模糊效果
 */
@property (nonatomic,assign) BOOL isBlurBackground;

/**
 *  <AUTHOR> 2015-04-21 15:04:15
 *
 *  初始化大小和名称
 *
 *  @param frame 区域
 *  @param name  名称
 *
 *  @return
 */
-(id)initWithFrame:(CGRect)frame name:(NSString *)name;

/**
 *  <AUTHOR> 2015-04-21 15:04:31
 *
 *  初始化区域大小
 *
 *  @param frame
 *
 *  @return
 */
-(id)initWithFrame:(CGRect)frame;

/**
 *  初始化上下文环境变量值
 */
-(void)initCTX;

/**
 * 处理截屏录屏检测通知
 */
-(void)handleCheckScreenShotRecord:(TKCheckScreenType)checkScreenType;

/**
 * 处理录屏检测变化通知
 */
-(void)handleScreenShotRecordChange:(BOOL)isScreenShotRecording;
@end
