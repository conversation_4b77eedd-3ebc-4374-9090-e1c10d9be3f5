//
//  TKMulImageBrowserView.h
//  TKPhotoSelectDemo
//
//  Created by liupm on 16/4/21.
//  Copyright © 2016年 liupm. All rights reserved.
//

#import <UIKit/UIKit.h>
@protocol TKMulImageBrowserViewDelegate <NSObject>

/**
 *  <AUTHOR> 16-04-21 13:04:34
 *
 *  @brief  滑动到某页
 *
 *  @param page
 */
-(void)didScrollToPage:(NSInteger)page;

@end


@interface TKMulImageBrowserView : UIView

@property(nonatomic,weak) id<TKMulImageBrowserViewDelegate>delegate;

/**
 *  <AUTHOR> 16-04-21 10:04:43
 *
 *  @brief  刷新数据
 *
 *  @param dataSource
 */
-(void)reloadData:(NSMutableArray *)dataSource;
@end

