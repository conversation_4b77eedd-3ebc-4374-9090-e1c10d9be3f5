//
//  TKSocketDao.h
//  TKApp
//
//  Created by l<PERSON><PERSON> on 14-11-24.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseDao.h"
#import "TKServiceDelegate.h"

/**
 *  <AUTHOR> 2014-11-25 23:11:31
 *
 *  socket协议
 
 =============请求错误定义======================
 -990 服务器连接认证失败
 -991 服务器获取连接异常
 -992 服务器建立连接中断
 -993 服务器建立连接超时
 -994 服务器建立连接拒绝
 -995 服务器建立连接网络异常
 -996 客户端数据请求超时
 -999 服务器返回的数据格式错误
 -900 请求功能号不能为空
 
=============行情错误定义======================
  0   正常
 -1   找不到股票代码
 -2   包体长度有误
 -100 服务器正在初始化
 -101 行情服务器返回数据包与客户端配置文件不匹配
 
 */
@interface TKBusDao : TKBaseDao

/**
 *  <AUTHOR> 2014-11-25 15:11:20
 *
 *  单例模式
 *
 *  @return 单例
 */
+(TKBusDao *)shareInstance;

@end
