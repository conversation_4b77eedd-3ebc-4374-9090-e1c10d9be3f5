//
//  TKKeyBoardDelegate.h
//  TKStockKeyBoardDemo
//
//  Created by liupm on 15-3-17.
//  Copyright (c) 2015年 liupm. All rights reserved.
//
/**
 键盘类型
 */
typedef enum
{
    /**
     *  <AUTHOR> 2015-03-31 13:03:52
     *
     *  有序数字键盘
     */
    TKKeyBoardTypeNum = 0,
    
    /**
     *  <AUTHOR> 2015-07-22 18:07:04
     *
     *  随机数字键盘
     */
    TKKeyBoardTypeRandNum = 1,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  加强版有序数字键盘
     */
    TKKeyBoardTypeNumStrong = 2,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  加强版随机数字键盘
     */
    TKKeyBoardTypeRandNumStrong = 3,
    
    /**
     *  <AUTHOR> 2015-03-31 13:03:02
     *
     *  英文键盘
     */
    TKKeyBoardTypeAlpha = 4,
    
    /**
     *  <AUTHOR> 2015-03-31 13:03:30
     *
     *  股票键盘
     */
    TKKeyBoardTypeStock = 5,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  交易买卖键盘
     */
    TKKeyBoardTypeTrade = 6,
    
    /**
     *  <AUTHOR> 2017-05-16 21:05:41
     *
     *  三板股票键盘
     */
    TKKeyBoardTypeSBStock = 7,
    
    /**
     *  <AUTHOR> 2017-05-16 21:05:41
     *
     *  科创版股票键盘
     */
    TKKeyBoardTypeKCBStock = 8,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  招商有序数字键盘
     */
    TKKeyBoardTypeMSNum  = 10,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  招商随机数字键盘
     */
    TKKeyBoardTypeMSRandNum = 11,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  招商英文键盘
     */
    TKKeyBoardTypeMSAlpha  = 12,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  招商数字符号键盘
     */
    TKKeyBoardTypeMSNumSymbol  = 13,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  招商符号键盘
     */
    TKKeyBoardTypeMSSymbol  = 14,
    
    /**
     *  <AUTHOR> 2017-04-20 00:04:03
     *
     *  天风登录数字键盘
     */
    TKKeyBoardTypeTFLoginNum = 20,
    
    /**
     *  <AUTHOR> 2017-04-20 00:04:49
     *
     *  天风股票数字键盘
     */
    TKKeyBoardTypeTFStock = 21,
    
    /**
     *  <AUTHOR> 2017-04-20 00:04:35
     *
     *  天风买入价格数字键盘
     */
    TKKeyBoardTypeTFBuyPrice = 22,
    
    /**
     *  <AUTHOR> 2017-04-20 00:04:35
     *
     *  天风买入数量数字键盘
     */
    TKKeyBoardTypeTFBuyNum = 23,
    
    /**
     *  <AUTHOR> 2017-04-20 00:04:35
     *
     *  天风字母键盘
     */
    TKKeyBoardTypeTFAlpha = 24,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  天风数字符号键盘
     */
    TKKeyBoardTypeTFNumSymbol  = 25,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  天风符号键盘
     */
    TKKeyBoardTypeTFSymbol  = 26,
    
    /**
     *  <AUTHOR> 2015-05-08 19:05:02
     *
     *  天风随机登录数字键盘
     */
    TKKeyBoardTypeTFLoginRandomNum  = 27,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:33
     *
     *  @brief  一创期权通键盘深圳快速报价键盘
     */
    TKKeyBoardTypeOptionSZFast  = 30,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:33
     *
     *  @brief  一创期权通键盘上海快速报价键盘
     */
    TKKeyBoardTypeOptionSHFast  = 31,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:50
     *
     *  @brief  一创期权通深圳数字报价键盘
     */
    TKKeyBoardTypeOptionSZNum = 32,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:50
     *
     *  @brief  一创期权通上海数字报价键盘
     */
    TKKeyBoardTypeOptionSHNum = 33,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:50
     *
     *  @brief  华泰英文键盘
     */
    TKKeyBoardTypeHTAlpha = 40,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:50
     *
     *  @brief  华泰数字键盘
     */
    TKKeyBoardTypeHTNum = 41,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:50
     *
     *  @brief  华泰符号键盘
     */
    TKKeyBoardTypeHTSymbol = 42,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:50
     *
     *  @brief  华安买卖数量键盘
     */
    TKKeyBoardTypeHABuyNum = 50,
    
    /**
     *  <AUTHOR> 16-10-12 14:10:50
     *
     *  @brief  华安买卖金额键盘
     */
    TKKeyBoardTypeHABuyPrice = 51,
    
    /**
     *  <AUTHOR> 2017-05-16 21:05:41
     *
     *  取消自定义键盘
     */
    TKKeyBoardTypeNone = 99
}TKKeyBoardType;

/**
 * <AUTHOR> 14-11-25 15:11:04
 * 键盘按键事件代理
 */
@protocol TKKeyBoardEventDelegate <NSObject>

@optional

/**
 *  追加字符
 */
- (void)appendChar:(NSString *)charStr;

/**
 *  退格删除字符
 */
- (void)deleteChar;

/**
 * 清空值
 */
-(void)clearValue;

/**
 * 点击确定
 */
-(void)doConfirm;

/**
 * 前进键
 */
-(void)doForward;

/**
 * 后退键
 */
-(void)doGoBack;

/**
 *  其他键
 */
-(void)doOtherChar:(NSString *)charStr;

/**
 * 键盘头部输入框事件
 */
-(void)doTitleInput:(NSString *)content;

/**
 * <AUTHOR> 15-03-17 15:03:43
 *
 * 切换系统中文键盘
 */
- (void)changeSysZHCNKeyBoard;

/**
 * <AUTHOR> 15-03-17 15:03:43
 *
 * 切换键盘
 */
- (void)changeKeyBoard:(NSString *)keyBoardType defaultKeyBoard:(NSString *)defaultKeyBoard;

/**
 * <AUTHOR> 15-03-17 15:03:43
 *
 * 切换键盘
 */
- (void)changeKeyBoard:(NSString *)keyBoardType;

/**
 * <AUTHOR> 15-03-17 15:03:43
 *
 * 切换键盘回调
 */
- (void)changeKeyBoardFinish;

/**
 * <AUTHOR> 14-11-25 11:11:11
 * 隐藏自定义键盘
 */
-(void)hideKeyBoard;

@end
