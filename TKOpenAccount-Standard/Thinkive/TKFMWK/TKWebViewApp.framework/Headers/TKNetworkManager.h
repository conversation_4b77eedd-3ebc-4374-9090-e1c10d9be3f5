//
//  TKNetworkManager.h
//  TKApp
//
//  Created by liu<PERSON> on 15-2-5.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKHttpAddress.h"

/**
 *  <AUTHOR> 2015-08-24 17:08:42
 *
 *  网络监听代理
 */
@protocol TKNetworkManagerDelegate <NSObject>

@optional

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  测速结果
 *
 *  @param address
 */
-(void)testSpeed:(TKHttpAddress *)address;

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  测速结束
 *
 *  @param address
 */
-(void)testSpeedFinished:(NSString *)serverName;

@end

/**
 *  <AUTHOR> 2015-02-05 09:02:30
 *
 *  网络智能测速管理模块
 */
@interface TKNetworkManager : NSObject

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  网络测速代理
 */
@property(nonatomic,weak)id<TKNetworkManagerDelegate> delegate;

/**
 *  <AUTHOR> 2015-02-05 09:02:10
 *
 *  单例模式
 *
 *  @return
 */
+(TKNetworkManager*)shareInstance;

/**
 *  <AUTHOR> 2015-02-05 14:02:02
 *
 *  开始测速
 */
-(void)startTest;

/**
 *  <AUTHOR> 2015-08-24 10:08:20
 *
 *  检查某个站点下面的各个线路的速度
 *
 *  @param serverName
 */
-(void)startCheck:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-08-24 10:08:20
 *
 *  测速某个站点下面的各个线路的速度，会执行回调用于渲染界面
 *
 *  @param serverName
 */
-(void)startTest:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-02-05 09:02:17
 *
 *  获取最快的网络环境
 *
 *  @param serverName 服务类别
 *
 *  @return 当前类别下最快的服务器地址
 */
-(NSString *)getFlastHost:(NSString *)serverName;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  设置服务器需要使用的站点地址
 *
 *  @param serverName 服务名称
 *  @param address    站点地址
 */
-(void)setServerUseAddress:(NSString *)serverName address:(TKHttpAddress *)address;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  获取当前正在使用的站点地址
 *
 *  @param serverName 服务名称
 */
-(TKHttpAddress *)getServerUseAddress:(NSString *)serverName;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  获取指定服务的站点地址
 *
 *  @param serverName 服务名称
 */
-(NSArray *)getServerAddresses:(NSString *)serverName;

/*
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  设置服务器是否开启手动保存模式,默认是NO，代表不开启
 *
 *  @param serverName      服务名称
 *  @param isManualMode    是否手动模式
 */
-(void)setServerUseAddressMode:(NSString *)serverName isManualMode:(BOOL)isManualMode;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  获取服务器是否开启手动保存模式
 *
 *  @param roomName 机房名称
 *  @param serverName      服务名称
 */
-(BOOL)isServerUseAddressManualMode:(NSString *)serverName;

/**
 * 清除本地缓存
 */
+(void)clearCache;
@end
