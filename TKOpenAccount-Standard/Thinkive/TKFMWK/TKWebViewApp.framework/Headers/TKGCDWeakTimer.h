//
//  TKGCDWeakTimer.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2019/8/23.
//  Copyright © 2019年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
typedef void (^TKGCDTimerHandler)(id userInfo);

@interface TKGCDWeakTimer : NSObject

+ (TKGCDWeakTimer *) scheduledTimerWithTimeInterval:(NSTimeInterval)interval
                                      target:(id)aTarget
                                    selector:(SEL)aSelector
                                    userInfo:(id)userInfo
                                     repeats:(BOOL)repeats;

+ (TKGCDWeakTimer *)scheduledTimerWithTimeInterval:(NSTimeInterval)interval
                                      block:(TKGCDTimerHandler)block
                                   userInfo:(id)userInfo
                                    repeats:(BOOL)repeats;

- (void)fire;
- (void)invalidate;

@property (nonatomic,copy) NSDate *fireDate;
@property (nonatomic,readonly) NSTimeInterval timeInterval;
@property (nonatomic,readonly) BOOL isValid;
@property (nonatomic, readonly, retain) id userInfo;

@end
