//
//  TKServer.h
//  TKAppBase_V1
//
//  Created by <PERSON><PERSON><PERSON> on 15-2-28.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKNetAddress.h"

typedef enum {
    TKIPMode_IPV4,
    TKIPMode_IPV6,
    TKIPMode_ALL
}TKIPMode;

typedef enum {
    //初始化串行测试
    TKSocketTestSpeedMode_InitSerial,
    //初始化并行测速
    TKSocketTestSpeedMode_InitConCurrent,
    //检测测速
    TKSocketTestSpeedMode_Test,
    //界面测速
    TKSocketTestSpeedMode_View
}TKSocketTestSpeedMode;

/**
 *  <AUTHOR> 2016-01-12 14:01:57
 *
 *  BUS服务器配置缓存
 *
 *  @return
 */
#define CACHE_BUS_CONFIG @"cache_busConfig.xml"

/**
 *  <AUTHOR> 2015-08-05 19:08:58
 *
 *  服务器配置缓存
 */
#define CACHE_SERVER_CONFIG @"cache_server.xml"


/**
 *  <AUTHOR> 2015-08-24 17:08:42
 *
 *  网络监听代理
 */
@protocol TKSocketSpeedServerDelegate <NSObject>

@optional

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  测速结果
 *
 *  @param address
 */
-(void)testSpeed:(TKNetAddress *)address testSpeedMode:(TKSocketTestSpeedMode)testSpeedMode;

/**
 *  <AUTHOR> 2015-08-24 17:08:15
 *
 *  测速结束
 *
 *  @param address
 */
-(void)testSpeedFinished:(NSString *)serverName testSpeedMode:(TKSocketTestSpeedMode)testSpeedMode;

@end

/**
 轮训模式
 */
typedef enum
{
    /**
     *  <AUTHOR> 2015-05-19 00:05:13
     *
     *  不测速模式
     */
    LBMODE_NONE = -1,
    /**
     *  <AUTHOR> 2015-05-19 00:05:13
     *
     *  随机模式
     */
    LBMODE_RANDOM = 0,
    /**
     *  <AUTHOR> 2015-05-19 00:05:22
     *
     *  顺序模式
     */
    LBMODE_LOOP = 1,
    /**
     *  <AUTHOR> 2015-05-19 00:05:32
     *
     *  主备模式
     */
    LBMODE_BACKUPS = 2,
    /**
     *  <AUTHOR> 2016-01-07 10:01:03
     *
     *  最优模式
     */
    LBMODE_BEST = 3,
    /**
     *  <AUTHOR> 2016-01-07 10:01:03
     *
     *  期货最优模式
     */
    LBMODE_QHBEST = 4,
    /**
     *  <AUTHOR> 2016-01-07 10:01:03
     *
     *  站点固定模式
     */
    LBMODE_NOCHANGE = 5,
    /**
     *  <AUTHOR> 2016-01-07 10:01:03
     *
     *  负载最优模式
     */
    LBMODE_FZBEST = 6
}LBMODE;

/**
 代理模式
 */
typedef enum
{
    /**
     *  <AUTHOR> 2015-05-19 00:05:13
     *
     *  普通
     */
    LBPOLICY_COMMON = 0,
    /**
     *  <AUTHOR> 2015-05-19 00:05:22
     *
     *  期货
     */
    LBPOLICY_FUTURES = 1,
    /**
     *  <AUTHOR> 2015-05-19 00:05:22
     *
     *  云模式
     */
    LBPOLICY_CLOUD = 2
}LBPOLICY;

/**
 *  <AUTHOR> 2015-02-28 16:02:17
 *
 *  服务器模型
 */
@interface TKServer : NSObject

/**
 *  <AUTHOR> 2015-02-28 16:02:33
 *
 *  是否开启国密站点
 */
@property(nonatomic, assign)BOOL isUseGMSite;

/**
 *  <AUTHOR> 2015-02-28 16:02:33
 *
 *  扫描时间间隔，秒
 */
@property(nonatomic,readonly,assign)int scanInterval;

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 *  网关名称
 */
@property(nonatomic,readonly,copy)NSString *gateWayName;

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 *  关联网关名称，主要用来标示行情服务器和行情推送服务器之间的关联关系
 */
@property(nonatomic,readonly,copy)NSString *refGateWayName;

/**
*  <AUTHOR> 2015-02-28 16:02:41
*
*  域名地址列表
*/
@property(nonatomic,retain)NSMutableArray *netDomainArr;
    
/**
 *  <AUTHOR> 2015-02-28 16:02:41
 *
 * IP地址列表
 */
@property(nonatomic,retain)NSMutableArray *netAddressArr;

/**
 *  <AUTHOR> 2015-02-28 17:02:57
 *
 *  服务器分组机房轮询模式
 */
@property(nonatomic,readonly,assign)int LBGroupMode;
    
/**
 *  <AUTHOR> 2015-02-28 17:02:57
 *
 *  服务器轮询模式
 */
@property(nonatomic,readonly,assign)int LBMode;

/**
 *  <AUTHOR> 2015-02-28 17:02:57
 *
 *  服务器策略
 */
@property(nonatomic,readonly,assign)int LBPolicy;

/**
 *  <AUTHOR> 2015-03-27 10:03:31
 *
 *  连接模式
 */
@property(nonatomic,readonly,assign)int mode;

/**
 *  <AUTHOR> 2015-02-28 17:02:41
 *
 *  版本号
 */
@property(nonatomic,readonly,copy)NSString *version;

/**
 *  <AUTHOR> 2015-02-28 17:02:13
 *
 *  超时时间
 */
@property(nonatomic,readonly,assign)int recTimeout;

/**
 *  <AUTHOR> 2015-02-28 17:02:13
 *
 *  心跳检测间隔时间，单位秒
 */
@property(nonatomic,readonly,assign)int heartTime;

/**
 *  <AUTHOR> 2015-02-28 17:02:13
 *
 *  站点缓存本地保留的时间，单位小时，默认7天，0代表永久缓存
 */
@property(nonatomic,readonly,assign)int saveCacheTime;

/**
 *  <AUTHOR> 2015-02-28 17:02:47
 *
 *  数据加密密钥
 */
@property(nonatomic,readonly,copy)NSString *key;

/**
 *  <AUTHOR> 2016-01-12 03:01:50
 *
 *  用户选择设置的地址
 */
@property(nonatomic,retain)TKNetAddress *useNetAddress;

/**
 *  <AUTHOR> 2016-01-12 03:01:50
 *
 *  当前正在使用的地址
 */
@property(nonatomic,retain,readonly)TKNetAddress *curNetAddress;

/**
 *  <AUTHOR> 2016-01-12 03:01:50
 *
 *  当前排除使用的地址列表
 */
@property(nonatomic,retain,readonly)NSMutableArray *excludesAddress;

/**
 *  <AUTHOR> 2016-01-12 03:01:50
 *
 *  当前正在使用的地址的索引
 */
@property(nonatomic,assign,readonly)NSInteger curNetAddressIndex;

/**
 *  <AUTHOR> 2016-03-01 00:03:53
 *
 *  配置文件名称
 */
@property(nonatomic,readonly,copy)NSString *functionConfig;

/**
 *  <AUTHOR> 2016-11-16 20:11:03
 *
 *  服务端公钥证书的名称
 */
@property(nonatomic,readonly,copy)NSString *serverPublicCer;

/**
 *  <AUTHOR> 2016-11-16 20:11:41
 *
 *  客户端公钥证书的名称
 */
@property(nonatomic,readonly,copy)NSString *clientPublicCer;

/**
 *  <AUTHOR> 2016-11-16 20:11:13
 *
 *  客户端私钥证书的名称
 */
@property(nonatomic,readonly,copy)NSString *clientPrivateCer;

/**
 *  <AUTHOR> 2016-11-16 20:11:48
 *
 *  客户端证书的密码
 */
@property(nonatomic,readonly,copy)NSString *clientPassword;

/**
 *  <AUTHOR> 2016-11-16 20:11:48
 *
 *  是否加密证书(0:否，1:是)，默认是0
 */
@property(nonatomic,readonly,assign)BOOL isEncrytCer;

/**
 *  <AUTHOR> 2016-11-16 20:11:48
 *
 *  是否发送Base64格式的加密证书到服务器认证(0:否，1:是)，默认是0
 */
@property(nonatomic,readonly,assign)BOOL isSendEncrytBase64Cer;

/**
 *  <AUTHOR> 2016-11-16 20:11:48
 *
 *  是否开启防外挂机制(0:否，1:是)，默认是0
 */
@property(nonatomic,readonly,assign)BOOL isGameGuard;

/**
 *  <AUTHOR> 2016-11-17 20:11:46
 *
 *  服务器版本号
 */
@property(nonatomic,readonly,copy)NSString *serverVersion;

/**
 *  <AUTHOR> 2016-11-17 20:11:46
 *
 *  服务器TAG
 */
@property(nonatomic,readonly,copy)NSString *serverTag;

/**
 *  <AUTHOR> 2016-11-18 10:11:43
 *
 *  服务器认证模式（0:不认证，1：密码认证，2：普通证书双向认证，3：普通证书双向认证+国密加密算法，4：国密证书双向认证）
 */
@property(nonatomic,readonly,copy)NSString *vityifyMode;

/**
 * 是否发送测速配置请求包（0:不发送，1:发送）默认是0
 */
@property(nonatomic,readonly,assign)BOOL isGetSpeedConfig;

/**
 *  <AUTHOR> 2015-02-28 17:02:26
 *
 *  是否以期货缓存的最优地址优先选择
 *
 *  @return
 */
@property(nonatomic,assign)BOOL isQHBestCacheAddressFirst;

/**
 * socket的实现类
 */
@property(nonatomic,copy)NSString *socketClassName;

/**
 * socket的测速实现类
 */
@property(nonatomic,copy)NSString *socketSpeedClassName;

/**
 *  <AUTHOR> 2015-06-26 20:06:40
 *
 *  用户信息
 */
@property (nonatomic,retain)id userInfo;

/**
 *  <AUTHOR> 2015-08-24 17:08:56
 *
 *  网络测速代理
 */
@property(nonatomic,weak)id<TKSocketSpeedServerDelegate> delegate;

/**
 *  <AUTHOR> 2015-02-28 17:02:00
 *
 *  初始化
 *
 *  @param name      名称
 *  @param configMap 配置文件
 *
 *  @return
 */
-(id)initWithServerName:(NSString *)name configMap:(NSMutableDictionary *)configMap;

/**
 *  <AUTHOR> 2015-02-28 17:02:26
 *
 *  获取配置所有的网关服务器地址（IP+域名），也就是属性netAddressArr + netDomainArr的集合
 *
 *  @return
 */
-(NSMutableArray *)getAllNetworkAddresses;

/**
 *  <AUTHOR> 2015-02-28 17:02:26
 *
 *  获取网关服务器地址
 *
 *  @return
 */
-(TKNetAddress *)getRmdServer;

/**
 *  <AUTHOR> 2015-02-28 17:02:26
 *
 *  获取指定地址和端口的网关服务器地址
 *
 *  @return
 */
-(TKNetAddress *)getServerAddressWithHost:(NSString *)host port:(int)port group:(NSString *)group;

/**
 *  <AUTHOR> 2015-02-28 17:02:26
 *
 *  获取指定地址和端口的网关服务器地址
 *  @param address 地址，格式为ip:port:group的字符串
 *
 *  @return
 */
-(TKNetAddress *)getServerAddress:(NSString*)address;

/**
 * 站点测速
 */
-(void)startTest:(TKSocketTestSpeedMode)testSpeedMode ipMode:(TKIPMode)ipMode;

/**
 * 取消测速
 */
-(void)stopTest;

@end
