//
//  TKQRCoderScanerViewController.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-6-23.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseViewController.h"

@protocol TKQRCoderScanerDelegate <NSObject>

/**
 *  <AUTHOR> 2015-06-23 20:06:14
 *
 *  处理扫描出来的二维码内容
 *
 *  @param content 内容
 */
-(void)processScanerContent:(NSString *)content;

/**
 *  <AUTHOR> 2017-02-27 12:02:52
 *
 *  处理照片的异常情况，例如没有权限
 *
 *  @param errorNo    错误号
 *  @param errorInfo  错误信息
 */
-(void)processCropperImageWithErrorNo:(int)errorNo errorInfo:(NSString *)errorInfo isDialogShownResult:(BOOL)isDialogShownResult;

@end

/**
 *  <AUTHOR> 2015-06-23 20:06:42
 *
 *  扫描图片二维码
 */
@interface TKQRCoderScanerViewController : TKBaseViewController

/**
 *  <AUTHOR> 2015-06-23 20:06:03
 *
 *  处理扫描出来的二维码内容代理对象
 */
@property(nonatomic,weak)id<TKQRCoderScanerDelegate> delegate;

/**
 *  <AUTHOR> 2015-07-28 13:07:00
 *
 *  状态栏背景颜色
 */
@property(nonatomic,retain)UIColor *statusBarBgColor;

/**
 *  <AUTHOR> 2017-02-18 13:02:41
 *
 *  如果Title不为空，用来设置Ttile的文字颜色
 */
@property(nonatomic,retain)UIColor *titleColor;

/**
 *  <AUTHOR> 2015-07-28 13:07:00
 *
 *  是否显示相册
 */
@property(nonatomic,assign)BOOL isShowPhoto;

@end
