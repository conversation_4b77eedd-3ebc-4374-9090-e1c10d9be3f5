//
// Copyright 2011-2014 NimbusKit
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#import "TKNIPreprocessorMacros.h" /* for weak */

@class TKNIOperation;

typedef void (^TKNIOperationBlock)(TKNIOperation* operation);
typedef void (^TKNIOperationDidFailBlock)(TKNIOperation* operation, NSError* error);

/**
 * For writing code that runs concurrently.
 *
 * @ingroup NimbusCore
 * @defgroup Operations Operations
 *
 * This collection of NSOperation implementations is meant to provide a set of common
 * operations that might be used in an application to offload complex processing to a separate
 * thread.
 */

@protocol TKNIOperationDelegate;

/**
 * A base implementation of an NSOperation that supports traditional delegation and blocks.
 *
 * <h2>Subclassing</h2>
 *
 * A subclass should call the operationDid* methods to notify the delegate on the main thread
 * of changes in the operation's state. Calling these methods will notify the delegate and the
 * blocks if provided.
 *
 * @ingroup Operations
 */
@interface TKNIOperation : NSOperation

@property (weak) id<TKNIOperationDelegate> delegate;
@property (readonly, strong) NSError* lastError;
@property (assign) NSInteger tag;

@property (copy) TKNIOperationBlock didStartBlock;
@property (copy) TKNIOperationBlock didFinishBlock;
@property (copy) TKNIOperationDidFailBlock didFailWithErrorBlock;
@property (copy) TKNIOperationBlock willFinishBlock;

- (void)didStart;
- (void)didFinish;
- (void)didFailWithError:(NSError *)error;
- (void)willFinish;

@end

/**
 * The delegate protocol for an TKNIOperation.
 *
 * @ingroup Operations
 */
@protocol TKNIOperationDelegate <NSObject>
@optional

/** @name [TKNIOperationDelegate] State Changes */

/** The operation has started executing. */
- (void)nimbusOperationDidStart:(TKNIOperation *)operation;

/**
 * The operation is about to complete successfully.
 *
 * This will not be called if the operation fails.
 *
 * This will be called from within the operation's runloop and must be thread safe.
 */
- (void)nimbusOperationWillFinish:(TKNIOperation *)operation;

/**
 * The operation has completed successfully.
 *
 * This will not be called if the operation fails.
 */
- (void)nimbusOperationDidFinish:(TKNIOperation *)operation;

/**
 * The operation failed in some way and has completed.
 *
 * operationDidFinish: will not be called.
 */
- (void)nimbusOperationDidFail:(TKNIOperation *)operation withError:(NSError *)error;

@end


// TKNIOperation

/** @name Delegation */

/**
 * The delegate through which changes are notified for this operation.
 *
 * All delegate methods are performed on the main thread.
 *
 * @fn TKNIOperation::delegate
 */


/** @name Post-Operation Properties */

/**
 * The error last passed to the didFailWithError notification.
 *
 * @fn TKNIOperation::lastError
 */


/** @name Identification */

/**
 * A simple tagging mechanism for identifying operations.
 *
 * @fn TKNIOperation::tag
 */


/** @name Blocks */

/**
 * The operation has started executing.
 *
 * Performed on the main thread.
 *
 * @fn TKNIOperation::didStartBlock
 */

/**
 * The operation has completed successfully.
 *
 * This will not be called if the operation fails.
 *
 * Performed on the main thread.
 *
 * @fn TKNIOperation::didFinishBlock
 */

/**
 * The operation failed in some way and has completed.
 *
 * didFinishBlock will not be executed.
 *
 * Performed on the main thread.
 *
 * @fn TKNIOperation::didFailWithErrorBlock
 */

/**
 * The operation is about to complete successfully.
 *
 * This will not be called if the operation fails.
 *
 * Performed in the operation's thread.
 *
 * @fn TKNIOperation::willFinishBlock
 */


/**
 * @name Subclassing
 *
 * The following methods are provided to aid in subclassing and are not meant to be
 * used externally.
 */

/**
 * On the main thread, notify the delegate that the operation has begun.
 *
 * @fn TKNIOperation::didStart
 */

/**
 * On the main thread, notify the delegate that the operation has finished.
 *
 * @fn TKNIOperation::didFinish
 */

/**
 * On the main thread, notify the delegate that the operation has failed.
 *
 * @fn TKNIOperation::didFailWithError:
 */

/**
 * In the operation's thread, notify the delegate that the operation will finish successfully.
 *
 * @fn TKNIOperation::willFinish
 */
