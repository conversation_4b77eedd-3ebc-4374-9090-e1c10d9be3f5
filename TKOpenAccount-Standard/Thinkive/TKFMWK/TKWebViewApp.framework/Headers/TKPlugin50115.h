//
//  TKPlugin50115.h
//  TKAppBase_V1
//
//  Created by liubao on 15-8-6.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBasePlugin.h"

/**
 *  <AUTHOR> 2014-11-27 12:11:34
 *
 *  打开webview控制器的拦截协议
 */
@protocol TKOpenWebViewControllerFilterDelegate <NSObject>

@required
/**
 * 打开webview控制器的拦截器，如果返回NO，代表被拦截不继续原来的功能，YES会继续向下执行
 */
-(BOOL)onFilterOpenWebViewController:(NSString *)moduleName param:(NSDictionary *)param currentPlugin:(TKBasePlugin *)currentPlugin currentController:(UIViewController *)currentController;

@end

/**
 *  <AUTHOR> 2015-08-06 20:08:43
 *
 *  实现通知原生打开一个WebView,加载url地址
 */
@interface TKPlugin50115 : TKBasePlugin

@end
