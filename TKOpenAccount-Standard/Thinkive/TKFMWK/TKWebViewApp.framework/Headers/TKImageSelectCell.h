//
//  TKIMGroupPhotoCell.h
//  TKPhotoSelectDemo
//
//  Created by liupm on 16/4/20.
//  Copyright © 2016年 liupm. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKPhotoModel.h"
@protocol TKImageSelectCellDelegate <NSObject>
/**
 *  <AUTHOR> 16-04-20 23:04:59
 *
 *  @brief  照片是否被选中
 *
 *  @param model
 */
-(void)photo:(TKPhotoModel *)model isSelected:(BOOL)isSelected;
@end
@interface TKImageSelectCell : UICollectionViewCell
@property(nonatomic,strong) UIButton *checkBox;
@property(nonatomic,strong)TKPhotoModel *model;
@property(nonatomic,weak) id<TKImageSelectCellDelegate> delegate;
/**
 *  <AUTHOR> 16-04-20 22:04:24
 *
 *  @brief  选中图片
 */
-(void)doCheck;

@end
