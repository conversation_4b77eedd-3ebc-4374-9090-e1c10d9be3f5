//
//  TKBaseSwipeBackWebViewController.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/7/24.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseQRCodeWebViewController.h"

/**
 * 支持滑动返回功能
 */
@interface TKBaseSwipeBackWebViewController : TKBaseQRCodeWebViewController

/**
 *  是否支持滑动返回
 */
@property (nonatomic,assign)BOOL isSupportSwipingBack;

/**
 * 是否隐藏滑动返回的展示效果
 */
@property (nonatomic,assign)BOOL isHideSwipingBackEffect;

/**
 * 当前是否使用仿微信WebView的滑动返回
 */
@property (nonatomic,assign)BOOL isUseWXSwipingBack;

/**
 * 是否支持后退
 */
@property (nonatomic,assign) BOOL allowsBackNavigationGestures;

/**
 * 是否支持前进
 */
@property (nonatomic,assign) BOOL allowsForwardNavigationGestures;

/**
 *  通知原生H5发生跳转，并记录历史堆栈,方便以后滑动返回
 *  @param fromPageUrl 来源页面编码
 *  @param toPageUrl   目标页面编码
 */
-(void)recordWebViewHistoryWithFromPageUrl:(NSString *)fromPageUrl toPageUrl:(NSString *)toPageUrl;

@end
