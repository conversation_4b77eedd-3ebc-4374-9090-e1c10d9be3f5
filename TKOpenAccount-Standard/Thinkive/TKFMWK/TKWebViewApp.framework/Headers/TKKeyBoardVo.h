//
//  TKKeyBoardVo.h
//  TKApp
//
//  Created by 刘宝 on 2019/9/25.
//  Copyright © 2019年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKKeyBoardBoxVo.h"
#import "TKKeyBoardBoxVo.h"

/**
 * 键盘模型配置
 */
@interface TKKeyBoardVo : NSObject

/**
 * 键盘实现类
 */
@property(nonatomic,copy) NSString *impClass;

/**
 * 键盘类型
 */
@property(nonatomic,copy) NSString *type;

/**
 * 是否支持中英文切换
 */
@property(nonatomic,copy) NSString *isZHCN;

/**
 * 键盘主题
 */
@property(nonatomic,copy) NSString *theme;

/**
 * 键盘宽度
 */
@property(nonatomic,assign)CGFloat width;

/**
 * 键盘高度
 */
@property(nonatomic,assign)CGFloat height;

/**
 * 键盘高度
 */
@property(nonatomic,assign)CGFloat relHeight;

/**
 * 键盘水平布局间距
 */
@property(nonatomic,assign)CGFloat hSpace;

/**
 * 键盘垂直布局间距
 */
@property(nonatomic,assign)CGFloat vSpace;

/**
 * 键盘背景色
 */
@property(nonatomic,retain)UIColor *bgColor;

/**
 * 键盘标题是否默认隐藏
 */
@property(nonatomic,assign)BOOL isHideTitle;

/**
 * 键盘隐藏标题栏后保留的顶部间距
 */
@property(nonatomic,assign)CGFloat hideTitleSpan;

/**
 * 键盘标题
 */
@property(nonatomic,retain)TKKeyBoardBoxVo *title;

/**
 * 键盘内容
 */
@property(nonatomic,retain)TKKeyBoardBoxVo *content;

/**
 * 键盘是否防止截屏
 */
@property(nonatomic,assign) BOOL isNoCutScreen;

/**
 * 键盘描述
 */
@property(nonatomic,copy)NSString *desc;

/**
 * 备用字段
 */
@property(nonatomic,retain)NSDictionary *userInfo;

@end
