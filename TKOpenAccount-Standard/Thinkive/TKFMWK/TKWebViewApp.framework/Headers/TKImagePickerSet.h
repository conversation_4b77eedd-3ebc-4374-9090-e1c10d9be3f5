//
//  TKImagePickerSetHelper.h
//  TKPhotoSelectDemo
//
//  Created by liupm on 16/4/21.
//  Copyright © 2016年 liupm. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKImagePickerDefinition.h"

@interface TKImagePickerSet : NSObject
//是否显示相册列表
@property(nonatomic,assign) BOOL showAblumList;
//是否允许多选
@property(nonatomic,assign) BOOL allowsMultipleSelection;
//最多允许选几个
@property(nonatomic,assign) NSInteger maxAllowSelect;
//图片回调
@property(nonatomic,copy) TKImagePickerCallBack imageCallBck;

/**
 *  <AUTHOR> 16-04-21 16:04:30
 *
 *  @brief  单例
 *
 *  @return
 */
+(TKImagePickerSet *)shareInstance;

@end
