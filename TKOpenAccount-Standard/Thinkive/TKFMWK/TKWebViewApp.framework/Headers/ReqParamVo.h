//
//  ReqParamVo.h
//  TKApp
//
//  Created by liubao on 14-11-24.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "LoadInfo.h"

/**
 *  <AUTHOR> 2014-11-26 10:11:11
 *
 *  Dao的类型
 */
typedef enum{
    TKDaoType_Http = 0,
    TKDaoType_Javascript = 1,
    TKDaoType_WebService = 2,
    TKDaoType_HttpToSocket = 98,
    TKDaoType_Socket = 99
}TKDaoType;

/**
 编码格式
 */
typedef enum {
  TKCharEncoding_DEFAULT = 0,
  TKCharEncoding_UTF_8,
  TKCharEncoding_GBK
}TKCharEncoding;

/**
 *  <AUTHOR> 2015-09-15 09:09:21
 *
 *  Dao请求模式
 */
typedef enum{
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  短连接
     */
    TKDaoMode_Short = 0,
    /**
     *  <AUTHOR> 2015-09-15 09:09:12
     *
     *  长连接
     */
    TKDaoMode_Long = 1
}TKDaoMode;

typedef enum{
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  AES加密
     */
    TKEncryMode_Aes = 0,
    /**
     *  <AUTHOR> 2015-09-15 09:09:12
     *
     *  DES加密
     */
    TKEncryMode_Des = 1
}TKEncryMode;

typedef enum{
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  正常数据
     */
    TKDataType_Normal = 0,
    /**
     *  <AUTHOR> 2015-09-15 09:09:12
     *
     *  加密
     */
    TKDataType_Encryt = 1,
    /**
     *  <AUTHOR> 2016-11-17 20:11:47
     *
     *  压缩
     */
    TKDataType_Compress = 2,
    /**
     *  <AUTHOR> 2016-11-17 20:11:27
     *
     *  先加密后压缩
     */
    TKDataType_Encryt_Compress = 3,
    /**
     *  <AUTHOR> 2016-11-17 20:11:00
     *
     *  先压缩后加密
     */
    TKDataType_Compress_Encryt = 4
}TKDataType;

typedef enum{
    /**
     *  默认自动适配模式
     */
    TKContentType_NONE = 0,
    
    /**
     *  application/x-www-form-urlencoded 类型
     */
    TKContentType_WWW_FORM = 1,
    
    /**
     *  multipart/form-data 类型
     */
    TKContentType_FORM_DATA = 2,
    
    /**
     *  application/json 类型
     */
    TKContentType_JSON = 3
}TKContentType;

/**
 *  <AUTHOR> 2015-09-08 20:09:29
 *
 *  上传代理
 */
@protocol TKUploadDelegate <NSObject>

@optional

/**
 *  <AUTHOR> 2015-09-08 20:09:03
 *
 *  显示进度百分比
 *
 *  @param newProgress
 */
- (void)showProgress:(float)newProgress;

/**
 *  <AUTHOR> 2015-09-08 20:09:28
 *
 *  显示上传进度数据
 *
 *  @param loadInfo
 */
- (void)showProgressData:(LoadInfo *)loadInfo;

@end

/**
 * 上传进度回调Block
 **/
typedef void(^TKUploadBlock)(LoadInfo *loadInfo);

/**
 *  请求对象
 */
@interface ReqParamVo : DynModel

/**
 *  流水号
 */
@property (nonatomic,copy)NSString *flowNo;

/**
 *  请求模块标示
 */
@property (nonatomic,copy)NSString *reqModule;

/**
 *  是否过滤重复请求，进行请求拦截
 */
@property (nonatomic,assign)BOOL isFilterRepeatRequest;

/**
 *  重复请求，进行请求拦截时间，单位毫秒
 */
@property (nonatomic,assign)int filterRepeatRequestTimeOut;

/**
 *  是否自动添加系统公共参数
 */
@property (nonatomic,assign)BOOL isAutoAddSysComParam;

/**
 *  是否全局请求，全局请求不可以取消掉,默认是NO
 */
@property (nonatomic,assign)BOOL isGlobRequest;

/**
 *  Http的请求的头
 */
@property (nonatomic,retain)NSDictionary *headerFieldDic;

/**
 *  请求对象
 */
@property (nonatomic,retain)NSMutableDictionary *reqParam;

/**
 *  URL地址
 */
@property (nonatomic,copy)NSString *url;

/**
 *  是否post请求
 */
@property (nonatomic,assign)BOOL isPost;

/**
 *  <AUTHOR> 2017-01-19 16:01:54
 *
 *  Http请求方法
 */
@property (nonatomic,copy)NSString *httpMethod;

/**
 *  <AUTHOR> 2017-01-19 16:01:54
 *
 *  Http请求ContentType
 */
@property (nonatomic,assign)TKContentType contentType;

/**
 *  是否异步
 */
@property (nonatomic,assign)BOOL isAsync __deprecated_msg("方法被建议取消使用，默认就是YES，设置为NO会不起作用");

/**
 *  <AUTHOR> 2015-10-09 21:10:55
 *
 *  请求超时时间，单位秒
 */
@property(nonatomic,assign)NSInteger timeOut;

/**
 *  请求的协议
 */
@property (nonatomic,assign)TKDaoType protocol;

/**
 *  调用开始时间
 */
@property (nonatomic,assign)NSTimeInterval beginTime;

/**
 *  是否显示缓冲效果（转菊花）
 */
@property (nonatomic,assign)BOOL isShowWait;

/**
 *  缓冲效果的文字
 */
@property (nonatomic,copy)NSString *waitTip;

/**
 *  <AUTHOR> 2015-05-04 20:05:21
 *
 *  是否显示网络缓冲效果
 */
@property (nonatomic,assign)BOOL isShowNetworkWait;

/**
 *  是否返回list数据
 */
@property (nonatomic,assign)BOOL isReturnList;

/**
 *  请求组号,如果是javascript请求，传对应浏览器对象的名称
 */
@property (nonatomic,copy)NSString *group;

/**
 *  数据处理函数，一般走默认不需要设置
 */
@property (nonatomic,weak)id processDataFunc;

/**
 *  <AUTHOR> 2014-11-26 22:11:51
 *
 *  扩展字段对象，目前javascript协议的时候传的是WebView对象，其他后面在定义
 */
@property (nonatomic,retain)id userInfo;

/**
 *  <AUTHOR> 2014-11-25 17:11:31
 *
 *  数据服务代理对象，一般走默认不需要设置
 */
@property(nonatomic,weak) id serviceDelegate;

/**
 *  <AUTHOR> 2015-04-22 10:04:46
 *
 *  是否上传文件
 */
@property(nonatomic,assign)BOOL isUpload;

/**
 *  <AUTHOR> 2015-09-08 20:09:34
 *
 *  上传代理
 */
@property(nonatomic,weak)id<TKUploadDelegate> uploadDelegate;

/**
 * 上传Block
 */
@property(nonatomic,copy)TKUploadBlock uploadBlock;

/**
 *  <AUTHOR> 2015-04-21 19:04:36
 *
 *  是否缓存
 */
@property(nonatomic,assign)BOOL isCache;

/**
 *  <AUTHOR> 2015-10-12 12:10:16
 *
 *  缓存类型
 */
@property(nonatomic,assign)TKCacheType cacheType;

/**
 *  <AUTHOR> 2015-10-09 21:10:15
 *
 *  缓存时间，单位是秒
 */
@property(nonatomic,assign)NSInteger cacheTime;

/**
 *  <AUTHOR> 2015-10-26 13:10:56
 *
 *  自定义dao的实现类名称，一般不需要设置
 */
@property(nonatomic,copy)NSString *daoName;

/**
 *  <AUTHOR> 2015-04-21 19:04:36
 *
 *  是否微服务RestFull接口
 */
@property(nonatomic,assign)BOOL isRestFull;

/**
 *  <AUTHOR> 2015-04-21 19:04:36
 *
 *  是否对参数进行url编码
 */
@property(nonatomic,assign)BOOL isURLEncode;

/**
 *  <AUTHOR> 2015-04-21 19:04:36
 *
 *  是否保持原始的参数格式
 */
@property(nonatomic,assign)BOOL isKeepOriginalParam;

/**
 *  <AUTHOR> 2015-04-21 19:04:36
 *
 *  是否对参数进行签名
 */
@property(nonatomic,assign)BOOL isURLSign;

/**
 *  <AUTHOR> 2016-08-12 12:08:44
 *
 *  签名key
 */
@property(nonatomic,copy)NSString *signKey;

/**
 *  <AUTHOR> 2016-08-12 12:08:28
 *
 *  签名ID
 */
@property(nonatomic,copy)NSString *signAppId;

/**
 *  <AUTHOR> 2016-04-29 10:04:23
 *
 *  是否对参数进行加密
 */
@property(nonatomic,assign)BOOL isURLEncry;

/**
*  <AUTHOR> 2016-04-29 10:04:23
*
*  是否对响应包进行加密
*/
@property(nonatomic,assign)BOOL isURLResponseEncry;

/**
 *  <AUTHOR> 2016-04-29 13:04:27
 *
 *  对参数进行加密模式
 */
@property(nonatomic,assign)TKEncryMode encryMode;

/**
 *  <AUTHOR> 2016-08-12 12:08:05
 *
 *  加密key
 */
@property(nonatomic,copy)NSString *encryKey;

/**
 渠道ID
 */
@property(nonatomic,copy)NSString *channelId;

/**
 *  <AUTHOR> 2016-11-17 20:11:51
 *
 *  请求公司编号
 */
@property(nonatomic,copy)NSString *companyId;

/**
 *  <AUTHOR> 2016-11-17 20:11:43
 *
 *  系统编号
 */
@property(nonatomic,copy)NSString *systemId;

/**
 *  <AUTHOR> 2017-06-07 23:06:03
 *
 *  socket的服务器ID,即BusConfig.xml里面配置的server的id，如果为空就自动用url代替
 */
@property(nonatomic,copy)NSString *busServerId;

/**
 *  <AUTHOR> 2017-06-07 23:06:10
 *
 *  socket的服务器接口功能号，如果为空，自动用reqParam里面的funcno或者funcNo代替
 */
@property(nonatomic,copy)NSString *busFuncNo;

/**
 *  <AUTHOR> 2017-01-14 14:01:11
 *
 *  是否登录请求
 */
@property(nonatomic,assign)BOOL isLoginReq;

/**
 *  <AUTHOR> 2016-01-29 00:01:16
 *
 *  返回字符编码集
 */
@property(nonatomic,assign)TKCharEncoding charEncoding;

/**
 *  <AUTHOR> 2016-11-17 20:11:18
 *
 *  数据类型协议
 */
@property(nonatomic,assign)TKDataType dataType;

/**
 *  <AUTHOR> 2015-09-15 09:09:41
 *
 *  Dao请求模式,目前针对2进制socket协议有效，支持长短连接
 */
@property(nonatomic,assign)TKDaoMode daoMode;

/**
 *  <AUTHOR> 2016-11-30 15:11:25
 *
 *  Https是否验证SSl证书的合法性
 */
@property(nonatomic,assign)BOOL isValidatesSSLCertificate;

/**
 *  <AUTHOR> 2017-03-25 14:03:30
 *
 *  是否回调到主线程运行响应，默认是YES
 */
@property(nonatomic,assign)BOOL isResponseRunMainThread;

/**
 *  <AUTHOR> 2017-05-12 04:05:01
 *
 *  是否需要调试
 */
@property(nonatomic,assign)BOOL isDebug;

/**
 *  <AUTHOR> 2017-05-12 04:05:01
 * 长连接服务器唯一标示
 *
 */
@property(nonatomic,copy)NSString *busClientUUID;

/**
 *  是否有回调函数
 */
@property (nonatomic,assign)BOOL isHasCallBackFunc;

@end
