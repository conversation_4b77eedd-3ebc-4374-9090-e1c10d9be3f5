//
//  TKBusClientManager.h
//  TKASClient
//
//  Created by l<PERSON><PERSON> on 15-6-30.
//  Copyright (c) 2015年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKComBusClient.h"

/**
 失败
 */
#define NOTE_BUSCLIENT_ERROR @"busClientError"

/**
 成功
 */
#define NOTE_BUSCLIENT_RESULT @"busClientResult"

/**
 连接/认证
 */
#define NOTE_BUSCLIENT_CONNECT @"busClientConnect"

/**
 断开
 */
#define NOTE_BUSCLIENT_DISCONNECT @"busClientDisConnect"

/**
 *  <AUTHOR> 2017-01-14 15:01:35
 *
 *  登录成功
 */
#define NOTE_BUSCLIENT_LOGIN @"busClientLogin"

/**
 *  <AUTHOR> 2017-01-14 15:01:35
 *
 *  退出登录
 */
#define NOTE_BUSCLIENT_LOGOUT @"busClientLogout"

/**
 *  <AUTHOR> 2014-11-25 23:11:31
 *
 *  数据推送协议通知
 */
#define NOTE_BUSCLIENT_PUSH_DATA @"note_bus_push_data"

/**
 * BusClient通知处理代理协议
 */
@protocol TKBusClientManagerNotificationDelegate <NSObject>

@required

/**
 * 处理通知消息
 * @param busClient 长连接对象
 * @param notName   通知名称
 * @param userInfo  携带数据
 */
-(void)onNotifyBusClientNotification:(TKComBusClient *)busClient noteName:(NSString *)noteName userInfo:(NSDictionary *)userInfo;

@end

/**
 *  <AUTHOR> 2015-06-29 22:06:22
 *
 *  BusClient管理中心，管理长/短连接
 */
@interface TKBusClientManager : NSObject<TKBusClientDelegate>

/**
 *  <AUTHOR> 2015-05-04 13:05:39
 *
 *  单例模式
 *
 *  @return
 */
+(TKBusClientManager *)shareInstance;

/**
 *  <AUTHOR> 2015-09-15 18:09:26
 *
 *  BusClient通知处理代理协议
 */
@property (nonatomic,weak)id<TKBusClientManagerNotificationDelegate> delegate;

/**
 *  <AUTHOR> 2015-09-15 18:09:26
 *
 *  是否网络ok
 */
@property (nonatomic,assign)BOOL isNetWorkOK;

/**
 *  <AUTHOR> 15-05-14 17:05:05
 *
 *  @brief  启动服务
 *
 *  @param serverName 服务名称
 *  @param version 服务版本
 */
-(void)start:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-05-04 13:05:49
 *
 *  重启服务
 *  @param serverName 服务名称
 *  @param version 服务版本
 */
-(void)restart:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-05-04 13:05:01
 *
 *  停止服务
 *  @param serverName 服务名称
 */
-(void)stop:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-06-29 23:06:41
 *
 *  删除服务
 *
 *  @param serverName 服务名称
 */
-(void)remove:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-06-29 23:06:41
 *
 *  延迟删除服务
 *
 *  @param serverName 服务名称
 */
-(void)delayRemove:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-06-29 23:06:11
 *
 *  获取服务连接
 *
 *  @param serverName 服务名
 *  @param version 版本号
 *
 *  @return 服务长连接
 */
-(TKComBusClient *)getTKBusClient:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-09-15 09:09:31
 *
 *  获取服务连接
 *
 *  @param serverName    服务名
 *  @param version       版本号
 *  @param isLongConnect 是否长连接
 *
 *  @return
 */
-(TKComBusClient *)getTKBusClient:(NSString *)serverName isLongConnect:(BOOL)isLongConnect;

/**
 *  <AUTHOR> 2017-01-14 15:01:30
 *
 *  获取指定名字缓存的长连接对象
 *
 *  @param serverName
 *
 *  @return
 */
-(TKComBusClient *)getCacheTKBusLongClient:(NSString *)serverName;

/**
 *  <AUTHOR> 2017-01-14 15:01:30
 *
 *  获取所有缓存的长连接对象
 *
 *  @param serverName
 *
 *  @return
 */
-(NSDictionary *)getAllCacheTKBusLongClient;

@end
