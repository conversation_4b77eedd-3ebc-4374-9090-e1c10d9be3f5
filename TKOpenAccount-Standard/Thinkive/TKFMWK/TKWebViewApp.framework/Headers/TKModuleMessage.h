//
//  TKMessage.h
//  TKApp
//
//  Created by liu<PERSON> on 14-12-1.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKUtil.h"
#import "ResultVo.h"

/**
 *  <AUTHOR> 2015-06-12 12:06:46
 *
 *  插件回调函数
 *
 *  @param result
 */
typedef void(^TKModuleMessageCallBackFunc)(ResultVo *resultVo);

/**
 *  <AUTHOR> 2017-01-13 08:01:42
 *
 *  模块交互行为
 */
typedef enum{
    /**
     *  <AUTHOR> 2017-01-13 08:01:23
     *
     *  弹层打开
     */
    TKModuleMessage_Action_Pop,
    /**
     *  <AUTHOR> 2017-01-13 08:01:32
     *
     *  Push打开
     */
    TKModuleMessage_Action_Push,
    /**
     *  <AUTHOR> 2017-01-13 08:01:44
     *
     *  关闭模块
     */
    TKModuleMessage_Action_Close,
    /**
     *  <AUTHOR> 2017-01-13 08:01:53
     *
     *  切换模块
     */
    TKModuleMessage_Action_Change,
    /**
     *  <AUTHOR> 2017-01-13 08:01:20
     *
     *  模块通知
     */
    TKModuleMessage_Action_NSNotify
}TKModuleMessage_Action;

/**
 *  <AUTHOR> 2014-12-09 16:12:05
 *
 *  模块消息
 */
@interface TKModuleMessage : DynModel

/**
 *  <AUTHOR> 2014-12-01 10:12:22
 *
 *  消息功能号
 */
@property(nonatomic,copy)NSString *funcNo;

/**
 *  <AUTHOR> 2014-12-09 16:12:54
 *
 *  消息来源的模块名称
 */
@property(nonatomic,copy)NSString *sourceMoudle;

/**
 *  <AUTHOR> 2014-12-09 16:12:54
 *
 *  接收消息的模块名称
 */
@property(nonatomic,copy)NSString *targetMoudle;

/**
 *  <AUTHOR> 2014-12-09 16:12:54
 *
 *  接收消息的排除模块名称集合，一般用于群发排除
 */
@property(nonatomic,retain)NSArray *excludeMoudles;

/**
 *  <AUTHOR> 2017-01-13 08:01:48
 *
 *  操作动作
 */
@property(nonatomic,assign)TKModuleMessage_Action action;

/**
 *  <AUTHOR> 2014-12-01 10:12:48
 *
 *  业务参数
 */
@property(nonatomic,retain)NSMutableDictionary *param;

/**
 * 发送消息当前控制器，先获取传过来的ViewController,获取不到再根据sourceModule获取ViewController，再获取不到取框架当前控制器对象
 */
@property(nonatomic,retain)UIViewController *currentViewController;

/**
 *  <AUTHOR> 2014-12-01 10:12:48
 *
 *  回调函数
 */
@property(nonatomic,copy)TKModuleMessageCallBackFunc callBackFunc;

@end
