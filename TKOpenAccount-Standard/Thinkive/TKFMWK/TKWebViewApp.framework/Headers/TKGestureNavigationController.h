//
//  MLNavigationController.h
//  MultiLayerNavigation
//
//  Created by <PERSON><PERSON> on 13-4-12.
//  Copyright (c) 2013年 Feather Chan. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol TKHidesBottomBarWhenPushedDelegate <NSObject>

-(BOOL)isHidesBottomBarWhenPushed:(UIViewController *)viewController;

@end

/**
 *  <AUTHOR> 2015-04-27 00:04:31
 *
 *  滑动返回导航栏
 *  1：解决ios7以下版本不支持滑动返回的效果 
 *  2:解决ios7和以上版本由于自定义navigaton的顶部item，设置左右item按钮导致的滑动返回被打断的问题。
 */
@interface TKGestureNavigationController : UINavigationController<UIGestureRecognizerDelegate>

/**
 控制是否要隐藏tabbar当push的时候
 */
@property (nonatomic,weak)id<TKHidesBottomBarWhenPushedDelegate>transitionDelegate;

/**
 *  <AUTHOR> 2015-04-27 00:04:27
 *
 *  是否在动画中
 */
@property (nonatomic, assign) BOOL viewTransitionInProgress;

/**
 *  <AUTHOR> 2015-04-27 00:04:27
 *
 *  是否可以滑动返回
 */
@property (nonatomic,assign) BOOL canDragBack;

/**
 *  <AUTHOR> 2015-04-20 21:04:54
 *
 *  设置无效的滑动区域
 */
@property (nonatomic,assign)CGRect disEnabledFrame;

/**
 *  <AUTHOR> 2015-07-07 09:07:19
 *
 *  截屏标志
 */
@property (nonatomic,assign)BOOL captureScreenFlag;

/**
 *  <AUTHOR> 2015-07-07 09:07:19
 *
 *  是否采用系统滑动返回模式，默认是YES
 */
@property (nonatomic,assign)BOOL isUseSystemMode;

/**
 *  <AUTHOR> 2015-12-09 10:12:44
 *
 *  弹出控制器
 */
- (void)popViewController;

/**
 *  <AUTHOR> 2016-02-25 13:02:24
 *
 *  是否添加滑动返回手势
 *
 *  @param isAddDragBackGestureRecognizer
 */
-(void)addDragBackGestureRecognizer:(BOOL)isAddDragBackGestureRecognizer;

@end
