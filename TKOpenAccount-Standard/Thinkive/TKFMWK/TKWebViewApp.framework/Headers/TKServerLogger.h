//
//  TKServerLogger.h
//  TKUtil_V1
//
//  Created by 刘宝 on 16/6/25.
//  Copyright © 2016年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKAbstractDatabaseLogger.h"

/**
 *  <AUTHOR> 2016-06-25 22:06:22
 *
 *  为保证顺序，同步发送日志到服务器
 *
 *  @param log 日志内容
 */
typedef BOOL(^TKSendLogToServerBlock)(NSArray * logs);

/**
 *  <AUTHOR> 2016-06-25 22:06:46
 *
 *  服务器日志记录
 */
@interface TKServerLogger : TKAbstractDatabaseLogger

/**
 *  <AUTHOR> 2016-06-25 22:06:28
 *
 *  代理对象
 */
@property(nonatomic,copy)TKSendLogToServerBlock sendLogToServerBlock;

@end
