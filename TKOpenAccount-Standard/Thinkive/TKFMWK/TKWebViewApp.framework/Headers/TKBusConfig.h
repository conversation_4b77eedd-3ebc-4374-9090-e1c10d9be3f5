//
//  TKBusConfig.h
//  TKAppBase_V1
//
//  Created by l<PERSON><PERSON> on 15-3-2.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  <AUTHOR> 2016-07-01 07:07:10
 *
 *  通知更新地址
 */
#define NOTE_BUSCONFIG_UPDATEURL @"note_busConfig_updateUrl"

/**
 *  <AUTHOR> 2015-03-02 14:03:25
 *
 *  服务器网关配置文件
 */
@interface TKBusConfig : NSObject

/**
 *  <AUTHOR> 2015-03-02 14:03:34
 *
 *  单例
 *
 *  @return 
 */
+(TKBusConfig *)shareInstance;

/**
 *  <AUTHOR> 2016-07-01 07:07:55
 *
 *  更新地址
 */
@property(nonatomic,copy,readonly)NSString *updateUrl;

/**
 *  <AUTHOR> 2016-07-01 07:07:55
 *
 *  测速时间
 */
@property(nonatomic,copy,readonly)NSString *speedTime;

/**
 *  <AUTHOR> 2015-03-02 15:03:33
 *
 *  获取配置项
 *
 *  @param serverName 服务器名称
 *  @param key        配置项名称
 *
 *  @return 
 */
-(NSString *)getStringWithSeverName:(NSString *)serverName withKey:(NSString *)key;

/**
 *  <AUTHOR> 2015-03-02 16:03:03
 *
 *  获取配置项的值
 *
 *  @param serverName   服务器名称
 *  @param key          配置项名称
 *  @param defaultValue 默认值
 *
 *  @return
 */
-(NSString *)getStringWithSeverName:(NSString *)serverName withKey:(NSString *)key withDefaultValue:(NSString *)defaultValue;

/**
 *  <AUTHOR> 2015-03-02 15:03:33
 *
 *  获取配置项
 *
 *  @param serverName 服务器名称
 *  @param key        配置项名称
 *
 *  @return
 */
-(int)getIntWithServerName:(NSString *)serverName withKey:(NSString *)key;

/**
 *  <AUTHOR> 2015-03-02 16:03:03
 *
 *  获取配置项的值
 *
 *  @param serverName   服务器名称
 *  @param key          配置项名称
 *  @param defaultValue 默认值
 *
 *  @return
 */
-(int)getIntWithServerName:(NSString *)serverName withKey:(NSString *)key withDefaultValue:(int)defaultValue;

/**
 *  <AUTHOR> 2015-03-02 16:03:10
 *
 *  得到所有服务器配置
 *
 *  @return
 */
-(NSMutableDictionary *)getServerConfig;

@end
