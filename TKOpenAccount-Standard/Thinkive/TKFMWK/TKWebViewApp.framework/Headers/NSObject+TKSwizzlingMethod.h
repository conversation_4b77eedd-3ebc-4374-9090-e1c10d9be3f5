//
//  NSObject+TKSwizzlingMethod.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/12/7.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <objc/runtime.h>

/**
 *  <AUTHOR> 2016-07-24 18:07:53
 *
 *  方法绑定交换
 *
 *  @param c
 *  @param origSEL
 *  @param newSEL
 */
void TKSwizzlingMethod(Class c, SEL origSEL, SEL newSEL);

@interface NSObject (TKSwizzlingMethod)

/**
 * 方法交换，和原方法名一样只是加tk_swizzled_前缀
 * 例如 test方法交换的方法名称为tk_swizzled_test
 */
+(void)TKSwizzlingMethod:(SEL)originSEL;

@end
