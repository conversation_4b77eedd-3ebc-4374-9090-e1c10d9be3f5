//
// Copyright 2011-2014 NimbusKit
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#import <Foundation/Foundation.h>

@class TKNICSSRuleset;
@class TKNIDOM;

/**
 * The protocol used by the TKNIStylesheet to apply TKNICSSRulesets to views.
 *
 * @ingroup NimbusCSS
 *
 * If you implement this protocol in a category it is recommended that you implement the
 * logic as a separate method and call that method from tkapplyStyleWithRuleSet: so as to allow
 * subclasses to call super implementations. See UILabel+TKNIStyleable.h/m for an example.
 */
@protocol TKNIStyleable <NSObject>
@required

/**
 * Please implement tkapplyStyleWithRuleSet:inDOM: instead to support relative positioning. The deprecated
 * warning will only catch calls to super rather than implementors, but not sure what else to do.
 */
- (void)tkapplyStyleWithRuleSet:(TKNICSSRuleset *)ruleSet DEPRECATED_ATTRIBUTE;

/**
 * The given ruleset should be applied to the view. The ruleset represents a composite of all
 * rulesets in the applicable stylesheet.
 */
- (void)tkapplyStyleWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

@optional
/**
 * Tells the CSS engine a set of pseudo classes that apply to views of this class.
 * In the case of UIButton, for example, this includes :selected, :highlighted, and :disabled.
 * In CSS, you specify these with selectors like UIButton:active. If you implement this you need to respond
 * to tkapplyStyleWithRuleSet:forPseudoClass:
 *
 * Make sure to include the leading colon.
 */
- (NSArray*) tkpseudoClasses;

/**
 * Applies the given rule set to this view but for a pseudo class. Thus it only supports the subset of
 * properties that can be set on states of the view. (e.g. UIButton textColor or background)
 */
- (void)tkapplyStyleWithRuleSet:(TKNICSSRuleset *)ruleSet forPseudoClass: (NSString*) pseudo inDOM: (TKNIDOM*) dom;

/**
 * Return a string describing what would be done with the view. The current implementations return actual
 * Objective-C using the view name as the message target. The intent is to allow developers to debug
 * the logic, but also to be able to strip out the CSS infrastructure if desired and replace it with manual code.
 */
- (NSString*) tkdescriptionWithRuleSet: (TKNICSSRuleset*) ruleSet forPseudoClass: (NSString*) pseudo inDOM: (TKNIDOM*) dom withViewName: (NSString*) name;

@end
