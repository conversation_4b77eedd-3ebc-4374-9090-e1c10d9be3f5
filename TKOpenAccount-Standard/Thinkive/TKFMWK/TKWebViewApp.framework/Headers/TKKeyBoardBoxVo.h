//
//  TKKeyBoardBoxVo.h
//  TKApp
//
//  Created by 刘宝 on 2019/9/29.
//  Copyright © 2019年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef enum {
    //水平布局
    TKKeyBoardBoxVoType_HBOX,
    //垂直布局
    TKKeyBoardBoxVoType_VBOX
}TKKeyBoardBoxVoType;

/**
 * 容器模型
 */
@interface TKKeyBoardBoxVo : NSObject

/**
 * 容器类型
 */
@property(nonatomic,assign)TKKeyBoardBoxVoType type;

/**
 *  容器坐标X
 */
@property(nonatomic,assign)CGFloat left;

/**
 *  容器坐标Y
 */
@property(nonatomic,assign)CGFloat top;

/**
 *  容器宽度
 */
@property(nonatomic,assign)CGFloat width;

/**
 *  容器高度
 */
@property(nonatomic,assign)CGFloat height;

/**
 * 元素布局偏移间距，距离左边，右边，上边，下边之类,水平模式下是左边的偏移量，垂直模式下是上面的偏移量
 */
@property(nonatomic,assign)CGFloat span;

/**
 * 容器间距
 */
@property(nonatomic,assign)CGFloat space;

/**
 * 容器背景色
 */
@property(nonatomic,retain)UIColor *bgColor;

/**
 * 容器边框粗细，用于title和content
 */
@property(nonatomic,assign)NSInteger borderWidth;

/**
 * 容器边框颜色，用于title和content
 */
@property(nonatomic,retain)UIColor *borderColor;

/**
 * 容器按键通用背景色
 */
@property(nonatomic,retain)UIColor *itemBgColor;

/**
 * 容器按键通用文字颜色
 */
@property(nonatomic,retain)UIColor *itemFontColor;

/**
 * 容器按键通用文字大小
 */
@property(nonatomic,assign)CGFloat itemFontSize;

/**
 * 容器按键通用文字粗细
 */
@property(nonatomic,assign)NSInteger itemFontWeight;

/**
 * 容器按键通用选中文字大小
 */
@property(nonatomic,assign)CGFloat itemSelectedFontSize;

/**
 * 容器按键通用选中文字粗细
 */
@property(nonatomic,assign)NSInteger itemSelectedFontWeight;

/**
 * 容器按键通用高亮背景色
 */
@property(nonatomic,retain)UIColor *itemHighlightBgColor;

/**
 * 容器按键通用选中背景色
 */
@property(nonatomic,retain)UIColor *itemSelectedBgColor;

/**
 * 容器按键通用高亮文字颜色
 */
@property(nonatomic,retain)UIColor *itemHighlightFontColor;

/**
 * 容器按键通用选中文字颜色
 */
@property(nonatomic,retain)UIColor *itemSelectedFontColor;

/**
 * 容器按键通用文字位置
 */
@property(nonatomic,copy)NSString *itemFontPosition;

/**
 * 容器按键通用按钮弧度
 */
@property(nonatomic,assign)CGFloat itemRadian;

/**
 * 容器按键通用按钮底部阴影弧度
 */
@property(nonatomic,assign)CGFloat itemShadow;

/**
 * 容器按键通用宽度
 */
@property(nonatomic,assign)CGFloat itemWidth;

/**
 * 容器按键通用高度
 */
@property(nonatomic,assign)CGFloat itemHeight;

/**
 * 容器是否需要绘制
 */
@property(nonatomic,assign)BOOL isNeedDraw;

/**
 * 容器子元素
 */
@property(nonatomic,retain)NSMutableArray *children;

/**
 * 备用字段
 */
@property(nonatomic,retain)NSDictionary *userInfo;

@end
