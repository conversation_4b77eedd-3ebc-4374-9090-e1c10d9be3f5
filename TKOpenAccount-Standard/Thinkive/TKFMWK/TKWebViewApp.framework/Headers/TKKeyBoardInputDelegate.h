//
//  TKKeyBoardInputDelegate.h
//  TKComponent_V1
//
//  Created by liu<PERSON> on 15-4-7.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  <AUTHOR> 2015-04-07 12:04:49
 *
 *  自定义键盘输入框代理
 */
@protocol TKKeyBoardInputDelegate <NSObject>

@optional

/**
 * <AUTHOR> 15-03-17 16:03:08
 *
 * 自定义键盘输入框，点击确定
 */
-(void)textFieldConfirm:(UITextField *)textField;

/**
 * <AUTHOR> 15-03-17 16:03:08
 *
 * 自定义键盘输入框，文字改变
 */
-(void)textFieldChange:(UITextField *)textField;

/**
 *  <AUTHOR> 2015-07-22 21:07:45
 *
 *  输入特殊字符
 *
 *  @param textField
 *  @param charStr   
 */
-(void)textField:(UITextField *)textField doOtherChar:(NSString *)charStr;

/**
 *  <AUTHOR> 2015-07-22 21:07:45
 *
 *  键盘头部输入框联动事件
 *
 *  @param textField
 *  @param content
 */
-(void)textField:(UITextField *)textField doTitleInput:(NSString *)content;

/**
 *  <AUTHOR> 2015-07-22 21:07:45
 *
 *  键盘类型切换事件
 *
 *  @param textField
 *  @param keyBoardType
 */
-(void)textField:(UITextField *)textField keyBoardChanged:(TKKeyBoardType)keyBoardType;

@end
