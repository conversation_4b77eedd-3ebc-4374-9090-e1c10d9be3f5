//
//  TKCaptureCameraController.h
//  TKApp
//
//  Created by 刘宝 on 2017/5/3.
//  Copyright © 2017年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import "TKBaseViewController.h"

@protocol TKCaptureCameraControllerDelegate<NSObject>

@required
-(void)didTakePhoto:(UIImage *)image;

@end

@interface TKCaptureCameraController : TKBaseViewController

@property (nonatomic,assign) BOOL isUseFrontCamara;

/**
 闪光灯的模式
 */
@property(nonatomic,assign) AVCaptureFlashMode cameraFlashMode;

@property (nonatomic,weak)  id<TKCaptureCameraControllerDelegate> takePhotoDelegate;

@end
