//
//  TKShaHelper.h
//  TKUtil
//
//  Created by l<PERSON><PERSON> on 14-11-6.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  SHA帮组类
 */
@interface TKShaHelper : NSObject

/**
 *  SHA224摘要算法
 *
 *  @param content 字符串或者二进制
 *
 *  @return 编码后的字符串
 */
+(NSString *) sha224:(NSObject *)content;

/**
 *  SHA256摘要算法
 *
 *  @param content 字符串或者二进制
 *
 *  @return 编码后的字符串
 */
+(NSString *) sha256:(NSObject *)content;

/**
 *  SHA384摘要算法
 *
 *  @param content 字符串或者二进制
 *
 *  @return 编码后的字符串
 */
+(NSString *) sha384:(NSObject *)content;

/**
 *  SHA512摘要算法
 *
 *  @param content 字符串或者二进制
 *
 *  @return 编码后的字符串
 */
+(NSString *) sha512:(NSObject *)content;

/**
 *  SHA1摘要算法,增加秘钥签名
 *
 *  @param content 字符串或者二进制
 *
 *  @return 编码后的字符串
 */
+(NSString *) hmacSHA1WithSecret:(NSString *)secret content:(NSObject *)content;

/**
 *  SHA256摘要算法,增加秘钥签名
 *
 *  @param content 字符串或者二进制
 *
 *  @return 编码后的字符串
 */
+(NSString *) hmacSHA256WithSecret:(NSString *)secret content:(NSObject *)content;

@end
