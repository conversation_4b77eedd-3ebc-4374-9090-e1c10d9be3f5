//
//  TKAppEngine.h
//  TKApp
//
//  Created by liubao on 14-11-28.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKMesageDelegate.h"
#import "TKAppInvokeDelegate.h"
#import "TKRootViewController.h"
#import "TKExternalJSBridge.h"
#import "TKPluginInvokeCenter.h"
#import "TKModuleEngine.h"

/**
 *  <AUTHOR> 2015-06-19 09:06:39
 *
 *  网络改变通知
 *
 */
#define NOTE_NETWORK_CHANGE @"note_network_change"

/**
 *  <AUTHOR> 2015-06-19 09:06:39
 *
 *  录屏截屏开启改变通知
 *
 */
#define NOTE_SCREENSHOTRECORD_CHANGE @"note_screenshotrecord_change"

/**
 *  <AUTHOR> 2014-12-01 11:12:51
 *
 *  消息中心
 */
@interface TKAppEngine : NSObject<TKMesageDelegate,TKPluginInvokeCenterDelegate,TKModuleMessageEngineDelegate,TKAppInvokeDelegate>

/**
 *  <AUTHOR> 2014-12-01 14:12:09
 *
 *  单例
 *
 *  @return 
 */
+(TKAppEngine *)shareInstance;

/**
 *  <AUTHOR> 2014-12-01 18:12:49
 *
 *  根控制器
 */
@property (nonatomic,readonly,strong)TKRootViewController *rootViewCtr;

/**
 *  <AUTHOR> 2014-12-12 22:12:27
 *
 *  js连接桥
 */
@property (nonatomic,readonly,strong)TKExternalJSBridge *jsBridge;

/**
 *  <AUTHOR> 2014-12-12 23:12:17
 *
 *  插件中心
 */
@property (nonatomic,readonly,strong)TKPluginInvokeCenter *pluginCenter;

/**
 *  <AUTHOR> 2014-12-12 23:12:17
 *
 *  模块中心引擎
 */
@property (nonatomic,readonly,strong)TKModuleEngine *moduleEngine;

/**
 *  <AUTHOR> 2014-12-15 15:12:14
 *
 *  网络监测
 */
@property (nonatomic,readonly,strong)TKReachability *reachability;

/**
 *  <AUTHOR> 2017-01-10 13:01:35
 *
 *  是否使用H5的拦截机制，解决防篡改和在线升级问题
 */
@property (nonatomic,assign) BOOL isUseH5URLProtocolFilter;

/**
 *  <AUTHOR> 2017-01-10 13:01:35
 *
 *  是否开启后台模糊效果
 */
@property (nonatomic,assign) BOOL isBlurBackground;

/**
 *  <AUTHOR> 2017-01-10 13:01:35
 *
 *  是否同意隐私协议
 */
@property (nonatomic,assign) BOOL isUserAgreeRight;

/**
 *  <AUTHOR> 2014-12-12 22:12:08
 *
 *  启动引擎
 */
-(void)start;

/**
 *  <AUTHOR> 2014-12-12 22:12:16
 *
 *  关闭引擎
 */
-(void)stop;

/**
 *  <AUTHOR> 2014-12-12 22:12:36
 *
 *  是否在运行
 *
 *  @return 运行状态
 */
-(BOOL)isRuning;

/**
 *  <AUTHOR> 2017-05-17 16:05:54
 *
 *  是否APP第一次安装
 *
 *  @return 
 */
-(BOOL)isAppFirstInstall;

/**
*  <AUTHOR> 2017-05-17 16:05:54
*
*  App是否进入了后台模式
*
*  @return
*/
-(BOOL)isApplicationDidEnterBackground;

/**
 * 是否正在录屏或者截屏
 */
-(BOOL)isScreenShotRecording;

/**
 *  <AUTHOR> 2017-05-17 16:05:54
 *
 *  检测证书的合法性
 *  @param cert         证书名称
 *  @param channel      渠道名称
 *  @param isEncryptCer 是否加密证书，一般是搞的明文证书
 *
 *  @return
 */
-(BOOL)checkCert:(NSString *)cert withChannel:(NSString *)channel isEncryptCer:(BOOL)isEncryptCer;

/**
 *  <AUTHOR> 2017-05-17 16:05:54
 *
 *  检测授权的合法性
 *  @param license      授权文件
 *  @param channel      渠道名称
 *
 *  @return
 */
-(BOOL)checkLicense:(NSString *)license withChannel:(NSString *)channel;

/**
 *  <AUTHOR> 2017-05-17 16:05:54
 *
 *  获取对应授权项的值
 *  @return
 */
-(NSString *)getLiceseValue:(NSString *)key;

@end
