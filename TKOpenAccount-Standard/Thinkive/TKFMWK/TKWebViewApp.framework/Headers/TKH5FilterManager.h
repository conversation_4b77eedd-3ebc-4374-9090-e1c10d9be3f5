//
//  TKH5FilterManager.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2017/8/1.
//  Copyright © 2017年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 H5拦截管理器
 */
@interface TKH5FilterManager : NSObject

/**
 *  <AUTHOR> 2015-02-03 18:02:30
 *
 *  单例模式
 *
 *  @return
 */
+(TKH5FilterManager *)shareInstance;

/**
 H5根目录
 */
@property(nonatomic,readonly,copy) NSString *platRoot;

/**
 是否默认加载安装包中的H5文件
 */
@property(nonatomic,readonly,assign) BOOL isLoadPackageH5;

/**
 是否启动实时在线更新策略
 */
@property(nonatomic,readonly,assign) BOOL isUseOnLineUpdate;

/**
 是否采用防篡改机制
 */
@property(nonatomic,readonly,assign) BOOL isUseNoTamper;

/**
 H5的MD5文件的加密Key
 */
@property(nonatomic,readonly,copy) NSString *encryptKey;

/**
 是否启动所有URL请求资源的缓存机制
 */
@property(nonatomic,readonly,assign) BOOL isUseURLCache;

/**
 根据对应H5模块的服务器地址

 @param moduleName H5模块名称
 @return
 */
-(NSString *)getServerUrl:(NSString *)moduleName;

/**
  获取配置的模块名称
 */
-(NSArray *)getH5Modules;

@end
