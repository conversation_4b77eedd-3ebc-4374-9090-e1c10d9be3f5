#ifndef __TKBLOWFISH_H__
#define __TKBLOWFISH_H__
#define TK_BLOWFISH_ECB 0 /*default*/
#define TK_BLOWFISH_CBC 1
#define TK_BLOWFISH_CFB 2
#define TK_BLOWFISH_MAX_KEY_SIZE 56
#define TK_BLOWFISH_MAX_PBLOCK_SIZE 18
#define TK_BLOWFISH_MAX_SBLOCK_XSIZE 4
#define TK_BLOWFISH_MAX_SBLOCK_YSIZE 256
/*Block Structure*/
typedef struct{
	unsigned int m_uil; /*Hi*/
	unsigned int m_uir; /*Lo*/
}TKBlowFishSBlock;
typedef struct{
	TKBlowFishSBlock m_oChain;
	unsigned int m_auiP[TK_BLOWFISH_MAX_PBLOCK_SIZE];
	unsigned int m_auiS[TK_BLOWFISH_MAX_SBLOCK_XSIZE][TK_BLOWFISH_MAX_SBLOCK_YSIZE];
}TK<PERSON>low<PERSON>;
/****************************************************************************************/
/*Constructor - Initialize the P and S boxes for a given Key*/
int TKBlowFishInit(TKBlowfish *blowfish, unsigned char* ucKey, size_t keysize);
/*Encrypt/Decrypt from Input Buffer to Output Buffer*/
int TKBlowFishEncrypt(TKBlowfish *blowfish, const unsigned char* in, size_t siz_i, unsigned char* out, size_t siz_o, int iMode);
int TKBlowFishDecrypt(TKBlowfish *blowfish, const unsigned char* in, size_t siz_i, unsigned char* out, size_t siz_o, int iMode);
/****************************************************************************************/
void TKBlowFishHexStr2CharStr(unsigned char *pszHexStr, int iSize, unsigned char *pucCharStr);
void TKBlowFishCharStr2HexStr(unsigned char *pucCharStr, int iSize,unsigned char *pszHexStr);
#endif /*__BLOWFISH_H__*/
