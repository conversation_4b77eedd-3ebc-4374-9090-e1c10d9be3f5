//
//  TKCommonQueue.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/7/16.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *
 * 通用队里处理
 */
@interface TKCommonQueue : NSObject

@property (nonatomic,readonly)dispatch_queue_t realQueue;

/**
 *  初始化队里
 *
 *  @param queueName 队里名称
 */
-(instancetype)initWithQueueName:(NSString *)queueName;

/**
 *  初始化队里
 *
 *  @param queueName 队里名称
 *  @param isSerial  是否串行
 */
-(instancetype)initWithQueueName:(NSString *)queueName isSerial:(BOOL)isSerial;

/**
 *  初始化队里
 *
 *  @param realQueue 队里名称
 */
-(instancetype)initWithQueue:(dispatch_queue_t)realQueue;

/**
 *  同步执行
 *
 */
- (void)syncExecute:(dispatch_block_t)action;

/**
 *  异步执行
 *
 */
- (void)asyncExecute:(dispatch_block_t)action;

/**
 * 释放请求队列
 */
- (void)releaseQueue;

/**
 *  <AUTHOR> 2016-08-15 09:08:12
 *
 *  是否在自己的队列中
 */
@property (nonatomic, readonly, getter=isOnInternalSelfQueue) BOOL onInternalSelfQueue;

@end
