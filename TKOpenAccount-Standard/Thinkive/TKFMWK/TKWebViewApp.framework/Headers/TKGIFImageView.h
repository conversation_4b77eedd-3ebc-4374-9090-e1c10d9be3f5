//
//  SCGIFImageView.h
//  TestGIF
//
//  Created by shichang<PERSON> on 11-7-12.
//  Copyright 2011 __MyCompanyName__. All rights reserved.
//

#import <UIKit/UIKit.h>

/**
 *  <AUTHOR> 2015-06-04 20:06:35
 *
 *  GIF视图界面
 */
@interface TKGIFImageView : UIImageView

@property (nonatomic, copy) NSString *runLoopMode;

/**
 *  <AUTHOR> 2015-06-04 21:06:47
 *
 *  设置图片数据
 *
 *  @param imageData
 */
- (void)setImageData:(NSData*)imageData;

/**
 *  <AUTHOR> 2015-06-04 21:06:47
 *
 *  设置图片数据
 *
 *  @param imageData
 *  @param repeatCount
 */
- (void)setImageData:(NSData*)imageData withRepeatCount:(NSInteger)repeatCount;

/**
 *  <AUTHOR> 2017-05-11 19:05:46
 *
 *  根据gif的名称设置
 *
 *  @param gifName
 */
- (void)setImageByName:(NSString *)gifName;

/**
 *  <AUTHOR> 2017-05-11 19:05:46
 *
 *  根据gif的名称设置
 *
 *  @param gifName
 */
- (void)setImageByName:(NSString *)gifName withRepeatCount:(NSInteger)repeatCount;

@end
