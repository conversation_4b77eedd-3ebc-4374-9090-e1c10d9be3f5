//
//  TKNetAddress.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-2-28.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  <AUTHOR> 2015-02-28 16:02:51
 *
 *  网络地址
 */
@interface TKNetAddress : NSObject

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 *  是否参与站点分配
 */
@property(nonatomic,assign)BOOL isJoinConnectSite;

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 *  是否国密站点
 */
@property(nonatomic,assign)BOOL isGMSite;

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 *  分组ID
 */
@property(nonatomic,copy)NSString *groupId;

/**
 *  <AUTHOR> 2015-02-28 16:02:37
 *
 *  网关名称ID
 */
@property(nonatomic,copy)NSString *gateWayName;

/**
 是否主域名
 */
@property(nonatomic,assign)BOOL isDomain;

/**
 是否IPV6
 */
@property(nonatomic,assign)BOOL isIPV6;

/**
 *  <AUTHOR> 2015-02-28 16:02:26
 *
 *  ip地址
 */
@property(nonatomic,copy)NSString *ip;

/**
 *  <AUTHOR> 2015-02-28 16:02:47
 *
 *  端口
 */
@property(nonatomic,assign)int port;

/**
 地址描述
 */
@property(nonatomic,copy)NSString *desc;

/**
 *  <AUTHOR> 2015-02-28 16:02:09
 *
 *  是否存活
 */
@property(nonatomic,assign)BOOL isAlive;

/**
 *  <AUTHOR> 2016-01-12 01:01:24
 *
 *  是否是最优测速模式
 */
@property(nonatomic,assign)BOOL isNeedTestSpeed;

/**
 *  <AUTHOR> 2016-01-07 10:01:37
 *
 *  测速的综合得分,分值越低表示越快
 */
@property(nonatomic,assign)double speed;

/**
 *  <AUTHOR> 2016-01-12 01:01:58
 *
 *  开始测试时间
 */
@property(nonatomic,assign)NSTimeInterval btime;

/**
 *  <AUTHOR> 2016-01-12 01:01:05
 *
 *  测试花费时间
 */
@property(nonatomic,assign)NSTimeInterval time;

/**
 *  <AUTHOR> 2016-01-12 01:01:53
 *
 *  服务器负载得分
 */
@property(nonatomic,assign)double serverScore;

@end
