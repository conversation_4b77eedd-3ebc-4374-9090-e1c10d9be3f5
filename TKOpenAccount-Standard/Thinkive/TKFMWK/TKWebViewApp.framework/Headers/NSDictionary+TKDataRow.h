//
//  NSDictionary+TKDataRow.h
//  TKUtil_V1
//
//  Created by 刘宝 on 16/3/24.
//  Copyright © 2016年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  DataRow
 */
@interface NSDictionary (TKDataRow)

/**
 *  获取字符串
 *
 *  @param key
 *
 *  @return
 */
-(NSString *)getStringWithKey:(NSString *)key;

/**
 *  获取字符串
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(NSString *)getStringWithKey:(NSString *)key defaultValue:(NSString *)defaultValue;

/**
 *  获取int
 *
 *  @param key
 *
 *  @return
 */
-(int)getIntWithKey:(NSString *)key;

/**
 *  获取int
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(int)getIntWithKey:(NSString *)key defaultValue:(int)defaultValue;

/**
 *  获取Long
 *
 *  @param key
 *
 *  @return
 */
-(long)getLongWithKey:(NSString *)key;

/**
 *  获取Long
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(long)getLongWithKey:(NSString *)key defaultValue:(long)defaultValue;

/**
 *  获取Long Long
 *
 *  @param key
 *
 *  @return
 */
-(long long)getLongLongWithKey:(NSString *)key;

/**
 *  获取Long Long
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(long long)getLongLongWithKey:(NSString *)key defaultValue:(long long)defaultValue;

/**
 *  获取float
 *
 *  @param key
 *
 *  @return
 */
-(float)getFloatWithKey:(NSString *)key;

/**
 *  获取float
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(float)getFloatWithKey:(NSString *)key defaultValue:(float)defaultValue;

/**
 *  获取double
 *
 *  @param key
 *
 *  @return
 */
-(double)getDoubleWithKey:(NSString *)key;

/**
 *  获取double
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(double)getDoubleWithKey:(NSString *)key defaultValue:(double)defaultValue;

/**
 *  获取数字
 *
 *  @param key
 *
 *  @return
 */
-(NSInteger)getIntegerWithKey:(NSString *)key;

/**
 *  获取数字
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(NSInteger)getIntegerWithKey:(NSString *)key defaultValue:(NSInteger)defaultValue;

/**
 *  获取Number
 *
 *  @param key
 *
 *  @return
 */
-(NSNumber *)getNumberWithKey:(NSString *)key;

/**
 *  获取Number
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(NSNumber *)getNumberWithKey:(NSString *)key defaultValue:(NSNumber *)defaultValue;

/**
 *  获取Bool
 *
 *  @param key
 *
 *  @return
 */
-(BOOL)getBoolWithKey:(NSString *)key;

/**
 *  获取Bool
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(BOOL)getBoolWithKey:(NSString *)key defaultValue:(BOOL)defaultValue;

/**
 *  获取对象
 *
 *  @param key
 *
 *  @return
 */
-(NSObject *)getObjectWithKey:(NSString *)key;

/**
 *  获取对象
 *
 *  @param key
 *  @param defaultValue
 *
 *  @return
 */
-(NSObject *)getObjectWithKey:(NSString *)key defaultValue:(NSObject *)defaultValue;

/**
 *  获取JSON对象
 *
 *  @param key
 *
 *  @return
 */
-(NSMutableDictionary *)getJsonObjectWithKey:(NSString *)key;

/**
 *  获取JSON数组对象
 *
 *  @param key
 *
 *  @return
 */
-(NSMutableArray *)getJsonArrayWithKey:(NSString *)key;

@end
