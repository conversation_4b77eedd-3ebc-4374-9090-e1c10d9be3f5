//
//  TKCommonService.h
//  TKApp
//
//  Created by liu<PERSON> on 14-11-26.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseService.h"
#import "TKIOSCallJSFilterDelegate.h"

/**
 *  <AUTHOR> 2014-11-26 18:11:45
 *
 *  通用基础Service
 */
@interface TKCommonService :TKBaseService

/**
  产生请求流水号
 */
+(int)generateRequestFlowNo;

/**
 设置调用js的全局代理拦截器
*/
+(void)setIOSCallJSGlobalFilter:(id<TKIOSCallJSFilterDelegate>)globalFilter;

/**
 *  <AUTHOR> 2014-11-26 16:11:56
 *
 *  构建初始化请求对象
 */
-(ReqParamVo *)createReqParamVo;

/**
 *  <AUTHOR> 2014-11-26 16:11:56
 *
 *  构建初始化请求对象
 *  @param url
    http/https格式：http://地址:端口/servlet/json?key=value&key=value
    socket格式：    socket://busconfig.xml中的serverID?companyId=THINKIVE&systemId=MALL&key=value
    server.xml格式：server://server.xml中的serverID?key=value&key=value
 *  @param reqMode
         请求加密模式(0:正常，1:Http加签，2：Http加密，3：Http加密加签，4:Socket加密，5：Socket压缩，6：Socket压缩加密，7：Http加密加签并加密响应包，8：微服务正常，9：微服务加签，10：微服务加密加签，11：微服务加密加签并加密响应包)
 */
-(ReqParamVo *)createReqParamVo:(NSString *)url reqMode:(NSString *)reqMode;

/**
 *  <AUTHOR> 2014-11-25 23:11:12
 *
 *  请求服务
 *
 *  @param reqParamVo   请求对象
 *  @param callBackFunc 回调函数
 *  @param isReturnList 是否返回List
 *  @param isRunInMainThread 回调函数是否在主线程上
 */
-(void)invoke:(ReqParamVo *)reqParamVo callBackFunc:(CallBackFunc)callBackFunc isReturnList:(BOOL)isReturnList isRunInMainThread:(BOOL)isRunInMainThread;

/**
 *  <AUTHOR> 2014-11-25 23:11:12
 *
 *  请求服务
 *
 *  @param reqParamVo   请求对象
 *  @param callBackFunc 回调函数
 *  @param isRunInMainThread 回调函数是否在主线程上
 */
-(void)invoke:(ReqParamVo *)reqParamVo callBackFunc:(CallBackFunc)callBackFunc isRunInMainThread:(BOOL)isRunInMainThread;

/**
 *  <AUTHOR> 2015-04-21 00:04:56
 *
 *  IOS调用JS
 *
 *  @param webViewName webView
 *  @param param       参数
 */
-(void)iosCallJs:(NSString *)webViewName param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-11-19 13:11:48
 *
 *  IOS调用JS，群发调用
 *
 *  @param param 入参
 */
-(void)iosCallJsWithParam:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-11-19 13:11:48
 *
 *  IOS调用JS，群发调用，排除某个模块WebView当前对象
 *
 *  @param param        入参
 *  @param webViewNames 排除WebView模块名称
 */
-(void)iosCallJsWithParam:(NSMutableDictionary *)param excludeNames:(NSArray *)webViewNames;

/**
 *  <AUTHOR> 2015-04-22 10:04:09
 *
 *  IOS调用JS
 *
 *  @param webViewName webView 名称
 *  @param function    函数名称
 *  @param param       Json格式的JS入参
 */
-(void)iosCallJs:(NSString *)webViewName function:(NSString *)function param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-04-22 10:04:09
 *
 *  IOS调用JS，群发调用
 *
 *  @param function    函数名称
 *  @param param       Json格式的JS入参
 */
-(void)iosCallJsWithFunction:(NSString *)function param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-04-22 10:04:09
 *
 *  IOS调用JS，群发调用
 *
 *  @param function     函数名称
 *  @param param        Json格式的JS入参
 *  @param webViewNames 排除WebView模块名称
 */
-(void)iosCallJsWithFunction:(NSString *)function param:(NSMutableDictionary *)param excludeNames:(NSArray *)webViewNames;

/**
 *  <AUTHOR> 2015-04-22 10:04:53
 *
 *  IOS调用JS
 *
 *  @param webViewName webView 名称
 *  @param function    函数名称
 *  @param params      多个JS入参用,分割的，这里用数组表示
 */
-(void)iosCallJs:(NSString *)webViewName function:(NSString *)function params:(NSArray *)params;

/**
 *  <AUTHOR> 2015-04-22 10:04:53
 *
 *  IOS调用JS，群发调用
 *
 *  @param function    函数名称
 *  @param params      多个JS入参用,分割的，这里用数组表示
 */
-(void)iosCallJsWithFunction:(NSString *)function params:(NSArray *)params;

/**
 *  <AUTHOR> 2015-04-22 10:04:53
 *
 *  IOS调用JS，群发调用
 *
 *  @param function     函数名称
 *  @param params       多个JS入参用,分割的，这里用数组表示
 *  @param webViewNames 排除WebView模块名称
 */
-(void)iosCallJsWithFunction:(NSString *)function params:(NSArray *)params excludeNames:(NSArray *)webViewNames;

/**
 *  <AUTHOR> 2016-06-28 16:06:05
 *
 *  发送请求日志
 *
 *  @param url          地址
 *  @param logs         日志
 *  @param callBackFunc 回调
 */
-(void)send:(NSString *)url logs:(NSArray *)logs callBackFunc:(CallBackFunc)callBackFunc;

/**
 *  <AUTHOR> 2016-02-25 14:02:58
 *
 *  实现网络请求代理
 *
 *  @param moduleName   模块名称
 *  @param protocol     网络协议（0：HTTP/HTTPS 1:行情长连接 2:交易长连接 3:资讯长连接 4:天风新版统一接入长连接 5：思迪新版统一接入长连接）
 *  @param url         网络地址 (URL地址或站点名称)
 *  @param paramMap     网络参数
 *  @param headerMap    请求头参数
 *  @param isPost       是否post
 *  @param timeOut      超时时间(单位秒)
 *  @param mode        请求加密模式(0:正常，1:Http加签，2：Http加密，3：Http加密加签，4:Socket加密，5：Socket压缩，6：Socket压缩加密，7：Http加密加签并加密响应包，
 *                              8：微服务正常，9：微服务加签，10：微服务加密加签，11：微服务加密加签并加密响应包)
 *  @param isFilterRepeatRequest  是否开启重复请求拦截机制，默认不开启
 *  @param filterRepeatRequestTimeOut 重复请求，进行请求拦截时间，单位毫秒
 *  @param isEncodeURL            是否编码入参，默认是编码
 *  @param isAutoAddSysComParam   是否自动添加系统公共入参，默认是添加
 *  @param isGlobRequest          是否全局请求，默认是NO
 *  @param netLinkMode           网络链路(0:普通链路，1：国密链路)，默认是0
 *  @param callBackFunc 回调函数
 */
-(NSString *)doProxyNetWorkService:(NSString *)moduleName protocol:(NSString *)protocol url:(NSString *)url paramMap:(NSMutableDictionary *)paramMap headerMap:(NSMutableDictionary *)headerMap isPost:(BOOL)isPost timeOut:(int)timeOut mode:(NSString *)mode isFilterRepeatRequest:(BOOL)isFilterRepeatRequest filterRepeatRequestTimeOut:(int)filterRepeatRequestTimeOut isEncodeURL:(BOOL)isEncodeURL isAutoAddSysComParam:(BOOL)isAutoAddSysComParam isGlobRequest:(BOOL)isGlobRequest netLinkMode:(NSString *)netLinkMode callBackFunc:(CallBackFunc)callBackFunc;

@end
