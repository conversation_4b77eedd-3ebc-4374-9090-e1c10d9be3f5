//
//  TKJSBasePlugin.h
//  TKApp
//
//  Created by l<PERSON><PERSON> on 14-11-27.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKPluginInvokeDelegate.h"
#import "JSCallBack.h"
#import "UIViewController+TKBaseViewController.h"
#import "TKCommonService.h"
#import "TKPluginInvokeCenterDelegate.h"

/**
 *  <AUTHOR> 2014-11-27 16:11:08
 *
 *  js插件基础类
 */
@interface TKBasePlugin : NSObject<TKPluginInvokeDelegate>
{
    @protected
    
    //服务类
    TKCommonService *_commonService;
    
    //是否全局缓存插件，默认缓存
    BOOL _isCache;
    
    //是否同步插件
    BOOL _isSyncPlugin;
}

/**
 *  <AUTHOR> 2017-06-03 13:06:19
 *
 *  是否需要缓存插件
 */
@property(nonatomic,assign,readonly)BOOL isCache;

/**
 *  <AUTHOR> 2017-06-03 13:06:19
 *
 *  是否同步插件，默认是NO
 */
@property(nonatomic,assign)BOOL isSyncPlugin;

/**
 *  <AUTHOR> 2014-12-10 00:12:35
 *
 *  当前操作模块名称
 */
@property (nonatomic,copy)NSString *moduleName;

/**
 *  <AUTHOR> 2015-06-12 10:06:19
 *
 *  是否H5调用
 */
@property (nonatomic,assign)BOOL isH5;

/**
 *  <AUTHOR> 2016-06-09 13:06:23
 *
 *  是否是JS回调模式
 */
@property (nonatomic,assign)BOOL isUseJsCallBack;

/**
 *  <AUTHOR> 2016-06-09 13:06:49
 *
 *  请求流水号
 */
@property (nonatomic,copy)NSString *flowNo;

/**
 *  <AUTHOR> 2015-06-12 12:06:50
 *
 *  回调函数
 */
@property (nonatomic,copy)TKPluginCallBackFunc callBackFunc;

/**
 *  <AUTHOR> 2014-12-10 00:12:25
 *
 *  当前模块名称对应的控制器
 */
@property (nonatomic,readonly)UIViewController *currentViewCtrl;

/**
 *  <AUTHOR> 2014-12-01 17:12:16
 *
 *  根控制器
 */
@property(nonatomic,readonly) UIViewController *rootViewCtrl;

/**
 *  <AUTHOR> 2014-12-24 00:12:54
 *
 *  根窗口
 */
@property(nonatomic,readonly) UIWindow *rootWindow;

/**
 *  <AUTHOR> 2014-12-24 00:12:54
 *
 *  当前的jsCallBack对象
 */
@property(nonatomic,readonly) JSCallBack *currentJSCallBack;

/**
 *  <AUTHOR> 2014-12-24 00:12:17
 *
 *  根代理
 */
@property(nonatomic,readonly)id<UIApplicationDelegate> rootDelegate;

/**
 *  <AUTHOR> 2017-03-09 21:03:15
 *
 *  请求过来的webview的UUID编号
 */
@property(nonatomic,copy)NSString *webviewUUID;

/**
 * 前拦截
 */
-(void)beforeServerInvoke:(id)param;

/**
* 后拦截
*/
-(void)afterServerInvoke:(ResultVo *)resultVo;

/**
 *  <AUTHOR> 2014-12-04 00:12:10
 *
 *  执行插件请求
 *
 *  @param param 参数
 *
 *  @return 结果
 */
-(ResultVo *)serverInvoke:(id)param;

/**
 *  <AUTHOR> 2015-04-21 00:04:56
 *
 *  IOS调用JS
 *
 *  @param param       参数
 */
-(void)iosCallJsWithParam:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-04-22 10:04:09
 *
 *  IOS调用JS
 *
 *  @param function    函数名称
 *  @param param       Json格式的JS入参
 */
-(void)iosCallJsWithFunction:(NSString *)function param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2015-04-22 10:04:53
 *
 *  IOS调用JS
 *
 *  @param function    函数名称
 *  @param params      多个JS入参用,分割的，这里用数组表示
 */
-(void)iosCallJsWithFunction:(NSString *)function params:(NSArray *)params;

/**
 *  <AUTHOR> 2015-06-12 12:06:12
 *
 *  原生执行插件的回调函数
 *
 *  @param param 参数
 */
-(void)iosCallPluginCallBack:(NSMutableDictionary *)param;

@end
