//
//  TKRootViewController.h
//  TKApp
//
//  Created by liubao on 14-12-1.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "TKAppInvokeDelegate.h"

/**
 *  <AUTHOR> 2017-01-13 10:01:46
 *
 *  控制器存储对象
 */
@interface TKViewControllerVo : NSObject

@property (nonatomic,weak)UIViewController *viewController;

@property (nonatomic,copy)NSString *name;

@end

/**
 *  <AUTHOR> 2014-12-01 17:12:25
 *
 *  应用根控制器
 */
@interface TKRootViewController : NSObject<TKAppInvokeDelegate>

/**
 *  <AUTHOR> 2014-12-01 17:12:16
 *
 *  根控制器
 */
@property(nonatomic,readonly) UIViewController *rootViewCtrl;

/**
 *  <AUTHOR> 2014-12-24 00:12:54
 *
 *  根窗口
 */
@property(nonatomic,readonly) UIWindow *rootWindow;

/**
 *  <AUTHOR> 2014-12-24 00:12:17
 *
 *  根代理
 */
@property(nonatomic,readonly)id<UIApplicationDelegate> rootDelegate;

/**
 *  <AUTHOR> 2015-08-05 03:08:28
 *
 *  当前控制器名称
 */
@property(nonatomic,copy)NSString *currentViewCtrlName;

/**
 *  <AUTHOR> 2015-08-05 03:08:32
 *
 *  当前注册的控制器
 */
@property(nonatomic,weak)UIViewController *currentViewCtrl;

/**
 *  <AUTHOR> 2015-08-05 03:08:32
 *
 *  当前注册的导航控制器
 */
@property(nonatomic,readonly)UINavigationController *currentNavigationController;

/**
 *  <AUTHOR> 2015-08-05 03:08:32
 *
 *  当前注册的Tab控制器
 */
@property(nonatomic,readonly)UITabBarController *currentTabBarController;

/**
 *  <AUTHOR> 2014-12-01 17:12:47
 *
 *  单例
 *
 *  @return 
 */
+(TKRootViewController *)shareInstance;

/**
 *  <AUTHOR> 2014-12-01 17:12:22
 *
 *  注册viewCtrl
 *
 *  @param viewCtrl 控制器
 */
-(void)registerViewCtrl:(UIViewController *)viewCtrl;

/**
 *  <AUTHOR> 2017-05-08 20:05:01
 *
 *  注册控制器
 *
 *  @param viewCtrl 控制器
 *  @param name     模块名称
 */
-(void)registerViewCtrl:(UIViewController *)viewCtrl withName:(NSString *)name;

/**
 *  <AUTHOR> 2014-12-01 17:12:45
 *
 *  卸载控制器
 *
 *  @param name 名称
 */
-(void)unRegisterViewCtrl:(NSString *)name;

/**
 *  <AUTHOR> 2014-12-04 12:12:59
 *
 *  卸载控制器
 *
 *  @param name 名称
 *  @param uuid 唯一值
 */
-(void)unRegisterViewCtrl:(NSString *)name withUUID:(NSString *)uuid;

/**
 *  <AUTHOR> 2014-12-01 17:12:52
 *
 *  获取控制器
 *
 *  @param name 名称
 *
 *  @return 控制器
 */
-(UIViewController *)getViewCtrlByName:(NSString *)name;

/**
 *  <AUTHOR> 2014-12-01 17:12:52
 *
 *  获取控制器
 *
 *  @param name 名称
 *
 *  @return 控制器
 */
-(UIViewController *)getViewCtrlByName:(NSString *)name className:(NSString *)className;

/**
 *  <AUTHOR> 2017-01-13 10:01:00
 *
 *  获取所有注册的控制器
 *
 *  @return
 */
-(NSDictionary *)getAllViewCtrlMap;

@end
