/**
 * <AUTHOR> 14-11-25 10:11:51
 * h5调用的股票键盘
 */

#import <UIKit/UIKit.h>
#import "TKKeyBoardEventDelegate.h"
#import "TKKeyBoard.h"

/**
 *  <AUTHOR> 2015-03-31 13:03:12
 *
 *  针对H5对原生键盘改造
 */
@interface TKH5KeyBoard : NSObject<TKKeyBoardEventDelegate>

/**
 *  <AUTHOR> 2015-03-31 16:03:57
 *
 *  键盘代理
 */
@property(nonatomic,weak) id<TKKeyBoardEventDelegate> delegate;

/**
 *  <AUTHOR> 2015-03-31 16:03:06
 *
 *  键盘类型
 */
@property(nonatomic,copy) NSString *keyBoardType;

/**
 *  <AUTHOR> 2015-04-01 09:04:26
 *
 *  确定按钮配置
 */
@property(nonatomic,strong) TKKeyBoardConfirmConfig *confirmConfig;

/**
 *  <AUTHOR> 2016-09-05 17:09:14
 *
 *  获取键盘的UI对象
 */
@property(nonatomic,readonly)TKKeyBoard *keyBoard;

/**
 *  <AUTHOR> 2016-09-05 17:09:54
 *
 *  键盘是否显示
 */
@property (nonatomic,readonly)BOOL isShow;

/**
 *  根据类型初始化键盘
 */
-(instancetype)initWithKeyBoardType:(NSString *)keyBoardType;

/**
 * 修改元素的值
 */
-(void)setKeyBoardItemText:(NSString *)text forTag:(NSString *)tag;

/**
 * <AUTHOR> 14-11-25 11:11:11
 * 显示键盘
 */
-(void)showKeyBoard;

/**
 * <AUTHOR> 15-03-18 10:03:00
 *
 * 重新设置键盘
 */
- (void)resetKeyBoard;

@end
