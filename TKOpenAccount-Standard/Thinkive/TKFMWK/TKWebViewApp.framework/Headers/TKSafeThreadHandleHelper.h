//
//  TKSafeThreadHandleHelper.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2020/7/8.
//  Copyright © 2020 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 * 线程切换安全执行帮助类
 */
@interface TKSafeThreadHandleHelper : NSObject

/**
 * 同步主线程安全切换
 */
+(void)safe_dispatch_sync_main_queue:(dispatch_block_t)block;

/**
 * 异步主线程安全切换
 */
+(void)safe_dispatch_async_main_queue:(dispatch_block_t)block;

/**
 * 异步主线程安全切换，如果当前已经是主线程了就同步执行，否则异步执行
 */
+(void)safe_dispatch_noforce_async_main_queue:(dispatch_block_t)block;

/**
 * 主线程延迟执行安全切换,延迟单位秒
 */
+(void)safe_dispatch_after_main_queue:(dispatch_block_t)block delayTime:(CGFloat)delayInSeconds;

/**
 * 异步子线程安全切换
 */
+(void)safe_dispatch_async_global_queue:(dispatch_block_t)block;

/**
 * 子线程延迟执行安全切换
 */
+(void)safe_dispatch_after_global_queue:(dispatch_block_t)block delayTime:(CGFloat)delayInSeconds;

/**
 * 同步自定义线程安全切换
 */
+(void)safe_dispatch_sync_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;

/**
 * 异步自定义线程安全切换
 */
+(void)safe_dispatch_async_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;

/**
 * 自定义线程延迟执行安全切换
 */
+(void)safe_dispatch_after_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block delayTime:(CGFloat)delayInSeconds;

/**
 * 异步自定义线程组安全切换
 */
+(void)safe_dispatch_group_async:(dispatch_group_t)group queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;

/**
 * 同步自定义线程栅栏函数安全切换
 */
+(void)safe_dispatch_barrier_sync_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;

/**
 * 异步自定义线程栅栏函数安全切换
 */
+(void)safe_dispatch_barrier_async_queue:(dispatch_queue_t)queue block:(dispatch_block_t)block;

@end
