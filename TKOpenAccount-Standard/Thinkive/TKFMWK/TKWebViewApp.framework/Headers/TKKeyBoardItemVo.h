//
//  TKKeyBoardItemVo.h
//  TKApp
//
//  Created by 刘宝 on 2019/9/25.
//  Copyright © 2019年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 * 键盘子元素
 */
@interface TKKeyBoardItemVo : NSObject

/**
 * 元素类型，默认是button
 */
@property(nonatomic,copy)NSString *type;

/**
 * 元素标示
 */
@property(nonatomic,copy)NSString *tag;

/**
 *  元素坐标X
 */
@property(nonatomic,assign)CGFloat left;

/**
 *  元素坐标Y
 */
@property(nonatomic,assign)CGFloat top;

/**
 * 元素宽度
 */
@property(nonatomic,assign)CGFloat width;

/**
 * 元素高度
 */
@property(nonatomic,assign)CGFloat height;

/**
 * 元素布局偏移间距，距离左边，右边，上边，下边之类,水平模式下是左边的偏移量，垂直模式下是上面的偏移量
 */
@property(nonatomic,assign)CGFloat span;

/**
 * 是否隐藏
 */
@property(nonatomic,assign)BOOL hidden;

/**
 * 元素背景色
 */
@property(nonatomic,retain)UIColor *bgColor;

/**
 * 元素文字颜色
 */
@property(nonatomic,retain)UIColor *fontColor;

/**
 * 元素文字颜色
 */
@property(nonatomic,copy)NSString *fontRangeColor;

/**
 * 元素文字位置
 */
@property(nonatomic,copy)NSString *fontPosition;

/**
 * 元素文字大小
 */
@property(nonatomic,assign)CGFloat fontSize;

/**
 * 元素文字粗细
 */
@property(nonatomic,assign)NSInteger fontWeight;

/**
 * 元素选中文字大小
 */
@property(nonatomic,assign)CGFloat selectedFontSize;

/**
 * 元素选中文字粗细
 */
@property(nonatomic,assign)NSInteger selectedFontWeight;

/**
 * 元素高亮背景色
 */
@property(nonatomic,retain)UIColor *highlightBgColor;

/**
 * 元素选中背景色
 */
@property(nonatomic,retain)UIColor *selectedBgColor;

/**
 * 元素高亮文字颜色
 */
@property(nonatomic,retain)UIColor *highlightFontColor;

/**
 * 元素选中文字颜色
 */
@property(nonatomic,retain)UIColor *selectedFontColor;

/**
 * 元素按钮弧度
 */
@property(nonatomic,assign)CGFloat radian;

/**
 * 元素按钮底部阴影弧度
 */
@property(nonatomic,assign)CGFloat shadow;

/**
 * 元素内容是否是Html格式
 */
@property(nonatomic,assign)BOOL isHtmlText;

/**
 * 元素内容
 */
@property(nonatomic,copy)NSString *text;

/**
 * 元素选中内容
 */
@property(nonatomic,copy)NSString *selectedText;

/**
 * 元素值
 */
@property(nonatomic,copy)NSString *value;

/**
 * 元素选中值
 */
@property(nonatomic,copy)NSString *selectedValue;

/**
 * 元素背景图片
 */
@property(nonatomic,copy)NSString *image;

/**
 * 元素高亮背景图片
 */
@property(nonatomic,copy)NSString *highlightImage;

/**
 * 元素选中背景图片
 */
@property(nonatomic,copy)NSString *selectedImage;

/**
 * 元素提示图片
 */
@property(nonatomic,copy)NSString *tipImage;

/**
 * 元素提示图片显示位置
 */
@property(nonatomic,copy)NSString *tipImagePosition;

/**
 * 元素提示图片宽度
 */
@property(nonatomic,assign)CGFloat tipImageWidth;

/**
 * 元素提示图片高度
 */
@property(nonatomic,assign)CGFloat tipImageHeight;

/**
 * 元素提示图片偏移量
 */
@property(nonatomic,assign)CGFloat tipImageSpan;

/**
 * 元素是否随机数字
 */
@property(nonatomic,assign)BOOL isRandNum;

/**
 * 元素下标线的颜色
 */
@property(nonatomic,retain)UIColor *indicatorColor;

/**
 * 元素下标线的宽度
 */
@property(nonatomic,assign)CGFloat indicatorWidth;

/**
 * 元素下标线的高度
 */
@property(nonatomic,assign)CGFloat indicatorHeight;

/**
 * 元素动作
 */
@property(nonatomic,copy)NSString *action;

/**
 * 是否获取光标后自动触发事件
 */
@property(nonatomic,assign)BOOL isFirstResponderAction;

/**
 * 元素切换目标键盘类型
 */
@property(nonatomic,copy)NSString *targetKeyBoard;

/**
 * 元素切换默认目标键盘类型
 */
@property(nonatomic,copy)NSString *targetDefaultKeyBoard;

/**
 * 备用字段
 */
@property(nonatomic,retain)NSDictionary *userInfo;

@end
