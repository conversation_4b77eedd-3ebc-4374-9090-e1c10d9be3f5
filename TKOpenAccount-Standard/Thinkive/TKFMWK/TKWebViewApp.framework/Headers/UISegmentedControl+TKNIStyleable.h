//
//  UISegmentedControl+TKNIStyleable.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-5-5.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <UIKit/UIKit.h>

@class TKNICSSRuleset;
@class TKNIDOM;

@interface UISegmentedControl (TKNIStyleable)

/**
 * Applies the given rule set to this navigation bar. Use tkapplyNavigationBarStyleWithRuleSet:inDOM: instead
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplySegmentedControlStyleWithRuleSet:(TKNICSSRuleset *)ruleSet DEPRECATED_ATTRIBUTE;

/**
 * Applies the given rule set to this navigation bar.
 *
 * This method is exposed primarily for subclasses to use when implementing the
 * tkapplyStyleWithRuleSet: method from TKNIStyleable.
 */
- (void)tkapplySegmentedControlStyleWithRuleSet:(TKNICSSRuleset *)ruleSet inDOM: (TKNIDOM*) dom;

@end
