//
//  TKDataVo.h
//  TKASClient
//
//  Created by <PERSON><PERSON><PERSON> on 15-6-30.
//  Copyright (c) 2015年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef enum{
    
    /**
     *  <AUTHOR> 2016-01-13 12:01:49
     *
     *  连接
     */
    TKDataVoMode_Connect,
    
    /**
     *  <AUTHOR> 2016-01-13 12:01:14
     *
     *  业务
     */
    TKDataVoMode_Business
    
}TKDataVoMode;

typedef enum{
    /**
     *  <AUTHOR> 2015-09-15 09:09:57
     *
     *  正常数据
     */
    TKDataVoType_Normal = 0,
    /**
     *  <AUTHOR> 2015-09-15 09:09:12
     *
     *  AES加密
     */
    TKDataVoType_AesEncryt = 1,
    /**
     *  <AUTHOR> 2016-11-17 20:11:47
     *
     *  压缩
     */
    TKDataVoType_Compress = 2,
    /**
     *  <AUTHOR> 2016-11-17 20:11:27
     *
     *  先加密后压缩
     */
    TKDataVoType_AesEncryt_Compress = 3,
    /**
     *  <AUTHOR> 2016-11-17 20:11:00
     *
     *  先压缩后加密
     */
    TKDataVoType_Compress_AesEncryt = 4,
    /**
     *  <AUTHOR> 2015-09-15 09:09:12
     *
     *  SM4加密
     */
    TKDataVoType_SM4Encryt = 5,
    /**
     *  <AUTHOR> 2016-11-17 20:11:27
     *
     *  先SM4加密后压缩
     */
    TKDataVoType_SM4Encryt_Compress = 6,
    /**
     *  <AUTHOR> 2016-11-17 20:11:00
     *
     *  先压缩后SM4加密
     */
    TKDataVoType_Compress_SM4Encryt = 7
}TKDataVoType;

/**
 *  <AUTHOR> 2015-06-30 13:06:33
 *
 *  数据对象
 */
@interface TKDataVo : NSObject

/**
 *  <AUTHOR> 2015-06-30 13:06:58
 *
 *  流水号
 */
@property(nonatomic,copy)NSString *flowNo;

/**
 *  <AUTHOR> 2016-11-17 20:11:10
 *
 *  功能号
 */
@property(nonatomic,assign)UInt32 funcNo;

/**
 *  <AUTHOR> 2015-06-30 14:06:17
 *
 *  数据
 */
@property(nonatomic,retain)NSData *data;

/**
 渠道ID
 */
@property(nonatomic,copy)NSString *channelId;

/**
 *  <AUTHOR> 2016-11-17 20:11:51
 *
 *  请求公司编号
 */
@property(nonatomic,copy)NSString *companyId;

/**
 *  <AUTHOR> 2016-11-17 20:11:43
 *
 *  系统编号
 */
@property(nonatomic,copy)NSString *systemId;

/**
 *  <AUTHOR> 2016-11-17 20:11:18
 *
 *  数据类型协议
 */
@property(nonatomic,assign)TKDataVoType dataType;

/**
 *  <AUTHOR> 2016-01-13 12:01:59
 *
 *  数据类型
 */
@property(nonatomic,assign)TKDataVoMode dataMode;

@end
