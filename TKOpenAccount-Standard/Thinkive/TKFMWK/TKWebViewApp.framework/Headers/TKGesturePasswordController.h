//
//  GesturePasswordController.h
//  GesturePassword
//
//  Created by hb on 14-8-23.
//  Copyright (c) 2014年 黑と白の印记. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKGesturePasswordButtonDelegate.h"
#import "TKGesturePasswordTouchDelegate.h"
#import "TKTentacleView.h"
#import "TKBaseViewController.h"

/**
 *  <AUTHOR> 2015-04-14 11:04:45
 *
 *  手势密码控制器
 */
@interface TKGesturePasswordController : TKBaseViewController

/**
 *  <AUTHOR> 2014-12-22 10:12:03
 *
 *  账号
 */
@property (nonatomic,copy)NSString *account;

/**
 *  <AUTHOR> 2015-04-21 11:04:56
 *
 *  用户图片
 */
@property (nonatomic,retain)UIImage *userImage;

/**
 *  <AUTHOR> 2014-12-22 11:12:17
 *
 *  密码
 */
@property (nonatomic,copy,readonly)NSString * password;

/**
 *  <AUTHOR> 2014-12-22 15:12:05
 *
 *  错误次数
 */
@property (nonatomic,assign)int maxErrorNum;

/**
 *  <AUTHOR> 2015-04-21 16:04:03
 *
 *  锁屏的秒数
 */
@property (nonatomic,assign)int lockSenconds;

/**
 *  <AUTHOR> 2014-12-25 14:12:43
 *
 *  密码最小长度
 */
@property (nonatomic,assign)int minPasswordLength;

/**
 *  <AUTHOR> 2016-03-10 13:03:19
 *
 *  是否有返回按钮
 */
@property (nonatomic,assign)BOOL isCanBack;

/**
 *  <AUTHOR> 2016-03-16 14:03:39
 *
 *  返回键的位置
 */
@property (nonatomic,copy)NSString *position;

/**
 *  <AUTHOR> 2016-03-17 00:03:13
 *
 *  连接线的样式，0：不带箭头，1：带箭头
 */
@property (nonatomic,copy)NSString *lineStyle;

/**
 *  <AUTHOR> 2017-04-01 12:04:55
 *
 *  是否验证通过后自动关闭手势密码
 */
@property (nonatomic,assign)BOOL isCloseAfterVitify;

/**
 *  <AUTHOR> 2015-04-21 11:04:48
 *
 *  按钮代理
 */
@property (nonatomic,weak)id<TKGesturePasswordButtonDelegate> buttonDelegate;

/**
 *  <AUTHOR> 2015-10-30 00:10:56
 *
 *  绘图完成代理函数
 */
@property (nonatomic,weak)id<TKGesturePasswordTouchDelegate> touchDelegate;

/**
 *  <AUTHOR> 2015-04-14 14:04:24
 *
 *  加载手势密码界面
 *
 *  @param type 手势类型
 *  @param innerCircle  是否初始显示中心圆
 */
- (void)loadPasswordView:(TKGesturePasswordType)type innerCircle:(BOOL)innerCircle;

/**
 *  <AUTHOR> 2014-12-22 10:12:22
 *
 *  清除数据
 */
- (void)clearPassword;

/**
 *  <AUTHOR> 2014-12-22 14:12:48
 *
 *  显示
 */
- (void)showPassword;

/**
 *  <AUTHOR> 2014-12-22 14:12:59
 *
 *  隐藏
 */
- (void)hidePassword;

@end
