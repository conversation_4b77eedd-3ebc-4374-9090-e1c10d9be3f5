//
//  TKModuleEngine.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/12/6.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "TKModuleDelegate.h"

/**
 * 模块消息引擎
 * 错误号定义：
 *  -1000:消息号不能为空!
 *  -1001:目标模块名称不能为空!
 *  -1002:消息被拦截!
 *  -1003:来源模块实例对象不能为空!
 *  -1004:来源模块未在导航控制器中!
 *  -1005:目标模块实例对象不能为空!
 *  -1006:目标模块对应的类不存在!
 *  -1007:目标模块对应的类未配置定义!
 */
@interface TKModuleEngine :NSObject<TKModuleMessageEngineDelegate>

/**
 *  <AUTHOR> 2014-12-01 17:12:47
 *
 *  单例
 *
 *  @return
 */
+(TKModuleEngine *)shareInstance;

@end
