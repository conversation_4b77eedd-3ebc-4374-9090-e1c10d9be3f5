//
//  TKPluginInvokeCenter.h
//  TKApp
//
//  Created by liubao on 14-12-10.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKPluginInvokeCenterDelegate.h"
#import "TKBasePlugin.h"

/**
 *  <AUTHOR> 2014-12-10 01:12:43
 *
 *  插件处理中心
 */
@interface TKPluginInvokeCenter : NSObject<TKPluginInvokeCenterDelegate>

/**
 *  <AUTHOR> 2014-11-27 15:11:44
 *
 *  单例对象
 *
 *  @return
 */
+(TKPluginInvokeCenter *)shareInstance;

/**
 *  <AUTHOR> 2016-07-08 17:07:46
 *
 *  插件配置
 */
@property(nonatomic,readonly)NSMutableDictionary *pluginMap;

/**
 *  <AUTHOR> 2016-10-18 20:10:22
 *
 *  获取缓存的插件对象
 *
 *  @param funcNo 功能号
 *
 *  @return 插件对象
 */
-(TKBasePlugin *)getCachePlugin:(NSString *)funcNo;

/**
 *  <AUTHOR> 2017-05-26 23:05:58
 *
 *  重新载入配置文件
 *
 *  @param pluginConfigPath 配置文件路径
 */
-(void)reloadPluginConfig:(NSString *)pluginConfigPath;

@end
