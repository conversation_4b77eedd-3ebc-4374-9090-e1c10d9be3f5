
/**
 * <AUTHOR> 15-03-17 10:03:24
 *
 * 自定义键盘输入框
 */
#import <UIKit/UIKit.h>
#import "TKKeyBoardEventDelegate.h"
#import "TKKeyBoardInputDelegate.h"
#import "TKKeyBoard.h"

/**
 *  <AUTHOR> 2015-03-31 21:03:51
 *
 *  原生键盘输入框
 */
@interface TKTextField : UITextField

/**
 *  <AUTHOR> 2015-03-31 21:03:44
 *
 *  输入框代理
 */
@property (nonatomic, weak) id < UITextFieldDelegate,TKKeyBoardInputDelegate> delegate;

/**
 *  <AUTHOR> 2015-03-31 21:03:21
 *
 * 是否显示切换中文标题
 */
@property(nonatomic,assign) BOOL isShowChangeZHCNTitle;

/**
 *  <AUTHOR> 2015-03-31 21:03:21
 *
 *  限制长度
 */
@property(nonatomic,assign) NSInteger limitLength;

/**
 *  <AUTHOR> 2015-03-31 16:03:06
 *
 *  键盘类型
 */
@property(nonatomic,assign) TKKeyBoardType tkKeyBoardType;

/**
 *  <AUTHOR> 2016-09-05 17:09:14
 *
 *  获取键盘的UI对象
 */
@property(nonatomic,readonly)TKKeyBoard *keyBoard;

/**
 *  <AUTHOR> 2016-05-19 12:05:34
 *
 *  是否允许copy，默认允许
 */
@property(nonatomic,assign) BOOL enableCopy;

/**
 *  <AUTHOR> 2015-04-01 09:04:13
 *
 *  确定按钮配置
 */
@property(nonatomic,strong) TKKeyBoardConfirmConfig *confirmConfig;

/**
 *  <AUTHOR> 2016-12-30 12:12:44
 *
 *  是否显示下划线
 */
@property(nonatomic,assign) BOOL isShowUnderline;

/**
 *  <AUTHOR> 2016-12-30 12:12:56
 *
 *  下划线颜色
 */
@property(nonatomic,strong) UIColor *underlineColor;

/**
 * 修改元素的值
 */
-(void)setKeyBoardItemText:(NSString *)text forTag:(NSString *)tag;

@end
