//
//  TKHttpRoomConfig.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2023/3/2.
//  Copyright © 2023 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 * 多机房配置解析
 */
@interface TKHttpRoomConfig : NSObject

/**
 *  <AUTHOR> 2015-03-02 14:03:34
 *
 *  单例
 *
 *  @return
 */
+(TKHttpRoomConfig *) shareInstance;

/**
 *  <AUTHOR> 2016-07-01 07:07:55
 *
 *  更新地址
 */
@property(nonatomic,copy,readonly)NSString *updateUrl;

/**
 *  <AUTHOR> 2016-07-01 07:07:55
 *
 *  测速地址
 */
@property(nonatomic,copy,readonly)NSString *speedUrl;

/**
 *  <AUTHOR> 2016-07-01 07:07:55
 *
 *  测速时间
 */
@property(nonatomic,copy,readonly)NSString *speedTime;

/**
 *  <AUTHOR> 2016-07-01 07:07:55
 *
 *  请求模式(0:正常，1:Http加签，2：Http加密，3：Http加密加签，4:Socket加密，5：Socket压缩，6：Socket压缩加密，7：Http加密加签并加密响应包，8：微服务正常，9：微服务加签，10：微服务加密加签，11：微服务加密加签并加密响应包)
 */
@property(nonatomic,copy,readonly)NSString *reqMode;

/**
 *  <AUTHOR> 2016-07-01 07:07:55
 *
 *  机房模式（0：随机，1：最快测速)
 */
@property(nonatomic,copy,readonly)NSString *roomMode;

/**
 *  <AUTHOR> 2015-03-02 16:03:10
 *
 *  得到所有服务器配置
 *
 *  @return
 */
-(NSMutableDictionary *)getRoomConfig;

/**
 * 清除本地缓存
 */
+ (void)clearCache;

@end
