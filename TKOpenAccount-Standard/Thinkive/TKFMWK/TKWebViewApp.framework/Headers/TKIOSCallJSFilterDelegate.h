//
//  TKIOSCallJSFilterDelegate.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2020/10/22.
//  Copyright © 2020 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  <AUTHOR> 2014-11-26 13:11:08
 *
 *  结果过滤器
 */
@protocol TKIOSCallJSFilterDelegate <NSObject>

@required

/**
 *  <AUTHOR> 2017-01-13 08:01:32
 *
 *  拦截JS业务请求
 *
 *  @param webViewName 浏览器标示
 *  @param function    JS函数
 *  @param param       JS对象
 *
 *  @return YES表示继续执行，NO就会被中断拦截
 */
-(BOOL)onFilterIOSCallJS:(NSString *)webViewName function:(NSString *)function param:(NSObject *)param;

@end
