//
//  TKDeviceHelper.h
//  TKUtil
//
//  Created by l<PERSON><PERSON> on 14-10-31.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKDevice.h"

//extern NSString *CTSettingCopyMyPhoneNumber();

#define TKIFPGA_NAMESTRING                @"iFPGA"

#define TKIPHONE_1G_NAMESTRING            @"iPhone 1G"
#define TKIPHONE_3G_NAMESTRING            @"iPhone 3G"
#define TKIPHONE_3GS_NAMESTRING           @"iPhone 3GS"
#define TKIPHONE_4_NAMESTRING             @"iPhone 4"
#define TKIPHONE_4S_NAMESTRING            @"iPhone 4S"
#define TKIPHONE_5_NAMESTRING             @"iPhone 5"
#define TKIPHONE_5C_NAMESTRING            @"iPhone 5C"
#define TKIPHONE_5S_NAMESTRING            @"iPhone 5S"
#define TKIPHONE_6_NAMESTRING             @"iPhone 6"
#define TKIPHONE_6PLUS_NAMESTRING         @"iPhone 6Plus"
#define TKIPHONE_6S_NAMESTRING            @"iPhone 6S"
#define TKIPHONE_6SPLUS_NAMESTRING        @"iPhone 6SPlus"
#define TKIPHONE_SE_NAMESTRING            @"iPhone SE"
#define TKIPHONE_7_NAMESTRING             @"iPhone 7"
#define TKIPHONE_7PLUS_NAMESTRING         @"iPhone 7Plus"
#define TKIPHONE_8_NAMESTRING             @"iPhone 8"
#define TKIPHONE_8PLUS_NAMESTRING         @"iPhone 8Plus"
#define TKIPHONE_X_NAMESTRING             @"iPhone X"
#define TKIPHONE_XR_NAMESTRING            @"iPhone XR"
#define TKIPHONE_XS_NAMESTRING            @"iPhone XS"
#define TKIPHONE_XSMax_NAMESTRING         @"iPhone XS Max"
#define TKIPHONE_11_NAMESTRING            @"iPhone 11"
#define TKIPHONE_11Pro_NAMESTRING         @"iPhone 11Pro"
#define TKIPHONE_11ProMax_NAMESTRING      @"iPhone 11Pro Max"
#define TKIPHONE_SE2_NAMESTRING           @"iPhone SE2"
#define TKIPHONE_12Mini_NAMESTRING        @"iPhone 12Mini"
#define TKIPHONE_12_NAMESTRING            @"iPhone 12"
#define TKIPHONE_12Pro_NAMESTRING         @"iPhone 12Pro"
#define TKIPHONE_12ProMax_NAMESTRING      @"iPhone 12Pro Max"
#define TKIPHONE_13Mini_NAMESTRING        @"iPhone 13Mini"
#define TKIPHONE_13_NAMESTRING            @"iPhone 13"
#define TKIPHONE_13Pro_NAMESTRING         @"iPhone 13Pro"
#define TKIPHONE_13ProMax_NAMESTRING      @"iPhone 13Pro Max"
#define TKIPHONE_SE3_NAMESTRING           @"iPhone SE3"
#define TKIPHONE_14_NAMESTRING            @"iPhone 14"
#define TKIPHONE_14PLUS_NAMESTRING        @"iPhone 14Plus"
#define TKIPHONE_14Pro_NAMESTRING         @"iPhone 14Pro"
#define TKIPHONE_14ProMax_NAMESTRING      @"iPhone 14Pro Max"
#define TKIPHONE_15_NAMESTRING            @"iPhone 15"
#define TKIPHONE_15PLUS_NAMESTRING        @"iPhone 15Plus"
#define TKIPHONE_15Pro_NAMESTRING         @"iPhone 15Pro"
#define TKIPHONE_15ProMax_NAMESTRING      @"iPhone 15Pro Max"
#define TKIPHONE_16_NAMESTRING            @"iPhone 16"
#define TKIPHONE_16PLUS_NAMESTRING        @"iPhone 16Plus"
#define TKIPHONE_16Pro_NAMESTRING         @"iPhone 16Pro"
#define TKIPHONE_16ProMax_NAMESTRING      @"iPhone 16Pro Max"
#define TKIPHONE_UNKNOWN_NAMESTRING       @"Unknown iPhone"

#define TKIPOD_1G_NAMESTRING              @"iPod touch 1G"
#define TKIPOD_2G_NAMESTRING              @"iPod touch 2G"
#define TKIPOD_3G_NAMESTRING              @"iPod touch 3G"
#define TKIPOD_4G_NAMESTRING              @"iPod touch 4G"
#define TKIPOD_5G_NAMESTRING              @"iPod touch 5G"
#define TKIPOD_6G_NAMESTRING              @"iPod touch 6G"
#define TKIPOD_7G_NAMESTRING              @"iPod touch 7G"
#define TKIPOD_8G_NAMESTRING              @"iPod touch 8G"
#define TKIPOD_9G_NAMESTRING              @"iPod touch 9G"
#define TKIPOD_UNKNOWN_NAMESTRING         @"Unknown iPod"

#define TKIPAD_1G_NAMESTRING              @"iPad"
#define TKIPAD_2G_NAMESTRING              @"iPad 2"
#define TKIPAD_3G_NAMESTRING              @"iPad 3"
#define TKIPAD_Min1_NAMESTRING            @"iPad Min"
#define TKIPAD_4G_NAMESTRING              @"iPad 4"
#define TKIPAD_Air1_NAMESTRING            @"iPad Air"
#define TKIPAD_Min2_NAMESTRING            @"iPad Min2"
#define TKIPAD_Min3_NAMESTRING            @"iPad Min3"
#define TKIPAD_Min4_NAMESTRING            @"iPad Min4"
#define TKIPAD_Air2_NAMESTRING            @"iPad Air2"
#define TKIPAD_Pro97_NAMESTRING           @"iPad Pro9.7"
#define TKIPAD_Pro129_NAMESTRING          @"iPad Pro12.9"
#define TKIPAD_5G_NAMESTRING              @"iPad 5"
#define TKIPAD_Pro129_2nd_NAMESTRING      @"iPad Pro12.9 2nd"
#define TKIPAD_Pro105_2nd_NAMESTRING      @"iPad Pro10.5 2nd"
#define TKIPAD_6G_NAMESTRING              @"iPad 6"
#define TKIPAD_7G_NAMESTRING              @"iPad 7"
#define TKIPAD_Pro11_3rd_NAMESTRING       @"iPad Pro11 3rd"
#define TKIPAD_Pro129_3rd_NAMESTRING      @"iPad Pro12.9 3rd"
#define TKIPAD_Pro11_4th_NAMESTRING       @"iPad Pro11 4th"
#define TKIPAD_Pro129_4th_NAMESTRING      @"iPad Pro12.9 4th"
#define TKIPAD_Min5_NAMESTRING            @"iPad Min5"
#define TKIPAD_Air3_NAMESTRING            @"iPad Air3"
#define TKIPAD_8G_NAMESTRING              @"iPad 8"
#define TKIPAD_9G_NAMESTRING              @"iPad 9"
#define TKIPAD_Min6_NAMESTRING            @"iPad Min6"
#define TKIPAD_Air4_NAMESTRING            @"iPad Air4"
#define TKIPAD_Pro11_5th_NAMESTRING       @"iPad Pro11 5th"
#define TKIPAD_Pro129_5th_NAMESTRING      @"iPad Pro12.9 5th"
#define TKIPAD_Air5_NAMESTRING            @"iPad Air5"
#define TKIPAD_10G_NAMESTRING             @"iPad 10"
#define TKIPAD_Pro129_6th_NAMESTRING      @"iPad Pro12.9 6th"
#define TKIPAD_Air6_NAMESTRING            @"iPad Air6"
#define TKIPAD_Air7_NAMESTRING            @"iPad Air7"
#define TKIPAD_Pro129_7th_NAMESTRING      @"iPad Pro12.9 7th"
#define TKIPAD_UNKNOWN_NAMESTRING         @"Unknown iPad"

#define TKAPPLETV_2G_NAMESTRING           @"Apple TV 2G"
#define TKAPPLETV_3G_NAMESTRING           @"Apple TV 3G"
#define TKAPPLETV_4G_NAMESTRING           @"Apple TV 4G"
#define TKAPPLETV_UNKNOWN_NAMESTRING      @"Unknown Apple TV"

#define TKIOS_FAMILY_UNKNOWN_DEVICE       @"Unknown iOS device"

#define TKSIMULATOR_NAMESTRING            @"iPhone Simulator"
#define TKSIMULATOR_IPHONE_NAMESTRING     @"iPhone Simulator"
#define TKSIMULATOR_IPAD_NAMESTRING       @"iPad Simulator"
#define TKSIMULATOR_APPLETV_NAMESTRING    @"Apple TV Simulator"

//iPhone 3G 以后各代的CPU型号和频率
#define TKIPHONE_3G_CPUTYPE               @"ARM11"
#define TKIPHONE_3G_CPUFREQUENCY          @"416MHz"
#define TKIPHONE_3GS_CPUTYPE              @"ARM Cortex A8"
#define TKIPHONE_3GS_CPUFREQUENCY         @"660MHz"
#define TKIPHONE_4_CPUTYPE                @"Apple A4"
#define TKIPHONE_4_CPUFREQUENCY           @"1GHz"
#define TKIPHONE_4S_CPUTYPE               @"Apple A5 Double Core"
#define TKIPHONE_4S_CPUFREQUENCY          @"800MHz"
#define TKIPHONE_5_CPUTYPE                @"Apple A6 Double Core"
#define TKIPHONE_5_CPUFREQUENCY           @"1GHz"
#define TKIPHONE_5C_CPUTYPE               @"Apple A6 Double Core"
#define TKIPHONE_5C_CPUFREQUENCY          @"1GHz"
#define TKIPHONE_5S_CPUTYPE               @"Apple A7/M7 Double Core"
#define TKIPHONE_5S_CPUFREQUENCY          @"1.3GHz"
#define TKIPHONE_6_CPUTYPE                @"Apple A8/M8 Double Core"
#define TKIPHONE_6_CPUFREQUENCY           @"1.4GHz"
#define TKIPHONE_6Plus_CPUTYPE            @"Apple A8/M8 Double Core"
#define TKIPHONE_6Plus_CPUFREQUENCY       @"1.4GHz"
#define TKIPHONE_6S_CPUTYPE               @"Apple A9/M9 Double Core"
#define TKIPHONE_6S_CPUFREQUENCY          @"1.85GHz"
#define TKIPHONE_6SPlus_CPUTYPE           @"Apple A9/M9 Double Core"
#define TKIPHONE_6SPlus_CPUFREQUENCY      @"1.85GHz"
#define TKIPHONE_SE_CPUTYPE               @"Apple A9/M9 Double Core"
#define TKIPHONE_SE_CPUFREQUENCY          @"1.85GHz"
#define TKIPHONE_7_CPUTYPE                @"Apple A10/M10 Four Core"
#define TKIPHONE_7_CPUFREQUENCY           @"2.39GHz"
#define TKIPHONE_7Plus_CPUTYPE            @"Apple A10/M10 Four Core"
#define TKIPHONE_7Plus_CPUFREQUENCY       @"2.39GHz"
#define TKIPHONE_8_CPUTYPE                @"Apple A11/M11 Six Core"
#define TKIPHONE_8_CPUFREQUENCY           @"2.39GHz"
#define TKIPHONE_8Plus_CPUTYPE            @"Apple A11/M11 Six Core"
#define TKIPHONE_8Plus_CPUFREQUENCY       @"2.39GHz"
#define TKIPHONE_X_CPUTYPE                @"Apple A11/M11 Six Core"
#define TKIPHONE_X_CPUFREQUENCY           @"2.39GHz"
#define TKIPHONE_XR_CPUTYPE               @"Apple A12/M12 Six Core"
#define TKIPHONE_XR_CPUFREQUENCY          @"2.49GHz"
#define TKIPHONE_XS_CPUTYPE               @"Apple A12/M12 Six Core"
#define TKIPHONE_XS_CPUFREQUENCY          @"2.49GHz"
#define TKIPHONE_XSMax_CPUTYPE            @"Apple A12/M12 Six Core"
#define TKIPHONE_XSMax_CPUFREQUENCY       @"2.49GHz"
#define TKIPHONE_11_CPUTYPE               @"Apple A13/M13 Six Core"
#define TKIPHONE_11_CPUFREQUENCY          @"2.66GHz"
#define TKIPHONE_11Pro_CPUTYPE            @"Apple A13/M13 Six Core"
#define TKIPHONE_11Pro_CPUFREQUENCY       @"2.66GHz"
#define TKIPHONE_11ProMax_CPUTYPE         @"Apple A13/M13 Six Core"
#define TKIPHONE_11ProMax_CPUFREQUENCY    @"2.66GHz"
#define TKIPHONE_SE2_CPUTYPE              @"Apple A13/M13 Six Core"
#define TKIPHONE_SE2_CPUFREQUENCY         @"2.66GHz"
#define TKIPHONE_12Mini_CPUTYPE           @"Apple A14/M14 Six Core"
#define TKIPHONE_12Mini_CPUFREQUENCY      @"3.1GHz"
#define TKIPHONE_12_CPUTYPE               @"Apple A14/M14 Six Core"
#define TKIPHONE_12_CPUFREQUENCY          @"3.1GHz"
#define TKIPHONE_12Pro_CPUTYPE            @"Apple A14/M14 Six Core"
#define TKIPHONE_12Pro_CPUFREQUENCY       @"3.1GHz"
#define TKIPHONE_12ProMax_CPUTYPE         @"Apple A14/M14 Six Core"
#define TKIPHONE_12ProMax_CPUFREQUENCY    @"3.1GHz"
#define TKIPHONE_13Mini_CPUTYPE           @"Apple A15/M15 Six Core"
#define TKIPHONE_13Mini_CPUFREQUENCY      @"3.23GHz"
#define TKIPHONE_13_CPUTYPE               @"Apple A15/M15 Six Core"
#define TKIPHONE_13_CPUFREQUENCY          @"3.23GHz"
#define TKIPHONE_13Pro_CPUTYPE            @"Apple A15/M15 Six Core"
#define TKIPHONE_13Pro_CPUFREQUENCY       @"3.23GHz"
#define TKIPHONE_13ProMax_CPUTYPE         @"Apple A15/M15 Six Core"
#define TKIPHONE_13ProMax_CPUFREQUENCY    @"3.23GHz"
#define TKIPHONE_14_CPUTYPE               @"Apple A15/M15 Six Core"
#define TKIPHONE_14_CPUFREQUENCY          @"3.23GHz"
#define TKIPHONE_14Plus_CPUTYPE           @"Apple A15/M15 Six Core"
#define TKIPHONE_14Plus_CPUFREQUENCY      @"3.23GHz"
#define TKIPHONE_SE3_CPUTYPE              @"Apple A15/M15 Six Core"
#define TKIPHONE_SE3_CPUFREQUENCY         @"3.23GHz"
#define TKIPHONE_14Pro_CPUTYPE            @"Apple A16/M16 Six Core"
#define TKIPHONE_14Pro_CPUFREQUENCY       @"3.46GHz"
#define TKIPHONE_14ProMax_CPUTYPE         @"Apple A16/M16 Six Core"
#define TKIPHONE_14ProMax_CPUFREQUENCY    @"3.46GHz"
#define TKIPHONE_15_CPUTYPE               @"Apple A16/M16 Six Core"
#define TKIPHONE_15_CPUFREQUENCY          @"3.46GHz"
#define TKIPHONE_15Plus_CPUTYPE           @"Apple A16/M16 Six Core"
#define TKIPHONE_15Plus_CPUFREQUENCY      @"3.46GHz"
#define TKIPHONE_15Pro_CPUTYPE            @"Apple A17/M17 Six Core"
#define TKIPHONE_15Pro_CPUFREQUENCY       @"3.77GHz"
#define TKIPHONE_15ProMax_CPUTYPE         @"Apple A17/M17 Six Core"
#define TKIPHONE_15ProMax_CPUFREQUENCY    @"3.77GHz"

#define TKIPHONE_16_CPUTYPE               @"Apple A18/M18 Six Core"
#define TKIPHONE_16_CPUFREQUENCY          @"4.04GHz"
#define TKIPHONE_16Plus_CPUTYPE           @"Apple A18/M18 Six Core"
#define TKIPHONE_16Plus_CPUFREQUENCY      @"4.04GHz"
#define TKIPHONE_16Pro_CPUTYPE            @"Apple A18Pro Six Core"
#define TKIPHONE_16Pro_CPUFREQUENCY       @"4.04GHz"
#define TKIPHONE_16ProMax_CPUTYPE         @"Apple A18Pro Six Core"
#define TKIPHONE_16ProMax_CPUFREQUENCY    @"4.04GHz"

//iPod touch 4G 的CPU型号和频率
#define TKIPOD_4G_CPUTYPE                 @"Apple A4"
#define TKIPOD_4G_CPUFREQUENCY            @"800MHz"

#define TKIOS_CPUTYPE_UNKNOWN             @"Unknown CPU type"
#define TKIOS_CPUFREQUENCY_UNKNOWN        @"Unknown CPU frequency"

typedef enum {
    TKUIDeviceUnknown,
    
    TKUIDeviceSimulator,
    TKUIDeviceSimulatoriPhone,
    TKUIDeviceSimulatoriPad,
    TKUIDeviceSimulatorAppleTV,
    
    TKUIDevice1GiPhone,
    TKUIDevice3GiPhone,
    TKUIDevice3GSiPhone,
    TKUIDevice4iPhone,
    TKUIDevice4SiPhone,
    TKUIDevice5iPhone,
    TKUIDevice5CPhone,
    TKUIDevice5SPhone,
    TKUIDevice6iPhone,
    TKUIDevice6PlusPhone,
    TKUIDevice6SPhone,
    TKUIDevice6SPlusPhone,
    TKUIDeviceSEPhone,
    TKUIDevice7iPhone,
    TKUIDevice7PlusPhone,
    TKUIDevice8iPhone,
    TKUIDevice8PlusPhone,
    TKUIDeviceXiPhone,
    TKUIDeviceXRPhone,
    TKUIDeviceXSPhone,
    TKUIDeviceXSMaxPhone,
    TKUIDevice11iPhone,
    TKUIDevice11ProPhone,
    TKUIDevice11ProMaxPhone,
    TKUIDeviceSE2Phone,
    TKUIDevice12MiniPhone,
    TKUIDevice12iPhone,
    TKUIDevice12ProPhone,
    TKUIDevice12ProMaxPhone,
    TKUIDevice13MiniPhone,
    TKUIDevice13iPhone,
    TKUIDevice13ProPhone,
    TKUIDevice13ProMaxPhone,
    TKUIDeviceSE3Phone,
    TKUIDevice14iPhone,
    TKUIDevice14PlusPhone,
    TKUIDevice14ProPhone,
    TKUIDevice14ProMaxPhone,
    TKUIDevice15iPhone,
    TKUIDevice15PlusPhone,
    TKUIDevice15ProPhone,
    TKUIDevice15ProMaxPhone,
    TKUIDevice16iPhone,
    TKUIDevice16PlusPhone,
    TKUIDevice16ProPhone,
    TKUIDevice16ProMaxPhone,

    TKUIDevice1GiPod,
    TKUIDevice2GiPod,
    TKUIDevice3GiPod,
    TKUIDevice4GiPod,
    TKUIDevice5GiPod,
    TKUIDevice6GiPod,
    TKUIDevice7GiPod,
    TKUIDevice8GiPod,
    TKUIDevice9GiPod,
    
    TKUIDevice1GiPad,
    TKUIDevice2GiPad,
    TKUIDevice3GiPad,
    TKUIDeviceMini1GiPad,
    TKUIDevice4GiPad,
    TKUIDeviceAir1GiPad,
    TKUIDeviceMini2GiPad,
    TKUIDeviceMini3GiPad,
    TKUIDeviceMini4GiPad,
    TKUIDeviceAir2GiPad,
    TKUIDevicePro9_7iPad,
    TKUIDevicePro12_9iPad,
    TKUIDevice5GiPad,
    TKUIDevicePro12_9_2ndiPad,
    TKUIDevicePro10_5_2ndiPad,
    TKUIDevice6GiPad,
    TKUIDevice7GiPad,
    TKUIDevicePro11_3rdiPad,
    TKUIDevicePro12_9_3rdiPad,
    TKUIDevicePro11_4thiPad,
    TKUIDevicePro12_9_4thiPad,
    TKUIDeviceMini5GiPad,
    TKUIDeviceAir3GiPad,
    TKUIDevice8GiPad,
    TKUIDevice9GiPad,
    TKUIDeviceMini6GiPad,
    TKUIDeviceAir4GiPad,
    TKUIDevicePro11_5thiPad,
    TKUIDevicePro12_9_5thiPad,
    TKUIDeviceAir5GiPad,
    TKUIDevice10GiPad,
    TKUIDevicePro12_9_6thiPad,
    TKUIDeviceAir6GiPad,
    TKUIDeviceAir7GiPad,
    TKUIDevicePro12_9_7thiPad,
    
    TKUIDeviceAppleTV2,
    TKUIDeviceAppleTV3,
    TKUIDeviceAppleTV4,
    
    TKUIDeviceUnknowniPhone,
    TKUIDeviceUnknowniPod,
    TKUIDeviceUnknowniPad,
    TKUIDeviceUnknownAppleTV,
    TKUIDeviceIFPGA,
    
} TKUIDevicePlatform;

typedef enum {
    TKUIDeviceFamilyiPhone,
    TKUIDeviceFamilyiPod,
    TKUIDeviceFamilyiPad,
    TKUIDeviceFamilyAppleTV,
    TKUIDeviceFamilyUnknown,
    
} TKUIDeviceFamily;

/**
 设备分辨率
 */
typedef enum {
    /**
     *  <AUTHOR> 2016-05-12 12:05:21
     *
     *  未知分辨率
     */
    TKUIDeviceResolution_Unknown      = 0,
    /**
     *iPhone 1,3,3GS 标准分辨率(320x480px)
     */
    TKUIDeviceResolution_iPhone3      = 1,
    /**
     *iPhone 4,4S 高清分辨率(640x960px)
     */
    TKUIDeviceResolution_iPhone4      = 2,
    /**
     *iPhone 5,5s,SE 高清分辨率(640x1136px)
     */
    TKUIDeviceResolution_iPhone5      = 3,
    /**
     *iPhone 6,6s,7,8 高清分辨率(750x1334px)
     */
    TKUIDeviceResolution_iPhone6      = 4,
    /**
     *iPhone 6Plus,6sPlus,7Plus,8Plus 高清分辨率(1242x2208px)
     */
    TKUIDeviceResolution_iPhone6Plus  = 5,
    /**
     *iPhone X,XS 高清分辨率(1125x2436px)
     */
    TKUIDeviceResolution_iPhoneX      = 6,
    /**
     *iPhoneXR 高清分辨率(828x1792px)
     */
    TKUIDeviceResolution_iPhoneXR     = 7,
    /**
     *iPhoneXSMax 高清分辨率(1242x2688px)
     */
    TKUIDeviceResolution_iPhoneXSMax  = 8,
    /**
     *iPhone12Mini，iPhone13Mini 高清分辨率(1080x2340px)
     */
    TKUIDeviceResolution_iPhone12Mini = 9,
    /**
     *iPhone12，iPhone12Pro，iPhone13，iPhone13Pro，iPhone14，iPhone14Pro 高清分辨率(1170x2532px)
     */
    TKUIDeviceResolution_iPhone12     = 10,
    /**
     *iPhone15, iPhone15Pro，iPhone16   高清分辨率(1179x2556px)
     */
    TKUIDeviceResolution_iPhone15     = 11,
    /**
     *iPhone16Pro   高清分辨率(1206x2622px)
     */
    TKUIDeviceResolution_iPhone16Pro  = 12,
    /**
     *iPhone12ProMax，iPhone13ProMax，iPhone14Plus，iPhone14ProMax 高清分辨率(1284x2778px)
     */
    TKUIDeviceResolution_iPhone12ProMax = 13,
    /**
     *iPhone15Plus, iPhone15ProMax 高清分辨率(1290x2796px)
     */
    TKUIDeviceResolution_iPhone15Plus   = 14,
    /**
     *iPhone16ProMax 高清分辨率(1320x2868px)
     */
    TKUIDeviceResolution_iPhone16ProMax = 15,
    /**
     * iPad 1,2 标准分辨率(1024x768px)
     */
    TKUIDevice_iPadStandardRes          = 50,
    /**
     * iPad 3 High Resolution(2048x1536px)
     */
    TKUIDevice_iPadHiRes                 = 51
}TKUIDeviceResolution;

/**
 *  设备信息获取代理接口
 */
@protocol TKDeviceInfoDelegate <NSObject>

@optional

/**
 *  获取设备的唯一性id，这里取MAC地址,大于7.0的取identifierForVendor
 *
 *  @return 设备平台信息
 */
- (NSString *) getDeviceMac;

/**
 *  获取设备的uuid
 *
 *  @return 获取设备的uuid
 */
- (NSString *)getDeviceUUID;

/**
 *  获取运营商信息(IMSI)
 *
 *  @return 获取运营商信息(IMSI)
 */
- (NSString *)getPhoneIMSI;

/**
 *  <AUTHOR> 2015-04-20 19:04:50
 *
 *  获取手机设备的本地IP地址
 *
 *  @return 手机设备的IP地址
 */
- (NSString *)getLocalIP;

@end


/**
 *  设备相关帮助类
 */
@interface TKDeviceHelper : NSObject

/**
 设置代理类
 */
+(void)setTKDeviceInfoDelegate:(id<TKDeviceInfoDelegate>) delegate;

/**
 *  获取系统设备磁盘空间信息
 *
 *  @return 系统设备磁盘空间信息
 */
+(TKDeviceSpace *)getDeviceSpace;

/**
 *  获取设备电池量
 *
 *  @return 设备电池信息
 */
+(NSString *)getDeviceBatteryPer;

/**
 *  获取设备cpu的情况
 *
 *  @return 设备cpu的信息
 */
+(TKDeviceCpu *)getDeviceCpu;

/**
 *  获取设备内存的情况
 *
 *  @return 设备内存的信息
 */
+(TKDeviceMemory *)getDeviceMemory;

/**
 *  获取设备的MAC地址,大于7.0的取identifierForVendor
 *
 *  @return 设备平台信息
 */
+(NSString *) getDeviceMac;

/**
 *  获取系统设备平台(系统自带的，没经过处理翻译)
 *
 *  @return 系统设备平台
 */
+(NSString *)getDevicePlatform;

/**
 *  获取设备型号
 *
 *  @return 设备型号
 */
+(TKUIDevicePlatform)getDevicePlatformType;

/**
 *  获取设备型号名称
 *
 *  @return 设备型号名称
 */
+(NSString *)getDevicePlatformInfo;

/**
 *  设备是否越狱
 *
 *  @return 是，否
 */
+ (BOOL) isDeviceJailBreak;

/**
 *  是否有蓝牙
 *
 *  @return 是，否
 */
+ (BOOL) isDeviceHasbluetooth;

/**
 *  获取总线的频率
 *
 *  @return 总线的频率
 */
+ (NSString *)getDeviceBusFrequency;

/**
 *  获取设备手机号
 *
 *  @return 手机号
 */
+ (NSString *)getDevicePhone;

/**
 *  获取当前设备的分辨率
 *
 *  @return 对应的分辨率类型
 */
+ (TKUIDeviceResolution)getDeviceResoluation;

/**
 *  获取当前设备的分辨率
 *
 *  @return 对应的分辨率类型
 */
+ (NSString *)getDeviceResoluationDescription;

/**
 *  获取设备的uuid
 *
 *  @return 获取设备的uuid
 */
+ (NSString *)getDeviceUUID;

/**
 *  获取系统版本号
 *
 *  @return 系统版本号
 */
+(NSString *)getDeviceSysVersion;

/**
 *  <AUTHOR> 2016-08-11 23:08:25
 *
 *  获取当前设备的语言
 *
 *  @return 系统语言
 */
+(NSString *)getDeviceSysLanguage;

/**
 *  <AUTHOR> 2016-08-11 23:08:25
 *
 *  获取当前设备的时区
 *
 *  @return 系统时区
 */
+(NSString *)getDeviceTimeZone;

/**
 *  <AUTHOR> 2016-08-11 23:08:25
 *
 *  获取设备类型(iPhone,iPad,iPod等)
 *
 *  @return 设备类型
 */
+(TKUIDeviceFamily)getDeviceFamily;

/**
 *  <AUTHOR> 2016-08-11 23:08:25
 *
 *  获取设备类型(iPhone,iPad,iPod等)
 *
 *  @return 设备类型名称
 */
+(NSString *)getDeviceFamilyName;

/**
 *  <AUTHOR> 2016-08-11 23:08:25
 *
 *  获取设备名称
 *
 *  @return 设备名称
 */
+(NSString *)getDeviceName;

@end
