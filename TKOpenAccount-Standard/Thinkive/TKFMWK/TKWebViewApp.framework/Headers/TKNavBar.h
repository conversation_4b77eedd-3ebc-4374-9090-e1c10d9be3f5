
//
//  Created by <PERSON><PERSON><PERSON> on 13-8-12.
//  Copyright (c) 2013年 YeJian. All rights reserved.
//
#import <UIKit/UIKit.h>

/**
 *  <AUTHOR> 2016-12-31 14:12:54
 *
 *  扩展支持头部颜色的设置透明度的设置和位置的设置
 */
@interface UINavigationBar (TKAwesome)

@property (nonatomic,assign,readonly)CGFloat elementAlpha;

- (void)tk_setBackgroundColor:(UIColor *)backgroundColor;

- (void)tk_setElementsAlpha:(CGFloat)alpha;

- (void)tk_setTranslationY:(CGFloat)translationY;

- (void)tk_reset;

@end

/**
 *  <AUTHOR> 2015-04-29 13:04:32
 *
 *  自定义导航栏
 */
@interface TKNavBar : UINavigationBar

/**
 *  <AUTHOR> 2015-04-29 13:04:19
 *
 *  这个是导航栏中关于状态栏的颜色,ios7以上的状态栏在内容里
 */
@property (nonatomic,strong)UIColor *stateBarColor;

/**
 *  <AUTHOR> 2015-04-29 13:04:19
 *
 *  这个是导航栏中关于状态栏的颜色,ios7以上的状态栏在内容里
 */
@property (nonatomic,strong)UIImage *stateBarImage;

/**
 *  <AUTHOR> 2015-07-01 12:07:18
 *
 *  导航栏标题文字颜色
 */
@property (nonatomic,strong)UIColor *barTitleColor;

/**
 *  <AUTHOR> 2015-04-22 18:04:11
 *
 *  导航栏的背景颜色
 */
@property (nonatomic,strong)UIColor *backgroundColor;

/**
 *  <AUTHOR> 2015-08-24 19:08:53
 *
 *  导航栏背景图片
 */
@property (nonatomic,strong)UIImage *backgroundImage;

/**
 *  <AUTHOR> 2015-04-29 22:04:14
 *
 *  导航栏的样式
 */
@property (nonatomic,assign)UIBarStyle cusBarStyle;

/**
 *  <AUTHOR> 2015-01-30 11:01:56
 *
 *  设置默认的颜色和背景色，样式等配置
 */
- (void)setDefault;

@end

/**
 导航栏的按钮类型
 */
typedef enum
{
    /**
     *  <AUTHOR> 2015-04-29 17:04:45
     *
     *  默认按钮
     */
    TKNavBarButtonItemTypeDefault = 0,
    /**
     *  <AUTHOR> 2015-04-29 17:04:02
     *
     *  返回按钮
     */
    TKNavBarButtonItemTypeBack = 1
    
}TKNavBarButtonItemType;

/**
 *  <AUTHOR> 2015-04-29 17:04:42
 *
 *  自定义导航栏按钮，用来生成导航栏按钮
 */
@interface TKNavBarButtonItem : NSObject

/**
 *  <AUTHOR> 2015-04-30 00:04:47
 *
 *  是否启用自适应图片颜色修改模板机制（会跟着button的TintColor颜色改变而改变或者UINavigationItem的titleColor）
 *
 */
@property (nonatomic,assign) BOOL isUseImageRenderingModeAlwaysTemplate;

/**
 *  <AUTHOR> 2015-04-29 17:04:59
 *
 *  按钮类型
 */
@property (nonatomic,assign)TKNavBarButtonItemType itemType;

/**
 *  <AUTHOR> 2015-04-29 17:04:42
 *
 *  生成的按钮对象
 */
@property (nonatomic,readonly)UIButton *button;

/**
 *  <AUTHOR> 2015-04-29 17:04:21
 *
 *  按钮上的文字
 */
@property (nonatomic,copy)NSString *title;

/**
 *  <AUTHOR> 2015-04-29 17:04:40
 *
 *  按钮的背景图片
 */
@property (nonatomic,copy)NSString *image;

/**
 *  <AUTHOR> 2015-04-29 17:04:51
 *
 *  按钮的字体
 */
@property (nonatomic,strong)UIFont *font;

/**
 *  <AUTHOR> 2015-04-29 18:04:46
 *
 *  正常文字颜色
 */
@property (nonatomic,strong)UIColor *normalColor;

/**
 *  <AUTHOR> 2015-04-29 18:04:03
 *
 *  选中文字的颜色
 */
@property (nonatomic,strong)UIColor *selectedColor;

/**
 *  <AUTHOR> 2015-04-29 18:04:19
 *
 *  设置高亮状态
 */
@property (nonatomic,assign)BOOL highlightedWhileSwitch;

/**
 *  <AUTHOR> 2015-04-29 22:04:28
 *
 *  生成默认的文字按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文字
 *
 *  @return 文字按钮
 */
+ (id)defauleItemWithTarget:(id)target action:(SEL)action title:(NSString *)title;

/**
 *  <AUTHOR> 2015-04-29 22:04:28
 *
 *  生成默认的文字按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文字
 *  @param fontSize 文字大小
 *  @param textColor 文字颜色
 *
 *  @return 文字按钮
 */
+ (id)defauleItemWithTarget:(id)target action:(SEL)action title:(NSString *)title fontSize:(CGFloat)fontSize textColor:(UIColor *)textColor;

/**
 *  <AUTHOR> 2015-04-29 22:04:21
 *
 *  生成默认的图片按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param image  图片名称
 *
 *  @return 图片按钮
 */
+ (id)defauleItemWithTarget:(id)target action:(SEL)action image:(NSString *)image;

/**
 *  <AUTHOR> 2015-04-29 22:04:21
 *
 *  生成默认的图片按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param image  图片名称
 *  @param isUseImageRenderingModeAlwaysTemplate 是否自适应背景色
 *
 *  @return 图片按钮
 */
+ (id)defauleItemWithTarget:(id)target action:(SEL)action image:(NSString *)image isUseImageRenderingModeAlwaysTemplate:(BOOL)isUseImageRenderingModeAlwaysTemplate;

/**
 *  <AUTHOR> 2015-04-29 22:04:07
 *
 *  生成回退按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文字
 *
 *  @return 回退按钮
 */
+ (id)backItemWithTarget:(id)target action:(SEL)action title:(NSString *)title;

/**
 *  <AUTHOR> 2015-04-29 22:04:07
 *
 *  生成回退按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文字
 *  @param fontSize 文字大小
 *  @param textColor 文字颜色
 *
 *  @return 回退按钮
 */
+ (id)backItemWithTarget:(id)target action:(SEL)action title:(NSString *)title fontSize:(CGFloat)fontSize textColor:(UIColor *)textColor;

@end

/**
 *  <AUTHOR> 2015-04-30 00:04:18
 *
 *  导航栏子容器对象
 */
@interface UINavigationItem (TKCustomBarButtonItem)

/**
 *  <AUTHOR> 2015-04-30 00:04:47
 *
 *  是否启用自适应图片颜色修改模板机制（会跟着button的TintColor颜色改变而改变或者UINavigationItem的titleColor）
 *
 */
@property (nonatomic,assign) BOOL isUseImageRenderingModeAlwaysTemplate;

/**
 *  <AUTHOR> 2015-04-30 00:04:47
 *
 *  设置文字标题
 *
 *  @param title     文字内容
 *  @param fontSize  文字大小
 *  @param textColor 文字颜色
 */
- (void)setTKNewTitle:(NSString *)title fontSize:(CGFloat)fontSize textColor:(UIColor *)textColor;

/**
 *  <AUTHOR> 2015-04-30 00:04:47
 *
 *  设置文字标题
 *
 *  @param title 文字
 */
- (void)setTKNewTitle:(NSString *)title;

/**
 *  <AUTHOR> 2015-04-30 00:04:24
 *
 *  设置标题图片
 *
 *  @param image 图片
 */
- (void)setTKNewTitleImage:(UIImage *)image;

/**
 *  <AUTHOR> 2015-04-30 00:04:54
 *
 *  设置左边普通文字按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文字
 */
- (void)setTKLeftItemWithTarget:(id)target action:(SEL)action title:(NSString *)title;

/**
 *  <AUTHOR> 2015-04-30 00:04:54
 *
 *  设置左边普通文字按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文字
 *  @param fontSize  文字大小
 *  @param textColor 文字颜色
 */
- (void)setTKLeftItemWithTarget:(id)target action:(SEL)action title:(NSString *)title fontSize:(CGFloat)fontSize textColor:(UIColor *)textColor;

/**
 *  <AUTHOR> 2015-04-30 00:04:46
 *
 *  设置左边图片按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param image  图片
 */
- (void)setTKLeftItemWithTarget:(id)target action:(SEL)action image:(NSString *)image;

/**
 *  <AUTHOR> 2015-04-30 00:04:04
 *
 *  设置左边按钮
 *
 *  @param item 按钮对象
 */
- (void)setTKLeftItemWithButtonItem:(TKNavBarButtonItem *)item;

/**
 *  <AUTHOR> 2015-04-30 00:04:21
 *
 *  设置右边文本按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文本
 */
- (void)setTKRightItemWithTarget:(id)target action:(SEL)action title:(NSString *)title;

/**
 *  <AUTHOR> 2015-04-30 00:04:21
 *
 *  设置右边文本按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文本
 *  @param fontSize  文字大小
 *  @param textColor 文字颜色
 */
- (void)setTKRightItemWithTarget:(id)target action:(SEL)action title:(NSString *)title fontSize:(CGFloat)fontSize textColor:(UIColor *)textColor;

/**
 *  <AUTHOR> 2015-04-30 00:04:17
 *
 *  设置右边图片按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param image  图片
 */
- (void)setTKRightItemWithTarget:(id)target action:(SEL)action image:(NSString *)image;

/**
 *  <AUTHOR> 2015-04-30 00:04:10
 *
 *  设置右边按钮
 *
 *  @param item 按钮对象
 */
- (void)setTKRightItemWithButtonItem:(TKNavBarButtonItem *)item;

/**
 *  <AUTHOR> 2015-04-30 00:04:40
 *
 *  设置后退按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 */
- (void)setTKBackItemWithTarget:(id)target action:(SEL)action;

/**
 *  <AUTHOR> 2015-04-30 00:04:23
 *
 *  设置后退按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文字
 */
- (void)setTKBackItemWithTarget:(id)target action:(SEL)action title:(NSString *)title;

/**
 *  <AUTHOR> 2015-04-30 00:04:23
 *
 *  设置后退按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param title  文字
 *  @param fontSize  文字大小
 *  @param textColor 文字颜色
 */
- (void)setTKBackItemWithTarget:(id)target action:(SEL)action title:(NSString *)title fontSize:(CGFloat)fontSize textColor:(UIColor *)textColor;

/**
 *  <AUTHOR> 2015-04-30 00:04:19
 *
 *  设置后退按钮
 *
 *  @param target 代理类
 *  @param action 代理方法
 *  @param image  图片
 */
- (void)setTKBackItemWithTarget:(id)target action:(SEL)action image:(NSString *)image;

/**
 * 设置导航左侧按钮
 * @param target 代理对象
 * @param action 事件
 * @param image  图片
 * @param space  间隔
 */
- (void)setTKLeftItemWithTarget:(id)target action:(SEL)action image:(NSString *)image space:(CGFloat)space;

/**
 *  <AUTHOR> 2015-04-23 14:04:55
 *
 *  设置导航左侧的按钮
 *
 *  @param target 代理对象
 *  @param action 动作事件
 *  @param title  标题
 *  @param space  间隔
 */
- (void)setTKLeftItemWithTarget:(id)target action:(SEL)action title:(NSString *)title space:(CGFloat)space;

/**
 *  <AUTHOR> 2015-04-23 14:04:55
 *
 *  设置导航左侧的按钮
 *
 *  @param target 代理对象
 *  @param action 动作事件
 *  @param title  标题
 *  @param space  间隔
 *  @param fontSize  文字大小
 *  @param textColor 文字颜色
 */
- (void)setTKLeftItemWithTarget:(id)target action:(SEL)action title:(NSString *)title space:(CGFloat)space fontSize:(CGFloat)fontSize textColor:(UIColor *)textColor;

/**
 * 设置导航右侧按钮
 * @param target 代理对象
 * @param action 事件
 * @param image  图片
 * @param space  间隔
 */
- (void)setTKRightItemWithTarget:(id)target action:(SEL)action image:(NSString *)image space:(CGFloat)space;

/**
 *  <AUTHOR> 2015-04-23 14:04:55
 *
 *  设置导航右侧的按钮
 *
 *  @param target 代理对象
 *  @param action 动作事件
 *  @param title  标题
 *  @param space  间隔
 *  @param fontSize  文字大小
 *  @param textColor 文字颜色
 */
- (void)setTKRightItemWithTarget:(id)target action:(SEL)action title:(NSString *)title space:(CGFloat)space;

/**
 *  <AUTHOR> 2015-04-23 14:04:55
 *
 *  设置导航右侧的按钮
 *
 *  @param target 代理对象
 *  @param action 动作事件
 *  @param title  标题
 *  @param space  间隔
 */
- (void)setTKRightItemWithTarget:(id)target action:(SEL)action title:(NSString *)title space:(CGFloat)space fontSize:(CGFloat)fontSize textColor:(UIColor *)textColor;

/**
 * 设置右侧加载按钮
 * @param space 右侧间距
 */
- (void)setTKRightItemWithLoadingSpace:(CGFloat)space;

@end
