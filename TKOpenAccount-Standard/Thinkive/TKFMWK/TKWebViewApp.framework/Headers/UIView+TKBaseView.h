//
//  UIView+TKBaseView.h
//  TKApp
//
//  Created by liubao on 14-12-2.
//  Copyright (c) 2014年 liubao. All rights reserved.
//

#import <UIKit/UIKit.h>
@class TKKeyBoard;

/**
 *  <AUTHOR> 2014-12-02 10:12:59
 *
 *  基础view
 */
@interface UIView (TKBaseView)

/**
 *  <AUTHOR> 2014-12-02 09:12:12
 *
 *  用户信息
 */
@property (nonatomic,retain)NSMutableDictionary *userInfo;

/**
 *  <AUTHOR> 2014-12-01 17:12:16
 *
 *  根控制器
 */
@property(nonatomic,readonly,strong) UIViewController *rootViewCtrl;

/**
 *  <AUTHOR> 2014-12-24 00:12:54
 *
 *  根窗口
 */
@property(nonatomic,readonly,strong) UIWindow *rootWindow;

/**
 *  <AUTHOR> 2014-12-24 00:12:17
 *
 *  根代理
 */
@property(nonatomic,readonly,strong)id<UIApplicationDelegate> rootDelegate;

/**
 *  获得父控制器
 */
- (UIViewController *)getViewController;

/**
 *  <AUTHOR> 2014-12-16 12:12:56
 *
 *  初始化界面
 */
-(void)loadView;

/**
 *  <AUTHOR> 2014-12-16 12:12:56
 *
 *  清理界面
 */
-(void)unLoadView;

/**
 *  隐藏键盘
 *  @param textfield 输入框
 */
-(void)closeSystemKeyBoard:(UITextField *)textfield;

/**
 *  <AUTHOR> 2015-04-10 12:04:15
 *
 *  获取窗口中得键盘对象
 *
 *  @return
 */
- (NSArray *)getSystemKeyBoardViews;

/**
 *  <AUTHOR> 2015-04-23 19:04:37
 *
 *  关闭系统键盘
 */
- (void)closeSystemKeyBoard;

/**
 *  <AUTHOR> 2015-04-21 00:04:25
 *
 *  获取H5键盘
 *
 *  @return
 */
-(TKKeyBoard *)getTKH5KeyBoard;

/**
 *  <AUTHOR> 2015-04-23 19:04:37
 *
 *  关闭H5键盘
 */
- (void)closeTKH5KeyBoard;

/**
 *  根据主window的坐标获取对应的元素View
 */
-(UIView *)findViewByPoint:(CGPoint)point;

/**
 *  根据主window的坐标来判断是否在指定view的区域内
 */
-(BOOL)isContainPointInView:(CGPoint)point;

@end
