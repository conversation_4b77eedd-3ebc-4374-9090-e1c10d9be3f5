//
//  TKBaseQRCodeWebViewController.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/7/23.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKBaseViewController.h"
#import "TKBaseComWebViewController.h"

/***
 *  WebView图片识别代理
 */
@protocol TKWebViewReadImageDelegate <NSObject>
@optional

/**
 *  <AUTHOR> 2015-04-21 10:04:50
 *
 *  处理识别出的图片二维码
 */
-(void)processReadedQRCode:(NSString *)data;

/**
 *  <AUTHOR> 2015-04-21 10:04:50
 *
 *  保存识别出的图片二维码
 */
-(void)saveReadedQRCodeImage:(UIImage *)image;

/**
 *  <AUTHOR> 2015-04-21 10:04:50
 *
 *  处理图片放大识别
 */
-(void)processReadedBiggerImage:(UIImage *)image;

@end

/**
 *  识别图片webview功能
 */
@interface TKBaseQRCodeWebViewController : TKBaseComWebViewController<TKWebViewReadImageDelegate>

/**
 *  <AUTHOR> 2015-04-10 16:04:26
 *
 *  是否支持二维码图片识别
 */
@property (nonatomic,assign)BOOL isSupportReadQRCodeImage;

/**
 *  <AUTHOR> 2015-04-10 16:04:26
 *
 *  是否支持图片放大识别
 */
@property (nonatomic,assign)BOOL isSupportReadLargerImage;

@end
