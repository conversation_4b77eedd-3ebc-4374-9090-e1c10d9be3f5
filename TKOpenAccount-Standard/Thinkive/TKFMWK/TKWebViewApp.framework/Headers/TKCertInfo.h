//
//  TKCertInfo.h
//  TKOpenAccount-Standard
//
//  Created by Clover on 2017/7/24.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface TKCertInfo : NSObject

/**
 *  证书序列号
 */
@property(nonatomic, retain) NSString *certSerialNumber;

/**
 *  证书起始日期
 */
@property(nonatomic, retain) NSDate *certStartDate;


/**
 *  证书结束日期
 */
@property(nonatomic, retain) NSDate *certEndDate;

/**
 *
 *@method stringToDate：
 *
 *@brief 将字符串转成日期对象
 *
 *@param dateStr 日期字符串
 */
- (NSDate*)stringToDate:(NSString*)dateStr;

/**
 *
 *@method isValidCert：
 *
 *@brief 检验证书是不是有效
 *
 */
- (BOOL)isValidCert;

@end
