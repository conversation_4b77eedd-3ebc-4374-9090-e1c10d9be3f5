//
// Copyright 2011-2014 NimbusKit
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

/**
 * Currently, for size units we only support pixels (resolution independent)
 * and percentage (of superview)
 */
typedef enum {
	TKCSS_PIXEL_UNIT,
	TKCSS_PERCENTAGE_UNIT,
  TKCSS_AUTO_UNIT
} TKNICSSUnitType;

/**
 * Width, height, top, left, right, bottom can be expressed in various units.
 */
typedef struct {
	TKNICSSUnitType type;
	CGFloat value;
} TKNICSSUnit;

typedef enum {
  TKNICSSButtonAdjustNone = 0,
  TKNICSSButtonAdjustHighlighted = 1,
  TKNICSSButtonAdjustDisabled = 2
} TKNICSSButtonAdjust;

/**
 * A simple translator from raw CSS rulesets to Objective-C values.
 *
 * @ingroup NimbusCSS
 *
 * Objective-C values are created on-demand and cached. These ruleset objects are cached
 * by TKNIStylesheet for a given CSS scope. When a memory warning is received, all ruleset objects
 * are removed from every stylesheet.
 */
@interface TKNICSSRuleset : NSObject {
@private
  NSMutableDictionary* _ruleset;
  
  UIColor* _textColor;
  UIColor* _selectedTextColor;
  UIColor* _highlightedTextColor;
  UIColor* _disabledTextColor;
  NSTextAlignment _textAlignment;
  UIFont* _font;
  UIColor* _textShadowColor;
  CGSize _textShadowOffset;
  NSLineBreakMode _lineBreakMode;
  NSInteger _numberOfLines;
  CGFloat _minimumFontSize;
  BOOL _adjustsFontSize;
  UIBaselineAdjustment _baselineAdjustment;
  CGFloat _opacity;
  UIColor* _backgroundColor;
  UIColor* _selectedBackgroundColor;
  UIColor* _highlightedBackgroundColor;
  UIColor* _disabledBackgroundColor;
  UIColor* _indicatorColor;
  UIColor* _indicatorLineColor;
  NSString* _backgroundImage;
  NSString* _selectedBackgroundImage;
  UIEdgeInsets _backgroundStretchInsets;
  NSString* _image;
  CGFloat _borderRadius;
  UIColor *_borderColor;
  CGFloat _borderWidth;
  UIColor *_tintColor;
  UIColor *_barTitleColor;
  UIColor *_barTintColor;
  UIColor *_navStateBarColor;
  NSString *_navStateBarImage;
  UIActivityIndicatorViewStyle _activityIndicatorStyle;
  UIViewAutoresizing _autoresizing;
  UITableViewCellSeparatorStyle _tableViewCellSeparatorStyle;
  UIColor *_tableViewCellSeparatorColor;
  UIScrollViewIndicatorStyle _scrollViewIndicatorStyle;
  NSTextAlignment _frameHorizontalAlign;
  UIViewContentMode _frameVerticalAlign;
  UIControlContentVerticalAlignment _verticalAlign;
  UIControlContentHorizontalAlignment _horizontalAlign;
  BOOL _visible;
  BOOL _masksToBounds;
  TKNICSSButtonAdjust _buttonAdjust;
  UIEdgeInsets _titleInsets;
  UIEdgeInsets _contentInsets;
  UIEdgeInsets _imageInsets;
  NSString *_textKey;
  NSString* _relativeToId;
  TKNICSSUnit _marginTop;
  TKNICSSUnit _marginLeft;
  TKNICSSUnit _marginRight;
  TKNICSSUnit _marginBottom;
  TKNICSSUnit _width;
  TKNICSSUnit _height;
  TKNICSSUnit _top;
  TKNICSSUnit _bottom;
  TKNICSSUnit _left;
  TKNICSSUnit _right;
  TKNICSSUnit _minHeight;
  TKNICSSUnit _minWidth;
  TKNICSSUnit _maxHeight;
  TKNICSSUnit _maxWidth;
  TKNICSSUnit _textHeight;
  
  union {
    struct {
      int TKTextColor : 1;
      int TKSelectedTextColor: 1;
      int TKHighlightedTextColor: 1;
      int TKDisabledTextColor: 1;
      int TKTextAlignment : 1;
      int TKFont : 1;
      int TKTextShadowColor : 1;
      int TKTextShadowOffset : 1;
      int TKLineBreakMode : 1;
      int TKNumberOfLines : 1;
      int TKMinimumFontSize : 1;
      int TKAdjustsFontSize : 1;
      int TKBaselineAdjustment : 1;
      int TKOpacity : 1;
      int TKBackgroundColor : 1;
      int TKSelectedBackgroundColor: 1;
      int TKDisabledBackgroundColor: 1;
      int TKHighlightedBackgroundColor: 1;
      int TKIndicatorColor:1;
      int TKIndicatorLineColor:1;
      int TKBackgroundImage: 1;
      int TKSelectedBackgroundImage:1;
      int TKBackgroundStretchInsets: 1;
      //16
      int TKImage: 1;
      int TKBorderRadius : 1;
      int TKBorderColor : 1;
      int TKBorderWidth : 1;
      int TKTintColor : 1;
      int TKBarTintColor :1;
      int TKBarTitleColor :1;
      int TKNavStateBarColor :1;
      int TKNavStateBarImage :1;
        
      int TKActivityIndicatorStyle : 1;
      int TKAutoresizing : 1;
      int TKTableViewCellSeparatorStyle : 1;
      int TKTableViewCellSeparatorColor : 1;
      int TKScrollViewIndicatorStyle : 1;
      int TKVerticalAlign: 1;
      int TKHorizontalAlign: 1;
      int TKWidth : 1;
      int TKHeight : 1;
      int TKTop : 1;
      int TKBottom : 1;
      int TKLeft : 1;
      // 32
      int TKRight : 1;
      int TKFrameHorizontalAlign: 1;
      int TKFrameVerticalAlign: 1;
      int TKVisible: 1;
      int TKMasksToBounds: 1;
      int TKTitleInsets: 1;
      int TKContentInsets: 1;
      int TKImageInsets: 1;
      int TKRelativeToId: 1;
      int TKMarginTop: 1;
      int TKMarginLeft: 1;
      int TKMarginRight: 1;
      int TKMarginBottom: 1;
      int TKMinWidth: 1;
      int TKMinHeight: 1;
      int TKMaxWidth: 1;
      int TKMaxHeight: 1;
      int TKTextHeight: 1;
      // 48
      int TKTextKey: 1;
      int TKButtonAdjust: 1;
      int TKHorizontalPadding: 1;
      int TKVerticalPadding: 1;
    } cached;
    int64_t _data;
  } _is;
}

- (void)addEntriesFromDictionary:(NSDictionary *)dictionary;
- (id)cssRuleForKey: (NSString*)key;

- (BOOL)hasTextColor;
- (UIColor *)textColor; // color

- (BOOL)hasSelectedTextColor;
- (UIColor *)selectedTextColor; // -ios-selected-color

- (BOOL)hasHighlightedTextColor; //-ios-highlighted-color
- (UIColor *)highlightedTextColor;

- (BOOL)hasDisabledTextColor; //-ios-disabled-color
- (UIColor *)disabledTextColor;

- (BOOL)hasTextAlignment;
- (NSTextAlignment)textAlignment; // text-align

- (BOOL)hasFont;
- (UIFont *)font; // font, font-family, font-size, font-style, font-weight

- (BOOL)hasTextShadowColor;
- (UIColor *)textShadowColor; // text-shadow

- (BOOL)hasTextShadowOffset;
- (CGSize)textShadowOffset; // text-shadow

- (BOOL)hasLineBreakMode;
- (NSLineBreakMode)lineBreakMode; // -ios-line-break-mode

- (BOOL)hasNumberOfLines;
- (NSInteger)numberOfLines; // -ios-number-of-lines

- (BOOL)hasMinimumFontSize;
- (CGFloat)minimumFontSize; // -ios-minimum-font-size

- (BOOL)hasAdjustsFontSize;
- (BOOL)adjustsFontSize; // -ios-adjusts-font-size

- (BOOL)hasBaselineAdjustment;
- (UIBaselineAdjustment)baselineAdjustment; // -ios-baseline-adjustment

- (BOOL)hasOpacity;
- (CGFloat)opacity; // opacity

- (BOOL)hasBackgroundColor;
- (UIColor *)backgroundColor; // background-color

- (BOOL)hasSelectedBackgroundColor;
- (UIColor *)selectedBackgroundColor; // -ios-selected-background-color

- (BOOL)hasDisabledBackgroundColor;
- (UIColor *)disabledBackgroundColor; // -ios-disabled-background-color

- (BOOL)hasHighlightedBackgroundColor;
- (UIColor *)highlightedBackgroundColor; // -ios-highlighted-background-color

- (BOOL)hasIndicatorColor;
- (UIColor *)indicatorColor; // -ios-indicator-color

- (BOOL)hasIndicatorLineColor;
- (UIColor *)indicatorLineColor; // -ios-indicator-line-color

- (BOOL)hasBackgroundImage;
- (NSString*)backgroundImage; // background-image

- (BOOL)hasSelectedBackgroundImage;
- (NSString*)selectedBackgroundImage; // -ios-selected-background-image

- (BOOL)hasBackgroundStretchInsets;
- (UIEdgeInsets)backgroundStretchInsets; // -mobile-background-stretch

- (BOOL)hasImage;
- (NSString*)image; // -mobile-image

- (BOOL)hasBorderRadius;
- (CGFloat)borderRadius; // border-radius

- (BOOL)hasBorderColor;
- (UIColor *)borderColor; // border, border-color

- (BOOL)hasBorderWidth;
- (CGFloat)borderWidth; // border, border-width

- (BOOL)hasWidth;
- (TKNICSSUnit)width; // width

- (BOOL)hasHeight;
- (TKNICSSUnit)height; // height

- (BOOL)hasTop;
- (TKNICSSUnit)top; // top

- (BOOL)hasBottom;
- (TKNICSSUnit)bottom; // bottom

- (BOOL)hasLeft;
- (TKNICSSUnit)left; // left

- (BOOL)hasRight;
- (TKNICSSUnit)right; // right

- (BOOL)hasMinWidth;
- (TKNICSSUnit)minWidth; // min-width

- (BOOL)hasMinHeight;
- (TKNICSSUnit)minHeight; // min-height

- (BOOL)hasMaxWidth;
- (TKNICSSUnit)maxWidth; // max-width

- (BOOL)hasMaxHeight;
- (TKNICSSUnit)maxHeight; // max-height

- (BOOL)hasTextHeight;
- (TKNICSSUnit)textHeight; // -mobile-text-height

- (BOOL)hasVerticalAlign;
- (UIControlContentVerticalAlignment)verticalAlign; // -mobile-content-valign

- (BOOL)hasHorizontalAlign;
- (UIControlContentHorizontalAlignment)horizontalAlign; // -mobile-content-halign

- (BOOL)hasFrameHorizontalAlign;
- (NSTextAlignment)frameHorizontalAlign; // -mobile-halign

- (BOOL)hasFrameVerticalAlign;
- (UIViewContentMode)frameVerticalAlign; // -mobile-valign

- (BOOL)hasTintColor;
- (UIColor *)tintColor; // -ios-tint-color

- (BOOL)hasBarTintColor;
- (UIColor *)barTintColor; // -ios-bar-tint-color

- (BOOL)hasBarTitleColor;
- (UIColor *)barTitleColor; // -ios-bar-title-color

- (BOOL)hasNavStateBarColor;
- (UIColor *)navStateBarColor; // -nav-state-bar-color

- (BOOL)hasNavStateBarImage;
- (NSString *)navStateBarImage; // -nav-state-bar-image

- (BOOL)hasActivityIndicatorStyle;
- (UIActivityIndicatorViewStyle)activityIndicatorStyle; // -ios-activity-indicator-style

- (BOOL)hasAutoresizing;
- (UIViewAutoresizing)autoresizing; // -ios-autoresizing

- (BOOL)hasTableViewCellSeparatorStyle;
- (UITableViewCellSeparatorStyle)tableViewCellSeparatorStyle; // -ios-table-view-cell-separator-style

- (BOOL)hasTableViewCellSeparatorColor;
- (UIColor *)tableViewCellSeparatorColor; // -ios-table-view-cell-separator-color

- (BOOL)hasScrollViewIndicatorStyle;
- (UIScrollViewIndicatorStyle)scrollViewIndicatorStyle; // -ios-scroll-view-indicator-style

- (BOOL)hasVisible;
- (BOOL)visible; // visibility

- (BOOL)hasMasksToBounds;
- (BOOL)masksToBounds; // masksToBounds

- (BOOL)hasButtonAdjust;
- (TKNICSSButtonAdjust)buttonAdjust; // -ios-button-adjust

- (BOOL)hasTitleInsets;
- (UIEdgeInsets)titleInsets; // -mobile-title-insets

- (BOOL)hasContentInsets;
- (UIEdgeInsets)contentInsets; // -mobile-content-insets

- (BOOL)hasImageInsets;
- (UIEdgeInsets)imageInsets; // -mobile-image-insets

- (BOOL)hasRelativeToId;
- (NSString*)relativeToId; // -mobile-relative

- (BOOL)hasMarginTop;
- (TKNICSSUnit)marginTop; // margin-top

- (BOOL)hasMarginBottom;
- (TKNICSSUnit)marginBottom; // margin-bottom

- (BOOL)hasMarginLeft;
- (TKNICSSUnit)marginLeft; // margin-left

- (BOOL)hasMarginRight;
- (TKNICSSUnit)marginRight; // margin-bottom

- (BOOL)hasTextKey;
- (NSString*)textKey; // -mobile-text-key

- (BOOL) hasHorizontalPadding;
- (TKNICSSUnit) horizontalPadding; // padding or -mobile-hPadding

- (BOOL) hasVerticalPadding;
- (TKNICSSUnit) verticalPadding; // padding or -mobile-vPadding

-(NSMutableDictionary *)getPropers;

+(UIImage *)imageByName:(NSString *)name;

@end

/**
 * Adds a raw CSS ruleset to this ruleset object.
 *
 * @fn TKNICSSRuleset::addEntriesFromDictionary:
 */

/**
 * Returns YES if the ruleset has a 'color' property.
 *
 * @fn TKNICSSRuleset::hasTextColor
 */

/**
 * Returns the text color.
 *
 * @fn TKNICSSRuleset::textColor
 */

/**
 * Returns YES if the ruleset has a 'text-align' property.
 *
 * @fn TKNICSSRuleset::hasTextAlignment
 */

/**
 * Returns the text alignment.
 *
 * @fn TKNICSSRuleset::textAlignment
 */

/**
 * Returns YES if the ruleset has a value for any of the following properties:
 * font, font-family, font-size, font-style, font-weight.
 *
 * Note: You can't specify bold or italic with a font-family due to the way fonts are
 * constructed. You also can't specify a font that is both bold and italic. In order to do
 * either of these things you must specify the font-family that corresponds to the bold or italic
 * version of your font.
 *
 * @fn TKNICSSRuleset::hasFont
 */

/**
 * Returns the font.
 *
 * @fn TKNICSSRuleset::font
 */

/**
 * Returns YES if the ruleset has a 'text-shadow' property.
 *
 * @fn TKNICSSRuleset::hasTextShadowColor
 */

/**
 * Returns the text shadow color.
 *
 * @fn TKNICSSRuleset::textShadowColor
 */

/**
 * Returns YES if the ruleset has a 'text-shadow' property.
 *
 * @fn TKNICSSRuleset::hasTextShadowOffset
 */

/**
 * Returns the text shadow offset.
 *
 * @fn TKNICSSRuleset::textShadowOffset
 */

/**
 * Returns YES if the ruleset has an '-ios-line-break-mode' property.
 *
 * @fn TKNICSSRuleset::hasLineBreakMode
 */

/**
 * Returns the line break mode.
 *
 * @fn TKNICSSRuleset::lineBreakMode
 */

/**
 * Returns YES if the ruleset has an '-ios-number-of-lines' property.
 *
 * @fn TKNICSSRuleset::hasNumberOfLines
 */

/**
 * Returns the number of lines.
 *
 * @fn TKNICSSRuleset::numberOfLines
 */

/**
 * Returns YES if the ruleset has an '-ios-minimum-font-size' property.
 *
 * @fn TKNICSSRuleset::hasMinimumFontSize
 */

/**
 * Returns the minimum font size.
 *
 * @fn TKNICSSRuleset::minimumFontSize
 */

/**
 * Returns YES if the ruleset has an '-ios-adjusts-font-size' property.
 *
 * @fn TKNICSSRuleset::hasAdjustsFontSize
 */

/**
 * Returns the adjustsFontSize value.
 *
 * @fn TKNICSSRuleset::adjustsFontSize
 */

/**
 * Returns YES if the ruleset has an '-ios-baseline-adjustment' property.
 *
 * @fn TKNICSSRuleset::hasBaselineAdjustment
 */

/**
 * Returns the baseline adjustment.
 *
 * @fn TKNICSSRuleset::baselineAdjustment
 */

/**
 * Returns YES if the ruleset has an 'opacity' property.
 *
 * @fn TKNICSSRuleset::hasOpacity
 */

/**
 * Returns the opacity.
 *
 * @fn TKNICSSRuleset::opacity
 */

/**
 * Returns YES if the ruleset has a 'background-color' property.
 *
 * @fn TKNICSSRuleset::hasBackgroundColor
 */

/**
 * Returns the background color.
 *
 * @fn TKNICSSRuleset::backgroundColor
 */

/**
 * Returns YES if the ruleset has a 'border-radius' property.
 *
 * @fn TKNICSSRuleset::hasBorderRadius
 */

/**
 * Returns the border radius.
 *
 * @fn TKNICSSRuleset::borderRadius
 */

/**
 * Returns YES if the ruleset has a 'border' or 'border-color' property.
 *
 * @fn TKNICSSRuleset::hasBorderColor
 */

/**
 * Returns the border color.
 *
 * @fn TKNICSSRuleset::borderColor
 */

/**
 * Returns YES if the ruleset has a 'border' or 'border-width' property.
 *
 * @fn TKNICSSRuleset::hasBorderWidth
 */

/**
 * Returns the border width.
 *
 * @fn TKNICSSRuleset::borderWidth
 */

/**
 * Returns YES if the ruleset has an '-ios-tint-color' property.
 *
 * @fn TKNICSSRuleset::hasTintColor
 */

/**
 * Returns the tint color.
 *
 * @fn TKNICSSRuleset::tintColor
 */

/**
 * Returns YES if the ruleset has a 'width' property.
 *
 * @fn TKNICSSRuleset::hasWidth
 */

/**
 * Returns the width.
 *
 * @fn TKNICSSRuleset::width
 */

/**
 * When relativeToId is set, a view will be positioned using margin-* directives relative to the view
 * identified by relativeToId. You can use id notation, e.g. #MyButton, or a few selectors:
 * .next, .prev, .first and .last which find the obviously named siblings. Note that the mechanics or
 * margin are not the same as CSS, which is of course a flow layout. So you cannot, for example,
 * combine margin-top and margin-bottom as only margin-top will be executed.
 *
 * Relative positioning also requires that you're careful about the order in which you register views
 * in the engine (for now), since we will evaluate the rules immediately. TODO add some simple dependency
 * management to make sure we've run the right views first.
 *
 * @fn TKNICSSRuleset::relativeToId
 */

/**
 * In combination with relativeToId, the margin fields control how a view is positioned relative to another.
 * margin-top: 0 means the top of this view will be aligned to the bottom of the view identified by relativeToId.
 * A positive number will move this further down, and a negative number further up. A *percentage* will operate
 * off the height of relativeToId and modify the position relative to margin-top:0. So -100% means "align top".
 * A value of auto means we will align the center y of relativeToId with the center y of this view.
 *
 * @fn TKNICSSRuleset::margin-top
 */

/**
 * In combination with relativeToId, the margin fields control how a view is positioned relative to another.
 * margin-bottom: 0 means the bottom of this view will be aligned to the bottom of the view identified by relativeToId.
 * A positive number will move this further down, and a negative number further up. A *percentage* will operate
 * off the height of relativeToId and modify the position relative to margin-bottom:0. So -100% means line up the bottom
 * of this view with the top of relativeToId.
 * A value of auto means we will align the center y of relativeToId with the center y of this view.
 *
 * @fn TKNICSSRuleset::margin-bottom
 */

/**
 * In combination with relativeToId, the margin fields control how a view is positioned relative to another.
 * margin-left: 0 means the left of this view will be aligned to the right of the view identified by relativeToId.
 * A positive number will move this further right, and a negative number further left. A *percentage* will operate
 * off the width of relativeToId and modify the position relative to margin-left:0. So -100% means line up the left
 * of this view with the left of relativeToId.
 * A value of auto means we will align the center x of relativeToId with the center x of this view.
 *
 * @fn TKNICSSRuleset::margin-left
 */

/**
 * In combination with relativeToId, the margin fields control how a view is positioned relative to another.
 * margin-right: 0 means the right of this view will be aligned to the right of the view identified by relativeToId.
 * A positive number will move this further right, and a negative number further left. A *percentage* will operate
 * off the width of relativeToId and modify the position relative to margin-left:0. So -100% means line up the right
 * of this view with the left of relativeToId.
 * A value of auto means we will align the center x of relativeToId with the center x of this view.
 *
 * @fn TKNICSSRuleset::margin-right
 */

/**
 * Return the rule values for a particular key, such as margin-top or width. Exposing this allows you, among
 * other things, use the CSS to hold variable information that has an effect on the layout of the views that
 * cannot be expressed as a style - such as padding.
 *
 * @fn TKNICSSRuleset::cssRuleForKey
 */

/**
 * For views that support sizeToFit, padding will add a value to the computed size
 *
 * @fn TKNICSSRuleset::horizontalPadding
 */

/**
 * For views that support sizeToFit, padding will add a value to the computed size
 *
 * @fn TKNICSSRuleset::verticalPadding
 */
