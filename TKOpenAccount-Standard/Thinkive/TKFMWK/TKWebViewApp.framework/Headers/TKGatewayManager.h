//
//  TKGatewayManager.h
//  TKAppBase_V1
//
//  Created by liu<PERSON> on 15-2-28.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKServer.h"

/**
 *  <AUTHOR> 2015-02-28 16:02:17
 *
 *  socket网关管理器
 */
@interface TKGatewayManager : NSObject

/**
 *  <AUTHOR> 2015-02-28 16:02:15
 *
 *  单例模式
 *
 *  @return
 */
+(TKGatewayManager *)shareInstance;

/**
 *  <AUTHOR> 2015-02-28 16:02:41
 *
 *  获取所有服务网关列表
 *
 *  @return
 */
-(NSMutableDictionary *)getServers;

/**
 *  <AUTHOR> 2017-06-05 22:06:58
 *
 *  获取所有的推送服务网关列表
 *
 *  @return
 */
-(NSMutableDictionary *)getPushServers;

/**
 *  <AUTHOR> 2015-03-02 16:03:43
 *
 *  获取服务器
 *
 *  @param serverName 服务器名称
 *
 *  @return
 */
-(TKServer *)getServer:(NSString *)serverName;

/**
 *  <AUTHOR> 2015-03-02 16:03:43
 *
 *  获取服务器功能号配置文件，目前是行情类产品在用
 *
 *  @param serverName 服务器名称
 *
 *  @return
 */
-(NSString *)getServerFunctionConfig:(NSString *)serverName;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  设置服务器需要使用的站点地址
 *
 *  @param serverName 服务名称
 *  @param address    站点地址
 */
-(void)setServerUseAddress:(NSString *)serverName address:(TKNetAddress *)address;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  设置服务站点是否开启国密模式,默认是NO，代表不开启
 *  @param serverName      服务名称
 *  @param isUseGMSite     是否开启国密模式
 */
-(void)setServerUseAddressGMMode:(NSString *)serverName isUseGMSite:(BOOL)isUseGMSite;

/**
 * 获取服务站点的是否开启国密模式
 */
-(BOOL)isServerUseAddressGMMode:(NSString *)serverName;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  设置服务器是否开启手动保存模式,默认是NO，代表不开启
 *  @param serverName      服务名称
 *  @param isManualMode    是否手动模式
 */
-(void)setServerUseAddressMode:(NSString *)serverName isManualMode:(BOOL)isManualMode;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  获取服务器是否开启手动保存模式
 *  @param serverName      服务名称
 */
-(BOOL)isServerUseAddressManualMode:(NSString *)serverName;

/**
 *  <AUTHOR> 2016-01-12 03:01:30
 *
 *  获取手动模式下缓存的未过期的站点地址
 */
-(NSString *)getCacheNoExpireServerUseAddress:(NSString *)serverName;

/**
 * 保存本地国密全局开关
 */
-(void)saveCacheGlobalGMFlag:(NSString *)GFFlag;

/**
 * 获取本地国密全局开关
 */
-(NSString *)getCacheGlobalGMFlag;

@end
