//
//  TKPluginInvokeCenterDelegate.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 2018/12/6.
//  Copyright © 2018年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "ResultVo.h"

/**
 *  <AUTHOR> 2015-06-12 12:06:46
 *
 *  插件回调函数
 *
 *  @param result
 */
typedef void(^TKPluginCallBackFunc)(NSMutableDictionary *result);

/**
 *  <AUTHOR> 2014-12-01 15:12:51
 *
 *  插件引擎处理
 */
@protocol TKPluginInvokeCenterDelegate <NSObject>

/**
 *  <AUTHOR> 2014-12-24 13:12:16
 *
 *  调用插件
 *
 *  @param funcNo     功能号
 *  @param param      参数
 *  @param moduleName 模块名称
 *
 *  @return
 */
-(ResultVo *)callPlugin:(NSString *)funcNo param:(id)param moduleName:(NSString *)moduleName;

/**
 *  <AUTHOR> 2014-12-24 13:12:16
 *
 *  调用插件
 *
 *  @param funcNo       功能号
 *  @param param        参数
 *  @param moduleName   模块名称
 *  @param callBackFunc 原生调用插件的回调函数
 *
 *  @return
 */
-(ResultVo *)callPlugin:(NSString *)funcNo param:(id)param moduleName:(NSString *)moduleName callBackFunc:(TKPluginCallBackFunc)callBackFunc;

/**
 *  <AUTHOR> 2014-12-24 13:12:16
 *
 *  调用插件
 *
 *  @param funcNo       功能号
 *  @param param        参数
 *  @param moduleName   模块名称
 *  @param callBackFunc 原生调用插件的回调函数
 *
 *  @return
 */
-(ResultVo *)callPlugin:(NSString *)funcNo param:(id)param moduleName:(NSString *)moduleName isH5:(BOOL)isH5 callBackFunc:(TKPluginCallBackFunc)callBackFunc;

@end
