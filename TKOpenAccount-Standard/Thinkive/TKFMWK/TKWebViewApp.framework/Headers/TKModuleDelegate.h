//
//  TKModuleDelegate.h
//  TKAppBase_V2
//
//  Created by 刘宝 on 17/1/13.
//  Copyright © 2017年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TKModuleMessage.h"

/**
 *  <AUTHOR> 2015-06-12 12:06:46
 *
 *  插件回调函数
 *
 *  @param result
 */
typedef void(^TKModuleMessageCallBackFunc)(ResultVo *resultVo);

/**
 *  <AUTHOR> 2014-12-01 11:12:18
 *
 *  模块接口定义
 */
@protocol TKModuleDelegate <NSObject>

@optional

/**
 *  <AUTHOR> 2017-01-13 08:01:32
 *
 *  处理模块消息引擎消息
 *
 *  @param message 消息
 */
-(void)onModuleMessage:(TKModuleMessage *)message __deprecated_msg("方法被建议取消使用，请用后续新增的详细分类方法代替，强烈建议");

/**
 *  <AUTHOR> 2017-01-13 08:01:32
 *
 *  处理模块消息引擎新开模块消息
 *
 *  @param message 消息
 */
-(void)onModuleOpenMessage:(TKModuleMessage *)message;

/**
 *  <AUTHOR> 2017-01-13 08:01:32
 *
 *  处理模块消息引擎关闭模块消息
 *
 *  @param message 消息
 */
-(void)onModuleCloseMessage:(TKModuleMessage *)message;

/**
 *  <AUTHOR> 2017-01-13 08:01:32
 *
 *  处理模块消息引擎切换模块消息
 *
 *  @param message 消息
 */
-(void)onModuleChangeMessage:(TKModuleMessage *)message;

/**
 *  <AUTHOR> 2017-01-13 08:01:32
 *
 *  处理模块消息引擎通知模块消息
 *
 *  @param message 消息
 */
-(void)onModuleNotifyMessage:(TKModuleMessage *)message;

@end

/**
 *  <AUTHOR> 2014-12-01 11:12:18
 *
 *  模块消息拦截定义
 */
@protocol TKModuleMessageFilterDelegate <NSObject>

/**
 *  <AUTHOR> 2017-01-13 08:01:32
 *
 *  拦截模块消息
 *
 *  @param message 消息
 *  @return YES表示继续，NO就会被中断
 */
-(BOOL)onFilterModuleMessage:(TKModuleMessage *)message;

@end

/**
 *  <AUTHOR> 2014-12-01 11:12:18
 *
 *  模块消息引擎代理
 */
@protocol TKModuleMessageEngineDelegate <NSObject>

#pragma mark 处理模块切换消息，进入模块消息分发引擎

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  注册模块拦截器
 */
-(void)onRegisterModuleMessageFilter:(id<TKModuleMessageFilterDelegate>)messageFilter;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  卸载模块拦截器
 */
-(void)unRegisterModuleMessageFilter:(id<TKModuleMessageFilterDelegate>)messageFilter;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo       功能号
 *  @param action       操作动作
 *  @param sourceModule 来源模块
 *  @param targetModule 目标模块
 *  @param param        业务入参
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo         功能号
 *  @param action         操作动作
 *  @param sourceModule   来源模块
 *  @param targetModule   目标模块
 *  @param excludeModules 目标排除模块，群发通知时候有效
 *  @param param          业务入参
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule excludeModules:(NSArray *)excludeModules param:(NSMutableDictionary *)param;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo       功能号
 *  @param action       操作动作
 *  @param sourceModule 来源模块
 *  @param targetModule 目标模块
 *  @param param        业务入参
 *  @param currentViewController  当前控制器
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule param:(NSMutableDictionary *)param currentViewController:(UIViewController *)currentViewController;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo         功能号
 *  @param action         操作动作
 *  @param sourceModule   来源模块
 *  @param targetModule   目标模块
 *  @param excludeModules 目标排除模块，群发通知时候有效
 *  @param param          业务入参
 *  @param currentViewController  当前控制器
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule excludeModules:(NSArray *)excludeModules param:(NSMutableDictionary *)param currentViewController:(UIViewController *)currentViewController;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo       功能号
 *  @param action       操作动作
 *  @param sourceModule 来源模块
 *  @param targetModule 目标模块
 *  @param param        业务入参
 *  @param moduleMessageCallBackFunc  回调函数
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule param:(NSMutableDictionary *)param moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo         功能号
 *  @param action         操作动作
 *  @param sourceModule   来源模块
 *  @param targetModule   目标模块
 *  @param excludeModules 目标排除模块，群发通知时候有效
 *  @param param          业务入参
 *  @param moduleMessageCallBackFunc  回调函数
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule excludeModules:(NSArray *)excludeModules param:(NSMutableDictionary *)param moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo       功能号
 *  @param action       操作动作
 *  @param sourceModule 来源模块
 *  @param targetModule 目标模块
 *  @param param        业务入参
 *  @param currentViewController  当前控制器
 *  @param moduleMessageCallBackFunc  回调函数
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule param:(NSMutableDictionary *)param currentViewController:(UIViewController *)currentViewController moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo         功能号
 *  @param action         操作动作
 *  @param sourceModule   来源模块
 *  @param targetModule   目标模块
 *  @param excludeModules 目标排除模块，群发通知时候有效
 *  @param param          业务入参
 *  @param currentViewController  当前控制器
 *  @param moduleMessageCallBackFunc  回调函数
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule excludeModules:(NSArray *)excludeModules param:(NSMutableDictionary *)param currentViewController:(UIViewController *)currentViewController moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo       功能号
 *  @param action       操作动作
 *  @param sourceModule 来源模块
 *  @param targetModule 目标模块
 *  @param param        业务入参
 *  @param isGreen      是否绿色通道,绿色通道可以绕过拦截器，默认是NO
 *  @param moduleMessageCallBackFunc  回调函数
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule param:(NSMutableDictionary *)param isGreen:(BOOL)isGreen moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo         功能号
 *  @param action         操作动作
 *  @param sourceModule   来源模块
 *  @param targetModule   目标模块
 *  @param excludeModules 目标排除模块，群发通知时候有效
 *  @param param          业务入参
 *  @param isGreen        是否绿色通道,绿色通道可以绕过拦截器，默认是NO
 *  @param moduleMessageCallBackFunc  回调函数
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule excludeModules:(NSArray *)excludeModules param:(NSMutableDictionary *)param isGreen:(BOOL)isGreen moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo       功能号
 *  @param action       操作动作
 *  @param sourceModule 来源模块
 *  @param targetModule 目标模块
 *  @param param        业务入参
 *  @param isGreen      是否绿色通道,绿色通道可以绕过拦截器，默认是NO
 *  @param currentViewController  当前控制器
 *  @param moduleMessageCallBackFunc  回调函数
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule param:(NSMutableDictionary *)param isGreen:(BOOL)isGreen currentViewController:(UIViewController *)currentViewController moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc;

/**
 *  <AUTHOR> 2017-01-13 09:01:16
 *
 *  发送模块交互消息，进入模块消息分发引擎
 *
 *  @param funcNo       功能号
 *  @param action       操作动作
 *  @param sourceModule 来源模块
 *  @param targetModule 目标模块
 *  @param excludeModules 目标排除模块，群发通知时候有效
 *  @param param        业务入参
 *  @param isGreen      是否绿色通道,绿色通道可以绕过拦截器，默认是NO
 *  @param currentViewController  当前控制器
 *  @param moduleMessageCallBackFunc  回调函数
 */
-(void)sendModuleMessage:(NSString *)funcNo action:(TKModuleMessage_Action)action sourceModule:(NSString *)sourceModule targetModule:(NSString *)targetModule excludeModules:(NSArray *)excludeModules param:(NSMutableDictionary *)param isGreen:(BOOL)isGreen currentViewController:(UIViewController *)currentViewController moduleMessageCallBackFunc:(TKModuleMessageCallBackFunc)moduleMessageCallBackFunc;

@end
