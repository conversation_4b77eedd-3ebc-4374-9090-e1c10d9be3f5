<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/CLLocation+TKCLLocation.h</key>
		<data>
		c75k5BhVDIop5XyEOUV3Kuehxlw=
		</data>
		<key>Headers/DynModel.h</key>
		<data>
		KfCsFbnWQxI2QkjTFvJecXby65Y=
		</data>
		<key>Headers/JSCallBack.h</key>
		<data>
		A2o8Av4lwnss+zUkgoFvzjucC+0=
		</data>
		<key>Headers/LoadInfo.h</key>
		<data>
		cnrkf+xuOnHrGlKkW6+k5YjWrUA=
		</data>
		<key>Headers/NSDictionary+TKDataRow.h</key>
		<data>
		uN1yj4GUYJClhxAiaaV0I/uIKBI=
		</data>
		<key>Headers/NSMutableDictionary+TKDataRow.h</key>
		<data>
		Z4LuerKjZ7xybZG27ASnBIK2jug=
		</data>
		<key>Headers/NSObject+TKSwizzlingMethod.h</key>
		<data>
		WeqawrmXbgAp/zcgP+1yQCD/r1o=
		</data>
		<key>Headers/NSString+TKArithmeticRegular.h</key>
		<data>
		kKdziFibICKSDF79jtGIlG+BkxM=
		</data>
		<key>Headers/ReqParamVo.h</key>
		<data>
		V17VBKDsKUYAULLaoMLJAidRKzc=
		</data>
		<key>Headers/ResultVo.h</key>
		<data>
		Sc3W7Hgm3a79LEtu+Ijm+5uDpzE=
		</data>
		<key>Headers/TKAFHTTPRequestOperation.h</key>
		<data>
		Q83MemG/yDXPytHyuT/akjmQrVo=
		</data>
		<key>Headers/TKAFHTTPRequestOperationManager.h</key>
		<data>
		AdVLTnjdD6lFa19r5itti8ycdyU=
		</data>
		<key>Headers/TKAFHTTPSessionManager.h</key>
		<data>
		5k4exOczd7ZrIeO/IvQPspNSReU=
		</data>
		<key>Headers/TKAFNetworkReachabilityManager.h</key>
		<data>
		5TDIoJ3hlor1+XCy2cWv5XwY/cg=
		</data>
		<key>Headers/TKAFNetworking.h</key>
		<data>
		O+WBvoHjwYs+Xz0J05pQBxjtDqE=
		</data>
		<key>Headers/TKAFSecurityPolicy.h</key>
		<data>
		Ln6lu9KRaOT1cc5OdlAgFfXtHyI=
		</data>
		<key>Headers/TKAFURLConnectionOperation.h</key>
		<data>
		l5BcppH5luiEN5yNuInljl946h8=
		</data>
		<key>Headers/TKAFURLRequestSerialization.h</key>
		<data>
		H/yRnnwKRNK49+HVS0IxHt/wRf0=
		</data>
		<key>Headers/TKAFURLResponseSerialization.h</key>
		<data>
		Cc09gC4qFbFx6cy+JjAcf4MxGYE=
		</data>
		<key>Headers/TKAFURLSessionManager.h</key>
		<data>
		v44Gih70e9Rechda+MXS2OmKZmY=
		</data>
		<key>Headers/TKASClient.h</key>
		<data>
		wAfgbyg6BvbGv7F401/xPEUsKn8=
		</data>
		<key>Headers/TKAbstractDatabaseLogger.h</key>
		<data>
		55F0Uoks/y7O3RfJjpwfQOLCBKc=
		</data>
		<key>Headers/TKAbstractLogger.h</key>
		<data>
		ASR1WShhC4G0rkDd+bMj3iGf2Lk=
		</data>
		<key>Headers/TKAesHelper.h</key>
		<data>
		OtdnFHLbYG0BL8gMpa36i9EvPeM=
		</data>
		<key>Headers/TKAlbumGroupViewController.h</key>
		<data>
		GoTfM1Gvq0Gr730fr+iCYl1Bg4c=
		</data>
		<key>Headers/TKAlbumModel.h</key>
		<data>
		ZhnT8IOTVORH+gM61g+COzfD6Fk=
		</data>
		<key>Headers/TKAlbumViewCell.h</key>
		<data>
		gVJ9USmCIyclm98uDuRWWCK5PHw=
		</data>
		<key>Headers/TKAlertHelper.h</key>
		<data>
		7IRk1ueMpE0vvfkSojykCitvkvQ=
		</data>
		<key>Headers/TKAppBase.h</key>
		<data>
		f9UdGuafaUsLTJy8oQMp9iW7Y9o=
		</data>
		<key>Headers/TKAppBaseDelegate.h</key>
		<data>
		FgcQ4BOR7J4ld8MMM1OgQPGQKlE=
		</data>
		<key>Headers/TKAppEngine.h</key>
		<data>
		zxY4jNxW3VerF24HLX493zUgdJA=
		</data>
		<key>Headers/TKAppInvokeDelegate.h</key>
		<data>
		1GEpMrtPFwZPO6HYwtJBBmVQJE8=
		</data>
		<key>Headers/TKAppStartManager.h</key>
		<data>
		JzCuihoYXrZdqETnAf5xAJxyk0M=
		</data>
		<key>Headers/TKAppStartPageView.h</key>
		<data>
		bg3IrRVDOAIKw5RZww7mwZ59jo8=
		</data>
		<key>Headers/TKAppStartViewController.h</key>
		<data>
		JfmZyDFe9BZsUMTnywOdz/sItuc=
		</data>
		<key>Headers/TKArrayHelper.h</key>
		<data>
		bmQ1HZuunZJCdeLIsK51yL9GUsQ=
		</data>
		<key>Headers/TKAuthorizationHelper.h</key>
		<data>
		SzYdTqm0Uoi2L2v8XiQC8KQUyi4=
		</data>
		<key>Headers/TKBase64Helper.h</key>
		<data>
		jIsjOs9J7RSWXmekYyDvolv8BLI=
		</data>
		<key>Headers/TKBaseComWebViewController.h</key>
		<data>
		C2IorBQGc75r5h9DHo+yf7aO0gs=
		</data>
		<key>Headers/TKBaseDao.h</key>
		<data>
		SwaFR3zJzGMUEi6EHDJLi8+GcWM=
		</data>
		<key>Headers/TKBaseHttpDao.h</key>
		<data>
		zpr7omEN/XEJqljTj2tM2rUYuf0=
		</data>
		<key>Headers/TKBaseKeyBoardView.h</key>
		<data>
		wNQFuMh5Q58pZvDa5rnt3eaiN9Q=
		</data>
		<key>Headers/TKBasePlugin.h</key>
		<data>
		lreQTYga67Orm3apinNs+L3LV1k=
		</data>
		<key>Headers/TKBaseQRCodeWebViewController.h</key>
		<data>
		qQCZbVnGlU/fa41dzIRH/U73/tk=
		</data>
		<key>Headers/TKBaseService.h</key>
		<data>
		YPBWFDd4NgPIVcMQE7otwEaULr0=
		</data>
		<key>Headers/TKBaseSwipeBackWebViewController.h</key>
		<data>
		gnbA5/S2DTWqRF22NW9eN/hJ940=
		</data>
		<key>Headers/TKBaseViewController.h</key>
		<data>
		sXUBzMdPBYA2DgkxK2Ab5EoGAmw=
		</data>
		<key>Headers/TKBaseWebViewController.h</key>
		<data>
		PPLrp3qrlgPAHD4iW1amNao4E2s=
		</data>
		<key>Headers/TKBlowFishHelper.h</key>
		<data>
		grYoCZRu5d8QKXjLQ5NK6WPYT+I=
		</data>
		<key>Headers/TKBusClientManager.h</key>
		<data>
		oQCCS0NXyRhX0fjcVWljyvXZH7w=
		</data>
		<key>Headers/TKBusConfig.h</key>
		<data>
		vrWHNbq8uvepbDp4X0U1GkVJPdI=
		</data>
		<key>Headers/TKBusDao.h</key>
		<data>
		cD9ia9FNOvyZYqe4jqtSFzGodI8=
		</data>
		<key>Headers/TKCLClassList.h</key>
		<data>
		Ox1tGhZryjWSqf9bPhTBzalFG2o=
		</data>
		<key>Headers/TKCLLocationManager.h</key>
		<data>
		bSroNpWEorpL7dR01/0xYe8TAFs=
		</data>
		<key>Headers/TKCLSplineInterpolator.h</key>
		<data>
		JZn3CNQH48sHwtnnc8dZpFlN/7Q=
		</data>
		<key>Headers/TKCSSTokens.h</key>
		<data>
		mmNomf5Sle/h6WQnGf0uY3vjW0A=
		</data>
		<key>Headers/TKCacheHelper.h</key>
		<data>
		z8tdetmi2Vw718YS7cVT6Va+bio=
		</data>
		<key>Headers/TKCacheManager.h</key>
		<data>
		kFCukRLjd8X3pJvL+p98u1IgWQs=
		</data>
		<key>Headers/TKCacheVo.h</key>
		<data>
		puQkXxb9xXUaCQ5thtf/xqKNaOo=
		</data>
		<key>Headers/TKCaptureCameraController.h</key>
		<data>
		UbZRCEzTK0AxqLr/IqVn6kjXgzY=
		</data>
		<key>Headers/TKCaptureSessionManager.h</key>
		<data>
		WJLJNbEzBSOKPasy40xnK177F4A=
		</data>
		<key>Headers/TKCertInfo.h</key>
		<data>
		21ik0J5/XWUoegk6+m/BkWKvQcw=
		</data>
		<key>Headers/TKCertLibHelper.h</key>
		<data>
		3zfMKuaxpbgGNuHj+LlBuXMk0UI=
		</data>
		<key>Headers/TKCertManager.h</key>
		<data>
		4OC91rpA6i+sb6H7NFVE/jbCwbg=
		</data>
		<key>Headers/TKCertUpdateManager.h</key>
		<data>
		2+7AL2Riz3+OFVW2P637ANTT/ZQ=
		</data>
		<key>Headers/TKChineseSort.h</key>
		<data>
		HUpAOnz0Qyox6ML+qLhJqkcHPd8=
		</data>
		<key>Headers/TKComBusClient.h</key>
		<data>
		3gmic9Uzx4eh+bI35InmKEqmKss=
		</data>
		<key>Headers/TKComBusTestSpeedClient.h</key>
		<data>
		E3DZgPSalisoVdXboWWURnZuIWk=
		</data>
		<key>Headers/TKComBusV3Client.h</key>
		<data>
		WTcajhO4uG0p02TgoqqZ2i8Fg4U=
		</data>
		<key>Headers/TKCommonQueue.h</key>
		<data>
		rN7MR8yT30DOfkBg1fqZh2xwfI8=
		</data>
		<key>Headers/TKCommonService.h</key>
		<data>
		kKfKm5PF+VkPZKGkYnueKyzHO3E=
		</data>
		<key>Headers/TKComponent.h</key>
		<data>
		FIQeom94PakvP/tWaIIzj5/kRyA=
		</data>
		<key>Headers/TKConfigUpdateManager.h</key>
		<data>
		oarLthc56H9Mi+2rnCcCXCvp9fc=
		</data>
		<key>Headers/TKDQAlertView.h</key>
		<data>
		v6VF7wXtrRMFDWFDnWHhv3Mbr3Q=
		</data>
		<key>Headers/TKDaoFactory.h</key>
		<data>
		eZKQlM8kIC9Pub5d68bBpJmS7wI=
		</data>
		<key>Headers/TKDataHelper.h</key>
		<data>
		pOdgnT2Zd36VDKNuhqsWdKzjtho=
		</data>
		<key>Headers/TKDataPicker.h</key>
		<data>
		Dy26ZN8lnglysBmCYeFYN8pVOuM=
		</data>
		<key>Headers/TKDataVo.h</key>
		<data>
		toSlLvO/HYzVusCxzvz2iBxtwXk=
		</data>
		<key>Headers/TKDateHelper.h</key>
		<data>
		cJBsIjffGPQlvqDYEotdbJ4twF0=
		</data>
		<key>Headers/TKDatePicker.h</key>
		<data>
		f910VbYN+OOvx7PVD8inrwmCHC0=
		</data>
		<key>Headers/TKDefaultHttpHanlder.h</key>
		<data>
		cXinvXCSklDZ3EWxquvQGvEIpbw=
		</data>
		<key>Headers/TKDevice.h</key>
		<data>
		4cfeloKQPjcw8lvaIZDbe8OBNKY=
		</data>
		<key>Headers/TKDeviceHelper.h</key>
		<data>
		bgqxDDfxCWRgBF8/pENqbO1zNI0=
		</data>
		<key>Headers/TKDirectionPanGestureRecognizer.h</key>
		<data>
		7kOFa/LtF034QJVr7+bfUdXC/gE=
		</data>
		<key>Headers/TKDock+TKNIStyleable.h</key>
		<data>
		J0w/biJxKZqG2tr9R3Ng3tuv9ro=
		</data>
		<key>Headers/TKDock.h</key>
		<data>
		pBf3PyQKsK7uX1PVuNBHYGorhj8=
		</data>
		<key>Headers/TKDockItem+TKNIStyleable.h</key>
		<data>
		/+YHaTejkTsX4xe99rKzFQ9esOI=
		</data>
		<key>Headers/TKDockItem.h</key>
		<data>
		GliUW+7ckzcokTXmLydKAdt4N8E=
		</data>
		<key>Headers/TKDownloadDataManager.h</key>
		<data>
		Ul5IjqFB656mjPz9FuYM88RzSa0=
		</data>
		<key>Headers/TKDownloadDelegate.h</key>
		<data>
		+x7orFKuU7VHv2oFd7jbNBWmCEc=
		</data>
		<key>Headers/TKDownloadModel.h</key>
		<data>
		aBHAeZyjd5HgsEjDtGe//bEZnOk=
		</data>
		<key>Headers/TKDownloadSessionManager.h</key>
		<data>
		zx4//sPNiXxNCnjUW04/w3hx1cE=
		</data>
		<key>Headers/TKDownloadUtility.h</key>
		<data>
		euhhqzXVf+1exjpnsh8V19x7Cw8=
		</data>
		<key>Headers/TKExternalJSBridge.h</key>
		<data>
		UNUkLk/zd1S+NItdk+aj20e53cs=
		</data>
		<key>Headers/TKFileHelper.h</key>
		<data>
		BCBitIXig3vrp55T+YgvyCnOBUI=
		</data>
		<key>Headers/TKFileLogger.h</key>
		<data>
		TavLJ+/iIg4cyzbF7rLyFip5OD8=
		</data>
		<key>Headers/TKFormatHelper.h</key>
		<data>
		LJ6TmZ2C2MkmxPfLYs9oFWZrbJM=
		</data>
		<key>Headers/TKFrameAdjustBlockManager.h</key>
		<data>
		WYvvE3r2JQEQENkwRRaXgFXxusY=
		</data>
		<key>Headers/TKGCDAsyncSocket.h</key>
		<data>
		1vSHWphE5MLHfLAGYuDWVZhdW+g=
		</data>
		<key>Headers/TKGCDAsyncUdpSocket.h</key>
		<data>
		o2oByIgTLlswp2PV1TWLv1yVpx0=
		</data>
		<key>Headers/TKGCDWeakTimer.h</key>
		<data>
		ro9Xn13+ooSq3LFl21uruqAKX1U=
		</data>
		<key>Headers/TKGIFImage.h</key>
		<data>
		G2B1H5YqBFBpM9o+ulZxH+GqL78=
		</data>
		<key>Headers/TKGIFImageView.h</key>
		<data>
		187728oHfYsoLsXFUZdB3jYSZss=
		</data>
		<key>Headers/TKGTMBase64.h</key>
		<data>
		RlnlQEVDfIiM9QpJbi9CD+uWw9M=
		</data>
		<key>Headers/TKGTMDefines.h</key>
		<data>
		zitHqkvuysXgPGXxP/4mB85pAjY=
		</data>
		<key>Headers/TKGatewayListener.h</key>
		<data>
		C0m+1AhmYHLTboZSw/D6QAn2rr4=
		</data>
		<key>Headers/TKGatewayManager.h</key>
		<data>
		mx/fD7EO9kjKGz5wHNFCXWxV+Gc=
		</data>
		<key>Headers/TKGestureNavigationController.h</key>
		<data>
		DTcd4rAGbuvzZBElgHPlZaVFSsw=
		</data>
		<key>Headers/TKGesturePagerController.h</key>
		<data>
		J2/1TmkZY2GGJoRHp73fxH+RhI0=
		</data>
		<key>Headers/TKGesturePasswordButton.h</key>
		<data>
		shEFpLy9PkuSU00CjfxZazqI3u8=
		</data>
		<key>Headers/TKGesturePasswordButtonDelegate.h</key>
		<data>
		84ThEdPru9Srkxi7jlq14w7lE04=
		</data>
		<key>Headers/TKGesturePasswordController.h</key>
		<data>
		hMDYlE6TFodPecrlWava1Cicwes=
		</data>
		<key>Headers/TKGesturePasswordTouchDelegate.h</key>
		<data>
		96l4EgfNSB4lk1T3iT1rctwL0og=
		</data>
		<key>Headers/TKGesturePasswordView.h</key>
		<data>
		ZNwG1O+33yuAjK8LYCAvLMgx8Ds=
		</data>
		<key>Headers/TKH5FilterManager.h</key>
		<data>
		043zpRc+X20WV1ofk+p1LBEMVmA=
		</data>
		<key>Headers/TKH5KeyBoard.h</key>
		<data>
		HzMyb64Vu5vJevXZ/phpNxmQtvc=
		</data>
		<key>Headers/TKH5ThirdZFManager.h</key>
		<data>
		Zu0oLCT+wapHQ1OC5jatBvEUP90=
		</data>
		<key>Headers/TKHexHelper.h</key>
		<data>
		20BVg/lAEoRVn7a+29M2QyEPo6U=
		</data>
		<key>Headers/TKHttpAddress.h</key>
		<data>
		PUEs6RZ9CFfvAMk/zgGG3Nd+jnE=
		</data>
		<key>Headers/TKHttpDao.h</key>
		<data>
		cxOecogu2bNGJuKPtnjqkGXGRlo=
		</data>
		<key>Headers/TKHttpRoom.h</key>
		<data>
		vJmvpFFrzZw91kBUURF+pB97pPo=
		</data>
		<key>Headers/TKHttpRoomConfig.h</key>
		<data>
		xDGi2Bhy65g2OAPOZwnQHH3j7ro=
		</data>
		<key>Headers/TKHttpRoomServerManager.h</key>
		<data>
		/O2IkR2Z3iqW4CDQtNGYQYBK3BA=
		</data>
		<key>Headers/TKHttpServer.h</key>
		<data>
		j2K3J461Wn+ejqyKga/DMgrfrTM=
		</data>
		<key>Headers/TKHttpSpeedChecker.h</key>
		<data>
		HSj+iWGKaE0qRw7ht/Ay0yQtMYc=
		</data>
		<key>Headers/TKIOSCallJSFilterDelegate.h</key>
		<data>
		KIG/rV+qb9SGAkhMNMM+GpjhdF4=
		</data>
		<key>Headers/TKImageClipEditorController.h</key>
		<data>
		b7Qf33Eb6aewLGIwravqMoZY57I=
		</data>
		<key>Headers/TKImageCropperManager.h</key>
		<data>
		lPJepbb/rmeigZ1l3glKPJ4YfIg=
		</data>
		<key>Headers/TKImageFooterView.h</key>
		<data>
		brLOv1aUJkbYXs9NLIoh7qT2CvM=
		</data>
		<key>Headers/TKImageHelper.h</key>
		<data>
		KoXwiDOCPO5xahWwuzPibEL/cVQ=
		</data>
		<key>Headers/TKImageLayerUtil.h</key>
		<data>
		I6ChxDDUD1jV6Zhof2JtINDrmUw=
		</data>
		<key>Headers/TKImagePicker.h</key>
		<data>
		g/LKD8tUGyLOg+QyBRGVzIJfWFo=
		</data>
		<key>Headers/TKImagePickerDefinition.h</key>
		<data>
		TJ7Cj/Vp3O/Qzqs2jGKs60AQ7WY=
		</data>
		<key>Headers/TKImagePickerSet.h</key>
		<data>
		/Redq27EbfVSeZnZK43Hw69sjw4=
		</data>
		<key>Headers/TKImagePickerViewController.h</key>
		<data>
		fUpQkuc7i/1wDMaOuUI8W4ErHio=
		</data>
		<key>Headers/TKImageSelectCell.h</key>
		<data>
		VfAWG0CM+zEq+BkKRAw7xkUv444=
		</data>
		<key>Headers/TKImageSelectView.h</key>
		<data>
		Ng9FZeXxCRXU6EeP0S5pyOOYQh8=
		</data>
		<key>Headers/TKImageSelectViewController.h</key>
		<data>
		UO2QO3Io1PRLL6A/XJAvHQQqb50=
		</data>
		<key>Headers/TKInvokeServerManager.h</key>
		<data>
		zjSzO0I/vBvUahmOz2tX36ovhHc=
		</data>
		<key>Headers/TKJDStatusBarNotification.h</key>
		<data>
		za8vdTRWEm1HvGTPU257uZFKCCY=
		</data>
		<key>Headers/TKJDStatusBarStyle.h</key>
		<data>
		W0xlyvjmC9yxrvouZtsTPszWicI=
		</data>
		<key>Headers/TKJDStatusBarView.h</key>
		<data>
		+3XyDxLrIqFixvyk5gkPYbSGhU8=
		</data>
		<key>Headers/TKJSCallBackManager.h</key>
		<data>
		qCwleUAHgFXLBmK04suLM66ww8E=
		</data>
		<key>Headers/TKJavascriptDao.h</key>
		<data>
		9fyVLdFRAwDBt34uDUdKs+oZswU=
		</data>
		<key>Headers/TKKeyBoard.h</key>
		<data>
		0BK8LZCGrcGHnXr10Lb1GNMMjLc=
		</data>
		<key>Headers/TKKeyBoardBoxVo.h</key>
		<data>
		jeD+ejdFiBt8K605bJQstgC2cmg=
		</data>
		<key>Headers/TKKeyBoardEventDelegate.h</key>
		<data>
		PhJ+LxLeML/PUoBx95UepfjMGnA=
		</data>
		<key>Headers/TKKeyBoardInputDelegate.h</key>
		<data>
		3t71A/yVvewG4GJxw9YgR9aBaHw=
		</data>
		<key>Headers/TKKeyBoardInputItemView.h</key>
		<data>
		JBvGNoDmMrl58yLa1mSijn556zA=
		</data>
		<key>Headers/TKKeyBoardItemView.h</key>
		<data>
		iNg9TsAuQyKWrqBSQy024sVkKdk=
		</data>
		<key>Headers/TKKeyBoardItemVo.h</key>
		<data>
		bxGeYfYHXr+v+1BRVF9eMlRMvQs=
		</data>
		<key>Headers/TKKeyBoardViewManager.h</key>
		<data>
		DSt6RzAtyi7foi7dAD8NtRwJY+Y=
		</data>
		<key>Headers/TKKeyBoardVo.h</key>
		<data>
		EdnAD/hnx13Gz+RrmVJ9WaKhfcw=
		</data>
		<key>Headers/TKKeyBoardVoManager.h</key>
		<data>
		91UHonDP/KO/CyLdDcHBTN4exHs=
		</data>
		<key>Headers/TKLayerView.h</key>
		<data>
		OvIImZa0B8E63ea2lyPWj+gjWSE=
		</data>
		<key>Headers/TKLayout.h</key>
		<data>
		B0qwOyK/1+hYr4KrNhi26mWrUW4=
		</data>
		<key>Headers/TKLittleTentacleView.h</key>
		<data>
		va/QrOHx0QpIkSNi/jl+zRwkJQg=
		</data>
		<key>Headers/TKLocalNotificationHelper.h</key>
		<data>
		BdbZk1akFKljIdH+PvYOkn8mVBo=
		</data>
		<key>Headers/TKLog.h</key>
		<data>
		/HV7Ejo64j7kttIFpiyagT1AB8c=
		</data>
		<key>Headers/TKLogFormatterDefault.h</key>
		<data>
		b10Vb7Im+lG9pdqANkd4EdfhPgA=
		</data>
		<key>Headers/TKLogFormatterDelegate.h</key>
		<data>
		wkb6VAYhMPLuVHMqr2hoWxsYEEY=
		</data>
		<key>Headers/TKLogManager.h</key>
		<data>
		A+ClyRcmTXzqCZge0iVNVUIa4q8=
		</data>
		<key>Headers/TKLogMessage.h</key>
		<data>
		KerhMcRwwA1XZ8AfHl3Scq9SN8U=
		</data>
		<key>Headers/TKLoggerDelegate.h</key>
		<data>
		R55SxSg+pkBGbRpzqRRtQsIfYZA=
		</data>
		<key>Headers/TKLoggerNode.h</key>
		<data>
		7BaXBGy5HQLZQ+XShmfy1sEthIo=
		</data>
		<key>Headers/TKMBProgressHUD.h</key>
		<data>
		/AA501KnyEIYEr1/EK/8RKnAQOw=
		</data>
		<key>Headers/TKMd5Helper.h</key>
		<data>
		zCom3vrtOo4hWoAToef7TjM3SB4=
		</data>
		<key>Headers/TKMesageDelegate.h</key>
		<data>
		L0ajQbWNXpAJTMBcq69m/ORtOTE=
		</data>
		<key>Headers/TKMessageCenter.h</key>
		<data>
		7ApYP8C6fLnTCzESxgubASfC4To=
		</data>
		<key>Headers/TKModuleDelegate.h</key>
		<data>
		hB6X9RdFEtswL32oMmBCLt+Ksbs=
		</data>
		<key>Headers/TKModuleEngine.h</key>
		<data>
		bxhgH2aOt+TBHYimR854AKe4880=
		</data>
		<key>Headers/TKModuleMessage.h</key>
		<data>
		ZpdA/U2t8Yx60oaKx4jSJb0sQdU=
		</data>
		<key>Headers/TKMovieCropperManager.h</key>
		<data>
		ipBbeAAEP6IKjgQdh8jq13l/k4k=
		</data>
		<key>Headers/TKMoviePlayerViewController.h</key>
		<data>
		9pUr/C9rPRJyJD8ha1W6YtHfQ0Q=
		</data>
		<key>Headers/TKMulBrowserCell.h</key>
		<data>
		wjWrusjRR3Cpkph4vXxYTxG1sqQ=
		</data>
		<key>Headers/TKMulImageBrowserView.h</key>
		<data>
		u9pbOjzZ+MTAFNJsfBw5ZB1yu0s=
		</data>
		<key>Headers/TKMulImageBrowserViewController.h</key>
		<data>
		CpmFjFXD1jsdv7zUd21ph2UlDyc=
		</data>
		<key>Headers/TKMulImagePickerManager.h</key>
		<data>
		RVjgTyIge043g/rf/Bx8PxU4vrI=
		</data>
		<key>Headers/TKNIActions+Subclassing.h</key>
		<data>
		h6tnINd9MTiv2n4+ssNqjf39W6k=
		</data>
		<key>Headers/TKNIActions.h</key>
		<data>
		LJJDkXmElirlyB1C6kGSzLmYvmA=
		</data>
		<key>Headers/TKNIButtonUtilities.h</key>
		<data>
		f+7UaOHalj7pybNSZTYGTjgKjdw=
		</data>
		<key>Headers/TKNICSSParser.h</key>
		<data>
		hXw0GPvaukgTt3mD1/hI1Ha8EpA=
		</data>
		<key>Headers/TKNICSSRuleset.h</key>
		<data>
		9/bSpJvpBxEtq/i71kGaj71KFYs=
		</data>
		<key>Headers/TKNICommonMetrics.h</key>
		<data>
		GmmksuqyMzm0ZlTZZGbJ8q7Ix5w=
		</data>
		<key>Headers/TKNIDOM.h</key>
		<data>
		0nw8hg/5L53uu41Q84J+QqHRFT8=
		</data>
		<key>Headers/TKNIDataStructures.h</key>
		<data>
		3lHhNRKulRreEgSbBElBJZUHPKc=
		</data>
		<key>Headers/TKNIDebuggingTools.h</key>
		<data>
		n1n2GVk+yr9luukVDoHKEeYZ4Cc=
		</data>
		<key>Headers/TKNIDeviceOrientation.h</key>
		<data>
		L2SOqxP4XfGMiP2xJpvSJPvF66A=
		</data>
		<key>Headers/TKNIError.h</key>
		<data>
		x4grlKy2wZGQVDR8I9JBZVsAxx4=
		</data>
		<key>Headers/TKNIFoundationMethods.h</key>
		<data>
		nNolC8h8dVeOrouk4IYm2CcyKQE=
		</data>
		<key>Headers/TKNIImageUtilities.h</key>
		<data>
		uruSYbYPtM/4LpVN5g6wx/V3nZk=
		</data>
		<key>Headers/TKNIInMemoryCache.h</key>
		<data>
		ZuuT0OnAOb4il8zbw7X46LMimTs=
		</data>
		<key>Headers/TKNINavigationAppearance.h</key>
		<data>
		8/Ij5x4+/EtcilnarNC9SGAvBs0=
		</data>
		<key>Headers/TKNINetworkActivity.h</key>
		<data>
		h5KOJzVX7DCDy8O/6rwVfxePvqM=
		</data>
		<key>Headers/TKNINonEmptyCollectionTesting.h</key>
		<data>
		wiC4jRp/leZYotSWxcE60TJFCYM=
		</data>
		<key>Headers/TKNINonRetainingCollections.h</key>
		<data>
		zf7MbvLzRmp6Wpy0qlx8yGLeGe4=
		</data>
		<key>Headers/TKNIOperations+Subclassing.h</key>
		<data>
		m3gBAplL7kQZfHPkGHLjIMVXopk=
		</data>
		<key>Headers/TKNIOperations.h</key>
		<data>
		2PCcmnVU6zJ4YUK4k2kCD8OJ26s=
		</data>
		<key>Headers/TKNIPaths.h</key>
		<data>
		bGvGttUrFVc57E2mltX7S2LRcH8=
		</data>
		<key>Headers/TKNIPreprocessorMacros.h</key>
		<data>
		P2BYVqqoy+MkH+jA00agVtwC434=
		</data>
		<key>Headers/TKNIRuntimeClassModifications.h</key>
		<data>
		XDenLOjdZD2XlaqyhEggMeKPQf0=
		</data>
		<key>Headers/TKNISDKAvailability.h</key>
		<data>
		JLqTnI+qwb63bFk9NEC4cfJ3G1Q=
		</data>
		<key>Headers/TKNISnapshotRotation.h</key>
		<data>
		QKa39dXNCpd6Lo2AHyb248M6wgU=
		</data>
		<key>Headers/TKNIState.h</key>
		<data>
		S4T7gUxfmuWeTBvWyR/y/dv7P4c=
		</data>
		<key>Headers/TKNIStyleable.h</key>
		<data>
		U+IowdIFJntLJ2Yb/kCcTLIbdQc=
		</data>
		<key>Headers/TKNIStylesheet.h</key>
		<data>
		5kPA073vJ2Qy1IX7yCYxUzyI4YU=
		</data>
		<key>Headers/TKNIStylesheetCache.h</key>
		<data>
		zvK5ozUNjTxi4Cs3n0iBpbxiJuE=
		</data>
		<key>Headers/TKNITextField+TKNIStyleable.h</key>
		<data>
		/liiMGYt0H5NS0z+jR5YC94HMqY=
		</data>
		<key>Headers/TKNITextField.h</key>
		<data>
		SyhQhDMzBlyi0GizPck5YTOSGbQ=
		</data>
		<key>Headers/TKNIUserInterfaceString.h</key>
		<data>
		F+oBJcBe6rr24fr+JWoHJ321Xjg=
		</data>
		<key>Headers/TKNIViewRecycler.h</key>
		<data>
		XdbAEVP1z7amLTQ6d1+H20EIJfw=
		</data>
		<key>Headers/TKNavBar+TKNIStyleable.h</key>
		<data>
		HkCfp7Mrl2VygPPRQvt/LbrQrHs=
		</data>
		<key>Headers/TKNavBar.h</key>
		<data>
		hc601UXFZ1Yd8P6v/11e2IABlZg=
		</data>
		<key>Headers/TKNetAddress.h</key>
		<data>
		4a2NpdXkzEwhYOLIndHN/Oc5pXM=
		</data>
		<key>Headers/TKNetHelper.h</key>
		<data>
		5EUod2BR8A8nD3L1uIZs/D0s+a8=
		</data>
		<key>Headers/TKNetworkInfo.h</key>
		<data>
		eDk7Mb+cxf3ptG+g3modXPHq/30=
		</data>
		<key>Headers/TKNetworkManager.h</key>
		<data>
		t/oGUHBN0gTrE/h82O+XVy1gLTA=
		</data>
		<key>Headers/TKNimbusCSS.h</key>
		<data>
		enK3L/+3rUL+4fkQqleNQJLmYXY=
		</data>
		<key>Headers/TKNimbusCore+Additions.h</key>
		<data>
		XLdxlDNkOgvBLjv2AGnPfe/NrXI=
		</data>
		<key>Headers/TKNimbusCore.h</key>
		<data>
		0gAOqwamskzqc+PvujM0UKOdimU=
		</data>
		<key>Headers/TKNumberHelper.h</key>
		<data>
		pWVYz0P7aRpq5kuj4O7SXfr4380=
		</data>
		<key>Headers/TKOpenUDID.h</key>
		<data>
		9Dz8AmEeS+foHsllMxtIZCcUlsY=
		</data>
		<key>Headers/TKOperatorCaculateFraction.h</key>
		<data>
		ONEUgYzGuicl2z5zbLI3rYo9huE=
		</data>
		<key>Headers/TKOperatorExpressionHandle.h</key>
		<data>
		28lAMky1oSYwI+OmsHE5qHFEBBw=
		</data>
		<key>Headers/TKOptionKeyBoardView.h</key>
		<data>
		OZ7wJ6Ja+yRsVYorbwAp/NKKAQ8=
		</data>
		<key>Headers/TKPasswordGenerator.h</key>
		<data>
		cZBgqlo3f0Z1JepczQGMQw81/P4=
		</data>
		<key>Headers/TKPdfViewController.h</key>
		<data>
		L66vtvNBNrz+tceZ+VG90Ap+lYY=
		</data>
		<key>Headers/TKPhotoModel.h</key>
		<data>
		4m11zXXNIlZxXDbeFmxH8FCptAI=
		</data>
		<key>Headers/TKPhotoSelectHelper.h</key>
		<data>
		mLAsIZ73VM66Si4ppgwXyHl9iiI=
		</data>
		<key>Headers/TKPlugin50000.h</key>
		<data>
		SDpNfdndjmHfsxdx4/1iv/F+JHU=
		</data>
		<key>Headers/TKPlugin50001.h</key>
		<data>
		Bv+AijZFctaWB4nJlnOImrsa+Gg=
		</data>
		<key>Headers/TKPlugin50002.h</key>
		<data>
		bpyCBwJQs+CXyS3xgLFPV173Sdc=
		</data>
		<key>Headers/TKPlugin50010.h</key>
		<data>
		3Mbcc+lbT+J/yVVd+jA6vY7iNys=
		</data>
		<key>Headers/TKPlugin50011.h</key>
		<data>
		4bkaSNYKyjkicGsA97gMlGcqLzM=
		</data>
		<key>Headers/TKPlugin50020.h</key>
		<data>
		m6KnyN56vyNRD3lR7twmy3F7hXo=
		</data>
		<key>Headers/TKPlugin50021.h</key>
		<data>
		9EVyFCMpQ/gDo2XSoliXiSkm8WI=
		</data>
		<key>Headers/TKPlugin50022.h</key>
		<data>
		FwWPIVHs1sTu7FdjAeW/JWD6PDY=
		</data>
		<key>Headers/TKPlugin50023.h</key>
		<data>
		SuFQ3wy8hY3dq1EM+joErAl7mCI=
		</data>
		<key>Headers/TKPlugin50024.h</key>
		<data>
		6oPvg6sNjzZdxEYhb4dfrYpjEYE=
		</data>
		<key>Headers/TKPlugin50025.h</key>
		<data>
		p4VDkZTrZJrr5VDwhoM7/mllUKA=
		</data>
		<key>Headers/TKPlugin50030.h</key>
		<data>
		t7NJk1SgKgd3jdBujd/+fWu1WVM=
		</data>
		<key>Headers/TKPlugin50031.h</key>
		<data>
		3zEIHflIOGk9ckqOi0766brxo60=
		</data>
		<key>Headers/TKPlugin50040.h</key>
		<data>
		m+9IDe52oDG2UNSCWcySAIOX17M=
		</data>
		<key>Headers/TKPlugin50041.h</key>
		<data>
		FF4TVVif3vSPvmfpY4kE1xzps9Q=
		</data>
		<key>Headers/TKPlugin50042.h</key>
		<data>
		syBZcxez6dwcIY+HspAnEScuiS0=
		</data>
		<key>Headers/TKPlugin50043.h</key>
		<data>
		WN1GHwJS3uI/0kWY6ssUKeXnlPk=
		</data>
		<key>Headers/TKPlugin50100.h</key>
		<data>
		jJOclPiv3ZFGttlUyzA/F/UMJc8=
		</data>
		<key>Headers/TKPlugin50101.h</key>
		<data>
		BCe7NJVT1VTnl87s0dPFHZNc9VQ=
		</data>
		<key>Headers/TKPlugin50104.h</key>
		<data>
		WrpwDbNoMAILenVGgTNPvyj05xM=
		</data>
		<key>Headers/TKPlugin50105.h</key>
		<data>
		M1iMoeN8MRJFXyb+pLySS5JXqx8=
		</data>
		<key>Headers/TKPlugin50106.h</key>
		<data>
		1T+HNecMZtRIOf3d4cWmM6VkSy0=
		</data>
		<key>Headers/TKPlugin50108.h</key>
		<data>
		kl2xHscU7+QP6CnF+8HW0MQt8cE=
		</data>
		<key>Headers/TKPlugin50109.h</key>
		<data>
		1fUS2a14bfd8wvzw/U5JC+L+ftI=
		</data>
		<key>Headers/TKPlugin50110.h</key>
		<data>
		8hbqSSxX4FDV45tSSpxidF5W3N8=
		</data>
		<key>Headers/TKPlugin50112.h</key>
		<data>
		pSHcCmHFvpW8sPr/GuAuGZJTNbM=
		</data>
		<key>Headers/TKPlugin50114.h</key>
		<data>
		kILiWw4JciqqFZMM8tzkm8WMYyo=
		</data>
		<key>Headers/TKPlugin50115.h</key>
		<data>
		gFA7ZFeD3ObsbpHY+N51rKVzMIQ=
		</data>
		<key>Headers/TKPlugin50116.h</key>
		<data>
		zYEHta/KSNfSZ6zWCSGxFwqYK2w=
		</data>
		<key>Headers/TKPlugin50118.h</key>
		<data>
		1wa1h52tbnchwPHiBRxzYfV4/Io=
		</data>
		<key>Headers/TKPlugin50119.h</key>
		<data>
		taYCbAoV4UoF27spZmpu5xOyRy8=
		</data>
		<key>Headers/TKPlugin50120.h</key>
		<data>
		cPttTBXRAKyOvNTjWKZ0SMXI8Fk=
		</data>
		<key>Headers/TKPlugin50122.h</key>
		<data>
		irmR2k6U5SEkazYzspw/BMI0uq8=
		</data>
		<key>Headers/TKPlugin50123.h</key>
		<data>
		GYoWxO1iSo0pcyBs6b4jZiFrB+k=
		</data>
		<key>Headers/TKPlugin50124.h</key>
		<data>
		lRsxVw0ZsSaEOxHBYQRxz4075WE=
		</data>
		<key>Headers/TKPlugin50125.h</key>
		<data>
		HpbJkd35/g5yTToRYe2eED5QV20=
		</data>
		<key>Headers/TKPlugin50127.h</key>
		<data>
		BncOoAGjGHiEBaGoptpUgmmVvJQ=
		</data>
		<key>Headers/TKPlugin50128.h</key>
		<data>
		KBA7YSdPb3A4AKJhfCog3g6s4Fo=
		</data>
		<key>Headers/TKPlugin50130.h</key>
		<data>
		2IIRa1mFsCbykMQCe7hUa5ZmqtQ=
		</data>
		<key>Headers/TKPlugin50131.h</key>
		<data>
		AfhMRbZdrnOoCHVluHMdGAk2Mnc=
		</data>
		<key>Headers/TKPlugin50140.h</key>
		<data>
		9uCKKtUOBNIAFBYjKa9iGaNIsD8=
		</data>
		<key>Headers/TKPlugin50141.h</key>
		<data>
		3hI2LdalaP17MwmsPQTqEy8XQKU=
		</data>
		<key>Headers/TKPlugin50200.h</key>
		<data>
		8KVxRMf77QkO9b52OeGpffzwcu8=
		</data>
		<key>Headers/TKPlugin50201.h</key>
		<data>
		JhbqpdTVhs+Rgrw61pffTll1wvw=
		</data>
		<key>Headers/TKPlugin50202.h</key>
		<data>
		DAoYfOrQr5Npl2rpZXxWWk85wvg=
		</data>
		<key>Headers/TKPlugin50203.h</key>
		<data>
		Sdg+Qivd+aLyXmd20od/05hVmY8=
		</data>
		<key>Headers/TKPlugin50210.h</key>
		<data>
		4+JtgvXs1hsB6HPH4Um0+2FdZ6k=
		</data>
		<key>Headers/TKPlugin50211.h</key>
		<data>
		/Z0l4tsCgM/ZdC6hLcGNih2OmAQ=
		</data>
		<key>Headers/TKPlugin50213.h</key>
		<data>
		iTo+ALmLgzXgEgZug3kppuQOhZM=
		</data>
		<key>Headers/TKPlugin50220.h</key>
		<data>
		Acm/tAmK7M/6XysjYXOD1LTXaUw=
		</data>
		<key>Headers/TKPlugin50221.h</key>
		<data>
		W0U6qXoi3m2W1tL0vchD5RVLbd8=
		</data>
		<key>Headers/TKPlugin50222.h</key>
		<data>
		Ks9Cj4gsiu60rLKKjuZQAdCzYpk=
		</data>
		<key>Headers/TKPlugin50224.h</key>
		<data>
		snucWKmiR8cJ9WaGDAp9FZe3nTY=
		</data>
		<key>Headers/TKPlugin50225.h</key>
		<data>
		X5t+8asLYiG/2Stipgh24enK/Jc=
		</data>
		<key>Headers/TKPlugin50228.h</key>
		<data>
		fesPKckPByb1ev5zXOUXUPNs6zc=
		</data>
		<key>Headers/TKPlugin50235.h</key>
		<data>
		qu5BViFn7ePIjGxVV8W+cvvb45c=
		</data>
		<key>Headers/TKPlugin50240.h</key>
		<data>
		7/+Qa+E9JQhrriYzACptGtQ8Lxc=
		</data>
		<key>Headers/TKPlugin50250.h</key>
		<data>
		SSHXQjAlQ3fPiPY5ujMEL0bNUpY=
		</data>
		<key>Headers/TKPlugin50252.h</key>
		<data>
		0Bwlpk3v1U0mZEtzfxTaCPiKmGc=
		</data>
		<key>Headers/TKPlugin50260.h</key>
		<data>
		Oe5QKaUmNF9d42ReuHGoQVRjZRI=
		</data>
		<key>Headers/TKPlugin50261.h</key>
		<data>
		VUUWr7lZxRVOEARtGNQtEmTcr0I=
		</data>
		<key>Headers/TKPlugin50263.h</key>
		<data>
		0Ag6L30cljTjCaPpiDkNhIAVgFM=
		</data>
		<key>Headers/TKPlugin50264.h</key>
		<data>
		HKwzBsELF57Rl2UO5ifN7HMKZXs=
		</data>
		<key>Headers/TKPlugin50266.h</key>
		<data>
		uCZXiP2wsxvdA7fN1XB0o4+M99g=
		</data>
		<key>Headers/TKPlugin50270.h</key>
		<data>
		muNI2xj0COD8ZDi5ItC2VxFvgGs=
		</data>
		<key>Headers/TKPlugin50271.h</key>
		<data>
		sHL8o75PsYF4vMP8kAtKGp+z8lk=
		</data>
		<key>Headers/TKPlugin50273.h</key>
		<data>
		xenQ5QVhQJFzmTSe8XV1tpIQHGU=
		</data>
		<key>Headers/TKPlugin50275.h</key>
		<data>
		Gm+akO1QlCRGeXNtGFV8rzKRqSQ=
		</data>
		<key>Headers/TKPlugin50276.h</key>
		<data>
		Dck0IuSPS1N6gF93S/tVqjB0m9A=
		</data>
		<key>Headers/TKPlugin50277.h</key>
		<data>
		ITgETuEBlZWfJPSsCxHwn1KOAco=
		</data>
		<key>Headers/TKPlugin50282.h</key>
		<data>
		tQu9k9eNQ+vCOIrIoFjijsuUl8U=
		</data>
		<key>Headers/TKPlugin50400.h</key>
		<data>
		inhsJXexKNjY5T4gUnmtx7fJfhg=
		</data>
		<key>Headers/TKPlugin50401.h</key>
		<data>
		q15F7LXXT+cnrNlYVdohxVl2ehI=
		</data>
		<key>Headers/TKPlugin50404.h</key>
		<data>
		AW9Um518pbBpUEVuf5FwfLWrpg8=
		</data>
		<key>Headers/TKPlugin50405.h</key>
		<data>
		rBGgDxxNcyauHppNxrxzjhs0DrQ=
		</data>
		<key>Headers/TKPlugin50406.h</key>
		<data>
		DzebKJn4qFPHwqVKOHTSIBLRjEY=
		</data>
		<key>Headers/TKPlugin50407.h</key>
		<data>
		Q7gyGBmINzs65zJPjLcNsoaoemg=
		</data>
		<key>Headers/TKPlugin50408.h</key>
		<data>
		B+2uRu8XWVa5OWh18vjCOkGqSXI=
		</data>
		<key>Headers/TKPlugin50409.h</key>
		<data>
		ssY4YuEXhgCOdf0ObLBzIxf+zSQ=
		</data>
		<key>Headers/TKPlugin50410.h</key>
		<data>
		TSRzPWidoWnOx1P5GbAvmFlL3gA=
		</data>
		<key>Headers/TKPlugin50411.h</key>
		<data>
		sCXwdSx/hmo+MuevMMgMOZfO0JA=
		</data>
		<key>Headers/TKPlugin50500.h</key>
		<data>
		qT74uvbe8v0eawH1iiaXG8L4k5k=
		</data>
		<key>Headers/TKPlugin50501.h</key>
		<data>
		5v9WGOUs7jwCJWssMe2P/VnUOJY=
		</data>
		<key>Headers/TKPlugin50502.h</key>
		<data>
		Ob+sFucxm9/CuTa1fC7GUFxnD/w=
		</data>
		<key>Headers/TKPlugin50503.h</key>
		<data>
		UrnysvN9/Rbd6OXqb/Hibt2qa+U=
		</data>
		<key>Headers/TKPlugin50504.h</key>
		<data>
		Od5+kLK1JCWuGSFF/KYIGq4656s=
		</data>
		<key>Headers/TKPluginInvokeCenter.h</key>
		<data>
		2m/ThPPar4qOeshNE92lL9yyT2g=
		</data>
		<key>Headers/TKPluginInvokeCenterDelegate.h</key>
		<data>
		UWfOsL4oz1xdJorzREqKqLge0EI=
		</data>
		<key>Headers/TKPluginInvokeDelegate.h</key>
		<data>
		Lv+x6ZgIhgdxKwTn53VM0EYSz8U=
		</data>
		<key>Headers/TKProcessDataDelegate.h</key>
		<data>
		/XLe88SNPK4tZqRwrMir5KiMt0I=
		</data>
		<key>Headers/TKQRCodeGenerator.h</key>
		<data>
		hjmy3lTKpyWCuKQWxWVQPRbV3YQ=
		</data>
		<key>Headers/TKQRCoderScanerViewController.h</key>
		<data>
		HeucLKwEPCVoc/MAHSVKBwjIqNg=
		</data>
		<key>Headers/TKQRCoderViewController.h</key>
		<data>
		JUTMPHKawMqxht4IjoZIlTUV+qs=
		</data>
		<key>Headers/TKRSA.h</key>
		<data>
		jn679tcg79QFg8D6QUPIWKYC8NU=
		</data>
		<key>Headers/TKReachability.h</key>
		<data>
		XZJB87bSD74cXftcU1ysEj6dbbg=
		</data>
		<key>Headers/TKRootViewController.h</key>
		<data>
		VEDLuiZU7na+lmxr7pscDSeCHHU=
		</data>
		<key>Headers/TKRsaHelper.h</key>
		<data>
		OWh1U7a2LEDOTm4zYmMlNgSTpuU=
		</data>
		<key>Headers/TKSDBusV3Client.h</key>
		<data>
		RJGR2HUB9jsfe/y1wxF3JpLh6ko=
		</data>
		<key>Headers/TKSM2Helper.h</key>
		<data>
		ZXohpp74wRuxMIamsYEKthuJJ+E=
		</data>
		<key>Headers/TKSM3Helper.h</key>
		<data>
		0aZISHOPJ1ZoWIoGseNkMoNrqsQ=
		</data>
		<key>Headers/TKSM4Helper.h</key>
		<data>
		kbrE2G1Ct6dEfZMiSUe/qsfX+y4=
		</data>
		<key>Headers/TKSMPageControl.h</key>
		<data>
		2hK8h0qJ7yd+01tj6MYVRzQIdCk=
		</data>
		<key>Headers/TKSSKeychain.h</key>
		<data>
		BZDwEXVF47dNZ3z6fbVj3as+UCk=
		</data>
		<key>Headers/TKSafeThreadHandleHelper.h</key>
		<data>
		Q8pRmpenxcpNCl9wvD/Mes12UFw=
		</data>
		<key>Headers/TKServer.h</key>
		<data>
		hlTg31YDOTcHN6fDD6h6V5HXKeY=
		</data>
		<key>Headers/TKServerInvokeDelegate.h</key>
		<data>
		IRN3ffySdgoTWpaIuiOkzfGa+5Q=
		</data>
		<key>Headers/TKServerLogger.h</key>
		<data>
		7W7oKYlHRXLg1btrU6xl4MHJri8=
		</data>
		<key>Headers/TKServiceDaoDelegate.h</key>
		<data>
		lV/mSDqBxCA+2pwK7xQZ+GOQP+E=
		</data>
		<key>Headers/TKServiceDelegate.h</key>
		<data>
		zz0hG/vab+iPgZ84pCuomca0sFw=
		</data>
		<key>Headers/TKServiceFilterDelegate.h</key>
		<data>
		KNl4C1QNTQ750Y5rtqL1dEm0vX0=
		</data>
		<key>Headers/TKShaHelper.h</key>
		<data>
		QJgHpZ+ALZdXAokmmKGU0ekM6NA=
		</data>
		<key>Headers/TKSlider.h</key>
		<data>
		dLuGClhiWJTM/AC75i+Cwi+l188=
		</data>
		<key>Headers/TKSocketSpeedChecker.h</key>
		<data>
		v6/8A+PzB15Bcxbx1OXD+vuY7mI=
		</data>
		<key>Headers/TKSoundHelper.h</key>
		<data>
		qFCd5pyYoFVYQOS91+jP7eZr2Eo=
		</data>
		<key>Headers/TKSpacer.h</key>
		<data>
		rkZiGAvPDcYbj8jGfGZeby+1Q48=
		</data>
		<key>Headers/TKStringHelper.h</key>
		<data>
		x8itjT0q7bdhAugI9Tzgvga5dVs=
		</data>
		<key>Headers/TKSynthesizeSigleton.h</key>
		<data>
		G6t+kuMYF6zw1qPk6LJ9Rg+tBgM=
		</data>
		<key>Headers/TKSystemHelper.h</key>
		<data>
		VvCH2QNvRZl1AhbIOkSSbNe0hi4=
		</data>
		<key>Headers/TKTTYLogger.h</key>
		<data>
		7RPfJ9tE+i8c5vT20UINl4T32d4=
		</data>
		<key>Headers/TKTabBarViewController.h</key>
		<data>
		FdaYUayhe1k37E9rvSxjqHuXnvU=
		</data>
		<key>Headers/TKTabView+TKNIStyleable.h</key>
		<data>
		lYRVN2sxdA3E4eQ33YcIM0phwIo=
		</data>
		<key>Headers/TKTabView.h</key>
		<data>
		J+BkPsrN4ivB1NNWhs+JQHFcMto=
		</data>
		<key>Headers/TKTelAddress.h</key>
		<data>
		QM6fg74a1vHME89DcffR608zR7c=
		</data>
		<key>Headers/TKTentacleView.h</key>
		<data>
		VGiUeV4M4RS4jMmzNPRkGmFMSkc=
		</data>
		<key>Headers/TKTextField.h</key>
		<data>
		6MSJPJpz+lJmFJrp5lOn3AT3uAo=
		</data>
		<key>Headers/TKThemeManager.h</key>
		<data>
		X85S0DcZv+inRxnxKNpGEmUkmpI=
		</data>
		<key>Headers/TKToast+UIView.h</key>
		<data>
		X8FeODaW8wJda2SA0Li5QdGCG6U=
		</data>
		<key>Headers/TKTraffic.h</key>
		<data>
		e7PBDN24Kz/ipglmToW9D2gMu9c=
		</data>
		<key>Headers/TKUIHelper.h</key>
		<data>
		QC9IU6XpcizmtP7maPZXsHTWQ34=
		</data>
		<key>Headers/TKUIView.h</key>
		<data>
		diHViaALbGWN7GCHwMEZdiJ+YSU=
		</data>
		<key>Headers/TKURLRequestHelper.h</key>
		<data>
		qX4S9dw9HBCHvHbL6igX4c01LaI=
		</data>
		<key>Headers/TKUUIDHelper.h</key>
		<data>
		J7yMOICeTyEec3M9hjATDY4jokg=
		</data>
		<key>Headers/TKUpdateManager.h</key>
		<data>
		qWy9zMOE2GOJAyqdAXgYfFSidqI=
		</data>
		<key>Headers/TKUploadManager.h</key>
		<data>
		B1I/o5CE19lxyfZAmeS+a90E1Pc=
		</data>
		<key>Headers/TKUtil.h</key>
		<data>
		7Uo0CQ8zlJjoWEvl+7YsRK+xpV4=
		</data>
		<key>Headers/TKVPImageCropperViewController.h</key>
		<data>
		QrZrUMZDZOMYTnm73Rp5NL1diPY=
		</data>
		<key>Headers/TKWeakTimer.h</key>
		<data>
		MKSMNagQOK0la9L058lJ2ehr7gs=
		</data>
		<key>Headers/TKWebView.h</key>
		<data>
		lMzSnPxFpKXjCbO5liY2j11l0Yo=
		</data>
		<key>Headers/TKWebViewApp.h</key>
		<data>
		K4We1AEE1UtJanfDPCS0Or+WTz8=
		</data>
		<key>Headers/TKWebViewHelper.h</key>
		<data>
		f/JKPHG7y8SIM0n00JXVq/+phIE=
		</data>
		<key>Headers/TKXML.h</key>
		<data>
		xt+d1AFYhHMDkwDkMCb6YJpSaWM=
		</data>
		<key>Headers/TKYYAnimatedImageView.h</key>
		<data>
		QhnknAg4zqYMTDBk1vwBWHCogWE=
		</data>
		<key>Headers/TKYYFrameImage.h</key>
		<data>
		/RJb3IkgXDWb38KEyzvCP5Cmvo8=
		</data>
		<key>Headers/TKYYImage.h</key>
		<data>
		4Z7RT15RtPbKjndYiLo4vhZo8Kc=
		</data>
		<key>Headers/TKYYImageCoder.h</key>
		<data>
		rVOk4E2/7ycxZ6/vcDj8tR3jyfM=
		</data>
		<key>Headers/TKYYSpriteSheetImage.h</key>
		<data>
		kPZ7uPxZS8Hg7VO++uyQuSFiFJo=
		</data>
		<key>Headers/TKZipArchive.h</key>
		<data>
		ZjdlJu1U+ksFtAdXS5uhBDVyP98=
		</data>
		<key>Headers/TKZlibHelper.h</key>
		<data>
		hAWN3JN7GjuQs4pPbuOItCx/8FQ=
		</data>
		<key>Headers/UIActivityIndicatorView+TKNIStyleable.h</key>
		<data>
		DELnh+xwCTnQPgaMWfqmA7kyQCc=
		</data>
		<key>Headers/UIApplication+TKApplication.h</key>
		<data>
		ZQuJKeJoWn4J8KbsQw1oIWK7OUM=
		</data>
		<key>Headers/UIButton+TKNIStyleable.h</key>
		<data>
		G66rIHUaCESNG1nel/8+yV2FMbA=
		</data>
		<key>Headers/UIControl+TKPunctuate.h</key>
		<data>
		nncvaxBgZRy38ZSEUUzh1D/fHwE=
		</data>
		<key>Headers/UIDevice+TKSystemVersion.h</key>
		<data>
		23YSw+2dLgddobCN2XIZpi6utK0=
		</data>
		<key>Headers/UIImage+TKUtility.h</key>
		<data>
		LjMLhz/ydWpOaEFBr/qv25WcLu8=
		</data>
		<key>Headers/UIImageView+TKNIStyleable.h</key>
		<data>
		zH3IzqNXKlAwbsb31TKCKpjQiQY=
		</data>
		<key>Headers/UILabel+TKAjust.h</key>
		<data>
		SqDIm+ND5A56WZnxTp6tN7e3b7I=
		</data>
		<key>Headers/UILabel+TKNIStyleable.h</key>
		<data>
		LW78MBkCJyx1savJCrC0K98iSc0=
		</data>
		<key>Headers/UINavigationBar+TKNIStyleable.h</key>
		<data>
		eDoUPWTwC8x+pcJcF3Ee885nNzM=
		</data>
		<key>Headers/UINavigationItem+TKTheme.h</key>
		<data>
		lAALaB0l83KhNryqxEP8nzhuF/4=
		</data>
		<key>Headers/UIResponder+TKImageRouter.h</key>
		<data>
		zhK4Mz417eKk6O7sPve8fz7n2tQ=
		</data>
		<key>Headers/UIResponder+TKNimbusCore.h</key>
		<data>
		p3ReonyLvZM3zwpnvL8qScy7pYc=
		</data>
		<key>Headers/UIScrollView+TKNIStyleable.h</key>
		<data>
		rdHHqhoULPoTY3ZMGBc0JXzscXY=
		</data>
		<key>Headers/UIScrollView+TKPullLoad.h</key>
		<data>
		PmNztu31jSfGfajFS6XskEvm9q0=
		</data>
		<key>Headers/UISearchBar+TKNIStyleable.h</key>
		<data>
		HcP0DpOeU1nPUGd+CS5n+PCh6+0=
		</data>
		<key>Headers/UISegmentedControl+TKNIStyleable.h</key>
		<data>
		orBrxa6ve9pkrCP9Uu32pZa1kWA=
		</data>
		<key>Headers/UITabBar+TKNIStyleable.h</key>
		<data>
		n3FpRMaWtQbDG4c/9tP9VrG8tKQ=
		</data>
		<key>Headers/UITableView+TKNIStyleable.h</key>
		<data>
		nLI6JYgZ37K0JBqHUT8CSWQo/AE=
		</data>
		<key>Headers/UITextField+TKNIStyleable.h</key>
		<data>
		C+0Xv2nWY+1KK6dGgYQOO+YoN/U=
		</data>
		<key>Headers/UITextView+TKNIStyleable.h</key>
		<data>
		/TlREpOgwq388/LQjb2zL1/Pm54=
		</data>
		<key>Headers/UIToolbar+TKNIStyleable.h</key>
		<data>
		ELBHBoyxn1Yl3ZCxJfB/Ua76tD4=
		</data>
		<key>Headers/UIView+TKBaseView.h</key>
		<data>
		nhUul+S2/tRJbtJFWhgAJZ8wtBg=
		</data>
		<key>Headers/UIView+TKFrame.h</key>
		<data>
		jLtL7YtuMb6JX1r7aezo9gRG7hQ=
		</data>
		<key>Headers/UIView+TKLayoutUtil.h</key>
		<data>
		t5YWqPlQ+6e7YmWh8WvFd9uDa4E=
		</data>
		<key>Headers/UIView+TKNIStyleable.h</key>
		<data>
		uqgoeQFz6m6MjOIFvyjn6FvE/Tc=
		</data>
		<key>Headers/UIView+TKPunctuate.h</key>
		<data>
		QjNwwrO0T/AZNSs+ueo6l1cbU4o=
		</data>
		<key>Headers/UIView+TKTheme.h</key>
		<data>
		xObvD8CF48CPjaL0btQ6czRymzk=
		</data>
		<key>Headers/UIViewController+TKBaseViewController.h</key>
		<data>
		obis61tcBiq6LBy0+PK8buTZKxM=
		</data>
		<key>Headers/tkblowfish.h</key>
		<data>
		xCDsc7kE/p8rXN4fEJ/U/Ev0qZk=
		</data>
		<key>Headers/tkcrypt.h</key>
		<data>
		Ol+6OBR08EAxNzgNxMHNJwiugzI=
		</data>
		<key>Headers/tkioapi.h</key>
		<data>
		g8pt5xrrLknNLdZuASn0b3yetCM=
		</data>
		<key>Headers/tkmztools.h</key>
		<data>
		+F5U/i8Tg3OmONI1AtRJfm66PZY=
		</data>
		<key>Headers/tkunzip.h</key>
		<data>
		dsSyKwPyIKpeZVFrMd76+Fpd7Xg=
		</data>
		<key>Headers/tkzip.h</key>
		<data>
		2HjDCQTUJycDpqAif77hQ3SPNZ0=
		</data>
		<key>Info.plist</key>
		<data>
		tknw03FYB+8qI/DtAVqBdGIyBvQ=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>开户标准版.txt</key>
		<data>
		AeFnhR0qQFUOyj+lMSOtqhe5AVc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/CLLocation+TKCLLocation.h</key>
		<dict>
			<key>hash</key>
			<data>
			c75k5BhVDIop5XyEOUV3Kuehxlw=
			</data>
			<key>hash2</key>
			<data>
			IoJUAuNveLFhr1J5iazQ6sadBT1bmOyp76oHsLGZ5dY=
			</data>
		</dict>
		<key>Headers/DynModel.h</key>
		<dict>
			<key>hash</key>
			<data>
			KfCsFbnWQxI2QkjTFvJecXby65Y=
			</data>
			<key>hash2</key>
			<data>
			9ASbwY/vzVxSRlWi3N46Z7AFnlw8Pae1hYr3aC5zTHI=
			</data>
		</dict>
		<key>Headers/JSCallBack.h</key>
		<dict>
			<key>hash</key>
			<data>
			A2o8Av4lwnss+zUkgoFvzjucC+0=
			</data>
			<key>hash2</key>
			<data>
			JSGmIq47i+MQ7V5LnvtuhizSUbM91wH8ME75le5h0UM=
			</data>
		</dict>
		<key>Headers/LoadInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			cnrkf+xuOnHrGlKkW6+k5YjWrUA=
			</data>
			<key>hash2</key>
			<data>
			gv+Oc+dm2Z7BriT+CCaVInTuIcNFDcIH0X45yYdh/Is=
			</data>
		</dict>
		<key>Headers/NSDictionary+TKDataRow.h</key>
		<dict>
			<key>hash</key>
			<data>
			uN1yj4GUYJClhxAiaaV0I/uIKBI=
			</data>
			<key>hash2</key>
			<data>
			lsidv0J/awzOGdD/uGFc9tEE3rmWmCEq8c1UGU27wFY=
			</data>
		</dict>
		<key>Headers/NSMutableDictionary+TKDataRow.h</key>
		<dict>
			<key>hash</key>
			<data>
			Z4LuerKjZ7xybZG27ASnBIK2jug=
			</data>
			<key>hash2</key>
			<data>
			94WeQ2IWLVGZ1aSWxGkPXGCIIKbznOuYDAIPvXlTJnk=
			</data>
		</dict>
		<key>Headers/NSObject+TKSwizzlingMethod.h</key>
		<dict>
			<key>hash</key>
			<data>
			WeqawrmXbgAp/zcgP+1yQCD/r1o=
			</data>
			<key>hash2</key>
			<data>
			s7uzzh8cV0oTgDV9QgHQaPILOykW2rw3eAypaXDMHY8=
			</data>
		</dict>
		<key>Headers/NSString+TKArithmeticRegular.h</key>
		<dict>
			<key>hash</key>
			<data>
			kKdziFibICKSDF79jtGIlG+BkxM=
			</data>
			<key>hash2</key>
			<data>
			h5jhsdDyMH36Oq2rjGg9OSqxr9nlrIqAayZFwK92igA=
			</data>
		</dict>
		<key>Headers/ReqParamVo.h</key>
		<dict>
			<key>hash</key>
			<data>
			V17VBKDsKUYAULLaoMLJAidRKzc=
			</data>
			<key>hash2</key>
			<data>
			+RzgRBNqgLFZD2mKnjQFkMoG2dMkqkDqBcO8ju9s5qY=
			</data>
		</dict>
		<key>Headers/ResultVo.h</key>
		<dict>
			<key>hash</key>
			<data>
			Sc3W7Hgm3a79LEtu+Ijm+5uDpzE=
			</data>
			<key>hash2</key>
			<data>
			B8wj8Q4oU6MqPu4y4pPuBsBRPG+s41+3H3C3JYGvcI8=
			</data>
		</dict>
		<key>Headers/TKAFHTTPRequestOperation.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q83MemG/yDXPytHyuT/akjmQrVo=
			</data>
			<key>hash2</key>
			<data>
			ccqpjvgK0IuNk2nv2Y0qFnV4XHvkTdlpe/7NuTF2jmo=
			</data>
		</dict>
		<key>Headers/TKAFHTTPRequestOperationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			AdVLTnjdD6lFa19r5itti8ycdyU=
			</data>
			<key>hash2</key>
			<data>
			PMXY4UGziX8hX3SDFMINoHKYHSB3vJ/Ni30hJ6bG6I0=
			</data>
		</dict>
		<key>Headers/TKAFHTTPSessionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			5k4exOczd7ZrIeO/IvQPspNSReU=
			</data>
			<key>hash2</key>
			<data>
			cMh6GVOQo/OmL7F18yuGvjcVzDht4t/Oz4Jvv5q2Lyg=
			</data>
		</dict>
		<key>Headers/TKAFNetworkReachabilityManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			5TDIoJ3hlor1+XCy2cWv5XwY/cg=
			</data>
			<key>hash2</key>
			<data>
			E2FFR4MyFTc92EzjzAIrbbZS/Nk+AmRLiq4SHYmOUIY=
			</data>
		</dict>
		<key>Headers/TKAFNetworking.h</key>
		<dict>
			<key>hash</key>
			<data>
			O+WBvoHjwYs+Xz0J05pQBxjtDqE=
			</data>
			<key>hash2</key>
			<data>
			Zsz2xmgJigfaKMcTwAy+Ytu6HR+tqd1oVGG8nMU2Kgw=
			</data>
		</dict>
		<key>Headers/TKAFSecurityPolicy.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ln6lu9KRaOT1cc5OdlAgFfXtHyI=
			</data>
			<key>hash2</key>
			<data>
			w9SiXK4pxh8U6hHH+M7Tc2o27MaSVFKJ01RxKjTah0k=
			</data>
		</dict>
		<key>Headers/TKAFURLConnectionOperation.h</key>
		<dict>
			<key>hash</key>
			<data>
			l5BcppH5luiEN5yNuInljl946h8=
			</data>
			<key>hash2</key>
			<data>
			LQ0sY8yN+Cg5rncQMeiTTJ2SOP3w7dXVTgh4W1ryKhg=
			</data>
		</dict>
		<key>Headers/TKAFURLRequestSerialization.h</key>
		<dict>
			<key>hash</key>
			<data>
			H/yRnnwKRNK49+HVS0IxHt/wRf0=
			</data>
			<key>hash2</key>
			<data>
			35vYbyZLev/ZO4H5/zeKk4aPNokrb94DcB/91QHlUUo=
			</data>
		</dict>
		<key>Headers/TKAFURLResponseSerialization.h</key>
		<dict>
			<key>hash</key>
			<data>
			Cc09gC4qFbFx6cy+JjAcf4MxGYE=
			</data>
			<key>hash2</key>
			<data>
			R7TgDRGf2hbY52Pru5y9hMBlGInSLx3O0JMHGcGvOYw=
			</data>
		</dict>
		<key>Headers/TKAFURLSessionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			v44Gih70e9Rechda+MXS2OmKZmY=
			</data>
			<key>hash2</key>
			<data>
			gh/8k/En7nKT1/O9hMLANHETHmsSlmiZSxxnCllvjgI=
			</data>
		</dict>
		<key>Headers/TKASClient.h</key>
		<dict>
			<key>hash</key>
			<data>
			wAfgbyg6BvbGv7F401/xPEUsKn8=
			</data>
			<key>hash2</key>
			<data>
			goeZ4sNdqDGL+5IhOGcEna0FWAZYSUtH4bZ6DHiS4Ec=
			</data>
		</dict>
		<key>Headers/TKAbstractDatabaseLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			55F0Uoks/y7O3RfJjpwfQOLCBKc=
			</data>
			<key>hash2</key>
			<data>
			LaMZJQTpyaA+wTLK+EMFTd8jB5qGIxkrMeB597ZksFc=
			</data>
		</dict>
		<key>Headers/TKAbstractLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			ASR1WShhC4G0rkDd+bMj3iGf2Lk=
			</data>
			<key>hash2</key>
			<data>
			7SSy175lygHLr6sg9u+ZCxUMYBieZZzwz/ohRSfXLLw=
			</data>
		</dict>
		<key>Headers/TKAesHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			OtdnFHLbYG0BL8gMpa36i9EvPeM=
			</data>
			<key>hash2</key>
			<data>
			+O+kj6z3wu1hZBZHcb1OmIJPrMgL7I2ug3uGx25/5yQ=
			</data>
		</dict>
		<key>Headers/TKAlbumGroupViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			GoTfM1Gvq0Gr730fr+iCYl1Bg4c=
			</data>
			<key>hash2</key>
			<data>
			28+jmwtr3B/bM5XPkLwu9kX/Kepj3itfcOBGtRlDfpY=
			</data>
		</dict>
		<key>Headers/TKAlbumModel.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZhnT8IOTVORH+gM61g+COzfD6Fk=
			</data>
			<key>hash2</key>
			<data>
			grdfYqe65O934bPKeowRG87Pb6D5FP+JRmOoCYeJmxk=
			</data>
		</dict>
		<key>Headers/TKAlbumViewCell.h</key>
		<dict>
			<key>hash</key>
			<data>
			gVJ9USmCIyclm98uDuRWWCK5PHw=
			</data>
			<key>hash2</key>
			<data>
			ME8TjOw6RQ72k3hKEqPkfjx9zR9vyAHcvfH8og5lSC0=
			</data>
		</dict>
		<key>Headers/TKAlertHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			7IRk1ueMpE0vvfkSojykCitvkvQ=
			</data>
			<key>hash2</key>
			<data>
			29aXa8rWrinvtsCtiHm/wvAVsBnYjVKIkWBUqiBEVeg=
			</data>
		</dict>
		<key>Headers/TKAppBase.h</key>
		<dict>
			<key>hash</key>
			<data>
			f9UdGuafaUsLTJy8oQMp9iW7Y9o=
			</data>
			<key>hash2</key>
			<data>
			886GrxonOi/CYbaVryOIz1UeEDWPm6VmQWbJV5Yrr1Y=
			</data>
		</dict>
		<key>Headers/TKAppBaseDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			FgcQ4BOR7J4ld8MMM1OgQPGQKlE=
			</data>
			<key>hash2</key>
			<data>
			M38XtoCm/jicSdwWtHS2/n4plVKCZBWx+nTPt6M1Yjg=
			</data>
		</dict>
		<key>Headers/TKAppEngine.h</key>
		<dict>
			<key>hash</key>
			<data>
			zxY4jNxW3VerF24HLX493zUgdJA=
			</data>
			<key>hash2</key>
			<data>
			9/TDKTg5KP1WEG9uwrHqOLqcDMXzk/b/t1ZkoSyBG+w=
			</data>
		</dict>
		<key>Headers/TKAppInvokeDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			1GEpMrtPFwZPO6HYwtJBBmVQJE8=
			</data>
			<key>hash2</key>
			<data>
			dKp/76XiUIv37GaKGdpmf/h9BPA2FSYLryUtR/tEGPI=
			</data>
		</dict>
		<key>Headers/TKAppStartManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			JzCuihoYXrZdqETnAf5xAJxyk0M=
			</data>
			<key>hash2</key>
			<data>
			XMWJp8vM6Zr9D7AfGVgNDPfalj/KE3/ptRRz5P0/Dqg=
			</data>
		</dict>
		<key>Headers/TKAppStartPageView.h</key>
		<dict>
			<key>hash</key>
			<data>
			bg3IrRVDOAIKw5RZww7mwZ59jo8=
			</data>
			<key>hash2</key>
			<data>
			Q3A4mgcozLvNyz16YeCPm/+gIdK11V0y7/44I1rhZHc=
			</data>
		</dict>
		<key>Headers/TKAppStartViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			JfmZyDFe9BZsUMTnywOdz/sItuc=
			</data>
			<key>hash2</key>
			<data>
			R25KssKH3FyX8jWO/AohpZllDdfP1ldFmnbsBpVKTG4=
			</data>
		</dict>
		<key>Headers/TKArrayHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			bmQ1HZuunZJCdeLIsK51yL9GUsQ=
			</data>
			<key>hash2</key>
			<data>
			v34mo9wdlD1nzcReWtNU61H/aG1iFWRIX7bdeZPwfyI=
			</data>
		</dict>
		<key>Headers/TKAuthorizationHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			SzYdTqm0Uoi2L2v8XiQC8KQUyi4=
			</data>
			<key>hash2</key>
			<data>
			kLx0Xh9affk/+uCOiADTrI5DkNs3+Vy1Gmtd3SWlaj8=
			</data>
		</dict>
		<key>Headers/TKBase64Helper.h</key>
		<dict>
			<key>hash</key>
			<data>
			jIsjOs9J7RSWXmekYyDvolv8BLI=
			</data>
			<key>hash2</key>
			<data>
			YlS3Kj1vYnyci1f5qySwGA/eBSZXQ4Ylz5l7ypE38hA=
			</data>
		</dict>
		<key>Headers/TKBaseComWebViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			C2IorBQGc75r5h9DHo+yf7aO0gs=
			</data>
			<key>hash2</key>
			<data>
			InXmxEVq8B47zi9W7SwKmsf5tGfUeA1rwPWDwF9X/Jg=
			</data>
		</dict>
		<key>Headers/TKBaseDao.h</key>
		<dict>
			<key>hash</key>
			<data>
			SwaFR3zJzGMUEi6EHDJLi8+GcWM=
			</data>
			<key>hash2</key>
			<data>
			X3tL6my16CHqvIZlpDR+Q7O3ftjxH7FrZCM1Zt+J04M=
			</data>
		</dict>
		<key>Headers/TKBaseHttpDao.h</key>
		<dict>
			<key>hash</key>
			<data>
			zpr7omEN/XEJqljTj2tM2rUYuf0=
			</data>
			<key>hash2</key>
			<data>
			1e5iA8B9rPm3fKk6bHfPag1BvNKaJv/n8GgLpJFft8A=
			</data>
		</dict>
		<key>Headers/TKBaseKeyBoardView.h</key>
		<dict>
			<key>hash</key>
			<data>
			wNQFuMh5Q58pZvDa5rnt3eaiN9Q=
			</data>
			<key>hash2</key>
			<data>
			Vb3P7P4s17uTJdZwPPTJztFoTWVL1lKwarftq0+ZZzw=
			</data>
		</dict>
		<key>Headers/TKBasePlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			lreQTYga67Orm3apinNs+L3LV1k=
			</data>
			<key>hash2</key>
			<data>
			gRwzbsdfufxabBNPEPVkZw9wSX9UFiA4x0kJlY8/Pf4=
			</data>
		</dict>
		<key>Headers/TKBaseQRCodeWebViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			qQCZbVnGlU/fa41dzIRH/U73/tk=
			</data>
			<key>hash2</key>
			<data>
			VxNRCriPkmKQmUK5CYr1xmIToC0O/GkmLm1zUS1IjAI=
			</data>
		</dict>
		<key>Headers/TKBaseService.h</key>
		<dict>
			<key>hash</key>
			<data>
			YPBWFDd4NgPIVcMQE7otwEaULr0=
			</data>
			<key>hash2</key>
			<data>
			jW1Cl/6tsxRmROUWbHsxKbiNrHzNcw5QTXy3z80jV7g=
			</data>
		</dict>
		<key>Headers/TKBaseSwipeBackWebViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			gnbA5/S2DTWqRF22NW9eN/hJ940=
			</data>
			<key>hash2</key>
			<data>
			iLn2SUZYKrNQZLwkPbgSiGZm0B7mjPDsQvrzpPFNNJ8=
			</data>
		</dict>
		<key>Headers/TKBaseViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			sXUBzMdPBYA2DgkxK2Ab5EoGAmw=
			</data>
			<key>hash2</key>
			<data>
			8ngelK6cvVQL7L86smu8ER2xVVgMux4WntDGBRQn7jk=
			</data>
		</dict>
		<key>Headers/TKBaseWebViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			PPLrp3qrlgPAHD4iW1amNao4E2s=
			</data>
			<key>hash2</key>
			<data>
			k1ANCyhnx1EPiW16uxPkOEwW/NPmNePRY3cjXYTfb1U=
			</data>
		</dict>
		<key>Headers/TKBlowFishHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			grYoCZRu5d8QKXjLQ5NK6WPYT+I=
			</data>
			<key>hash2</key>
			<data>
			F4AlyP7AdgPTmmMHKAjy/1UXvx82V2MYYaGOQfgSnig=
			</data>
		</dict>
		<key>Headers/TKBusClientManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			oQCCS0NXyRhX0fjcVWljyvXZH7w=
			</data>
			<key>hash2</key>
			<data>
			TL9m8lCsNJG+0eWFuKQv8iF1+ZYeFbN2Z37JCbvokTE=
			</data>
		</dict>
		<key>Headers/TKBusConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			vrWHNbq8uvepbDp4X0U1GkVJPdI=
			</data>
			<key>hash2</key>
			<data>
			u/YZVGFULlF+5szTcJY0CJYA1bDQK1qiMVhXCfODrVc=
			</data>
		</dict>
		<key>Headers/TKBusDao.h</key>
		<dict>
			<key>hash</key>
			<data>
			cD9ia9FNOvyZYqe4jqtSFzGodI8=
			</data>
			<key>hash2</key>
			<data>
			sVGlPaKfivd+Gzv/wMWvqG/R1BM6MrAZYZk321/OEjw=
			</data>
		</dict>
		<key>Headers/TKCLClassList.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ox1tGhZryjWSqf9bPhTBzalFG2o=
			</data>
			<key>hash2</key>
			<data>
			DCNSRzpx/X6gT3dmicFAa38iGXa4AyWMoLbH2aGFiJU=
			</data>
		</dict>
		<key>Headers/TKCLLocationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			bSroNpWEorpL7dR01/0xYe8TAFs=
			</data>
			<key>hash2</key>
			<data>
			CY2VBbgpwurisyLbLJYPVSDwsdnFeQ1VyMApYWb+vPg=
			</data>
		</dict>
		<key>Headers/TKCLSplineInterpolator.h</key>
		<dict>
			<key>hash</key>
			<data>
			JZn3CNQH48sHwtnnc8dZpFlN/7Q=
			</data>
			<key>hash2</key>
			<data>
			0d1vJXjFJj/KubrS9cfKR35SQkTLPQmXErRmDMjayfo=
			</data>
		</dict>
		<key>Headers/TKCSSTokens.h</key>
		<dict>
			<key>hash</key>
			<data>
			mmNomf5Sle/h6WQnGf0uY3vjW0A=
			</data>
			<key>hash2</key>
			<data>
			vjE2Waqcco4KFfOkYT7IuB+mFglrlbT5dejD2DSUjIM=
			</data>
		</dict>
		<key>Headers/TKCacheHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			z8tdetmi2Vw718YS7cVT6Va+bio=
			</data>
			<key>hash2</key>
			<data>
			0+L8h60w8ZN+46VKhoBHV8Qyvvg/tusGKVg/JSYhN6k=
			</data>
		</dict>
		<key>Headers/TKCacheManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			kFCukRLjd8X3pJvL+p98u1IgWQs=
			</data>
			<key>hash2</key>
			<data>
			kJHKI4GasRIyZLJd1XTJMzuA6UxgvTcfmcoAuDsF/kI=
			</data>
		</dict>
		<key>Headers/TKCacheVo.h</key>
		<dict>
			<key>hash</key>
			<data>
			puQkXxb9xXUaCQ5thtf/xqKNaOo=
			</data>
			<key>hash2</key>
			<data>
			x6JI8uNQl/taQ17fZ8FubqtFjoa/IAJnu+OHVzkfF8M=
			</data>
		</dict>
		<key>Headers/TKCaptureCameraController.h</key>
		<dict>
			<key>hash</key>
			<data>
			UbZRCEzTK0AxqLr/IqVn6kjXgzY=
			</data>
			<key>hash2</key>
			<data>
			GiixQlxFFLSX36S4ZQ16YtmntfnIob+Gt6ue+NLKoko=
			</data>
		</dict>
		<key>Headers/TKCaptureSessionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			WJLJNbEzBSOKPasy40xnK177F4A=
			</data>
			<key>hash2</key>
			<data>
			OcAuw6RAwXgoyQVILhV/GfkXbINJF2x9bjeMo6xuTxs=
			</data>
		</dict>
		<key>Headers/TKCertInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			21ik0J5/XWUoegk6+m/BkWKvQcw=
			</data>
			<key>hash2</key>
			<data>
			bunTyBg9BP1by4TqCB2iweWn1sZUBhU0Dco39GYBdQQ=
			</data>
		</dict>
		<key>Headers/TKCertLibHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			3zfMKuaxpbgGNuHj+LlBuXMk0UI=
			</data>
			<key>hash2</key>
			<data>
			9jt7ljd/505RzSEREBana3RsVoUIaBFHZ3QBaosR2xc=
			</data>
		</dict>
		<key>Headers/TKCertManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			4OC91rpA6i+sb6H7NFVE/jbCwbg=
			</data>
			<key>hash2</key>
			<data>
			EijzeAurYm6QrsVmj+MqeOXI6FDi2Prl9KCf+zkUvW0=
			</data>
		</dict>
		<key>Headers/TKCertUpdateManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			2+7AL2Riz3+OFVW2P637ANTT/ZQ=
			</data>
			<key>hash2</key>
			<data>
			1XXWAKBBgPVMawIzqVjeZWqcSw168r1WMurDAVVfxQQ=
			</data>
		</dict>
		<key>Headers/TKChineseSort.h</key>
		<dict>
			<key>hash</key>
			<data>
			HUpAOnz0Qyox6ML+qLhJqkcHPd8=
			</data>
			<key>hash2</key>
			<data>
			mBklUQBLBv7JmN/+rafmkt47a32dxvqYkl2e2Oeucek=
			</data>
		</dict>
		<key>Headers/TKComBusClient.h</key>
		<dict>
			<key>hash</key>
			<data>
			3gmic9Uzx4eh+bI35InmKEqmKss=
			</data>
			<key>hash2</key>
			<data>
			hX1WBl9Kj6cGWthqGn6XqF1JR2QWkjaaI0gmHJuhUCM=
			</data>
		</dict>
		<key>Headers/TKComBusTestSpeedClient.h</key>
		<dict>
			<key>hash</key>
			<data>
			E3DZgPSalisoVdXboWWURnZuIWk=
			</data>
			<key>hash2</key>
			<data>
			ra/SpeIxD+muTsHH6+D8C44BZc7VLkzdMEp0EMdeCSg=
			</data>
		</dict>
		<key>Headers/TKComBusV3Client.h</key>
		<dict>
			<key>hash</key>
			<data>
			WTcajhO4uG0p02TgoqqZ2i8Fg4U=
			</data>
			<key>hash2</key>
			<data>
			UVyq/YLSe8yblyx8sw9woOdMIsmnN89/TZmXzAPgigU=
			</data>
		</dict>
		<key>Headers/TKCommonQueue.h</key>
		<dict>
			<key>hash</key>
			<data>
			rN7MR8yT30DOfkBg1fqZh2xwfI8=
			</data>
			<key>hash2</key>
			<data>
			GHUP1yD429DUI2VCCu/HKXB7wg90H8CuL2n/0mJ+gQY=
			</data>
		</dict>
		<key>Headers/TKCommonService.h</key>
		<dict>
			<key>hash</key>
			<data>
			kKfKm5PF+VkPZKGkYnueKyzHO3E=
			</data>
			<key>hash2</key>
			<data>
			PaErvHtUNlsSfCj0Lx37Y2OOMTIQ50YCd6fFG2UwlU0=
			</data>
		</dict>
		<key>Headers/TKComponent.h</key>
		<dict>
			<key>hash</key>
			<data>
			FIQeom94PakvP/tWaIIzj5/kRyA=
			</data>
			<key>hash2</key>
			<data>
			cnRfoIwVSUFbbOxXJAqv0YnoCjbrpB9xht6Ln71vt2Y=
			</data>
		</dict>
		<key>Headers/TKConfigUpdateManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			oarLthc56H9Mi+2rnCcCXCvp9fc=
			</data>
			<key>hash2</key>
			<data>
			Lutu/RSywq5ujcAK2nAUNZdjYFSBqHznd5dGklT/tto=
			</data>
		</dict>
		<key>Headers/TKDQAlertView.h</key>
		<dict>
			<key>hash</key>
			<data>
			v6VF7wXtrRMFDWFDnWHhv3Mbr3Q=
			</data>
			<key>hash2</key>
			<data>
			PNZouwFowFq2oW+OpLrn2r3C3q1p4MMbiHjpw/BHHgs=
			</data>
		</dict>
		<key>Headers/TKDaoFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			eZKQlM8kIC9Pub5d68bBpJmS7wI=
			</data>
			<key>hash2</key>
			<data>
			XfRU1xVCzV1+nRCugfuyauTi9DCvHcxwmCNKUhvzJ1Y=
			</data>
		</dict>
		<key>Headers/TKDataHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			pOdgnT2Zd36VDKNuhqsWdKzjtho=
			</data>
			<key>hash2</key>
			<data>
			llbO6iwZ29z1SUDqWy0dkgv0HBGyQkTtWO6IBCTrws8=
			</data>
		</dict>
		<key>Headers/TKDataPicker.h</key>
		<dict>
			<key>hash</key>
			<data>
			Dy26ZN8lnglysBmCYeFYN8pVOuM=
			</data>
			<key>hash2</key>
			<data>
			/40wMAcG9cKFqYtgieWTSGOsGKosQQRO1EI09ErKcxQ=
			</data>
		</dict>
		<key>Headers/TKDataVo.h</key>
		<dict>
			<key>hash</key>
			<data>
			toSlLvO/HYzVusCxzvz2iBxtwXk=
			</data>
			<key>hash2</key>
			<data>
			p2QQNXSja58UlW6Rm3edruGNqP3xSfqpRo41fRgAvLc=
			</data>
		</dict>
		<key>Headers/TKDateHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			cJBsIjffGPQlvqDYEotdbJ4twF0=
			</data>
			<key>hash2</key>
			<data>
			0QrNON/aaLJt3Wo7Nv7tQLWMxKTbWflsjBcVJTissiw=
			</data>
		</dict>
		<key>Headers/TKDatePicker.h</key>
		<dict>
			<key>hash</key>
			<data>
			f910VbYN+OOvx7PVD8inrwmCHC0=
			</data>
			<key>hash2</key>
			<data>
			QhSnCQmo27ZU/GJ7/YgT6c/zzU3ThwGTbdJOoBXkjNA=
			</data>
		</dict>
		<key>Headers/TKDefaultHttpHanlder.h</key>
		<dict>
			<key>hash</key>
			<data>
			cXinvXCSklDZ3EWxquvQGvEIpbw=
			</data>
			<key>hash2</key>
			<data>
			RGPb1aqCFT1TFe2IIsUuj3ppmBvnRH19nTr8TTvD12Y=
			</data>
		</dict>
		<key>Headers/TKDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			4cfeloKQPjcw8lvaIZDbe8OBNKY=
			</data>
			<key>hash2</key>
			<data>
			Mu/hw12/dOsR/T32ZxgqQFW2p0e2wm5ClbXjTfHR6KI=
			</data>
		</dict>
		<key>Headers/TKDeviceHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			bgqxDDfxCWRgBF8/pENqbO1zNI0=
			</data>
			<key>hash2</key>
			<data>
			w1uHIHwHHgHi1nFlr0TlO6uLofXhsCB0WMme6FROWHw=
			</data>
		</dict>
		<key>Headers/TKDirectionPanGestureRecognizer.h</key>
		<dict>
			<key>hash</key>
			<data>
			7kOFa/LtF034QJVr7+bfUdXC/gE=
			</data>
			<key>hash2</key>
			<data>
			eFcEji3hb9mB/i/9TEUHyyw77FS3C3loTlFA53sViv0=
			</data>
		</dict>
		<key>Headers/TKDock+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			J0w/biJxKZqG2tr9R3Ng3tuv9ro=
			</data>
			<key>hash2</key>
			<data>
			20eOIlZcHjBR85tlE4M+8qGNcULYJGur9GPNj6bntcg=
			</data>
		</dict>
		<key>Headers/TKDock.h</key>
		<dict>
			<key>hash</key>
			<data>
			pBf3PyQKsK7uX1PVuNBHYGorhj8=
			</data>
			<key>hash2</key>
			<data>
			kAkHUi5hiBCBhwPeCikkv0XiFuJc+DGaqJ51TAaDYVo=
			</data>
		</dict>
		<key>Headers/TKDockItem+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			/+YHaTejkTsX4xe99rKzFQ9esOI=
			</data>
			<key>hash2</key>
			<data>
			K6jIpcH+gKBEFztsaTIahlVuzpr8e7RUdZ4ij0ZFT6A=
			</data>
		</dict>
		<key>Headers/TKDockItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			GliUW+7ckzcokTXmLydKAdt4N8E=
			</data>
			<key>hash2</key>
			<data>
			gdgyQ72ucJOVrzG8xs1oSzajPs1ghinSu/MzEI6i/GI=
			</data>
		</dict>
		<key>Headers/TKDownloadDataManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ul5IjqFB656mjPz9FuYM88RzSa0=
			</data>
			<key>hash2</key>
			<data>
			GrIULIoIFvqWj4QDPfyq0mSE9m8qXRSrZoa1xrlD4qc=
			</data>
		</dict>
		<key>Headers/TKDownloadDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			+x7orFKuU7VHv2oFd7jbNBWmCEc=
			</data>
			<key>hash2</key>
			<data>
			TIa2TcUf3RE464cAbqJW00kQx8QCpDcuVUfE4tEVHbc=
			</data>
		</dict>
		<key>Headers/TKDownloadModel.h</key>
		<dict>
			<key>hash</key>
			<data>
			aBHAeZyjd5HgsEjDtGe//bEZnOk=
			</data>
			<key>hash2</key>
			<data>
			tqbm8/eA/0K0smyG1m9eKtOO+sVW4wM9R8nDn2mtsbg=
			</data>
		</dict>
		<key>Headers/TKDownloadSessionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			zx4//sPNiXxNCnjUW04/w3hx1cE=
			</data>
			<key>hash2</key>
			<data>
			EQOLieBA+PKVZtDdLonhE0WdwB8XzhX+EKSkHcP2oYw=
			</data>
		</dict>
		<key>Headers/TKDownloadUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			euhhqzXVf+1exjpnsh8V19x7Cw8=
			</data>
			<key>hash2</key>
			<data>
			b9QDkxzNgDHHcMRk7LT7avihLjWQeVJcv6hGQMH+mak=
			</data>
		</dict>
		<key>Headers/TKExternalJSBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			UNUkLk/zd1S+NItdk+aj20e53cs=
			</data>
			<key>hash2</key>
			<data>
			KPLw/nkBImTNHuhHi0i2d4N5/O3reXZKQKgf5k9W6O8=
			</data>
		</dict>
		<key>Headers/TKFileHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			BCBitIXig3vrp55T+YgvyCnOBUI=
			</data>
			<key>hash2</key>
			<data>
			lECHI5iAtiuZp06BOj4lnk5DS8SAgNBTrzFA2Bam19U=
			</data>
		</dict>
		<key>Headers/TKFileLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			TavLJ+/iIg4cyzbF7rLyFip5OD8=
			</data>
			<key>hash2</key>
			<data>
			64yzr0/QuRibnFS9tgM0PzShb9nzQRLZ3yZV3hxUWA8=
			</data>
		</dict>
		<key>Headers/TKFormatHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			LJ6TmZ2C2MkmxPfLYs9oFWZrbJM=
			</data>
			<key>hash2</key>
			<data>
			QEBcNi8VN/G+JqyKPr/IRWO90gq9oVgJ7YY9zLFsf8o=
			</data>
		</dict>
		<key>Headers/TKFrameAdjustBlockManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			WYvvE3r2JQEQENkwRRaXgFXxusY=
			</data>
			<key>hash2</key>
			<data>
			SdTr4mqypbWldTYe2Iy3/voVKkFmeg2H1m6MHgG5nbw=
			</data>
		</dict>
		<key>Headers/TKGCDAsyncSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			1vSHWphE5MLHfLAGYuDWVZhdW+g=
			</data>
			<key>hash2</key>
			<data>
			ItA9dLgRZ5lJetcwRQqNmBo7jBLDvApMAqtUG6Ckq8A=
			</data>
		</dict>
		<key>Headers/TKGCDAsyncUdpSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			o2oByIgTLlswp2PV1TWLv1yVpx0=
			</data>
			<key>hash2</key>
			<data>
			cypIJNUWw9tBXc7XlHzym+MvQvli2PdcbkQJIWOVhGE=
			</data>
		</dict>
		<key>Headers/TKGCDWeakTimer.h</key>
		<dict>
			<key>hash</key>
			<data>
			ro9Xn13+ooSq3LFl21uruqAKX1U=
			</data>
			<key>hash2</key>
			<data>
			OOb7THIH5xTH2oWA83up/f55FFiI5Iis0Ad7IY/3g6M=
			</data>
		</dict>
		<key>Headers/TKGIFImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			G2B1H5YqBFBpM9o+ulZxH+GqL78=
			</data>
			<key>hash2</key>
			<data>
			4Jj4oSVsyswVkcO04h4fU3itDgvjl/78zru1K+KLGPY=
			</data>
		</dict>
		<key>Headers/TKGIFImageView.h</key>
		<dict>
			<key>hash</key>
			<data>
			187728oHfYsoLsXFUZdB3jYSZss=
			</data>
			<key>hash2</key>
			<data>
			IjbFuUtvIvfJvAGbeWB+ALsnUwoUCIgzJROPyxELC1g=
			</data>
		</dict>
		<key>Headers/TKGTMBase64.h</key>
		<dict>
			<key>hash</key>
			<data>
			RlnlQEVDfIiM9QpJbi9CD+uWw9M=
			</data>
			<key>hash2</key>
			<data>
			1mIJ77UOdzcn9lKbDaLV3A5IHqEhX93XlHieAkd03Vc=
			</data>
		</dict>
		<key>Headers/TKGTMDefines.h</key>
		<dict>
			<key>hash</key>
			<data>
			zitHqkvuysXgPGXxP/4mB85pAjY=
			</data>
			<key>hash2</key>
			<data>
			Xpgq1CmXlCWr7oV85MB7ADBYIBL332pbqVlHizj4mOU=
			</data>
		</dict>
		<key>Headers/TKGatewayListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			C0m+1AhmYHLTboZSw/D6QAn2rr4=
			</data>
			<key>hash2</key>
			<data>
			o/J3DCNe3wzwTr/wfLBYBDW0S4/dpP2g3J2fCW4CP/w=
			</data>
		</dict>
		<key>Headers/TKGatewayManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			mx/fD7EO9kjKGz5wHNFCXWxV+Gc=
			</data>
			<key>hash2</key>
			<data>
			z4zjwtUL/cJlQc97p56dUcaDKz+ithwOed0/rZa9xqY=
			</data>
		</dict>
		<key>Headers/TKGestureNavigationController.h</key>
		<dict>
			<key>hash</key>
			<data>
			DTcd4rAGbuvzZBElgHPlZaVFSsw=
			</data>
			<key>hash2</key>
			<data>
			jYWyMF6gQ6GP6dsj0epSL+A/FYMbDGCiUdD7/+FhLFQ=
			</data>
		</dict>
		<key>Headers/TKGesturePagerController.h</key>
		<dict>
			<key>hash</key>
			<data>
			J2/1TmkZY2GGJoRHp73fxH+RhI0=
			</data>
			<key>hash2</key>
			<data>
			v9Ok+9vSatovmJEtg32L2TdwTHUwi6kmBL+N4j4h1as=
			</data>
		</dict>
		<key>Headers/TKGesturePasswordButton.h</key>
		<dict>
			<key>hash</key>
			<data>
			shEFpLy9PkuSU00CjfxZazqI3u8=
			</data>
			<key>hash2</key>
			<data>
			Uwlv5KOwoOqRq1AhJhUYX5RMUgz9h1wbbt9kpdZG7mM=
			</data>
		</dict>
		<key>Headers/TKGesturePasswordButtonDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			84ThEdPru9Srkxi7jlq14w7lE04=
			</data>
			<key>hash2</key>
			<data>
			cwOVlPBZsIwCcvDzzR7xapHvTH9AUoIk4MQ+JYJOwfA=
			</data>
		</dict>
		<key>Headers/TKGesturePasswordController.h</key>
		<dict>
			<key>hash</key>
			<data>
			hMDYlE6TFodPecrlWava1Cicwes=
			</data>
			<key>hash2</key>
			<data>
			r1+0r7ky+RkLdbnr2GjslcNtHxECvzdfZP+RatH5lmc=
			</data>
		</dict>
		<key>Headers/TKGesturePasswordTouchDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			96l4EgfNSB4lk1T3iT1rctwL0og=
			</data>
			<key>hash2</key>
			<data>
			esu1l9So9u6UY2up0hSjqcR+jtaWtkIi3H097Oaos3s=
			</data>
		</dict>
		<key>Headers/TKGesturePasswordView.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZNwG1O+33yuAjK8LYCAvLMgx8Ds=
			</data>
			<key>hash2</key>
			<data>
			mIe27hURHCkBPAg4+DTy4ptgeQgb5Wyql4JLv32h1WI=
			</data>
		</dict>
		<key>Headers/TKH5FilterManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			043zpRc+X20WV1ofk+p1LBEMVmA=
			</data>
			<key>hash2</key>
			<data>
			b/7RFhTbQ9h/O1Ty26jly4Yuwjn3vSU1GbIR/01RCo4=
			</data>
		</dict>
		<key>Headers/TKH5KeyBoard.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzMyb64Vu5vJevXZ/phpNxmQtvc=
			</data>
			<key>hash2</key>
			<data>
			/tHKaNxmobecOCGIH0OZDyEFppMCmR54mn5LgHYZsCg=
			</data>
		</dict>
		<key>Headers/TKH5ThirdZFManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zu0oLCT+wapHQ1OC5jatBvEUP90=
			</data>
			<key>hash2</key>
			<data>
			eQ7d2h9fYNymg5GqcnmkItmtoTDpfvuF3xYAcIOdohw=
			</data>
		</dict>
		<key>Headers/TKHexHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			20BVg/lAEoRVn7a+29M2QyEPo6U=
			</data>
			<key>hash2</key>
			<data>
			33ar5T99p7ruUhBAi1wTFAWkzhHcV1RxnBNw/cN/x3U=
			</data>
		</dict>
		<key>Headers/TKHttpAddress.h</key>
		<dict>
			<key>hash</key>
			<data>
			PUEs6RZ9CFfvAMk/zgGG3Nd+jnE=
			</data>
			<key>hash2</key>
			<data>
			G9RljEq9q3UXwnksir6mK8rQGqwlZniQEi/zhwtV248=
			</data>
		</dict>
		<key>Headers/TKHttpDao.h</key>
		<dict>
			<key>hash</key>
			<data>
			cxOecogu2bNGJuKPtnjqkGXGRlo=
			</data>
			<key>hash2</key>
			<data>
			5q8s/GlpN6nhg5qzzZXmnUcbGczAyQHQ61TcmvrDBOs=
			</data>
		</dict>
		<key>Headers/TKHttpRoom.h</key>
		<dict>
			<key>hash</key>
			<data>
			vJmvpFFrzZw91kBUURF+pB97pPo=
			</data>
			<key>hash2</key>
			<data>
			8EKPa0wPabe3g2l8IRWvehzcDDTIw1eb/1hm75u5VNA=
			</data>
		</dict>
		<key>Headers/TKHttpRoomConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			xDGi2Bhy65g2OAPOZwnQHH3j7ro=
			</data>
			<key>hash2</key>
			<data>
			4WWSAlZK3fKGteeAfL6o4dw5Vx341lobBtO47ZU2Qhw=
			</data>
		</dict>
		<key>Headers/TKHttpRoomServerManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			/O2IkR2Z3iqW4CDQtNGYQYBK3BA=
			</data>
			<key>hash2</key>
			<data>
			YdjPXnDS+09WXa1FJ8oVNz0qb1kdRBSlB/YJBadkycs=
			</data>
		</dict>
		<key>Headers/TKHttpServer.h</key>
		<dict>
			<key>hash</key>
			<data>
			j2K3J461Wn+ejqyKga/DMgrfrTM=
			</data>
			<key>hash2</key>
			<data>
			BqDF2NrSrQ7s9qF1xqQsA75SFp4n+9OKw2p+hYVBrAA=
			</data>
		</dict>
		<key>Headers/TKHttpSpeedChecker.h</key>
		<dict>
			<key>hash</key>
			<data>
			HSj+iWGKaE0qRw7ht/Ay0yQtMYc=
			</data>
			<key>hash2</key>
			<data>
			CQVIkU8MRAQpFuavJyulLEKssF72dht9ZIqxSA0JTJw=
			</data>
		</dict>
		<key>Headers/TKIOSCallJSFilterDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			KIG/rV+qb9SGAkhMNMM+GpjhdF4=
			</data>
			<key>hash2</key>
			<data>
			AygqXInn6agep6gjVkF6GTNT6giAworxGiqKSi219/Y=
			</data>
		</dict>
		<key>Headers/TKImageClipEditorController.h</key>
		<dict>
			<key>hash</key>
			<data>
			b7Qf33Eb6aewLGIwravqMoZY57I=
			</data>
			<key>hash2</key>
			<data>
			P5CxkmYCeEsR2znU00Fk70otY9gbLbqrXDJTyY3GBDY=
			</data>
		</dict>
		<key>Headers/TKImageCropperManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			lPJepbb/rmeigZ1l3glKPJ4YfIg=
			</data>
			<key>hash2</key>
			<data>
			Ys4h/pu6bmFYK5YOe7xFYhHItoDVpVfd2cGFdp0dPUk=
			</data>
		</dict>
		<key>Headers/TKImageFooterView.h</key>
		<dict>
			<key>hash</key>
			<data>
			brLOv1aUJkbYXs9NLIoh7qT2CvM=
			</data>
			<key>hash2</key>
			<data>
			g1H4jssaGxw8+20i8kQbia+zyN8duYx/tWzCBkX9pNI=
			</data>
		</dict>
		<key>Headers/TKImageHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			KoXwiDOCPO5xahWwuzPibEL/cVQ=
			</data>
			<key>hash2</key>
			<data>
			6X7W0m6PHB4geEuAef94yOyCazT2wb/raaTNlnXOU9Q=
			</data>
		</dict>
		<key>Headers/TKImageLayerUtil.h</key>
		<dict>
			<key>hash</key>
			<data>
			I6ChxDDUD1jV6Zhof2JtINDrmUw=
			</data>
			<key>hash2</key>
			<data>
			tPKARfrtvGtRZdE1ltzlPz5ojyUSxxPOp6qe2U+7lfg=
			</data>
		</dict>
		<key>Headers/TKImagePicker.h</key>
		<dict>
			<key>hash</key>
			<data>
			g/LKD8tUGyLOg+QyBRGVzIJfWFo=
			</data>
			<key>hash2</key>
			<data>
			zP1u/ec4r6Eo3XaAEJ6y33yKgGOGniTTGqKm8/5abIQ=
			</data>
		</dict>
		<key>Headers/TKImagePickerDefinition.h</key>
		<dict>
			<key>hash</key>
			<data>
			TJ7Cj/Vp3O/Qzqs2jGKs60AQ7WY=
			</data>
			<key>hash2</key>
			<data>
			EKUFncKDeHL3Jok8UbymUZhrEShkbR8nj05jrq9P8k0=
			</data>
		</dict>
		<key>Headers/TKImagePickerSet.h</key>
		<dict>
			<key>hash</key>
			<data>
			/Redq27EbfVSeZnZK43Hw69sjw4=
			</data>
			<key>hash2</key>
			<data>
			ZFYG6XoKcFIL/nAMdSDaQ8XRu4YtmAPYwqclI1AnLnc=
			</data>
		</dict>
		<key>Headers/TKImagePickerViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			fUpQkuc7i/1wDMaOuUI8W4ErHio=
			</data>
			<key>hash2</key>
			<data>
			0e9oUNFyqIsimA71VQmIF3fkVIGLk876Xzt9Dixuaxg=
			</data>
		</dict>
		<key>Headers/TKImageSelectCell.h</key>
		<dict>
			<key>hash</key>
			<data>
			VfAWG0CM+zEq+BkKRAw7xkUv444=
			</data>
			<key>hash2</key>
			<data>
			U1/aC+hJVT+Hoh84cXhcl7tSSrySerkDr8hql8yF7kc=
			</data>
		</dict>
		<key>Headers/TKImageSelectView.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ng9FZeXxCRXU6EeP0S5pyOOYQh8=
			</data>
			<key>hash2</key>
			<data>
			806mHENJJ/x0mllF+yr2BPUn1hIuvx+Sn3KtscYjdys=
			</data>
		</dict>
		<key>Headers/TKImageSelectViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			UO2QO3Io1PRLL6A/XJAvHQQqb50=
			</data>
			<key>hash2</key>
			<data>
			BTGmMe16XbLXyw0ZtsQTW6hyPxk9/ZEUXT3rxt6tkT8=
			</data>
		</dict>
		<key>Headers/TKInvokeServerManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			zjSzO0I/vBvUahmOz2tX36ovhHc=
			</data>
			<key>hash2</key>
			<data>
			qlfW+vjLDN/P7yhnfLj6PhNtoRPFDE5P/kXbxgXD8mo=
			</data>
		</dict>
		<key>Headers/TKJDStatusBarNotification.h</key>
		<dict>
			<key>hash</key>
			<data>
			za8vdTRWEm1HvGTPU257uZFKCCY=
			</data>
			<key>hash2</key>
			<data>
			GZ4o/E/YBxG/UVnWs50rdxvxCE5yqdJwzT8xdj00SHU=
			</data>
		</dict>
		<key>Headers/TKJDStatusBarStyle.h</key>
		<dict>
			<key>hash</key>
			<data>
			W0xlyvjmC9yxrvouZtsTPszWicI=
			</data>
			<key>hash2</key>
			<data>
			ksX1BfWHp/EbTzl60LD7JM4bBz5Yvx7xKmF1SVcVNaY=
			</data>
		</dict>
		<key>Headers/TKJDStatusBarView.h</key>
		<dict>
			<key>hash</key>
			<data>
			+3XyDxLrIqFixvyk5gkPYbSGhU8=
			</data>
			<key>hash2</key>
			<data>
			236CyNXRmTtPSSNMPHT2nini/IWrjIEmReY14Iu2/Bo=
			</data>
		</dict>
		<key>Headers/TKJSCallBackManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			qCwleUAHgFXLBmK04suLM66ww8E=
			</data>
			<key>hash2</key>
			<data>
			JxqDzdsVxrDKN5tHJFGWAG52R9ck0SEFh4m/um3JA0E=
			</data>
		</dict>
		<key>Headers/TKJavascriptDao.h</key>
		<dict>
			<key>hash</key>
			<data>
			9fyVLdFRAwDBt34uDUdKs+oZswU=
			</data>
			<key>hash2</key>
			<data>
			BV9dhieNCCpwbLNhA7JmaEhM+7j3rqc4CgMZycB31po=
			</data>
		</dict>
		<key>Headers/TKKeyBoard.h</key>
		<dict>
			<key>hash</key>
			<data>
			0BK8LZCGrcGHnXr10Lb1GNMMjLc=
			</data>
			<key>hash2</key>
			<data>
			vNxkBnJwBPsrEXzDQkS8/ePCVTfskEx0ySknqs2GNn8=
			</data>
		</dict>
		<key>Headers/TKKeyBoardBoxVo.h</key>
		<dict>
			<key>hash</key>
			<data>
			jeD+ejdFiBt8K605bJQstgC2cmg=
			</data>
			<key>hash2</key>
			<data>
			AkDNsN2oh793FbJP55KFe+B7vMTk6M9ZjbOppX5mHD8=
			</data>
		</dict>
		<key>Headers/TKKeyBoardEventDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			PhJ+LxLeML/PUoBx95UepfjMGnA=
			</data>
			<key>hash2</key>
			<data>
			gMDDUqAkN+ncC6Mxbq2HkwLueDQ+HqK8U37lgyM4DIs=
			</data>
		</dict>
		<key>Headers/TKKeyBoardInputDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			3t71A/yVvewG4GJxw9YgR9aBaHw=
			</data>
			<key>hash2</key>
			<data>
			8ipzQgVKIsbLEraEMMB7zzvK8IsraBjhu74VV/VHi7c=
			</data>
		</dict>
		<key>Headers/TKKeyBoardInputItemView.h</key>
		<dict>
			<key>hash</key>
			<data>
			JBvGNoDmMrl58yLa1mSijn556zA=
			</data>
			<key>hash2</key>
			<data>
			Cd7Wn8yQFTzErR9Ze0YN1395Rm2GC6ntmqSecvCReIs=
			</data>
		</dict>
		<key>Headers/TKKeyBoardItemView.h</key>
		<dict>
			<key>hash</key>
			<data>
			iNg9TsAuQyKWrqBSQy024sVkKdk=
			</data>
			<key>hash2</key>
			<data>
			q1mLvLH8JSoYy5zT6VEgne9H4/U5OhI9kKU6dIWfIQo=
			</data>
		</dict>
		<key>Headers/TKKeyBoardItemVo.h</key>
		<dict>
			<key>hash</key>
			<data>
			bxGeYfYHXr+v+1BRVF9eMlRMvQs=
			</data>
			<key>hash2</key>
			<data>
			a8iMj8wrpwgZCR6rcQRDTIx5wrteMWZTLDsUR9zuER0=
			</data>
		</dict>
		<key>Headers/TKKeyBoardViewManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			DSt6RzAtyi7foi7dAD8NtRwJY+Y=
			</data>
			<key>hash2</key>
			<data>
			c8yjdaMhsxzjItnLz1slc+Q5fzpVDuqUCRfJFfxu9G0=
			</data>
		</dict>
		<key>Headers/TKKeyBoardVo.h</key>
		<dict>
			<key>hash</key>
			<data>
			EdnAD/hnx13Gz+RrmVJ9WaKhfcw=
			</data>
			<key>hash2</key>
			<data>
			3T9NCJBWxx+c6//W/RajHEQQjYWU7j8zAB/GPuoAsws=
			</data>
		</dict>
		<key>Headers/TKKeyBoardVoManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			91UHonDP/KO/CyLdDcHBTN4exHs=
			</data>
			<key>hash2</key>
			<data>
			TX3Mv/okvPkaWbiUNCQEkOeI6eD6aLlV+cGvaFCVwfA=
			</data>
		</dict>
		<key>Headers/TKLayerView.h</key>
		<dict>
			<key>hash</key>
			<data>
			OvIImZa0B8E63ea2lyPWj+gjWSE=
			</data>
			<key>hash2</key>
			<data>
			jfTQzjcJ/5oSuse5WEQpUFCfga1VyVOgahG+INLMXU4=
			</data>
		</dict>
		<key>Headers/TKLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			B0qwOyK/1+hYr4KrNhi26mWrUW4=
			</data>
			<key>hash2</key>
			<data>
			wQ5DsAR7lygvS2w7zBsLgfd8pjuUP+vHcDcQnm6dsws=
			</data>
		</dict>
		<key>Headers/TKLittleTentacleView.h</key>
		<dict>
			<key>hash</key>
			<data>
			va/QrOHx0QpIkSNi/jl+zRwkJQg=
			</data>
			<key>hash2</key>
			<data>
			5DwFdyHo/pvqLQHTqYn9vRMMWtkKy1/zJ0byhSNH2FY=
			</data>
		</dict>
		<key>Headers/TKLocalNotificationHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			BdbZk1akFKljIdH+PvYOkn8mVBo=
			</data>
			<key>hash2</key>
			<data>
			4amKmEK9CaVMCsnidcYEIyZSBX/asrsAL2CRA+H15XQ=
			</data>
		</dict>
		<key>Headers/TKLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			/HV7Ejo64j7kttIFpiyagT1AB8c=
			</data>
			<key>hash2</key>
			<data>
			Nbrz6Yq0VXOHbaUkJXBkeYKmIMVOUeKFRyd6CMAxi3U=
			</data>
		</dict>
		<key>Headers/TKLogFormatterDefault.h</key>
		<dict>
			<key>hash</key>
			<data>
			b10Vb7Im+lG9pdqANkd4EdfhPgA=
			</data>
			<key>hash2</key>
			<data>
			5qXEoROJH23SN9tzQCtcWkOjzU1c9/A7CeqlIUlMU6A=
			</data>
		</dict>
		<key>Headers/TKLogFormatterDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			wkb6VAYhMPLuVHMqr2hoWxsYEEY=
			</data>
			<key>hash2</key>
			<data>
			akkZYsWr63KR2y5FfGC+/gxndhDjxlQIAXb+X2L5zV8=
			</data>
		</dict>
		<key>Headers/TKLogManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			A+ClyRcmTXzqCZge0iVNVUIa4q8=
			</data>
			<key>hash2</key>
			<data>
			X/vD8PwfpS34R9AaXT1+9/R0E3m3rNTuF/mbGiTBnUc=
			</data>
		</dict>
		<key>Headers/TKLogMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			KerhMcRwwA1XZ8AfHl3Scq9SN8U=
			</data>
			<key>hash2</key>
			<data>
			l1NOv0hZrHwhw25b/07v77G009znk0amPGoYfCcxxiw=
			</data>
		</dict>
		<key>Headers/TKLoggerDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			R55SxSg+pkBGbRpzqRRtQsIfYZA=
			</data>
			<key>hash2</key>
			<data>
			tdJ2uJQ0no+44zpPqPbfcZbqzEEn+guFZGEHlvj9Kmc=
			</data>
		</dict>
		<key>Headers/TKLoggerNode.h</key>
		<dict>
			<key>hash</key>
			<data>
			7BaXBGy5HQLZQ+XShmfy1sEthIo=
			</data>
			<key>hash2</key>
			<data>
			IJoiEHBoqNau6/zYnUDnZmoUCnq2Clu+h5RJlNTFkuM=
			</data>
		</dict>
		<key>Headers/TKMBProgressHUD.h</key>
		<dict>
			<key>hash</key>
			<data>
			/AA501KnyEIYEr1/EK/8RKnAQOw=
			</data>
			<key>hash2</key>
			<data>
			qMjjN52KmcElkDgDKnS9PUX6UNx3AuW8b4SVSpfT2AY=
			</data>
		</dict>
		<key>Headers/TKMd5Helper.h</key>
		<dict>
			<key>hash</key>
			<data>
			zCom3vrtOo4hWoAToef7TjM3SB4=
			</data>
			<key>hash2</key>
			<data>
			lPmCtTUP2s+dez2tULU0ew7p9p9ual0aHYS2PlAPp+o=
			</data>
		</dict>
		<key>Headers/TKMesageDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			L0ajQbWNXpAJTMBcq69m/ORtOTE=
			</data>
			<key>hash2</key>
			<data>
			3XyPlEng05nE5we8KnbtLXal4xCE6ngggI6WYtsbp/o=
			</data>
		</dict>
		<key>Headers/TKMessageCenter.h</key>
		<dict>
			<key>hash</key>
			<data>
			7ApYP8C6fLnTCzESxgubASfC4To=
			</data>
			<key>hash2</key>
			<data>
			B6V9S8OAkFwtcCANPZtNsBTydayPsMdi8llFjdOwqy0=
			</data>
		</dict>
		<key>Headers/TKModuleDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			hB6X9RdFEtswL32oMmBCLt+Ksbs=
			</data>
			<key>hash2</key>
			<data>
			SOCnt7iDEFhjACzwR5b0pcDp+NodgKsCmkBaNB9+wZo=
			</data>
		</dict>
		<key>Headers/TKModuleEngine.h</key>
		<dict>
			<key>hash</key>
			<data>
			bxhgH2aOt+TBHYimR854AKe4880=
			</data>
			<key>hash2</key>
			<data>
			BEzX6gK7UuKRwkbuWfGwu85QgA4m14VfIAPVoluKisE=
			</data>
		</dict>
		<key>Headers/TKModuleMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZpdA/U2t8Yx60oaKx4jSJb0sQdU=
			</data>
			<key>hash2</key>
			<data>
			cVjXn6rgT3ZJPHOXOE5Lbunl4VjYED7ySBDTdf/y3i8=
			</data>
		</dict>
		<key>Headers/TKMovieCropperManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			ipBbeAAEP6IKjgQdh8jq13l/k4k=
			</data>
			<key>hash2</key>
			<data>
			DoS7vxIz+g4F1/1IJMEAOUnS7nh/oo8M2J8L78RcdfE=
			</data>
		</dict>
		<key>Headers/TKMoviePlayerViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			9pUr/C9rPRJyJD8ha1W6YtHfQ0Q=
			</data>
			<key>hash2</key>
			<data>
			N5wpespSHJ6ZNvap+w9a47YRvel3CISxgXeKEJswnjU=
			</data>
		</dict>
		<key>Headers/TKMulBrowserCell.h</key>
		<dict>
			<key>hash</key>
			<data>
			wjWrusjRR3Cpkph4vXxYTxG1sqQ=
			</data>
			<key>hash2</key>
			<data>
			FPwbuS4Z3nKR7N2GWJ8S9hEtzPSPZtEkvbxiOhr07T8=
			</data>
		</dict>
		<key>Headers/TKMulImageBrowserView.h</key>
		<dict>
			<key>hash</key>
			<data>
			u9pbOjzZ+MTAFNJsfBw5ZB1yu0s=
			</data>
			<key>hash2</key>
			<data>
			q4ZtxXJjqcXiS5QkeAs+038Mx2T9BG/Soopi2WWzPFc=
			</data>
		</dict>
		<key>Headers/TKMulImageBrowserViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			CpmFjFXD1jsdv7zUd21ph2UlDyc=
			</data>
			<key>hash2</key>
			<data>
			Nuupp1fOcS9SGa3ww8F1rl/vgwReKPR2F7wTshAp5JE=
			</data>
		</dict>
		<key>Headers/TKMulImagePickerManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			RVjgTyIge043g/rf/Bx8PxU4vrI=
			</data>
			<key>hash2</key>
			<data>
			3HlLQEtaNFzYQEi+pvXiLpMbUHqP9CRIOh1LB8P6HEY=
			</data>
		</dict>
		<key>Headers/TKNIActions+Subclassing.h</key>
		<dict>
			<key>hash</key>
			<data>
			h6tnINd9MTiv2n4+ssNqjf39W6k=
			</data>
			<key>hash2</key>
			<data>
			5yW8gGosvxg/SAVtqSE0NQiIuqw+qZeSQKPfb0ODAoQ=
			</data>
		</dict>
		<key>Headers/TKNIActions.h</key>
		<dict>
			<key>hash</key>
			<data>
			LJJDkXmElirlyB1C6kGSzLmYvmA=
			</data>
			<key>hash2</key>
			<data>
			78ThVg79Kqy8UeUBvaTXv+IFoleyPLT2p0yFvdCPK+0=
			</data>
		</dict>
		<key>Headers/TKNIButtonUtilities.h</key>
		<dict>
			<key>hash</key>
			<data>
			f+7UaOHalj7pybNSZTYGTjgKjdw=
			</data>
			<key>hash2</key>
			<data>
			jIyuMQP4gm60hZpmXGCJ/hMA5BH9b892ip8wgWo5r5w=
			</data>
		</dict>
		<key>Headers/TKNICSSParser.h</key>
		<dict>
			<key>hash</key>
			<data>
			hXw0GPvaukgTt3mD1/hI1Ha8EpA=
			</data>
			<key>hash2</key>
			<data>
			Xp2gNG8zm0Uii0GbkkzVb/oVuS1nqh7XHkl2/33mxnY=
			</data>
		</dict>
		<key>Headers/TKNICSSRuleset.h</key>
		<dict>
			<key>hash</key>
			<data>
			9/bSpJvpBxEtq/i71kGaj71KFYs=
			</data>
			<key>hash2</key>
			<data>
			xTUgU6SmpC1g7CgTUAO0LeiuJthD8LCiFyjYxS1D2Sw=
			</data>
		</dict>
		<key>Headers/TKNICommonMetrics.h</key>
		<dict>
			<key>hash</key>
			<data>
			GmmksuqyMzm0ZlTZZGbJ8q7Ix5w=
			</data>
			<key>hash2</key>
			<data>
			s+RBz2wnkS14qFwMZOL0AbCjbXo3POty5jwYzHm2ERs=
			</data>
		</dict>
		<key>Headers/TKNIDOM.h</key>
		<dict>
			<key>hash</key>
			<data>
			0nw8hg/5L53uu41Q84J+QqHRFT8=
			</data>
			<key>hash2</key>
			<data>
			RUnHvLqEppwhyW/pudIh0xVEzlcoabIyir0bLEFddOQ=
			</data>
		</dict>
		<key>Headers/TKNIDataStructures.h</key>
		<dict>
			<key>hash</key>
			<data>
			3lHhNRKulRreEgSbBElBJZUHPKc=
			</data>
			<key>hash2</key>
			<data>
			LdTs1i0Fq6mHAlOO9e6kfoblYCcNZxjQ2PTJ19meEDo=
			</data>
		</dict>
		<key>Headers/TKNIDebuggingTools.h</key>
		<dict>
			<key>hash</key>
			<data>
			n1n2GVk+yr9luukVDoHKEeYZ4Cc=
			</data>
			<key>hash2</key>
			<data>
			vjGzZvpWUJEOtzfG1CgJITIrz6L2rRqrvEMTD3Jv5lY=
			</data>
		</dict>
		<key>Headers/TKNIDeviceOrientation.h</key>
		<dict>
			<key>hash</key>
			<data>
			L2SOqxP4XfGMiP2xJpvSJPvF66A=
			</data>
			<key>hash2</key>
			<data>
			8F7j6/w91XQCj+PukNt3lHtYTvfzGAjJnvafQzRBx5U=
			</data>
		</dict>
		<key>Headers/TKNIError.h</key>
		<dict>
			<key>hash</key>
			<data>
			x4grlKy2wZGQVDR8I9JBZVsAxx4=
			</data>
			<key>hash2</key>
			<data>
			dfNQP1xkrffE4qye4TFpC7IFxqFCnnX+USfCeEpTe/I=
			</data>
		</dict>
		<key>Headers/TKNIFoundationMethods.h</key>
		<dict>
			<key>hash</key>
			<data>
			nNolC8h8dVeOrouk4IYm2CcyKQE=
			</data>
			<key>hash2</key>
			<data>
			1EAL8jqovkcFTyTho9VFFqV2q/U3vvwoNAFDY27ZJuc=
			</data>
		</dict>
		<key>Headers/TKNIImageUtilities.h</key>
		<dict>
			<key>hash</key>
			<data>
			uruSYbYPtM/4LpVN5g6wx/V3nZk=
			</data>
			<key>hash2</key>
			<data>
			RMDOX6f/Ut+GmOirbJWTbu2805Mhp8caf1g5SV2mhDo=
			</data>
		</dict>
		<key>Headers/TKNIInMemoryCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZuuT0OnAOb4il8zbw7X46LMimTs=
			</data>
			<key>hash2</key>
			<data>
			UuLrSVOP1RdWICNgRf8wbDOh8Wt8lC2b4Op075cYQEs=
			</data>
		</dict>
		<key>Headers/TKNINavigationAppearance.h</key>
		<dict>
			<key>hash</key>
			<data>
			8/Ij5x4+/EtcilnarNC9SGAvBs0=
			</data>
			<key>hash2</key>
			<data>
			vKGidIIi6w6kdWri9vWR2mEHocxV+AFND7V+qx66JZg=
			</data>
		</dict>
		<key>Headers/TKNINetworkActivity.h</key>
		<dict>
			<key>hash</key>
			<data>
			h5KOJzVX7DCDy8O/6rwVfxePvqM=
			</data>
			<key>hash2</key>
			<data>
			lOoGtnLY1lw47FdRZQ8MoH6S/9weDsVcHm85kAeQtH4=
			</data>
		</dict>
		<key>Headers/TKNINonEmptyCollectionTesting.h</key>
		<dict>
			<key>hash</key>
			<data>
			wiC4jRp/leZYotSWxcE60TJFCYM=
			</data>
			<key>hash2</key>
			<data>
			Av7ZWIR9vv5OW4mzkLu09LVT/jVuBgZWxERuPdOE6t4=
			</data>
		</dict>
		<key>Headers/TKNINonRetainingCollections.h</key>
		<dict>
			<key>hash</key>
			<data>
			zf7MbvLzRmp6Wpy0qlx8yGLeGe4=
			</data>
			<key>hash2</key>
			<data>
			97fa2C7mmL6xBZtOS/bsprZU4LvVpwIDwB7dNK2CEZI=
			</data>
		</dict>
		<key>Headers/TKNIOperations+Subclassing.h</key>
		<dict>
			<key>hash</key>
			<data>
			m3gBAplL7kQZfHPkGHLjIMVXopk=
			</data>
			<key>hash2</key>
			<data>
			SeB521z+5q6zIl02Xdf4Ypv84wzMaPO1b6Cv6cye6oA=
			</data>
		</dict>
		<key>Headers/TKNIOperations.h</key>
		<dict>
			<key>hash</key>
			<data>
			2PCcmnVU6zJ4YUK4k2kCD8OJ26s=
			</data>
			<key>hash2</key>
			<data>
			chZnQAds+BDe8A/EMJx+GB8EP5Za9NRHiMSXI00l3GU=
			</data>
		</dict>
		<key>Headers/TKNIPaths.h</key>
		<dict>
			<key>hash</key>
			<data>
			bGvGttUrFVc57E2mltX7S2LRcH8=
			</data>
			<key>hash2</key>
			<data>
			YNc+ySC6m1HRs0G5G7mzILovpDWfW+CxYayLwGopdvo=
			</data>
		</dict>
		<key>Headers/TKNIPreprocessorMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			P2BYVqqoy+MkH+jA00agVtwC434=
			</data>
			<key>hash2</key>
			<data>
			vAehVEF1gbbQr6B1mt1CYr78JYX4fyaNBX8b8erswd0=
			</data>
		</dict>
		<key>Headers/TKNIRuntimeClassModifications.h</key>
		<dict>
			<key>hash</key>
			<data>
			XDenLOjdZD2XlaqyhEggMeKPQf0=
			</data>
			<key>hash2</key>
			<data>
			QYHkRzK3fy4Pjf4T6gGY+6dkV5dR+Jq2ytMr84A10P8=
			</data>
		</dict>
		<key>Headers/TKNISDKAvailability.h</key>
		<dict>
			<key>hash</key>
			<data>
			JLqTnI+qwb63bFk9NEC4cfJ3G1Q=
			</data>
			<key>hash2</key>
			<data>
			7W3XACt9ADNV9GwXtypLFMApYs37SvoILqCscQQNImg=
			</data>
		</dict>
		<key>Headers/TKNISnapshotRotation.h</key>
		<dict>
			<key>hash</key>
			<data>
			QKa39dXNCpd6Lo2AHyb248M6wgU=
			</data>
			<key>hash2</key>
			<data>
			spFP3+yIqb0YtyseDOX9na7cnlHL9UJvR0JOA9SFdmw=
			</data>
		</dict>
		<key>Headers/TKNIState.h</key>
		<dict>
			<key>hash</key>
			<data>
			S4T7gUxfmuWeTBvWyR/y/dv7P4c=
			</data>
			<key>hash2</key>
			<data>
			WchMMevp19S3Mi/vI1xbrFAxfSU6dea28XjCz8hj7k4=
			</data>
		</dict>
		<key>Headers/TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			U+IowdIFJntLJ2Yb/kCcTLIbdQc=
			</data>
			<key>hash2</key>
			<data>
			8D1ocjshSne4jMGnTxpHxq5Ob3e72gmvOLpRB514q/w=
			</data>
		</dict>
		<key>Headers/TKNIStylesheet.h</key>
		<dict>
			<key>hash</key>
			<data>
			5kPA073vJ2Qy1IX7yCYxUzyI4YU=
			</data>
			<key>hash2</key>
			<data>
			Ye0b6vubwvl25oDx7JWGMlVYuczeAKM6of5tqgG9c1o=
			</data>
		</dict>
		<key>Headers/TKNIStylesheetCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			zvK5ozUNjTxi4Cs3n0iBpbxiJuE=
			</data>
			<key>hash2</key>
			<data>
			BsdWPIzL+Xzevy9tTGCvxvYXkhklj/AibxIaHtazz8c=
			</data>
		</dict>
		<key>Headers/TKNITextField+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			/liiMGYt0H5NS0z+jR5YC94HMqY=
			</data>
			<key>hash2</key>
			<data>
			mjyYuVdT2bg2dgu/68cNJWuuUGLU8nMGPu43d4ATUTM=
			</data>
		</dict>
		<key>Headers/TKNITextField.h</key>
		<dict>
			<key>hash</key>
			<data>
			SyhQhDMzBlyi0GizPck5YTOSGbQ=
			</data>
			<key>hash2</key>
			<data>
			AFblfcppgYxkt4mopGhFYn517GegpIALws0HNIwBLTE=
			</data>
		</dict>
		<key>Headers/TKNIUserInterfaceString.h</key>
		<dict>
			<key>hash</key>
			<data>
			F+oBJcBe6rr24fr+JWoHJ321Xjg=
			</data>
			<key>hash2</key>
			<data>
			wNxh8F5aJ/6ANVp9O5TRB2+eDc+spi8JNuu/+bgVi2U=
			</data>
		</dict>
		<key>Headers/TKNIViewRecycler.h</key>
		<dict>
			<key>hash</key>
			<data>
			XdbAEVP1z7amLTQ6d1+H20EIJfw=
			</data>
			<key>hash2</key>
			<data>
			hFbHc8m1tJQutXx3V738Iq/E0OBEfG7PRyvRTkNZNkw=
			</data>
		</dict>
		<key>Headers/TKNavBar+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			HkCfp7Mrl2VygPPRQvt/LbrQrHs=
			</data>
			<key>hash2</key>
			<data>
			Ke1Ib0nUkOSHKt/vSrGDbmMQS8ASpCyn7Trk1awnTJ4=
			</data>
		</dict>
		<key>Headers/TKNavBar.h</key>
		<dict>
			<key>hash</key>
			<data>
			hc601UXFZ1Yd8P6v/11e2IABlZg=
			</data>
			<key>hash2</key>
			<data>
			dcXtiJm8C6IZsSx3BZj3xwIk/GWcmXJ1+6AzA3zoxc4=
			</data>
		</dict>
		<key>Headers/TKNetAddress.h</key>
		<dict>
			<key>hash</key>
			<data>
			4a2NpdXkzEwhYOLIndHN/Oc5pXM=
			</data>
			<key>hash2</key>
			<data>
			HwVzHCyK+cuhnzzlwkXIlm6IgWrZCF0iLwgdkU7B93M=
			</data>
		</dict>
		<key>Headers/TKNetHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			5EUod2BR8A8nD3L1uIZs/D0s+a8=
			</data>
			<key>hash2</key>
			<data>
			ie0iLNcnnkIxHRh3uhsPppZkcxcmWCZCPdv2XE5Hvxs=
			</data>
		</dict>
		<key>Headers/TKNetworkInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			eDk7Mb+cxf3ptG+g3modXPHq/30=
			</data>
			<key>hash2</key>
			<data>
			pKlpxMb5SFTt90sYivxr2cF+bAKsnPNnjSi1PCzNVfE=
			</data>
		</dict>
		<key>Headers/TKNetworkManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			t/oGUHBN0gTrE/h82O+XVy1gLTA=
			</data>
			<key>hash2</key>
			<data>
			6WhY9TONZtpbcWUM4kTPb/iIgwhMw5dHiSLKJSyIcCA=
			</data>
		</dict>
		<key>Headers/TKNimbusCSS.h</key>
		<dict>
			<key>hash</key>
			<data>
			enK3L/+3rUL+4fkQqleNQJLmYXY=
			</data>
			<key>hash2</key>
			<data>
			3L+SuZ1G0pgCK/unUMkj8Cl7OIE+Y8i/N6riFRQEXQk=
			</data>
		</dict>
		<key>Headers/TKNimbusCore+Additions.h</key>
		<dict>
			<key>hash</key>
			<data>
			XLdxlDNkOgvBLjv2AGnPfe/NrXI=
			</data>
			<key>hash2</key>
			<data>
			RB76otz3l9EvEyzzCPEqbf9S17lKwkkFIrj0I+5Nk30=
			</data>
		</dict>
		<key>Headers/TKNimbusCore.h</key>
		<dict>
			<key>hash</key>
			<data>
			0gAOqwamskzqc+PvujM0UKOdimU=
			</data>
			<key>hash2</key>
			<data>
			gv9xqVvznXmGW38E+VA+UEMZiK5ggVZYE45qlq+Cp5g=
			</data>
		</dict>
		<key>Headers/TKNumberHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			pWVYz0P7aRpq5kuj4O7SXfr4380=
			</data>
			<key>hash2</key>
			<data>
			jdm9oxxbAlpb7xV9zE4MrV/9yIkX+4JE5EkSkOBE7tU=
			</data>
		</dict>
		<key>Headers/TKOpenUDID.h</key>
		<dict>
			<key>hash</key>
			<data>
			9Dz8AmEeS+foHsllMxtIZCcUlsY=
			</data>
			<key>hash2</key>
			<data>
			JgcKohuu+xONezFiINJGSXRw5ojcpIz9T8sjXYENrpY=
			</data>
		</dict>
		<key>Headers/TKOperatorCaculateFraction.h</key>
		<dict>
			<key>hash</key>
			<data>
			ONEUgYzGuicl2z5zbLI3rYo9huE=
			</data>
			<key>hash2</key>
			<data>
			54SRa/T1ZOuVpr6ALRqemzWxk76isv8e3BcmSJUzXnk=
			</data>
		</dict>
		<key>Headers/TKOperatorExpressionHandle.h</key>
		<dict>
			<key>hash</key>
			<data>
			28lAMky1oSYwI+OmsHE5qHFEBBw=
			</data>
			<key>hash2</key>
			<data>
			S8Frr09Fc5ErxuDPMRMM2P6s7HaAhuJ78Koev3jv6M0=
			</data>
		</dict>
		<key>Headers/TKOptionKeyBoardView.h</key>
		<dict>
			<key>hash</key>
			<data>
			OZ7wJ6Ja+yRsVYorbwAp/NKKAQ8=
			</data>
			<key>hash2</key>
			<data>
			KLQzkcMwoji9iNSUYsMYMAh+Zj5wVMta13HCESAeUGo=
			</data>
		</dict>
		<key>Headers/TKPasswordGenerator.h</key>
		<dict>
			<key>hash</key>
			<data>
			cZBgqlo3f0Z1JepczQGMQw81/P4=
			</data>
			<key>hash2</key>
			<data>
			46wlMGYVxMJLy+aqFdQOn9WIPu0px2Ok0ZOqfc5/fTc=
			</data>
		</dict>
		<key>Headers/TKPdfViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			L66vtvNBNrz+tceZ+VG90Ap+lYY=
			</data>
			<key>hash2</key>
			<data>
			YTyBHHXecZ9wxP1beYE6B+LBsdmjOYMMmmJ6TWvabCQ=
			</data>
		</dict>
		<key>Headers/TKPhotoModel.h</key>
		<dict>
			<key>hash</key>
			<data>
			4m11zXXNIlZxXDbeFmxH8FCptAI=
			</data>
			<key>hash2</key>
			<data>
			M5TeojhNtQmu3gpImrmxhaYmk7bBo+MZKbWwD2q5eIk=
			</data>
		</dict>
		<key>Headers/TKPhotoSelectHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			mLAsIZ73VM66Si4ppgwXyHl9iiI=
			</data>
			<key>hash2</key>
			<data>
			QQablMwiU+O9IAZpsDR946MxX7W0Vx5GYb7lg8qc4Dk=
			</data>
		</dict>
		<key>Headers/TKPlugin50000.h</key>
		<dict>
			<key>hash</key>
			<data>
			SDpNfdndjmHfsxdx4/1iv/F+JHU=
			</data>
			<key>hash2</key>
			<data>
			rRQW881YdFy0FBmHMd0j/+vwnmL1IYof4R3uhEXGLrs=
			</data>
		</dict>
		<key>Headers/TKPlugin50001.h</key>
		<dict>
			<key>hash</key>
			<data>
			Bv+AijZFctaWB4nJlnOImrsa+Gg=
			</data>
			<key>hash2</key>
			<data>
			q6QvhFq+MYvWul2XYQgZaDoybW9BHQGLyGr+2J/flJI=
			</data>
		</dict>
		<key>Headers/TKPlugin50002.h</key>
		<dict>
			<key>hash</key>
			<data>
			bpyCBwJQs+CXyS3xgLFPV173Sdc=
			</data>
			<key>hash2</key>
			<data>
			GvRqIsnHkemKmUtjKnvZs/fBGhTtKa5Sx6IJ/DXq7PI=
			</data>
		</dict>
		<key>Headers/TKPlugin50010.h</key>
		<dict>
			<key>hash</key>
			<data>
			3Mbcc+lbT+J/yVVd+jA6vY7iNys=
			</data>
			<key>hash2</key>
			<data>
			yuby0VFjNuthY9Rw/6roj+3ey0YW5UmoFGnhQVMYKsw=
			</data>
		</dict>
		<key>Headers/TKPlugin50011.h</key>
		<dict>
			<key>hash</key>
			<data>
			4bkaSNYKyjkicGsA97gMlGcqLzM=
			</data>
			<key>hash2</key>
			<data>
			UEESy4p6QVH+iiiKERRf7j3dPT+04nklwwfbilKGST8=
			</data>
		</dict>
		<key>Headers/TKPlugin50020.h</key>
		<dict>
			<key>hash</key>
			<data>
			m6KnyN56vyNRD3lR7twmy3F7hXo=
			</data>
			<key>hash2</key>
			<data>
			u59AxEX7/jd9aL3qiPbcaMsE9oKfhxmcyZJqHFzTFfA=
			</data>
		</dict>
		<key>Headers/TKPlugin50021.h</key>
		<dict>
			<key>hash</key>
			<data>
			9EVyFCMpQ/gDo2XSoliXiSkm8WI=
			</data>
			<key>hash2</key>
			<data>
			cYo6HMyeXsRrcn8cjNWPzkICrsyEfRy4m+xiIsy9z2A=
			</data>
		</dict>
		<key>Headers/TKPlugin50022.h</key>
		<dict>
			<key>hash</key>
			<data>
			FwWPIVHs1sTu7FdjAeW/JWD6PDY=
			</data>
			<key>hash2</key>
			<data>
			2jMgjcgeZ8fQSjms/Kl0BJYiPyWdtv4uUBdxtKTmthI=
			</data>
		</dict>
		<key>Headers/TKPlugin50023.h</key>
		<dict>
			<key>hash</key>
			<data>
			SuFQ3wy8hY3dq1EM+joErAl7mCI=
			</data>
			<key>hash2</key>
			<data>
			DhwFXIw7V5gth7pazz9ZiVvfUoooZU64dA+fTIyNs6k=
			</data>
		</dict>
		<key>Headers/TKPlugin50024.h</key>
		<dict>
			<key>hash</key>
			<data>
			6oPvg6sNjzZdxEYhb4dfrYpjEYE=
			</data>
			<key>hash2</key>
			<data>
			4kIjJYBMhAgjMrGniwJVnmYRyaa4JSFrVQZ41YFOuAM=
			</data>
		</dict>
		<key>Headers/TKPlugin50025.h</key>
		<dict>
			<key>hash</key>
			<data>
			p4VDkZTrZJrr5VDwhoM7/mllUKA=
			</data>
			<key>hash2</key>
			<data>
			y7dC1wcJhYQa2R6wYI3zgutyrBSQbU8yrxinBautAq0=
			</data>
		</dict>
		<key>Headers/TKPlugin50030.h</key>
		<dict>
			<key>hash</key>
			<data>
			t7NJk1SgKgd3jdBujd/+fWu1WVM=
			</data>
			<key>hash2</key>
			<data>
			xwZPrjak97gcixoItIjUFW3iyxYmqX+YDli/hCotU88=
			</data>
		</dict>
		<key>Headers/TKPlugin50031.h</key>
		<dict>
			<key>hash</key>
			<data>
			3zEIHflIOGk9ckqOi0766brxo60=
			</data>
			<key>hash2</key>
			<data>
			GS0fIo4oq+8Fz705AK3aj7JpS5QpxcFgYMTWWBNLt64=
			</data>
		</dict>
		<key>Headers/TKPlugin50040.h</key>
		<dict>
			<key>hash</key>
			<data>
			m+9IDe52oDG2UNSCWcySAIOX17M=
			</data>
			<key>hash2</key>
			<data>
			0SCFr/LlOyjNkqcHK7JnwNoJe8XssvrYU3ZXtz/6LMg=
			</data>
		</dict>
		<key>Headers/TKPlugin50041.h</key>
		<dict>
			<key>hash</key>
			<data>
			FF4TVVif3vSPvmfpY4kE1xzps9Q=
			</data>
			<key>hash2</key>
			<data>
			dG2rx0esd/puCg8qie0jJWSmJXVc8Yq3sFIVdoLlk4g=
			</data>
		</dict>
		<key>Headers/TKPlugin50042.h</key>
		<dict>
			<key>hash</key>
			<data>
			syBZcxez6dwcIY+HspAnEScuiS0=
			</data>
			<key>hash2</key>
			<data>
			0PrxDJvcBBLyJ4ab69LjW+ghx5N1DI0UKQpTDYq04j8=
			</data>
		</dict>
		<key>Headers/TKPlugin50043.h</key>
		<dict>
			<key>hash</key>
			<data>
			WN1GHwJS3uI/0kWY6ssUKeXnlPk=
			</data>
			<key>hash2</key>
			<data>
			8TYpAKRUCpY7idLEPdgPHU0Zz2ajP/4wEGWCZUVcwhw=
			</data>
		</dict>
		<key>Headers/TKPlugin50100.h</key>
		<dict>
			<key>hash</key>
			<data>
			jJOclPiv3ZFGttlUyzA/F/UMJc8=
			</data>
			<key>hash2</key>
			<data>
			TK+BGiGBX+/XR2ClZM0HesttZ31UUoudif5btr3gWVk=
			</data>
		</dict>
		<key>Headers/TKPlugin50101.h</key>
		<dict>
			<key>hash</key>
			<data>
			BCe7NJVT1VTnl87s0dPFHZNc9VQ=
			</data>
			<key>hash2</key>
			<data>
			nB+e1o2e8C2Jc+ISJ2kIQt5RjVCyy+G/S736RbufabU=
			</data>
		</dict>
		<key>Headers/TKPlugin50104.h</key>
		<dict>
			<key>hash</key>
			<data>
			WrpwDbNoMAILenVGgTNPvyj05xM=
			</data>
			<key>hash2</key>
			<data>
			NEzY/Pdyy/GeaF+wusrD9wyAaTF/E66J2i6cvk68E2A=
			</data>
		</dict>
		<key>Headers/TKPlugin50105.h</key>
		<dict>
			<key>hash</key>
			<data>
			M1iMoeN8MRJFXyb+pLySS5JXqx8=
			</data>
			<key>hash2</key>
			<data>
			KCmoRVYO5fonIrETXI5nK6wVKGKd5mcoZinCr7DLfew=
			</data>
		</dict>
		<key>Headers/TKPlugin50106.h</key>
		<dict>
			<key>hash</key>
			<data>
			1T+HNecMZtRIOf3d4cWmM6VkSy0=
			</data>
			<key>hash2</key>
			<data>
			BkWwnpI9fbeigIQiphCNzgXekqAQk1hcrWoVxRvhKtU=
			</data>
		</dict>
		<key>Headers/TKPlugin50108.h</key>
		<dict>
			<key>hash</key>
			<data>
			kl2xHscU7+QP6CnF+8HW0MQt8cE=
			</data>
			<key>hash2</key>
			<data>
			CWlsfhiFS81sYMSUBsORcKVLZMrk6FjC0G70FuMKvvk=
			</data>
		</dict>
		<key>Headers/TKPlugin50109.h</key>
		<dict>
			<key>hash</key>
			<data>
			1fUS2a14bfd8wvzw/U5JC+L+ftI=
			</data>
			<key>hash2</key>
			<data>
			C8tnOaaDRJ6rm62eZ5U5UkmM2zbGZETbM2wBjNsVMOk=
			</data>
		</dict>
		<key>Headers/TKPlugin50110.h</key>
		<dict>
			<key>hash</key>
			<data>
			8hbqSSxX4FDV45tSSpxidF5W3N8=
			</data>
			<key>hash2</key>
			<data>
			u3sgwGzteFb8SOq6Slit7BDNtQIT7hquQNNVCEe/xNU=
			</data>
		</dict>
		<key>Headers/TKPlugin50112.h</key>
		<dict>
			<key>hash</key>
			<data>
			pSHcCmHFvpW8sPr/GuAuGZJTNbM=
			</data>
			<key>hash2</key>
			<data>
			cuOOtXiEensUDy8mkR/vXcG3E4IsOz+VO5DfJWkdtr4=
			</data>
		</dict>
		<key>Headers/TKPlugin50114.h</key>
		<dict>
			<key>hash</key>
			<data>
			kILiWw4JciqqFZMM8tzkm8WMYyo=
			</data>
			<key>hash2</key>
			<data>
			I42q/KrAz1rRHd8ydCQZGly//GIc+qK6H266hN5jyIE=
			</data>
		</dict>
		<key>Headers/TKPlugin50115.h</key>
		<dict>
			<key>hash</key>
			<data>
			gFA7ZFeD3ObsbpHY+N51rKVzMIQ=
			</data>
			<key>hash2</key>
			<data>
			vhR1qM+shaotwAwlJDLQ9JRjPtiyYffLImwVesfmdUk=
			</data>
		</dict>
		<key>Headers/TKPlugin50116.h</key>
		<dict>
			<key>hash</key>
			<data>
			zYEHta/KSNfSZ6zWCSGxFwqYK2w=
			</data>
			<key>hash2</key>
			<data>
			So7c+XPT1Ly+3fNknlsytgpTE7y0sfFtj2aF65fwMdA=
			</data>
		</dict>
		<key>Headers/TKPlugin50118.h</key>
		<dict>
			<key>hash</key>
			<data>
			1wa1h52tbnchwPHiBRxzYfV4/Io=
			</data>
			<key>hash2</key>
			<data>
			VenEDPDAP8PU/mkvd1LmuDUe+AR745PwYDCNi6weGa4=
			</data>
		</dict>
		<key>Headers/TKPlugin50119.h</key>
		<dict>
			<key>hash</key>
			<data>
			taYCbAoV4UoF27spZmpu5xOyRy8=
			</data>
			<key>hash2</key>
			<data>
			PD6sCEAZ/Brhyr9gxwQA/IXtC+suh1sUeFFW/vlyqmg=
			</data>
		</dict>
		<key>Headers/TKPlugin50120.h</key>
		<dict>
			<key>hash</key>
			<data>
			cPttTBXRAKyOvNTjWKZ0SMXI8Fk=
			</data>
			<key>hash2</key>
			<data>
			gkNTKow95mgYjpP/vSesUcFM1a1PmA2K4louLjFbfDM=
			</data>
		</dict>
		<key>Headers/TKPlugin50122.h</key>
		<dict>
			<key>hash</key>
			<data>
			irmR2k6U5SEkazYzspw/BMI0uq8=
			</data>
			<key>hash2</key>
			<data>
			ydVE8jvd5Knep9Fcrz4WRtW8l777B5johLchTbVhmmI=
			</data>
		</dict>
		<key>Headers/TKPlugin50123.h</key>
		<dict>
			<key>hash</key>
			<data>
			GYoWxO1iSo0pcyBs6b4jZiFrB+k=
			</data>
			<key>hash2</key>
			<data>
			Qv5yClTLCMMvYDPVIK71VqeFoWsS1Anu+OzOYXcPlA0=
			</data>
		</dict>
		<key>Headers/TKPlugin50124.h</key>
		<dict>
			<key>hash</key>
			<data>
			lRsxVw0ZsSaEOxHBYQRxz4075WE=
			</data>
			<key>hash2</key>
			<data>
			0wWVT5u/AAwovVqjxti5sFHaBKfpbiTZSXy7xKAyzIk=
			</data>
		</dict>
		<key>Headers/TKPlugin50125.h</key>
		<dict>
			<key>hash</key>
			<data>
			HpbJkd35/g5yTToRYe2eED5QV20=
			</data>
			<key>hash2</key>
			<data>
			+3DGXcM725vxSC/h53hAjD68YGhXuJF6e3ca7CiybVI=
			</data>
		</dict>
		<key>Headers/TKPlugin50127.h</key>
		<dict>
			<key>hash</key>
			<data>
			BncOoAGjGHiEBaGoptpUgmmVvJQ=
			</data>
			<key>hash2</key>
			<data>
			U0tKoFePsntG4vWlLUKtdw8EV6pYGfmrjeLH21tyu/M=
			</data>
		</dict>
		<key>Headers/TKPlugin50128.h</key>
		<dict>
			<key>hash</key>
			<data>
			KBA7YSdPb3A4AKJhfCog3g6s4Fo=
			</data>
			<key>hash2</key>
			<data>
			09tDRdBS9eSZAgePKvrq6LCf42oDpYVfg52y99L8iIQ=
			</data>
		</dict>
		<key>Headers/TKPlugin50130.h</key>
		<dict>
			<key>hash</key>
			<data>
			2IIRa1mFsCbykMQCe7hUa5ZmqtQ=
			</data>
			<key>hash2</key>
			<data>
			+DMlp8+UEWUnfXpSjslzkmU9J282bt2b+IhssloYMFc=
			</data>
		</dict>
		<key>Headers/TKPlugin50131.h</key>
		<dict>
			<key>hash</key>
			<data>
			AfhMRbZdrnOoCHVluHMdGAk2Mnc=
			</data>
			<key>hash2</key>
			<data>
			QzCLu/oobg5QaNmOMrkz9HJp09XqeE8nao+Z8jf106Q=
			</data>
		</dict>
		<key>Headers/TKPlugin50140.h</key>
		<dict>
			<key>hash</key>
			<data>
			9uCKKtUOBNIAFBYjKa9iGaNIsD8=
			</data>
			<key>hash2</key>
			<data>
			hixkBsdZc3gfcVZ9u6w3NEcjApztM5sdyUlP9K7P/HA=
			</data>
		</dict>
		<key>Headers/TKPlugin50141.h</key>
		<dict>
			<key>hash</key>
			<data>
			3hI2LdalaP17MwmsPQTqEy8XQKU=
			</data>
			<key>hash2</key>
			<data>
			Sg3y8QVKZp93qEf1YStK3MSoRv5/UOHhGsDLj+RExXw=
			</data>
		</dict>
		<key>Headers/TKPlugin50200.h</key>
		<dict>
			<key>hash</key>
			<data>
			8KVxRMf77QkO9b52OeGpffzwcu8=
			</data>
			<key>hash2</key>
			<data>
			d81Q6oXRKcYMS7IK7VBkYFktoEtvDl5KxBtyNMOIdeM=
			</data>
		</dict>
		<key>Headers/TKPlugin50201.h</key>
		<dict>
			<key>hash</key>
			<data>
			JhbqpdTVhs+Rgrw61pffTll1wvw=
			</data>
			<key>hash2</key>
			<data>
			PzKQi5lS9GkUYSDGphDJzFh2TjoQgm2NtkutCg2mgIg=
			</data>
		</dict>
		<key>Headers/TKPlugin50202.h</key>
		<dict>
			<key>hash</key>
			<data>
			DAoYfOrQr5Npl2rpZXxWWk85wvg=
			</data>
			<key>hash2</key>
			<data>
			hoDe4vZUggw8xmudmTASW83kWs/+/MwktTNcKnH0Qwk=
			</data>
		</dict>
		<key>Headers/TKPlugin50203.h</key>
		<dict>
			<key>hash</key>
			<data>
			Sdg+Qivd+aLyXmd20od/05hVmY8=
			</data>
			<key>hash2</key>
			<data>
			IRRFle9mtieZ8Y9cM3a6arPA+qPSH02dQvBCNKLYlW8=
			</data>
		</dict>
		<key>Headers/TKPlugin50210.h</key>
		<dict>
			<key>hash</key>
			<data>
			4+JtgvXs1hsB6HPH4Um0+2FdZ6k=
			</data>
			<key>hash2</key>
			<data>
			z7CW5MdUI6kVLh9IgEQa2XLGXfrxI/BOxW0lO4HaakU=
			</data>
		</dict>
		<key>Headers/TKPlugin50211.h</key>
		<dict>
			<key>hash</key>
			<data>
			/Z0l4tsCgM/ZdC6hLcGNih2OmAQ=
			</data>
			<key>hash2</key>
			<data>
			XNxMqpA1AeMz3lmUk/3XKIlH5c+8eJuywHu0Iy3VdCs=
			</data>
		</dict>
		<key>Headers/TKPlugin50213.h</key>
		<dict>
			<key>hash</key>
			<data>
			iTo+ALmLgzXgEgZug3kppuQOhZM=
			</data>
			<key>hash2</key>
			<data>
			atl1HMwN1+kkVXLAWiDFg1mx+dbkfSMhvA2wdPSlLrs=
			</data>
		</dict>
		<key>Headers/TKPlugin50220.h</key>
		<dict>
			<key>hash</key>
			<data>
			Acm/tAmK7M/6XysjYXOD1LTXaUw=
			</data>
			<key>hash2</key>
			<data>
			O1i5M1RfFPXFEOhmfw7TnNLzSQvzooJ2jk9Dj99vebc=
			</data>
		</dict>
		<key>Headers/TKPlugin50221.h</key>
		<dict>
			<key>hash</key>
			<data>
			W0U6qXoi3m2W1tL0vchD5RVLbd8=
			</data>
			<key>hash2</key>
			<data>
			dtStUsHA2NN7p/ODAu/9X2Lf3hRVh/g6n7UGfnGaRq0=
			</data>
		</dict>
		<key>Headers/TKPlugin50222.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ks9Cj4gsiu60rLKKjuZQAdCzYpk=
			</data>
			<key>hash2</key>
			<data>
			qDlYjZ7487305nABYvo6UK0ENwlLD4vUBS4hECSf8sU=
			</data>
		</dict>
		<key>Headers/TKPlugin50224.h</key>
		<dict>
			<key>hash</key>
			<data>
			snucWKmiR8cJ9WaGDAp9FZe3nTY=
			</data>
			<key>hash2</key>
			<data>
			lPaDgU3s3VjlZzjLPjisAZMKbpaitn9PBqiTddWBA7c=
			</data>
		</dict>
		<key>Headers/TKPlugin50225.h</key>
		<dict>
			<key>hash</key>
			<data>
			X5t+8asLYiG/2Stipgh24enK/Jc=
			</data>
			<key>hash2</key>
			<data>
			yROX8dZ0w0Wk/9+czR+1K2+slm3ksq4RZapih8o2roo=
			</data>
		</dict>
		<key>Headers/TKPlugin50228.h</key>
		<dict>
			<key>hash</key>
			<data>
			fesPKckPByb1ev5zXOUXUPNs6zc=
			</data>
			<key>hash2</key>
			<data>
			m3mE8XZ/aFNQRdOwNT1FqrO9ywlrnnbWxvXhDMK2VWU=
			</data>
		</dict>
		<key>Headers/TKPlugin50235.h</key>
		<dict>
			<key>hash</key>
			<data>
			qu5BViFn7ePIjGxVV8W+cvvb45c=
			</data>
			<key>hash2</key>
			<data>
			DGy2wFMRCEDlgTbcrEddmsJgdLmdDu0DpcObOqfn6VU=
			</data>
		</dict>
		<key>Headers/TKPlugin50240.h</key>
		<dict>
			<key>hash</key>
			<data>
			7/+Qa+E9JQhrriYzACptGtQ8Lxc=
			</data>
			<key>hash2</key>
			<data>
			h+5wRrZsEUtJTl5mRHJ+71Esd1IL4eyvn937V2djYpQ=
			</data>
		</dict>
		<key>Headers/TKPlugin50250.h</key>
		<dict>
			<key>hash</key>
			<data>
			SSHXQjAlQ3fPiPY5ujMEL0bNUpY=
			</data>
			<key>hash2</key>
			<data>
			HYb9yB1VmMAtnX6R1sM1jd5j75TOKFLIRHl9EYvyHug=
			</data>
		</dict>
		<key>Headers/TKPlugin50252.h</key>
		<dict>
			<key>hash</key>
			<data>
			0Bwlpk3v1U0mZEtzfxTaCPiKmGc=
			</data>
			<key>hash2</key>
			<data>
			sTq83sGCnABIIdy56x8JX458d6M4q3/Oj6aKnEqr4Wk=
			</data>
		</dict>
		<key>Headers/TKPlugin50260.h</key>
		<dict>
			<key>hash</key>
			<data>
			Oe5QKaUmNF9d42ReuHGoQVRjZRI=
			</data>
			<key>hash2</key>
			<data>
			LiIiVKHds5GyYDTQpsHFX/iRAELP8tHitL7KVM8IFyw=
			</data>
		</dict>
		<key>Headers/TKPlugin50261.h</key>
		<dict>
			<key>hash</key>
			<data>
			VUUWr7lZxRVOEARtGNQtEmTcr0I=
			</data>
			<key>hash2</key>
			<data>
			KNUaFM6brl+znWRmjjcRcwcY4SeiFCZT5kyn8hEBR5I=
			</data>
		</dict>
		<key>Headers/TKPlugin50263.h</key>
		<dict>
			<key>hash</key>
			<data>
			0Ag6L30cljTjCaPpiDkNhIAVgFM=
			</data>
			<key>hash2</key>
			<data>
			hj35AFxWJQZUK6Pf05igW0Zq4ECItEP6Dwd950Bl4Xs=
			</data>
		</dict>
		<key>Headers/TKPlugin50264.h</key>
		<dict>
			<key>hash</key>
			<data>
			HKwzBsELF57Rl2UO5ifN7HMKZXs=
			</data>
			<key>hash2</key>
			<data>
			N3ny3VtZnVHhOf+9oZRy/r44dFTNkE49aF5DZh4fJKg=
			</data>
		</dict>
		<key>Headers/TKPlugin50266.h</key>
		<dict>
			<key>hash</key>
			<data>
			uCZXiP2wsxvdA7fN1XB0o4+M99g=
			</data>
			<key>hash2</key>
			<data>
			BhZdEFnleDL+jpSSMpMveqlZ/K4a0MnmpQMFPRyUwtc=
			</data>
		</dict>
		<key>Headers/TKPlugin50270.h</key>
		<dict>
			<key>hash</key>
			<data>
			muNI2xj0COD8ZDi5ItC2VxFvgGs=
			</data>
			<key>hash2</key>
			<data>
			BfgDz0+3K1U/cyNM/QUZFRcDwcvBzQJFRL8UPfBXNlQ=
			</data>
		</dict>
		<key>Headers/TKPlugin50271.h</key>
		<dict>
			<key>hash</key>
			<data>
			sHL8o75PsYF4vMP8kAtKGp+z8lk=
			</data>
			<key>hash2</key>
			<data>
			UWF28aSsOgr31SoxMPBVrd/c7aRxA/6wunqS5l/w+zw=
			</data>
		</dict>
		<key>Headers/TKPlugin50273.h</key>
		<dict>
			<key>hash</key>
			<data>
			xenQ5QVhQJFzmTSe8XV1tpIQHGU=
			</data>
			<key>hash2</key>
			<data>
			5H8KJCw16JpAdaKZeEbbxVPm0Kt4QwwRaoG6QQKQNh8=
			</data>
		</dict>
		<key>Headers/TKPlugin50275.h</key>
		<dict>
			<key>hash</key>
			<data>
			Gm+akO1QlCRGeXNtGFV8rzKRqSQ=
			</data>
			<key>hash2</key>
			<data>
			6OxPozy5H6iurrtGKj4z4rvSMzAYpq2ezYon3r/DfN4=
			</data>
		</dict>
		<key>Headers/TKPlugin50276.h</key>
		<dict>
			<key>hash</key>
			<data>
			Dck0IuSPS1N6gF93S/tVqjB0m9A=
			</data>
			<key>hash2</key>
			<data>
			Up7GMUc/wDHiofXe8LJ14iWM/vUrjhYroaC7ebui91k=
			</data>
		</dict>
		<key>Headers/TKPlugin50277.h</key>
		<dict>
			<key>hash</key>
			<data>
			ITgETuEBlZWfJPSsCxHwn1KOAco=
			</data>
			<key>hash2</key>
			<data>
			Y9uj7PEUOqoeKP91o6txxE+W1xCJPsZj7oIsry7saRs=
			</data>
		</dict>
		<key>Headers/TKPlugin50282.h</key>
		<dict>
			<key>hash</key>
			<data>
			tQu9k9eNQ+vCOIrIoFjijsuUl8U=
			</data>
			<key>hash2</key>
			<data>
			5YuJ9u3irPWW9ag9J+/8bTaRwow5P2q5oMJmDuKbtIY=
			</data>
		</dict>
		<key>Headers/TKPlugin50400.h</key>
		<dict>
			<key>hash</key>
			<data>
			inhsJXexKNjY5T4gUnmtx7fJfhg=
			</data>
			<key>hash2</key>
			<data>
			UZ0kP5C+1+FWAyG4cKfKo5Po2Yg4cv6gHT2DbZRCMB8=
			</data>
		</dict>
		<key>Headers/TKPlugin50401.h</key>
		<dict>
			<key>hash</key>
			<data>
			q15F7LXXT+cnrNlYVdohxVl2ehI=
			</data>
			<key>hash2</key>
			<data>
			0ro7JysoR7JowZCtzGikRC/vsNCM5M3FEyoizfoLcl8=
			</data>
		</dict>
		<key>Headers/TKPlugin50404.h</key>
		<dict>
			<key>hash</key>
			<data>
			AW9Um518pbBpUEVuf5FwfLWrpg8=
			</data>
			<key>hash2</key>
			<data>
			Gapft+/pZZkNalRoS1PJP4YNKa1baewA+5P0H2hRSU4=
			</data>
		</dict>
		<key>Headers/TKPlugin50405.h</key>
		<dict>
			<key>hash</key>
			<data>
			rBGgDxxNcyauHppNxrxzjhs0DrQ=
			</data>
			<key>hash2</key>
			<data>
			njn+lRXDR7PX+gQyglvpDqMgas/A8ornjkwdtTCYRVo=
			</data>
		</dict>
		<key>Headers/TKPlugin50406.h</key>
		<dict>
			<key>hash</key>
			<data>
			DzebKJn4qFPHwqVKOHTSIBLRjEY=
			</data>
			<key>hash2</key>
			<data>
			DdJMP6Q00rpuqQcWPGnsJhGbW5ZrwZLmU0en/zWpnVo=
			</data>
		</dict>
		<key>Headers/TKPlugin50407.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q7gyGBmINzs65zJPjLcNsoaoemg=
			</data>
			<key>hash2</key>
			<data>
			vSmQhmFOEUlN4eyPh90eLHztAe0K8/XuBYQWw1oNSLE=
			</data>
		</dict>
		<key>Headers/TKPlugin50408.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+2uRu8XWVa5OWh18vjCOkGqSXI=
			</data>
			<key>hash2</key>
			<data>
			GvXAbEjNuhDc4tMJparxAJcYl5+zAQBcOnKVi2skYKU=
			</data>
		</dict>
		<key>Headers/TKPlugin50409.h</key>
		<dict>
			<key>hash</key>
			<data>
			ssY4YuEXhgCOdf0ObLBzIxf+zSQ=
			</data>
			<key>hash2</key>
			<data>
			ENygkce47KZC8di3HNvrwIQiNLNFk8nnos1nClBmuZI=
			</data>
		</dict>
		<key>Headers/TKPlugin50410.h</key>
		<dict>
			<key>hash</key>
			<data>
			TSRzPWidoWnOx1P5GbAvmFlL3gA=
			</data>
			<key>hash2</key>
			<data>
			axwO5dkJ0thA1DhVZqxSf9DyimVGDiOnPn8csB1isDc=
			</data>
		</dict>
		<key>Headers/TKPlugin50411.h</key>
		<dict>
			<key>hash</key>
			<data>
			sCXwdSx/hmo+MuevMMgMOZfO0JA=
			</data>
			<key>hash2</key>
			<data>
			E4sdQkKijRQqq6LFfPUGsvRB9MDedw/0BaXReOKIT0Y=
			</data>
		</dict>
		<key>Headers/TKPlugin50500.h</key>
		<dict>
			<key>hash</key>
			<data>
			qT74uvbe8v0eawH1iiaXG8L4k5k=
			</data>
			<key>hash2</key>
			<data>
			5LpYuSEx8azz+0Ha9n5oQCDg8QjB+k6S28MTBxA7pMc=
			</data>
		</dict>
		<key>Headers/TKPlugin50501.h</key>
		<dict>
			<key>hash</key>
			<data>
			5v9WGOUs7jwCJWssMe2P/VnUOJY=
			</data>
			<key>hash2</key>
			<data>
			dx2Dgx4uuLzf98yi3mqk+B3a9j5ry7OqekQFq39c/n8=
			</data>
		</dict>
		<key>Headers/TKPlugin50502.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ob+sFucxm9/CuTa1fC7GUFxnD/w=
			</data>
			<key>hash2</key>
			<data>
			qa9NriYhgWi769ePllWhurLM39fQksd4vltYmPbuZY4=
			</data>
		</dict>
		<key>Headers/TKPlugin50503.h</key>
		<dict>
			<key>hash</key>
			<data>
			UrnysvN9/Rbd6OXqb/Hibt2qa+U=
			</data>
			<key>hash2</key>
			<data>
			5kbxwccjOfHjvrcW9Vsu72BiYT/f25PC30OdMQjPask=
			</data>
		</dict>
		<key>Headers/TKPlugin50504.h</key>
		<dict>
			<key>hash</key>
			<data>
			Od5+kLK1JCWuGSFF/KYIGq4656s=
			</data>
			<key>hash2</key>
			<data>
			Gw4tAskNUrYDgHY46LwxT6TJB8asBxV2t49IUmGPR2M=
			</data>
		</dict>
		<key>Headers/TKPluginInvokeCenter.h</key>
		<dict>
			<key>hash</key>
			<data>
			2m/ThPPar4qOeshNE92lL9yyT2g=
			</data>
			<key>hash2</key>
			<data>
			mDs2YR8oCOAuy8gjYpyL14aq/a/77So7Hv9OnBYXXSQ=
			</data>
		</dict>
		<key>Headers/TKPluginInvokeCenterDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			UWfOsL4oz1xdJorzREqKqLge0EI=
			</data>
			<key>hash2</key>
			<data>
			y6pPHH0v42Jvy73GE15O/Nh0PUhpTIWEFM8GzxbPso8=
			</data>
		</dict>
		<key>Headers/TKPluginInvokeDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			Lv+x6ZgIhgdxKwTn53VM0EYSz8U=
			</data>
			<key>hash2</key>
			<data>
			6kDuy1rOHsTetSRC5ECXqYXjUiF/jbRAsyx8JFUQbpE=
			</data>
		</dict>
		<key>Headers/TKProcessDataDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			/XLe88SNPK4tZqRwrMir5KiMt0I=
			</data>
			<key>hash2</key>
			<data>
			zZH3td3Uf7XIhlvMfp1Yxb9q4JXlxcZvscg0enYcY/M=
			</data>
		</dict>
		<key>Headers/TKQRCodeGenerator.h</key>
		<dict>
			<key>hash</key>
			<data>
			hjmy3lTKpyWCuKQWxWVQPRbV3YQ=
			</data>
			<key>hash2</key>
			<data>
			/9zmAfphztZc1J/Ku7HIDTkhMUT+w3tCNiphy+09pzA=
			</data>
		</dict>
		<key>Headers/TKQRCoderScanerViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			HeucLKwEPCVoc/MAHSVKBwjIqNg=
			</data>
			<key>hash2</key>
			<data>
			my12lRMceGVJU8rLizQHjhmlEoqko6AX6rKN/4+huYM=
			</data>
		</dict>
		<key>Headers/TKQRCoderViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			JUTMPHKawMqxht4IjoZIlTUV+qs=
			</data>
			<key>hash2</key>
			<data>
			95Ho8ofdLuOGWENX52JXD0wiz+rMhADonvegoACfdW8=
			</data>
		</dict>
		<key>Headers/TKRSA.h</key>
		<dict>
			<key>hash</key>
			<data>
			jn679tcg79QFg8D6QUPIWKYC8NU=
			</data>
			<key>hash2</key>
			<data>
			u/Jv8KpiK93SprypmQM6QMfM4WoVokfcH5b0kWK/tvI=
			</data>
		</dict>
		<key>Headers/TKReachability.h</key>
		<dict>
			<key>hash</key>
			<data>
			XZJB87bSD74cXftcU1ysEj6dbbg=
			</data>
			<key>hash2</key>
			<data>
			2013z5HtiYoytz6EY87AlKuhR22Pku9ZJfi3tQT8hE0=
			</data>
		</dict>
		<key>Headers/TKRootViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			VEDLuiZU7na+lmxr7pscDSeCHHU=
			</data>
			<key>hash2</key>
			<data>
			E8SBOaWF0qb4hfQ05eDWyveOkzzQRlQ3NnytEtcOnPc=
			</data>
		</dict>
		<key>Headers/TKRsaHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			OWh1U7a2LEDOTm4zYmMlNgSTpuU=
			</data>
			<key>hash2</key>
			<data>
			PSLEPJT/kMsnQlkitomech1Tu7rzxTPM3nY/LMNTVq8=
			</data>
		</dict>
		<key>Headers/TKSDBusV3Client.h</key>
		<dict>
			<key>hash</key>
			<data>
			RJGR2HUB9jsfe/y1wxF3JpLh6ko=
			</data>
			<key>hash2</key>
			<data>
			kY3CW+MthIppWgveHG07Bj4DyGGfDD6cbJYGBYO/ko0=
			</data>
		</dict>
		<key>Headers/TKSM2Helper.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZXohpp74wRuxMIamsYEKthuJJ+E=
			</data>
			<key>hash2</key>
			<data>
			Lac+u07k1HEO3bPQDjK5bOcdGTxpEvMFJYDYzItgi90=
			</data>
		</dict>
		<key>Headers/TKSM3Helper.h</key>
		<dict>
			<key>hash</key>
			<data>
			0aZISHOPJ1ZoWIoGseNkMoNrqsQ=
			</data>
			<key>hash2</key>
			<data>
			GGucK3Quu28ZYVFaxzxmdFWEDxa1e65WUhRYSK1jiME=
			</data>
		</dict>
		<key>Headers/TKSM4Helper.h</key>
		<dict>
			<key>hash</key>
			<data>
			kbrE2G1Ct6dEfZMiSUe/qsfX+y4=
			</data>
			<key>hash2</key>
			<data>
			HA43vPhgf/xyH9p7gxHo5sWoa8fGfIk9Vm88KpxVApQ=
			</data>
		</dict>
		<key>Headers/TKSMPageControl.h</key>
		<dict>
			<key>hash</key>
			<data>
			2hK8h0qJ7yd+01tj6MYVRzQIdCk=
			</data>
			<key>hash2</key>
			<data>
			Uyx4k3fsOKw6TmPm6IcsD0atF4HHScH+8OgNZKXuezs=
			</data>
		</dict>
		<key>Headers/TKSSKeychain.h</key>
		<dict>
			<key>hash</key>
			<data>
			BZDwEXVF47dNZ3z6fbVj3as+UCk=
			</data>
			<key>hash2</key>
			<data>
			b1rQMr44Mr2D+b4ElrtsYXAZJS8XWqTg7GzjNo10jrQ=
			</data>
		</dict>
		<key>Headers/TKSafeThreadHandleHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q8pRmpenxcpNCl9wvD/Mes12UFw=
			</data>
			<key>hash2</key>
			<data>
			+3l1cVJv8YvTYOAD5LMAwj8tOqIcPNpN22QdeDksP3k=
			</data>
		</dict>
		<key>Headers/TKServer.h</key>
		<dict>
			<key>hash</key>
			<data>
			hlTg31YDOTcHN6fDD6h6V5HXKeY=
			</data>
			<key>hash2</key>
			<data>
			6pCQ/mRWiGV9gRXMxFM+wQU0YNmljF3o+ra9/KMfWAA=
			</data>
		</dict>
		<key>Headers/TKServerInvokeDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			IRN3ffySdgoTWpaIuiOkzfGa+5Q=
			</data>
			<key>hash2</key>
			<data>
			pqiIJxwBRcprZ0/gZmSVizSwGP+hN6cSLeF8apR2yMM=
			</data>
		</dict>
		<key>Headers/TKServerLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			7W7oKYlHRXLg1btrU6xl4MHJri8=
			</data>
			<key>hash2</key>
			<data>
			7TvsJzXhF0+omAlMuExRkrHNT8KQDqer2+Gntk/PUDg=
			</data>
		</dict>
		<key>Headers/TKServiceDaoDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			lV/mSDqBxCA+2pwK7xQZ+GOQP+E=
			</data>
			<key>hash2</key>
			<data>
			sVdtzLzrlFfKO2QDMohU0TQ/KBMWOIvN4mBFgzrseVM=
			</data>
		</dict>
		<key>Headers/TKServiceDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			zz0hG/vab+iPgZ84pCuomca0sFw=
			</data>
			<key>hash2</key>
			<data>
			6t8wMzHQFhKdqjXC0rjPTdAGsM/oH38UIO5xFgMtMb4=
			</data>
		</dict>
		<key>Headers/TKServiceFilterDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			KNl4C1QNTQ750Y5rtqL1dEm0vX0=
			</data>
			<key>hash2</key>
			<data>
			LW86zZ8+GjcgZJOn2flqabCadkqL+SpDB2HXojQbJAQ=
			</data>
		</dict>
		<key>Headers/TKShaHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			QJgHpZ+ALZdXAokmmKGU0ekM6NA=
			</data>
			<key>hash2</key>
			<data>
			ryYU9yc2wc/lpLpg+hR/apMKfnlUdkv2tCqjAw3/Obw=
			</data>
		</dict>
		<key>Headers/TKSlider.h</key>
		<dict>
			<key>hash</key>
			<data>
			dLuGClhiWJTM/AC75i+Cwi+l188=
			</data>
			<key>hash2</key>
			<data>
			/fx9AtW+yYQSCnQpkIfXV4nIgajc+P4xGpX7tKBqSAU=
			</data>
		</dict>
		<key>Headers/TKSocketSpeedChecker.h</key>
		<dict>
			<key>hash</key>
			<data>
			v6/8A+PzB15Bcxbx1OXD+vuY7mI=
			</data>
			<key>hash2</key>
			<data>
			5lGtxcJdsLTZ6fkm28kGskwmYxe6A79nSSZ5+s6YOYQ=
			</data>
		</dict>
		<key>Headers/TKSoundHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			qFCd5pyYoFVYQOS91+jP7eZr2Eo=
			</data>
			<key>hash2</key>
			<data>
			noApF7kkcuYHvvkDPLdj2D0UVLob3lAJcDqEA1AqAmQ=
			</data>
		</dict>
		<key>Headers/TKSpacer.h</key>
		<dict>
			<key>hash</key>
			<data>
			rkZiGAvPDcYbj8jGfGZeby+1Q48=
			</data>
			<key>hash2</key>
			<data>
			/OaVCmU1b4fPsrBH/jXb9Uz1jRBJUh3j2HK0ew1oswo=
			</data>
		</dict>
		<key>Headers/TKStringHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			x8itjT0q7bdhAugI9Tzgvga5dVs=
			</data>
			<key>hash2</key>
			<data>
			7tvP/z93P9Qos3v1m0JC55cGNgfOlv+GK+A22O1TgUk=
			</data>
		</dict>
		<key>Headers/TKSynthesizeSigleton.h</key>
		<dict>
			<key>hash</key>
			<data>
			G6t+kuMYF6zw1qPk6LJ9Rg+tBgM=
			</data>
			<key>hash2</key>
			<data>
			VFnJOHS41SbaM0B+UKUrMPM16r7PvRpGtX385odSTmc=
			</data>
		</dict>
		<key>Headers/TKSystemHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			VvCH2QNvRZl1AhbIOkSSbNe0hi4=
			</data>
			<key>hash2</key>
			<data>
			eEy7fRbebnPEIRDwGcA7kxDMP7tMl33H2Ki2PjBs7PA=
			</data>
		</dict>
		<key>Headers/TKTTYLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			7RPfJ9tE+i8c5vT20UINl4T32d4=
			</data>
			<key>hash2</key>
			<data>
			N+dzD4h40chD9AzTeHB7ufTzc/lrR6KfJrBxairipps=
			</data>
		</dict>
		<key>Headers/TKTabBarViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			FdaYUayhe1k37E9rvSxjqHuXnvU=
			</data>
			<key>hash2</key>
			<data>
			n0UTmssM45O7a5lutcEzWkNWuoC6qWs+tl7hFNVTZiA=
			</data>
		</dict>
		<key>Headers/TKTabView+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			lYRVN2sxdA3E4eQ33YcIM0phwIo=
			</data>
			<key>hash2</key>
			<data>
			rm/nT3Bp2AuCzwWjeOKAhXNRzJOKTvk/931IlBU2JcI=
			</data>
		</dict>
		<key>Headers/TKTabView.h</key>
		<dict>
			<key>hash</key>
			<data>
			J+BkPsrN4ivB1NNWhs+JQHFcMto=
			</data>
			<key>hash2</key>
			<data>
			ZGgpSSrvdvAVZ5vqe3ISOABEw4Ty6tzpjV/wwQC9dI8=
			</data>
		</dict>
		<key>Headers/TKTelAddress.h</key>
		<dict>
			<key>hash</key>
			<data>
			QM6fg74a1vHME89DcffR608zR7c=
			</data>
			<key>hash2</key>
			<data>
			u+VGIJj5HFhTvSoN1pHAJm3gOFNV7LFpTEfUEdrGMAk=
			</data>
		</dict>
		<key>Headers/TKTentacleView.h</key>
		<dict>
			<key>hash</key>
			<data>
			VGiUeV4M4RS4jMmzNPRkGmFMSkc=
			</data>
			<key>hash2</key>
			<data>
			yWS/a4lS2gpXz9g5rT7ozbnKW7pSWNpNwLYXYoHpbyQ=
			</data>
		</dict>
		<key>Headers/TKTextField.h</key>
		<dict>
			<key>hash</key>
			<data>
			6MSJPJpz+lJmFJrp5lOn3AT3uAo=
			</data>
			<key>hash2</key>
			<data>
			j6CS+4UM0j/HszTlPbGQSH/WMkVnALDWXuo/BOWG4+E=
			</data>
		</dict>
		<key>Headers/TKThemeManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			X85S0DcZv+inRxnxKNpGEmUkmpI=
			</data>
			<key>hash2</key>
			<data>
			62rm5vutsB67GhqED+WUQl2DWowZXOtOMZVJcjrwRGI=
			</data>
		</dict>
		<key>Headers/TKToast+UIView.h</key>
		<dict>
			<key>hash</key>
			<data>
			X8FeODaW8wJda2SA0Li5QdGCG6U=
			</data>
			<key>hash2</key>
			<data>
			+7bXw990PoDOpqoxfgrnF/qu2Qiz+7urXiS9ht+vdVw=
			</data>
		</dict>
		<key>Headers/TKTraffic.h</key>
		<dict>
			<key>hash</key>
			<data>
			e7PBDN24Kz/ipglmToW9D2gMu9c=
			</data>
			<key>hash2</key>
			<data>
			w1lavINjwYhUS/FgHbhqrZ4UQJ0MabVOFoyUL9fVwIg=
			</data>
		</dict>
		<key>Headers/TKUIHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			QC9IU6XpcizmtP7maPZXsHTWQ34=
			</data>
			<key>hash2</key>
			<data>
			eDaqPPa2AEOfg0vR8hMjFypkhPET2A8orsRQmSmisHA=
			</data>
		</dict>
		<key>Headers/TKUIView.h</key>
		<dict>
			<key>hash</key>
			<data>
			diHViaALbGWN7GCHwMEZdiJ+YSU=
			</data>
			<key>hash2</key>
			<data>
			APPS7udBAW0rW8Cye9UATqHsXJRgDNowG7kqmWvbfXA=
			</data>
		</dict>
		<key>Headers/TKURLRequestHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			qX4S9dw9HBCHvHbL6igX4c01LaI=
			</data>
			<key>hash2</key>
			<data>
			SIdprvAl3hkTGGyrBW+b4Rv/dbvzCnOUcdeEkkdt2kE=
			</data>
		</dict>
		<key>Headers/TKUUIDHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			J7yMOICeTyEec3M9hjATDY4jokg=
			</data>
			<key>hash2</key>
			<data>
			SjwFSRe3GtbgyJQjdPL1Hw9gMgrOSV740+td6VybQV8=
			</data>
		</dict>
		<key>Headers/TKUpdateManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			qWy9zMOE2GOJAyqdAXgYfFSidqI=
			</data>
			<key>hash2</key>
			<data>
			tUlToWOeybsVXqpBxnYG8rkckIDFsTLbgFgRIh9vT9Q=
			</data>
		</dict>
		<key>Headers/TKUploadManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			B1I/o5CE19lxyfZAmeS+a90E1Pc=
			</data>
			<key>hash2</key>
			<data>
			yxZ3hZ74XD9/Z5DkMZGVJYAZQ8CCFtMoKN0lS5u42A8=
			</data>
		</dict>
		<key>Headers/TKUtil.h</key>
		<dict>
			<key>hash</key>
			<data>
			7Uo0CQ8zlJjoWEvl+7YsRK+xpV4=
			</data>
			<key>hash2</key>
			<data>
			p78MfeIFA7vXyvsfr0RyVKWLX/Bu3etWBcCiyKJuwM0=
			</data>
		</dict>
		<key>Headers/TKVPImageCropperViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			QrZrUMZDZOMYTnm73Rp5NL1diPY=
			</data>
			<key>hash2</key>
			<data>
			dIZSE83V3FpMgxleX6/98laJT7UAn9DrVpFjGrqeH1k=
			</data>
		</dict>
		<key>Headers/TKWeakTimer.h</key>
		<dict>
			<key>hash</key>
			<data>
			MKSMNagQOK0la9L058lJ2ehr7gs=
			</data>
			<key>hash2</key>
			<data>
			hQsh/OGT/v8sWf0ttIDhHVhML+ZLWw/V0fQLRvLoGxk=
			</data>
		</dict>
		<key>Headers/TKWebView.h</key>
		<dict>
			<key>hash</key>
			<data>
			lMzSnPxFpKXjCbO5liY2j11l0Yo=
			</data>
			<key>hash2</key>
			<data>
			2J4+bRD3aF70xs4mOsCXnWb59M/Gal5WJDu7MY6kUqU=
			</data>
		</dict>
		<key>Headers/TKWebViewApp.h</key>
		<dict>
			<key>hash</key>
			<data>
			K4We1AEE1UtJanfDPCS0Or+WTz8=
			</data>
			<key>hash2</key>
			<data>
			IEvH3b0p8Y04DC0V/wOnfCq9+oIYsl0zNJDArOq+Pls=
			</data>
		</dict>
		<key>Headers/TKWebViewHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			f/JKPHG7y8SIM0n00JXVq/+phIE=
			</data>
			<key>hash2</key>
			<data>
			g8VSsMCxx1qQX4vmmVVd2mMxAmfk+jKRlCwYQMmCt/0=
			</data>
		</dict>
		<key>Headers/TKXML.h</key>
		<dict>
			<key>hash</key>
			<data>
			xt+d1AFYhHMDkwDkMCb6YJpSaWM=
			</data>
			<key>hash2</key>
			<data>
			kxAqOnof5/b6hspUPYOLJxAqsoyckkrUpGyTFfiO+us=
			</data>
		</dict>
		<key>Headers/TKYYAnimatedImageView.h</key>
		<dict>
			<key>hash</key>
			<data>
			QhnknAg4zqYMTDBk1vwBWHCogWE=
			</data>
			<key>hash2</key>
			<data>
			/cxCRsN2eV4kgWepZls6DhddW2WR9TZh4xiQwtDl8Ak=
			</data>
		</dict>
		<key>Headers/TKYYFrameImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			/RJb3IkgXDWb38KEyzvCP5Cmvo8=
			</data>
			<key>hash2</key>
			<data>
			13v91vUTNSa8Cb4IGNpN67fL3i8eRcFiYWTJdBOG348=
			</data>
		</dict>
		<key>Headers/TKYYImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			4Z7RT15RtPbKjndYiLo4vhZo8Kc=
			</data>
			<key>hash2</key>
			<data>
			lAfaxjpwdbfeolEQUjY9AR+OvWT07SIl5ZEg7fOvrbQ=
			</data>
		</dict>
		<key>Headers/TKYYImageCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			rVOk4E2/7ycxZ6/vcDj8tR3jyfM=
			</data>
			<key>hash2</key>
			<data>
			+OfdExL5EDpc3zN5DsVjGUb/YOgHRt6wHxvYRbK8W2k=
			</data>
		</dict>
		<key>Headers/TKYYSpriteSheetImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			kPZ7uPxZS8Hg7VO++uyQuSFiFJo=
			</data>
			<key>hash2</key>
			<data>
			g19bR02hP40qM1a1rPT2GUmTNjxEUK9IPI2ltdmwOlA=
			</data>
		</dict>
		<key>Headers/TKZipArchive.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZjdlJu1U+ksFtAdXS5uhBDVyP98=
			</data>
			<key>hash2</key>
			<data>
			bM/KsaT1rpqEz/d1VK7O3muE9ifZPNTSkrMe2ZHScKA=
			</data>
		</dict>
		<key>Headers/TKZlibHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			hAWN3JN7GjuQs4pPbuOItCx/8FQ=
			</data>
			<key>hash2</key>
			<data>
			5lkwDKM235zTny5iUwhSqq5CMDguVzGuQAmJwWwdBPQ=
			</data>
		</dict>
		<key>Headers/UIActivityIndicatorView+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			DELnh+xwCTnQPgaMWfqmA7kyQCc=
			</data>
			<key>hash2</key>
			<data>
			qmp0Nouq60rYtplb+UC4y8bn1+MFW4KEIaSOLFB1Cww=
			</data>
		</dict>
		<key>Headers/UIApplication+TKApplication.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZQuJKeJoWn4J8KbsQw1oIWK7OUM=
			</data>
			<key>hash2</key>
			<data>
			ajbTMya2WSKhd3IKosE8pvmp5dMS/fmdFMw2GbgC96A=
			</data>
		</dict>
		<key>Headers/UIButton+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			G66rIHUaCESNG1nel/8+yV2FMbA=
			</data>
			<key>hash2</key>
			<data>
			1ZLrsJyFxv5opUdSGH+7JA4DUXVHnO5bsHR0skzMUOg=
			</data>
		</dict>
		<key>Headers/UIControl+TKPunctuate.h</key>
		<dict>
			<key>hash</key>
			<data>
			nncvaxBgZRy38ZSEUUzh1D/fHwE=
			</data>
			<key>hash2</key>
			<data>
			RaTWa/Im9J2MuUFF0epJ+ykMj7eHsc3sroCPzc96Oc0=
			</data>
		</dict>
		<key>Headers/UIDevice+TKSystemVersion.h</key>
		<dict>
			<key>hash</key>
			<data>
			23YSw+2dLgddobCN2XIZpi6utK0=
			</data>
			<key>hash2</key>
			<data>
			PapirfUhpmN4RHEpy50121/rlh/4ra38/uXHxRqv7JM=
			</data>
		</dict>
		<key>Headers/UIImage+TKUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			LjMLhz/ydWpOaEFBr/qv25WcLu8=
			</data>
			<key>hash2</key>
			<data>
			Mu7y28RmIWK12z/r9Hev/dc6WS9O3sc/hu5CDoR/gVw=
			</data>
		</dict>
		<key>Headers/UIImageView+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			zH3IzqNXKlAwbsb31TKCKpjQiQY=
			</data>
			<key>hash2</key>
			<data>
			P5LJv12ImaL6v31P5rXUs9b93olp7cDdQ2mxnDaKoyg=
			</data>
		</dict>
		<key>Headers/UILabel+TKAjust.h</key>
		<dict>
			<key>hash</key>
			<data>
			SqDIm+ND5A56WZnxTp6tN7e3b7I=
			</data>
			<key>hash2</key>
			<data>
			D1jdfRyUcXWbcTZAyivkDDDHznNUB63wRqE2KXJSgmQ=
			</data>
		</dict>
		<key>Headers/UILabel+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			LW78MBkCJyx1savJCrC0K98iSc0=
			</data>
			<key>hash2</key>
			<data>
			XTO7lP+p2g1Pz6LhXr0q6pIY2vj53+xqQybxV9roXAA=
			</data>
		</dict>
		<key>Headers/UINavigationBar+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			eDoUPWTwC8x+pcJcF3Ee885nNzM=
			</data>
			<key>hash2</key>
			<data>
			iHsXFPKo13ES8oSy4BekbRtE2VmG43XaFG8c0IP5rIk=
			</data>
		</dict>
		<key>Headers/UINavigationItem+TKTheme.h</key>
		<dict>
			<key>hash</key>
			<data>
			lAALaB0l83KhNryqxEP8nzhuF/4=
			</data>
			<key>hash2</key>
			<data>
			gKdnbVTO7eUeBFM5s0qxzZxXkDHGEh5pc/qQ1YKUIN8=
			</data>
		</dict>
		<key>Headers/UIResponder+TKImageRouter.h</key>
		<dict>
			<key>hash</key>
			<data>
			zhK4Mz417eKk6O7sPve8fz7n2tQ=
			</data>
			<key>hash2</key>
			<data>
			iplqvrMCxroZpZDrt056UPrIEP6CoMDcABhyDfqXsHM=
			</data>
		</dict>
		<key>Headers/UIResponder+TKNimbusCore.h</key>
		<dict>
			<key>hash</key>
			<data>
			p3ReonyLvZM3zwpnvL8qScy7pYc=
			</data>
			<key>hash2</key>
			<data>
			XlxPnEr+btmez48a45kX1zyFigtP6Aa/RKBYLv/k+Lc=
			</data>
		</dict>
		<key>Headers/UIScrollView+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			rdHHqhoULPoTY3ZMGBc0JXzscXY=
			</data>
			<key>hash2</key>
			<data>
			clz29Kh8ZvBXVyoBUZ5S6nCeMZvsWBvkpCxfOd87fNM=
			</data>
		</dict>
		<key>Headers/UIScrollView+TKPullLoad.h</key>
		<dict>
			<key>hash</key>
			<data>
			PmNztu31jSfGfajFS6XskEvm9q0=
			</data>
			<key>hash2</key>
			<data>
			wUG4a5YdeVg+vZVOLMAqs1K1Jem6jdmeNulVMpUkxuw=
			</data>
		</dict>
		<key>Headers/UISearchBar+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			HcP0DpOeU1nPUGd+CS5n+PCh6+0=
			</data>
			<key>hash2</key>
			<data>
			XpP6xh1+nvR474yLcTOnTI2ZSXUopgaXjWfL7Ri58SU=
			</data>
		</dict>
		<key>Headers/UISegmentedControl+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			orBrxa6ve9pkrCP9Uu32pZa1kWA=
			</data>
			<key>hash2</key>
			<data>
			zaBhN+t0Fn22Ej7z15KXEssIpp42+Q7fTAbiUKK0ak8=
			</data>
		</dict>
		<key>Headers/UITabBar+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			n3FpRMaWtQbDG4c/9tP9VrG8tKQ=
			</data>
			<key>hash2</key>
			<data>
			a46YqTzWh+3/FWac3a1lMXU8g7cZy2BUS48P1eNKZmM=
			</data>
		</dict>
		<key>Headers/UITableView+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			nLI6JYgZ37K0JBqHUT8CSWQo/AE=
			</data>
			<key>hash2</key>
			<data>
			faXleY+L3YR9/dTzPiQEkqbxkmEV5aE4fndAY1Cd3rw=
			</data>
		</dict>
		<key>Headers/UITextField+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			C+0Xv2nWY+1KK6dGgYQOO+YoN/U=
			</data>
			<key>hash2</key>
			<data>
			txwczWSFzsqbrbD9FW39Xx+usvxEV9aN9w5ZLy/Ap4M=
			</data>
		</dict>
		<key>Headers/UITextView+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			/TlREpOgwq388/LQjb2zL1/Pm54=
			</data>
			<key>hash2</key>
			<data>
			VKmFrxF8LfFOjllniWZu3kinVjvtu0s1CPz3SffEAiU=
			</data>
		</dict>
		<key>Headers/UIToolbar+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			ELBHBoyxn1Yl3ZCxJfB/Ua76tD4=
			</data>
			<key>hash2</key>
			<data>
			kdUMP5IryNcyrzh0SXZjFzXQorDg9SCoxh2fSxs096c=
			</data>
		</dict>
		<key>Headers/UIView+TKBaseView.h</key>
		<dict>
			<key>hash</key>
			<data>
			nhUul+S2/tRJbtJFWhgAJZ8wtBg=
			</data>
			<key>hash2</key>
			<data>
			bBKdVhdDjSRLDpCzcIyU7umiwV62234HDI1Dq5T8SLY=
			</data>
		</dict>
		<key>Headers/UIView+TKFrame.h</key>
		<dict>
			<key>hash</key>
			<data>
			jLtL7YtuMb6JX1r7aezo9gRG7hQ=
			</data>
			<key>hash2</key>
			<data>
			yQ9+iM+3gt6yem5Uba58wO2M5XRUU5mJjHOA5jfjsEY=
			</data>
		</dict>
		<key>Headers/UIView+TKLayoutUtil.h</key>
		<dict>
			<key>hash</key>
			<data>
			t5YWqPlQ+6e7YmWh8WvFd9uDa4E=
			</data>
			<key>hash2</key>
			<data>
			8ArIWXz4/cAR5of37ghA16OcWMFTEsj7ou+B6wOwq/I=
			</data>
		</dict>
		<key>Headers/UIView+TKNIStyleable.h</key>
		<dict>
			<key>hash</key>
			<data>
			uqgoeQFz6m6MjOIFvyjn6FvE/Tc=
			</data>
			<key>hash2</key>
			<data>
			8Z9x3/VGQ1DMoHzWKiitGJApiPJFErgIcLgHEtViibg=
			</data>
		</dict>
		<key>Headers/UIView+TKPunctuate.h</key>
		<dict>
			<key>hash</key>
			<data>
			QjNwwrO0T/AZNSs+ueo6l1cbU4o=
			</data>
			<key>hash2</key>
			<data>
			PCTdPexU84bErHob023oyEsaJKrXpoKyu/jlD0xoAjA=
			</data>
		</dict>
		<key>Headers/UIView+TKTheme.h</key>
		<dict>
			<key>hash</key>
			<data>
			xObvD8CF48CPjaL0btQ6czRymzk=
			</data>
			<key>hash2</key>
			<data>
			CpkK6oHH8HJzBT0xc4GUrdHhgoO0ocMJhKW99YpxSio=
			</data>
		</dict>
		<key>Headers/UIViewController+TKBaseViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			obis61tcBiq6LBy0+PK8buTZKxM=
			</data>
			<key>hash2</key>
			<data>
			rhfJrioBqXxeki7IuKm+55Unu+Nbuj7+wf5C9pKYMoE=
			</data>
		</dict>
		<key>Headers/tkblowfish.h</key>
		<dict>
			<key>hash</key>
			<data>
			xCDsc7kE/p8rXN4fEJ/U/Ev0qZk=
			</data>
			<key>hash2</key>
			<data>
			oF/vx6pQ0etQGKAaPAlY56dRTrrjcwYwFE6iJGQKEqA=
			</data>
		</dict>
		<key>Headers/tkcrypt.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ol+6OBR08EAxNzgNxMHNJwiugzI=
			</data>
			<key>hash2</key>
			<data>
			TIC7fqIv/x8rFDnaLlLoXMpaQ4F1XjSWknujCN7s940=
			</data>
		</dict>
		<key>Headers/tkioapi.h</key>
		<dict>
			<key>hash</key>
			<data>
			g8pt5xrrLknNLdZuASn0b3yetCM=
			</data>
			<key>hash2</key>
			<data>
			ggDKx7J+ZpUjqyhRtmQPYNc9GqYPN7mRFxFneV6bztM=
			</data>
		</dict>
		<key>Headers/tkmztools.h</key>
		<dict>
			<key>hash</key>
			<data>
			+F5U/i8Tg3OmONI1AtRJfm66PZY=
			</data>
			<key>hash2</key>
			<data>
			CH1eSI7Wbw5MrmwCjAM3V+s99N8dS1ubKJP1xkYM1Us=
			</data>
		</dict>
		<key>Headers/tkunzip.h</key>
		<dict>
			<key>hash</key>
			<data>
			dsSyKwPyIKpeZVFrMd76+Fpd7Xg=
			</data>
			<key>hash2</key>
			<data>
			KUYG1qF+bF67IfO2MZmHzk1B1RwvTvvB/FCIsifD8DM=
			</data>
		</dict>
		<key>Headers/tkzip.h</key>
		<dict>
			<key>hash</key>
			<data>
			2HjDCQTUJycDpqAif77hQ3SPNZ0=
			</data>
			<key>hash2</key>
			<data>
			AXx5pRlc8k4AZo0RCxdP4vTqMUjXfr9mf50EHA5995M=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>hash2</key>
			<data>
			kmHsztpgjvF0JW5f3HdMHm49z1M0CcG8OT1JDQHHE/E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>开户标准版.txt</key>
		<dict>
			<key>hash</key>
			<data>
			AeFnhR0qQFUOyj+lMSOtqhe5AVc=
			</data>
			<key>hash2</key>
			<data>
			tNM+VNT6xjsZQIFXrP1Typx/MRzGySEpuUvmjK478SM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
